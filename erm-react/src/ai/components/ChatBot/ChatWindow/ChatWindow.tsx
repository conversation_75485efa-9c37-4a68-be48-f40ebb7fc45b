import React from 'react';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';

import { defaultStyledOptions } from '@protecht/ui-library/library/utils/defaultStyledOptions';

import { strings } from 'common/utils/i18n';
import { ChatBotOrientation } from '../types';
import Header from './Header';
import Body from './Body';

export const TOP_OFFSET_UNPINNED = 64;
export const BOTTOM_OFFSET_UNPINNED = 32;

const ChatWindowContainer = styled(
    Box,
    defaultStyledOptions,
)<{ $topBarOffset: number; $isPinned: boolean; $orientation: ChatBotOrientation }>(({ theme, $topBarOffset, $isPinned, $orientation }) => ({
    position: 'fixed',
    width: '360px',
    minHeight: '325px',
    border: `1px solid ${theme.palette.protechtGrey?.grey_164}`,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    backgroundColor: theme.palette.protechtGrey?.white,
    boxShadow: '0px 8px 48px 0px rgba(0, 0, 0, 0.30)',
    zIndex: 1000,

    ...($orientation === 'left' && {
        left: 0,
        right: 'auto',
        borderTopRightRadius: theme.spacing(2),
        borderBottomRightRadius: theme.spacing(2),
        borderLeft: 0,
    }),

    ...($orientation === 'right' && {
        left: 'auto',
        right: 0,
        borderTopLeftRadius: theme.spacing(2),
        borderBottomLeftRadius: theme.spacing(2),
        borderRight: 0,
    }),

    // When pinned, the chat window should be displayed as not floating right below the top bar
    // and should occupy the full height of the viewport minus the top bar height
    ...($isPinned && {
        bottom: 0,
        maxHeight: `calc(100vh - ${$topBarOffset}px)`,
    }),

    // When unpinned, the chat window should be displayed as floating
    // and should not overlap with the top bar
    ...(!$isPinned && {
        bottom: `${BOTTOM_OFFSET_UNPINNED}px`,
        maxHeight: `calc(100vh - ${TOP_OFFSET_UNPINNED + $topBarOffset}px - ${BOTTOM_OFFSET_UNPINNED}px)`,
    }),
}));

type ChatWindowProps = {
    topBarOffset?: number;
    isPinned?: boolean;
    orientation?: ChatBotOrientation;
    onClose: () => void;
};

const ChatWindow: React.FC<ChatWindowProps> = ({ topBarOffset = 0, isPinned = false, orientation = 'right', onClose }) => {
    return (
        <ChatWindowContainer
            $topBarOffset={topBarOffset}
            $isPinned={isPinned}
            $orientation={orientation}
            role="complementary"
            aria-label={strings('ai:chatBot.label')}
            // The className is needed to correct styles which are overridden by the GWT
            // Do not remove this class name
            className="modern-component"
            data-testid="aiChatBot-chatWindow"
        >
            <Header onClose={onClose} />
            <Body />
        </ChatWindowContainer>
    );
};

export default ChatWindow;
