import React, { useRef } from 'react';
import { styled } from '@mui/material/styles';

import { Input } from '@protecht/ui-library/library/components/Inputs';

import OverlayScrollbars, { OverlayScrollbarsRef } from 'common/components/OverlayScrollbars';
import { strings } from 'common/utils/i18n';

const BodyContainer = styled(OverlayScrollbars)(({ theme }) => ({
    padding: theme.spacing(3),
    paddingTop: theme.spacing(2.5),
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(3),
}));

const Body: React.FC = () => {
    const contentRef = useRef<OverlayScrollbarsRef>(null);

    return (
        <BodyContainer
            data-testid="aiChatBot-body"
            ref={contentRef}
            events={{
                initialized: (instance) => {
                    // Scroll to the bottom of the content when the scrollbar is initialized
                    const scrollHeight = instance.elements().viewport.scrollHeight;
                    instance.elements().viewport.scrollTo(0, scrollHeight);
                },
            }}
        >
            <Input
                multiline
                placeholder={strings('ai:chatBot.inputPlaceholder')}
                dataTestId="aiChatBot-input"
            />
        </BodyContainer>
    );
};

export default Body;
