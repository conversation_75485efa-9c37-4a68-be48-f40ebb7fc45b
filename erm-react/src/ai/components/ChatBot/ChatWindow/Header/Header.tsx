import React from 'react';
import { styled, useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

import { Close } from '@protecht/ui-library/library/components/SVGIcons';
import IconButton from '@protecht/ui-library/library/components/IconButton';

import { strings } from 'common/utils/i18n';
import { CHAT_BOT_NAME } from 'ai/components/ChatBot/config';
import ChatBotIcon from 'ai/icons/ChatBotIcon';

type HeaderProps = {
    onClose: () => void;
};

const HeaderContainer = styled(Box)(({ theme }) => ({
    height: theme.spacing(7),
    padding: theme.spacing(0, 3),
    display: 'flex',
    alignItems: 'center',
    borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_164}`,
    backgroundColor: theme.palette.protechtGrey?.grey_245,
    gap: '10px',
    flex: '0 0 auto',
}));

const Header: React.FC<HeaderProps> = ({ onClose }) => {
    const theme = useTheme();

    return (
        <HeaderContainer data-testid="aiChatBot-header">
            <ChatBotIcon style={{ margin: `0 ${theme.spacing(0.5)}` }} />
            <Typography
                variant="h3"
                sx={{ flex: 1 }}
            >
                {CHAT_BOT_NAME}
            </Typography>
            <IconButton
                onClick={onClose}
                size="small"
                aria-label={strings('ai:chatBot.closeChatBot')}
                data-testid="aiChatBot-closeButton"
            >
                <Close color={theme.palette.primary.main} />
            </IconButton>
        </HeaderContainer>
    );
};

export default Header;
