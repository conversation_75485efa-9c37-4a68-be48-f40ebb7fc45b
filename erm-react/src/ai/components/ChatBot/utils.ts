import { createRouteRegex, getGwtModuleFromUrl } from 'app/components/MainAppLayout/utils';
import { REACT_PAGES_WITHOUT_CHAT_BOT, GWT_MODULES_WITHOUT_CHAT_BOT } from './config';

export const isModuleWithoutChatBot = (locationPathName: string): boolean => {
    return REACT_PAGES_WITHOUT_CHAT_BOT.some((path) => {
        const regex = createRouteRegex(path);
        return regex.test(locationPathName);
    });
};

export const isGwtModuleWithoutChatBot = (widgetId?: string) => {
    // if we know widgetId, we can check it directly
    if (widgetId) {
        return GWT_MODULES_WITHOUT_CHAT_BOT.includes(widgetId);
    }

    // otherwise, we need to get module from the URL
    const widget = getGwtModuleFromUrl();

    if (widget && GWT_MODULES_WITHOUT_CHAT_BOT.includes(widget)) {
        return true;
    }

    return false;
};
