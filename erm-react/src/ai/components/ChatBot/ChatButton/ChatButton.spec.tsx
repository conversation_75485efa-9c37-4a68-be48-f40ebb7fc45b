import React from 'react';
import { render, screen } from 'test/utils';

import ChatButton from './ChatButton';
import { strings } from 'common/utils/i18n';

describe('ChatButton', () => {
    const mockOnClick = jest.fn();

    beforeEach(() => {
        mockOnClick.mockClear();
    });

    it('renders with default props', () => {
        const { container } = render(<ChatButton onClick={mockOnClick} />);

        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        expect(button).toHaveAttribute('aria-label', strings('ai:chatBot.openChatBot'));
        expect(screen.getByText('Cognita')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders with left orientation', () => {
        const { container } = render(
            <ChatButton
                onClick={mockOnClick}
                orientation="left"
            />,
        );

        expect(container).toMatchSnapshot();
    });

    it('renders with right orientation and matches snapshot', () => {
        const { container } = render(
            <ChatButton
                onClick={mockOnClick}
                orientation="right"
            />,
        );

        expect(container).toMatchSnapshot();
    });

    it('calls onClick when clicked', async () => {
        const { user } = render(<ChatButton onClick={mockOnClick} />);

        const button = screen.getByRole('button');
        await user.click(button);

        expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('can be focused and activated with keyboard', async () => {
        const { user } = render(<ChatButton onClick={mockOnClick} />);

        const button = screen.getByRole('button');
        await user.tab();
        expect(button).toHaveFocus();

        await user.keyboard('{Enter}');
        expect(mockOnClick).toHaveBeenCalledTimes(1);

        await user.keyboard(' ');
        expect(mockOnClick).toHaveBeenCalledTimes(2);
    });
});
