import React from 'react';
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';

import { strings } from 'common/utils/i18n';
import { CHAT_BOT_NAME } from 'ai/components/ChatBot/config';
import ChatBotSolidIcon from 'ai/icons/ChatBotSolidIcon';
import { ChatBotOrientation } from '../types';

const BUTTON_HEIGHT = 38;
const BOTTOM_OFFSET = 60;

const StyledButton = styled('button')<{ $orientation: ChatBotOrientation }>(({ theme, $orientation }) => ({
    height: `${BUTTON_HEIGHT}px`,
    position: 'fixed',
    left: 'auto',
    right: 'auto',
    bottom: `${BOTTOM_OFFSET}px`,
    zIndex: 1000,
    cursor: 'pointer',
    border: 'none',
    borderRadius: theme.spacing(0.5),
    padding: `${theme.spacing(0)} ${theme.spacing(1)}`,
    display: 'flex',
    alignItems: 'center',
    color: theme.palette.primary.contrastText,
    background: `linear-gradient(90deg, rgba(27, 74, 213, 0.20) 0%, rgba(72, 21, 126, 0.20) 100%), ${theme.palette.primary.main}`,
    '&:hover': {
        background: `linear-gradient(0deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.08) 100%), linear-gradient(90deg, rgba(27, 74, 213, 0.80) 0%, rgba(72, 21, 126, 0.80) 100%), ${theme.palette.primary.main}`,
    },
    '&:focus': {
        background: `linear-gradient(0deg, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0.15) 100%), linear-gradient(90deg, rgba(27, 74, 213, 0.80) 0%, rgba(72, 21, 126, 0.80) 100%), ${theme.palette.primary.main}`,
    },
    '&:focus-visible': {
        outline: 'none',
    },

    ...($orientation === 'right' && {
        transform: 'rotate(90deg)',
        transformOrigin: 'bottom right',
        right: `${BUTTON_HEIGHT}px`, // Offset by button height (which becomes width after rotation)
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        boxShadow: '0 8px 48px 0 rgba(0, 0, 0, 0.3)',
    }),

    ...($orientation === 'left' && {
        transform: 'rotate(90deg) translateX(-100%)',
        transformOrigin: 'bottom left',
        left: 0,
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        boxShadow: '0 -8px 48px 0 rgba(0, 0, 0, 0.3)',
    }),
}));

type ChatButtonProps = {
    onClick: () => void;
    orientation?: ChatBotOrientation;
};

const ChatButton: React.FC<ChatButtonProps> = ({ onClick, orientation = 'right' }) => {
    return (
        <StyledButton
            onClick={onClick}
            $orientation={orientation}
            aria-label={strings('ai:chatBot.openChatBot')}
            data-testid="aiChatBot-toggleButton"
            className="modern-component"
        >
            <ChatBotSolidIcon />
            <Typography
                variant="h6"
                component="span"
                color="primary.contrastText"
                sx={{ padding: '0px 5px 1px 4px' }}
            >
                {CHAT_BOT_NAME}
            </Typography>
        </StyledButton>
    );
};

export default ChatButton;
