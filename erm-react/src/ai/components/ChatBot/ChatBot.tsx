import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import Portal from '@mui/material/Portal';
import { ThemeProvider } from '@mui/material/styles';
import { deepmerge } from '@mui/utils';

import { protechtBlue } from '@protecht/ui-library/library/theme/colors/colors';
import { createTheme } from '@protecht/ui-library/library/theme';

import { useDispatch } from 'store';
import { getPageTopBarHeight } from 'app/selectors';
import { ermTheme } from 'app/theme/themes/protechtDefault';
import { getChatBotEnabledInModule, getChatBotOpen, getChatBotOrientation, getChatBotPinned } from 'ai/selectors';
import { closeChatBot, openChatBot } from 'ai/reducer';

import ChatButton from './ChatButton';
import ChatWindow from './ChatWindow';
import { useModuleSync } from './hooks/useModuleSync';

const chatBotTheme = {
    palette: {
        primary: {
            main: protechtBlue,
        },
    },
};

const theme = createTheme(deepmerge(ermTheme, chatBotTheme));

const ChatBot: React.FC = () => {
    const dispatch = useDispatch();
    const isOpen = useSelector(getChatBotOpen);
    const isPinned = useSelector(getChatBotPinned);
    const orientation = useSelector(getChatBotOrientation);
    const isChatBotEnabledInModule = useSelector(getChatBotEnabledInModule);
    const topBarOffset = useSelector(getPageTopBarHeight);

    useModuleSync();

    const handleOnChatButtonClick = useCallback(() => {
        dispatch(openChatBot());
    }, [dispatch]);

    const handleOnClose = useCallback(() => {
        dispatch(closeChatBot());
    }, [dispatch]);

    if (!isChatBotEnabledInModule) {
        return null;
    }

    return (
        <ThemeProvider theme={theme}>
            {isOpen ? (
                <Portal container={document.body}>
                    <ChatWindow
                        topBarOffset={topBarOffset}
                        isPinned={isPinned}
                        orientation={orientation}
                        onClose={handleOnClose}
                    />
                </Portal>
            ) : (
                <Portal container={document.body}>
                    <ChatButton
                        onClick={handleOnChatButtonClick}
                        orientation={orientation}
                    />
                </Portal>
            )}
        </ThemeProvider>
    );
};

export default React.memo(ChatBot);
