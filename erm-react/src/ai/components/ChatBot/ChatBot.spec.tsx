import React from 'react';
import { render, screen } from 'test/utils';

import ChatBot from './ChatBot';
import { useModuleSync } from './hooks/useModuleSync';

jest.mock('./ChatButton', () => ({
    __esModule: true,
    default: jest.fn(({ onClick, orientation }) => (
        <button
            data-testid="chat-button"
            onClick={onClick}
            data-orientation={orientation}
        >
            Chat <PERSON>
        </button>
    )),
}));

jest.mock('./ChatWindow', () => ({
    __esModule: true,
    default: jest.fn(({ onClose, topBarOffset, isPinned, orientation }) => (
        <div
            data-testid="chat-window"
            data-top-bar-offset={topBarOffset}
            data-is-pinned={isPinned}
            data-orientation={orientation}
        >
            <button
                data-testid="close-button"
                onClick={onClose}
            >
                Close
            </button>
            Chat Window
        </div>
    )),
}));

jest.mock('./hooks/useModuleSync', () => ({
    useModuleSync: jest.fn(),
}));

const mockGetChatBotOpen = jest.fn();
const mockGetChatBotPinned = jest.fn();
const mockGetChatBotOrientation = jest.fn();
const mockGetChatBotEnabledInModule = jest.fn();
const mockGetPageTopBarHeight = jest.fn();

jest.mock('ai/selectors', () => ({
    getChatBotOpen: () => mockGetChatBotOpen(),
    getChatBotPinned: () => mockGetChatBotPinned(),
    getChatBotOrientation: () => mockGetChatBotOrientation(),
    getChatBotEnabledInModule: () => mockGetChatBotEnabledInModule(),
}));

jest.mock('app/selectors', () => ({
    getPageTopBarHeight: () => mockGetPageTopBarHeight(),
}));

describe('ChatBot', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockGetChatBotOpen.mockReturnValue(false);
        mockGetChatBotPinned.mockReturnValue(false);
        mockGetChatBotOrientation.mockReturnValue('right');
        mockGetChatBotEnabledInModule.mockReturnValue(true);
        mockGetPageTopBarHeight.mockReturnValue(60);
        (useModuleSync as jest.Mock).mockImplementation(() => {});
    });

    it('renders ChatButton when chat is closed and enabled in module', () => {
        mockGetChatBotOpen.mockReturnValue(false);
        mockGetChatBotEnabledInModule.mockReturnValue(true);

        render(<ChatBot />);

        expect(screen.getByTestId('chat-button')).toBeInTheDocument();
        expect(screen.queryByTestId('chat-window')).not.toBeInTheDocument();
    });

    it('renders ChatWindow when chat is open and enabled in module', () => {
        mockGetChatBotOpen.mockReturnValue(true);
        mockGetChatBotEnabledInModule.mockReturnValue(true);
        mockGetChatBotPinned.mockReturnValue(true);
        mockGetPageTopBarHeight.mockReturnValue(80);

        render(<ChatBot />);

        expect(screen.queryByTestId('chat-button')).not.toBeInTheDocument();
        expect(screen.getByTestId('chat-window')).toBeInTheDocument();

        const chatWindow = screen.getByTestId('chat-window');
        expect(chatWindow).toHaveAttribute('data-top-bar-offset', '80');
        expect(chatWindow).toHaveAttribute('data-is-pinned', 'true');
        expect(chatWindow).toHaveAttribute('data-orientation', 'right');
    });

    it('does not render anything when chat is disabled in module', () => {
        mockGetChatBotEnabledInModule.mockReturnValue(false);

        render(<ChatBot />);

        expect(screen.queryByTestId('chat-button')).not.toBeInTheDocument();
        expect(screen.queryByTestId('chat-window')).not.toBeInTheDocument();
    });

    it('passes correct props to ChatButton', () => {
        mockGetChatBotOpen.mockReturnValue(false);
        mockGetChatBotEnabledInModule.mockReturnValue(true);
        mockGetChatBotOrientation.mockReturnValue('left');

        render(<ChatBot />);

        const chatButton = screen.getByTestId('chat-button');
        expect(chatButton).toHaveAttribute('data-orientation', 'left');
        // onClick prop is tested in the dispatch test
    });

    it('passes correct props to ChatWindow', () => {
        mockGetChatBotOpen.mockReturnValue(true);
        mockGetChatBotEnabledInModule.mockReturnValue(true);
        mockGetChatBotOrientation.mockReturnValue('left');
        mockGetChatBotPinned.mockReturnValue(true);
        mockGetPageTopBarHeight.mockReturnValue(100);

        render(<ChatBot />);

        const chatWindow = screen.getByTestId('chat-window');
        expect(chatWindow).toHaveAttribute('data-orientation', 'left');
        expect(chatWindow).toHaveAttribute('data-is-pinned', 'true');
        expect(chatWindow).toHaveAttribute('data-top-bar-offset', '100');
        // onClose prop is tested in the dispatch test
    });

    it('calls useModuleSync hook', () => {
        mockGetChatBotEnabledInModule.mockReturnValue(true);

        render(<ChatBot />);

        expect(useModuleSync).toHaveBeenCalled();
    });

    it('dispatches openChatBot when ChatButton is clicked', async () => {
        mockGetChatBotOpen.mockReturnValue(false);
        mockGetChatBotEnabledInModule.mockReturnValue(true);

        const { store, user } = render(<ChatBot />);
        const chatButton = screen.getByTestId('chat-button');

        await user.click(chatButton);

        // Verify that the store state has been updated
        expect(store.getState().ai.chatBot.open).toBe(true);
    });

    it('dispatches closeChatBot when ChatWindow close button is clicked', async () => {
        mockGetChatBotOpen.mockReturnValue(true);
        mockGetChatBotEnabledInModule.mockReturnValue(true);

        const { store, user } = render(<ChatBot />, {
            preloadedState: {
                ai: { chatBot: { open: true, pinned: false, orientation: 'right', enabledInModule: true } },
            },
        });
        const closeButton = screen.getByTestId('close-button');

        await user.click(closeButton);

        // Verify that the store state has been updated
        expect(store.getState().ai.chatBot.open).toBe(false);
    });
});
