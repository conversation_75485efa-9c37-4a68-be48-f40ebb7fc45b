import { useEffect } from 'react';

import { useDispatch } from 'store';
import { appEvents, AppEventType } from 'app/NotificationService';
import { setChatBotEnabledInModule } from 'ai/reducer';
import { isGwtModuleWithoutChatBot, isModuleWithoutChatBot } from '../utils';

/**
 * Custom hook to synchronize the chat bot state with the current module.
 * It listens for navigation events and updates the chat bot enabled state accordingly.
 */
export const useModuleSync = () => {
    const dispatch = useDispatch();

    useEffect(() => {
        const subscription = appEvents.subscribe((event) => {
            switch (event.type) {
                case AppEventType.NAVIGATION: {
                    if (event.locationPathName) {
                        const isWithoutChatBot = isModuleWithoutChatBot(event.locationPathName);
                        dispatch(setChatBotEnabledInModule(!isWithoutChatBot));
                    } else if (event.widgetId) {
                        const isWithoutChatBot = isGwtModuleWithoutChatBot(event.widgetId);
                        dispatch(setChatBotEnabledInModule(!isWithoutChatBot));
                    }
                    break;
                }
                default:
                    break;
            }
        });

        return () => {
            subscription.unsubscribe();
        };
    }, [dispatch]);
};
