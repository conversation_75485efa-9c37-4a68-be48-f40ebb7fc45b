import { Subject } from 'rxjs';
import { renderHookWithStore } from 'test/utils/rtl';
import { useModuleSync } from './useModuleSync';
import { isGwtModuleWithoutChatBot, isModuleWithoutChatBot } from '../utils';
import { mockedAiSlice } from 'ai/mock';
import { AppEventType } from 'app/NotificationService';
import { setChatBotEnabledInModule } from 'ai/reducer';

jest.mock('../utils', () => ({
    isGwtModuleWithoutChatBot: jest.fn(),
    isModuleWithoutChatBot: jest.fn(),
}));

const mockDispatch = jest.fn();
jest.mock('store', () => ({
    ...jest.requireActual('store'),
    useDispatch: () => mockDispatch,
}));

const mockAppSubject = new Subject();
jest.mock('app/NotificationService', () => ({
    AppEventType: {
        NAVIGATION: 'navigation',
    },
    get appEvents() {
        return mockAppSubject.asObservable();
    },
}));

describe('useModuleSync', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockDispatch.mockClear();
        (isGwtModuleWithoutChatBot as jest.Mock).mockReturnValue(false);
        (isModuleWithoutChatBot as jest.Mock).mockReturnValue(false);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('enables chatbot when navigation event has locationPathName and module allows chatbot', () => {
        (isModuleWithoutChatBot as jest.Mock).mockReturnValue(false);

        renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        mockAppSubject.next({
            type: AppEventType.NAVIGATION,
            locationPathName: '/some/path',
        });

        expect(isModuleWithoutChatBot).toHaveBeenCalledWith('/some/path');
        expect(mockDispatch).toHaveBeenCalledWith(setChatBotEnabledInModule(true));
    });

    it('disables chatbot when navigation event has locationPathName and module disallows chatbot', () => {
        (isModuleWithoutChatBot as jest.Mock).mockReturnValue(true);

        renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        mockAppSubject.next({
            type: AppEventType.NAVIGATION,
            locationPathName: '/vendor-portal',
        });

        expect(isModuleWithoutChatBot).toHaveBeenCalledWith('/vendor-portal');
        expect(mockDispatch).toHaveBeenCalledWith(setChatBotEnabledInModule(false));
    });

    it('enables chatbot when navigation event has widgetId and GWT module allows chatbot', () => {
        (isGwtModuleWithoutChatBot as jest.Mock).mockReturnValue(false);

        renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        mockAppSubject.next({
            type: AppEventType.NAVIGATION,
            widgetId: 'SomeModule',
        });

        expect(isGwtModuleWithoutChatBot).toHaveBeenCalledWith('SomeModule');
        expect(mockDispatch).toHaveBeenCalledWith(setChatBotEnabledInModule(true));
    });

    it('disables chatbot when navigation event has widgetId and GWT module disallows chatbot', () => {
        (isGwtModuleWithoutChatBot as jest.Mock).mockReturnValue(true);

        renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        mockAppSubject.next({
            type: AppEventType.NAVIGATION,
            widgetId: 'VendorPortal',
        });

        expect(isGwtModuleWithoutChatBot).toHaveBeenCalledWith('VendorPortal');
        expect(mockDispatch).toHaveBeenCalledWith(setChatBotEnabledInModule(false));
    });

    it('prioritizes locationPathName over widgetId when both are present', () => {
        (isModuleWithoutChatBot as jest.Mock).mockReturnValue(false);
        (isGwtModuleWithoutChatBot as jest.Mock).mockReturnValue(true);

        renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        mockAppSubject.next({
            type: AppEventType.NAVIGATION,
            locationPathName: '/some/path',
            widgetId: 'VendorPortal',
        });

        expect(isModuleWithoutChatBot).toHaveBeenCalledWith('/some/path');
        expect(isGwtModuleWithoutChatBot).not.toHaveBeenCalled();
        expect(mockDispatch).toHaveBeenCalledWith(setChatBotEnabledInModule(true));
    });

    it('ignores navigation events without locationPathName or widgetId', () => {
        renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        mockAppSubject.next({
            type: AppEventType.NAVIGATION,
        });

        expect(isModuleWithoutChatBot).not.toHaveBeenCalled();
        expect(isGwtModuleWithoutChatBot).not.toHaveBeenCalled();
        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it('ignores non-navigation events', () => {
        renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        mockAppSubject.next({
            type: 'OTHER_EVENT',
            locationPathName: '/some/path',
        });

        expect(isModuleWithoutChatBot).not.toHaveBeenCalled();
        expect(isGwtModuleWithoutChatBot).not.toHaveBeenCalled();
        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it('unsubscribes from app events on unmount', () => {
        const unsubscribeSpy = jest.fn();
        const mockSubscription = {
            unsubscribe: unsubscribeSpy,
        };

        jest.spyOn(mockAppSubject, 'subscribe').mockReturnValue(mockSubscription as any);

        const { unmount } = renderHookWithStore(() => useModuleSync(), { ai: mockedAiSlice });

        unmount();

        expect(unsubscribeSpy).toHaveBeenCalled();
    });
});
