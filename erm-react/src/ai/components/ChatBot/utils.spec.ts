import { isModuleWithoutChatBot, isGwtModuleWithoutChatBot } from './utils';
import { getGwtModuleFromUrl } from 'app/components/MainAppLayout/utils';

jest.mock('app/components/MainAppLayout/utils', () => ({
    ...jest.requireActual('app/components/MainAppLayout/utils'),
    getGwtModuleFromUrl: jest.fn(),
}));

jest.mock('./config', () => ({
    REACT_PAGES_WITHOUT_CHAT_BOT: ['/vendor-portal', '/marketplace', '/anonymous'],
    GWT_MODULES_WITHOUT_CHAT_BOT: ['VendorPortal', 'MarketplaceCenter', 'AnonymousEntry'],
}));

describe('ChatBot utils', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('isModuleWithoutChatBot', () => {
        it('returns true when location matches a page without chatbot', () => {
            const result = isModuleWithoutChatBot('/vendor-portal/dashboard');

            expect(result).toBe(true);
        });

        it('returns false when location does not match any page without chatbot', () => {
            const result = isModuleWithoutChatBot('/dashboard');

            expect(result).toBe(false);
        });

        it('returns true when location matches marketplace path', () => {
            const result = isModuleWithoutChatBot('/marketplace/packages');

            expect(result).toBe(true);
        });
    });

    describe('isGwtModuleWithoutChatBot', () => {
        it('returns true when widgetId is in the excluded list', () => {
            const result = isGwtModuleWithoutChatBot('VendorPortal');
            expect(result).toBe(true);
        });

        it('returns false when widgetId is not in the excluded list', () => {
            const result = isGwtModuleWithoutChatBot('Dashboard');
            expect(result).toBe(false);
        });

        it('returns true when widgetId is MarketplaceCenter', () => {
            const result = isGwtModuleWithoutChatBot('MarketplaceCenter');
            expect(result).toBe(true);
        });

        it('returns true when widgetId is AnonymousEntry', () => {
            const result = isGwtModuleWithoutChatBot('AnonymousEntry');
            expect(result).toBe(true);
        });

        it('returns false when widgetId is undefined and URL module is not excluded', () => {
            (getGwtModuleFromUrl as jest.Mock).mockReturnValue('Dashboard');

            const result = isGwtModuleWithoutChatBot();

            expect(getGwtModuleFromUrl).toHaveBeenCalled();
            expect(result).toBe(false);
        });

        it('returns true when widgetId is undefined and URL module is excluded', () => {
            (getGwtModuleFromUrl as jest.Mock).mockReturnValue('VendorPortal');

            const result = isGwtModuleWithoutChatBot();

            expect(getGwtModuleFromUrl).toHaveBeenCalled();
            expect(result).toBe(true);
        });

        it('returns false when widgetId is undefined and URL module is null', () => {
            (getGwtModuleFromUrl as jest.Mock).mockReturnValue(null);

            const result = isGwtModuleWithoutChatBot();

            expect(getGwtModuleFromUrl).toHaveBeenCalled();
            expect(result).toBe(false);
        });
    });
});
