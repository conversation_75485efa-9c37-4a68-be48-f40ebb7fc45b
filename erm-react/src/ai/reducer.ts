import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatBotOrientation } from './components/ChatBot/types';

export interface State {
    chatBot: {
        orientation: ChatBotOrientation;
        enabledInModule: boolean;
        open: boolean;
        pinned: boolean;
    };
}

const initialState: State = {
    chatBot: {
        orientation: 'right',
        enabledInModule: false, // False by default, will be set based on the current module
        open: false,
        pinned: false,
    },
};

const aiSlice = createSlice({
    name: 'ai',
    initialState,
    reducers: {
        openChatBot: (state) => {
            state.chatBot.open = true;
        },
        closeChatBot: (state) => {
            state.chatBot.open = false;
        },
        setChatBotOrientation: (state, action: PayloadAction<ChatBotOrientation>) => {
            state.chatBot.orientation = action.payload;
        },
        setChatBotPinned: (state, action: PayloadAction<boolean>) => {
            state.chatBot.pinned = action.payload;
        },
        setChatBotEnabledInModule: (state, action: PayloadAction<boolean>) => {
            state.chatBot.enabledInModule = action.payload;
        },
    },
});

export const { openChatBot, closeChatBot, setChatBotPinned, setChatBotEnabledInModule } = aiSlice.actions;
export default aiSlice;
