import * as React from 'react';
import type { SVGProps } from 'react';

const AIChatBotIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="currentColor"
        {...props}
    >
        <path
            d="M10 14.3867C12.7542 14.3867 14.9873 15.6432 14.9873 17.1934C14.9871 18.7434 12.7541 20 10 20C7.2459 20 5.01287 18.7434 5.0127 17.1934C5.0127 15.6432 7.24579 14.3867 10 14.3867ZM2.80664 5.0127C4.35679 5.0127 5.61328 7.24579 5.61328 10C5.61328 12.7542 4.35678 14.9873 2.80664 14.9873C1.25657 14.9872 4.93142e-06 12.7541 0 10C0 7.24588 1.25656 5.01283 2.80664 5.0127ZM17.1934 5.0127C18.7434 5.01284 20 7.24588 20 10C20 12.7541 18.7434 14.9872 17.1934 14.9873C15.6432 14.9873 14.3867 12.7542 14.3867 10C14.3867 7.24579 15.6432 5.0127 17.1934 5.0127ZM10 6.73145C11.8053 6.73148 13.2686 8.19464 13.2686 10C13.2685 11.8053 11.8053 13.2685 10 13.2686C8.19463 13.2686 6.73145 11.8054 6.73145 10C6.73145 8.19462 8.19462 6.73145 10 6.73145ZM10 0C12.7541 2.19004e-05 14.9872 1.25658 14.9873 2.80664C14.9873 4.35677 12.7542 5.61326 10 5.61328C7.24579 5.61328 5.0127 4.35679 5.0127 2.80664C5.01283 1.25656 7.24588 0 10 0Z"
            fill="url(#paint0_linear_596_1105377)"
        />
        <defs>
            <linearGradient
                id="paint0_linear_596_1105377"
                x1="7.21672"
                y1="-0.302879"
                x2="20.4308"
                y2="13.342"
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#89E2ED" />
                <stop
                    offset="0.06"
                    stopColor="#7BCFEE"
                />
                <stop
                    offset="0.19"
                    stopColor="#5FAAF0"
                />
                <stop
                    offset="0.33"
                    stopColor="#478BF1"
                />
                <stop
                    offset="0.47"
                    stopColor="#3574F3"
                />
                <stop
                    offset="0.62"
                    stopColor="#2963F4"
                />
                <stop
                    offset="0.79"
                    stopColor="#2159F4"
                />
                <stop
                    offset="1"
                    stopColor="#1F56F5"
                />
            </linearGradient>
        </defs>
    </svg>
);
export default AIChatBotIcon;
