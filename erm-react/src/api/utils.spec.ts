import { getErrorCode } from './utils';

describe('getErrorCode', () => {
    it('returns undefined when error not available', () => {
        expect(getErrorCode(undefined)).toBeUndefined();
    });

    it('returns undefined when error is not object', () => {
        expect(getErrorCode('error')).toBeUndefined();
    });

    it('returns undefined when status is not available', () => {
        expect(getErrorCode({ message: 'some error' })).toBeUndefined();
    });

    it('returns correct status code when status is available', () => {
        expect(getErrorCode({ message: 'some error', status: 404 })).toEqual(404);
    });
});
