import { PrsiGetStatesUsingPostApiResponse, PrsiGetStatesUsingPostApiArg, StateRest, PaginRestResultStateRest, ViewExpressionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        prsiGetStatesUsingPost: build.mutation<PrsiGetStatesUsingPostApiResponse, PrsiGetStatesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ipickers/states`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
