import { ScrsGetPropertiesUsingGetApiResponse, ScrsGetPropertiesUsingGetApiArg, ScrsGetTimezonesUsingGetApiResponse, ScrsGetTimezonesUsingGetApiArg, TimezonesRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        scrsGetPropertiesUsingGet: build.query<ScrsGetPropertiesUsingGetApiResponse, ScrsGetPropertiesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/system`,
                params: {
                    key: queryArg.key,
                },
            }),
        }),
        scrsGetTimezonesUsingGet: build.query<ScrsGetTimezonesUsingGetApiResponse, ScrsGetTimezonesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/system/timezones` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
