import { ArsUploadAttachmentUsingPostApiResponse, ArsUploadAttachmentUsingPostApiArg, ArsDownloadAllAttachmentsUsingGetApiResponse, ArsDownloadAllAttachmentsUsingGetApiArg, GetAttachmentsApiResponse, GetAttachmentsApiArg, ArsCheckAttachDownloadUsingGetApiResponse, ArsCheckAttachDownloadUsingGetApiArg, ArsAttachReportUsingPostApiResponse, ArsAttachReportUsingPostApiArg, ArsLinkReportUsingPostApiResponse, ArsLinkReportUsingPostApiArg, UploadTempAttachmentApiResponse, UploadTempAttachmentApiArg, LinkTempAttachmentsApiResponse, LinkTempAttachmentsApiArg, ArsGetAttachmentByUuidUsingGetApiResponse, ArsGetAttachmentByUuidUsingGetApiArg, ArsDeleteAttachmentUsingDeleteApiResponse, ArsDeleteAttachmentUsingDeleteApiArg, AttachmentsRest, EnumerationString, ServletInputStream, Locale, EnumerationLocale, BufferedReader, Annotation, Package, ModuleDescriptor, ModuleLayer, Module, ClassLoader, FilterRegistration, JspPropertyGroupDescriptor, TaglibDescriptor, JspConfigDescriptor, ServletRegistration, EnumerationServlet, SessionCookieConfig, ServletContext, ServletRequest, ServletOutputStream, PrintWriter, ServletResponse, AsyncContext, Cookie, HttpServletMapping, InputStream, Part, StringBuffer, HttpSessionContext, HttpSession, Principal, HttpServletRequest, AttachProcessingResponse, PaginRestResultAttachmentsRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        arsUploadAttachmentUsingPost: build.mutation<ArsUploadAttachmentUsingPostApiResponse, ArsUploadAttachmentUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments`,
                method: 'POST',
                body: queryArg.httpServletRequest,
                params: {
                    linkedId: queryArg.linkedId,
                    linkedTable: queryArg.linkedTable,
                    linkedColumn: queryArg.linkedColumn,
                },
            }),
        }),
        arsDownloadAllAttachmentsUsingGet: build.query<ArsDownloadAllAttachmentsUsingGetApiResponse, ArsDownloadAllAttachmentsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments/all`,
                params: {
                    tablename: queryArg.tablename,
                    id: queryArg.id,
                    subtableFiles: queryArg.subtableFiles,
                },
            }),
        }),
        getAttachments: build.query<GetAttachmentsApiResponse, GetAttachmentsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments/attachments`,
                params: {
                    linkedId: queryArg.linkedId,
                    linkedTable: queryArg.linkedTable,
                    linkedColumn: queryArg.linkedColumn,
                },
            }),
        }),
        arsCheckAttachDownloadUsingGet: build.query<ArsCheckAttachDownloadUsingGetApiResponse, ArsCheckAttachDownloadUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments/progress`,
                params: {
                    processid: queryArg.processid,
                },
            }),
        }),
        arsAttachReportUsingPost: build.mutation<ArsAttachReportUsingPostApiResponse, ArsAttachReportUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments/report`,
                method: 'POST',
                params: {
                    linkedId: queryArg.linkedId,
                    linkedTable: queryArg.linkedTable,
                    linkedColumn: queryArg.linkedColumn,
                    reportUri: queryArg.reportUri,
                    reportType: queryArg.reportType,
                },
            }),
        }),
        arsLinkReportUsingPost: build.mutation<ArsLinkReportUsingPostApiResponse, ArsLinkReportUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments/report/link`,
                method: 'POST',
                params: {
                    linkedId: queryArg.linkedId,
                    linkedTable: queryArg.linkedTable,
                    linkedColumn: queryArg.linkedColumn,
                    reportUri: queryArg.reportUri,
                    reportType: queryArg.reportType,
                },
            }),
        }),
        uploadTempAttachment: build.mutation<UploadTempAttachmentApiResponse, UploadTempAttachmentApiArg>({
            query: (queryArg) => ({ url: `/v1/api/attachments/temp`, method: 'POST', body: queryArg.httpServletRequest }),
        }),
        linkTempAttachments: build.mutation<LinkTempAttachmentsApiResponse, LinkTempAttachmentsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments/temp/link`,
                method: 'POST',
                params: {
                    linkedId: queryArg.linkedId,
                    linkedTable: queryArg.linkedTable,
                    linkedColumn: queryArg.linkedColumn,
                    fileIdentifier: queryArg.fileIdentifier,
                    fileName: queryArg.fileName,
                },
            }),
        }),
        arsGetAttachmentByUuidUsingGet: build.query<ArsGetAttachmentByUuidUsingGetApiResponse, ArsGetAttachmentByUuidUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/attachments/${queryArg.attachmentUuid}`,
                params: {
                    linkTable: queryArg.linkTable,
                },
            }),
        }),
        arsDeleteAttachmentUsingDelete: build.mutation<ArsDeleteAttachmentUsingDeleteApiResponse, ArsDeleteAttachmentUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/attachments/${queryArg.attachmentUuid}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
