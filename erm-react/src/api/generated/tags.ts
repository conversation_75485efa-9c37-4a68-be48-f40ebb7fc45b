import { TrsGetTagsUsingGetApiResponse, TrsGetTagsUsingGetApiArg, TrsCreateTagUsingPostApiResponse, TrsCreateTagUsingPostApiArg, TrsGetTagTypesUsingGetApiResponse, TrsGetTagTypesUsingGetApiArg, TrsCreateTagTypeUsingPostApiResponse, TrsCreateTagTypeUsingPostApiArg, TrsGetTagTypesUsingPostApiResponse, TrsGetTagTypesUsingPostApiArg, TrsUpdateTagTypeUsingPutApiResponse, TrsUpdateTagTypeUsingPutApiArg, TrsRemoveTagTypeUsingDeleteApiResponse, TrsRemoveTagTypeUsingDeleteApiArg, TrsGetTagsUsingPostApiResponse, TrsGetTagsUsingPostApiArg, TrsSuggestTagsUsingPostApiResponse, TrsSuggestTagsUsingPostApiArg, TrsGetTagTreeUsingGetApiResponse, TrsGetTagTreeUsingGetApiArg, TrsUpdateTagUsingPutApiResponse, TrsUpdateTagUsingPutApiArg, TrsRemoveTagUsingDeleteApiResponse, TrsRemoveTagUsingDeleteApiArg, TagTypeRest, TagRest, PaginRestResultTagTypeRest, ViewExpressionRest, FilterContextRest, PaginRestResultTagRest, TagTreeItemRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        trsGetTagsUsingGet: build.query<TrsGetTagsUsingGetApiResponse, TrsGetTagsUsingGetApiArg>({
            query: () => ({ url: `/v1/api/tags` }),
        }),
        trsCreateTagUsingPost: build.mutation<TrsCreateTagUsingPostApiResponse, TrsCreateTagUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/tags`, method: 'POST', body: queryArg.tagRest }),
        }),
        trsGetTagTypesUsingGet: build.query<TrsGetTagTypesUsingGetApiResponse, TrsGetTagTypesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/tags/category` }),
        }),
        trsCreateTagTypeUsingPost: build.mutation<TrsCreateTagTypeUsingPostApiResponse, TrsCreateTagTypeUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/tags/category`, method: 'POST', body: queryArg.tagTypeRest }),
        }),
        trsGetTagTypesUsingPost: build.mutation<TrsGetTagTypesUsingPostApiResponse, TrsGetTagTypesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/tags/category/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        trsUpdateTagTypeUsingPut: build.mutation<TrsUpdateTagTypeUsingPutApiResponse, TrsUpdateTagTypeUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/tags/category/${queryArg.id}`, method: 'PUT', body: queryArg.tagTypeRest }),
        }),
        trsRemoveTagTypeUsingDelete: build.mutation<TrsRemoveTagTypeUsingDeleteApiResponse, TrsRemoveTagTypeUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/tags/category/${queryArg.id}`, method: 'DELETE' }),
        }),
        trsGetTagsUsingPost: build.mutation<TrsGetTagsUsingPostApiResponse, TrsGetTagsUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/tags/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        trsSuggestTagsUsingPost: build.mutation<TrsSuggestTagsUsingPostApiResponse, TrsSuggestTagsUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/tags/suggest`,
                method: 'POST',
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    searchingText: queryArg.searchingText,
                },
            }),
        }),
        trsGetTagTreeUsingGet: build.query<TrsGetTagTreeUsingGetApiResponse, TrsGetTagTreeUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/tags/tree`,
                params: {
                    tagContext: queryArg.tagContext,
                    tagHierarchy: queryArg.tagHierarchy,
                },
            }),
        }),
        trsUpdateTagUsingPut: build.mutation<TrsUpdateTagUsingPutApiResponse, TrsUpdateTagUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/tags/${queryArg.id}`, method: 'PUT', body: queryArg.tagRest }),
        }),
        trsRemoveTagUsingDelete: build.mutation<TrsRemoveTagUsingDeleteApiResponse, TrsRemoveTagUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/tags/${queryArg.id}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
