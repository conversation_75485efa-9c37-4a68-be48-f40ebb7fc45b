import { TmrsiGetRegistersSummaryUsingGetApiResponse, TmrsiGetRegistersSummaryUsingGetApiArg, RegisterSummaryDto, StackTraceElement, Throwable, HttpNotFound, Forbidden, InternalServerError } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        tmrsiGetRegistersSummaryUsingGet: build.query<TmrsiGetRegistersSummaryUsingGetApiResponse, TmrsiGetRegistersSummaryUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iregisters/summary`,
                params: {
                    applicationId: queryArg.applicationId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
