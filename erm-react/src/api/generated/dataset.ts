import { GetExportContentGetApiResponse, GetExportContentGetApiArg, GetExportProgressGetApiResponse, GetExportProgressGetApiArg, ImportRegisterEntriesPostApiResponse, ImportRegisterEntriesPostApiArg, GetImportProgressGetApiResponse, GetImportProgressGetApiArg, ValidateImportRegisterEntriesPostApiResponse, ValidateImportRegisterEntriesPostApiArg, ExportRegisterEntriesPostApiResponse, ExportRegisterEntriesPostApiArg, ImportRegisterEntriesPost1ApiResponse, ImportRegisterEntriesPost1ApiArg, GetTemplateGetApiResponse, GetTemplateGetApiArg, ValidateImportRegisterEntriesPost1ApiResponse, ValidateImportRegisterEntriesPost1ApiArg, ExportProgressResponse, InputStream, Resource, MultipartFile, DataSetImportRequest, ProcessingError, StackTraceElement, Throwable, ImportException, ProcessingErrorWithStageTypeImportException, ImportWarningMessage, ProcessingErrorWithStageTypeImportWarningMessage, RunningProcess, RunningProcessProgress, ValidationException, ProcessingErrorWithStageTypeValidationException, ValidationInfoException, ProcessingErrorWithStageTypeValidationInfoException, ProgressResponse, DataSetExportConfiguration, ViewExpressionRest, DataSetExportRequest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        getExportContentGet: build.query<GetExportContentGetApiResponse, GetExportContentGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/dataset/export/content`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        getExportProgressGet: build.query<GetExportProgressGetApiResponse, GetExportProgressGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/dataset/export/progress`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        importRegisterEntriesPost: build.mutation<ImportRegisterEntriesPostApiResponse, ImportRegisterEntriesPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/dataset/import`, method: 'POST', body: queryArg.dataSetImportRequest }),
        }),
        getImportProgressGet: build.query<GetImportProgressGetApiResponse, GetImportProgressGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/dataset/import/progress`,
                params: {
                    importid: queryArg.importid,
                },
            }),
        }),
        validateImportRegisterEntriesPost: build.mutation<ValidateImportRegisterEntriesPostApiResponse, ValidateImportRegisterEntriesPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/dataset/validate-import`, method: 'POST', body: queryArg.dataSetImportRequest }),
        }),
        exportRegisterEntriesPost: build.mutation<ExportRegisterEntriesPostApiResponse, ExportRegisterEntriesPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/dataset/${queryArg.regId}/export`,
                method: 'POST',
                body: queryArg.dataSetExportRequest,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    myTaskStates: queryArg.myTaskStates,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    isSuggestion: queryArg.isSuggestion,
                    stateId: queryArg.stateId,
                    excludeSystemFields: queryArg.excludeSystemFields,
                },
            }),
        }),
        importRegisterEntriesPost1: build.mutation<ImportRegisterEntriesPost1ApiResponse, ImportRegisterEntriesPost1ApiArg>({
            query: (queryArg) => ({ url: `/v1/api/dataset/${queryArg.regId}/import`, method: 'POST', body: queryArg.dataSetImportRequest }),
        }),
        getTemplateGet: build.query<GetTemplateGetApiResponse, GetTemplateGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/dataset/${queryArg.regId}/template`,
                params: {
                    suggestedFileName: queryArg.suggestedFileName,
                },
            }),
        }),
        validateImportRegisterEntriesPost1: build.mutation<ValidateImportRegisterEntriesPost1ApiResponse, ValidateImportRegisterEntriesPost1ApiArg>({
            query: (queryArg) => ({ url: `/v1/api/dataset/${queryArg.regId}/validate-import`, method: 'POST', body: queryArg.dataSetImportRequest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
