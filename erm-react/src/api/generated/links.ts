import { FlcGetFrameworkNodeLinksCountDetailUsingGetApiResponse, FlcGetFrameworkNodeLinksCountDetailUsingGetApiArg, FlcGetLinkedEntityCountsUsingGetApiResponse, FlcGetLinkedEntityCountsUsingGetApiArg, FlcUpdateFrameworkExternalLinkingUsingPutApiResponse, FlcUpdateFrameworkExternalLinkingUsingPutApiArg, FlcGetFrameworkInternalLinkingUsingGetApiResponse, FlcGetFrameworkInternalLinkingUsingGetApiArg, FlcCreateFrameworkInternalLinkingUsingPostApiResponse, FlcCreateFrameworkInternalLinkingUsingPostApiArg, FlcDeleteFrameworkInternalLinkingUsingDeleteApiResponse, FlcDeleteFrameworkInternalLinkingUsingDeleteApiArg, FlcGetLinkedNodesUsingGetApiResponse, FlcGetLinkedNodesUsingGetApiArg, FlcGetLinkedEntityListUsingGetApiResponse, FlcGetLinkedEntityListUsingGetApiArg, FlcGetFrameworkNodesLinksCountsUsingGetApiResponse, FlcGetFrameworkNodesLinksCountsUsingGetApiArg, FrameworkNodeLinksCountDetail, NodeLinkCountDetail, NodeLinkCountDetailRead, FrameworkNodeLinksCountDetailRead, LinkedDetails, LinkedDetailsRead, ExternalNodeLink, ExternalNodeLinkRead, NodeLinkDetail, NodeLinkDetailRead, NodeLinkTarget, NodeLinkTargetRead, FrameworkStub, FrameworkStubRead, FrameworkRegister, FrameworkRegisterRead, LinkedEntity, LinkedEntityRead, FrameworkNodesLinksCounts, NodeLinksCount, FrameworkNodesLinksCountsRead } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        flcGetFrameworkNodeLinksCountDetailUsingGet: build.query<
            FlcGetFrameworkNodeLinksCountDetailUsingGetApiResponse,
            FlcGetFrameworkNodeLinksCountDetailUsingGetApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/links/node/${queryArg.id}/counts`,
                params: {
                    register: queryArg.register,
                },
            }),
        }),
        flcGetLinkedEntityCountsUsingGet: build.query<FlcGetLinkedEntityCountsUsingGetApiResponse, FlcGetLinkedEntityCountsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/links/${queryArg.id}/counts`,
                params: {
                    register: queryArg.register,
                    framework: queryArg.framework,
                },
            }),
        }),
        flcUpdateFrameworkExternalLinkingUsingPut: build.mutation<
            FlcUpdateFrameworkExternalLinkingUsingPutApiResponse,
            FlcUpdateFrameworkExternalLinkingUsingPutApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/links/${queryArg.id}/external`, method: 'PUT', body: queryArg.body }),
        }),
        flcGetFrameworkInternalLinkingUsingGet: build.query<FlcGetFrameworkInternalLinkingUsingGetApiResponse, FlcGetFrameworkInternalLinkingUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/links/${queryArg.id}/internal` }),
        }),
        flcCreateFrameworkInternalLinkingUsingPost: build.mutation<
            FlcCreateFrameworkInternalLinkingUsingPostApiResponse,
            FlcCreateFrameworkInternalLinkingUsingPostApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/links/${queryArg.id}/internal`, method: 'POST', body: queryArg.body }),
        }),
        flcDeleteFrameworkInternalLinkingUsingDelete: build.mutation<
            FlcDeleteFrameworkInternalLinkingUsingDeleteApiResponse,
            FlcDeleteFrameworkInternalLinkingUsingDeleteApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/links/${queryArg.id}/internal`, method: 'DELETE' }),
        }),
        flcGetLinkedNodesUsingGet: build.query<FlcGetLinkedNodesUsingGetApiResponse, FlcGetLinkedNodesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/links/${queryArg.id}/linked-records`,
                params: {
                    register: queryArg.register,
                    framework: queryArg.framework,
                },
            }),
        }),
        flcGetLinkedEntityListUsingGet: build.query<FlcGetLinkedEntityListUsingGetApiResponse, FlcGetLinkedEntityListUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/links/${queryArg.id}/list` }),
        }),
        flcGetFrameworkNodesLinksCountsUsingGet: build.query<FlcGetFrameworkNodesLinksCountsUsingGetApiResponse, FlcGetFrameworkNodesLinksCountsUsingGetApiArg>(
            {
                query: (queryArg) => ({
                    url: `/v1/api/links/${queryArg.id}/nodes/counts`,
                    params: {
                        register: queryArg.register,
                    },
                }),
            },
        ),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
