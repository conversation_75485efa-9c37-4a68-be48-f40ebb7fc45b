import { BursCreateBuUsingPostApiResponse, BursCreateBuUsingPostApiArg, BursGetCalendarsUsingGetApiResponse, BursGetCalendarsUsingGetApiArg, BursGetCountriesUsingGetApiResponse, BursGetCountriesUsingGetApiArg, BursGetRootBusUsingGetApiResponse, BursGetRootBusUsingGetApiArg, BursGetRootSimpleUsingGetApiResponse, BursGetRootSimpleUsingGetApiArg, BursGetRootTreeUsingGetApiResponse, BursGetRootTreeUsingGetApiArg, BursFilterBusinessUnitsUsingGetApiResponse, BursFilterBusinessUnitsUsingGetApiArg, FilterBusinessUnitsApiResponse, FilterBusinessUnitsApiArg, BursGetStatesUsingGetApiResponse, BursGetStatesUsingGetApiArg, BursGetBusinessUnitsByIdsUsingPostApiResponse, BursGetBusinessUnitsByIdsUsingPostApiArg, BursGetBuUsingGetApiResponse, BursGetBuUsingGetApiArg, BursUpdateBuUsingPutApiResponse, BursUpdateBuUsingPutApiArg, BursArchiveBuUsingPutApiResponse, BursArchiveBuUsingPutApiArg, BursAddNewChildrenBusinessUnitsUsingPostApiResponse, BursAddNewChildrenBusinessUnitsUsingPostApiArg, BursGetAllChildrenIdsUsingGetApiResponse, BursGetAllChildrenIdsUsingGetApiArg, BursGetBuReferencesUsingGetApiResponse, BursGetBuReferencesUsingGetApiArg, BursRestoreBuUsingPutApiResponse, BursRestoreBuUsingPutApiArg, BursGetBuSimpleUsingGetApiResponse, BursGetBuSimpleUsingGetApiArg, BursGetBuTreeUsingGetApiResponse, BursGetBuTreeUsingGetApiArg, IdWithNameRest, TagTypeRest, TagRest, BusinessUnitRest, BusinessUnitSimpleRest, BusinessUnitSimpleTreeFilteredRest, PaginRestResultBusinessUnitSimpleTreeFilteredRest, ViewExpressionRest, FilterContextRest, RestListBusinessUnitRest, BuReferencesRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        bursCreateBuUsingPost: build.mutation<BursCreateBuUsingPostApiResponse, BursCreateBuUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits`, method: 'POST', body: queryArg.businessUnitRest }),
        }),
        bursGetCalendarsUsingGet: build.query<BursGetCalendarsUsingGetApiResponse, BursGetCalendarsUsingGetApiArg>({
            query: () => ({ url: `/v1/api/businessunits/calendars` }),
        }),
        bursGetCountriesUsingGet: build.query<BursGetCountriesUsingGetApiResponse, BursGetCountriesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/businessunits/countries` }),
        }),
        bursGetRootBusUsingGet: build.query<BursGetRootBusUsingGetApiResponse, BursGetRootBusUsingGetApiArg>({
            query: () => ({ url: `/v1/api/businessunits/root` }),
        }),
        bursGetRootSimpleUsingGet: build.query<BursGetRootSimpleUsingGetApiResponse, BursGetRootSimpleUsingGetApiArg>({
            query: () => ({ url: `/v1/api/businessunits/root/simple` }),
        }),
        bursGetRootTreeUsingGet: build.query<BursGetRootTreeUsingGetApiResponse, BursGetRootTreeUsingGetApiArg>({
            query: () => ({ url: `/v1/api/businessunits/root/tree` }),
        }),
        bursFilterBusinessUnitsUsingGet: build.query<BursFilterBusinessUnitsUsingGetApiResponse, BursFilterBusinessUnitsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/businessunits/search`,
                params: {
                    value: queryArg.value,
                },
            }),
        }),
        filterBusinessUnits: build.mutation<FilterBusinessUnitsApiResponse, FilterBusinessUnitsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/businessunits/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                },
            }),
        }),
        bursGetStatesUsingGet: build.query<BursGetStatesUsingGetApiResponse, BursGetStatesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/businessunits/states` }),
        }),
        bursGetBusinessUnitsByIdsUsingPost: build.mutation<BursGetBusinessUnitsByIdsUsingPostApiResponse, BursGetBusinessUnitsByIdsUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/tree`, method: 'POST', body: queryArg.body }),
        }),
        bursGetBuUsingGet: build.query<BursGetBuUsingGetApiResponse, BursGetBuUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}` }),
        }),
        bursUpdateBuUsingPut: build.mutation<BursUpdateBuUsingPutApiResponse, BursUpdateBuUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}`, method: 'PUT', body: queryArg.businessUnitRest }),
        }),
        bursArchiveBuUsingPut: build.mutation<BursArchiveBuUsingPutApiResponse, BursArchiveBuUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}/archive`, method: 'PUT' }),
        }),
        bursAddNewChildrenBusinessUnitsUsingPost: build.mutation<
            BursAddNewChildrenBusinessUnitsUsingPostApiResponse,
            BursAddNewChildrenBusinessUnitsUsingPostApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}/child`, method: 'POST', body: queryArg.restListBusinessUnitRest }),
        }),
        bursGetAllChildrenIdsUsingGet: build.query<BursGetAllChildrenIdsUsingGetApiResponse, BursGetAllChildrenIdsUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}/ids` }),
        }),
        bursGetBuReferencesUsingGet: build.query<BursGetBuReferencesUsingGetApiResponse, BursGetBuReferencesUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}/refs` }),
        }),
        bursRestoreBuUsingPut: build.mutation<BursRestoreBuUsingPutApiResponse, BursRestoreBuUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}/restore`, method: 'PUT' }),
        }),
        bursGetBuSimpleUsingGet: build.query<BursGetBuSimpleUsingGetApiResponse, BursGetBuSimpleUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}/simple` }),
        }),
        bursGetBuTreeUsingGet: build.query<BursGetBuTreeUsingGetApiResponse, BursGetBuTreeUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/businessunits/${queryArg.businessunitId}/tree` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
