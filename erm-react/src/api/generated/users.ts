import { PursGetUsersUsingGetApiResponse, PursGetUsersUsingGetApiArg, PursCreateUserUsingPostApiResponse, PursCreateUserUsingPostApiArg, PursGetCurrentUserUsingGetApiResponse, PursGetCurrentUserUsingGetApiArg, PursUpdateCurrentUserUsingPutApiResponse, PursUpdateCurrentUserUsingPutApiArg, PursGetUserPermissionsUsingGetApiResponse, PursGetUserPermissionsUsingGetApiArg, GetUsersApiResponse, GetUsersApiArg, PursGetUserUsingGetApiResponse, PursGetUserUsingGetApiArg, PursUpdateUserUsingPutApiResponse, PursUpdateUserUsingPutApiArg, PursDeleteUserUsingDeleteApiResponse, PursDeleteUserUsingDeleteApiArg, PursGetUserHistoryUsingPostApiResponse, PursGetUserHistoryUsingPostApiArg, PursLockUserUsingPutApiResponse, PursLockUserUsingPutApiArg, PursDeleteManagerFromUserUsingDeleteApiResponse, PursDeleteManagerFromUserUsingDeleteApiArg, PursGetUserPermissionsUsingGet1ApiResponse, PursGetUserPermissionsUsingGet1ApiArg, PursRestoreUserUsingPostApiResponse, PursRestoreUserUsingPostApiArg, PursAddRoleToUserUsingPutApiResponse, PursAddRoleToUserUsingPutApiArg, PursDeleteRoleFromUserUsingDeleteApiResponse, PursDeleteRoleFromUserUsingDeleteApiArg, PursAddBuToUserUsingPutApiResponse, PursAddBuToUserUsingPutApiArg, PursDeleteBuFromUserUsingDeleteApiResponse, PursDeleteBuFromUserUsingDeleteApiArg, PursUnlockUserUsingPutApiResponse, PursUnlockUserUsingPutApiArg, IdWithNameRest, Timestamp, ProtechtUserRest, PermissionRest, PaginRestResultProtechtUserRest, ViewExpressionRest, UserFilterContext } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        pursGetUsersUsingGet: build.query<PursGetUsersUsingGetApiResponse, PursGetUsersUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    permissionFilterId: queryArg.permissionFilterId,
                    roleFilterId: queryArg.roleFilterId,
                },
            }),
        }),
        pursCreateUserUsingPost: build.mutation<PursCreateUserUsingPostApiResponse, PursCreateUserUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users`, method: 'POST', body: queryArg.protechtUserRest }),
        }),
        pursGetCurrentUserUsingGet: build.query<PursGetCurrentUserUsingGetApiResponse, PursGetCurrentUserUsingGetApiArg>({
            query: () => ({ url: `/v1/api/users/me` }),
        }),
        pursUpdateCurrentUserUsingPut: build.mutation<PursUpdateCurrentUserUsingPutApiResponse, PursUpdateCurrentUserUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users/me`,
                method: 'PUT',
                body: queryArg.protechtUserRest,
                params: {
                    includeBUs: queryArg.includeBUs,
                },
            }),
        }),
        pursGetUserPermissionsUsingGet: build.query<PursGetUserPermissionsUsingGetApiResponse, PursGetUserPermissionsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users/me/permissions`,
                params: {
                    filter: queryArg.filter,
                },
            }),
        }),
        getUsers: build.query<GetUsersApiResponse, GetUsersApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users/search`,
                method: 'POST',
                body: queryArg.userFilterContext,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    permissionFilterId: queryArg.permissionFilterId,
                    roleFilterId: queryArg.roleFilterId,
                },
            }),
        }),
        pursGetUserUsingGet: build.query<PursGetUserUsingGetApiResponse, PursGetUserUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}` }),
        }),
        pursUpdateUserUsingPut: build.mutation<PursUpdateUserUsingPutApiResponse, PursUpdateUserUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users/${queryArg.userId}`,
                method: 'PUT',
                body: queryArg.protechtUserRest,
                params: {
                    includeBUs: queryArg.includeBUs,
                },
            }),
        }),
        pursDeleteUserUsingDelete: build.mutation<PursDeleteUserUsingDeleteApiResponse, PursDeleteUserUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}`, method: 'DELETE' }),
        }),
        pursGetUserHistoryUsingPost: build.mutation<PursGetUserHistoryUsingPostApiResponse, PursGetUserHistoryUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users/${queryArg.userId}/history`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        pursLockUserUsingPut: build.mutation<PursLockUserUsingPutApiResponse, PursLockUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/lock`, method: 'PUT' }),
        }),
        pursDeleteManagerFromUserUsingDelete: build.mutation<PursDeleteManagerFromUserUsingDeleteApiResponse, PursDeleteManagerFromUserUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/manager`, method: 'DELETE' }),
        }),
        pursGetUserPermissionsUsingGet1: build.query<PursGetUserPermissionsUsingGet1ApiResponse, PursGetUserPermissionsUsingGet1ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/users/${queryArg.userId}/permissions`,
                params: {
                    filter: queryArg.filter,
                },
            }),
        }),
        pursRestoreUserUsingPost: build.mutation<PursRestoreUserUsingPostApiResponse, PursRestoreUserUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/restore`, method: 'POST' }),
        }),
        pursAddRoleToUserUsingPut: build.mutation<PursAddRoleToUserUsingPutApiResponse, PursAddRoleToUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/role/${queryArg.roleId}`, method: 'PUT' }),
        }),
        pursDeleteRoleFromUserUsingDelete: build.mutation<PursDeleteRoleFromUserUsingDeleteApiResponse, PursDeleteRoleFromUserUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/role/${queryArg.roleId}`, method: 'DELETE' }),
        }),
        pursAddBuToUserUsingPut: build.mutation<PursAddBuToUserUsingPutApiResponse, PursAddBuToUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/unit/${queryArg.unitId}`, method: 'PUT', body: queryArg.body }),
        }),
        pursDeleteBuFromUserUsingDelete: build.mutation<PursDeleteBuFromUserUsingDeleteApiResponse, PursDeleteBuFromUserUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/unit/${queryArg.unitId}`, method: 'DELETE', body: queryArg.body }),
        }),
        pursUnlockUserUsingPut: build.mutation<PursUnlockUserUsingPutApiResponse, PursUnlockUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/users/${queryArg.userId}/unlock`, method: 'PUT' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
