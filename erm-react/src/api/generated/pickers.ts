import { PrsGetConsequencesUsingGetApiResponse, PrsGetConsequencesUsingGetApiArg, PrsGetCountriesUsingPostApiResponse, PrsGetCountriesUsingPostApiArg, PrsGetLikelihoodsUsingGetApiResponse, PrsGetLikelihoodsUsingGetApiArg, PrsGetStatesUsingPostApiResponse, PrsGetStatesUsingPostApiArg, ScaleItemRest, IdWithNameRest, PaginRestResultIdWithNameRest, ViewExpressionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        prsGetConsequencesUsingGet: build.query<PrsGetConsequencesUsingGetApiResponse, PrsGetConsequencesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/pickers/consequences`,
                params: {
                    columnName: queryArg.columnName,
                },
            }),
        }),
        prsGetCountriesUsingPost: build.mutation<PrsGetCountriesUsingPostApiResponse, PrsGetCountriesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/pickers/countries`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        prsGetLikelihoodsUsingGet: build.query<PrsGetLikelihoodsUsingGetApiResponse, PrsGetLikelihoodsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/pickers/likelihoods`,
                params: {
                    columnName: queryArg.columnName,
                },
            }),
        }),
        prsGetStatesUsingPost: build.mutation<PrsGetStatesUsingPostApiResponse, PrsGetStatesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/pickers/states`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
