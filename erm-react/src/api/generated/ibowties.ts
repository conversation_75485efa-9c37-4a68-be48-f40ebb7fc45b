import { BtciGetDiagramsUsingPostApiResponse, BtciGetDiagramsUsingPostApiArg, IdWithNameRest, Timestamp, JsonNode, BowTieLinkNotificationRest, BowTieDiagramRest, PaginRestResultBowTieDiagramRest, ViewExpressionRest, FilterContextRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        btciGetDiagramsUsingPost: build.mutation<BtciGetDiagramsUsingPostApiResponse, BtciGetDiagramsUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibowties/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    page: queryArg.page,
                    size: queryArg.size,
                    viewId: queryArg.viewId,
                    'sort-by': queryArg['sort-by'],
                    'sort-dir': queryArg['sort-dir'],
                    'group-by': queryArg['group-by'],
                    'tag-type': queryArg['tag-type'],
                    'filter-by': queryArg['filter-by'],
                    'filter-value': queryArg['filter-value'],
                    tags: queryArg.tags,
                    'tag-operator': queryArg['tag-operator'],
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
