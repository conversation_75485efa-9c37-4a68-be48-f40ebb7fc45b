import { SrsGetAllSettingsUsingGetApiResponse, SrsGetAllSettingsUsingGetApiArg, SrsUpdateSettingUsingPatchApiResponse, SrsUpdateSettingUsingPatchApiArg, ProtechtDetailsRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        srsGetAllSettingsUsingGet: build.query<SrsGetAllSettingsUsingGetApiResponse, SrsGetAllSettingsUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/settings/all/${queryArg.category}` }),
        }),
        srsUpdateSettingUsingPatch: build.mutation<SrsUpdateSettingUsingPatchApiResponse, SrsUpdateSettingUsingPatchApiArg>({
            query: (queryArg) => ({ url: `/v1/api/settings/${queryArg.key}`, method: 'PATCH', body: queryArg.protechtDetailsRest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
