import { MtrsGetMyTasksUsingGetApiResponse, MtrsGetMyTasksUsingGetApiArg, MyTasksDataRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        mtrsGetMyTasksUsingGet: build.query<MtrsGetMyTasksUsingGetApiResponse, MtrsGetMyTasksUsingGetApiArg>({
            query: () => ({ url: `/v1/api/tasks/my` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
