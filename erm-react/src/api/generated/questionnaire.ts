import { QrsGetQuestionnairesByMetricUsingPostApiResponse, QrsGetQuestionnairesByMetricUsingPostApiArg, QrsGetQuestionnaireRegistersUsingPostApiResponse, QrsGetQuestionnaireRegistersUsingPostApiArg, QrsGetQuestionnaireRegistersGetUsingGetApiResponse, QrsGetQuestionnaireRegistersGetUsingGetApiArg, QrsAssigneMultipleQuestionnairesToVendorUsingPostApiResponse, QrsAssigneMultipleQuestionnairesToVendorUsingPostApiArg, QrsAssignMultipleQuestionnairesToMultipleVendorsUsingPostApiResponse, QrsAssignMultipleQuestionnairesToMultipleVendorsUsingPostApiArg, QrsAssignQuestionnaireUsingPostApiResponse, QrsAssignQuestionnaireUsingPostApiArg, QrsGetQuestionnairesUsingPostApiResponse, QrsGetQuestionnairesUsingPostApiArg, QrsGetQuestionnairesGetUsingGetApiResponse, QrsGetQuestionnairesGetUsingGetApiArg, QrsBulkAssignQuestionnaireUsingPostApiResponse, QrsBulkAssignQuestionnaireUsingPostApiArg, QrsDeleteQuestionnaireUsingDeleteApiResponse, QrsDeleteQuestionnaireUsingDeleteApiArg, QrsGetQuestionnairesForVendorUserUsingPostApiResponse, QrsGetQuestionnairesForVendorUserUsingPostApiArg, IdWithNameRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest, QuestionnaireDataRest, PaginRestResultQuestionnaireDataRest, ViewExpressionRest, ContractMappingRestField, ContractMappingRest, GlobalListRest, FormulaComponentRest, FieldRest, FieldActionRest, FieldConditionRest, FieldRuleStatusRest, FieldRuleRest, FieldRuleSetRest, RiskMatrixRatingRest, RiskMatrixCellRest, ScaleItemRest, ScaleRest, ScaleSetRest, StyleRest, SectionRest, PermissionRest, IdWithNameAndStatusRest, RoleRest, RegisterStateTransitionRest, RegisterStateRest, RegisterStateDefinitionRest, TaskColumnRest, RestParamRest, RestResponseActionConditionRest, RestResponseTranslationMapRest, RestResponseActionFieldMapRest, RestResponseActionRest, RestResponseHandlerRest, WorkflowTemplateRest, WorkFlowApiAction, WorkflowCalendarEventAction, WorkFlowConditionField, WorkFlowCondition, WorkFlowEmailAction, WorkFlowSmsAction, WorkFlowStatus, WorkFlowRuleRest, TableMetadataRest, PaginRestResultTableMetadataRest, FilterContextRest, IdWithDueDate, QuestionnairesVendorsDataRest, QuestionnaireAssignment, QuestionnaireVendorUserDataRest, PaginRestResultQuestionnaireVendorUserDataRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        qrsGetQuestionnairesByMetricUsingPost: build.mutation<QrsGetQuestionnairesByMetricUsingPostApiResponse, QrsGetQuestionnairesByMetricUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/questionnaire/metric-search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    vendorId: queryArg.vendorId,
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    metricId: queryArg.metricId,
                },
            }),
        }),
        qrsGetQuestionnaireRegistersUsingPost: build.mutation<QrsGetQuestionnaireRegistersUsingPostApiResponse, QrsGetQuestionnaireRegistersUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/questionnaire/registers/${queryArg.questionnaireGroupId}`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    skipSectionData: queryArg.skipSectionData,
                },
            }),
        }),
        qrsGetQuestionnaireRegistersGetUsingGet: build.query<QrsGetQuestionnaireRegistersGetUsingGetApiResponse, QrsGetQuestionnaireRegistersGetUsingGetApiArg>(
            {
                query: (queryArg) => ({
                    url: `/v1/api/questionnaire/registers/${queryArg.questionnaireGroupId}/all`,
                    params: {
                        offset: queryArg.offset,
                        limit: queryArg.limit,
                        orderBy: queryArg.orderBy,
                        orderType: queryArg.orderType,
                        groupBy: queryArg.groupBy,
                        viewId: queryArg.viewId,
                        skipSectionData: queryArg.skipSectionData,
                        payload: queryArg.payload,
                    },
                }),
            },
        ),
        qrsAssigneMultipleQuestionnairesToVendorUsingPost: build.mutation<
            QrsAssigneMultipleQuestionnairesToVendorUsingPostApiResponse,
            QrsAssigneMultipleQuestionnairesToVendorUsingPostApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/questionnaire/vendor/${queryArg.vendorId}`, method: 'POST', body: queryArg.body }),
        }),
        qrsAssignMultipleQuestionnairesToMultipleVendorsUsingPost: build.mutation<
            QrsAssignMultipleQuestionnairesToMultipleVendorsUsingPostApiResponse,
            QrsAssignMultipleQuestionnairesToMultipleVendorsUsingPostApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/questionnaire/vendors`, method: 'POST', body: queryArg.questionnairesVendorsDataRest }),
        }),
        qrsAssignQuestionnaireUsingPost: build.mutation<QrsAssignQuestionnaireUsingPostApiResponse, QrsAssignQuestionnaireUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/questionnaire/${queryArg.registerId}`, method: 'POST', body: queryArg.questionnaireAssignment }),
        }),
        qrsGetQuestionnairesUsingPost: build.mutation<QrsGetQuestionnairesUsingPostApiResponse, QrsGetQuestionnairesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/questionnaire/${queryArg.registerId}/search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    vendorId: queryArg.vendorId,
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                },
            }),
        }),
        qrsGetQuestionnairesGetUsingGet: build.query<QrsGetQuestionnairesGetUsingGetApiResponse, QrsGetQuestionnairesGetUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/questionnaire/${queryArg.registerId}/search/all`,
                params: {
                    vendorId: queryArg.vendorId,
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    payload: queryArg.payload,
                },
            }),
        }),
        qrsBulkAssignQuestionnaireUsingPost: build.mutation<QrsBulkAssignQuestionnaireUsingPostApiResponse, QrsBulkAssignQuestionnaireUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/questionnaire/${queryArg.registerId}/vendor`, method: 'POST', body: queryArg.body }),
        }),
        qrsDeleteQuestionnaireUsingDelete: build.mutation<QrsDeleteQuestionnaireUsingDeleteApiResponse, QrsDeleteQuestionnaireUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/questionnaire/${queryArg.registerId}/${queryArg.entryId}`, method: 'DELETE' }),
        }),
        qrsGetQuestionnairesForVendorUserUsingPost: build.mutation<
            QrsGetQuestionnairesForVendorUserUsingPostApiResponse,
            QrsGetQuestionnairesForVendorUserUsingPostApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/questionnaire/${queryArg.registerId}/${queryArg.vendorUserId}/search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
