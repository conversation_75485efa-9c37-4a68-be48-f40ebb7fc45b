import { VmrsGetRepositoryHierarchyUsingGetApiResponse, VmrsGetRepositoryHierarchyUsingGetApiArg, VmrsGetDefinitionUsingGetApiResponse, VmrsGetDefinitionUsingGetApiArg, VmrsUpdateDiagramDefinitionUsingPutApiResponse, VmrsUpdateDiagramDefinitionUsingPutApiArg, VmrsGetVendorRegisterPermissionsUsingGetApiResponse, VmrsGetVendorRegisterPermissionsUsingGetApiArg, VmrsUpdateVendorRegisterPermissionsUsingPutApiResponse, VmrsUpdateVendorRegisterPermissionsUsingPutApiArg, VrsCreateVendorEntryUsingPostApiResponse, VrsCreateVendorEntryUsingPostApiArg, VrsGetVendorEntryUsingGetApiResponse, VrsGetVendorEntryUsingGetApiArg, VrsUpdateVendorNameUsingPutApiResponse, VrsUpdateVendorNameUsingPutApiArg, VrsCreateRegisterEntryUsingPostApiResponse, VrsCreateRegisterEntryUsingPostApiArg, VrsCheckUniqueVendorUsingPostApiResponse, VrsCheckUniqueVendorUsingPostApiArg, VursCreateVendorUserUsingPostApiResponse, VursCreateVendorUserUsingPostApiArg, VursUpdateLimitedCurrentUserPropertiesUsingPutApiResponse, VursUpdateLimitedCurrentUserPropertiesUsingPutApiArg, VursUpdateCurrentUserPasswordUsingPutApiResponse, VursUpdateCurrentUserPasswordUsingPutApiArg, VursSearchUsersUsingPostApiResponse, VursSearchUsersUsingPostApiArg, VursGetUserUsingGetApiResponse, VursGetUserUsingGetApiArg, VursUpdateUserUsingPutApiResponse, VursUpdateUserUsingPutApiArg, VursDeleteUserUsingDeleteApiResponse, VursDeleteUserUsingDeleteApiArg, VursLockUserUsingPutApiResponse, VursLockUserUsingPutApiArg, VursUnlockUserUsingPutApiResponse, VursUnlockUserUsingPutApiArg, RepositoryRest, VendorManagementRegister, VendorManagementDefinition, SectionPermission, TransitionPermission, VendorManagementRegisterPermissions, IdWithNameRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest, Timestamp, ProtechtUserRest, LimitedProtechtUserRest, OldAndNewPasswordsRest, PaginRestResultProtechtUserRest, ViewExpressionRest, FilterContextRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        vmrsGetRepositoryHierarchyUsingGet: build.query<VmrsGetRepositoryHierarchyUsingGetApiResponse, VmrsGetRepositoryHierarchyUsingGetApiArg>({
            query: () => ({ url: `/v1/api/vendor/config/dashboards` }),
        }),
        vmrsGetDefinitionUsingGet: build.query<VmrsGetDefinitionUsingGetApiResponse, VmrsGetDefinitionUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/vendor/config/definition`,
                params: {
                    extended: queryArg.extended,
                },
            }),
        }),
        vmrsUpdateDiagramDefinitionUsingPut: build.mutation<VmrsUpdateDiagramDefinitionUsingPutApiResponse, VmrsUpdateDiagramDefinitionUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/config/definition`, method: 'PUT', body: queryArg.vendorManagementDefinition }),
        }),
        vmrsGetVendorRegisterPermissionsUsingGet: build.query<
            VmrsGetVendorRegisterPermissionsUsingGetApiResponse,
            VmrsGetVendorRegisterPermissionsUsingGetApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/vendor/config/permissions/${queryArg.regId}` }),
        }),
        vmrsUpdateVendorRegisterPermissionsUsingPut: build.mutation<
            VmrsUpdateVendorRegisterPermissionsUsingPutApiResponse,
            VmrsUpdateVendorRegisterPermissionsUsingPutApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/vendor/config/permissions/${queryArg.regId}`,
                method: 'PUT',
                body: queryArg.vendorManagementRegisterPermissions,
            }),
        }),
        vrsCreateVendorEntryUsingPost: build.mutation<VrsCreateVendorEntryUsingPostApiResponse, VrsCreateVendorEntryUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/entry`, method: 'POST', body: queryArg.body }),
        }),
        vrsGetVendorEntryUsingGet: build.query<VrsGetVendorEntryUsingGetApiResponse, VrsGetVendorEntryUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/vendor/entry/${queryArg.vendorId}`,
                params: {
                    tableName: queryArg.tableName,
                },
            }),
        }),
        vrsUpdateVendorNameUsingPut: build.mutation<VrsUpdateVendorNameUsingPutApiResponse, VrsUpdateVendorNameUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/name`, method: 'PUT', body: queryArg.idWithNameRest }),
        }),
        vrsCreateRegisterEntryUsingPost: build.mutation<VrsCreateRegisterEntryUsingPostApiResponse, VrsCreateRegisterEntryUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/register`, method: 'POST', body: queryArg.body }),
        }),
        vrsCheckUniqueVendorUsingPost: build.mutation<VrsCheckUniqueVendorUsingPostApiResponse, VrsCheckUniqueVendorUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/search/unique`, method: 'POST', body: queryArg.body }),
        }),
        vursCreateVendorUserUsingPost: build.mutation<VursCreateVendorUserUsingPostApiResponse, VursCreateVendorUserUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/users`, method: 'POST', body: queryArg.protechtUserRest }),
        }),
        vursUpdateLimitedCurrentUserPropertiesUsingPut: build.mutation<
            VursUpdateLimitedCurrentUserPropertiesUsingPutApiResponse,
            VursUpdateLimitedCurrentUserPropertiesUsingPutApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/vendor/users/me`, method: 'PUT', body: queryArg.limitedProtechtUserRest }),
        }),
        vursUpdateCurrentUserPasswordUsingPut: build.mutation<VursUpdateCurrentUserPasswordUsingPutApiResponse, VursUpdateCurrentUserPasswordUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/users/me/password`, method: 'PUT', body: queryArg.oldAndNewPasswordsRest }),
        }),
        vursSearchUsersUsingPost: build.mutation<VursSearchUsersUsingPostApiResponse, VursSearchUsersUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/vendor/users/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    vendorId: queryArg.vendorId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    permissionFilterId: queryArg.permissionFilterId,
                    roleFilterId: queryArg.roleFilterId,
                },
            }),
        }),
        vursGetUserUsingGet: build.query<VursGetUserUsingGetApiResponse, VursGetUserUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/users/${queryArg.userId}` }),
        }),
        vursUpdateUserUsingPut: build.mutation<VursUpdateUserUsingPutApiResponse, VursUpdateUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/users/${queryArg.userId}`, method: 'PUT', body: queryArg.protechtUserRest }),
        }),
        vursDeleteUserUsingDelete: build.mutation<VursDeleteUserUsingDeleteApiResponse, VursDeleteUserUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/users/${queryArg.userId}`, method: 'DELETE' }),
        }),
        vursLockUserUsingPut: build.mutation<VursLockUserUsingPutApiResponse, VursLockUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/users/${queryArg.userId}/lock`, method: 'PUT' }),
        }),
        vursUnlockUserUsingPut: build.mutation<VursUnlockUserUsingPutApiResponse, VursUnlockUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/vendor/users/${queryArg.userId}/unlock`, method: 'PUT' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
