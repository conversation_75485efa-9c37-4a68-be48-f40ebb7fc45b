import { SrsSuggestObjectNamesUsingGetApiResponse, SrsSuggestObjectNamesUsingGetApiArg, SuggestionStubRest, PaginRestResultSuggestionStubRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        srsSuggestObjectNamesUsingGet: build.query<SrsSuggestObjectNamesUsingGetApiResponse, SrsSuggestObjectNamesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/suggestions/all/${queryArg.tableName}/${queryArg.columnName}`,
                params: {
                    searchingText: queryArg.searchingText,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
