export type ArsCreateActionLinkUsingPostApiResponse = /** status 200 The action link was created successfully. */ ActionLinkRest;
export type ArsCreateActionLinkUsingPostApiArg = {
    /** <b>ActionLinkRest</b> JSON data used to create the action link. */
    actionLinkRest: ActionLinkRest;
};
export type ArsDeleteActionLinkUsingDeleteApiResponse = unknown;
export type ArsDeleteActionLinkUsingDeleteApiArg = {
    /** The unique ID for the existing action link to be deleted. */
    actionLinkId: number;
};
export type ActionLinkModel = {
    /** The ID of the linked actions entry */
    actionId: number;
    /** The table name that represents the actions register */
    actionTable: string;
    /** The ID of the user who created this action link - generated by the system */
    createdBy?: number;
    /** The date that the action link was created - generated by the system */
    createdDate?: string;
    /** Unique ID for the action link */
    id?: number;
    /** The column name that represents the action link field in the register or module */
    linkColumn?: string;
    /** The unique ID for the KRI, compliance, or register entry where the action is linked */
    linkId: number;
    /** The table name that represents the register or module that contains the action link field */
    linkTable: string;
};
export type ActionLinkRest = {
    record?: ActionLinkModel;
};
export type GetApplicationsApiResponse = /** status 200 Returns list of applications. */ PaginRestResultApplicationRest;
export type GetApplicationsApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
};
export type ArsCreateNewUsingPostApiResponse = /** status 200 successful operation */ ApplicationRest;
export type ArsCreateNewUsingPostApiArg = {
    /** <b>ApplicationRest</b> JSON data used to create the application. ID is autogenerated and will override input id. */
    applicationRest: ApplicationRest;
};
export type ArsFindyIdUsingGetApiResponse = /** status 200 The application matching the unique ID provided is returned. */ ApplicationRest;
export type ArsFindyIdUsingGetApiArg = {
    /** The unique ID of the application. */
    appId: number;
};
export type ArsUpdateUsingPutApiResponse = /** status 200 The application was successfully updated and the updated version is returned. */ ApplicationRest;
export type ArsUpdateUsingPutApiArg = {
    /** The unique ID of the application to be updated. */
    appId: number;
    /** <b>ApplicationRest</b> JSON data used to update the application. */
    applicationRest: ApplicationRest;
};
export type ArsDeleteUsingDeleteApiResponse = unknown;
export type ArsDeleteUsingDeleteApiArg = {
    /** The unique ID of the application to be deleted. */
    appId: number;
};
export type ApplicationRest = {
    /** Application description */
    description?: string;
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
};
export type PaginRestResultApplicationRest = {
    maxPage?: number;
    records?: ApplicationRest[];
    response?: ApplicationRest;
    totalCount?: number;
};
export type ArsUploadAttachmentUsingPostApiResponse = /** status 200 The attachment was  added successfully. */ AttachmentsRest;
export type ArsUploadAttachmentUsingPostApiArg = {
    /** The unique ID of the register entry where the attachment is to be added. */
    linkedId: number;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    linkedTable: string;
    /** The column name that represents the attachment field within the register. For example: "col_xxxxxx". */
    linkedColumn?: string;
    /** The attachment to be added to the register entry. */
    httpServletRequest: HttpServletRequest;
};
export type ArsDownloadAllAttachmentsUsingGetApiResponse = /** status 200 successful operation */ AttachProcessingResponse;
export type ArsDownloadAllAttachmentsUsingGetApiArg = {
    tablename: string;
    id?: number[];
    subtableFiles?: boolean;
};
export type GetAttachmentsApiResponse = /** status 200 Returns a list of attachments in the response body. */ PaginRestResultAttachmentsRest;
export type GetAttachmentsApiArg = {
    /** The unique ID of the register entry where the attachment is located. */
    linkedId: number;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    linkedTable: string;
    /** The column name that represents the attachment field within the register. For example: "col_xxxxxx". */
    linkedColumn?: string;
};
export type ArsCheckAttachDownloadUsingGetApiResponse = /** status 200 successful operation */ AttachProcessingResponse;
export type ArsCheckAttachDownloadUsingGetApiArg = {
    processid: string;
};
export type ArsAttachReportUsingPostApiResponse =
    /** status 200 Returns the newly generated report attachment metadata in the response body. */ AttachmentsRest;
export type ArsAttachReportUsingPostApiArg = {
    /** The unique ID of the register entry where the attachment is added. */
    linkedId: number;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    linkedTable: string;
    /** The column name that represents the attachment field within the register. For example: "col_xxxxxx". */
    linkedColumn: string;
    /** The file path to the dashboard or report location in the Analytics module. */
    reportUri: string;
    /** The file type for the generated dashboard or report. */
    reportType?: 'PDF' | 'RTF' | 'RTF_LAYOUT' | 'EXCEL_SHEET' | 'CSV' | 'HTML_BUNDLE';
};
export type ArsLinkReportUsingPostApiResponse = /** status 200 Returns the newly generated report attachment metadata in the response body. */ AttachmentsRest;
export type ArsLinkReportUsingPostApiArg = {
    /** The unique ID of the register entry where the attachment is to be added. */
    linkedId: number;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    linkedTable: string;
    /** The column name that represents the attachment field within the register. For example: "col_xxxxxx". */
    linkedColumn: string;
    /** The file path to the dashboard or report location in the Analytics module. */
    reportUri: string;
    /** The file type for the generated dashboard or report. */
    reportType?: 'PDF' | 'RTF' | 'RTF_LAYOUT' | 'EXCEL_SHEET' | 'CSV' | 'HTML_BUNDLE';
};
export type UploadTempAttachmentApiResponse = /** status 200 Returns the temporary file name in the response body. */ string;
export type UploadTempAttachmentApiArg = {
    /** The attachment to be uploaded and stored until the system is restarted. */
    httpServletRequest: HttpServletRequest;
};
export type LinkTempAttachmentsApiResponse = /** status 200 Returns the newly created attachment metadata in the response body. */ AttachmentsRest;
export type LinkTempAttachmentsApiArg = {
    /** The unique ID of the register entry where the attachment is to be added. */
    linkedId: number;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    linkedTable: string;
    /** The column name that represents the attachment field within the register. For example: "col_xxxxxx". */
    linkedColumn: string;
    /** The unique temporary file name. For more information, see Field Reference Table above. */
    fileIdentifier: string;
    /** The perferred name of the temporary file once it is added to the register entry. */
    fileName: string;
};
export type ArsGetAttachmentByUuidUsingGetApiResponse = /** status 200 Returns the attachment metadata in the response body. */ AttachmentsRest;
export type ArsGetAttachmentByUuidUsingGetApiArg = {
    /** The attachment unique ID - this ID is system generated and assigned to an attachment when it is uploaded into Protecht.ERM. */
    attachmentUuid: string;
    /** The table name that represents the register where the attachment is attached to an entry. */
    linkTable: string;
};
export type ArsDeleteAttachmentUsingDeleteApiResponse = unknown;
export type ArsDeleteAttachmentUsingDeleteApiArg = {
    /** The attachment unique ID - this ID is system generated and assigned to an attachment when it is uploaded into Protecht.ERM. */
    attachmentUuid: string;
};
export type AttachmentsRest = {
    /** Attachment category */
    category?: string;
    /** Attachment creation date */
    createDate?: string;
    /** Unique ID of the user who created the attachment */
    createdBy?: number;
    /** Attachment description */
    description?: string;
    /** Attachment name */
    fileName?: string;
    /** Attachment size */
    fileSize?: number;
    /** Attachment system ID */
    id?: number;
    /** If true, the attachment is accessible and useable within a register entry. If false, the attachment will remain in Protecht.ERM but be unavilable for use in register entries. */
    isActive?: boolean;
    /**  Unique ID of the user who last modified the attachment */
    lastModifiedBy?: number;
    /** Attachment last modified date */
    lastModifiedDate?: string;
    /** Column name that represents the field the attachment is added to */
    linkColumn?: string;
    /** Unique ID of the register entry where the attachment is added */
    linkId?: number;
    /** Table name that represents the register where the attachment is added to an entry */
    linkTable?: string;
    /** Attachment unique ID - this ID is randomly generated and unique for every attachment uploaded to the system */
    uuid?: string;
    /** Attachment version */
    version?: number;
};
export type EnumerationString = object;
export type ServletInputStream = {
    finished?: boolean;
    ready?: boolean;
};
export type Locale = {
    country?: string;
    displayCountry?: string;
    displayLanguage?: string;
    displayName?: string;
    displayScript?: string;
    displayVariant?: string;
    extensionKeys?: string[];
    iso3Country?: string;
    iso3Language?: string;
    language?: string;
    script?: string;
    unicodeLocaleAttributes?: string[];
    unicodeLocaleKeys?: string[];
    variant?: string;
};
export type EnumerationLocale = object;
export type BufferedReader = object;
export type Annotation = object;
export type Package = {
    annotations?: Annotation[];
    declaredAnnotations?: Annotation[];
    implementationTitle?: string;
    implementationVendor?: string;
    implementationVersion?: string;
    name?: string;
    sealed?: boolean;
    specificationTitle?: string;
    specificationVendor?: string;
    specificationVersion?: string;
};
export type ModuleDescriptor = {
    automatic?: boolean;
    open?: boolean;
};
export type ModuleLayer = object;
export type Module = {
    annotations?: Annotation[];
    classLoader?: ClassLoader;
    declaredAnnotations?: Annotation[];
    descriptor?: ModuleDescriptor;
    layer?: ModuleLayer;
    name?: string;
    named?: boolean;
    nativeAccessEnabled?: boolean;
    packages?: string[];
};
export type ClassLoader = {
    definedPackages?: Package[];
    name?: string;
    parent?: ClassLoader;
    registeredAsParallelCapable?: boolean;
    unnamedModule?: Module;
};
export type FilterRegistration = {
    className?: string;
    initParameters?: {
        [key: string]: string;
    };
    name?: string;
    servletNameMappings?: string[];
    urlPatternMappings?: string[];
};
export type JspPropertyGroupDescriptor = {
    buffer?: string;
    defaultContentType?: string;
    deferredSyntaxAllowedAsLiteral?: string;
    elIgnored?: string;
    errorOnUndeclaredNamespace?: string;
    includeCodas?: string[];
    includePreludes?: string[];
    isXml?: string;
    pageEncoding?: string;
    scriptingInvalid?: string;
    trimDirectiveWhitespaces?: string;
    urlPatterns?: string[];
};
export type TaglibDescriptor = {
    taglibLocation?: string;
    taglibURI?: string;
};
export type JspConfigDescriptor = {
    jspPropertyGroups?: JspPropertyGroupDescriptor[];
    taglibs?: TaglibDescriptor[];
};
export type ServletRegistration = {
    className?: string;
    initParameters?: {
        [key: string]: string;
    };
    mappings?: string[];
    name?: string;
    runAsRole?: string;
};
export type EnumerationServlet = object;
export type SessionCookieConfig = {
    comment?: string;
    domain?: string;
    httpOnly?: boolean;
    maxAge?: number;
    name?: string;
    path?: string;
    secure?: boolean;
};
export type ServletContext = {
    attributeNames?: EnumerationString;
    classLoader?: ClassLoader;
    contextPath?: string;
    defaultSessionTrackingModes?: ('COOKIE' | 'URL' | 'SSL')[];
    effectiveMajorVersion?: number;
    effectiveMinorVersion?: number;
    effectiveSessionTrackingModes?: ('COOKIE' | 'URL' | 'SSL')[];
    filterRegistrations?: {
        [key: string]: FilterRegistration;
    };
    initParameterNames?: EnumerationString;
    jspConfigDescriptor?: JspConfigDescriptor;
    majorVersion?: number;
    minorVersion?: number;
    requestCharacterEncoding?: string;
    responseCharacterEncoding?: string;
    serverInfo?: string;
    servletContextName?: string;
    servletNames?: EnumerationString;
    servletRegistrations?: {
        [key: string]: ServletRegistration;
    };
    servlets?: EnumerationServlet;
    sessionCookieConfig?: SessionCookieConfig;
    sessionTimeout?: number;
    virtualServerName?: string;
};
export type ServletRequest = {
    asyncContext?: AsyncContext;
    asyncStarted?: boolean;
    asyncSupported?: boolean;
    attributeNames?: EnumerationString;
    characterEncoding?: string;
    contentLength?: number;
    contentLengthLong?: number;
    contentType?: string;
    dispatcherType?: 'FORWARD' | 'INCLUDE' | 'REQUEST' | 'ASYNC' | 'ERROR';
    inputStream?: ServletInputStream;
    localAddr?: string;
    localName?: string;
    localPort?: number;
    locale?: Locale;
    locales?: EnumerationLocale;
    parameterMap?: {
        [key: string]: string[];
    };
    parameterNames?: EnumerationString;
    protocol?: string;
    reader?: BufferedReader;
    remoteAddr?: string;
    remoteHost?: string;
    remotePort?: number;
    scheme?: string;
    secure?: boolean;
    serverName?: string;
    serverPort?: number;
    servletContext?: ServletContext;
};
export type ServletOutputStream = {
    ready?: boolean;
};
export type PrintWriter = object;
export type ServletResponse = {
    bufferSize?: number;
    characterEncoding?: string;
    committed?: boolean;
    contentType?: string;
    locale?: Locale;
    outputStream?: ServletOutputStream;
    writer?: PrintWriter;
};
export type AsyncContext = {
    request?: ServletRequest;
    response?: ServletResponse;
    timeout?: number;
};
export type Cookie = {
    comment?: string;
    domain?: string;
    httpOnly?: boolean;
    maxAge?: number;
    name?: string;
    path?: string;
    secure?: boolean;
    value?: string;
    version?: number;
};
export type HttpServletMapping = {
    mappingMatch?: 'CONTEXT_ROOT' | 'DEFAULT' | 'EXACT' | 'EXTENSION' | 'PATH';
    matchValue?: string;
    pattern?: string;
    servletName?: string;
};
export type InputStream = object;
export type Part = {
    contentType?: string;
    headerNames?: string[];
    inputStream?: InputStream;
    name?: string;
    size?: number;
    submittedFileName?: string;
};
export type StringBuffer = {
    empty?: boolean;
};
export type HttpSessionContext = {
    ids?: EnumerationString;
};
export type HttpSession = {
    attributeNames?: EnumerationString;
    creationTime?: number;
    id?: string;
    lastAccessedTime?: number;
    maxInactiveInterval?: number;
    new?: boolean;
    servletContext?: ServletContext;
    sessionContext?: HttpSessionContext;
    valueNames?: string[];
};
export type Principal = {
    name?: string;
};
export type HttpServletRequest = {
    asyncContext?: AsyncContext;
    asyncStarted?: boolean;
    asyncSupported?: boolean;
    attributeNames?: EnumerationString;
    authType?: string;
    characterEncoding?: string;
    contentLength?: number;
    contentLengthLong?: number;
    contentType?: string;
    contextPath?: string;
    cookies?: Cookie[];
    dispatcherType?: 'FORWARD' | 'INCLUDE' | 'REQUEST' | 'ASYNC' | 'ERROR';
    headerNames?: EnumerationString;
    httpServletMapping?: HttpServletMapping;
    inputStream?: ServletInputStream;
    localAddr?: string;
    localName?: string;
    localPort?: number;
    locale?: Locale;
    locales?: EnumerationLocale;
    method?: string;
    parameterMap?: {
        [key: string]: string[];
    };
    parameterNames?: EnumerationString;
    parts?: Part[];
    pathInfo?: string;
    pathTranslated?: string;
    protocol?: string;
    queryString?: string;
    reader?: BufferedReader;
    remoteAddr?: string;
    remoteHost?: string;
    remotePort?: number;
    remoteUser?: string;
    requestURI?: string;
    requestURL?: StringBuffer;
    requestedSessionId?: string;
    requestedSessionIdFromCookie?: boolean;
    requestedSessionIdFromURL?: boolean;
    requestedSessionIdFromUrl?: boolean;
    requestedSessionIdValid?: boolean;
    scheme?: string;
    secure?: boolean;
    serverName?: string;
    serverPort?: number;
    servletContext?: ServletContext;
    servletPath?: string;
    session?: HttpSession;
    trailerFields?: {
        [key: string]: string;
    };
    trailerFieldsReady?: boolean;
    userPrincipal?: Principal;
};
export type AttachProcessingResponse = {
    all?: number;
    archived?: number;
    errorMessage?: string;
    failed?: number;
    finished?: boolean;
    processId?: string;
    processed?: number;
    resourceUri?: string;
    status?: 'PENDING' | 'REJECTED' | 'IN_PROGRESS' | 'FINISHED' | 'FAILED' | 'PACKING';
};
export type PaginRestResultAttachmentsRest = {
    maxPage?: number;
    records?: AttachmentsRest[];
    response?: AttachmentsRest;
    totalCount?: number;
};
export type AqrsGetAuditQuestionsUsingPostApiResponse = /** status 200 successful operation */ PaginRestResultAuditQuestionBaseRest;
export type AqrsGetAuditQuestionsUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type AqrsCreateQuestionUsingPostApiResponse = /** status 200 The new audit question was successfully created. */ AuditQuestionRest;
export type AqrsCreateQuestionUsingPostApiArg = {
    /** <b>AuditQuestionRest</b> JSON data used to create the audit question. */
    auditQuestionRest: AuditQuestionRest;
};
export type AqrsGetQuestionUsingGetApiResponse = /** status 200 The audit question matching the unique ID provided is returned. */ AuditQuestionRest;
export type AqrsGetQuestionUsingGetApiArg = {
    /** The unique ID of the audit question. */
    questionId: number;
};
export type AqrsUpdateQuestionUsingPutApiResponse = /** status 200 Audit Question updated successfully. */ AuditQuestionRest;
export type AqrsUpdateQuestionUsingPutApiArg = {
    /** The unique ID of the audit question. */
    questionId: number;
    /** <b>AuditQuestionRest</b> JSON data used to update the audit question. */
    auditQuestionRest: AuditQuestionRest;
};
export type IdWithNameRest = {
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
};
export type AuditQuestionBaseRest = {
    approval?: string;
    createDate?: string;
    createdBy?: IdWithNameRest;
    /** Unique entity ID */
    id?: number;
    lastModifiedBy?: IdWithNameRest;
    lastModifiedDate?: string;
    /** Entity name */
    name?: string;
    programs?: IdWithNameRest[];
    question?: string;
    tags?: IdWithNameRest[];
};
export type PaginRestResultAuditQuestionBaseRest = {
    maxPage?: number;
    records?: AuditQuestionBaseRest[];
    response?: AuditQuestionBaseRest;
    totalCount?: number;
};
export type ViewExpressionRest = {
    /** Comparison operation performed */
    expression?:
        | '='
        | '<>'
        | '<'
        | '><='
        | '>='
        | 'in'
        | 'not in'
        | 'starts with'
        | 'not starts with'
        | 'ends with'
        | 'not ends with'
        | 'contains'
        | 'not contains'
        | 'is not set'
        | 'is set'
        | 'last'
        | 'next'
        | 'current';
    /** View expression unique ID */
    id?: number;
    /** Property of the model being evaluated */
    property?: string;
    /** Data type of the value */
    type?: string;
    /** Value of the property for comparison */
    value?: string;
};
export type FilterContextRest = {
    /** An expression that filters the data set by relating a property and value through using standard comparison operators */
    expressions?: ViewExpressionRest[];
    /** Entry ids for filter */
    ids?: number[];
};
export type AuditQuestionResponseRest = {
    /** Audit question response ID */
    id?: number;
    /** Audit question response label */
    label?: string;
    /** Order in which the responses are displayed */
    order?: number;
    /** A score signifying the outcome of an audit question as positive, negative, or neutral */
    score?: number;
};
export type AuditQuestionStepRest = {
    /** Audit question step description */
    description?: string;
    /** Audit question step ID */
    id?: number;
    /** Audit question step name */
    name?: string;
    /** Order in which the steps must be completed */
    stepOrder?: number;
};
export type AuditQuestionRest = {
    /** Audit question description */
    description?: string;
    /** Audit question unique ID */
    id?: number;
    /** Audit question text (in the UI, this field is called Question not Name) */
    name?: string;
    /** Objective of the audit question */
    objective?: string;
    /** List of available responses for an audit question */
    responses?: AuditQuestionResponseRest[];
    /** List of ordered steps required to complete the audit question */
    steps?: AuditQuestionStepRest[];
};
export type BtrsCreateDiagramUsingPostApiResponse = /** status 200 successful operation */ BowTieDiagramRest;
export type BtrsCreateDiagramUsingPostApiArg = {
    withDiagram?: boolean;
    bowTieCreateDiagramRest: BowTieCreateDiagramRest;
};
export type BtrsGetDefinitionUsingGetApiResponse = /** status 200 successful operation */ BowTieDefinition;
export type BtrsGetDefinitionUsingGetApiArg = void;
export type BtrsUpdateDiagramDefinitionUsingPutApiResponse = unknown;
export type BtrsUpdateDiagramDefinitionUsingPutApiArg = {
    bowTieDefinition: BowTieDefinition;
};
export type BtrsGetRecentDiagramsUsingGetApiResponse = /** status 200 successful operation */ BowTieDiagramRest[];
export type BtrsGetRecentDiagramsUsingGetApiArg = void;
export type BtrsGetDiagramStyleUsingGetApiResponse = /** status 200 successful operation */ ObjectNode;
export type BtrsGetDiagramStyleUsingGetApiArg = void;
export type BtrsValidateBowtieNameUsingGetApiResponse = /** status 200 successful operation */ boolean;
export type BtrsValidateBowtieNameUsingGetApiArg = {
    diagramName: string;
};
export type BtrsVerifyUserBUsUsingPutApiResponse = /** status 200 successful operation */ boolean;
export type BtrsVerifyUserBUsUsingPutApiArg = {
    body: number[];
};
export type BtrsLinkBowTieUsingPutApiResponse = unknown;
export type BtrsLinkBowTieUsingPutApiArg = {
    /** The register ID */
    regId: number;
    /** The unique ID of the register entry. */
    entryId: number;
    /** The bow tie diagram ID. */
    bowTieId: number;
    /** Field to be updated. */
    fieldBowTieRest: FieldBowTieRest;
};
export type BtrsUnlinkUsingPutApiResponse = unknown;
export type BtrsUnlinkUsingPutApiArg = {
    /** The register ID */
    regId: number;
    /** The unique ID of the register entry. */
    entryId: number;
    /** The bow tie diagram ID. */
    bowTieId: number;
    /** Field to be updated. */
    fieldBowTieRest: FieldBowTieRest;
};
export type BtrsGetDiagramUsingGetApiResponse = /** status 200 successful operation */ BowTieDiagramRest;
export type BtrsGetDiagramUsingGetApiArg = {
    id: number;
    withDiagram?: boolean;
};
export type BtrsUpdateDiagramUsingPutApiResponse = /** status 200 successful operation */ BowTieDiagramRest;
export type BtrsUpdateDiagramUsingPutApiArg = {
    id: number;
    withDiagram?: boolean;
    bowTieDiagramRest: BowTieDiagramRest;
};
export type BtrsDeleteDiagramUsingDeleteApiResponse = unknown;
export type BtrsDeleteDiagramUsingDeleteApiArg = {
    id: number;
};
export type BtrsGetDiagramCopyUsingPutApiResponse = /** status 200 successful operation */ BowTieDiagramRest;
export type BtrsGetDiagramCopyUsingPutApiArg = {
    id: number;
    withDiagram?: boolean;
    body: string;
};
export type BtrsGetEntriesUsingGetApiResponse = /** status 200 successful operation */ RegisterDataBowTiesRest[];
export type BtrsGetEntriesUsingGetApiArg = {
    /** Bow tie diagram ID. */
    id: number;
};
export type BtrsChangeDiagramLockStatusUsingPutApiResponse = unknown;
export type BtrsChangeDiagramLockStatusUsingPutApiArg = {
    id: number;
    lock: boolean;
};
export type BtrsPublishDiagramUsingPutApiResponse = unknown;
export type BtrsPublishDiagramUsingPutApiArg = {
    id: number;
    body: number[];
};
export type BtrsPurgeDiagramUsingDeleteApiResponse = unknown;
export type BtrsPurgeDiagramUsingDeleteApiArg = {
    id: number;
};
export type BtrsRenameDiagramUsingPutApiResponse = /** status 200 successful operation */ BowTieDiagramRest;
export type BtrsRenameDiagramUsingPutApiArg = {
    id: number;
    body: string;
};
export type BtrsRestoreDiagramUsingPutApiResponse = unknown;
export type BtrsRestoreDiagramUsingPutApiArg = {
    id: number;
};
export type BtrsUnpublishDiagramUsingPutApiResponse = unknown;
export type BtrsUnpublishDiagramUsingPutApiArg = {
    id: number;
};
export type BtrsGetDiagramsUsingGetApiResponse = /** status 200 successful operation */ PaginRestResultBowTieDiagramRest;
export type BtrsGetDiagramsUsingGetApiArg = {
    page?: number;
    size?: number;
    view?: number;
    'sort-by'?: string;
    'sort-dir'?: 'ASC' | 'DESC';
    'group-by'?: string;
    'tag-type'?: number;
    'filter-by'?: string;
    'filter-value'?: string;
    tags?: number[];
    'tag-operator'?: string;
    status: string;
};
export type BtrsGetDiagramsUsingPostApiResponse = /** status 200 successful operation */ PaginRestResultBowTieDiagramRest;
export type BtrsGetDiagramsUsingPostApiArg = {
    status: string;
    bowTieListRequest: BowTieListRequest;
};
export type Timestamp = {
    date?: number;
    day?: number;
    hours?: number;
    minutes?: number;
    month?: number;
    nanos?: number;
    seconds?: number;
    time?: number;
    timezoneOffset?: number;
    year?: number;
};
export type JsonNode = {
    array?: boolean;
    bigDecimal?: boolean;
    bigInteger?: boolean;
    binary?: boolean;
    boolean?: boolean;
    containerNode?: boolean;
    double?: boolean;
    float?: boolean;
    floatingPointNumber?: boolean;
    int?: boolean;
    integralNumber?: boolean;
    long?: boolean;
    missingNode?: boolean;
    nodeType?: 'ARRAY' | 'BINARY' | 'BOOLEAN' | 'MISSING' | 'NULL' | 'NUMBER' | 'OBJECT' | 'POJO' | 'STRING';
    null?: boolean;
    number?: boolean;
    object?: boolean;
    pojo?: boolean;
    short?: boolean;
    textual?: boolean;
    valueNode?: boolean;
};
export type BowTieLinkNotificationRest = {
    id?: number;
    name?: string;
    type?: string;
};
export type BowTieCreateDiagramRest = {
    description?: string;
    diagramModel?: JsonNode;
    name?: string;
};
export type BowTieRegister = {
    applicationId?: number;
    displayColumn?: string;
    name?: string;
    primary?: boolean;
    registerId?: number;
    registerTable?: string;
};
export type BowTieDefinition = {
    riskCauseRegister?: BowTieRegister;
    riskControlRegister?: BowTieRegister;
    riskEventRegister?: BowTieRegister;
    riskImpactRegister?: BowTieRegister;
};
export type ObjectNode = {
    array?: boolean;
    bigDecimal?: boolean;
    bigInteger?: boolean;
    binary?: boolean;
    boolean?: boolean;
    containerNode?: boolean;
    double?: boolean;
    float?: boolean;
    floatingPointNumber?: boolean;
    int?: boolean;
    integralNumber?: boolean;
    long?: boolean;
    missingNode?: boolean;
    nodeType?: 'ARRAY' | 'BINARY' | 'BOOLEAN' | 'MISSING' | 'NULL' | 'NUMBER' | 'OBJECT' | 'POJO' | 'STRING';
    null?: boolean;
    number?: boolean;
    object?: boolean;
    pojo?: boolean;
    short?: boolean;
    textual?: boolean;
    valueNode?: boolean;
};
export type FieldBowTieRest = {
    /** The unique ID of a field within a register */
    fieldId?: string;
    /** The column name that represents the field within a register. For example:col_xxxxx */
    fieldName?: string;
    /** Display name of a field */
    label?: string;
};
export type RegisterDataBowTiesRest = {
    /** Application id. */
    appId?: number;
    /** Display value */
    displayValue?: string;
    /** Entry id. */
    entryId?: number;
    /** List of register fields which represents bow ties multi select library fields. */
    fields?: FieldBowTieRest[];
    /** Register label name. */
    label?: string;
    /** Register id. */
    regId?: number;
    /** Register table name. */
    tableName?: string;
};
export type PaginRestResultBowTieDiagramRest = {
    maxPage?: number;
    records?: BowTieDiagramRest[];
    response?: BowTieDiagramRest;
    totalCount?: number;
};
export type BowTieListRequest = object;
export type BursCreateBuUsingPostApiResponse = /** status 200 Returns the newly created business unit in the response body. */ BusinessUnitRest;
export type BursCreateBuUsingPostApiArg = {
    /** <b>BusinessUnitRest</b> JSON data used to create the business unit. */
    businessUnitRest: BusinessUnitRest;
};
export type BursGetCalendarsUsingGetApiResponse = /** status 200 Returns a list of available trading calendars in the response body. */ IdWithNameRest[];
export type BursGetCalendarsUsingGetApiArg = void;
export type BursGetCountriesUsingGetApiResponse = /** status 200 Returns a list of available countries in the response body. */ IdWithNameRest[];
export type BursGetCountriesUsingGetApiArg = void;
export type BursGetRootBusUsingGetApiResponse =
    /** status 200 Returns the root business unit and its immediate child units in the response body. */ BusinessUnitRest[];
export type BursGetRootBusUsingGetApiArg = void;
export type BursGetRootSimpleUsingGetApiResponse =
    /** status 200 Returns the root business unit and its immediate child units in the response body. */ BusinessUnitSimpleRest[];
export type BursGetRootSimpleUsingGetApiArg = void;
export type BursGetRootTreeUsingGetApiResponse =
    /** status 200 Returns the root business unit with all child units as a tree hierarchy in the response body. */ BusinessUnitSimpleRest[];
export type BursGetRootTreeUsingGetApiArg = void;
export type BursFilterBusinessUnitsUsingGetApiResponse =
    /** status 200 Returns a filtered list of business units in the response body. */ BusinessUnitSimpleTreeFilteredRest;
export type BursFilterBusinessUnitsUsingGetApiArg = {
    /** search */
    value: string;
};
export type FilterBusinessUnitsApiResponse =
    /** status 200 Returns a list of business units as a tree hierarchy in the response body. */ PaginRestResultBusinessUnitSimpleTreeFilteredRest;
export type FilterBusinessUnitsApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type BursGetStatesUsingGetApiResponse = /** status 200 Returns a list of states in the response body. */ IdWithNameRest[];
export type BursGetStatesUsingGetApiArg = void;
export type BursGetBusinessUnitsByIdsUsingPostApiResponse =
    /** status 200 Returns the matching business units and all parent units as a tree hierarchy in the response body. */ BusinessUnitSimpleTreeFilteredRest;
export type BursGetBusinessUnitsByIdsUsingPostApiArg = {
    /** The unique IDs of the BUs. */
    body: number[];
};
export type BursGetBuUsingGetApiResponse = /** status 200 The business unit matching the unique ID provided is returned. */ BusinessUnitRest;
export type BursGetBuUsingGetApiArg = {
    /** The unique ID of the business unit. */
    businessunitId: number;
};
export type BursUpdateBuUsingPutApiResponse = /** status 200 Returns the updated business unit in the response body. */ BusinessUnitRest;
export type BursUpdateBuUsingPutApiArg = {
    /** The unique ID of the business unit to be updated. */
    businessunitId: number;
    /** <b>BusinessUnitRest</b> JSON data used to update the business unit. */
    businessUnitRest: BusinessUnitRest;
};
export type BursArchiveBuUsingPutApiResponse = unknown;
export type BursArchiveBuUsingPutApiArg = {
    /** The unique ID of the business unit to be archived. */
    businessunitId: number;
};
export type BursAddNewChildrenBusinessUnitsUsingPostApiResponse =
    /** status 200 Returns the updated parent business unit in the response body. */ BusinessUnitRest;
export type BursAddNewChildrenBusinessUnitsUsingPostApiArg = {
    /** The unique ID of the parent business unit in which the child units will be created */
    businessunitId: number;
    /** A list of <b>BusinessUnitRest</b> JSON objects used to create the child business units. */
    restListBusinessUnitRest: RestListBusinessUnitRest;
};
export type BursGetAllChildrenIdsUsingGetApiResponse =
    /** status 200 Returns a list of child units as IdWithNameRest objects in the response body. */ IdWithNameRest[];
export type BursGetAllChildrenIdsUsingGetApiArg = {
    /** The unique ID of the parent business unit for which the child units will be returned */
    businessunitId: number;
};
export type BursGetBuReferencesUsingGetApiResponse = /** status 200 The list of references to the matching business unit is returned. */ BuReferencesRest[];
export type BursGetBuReferencesUsingGetApiArg = {
    /** The unique ID of the business unit. */
    businessunitId: number;
};
export type BursRestoreBuUsingPutApiResponse = unknown;
export type BursRestoreBuUsingPutApiArg = {
    /** The unique ID of the business unit to be restored. */
    businessunitId: number;
};
export type BursGetBuSimpleUsingGetApiResponse = /** status 200 The business unit matching the unique ID provided is returned. */ BusinessUnitSimpleRest;
export type BursGetBuSimpleUsingGetApiArg = {
    /** The unique ID of the business unit. */
    businessunitId: number;
};
export type BursGetBuTreeUsingGetApiResponse =
    /** status 200 Returns the matching business unit and all child units as a tree hierarchy in the response body. */ BusinessUnitSimpleRest;
export type BursGetBuTreeUsingGetApiArg = {
    /** The unique ID of the business unit. */
    businessunitId: number;
};
export type TagTypeRest = {
    /** Tag type context (the logical group that this tag type is in) */
    context?: 'control' | 'riskevent' | 'riskcause' | 'keyriskindicator' | 'system';
    /** Tag ID */
    id?: number;
    /** Tag type name */
    name?: string;
    /** A list of tag Ids that belong to this tag type */
    tagIds?: number[];
    /** A list of tags that belong to this tag type */
    tags?: TagRest[];
};
export type TagRest = {
    /** Tag ID */
    id?: number;
    type?: TagTypeRest;
    /** Tag name */
    value?: string;
};
export type BusinessUnitRest = {
    Calendar?: IdWithNameRest;
    Country?: IdWithNameRest;
    State?: IdWithNameRest;
    /** A list of child units that belong to this business unit */
    children?: BusinessUnitRest[];
    /** Business unit city */
    city?: string;
    /** Business unit ID */
    id?: number;
    /** Business unit latitude */
    lat?: number;
    /** Business unit longtitude */
    lon?: number;
    manager?: IdWithNameRest;
    /** Business unit name */
    name: string;
    /** A number of children for business unit */
    numberOfChildren?: number;
    parent: IdWithNameRest;
    /** Business unit street */
    street?: string;
    /** A list of tags that belong to this business unit */
    tags?: TagRest[];
    /** A Business unit type. Default is STANDARD. */
    type?: string;
    /** Business unit postal code */
    zip?: string;
};
export type BusinessUnitSimpleRest = {
    /** A list of child units (simple model) that belong to this business unit */
    children?: BusinessUnitSimpleRest[];
    /** Business unit ID */
    id?: number;
    /** Business unit name */
    name: string;
    /** A number of children for business unit */
    numberOfChildren?: number;
    /** Business unit parent ID */
    parentID?: number;
};
export type BusinessUnitSimpleTreeFilteredRest = {
    /** A list of business units (simple model) */
    businessUnitSimpleRests?: BusinessUnitSimpleRest[];
    /** Business unit parent ID */
    matchingBusinessunits?: number[];
};
export type PaginRestResultBusinessUnitSimpleTreeFilteredRest = {
    maxPage?: number;
    records?: BusinessUnitSimpleTreeFilteredRest[];
    response?: BusinessUnitSimpleTreeFilteredRest;
    totalCount?: number;
};
export type RestListBusinessUnitRest = {
    items?: BusinessUnitRest[];
};
export type BuReferencesRest = {
    deleted?: boolean;
    historical?: boolean;
    id?: number;
    name?: string;
    originalId?: number;
    type?: string;
};
export type GetComplianceAssignmentsApiResponse =
    /** status 200 Returns a list of compliance assignments in the response body. */ PaginRestResultComplianceFrequencyRest;
export type GetComplianceAssignmentsApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
};
export type CqarsCreateComplianceFrequencyUsingPostApiResponse = /** status 200 successful operation */ ComplianceFrequencyRest;
export type CqarsCreateComplianceFrequencyUsingPostApiArg = {
    /** <b>ComplianceFrequencyRest</b> JSON data used to create the compliance assignment. <br><br> For the assignment to be successful, you must provide the relevant <b>businessUnit</b> and <b>control</b> (the question to be assigned). You must also provide either responsibility (a user responsible for answering the question) or role (a role responsible for answering the question). The user or role provided must exist within the nominated business unit. */
    complianceFrequencyRest: ComplianceFrequencyRest;
};
export type UpdateComplianceAssignmentApiResponse = /** status 200 The compliance assignment was  updated successfully. */ ComplianceFrequencyRest;
export type UpdateComplianceAssignmentApiArg = {
    /** <b>ComplianceFrequencyRest</b> JSON data used to identify and update the compliance assignment. Non-mandatory fields that are left blank will be updated to an empty value in the record.<br><br> All compliance assignments must have a relevant <b>businessUnit</b> and a <b>control</b> (the question to be assigned). You must also provide either <b>responsibility</b> (a user responsible for answering the question) or <b>role</b> (a role responsible for answering the question). The user or role provided must exist within the nominated business unit. */
    complianceFrequencyRest: ComplianceFrequencyRest;
};
export type CqarsCreateComplianceFrequencyUsingPost1ApiResponse = /** status 200 successful operation */ ComplianceFrequencyRest[];
export type CqarsCreateComplianceFrequencyUsingPost1ApiArg = {
    /** ComplianceFrequencyBulkCreateRest JSON data used to create the compliance assignments. */
    complianceFrequencyBulkCreateRest: ComplianceFrequencyBulkCreateRest;
};
export type CqarsDeleteComplianceFrequencyUsingDeleteApiResponse = unknown;
export type CqarsDeleteComplianceFrequencyUsingDeleteApiArg = {
    /** A list of unique IDs of the existing compliance assignment records to be deleted. */
    body: number[];
};
export type CqarsGetComplianceFrequencyUsingGetApiResponse =
    /** status 200 The compliance assignment record matching the unique ID provided is returned. */ ComplianceFrequencyRest;
export type CqarsGetComplianceFrequencyUsingGetApiArg = {
    /** The unique ID of the compliance assignment record. */
    id: number;
};
export type CqarsDeleteComplianceFrequencyUsingDelete1ApiResponse = unknown;
export type CqarsDeleteComplianceFrequencyUsingDelete1ApiArg = {
    /** The unique ID of the compliance assignment record to be deleted. */
    id: number;
};
export type CersGetAllComplianceEntriesUsingGetApiResponse =
    /** status 200 Returns a list of compliance entries in the response body. */ PaginRestResultComplianceEntryRest;
export type CersGetAllComplianceEntriesUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the compliance entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth?: boolean;
    /** Filter used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    filter?: string;
};
export type CersGetAllViewComplianceEntriesUsingPostApiResponse =
    /** status 200 Returns a list of compliance entries in the response body. */ PaginRestResultComplianceEntryRest;
export type CersGetAllViewComplianceEntriesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the compliance entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth: boolean;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    complianceKriFilterWrapperRest: ComplianceKriFilterWrapperRest;
};
export type CersGetMyComplianceEntriesUsingGetApiResponse =
    /** status 200 Returns a list of compliance entries in the response body. */ PaginRestResultComplianceEntryRest;
export type CersGetMyComplianceEntriesUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the compliance entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth: boolean;
    /** Filter used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    filter?: string;
    onePage?: boolean;
};
export type CersGetMyViewComplianceEntriesUsingPostApiResponse =
    /** status 200 Returns a list of compliance entries in the response body. */ PaginRestResultComplianceEntryRest;
export type CersGetMyViewComplianceEntriesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the compliance entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth: boolean;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    complianceKriFilterWrapperRest: ComplianceKriFilterWrapperRest;
};
export type CersUpdateEntriesBulkUsingPutApiResponse =
    /** status 200 The compliance entry was successfully updated and the updated version returned. */ ComplianceEntryRest[];
export type CersUpdateEntriesBulkUsingPutApiArg = {
    bulkComplianceEntryCompleteEnvelope: BulkComplianceEntryCompleteEnvelope;
};
export type CersGetEntryByIdentifierUsingGetApiResponse =
    /** status 200 The compliance entry matching the unique ID provided is returned. */ ComplianceEntryRest;
export type CersGetEntryByIdentifierUsingGetApiArg = {
    /** The unique ID of the compliance entry. */
    entryId: number;
};
export type CersUpdateEntryUsingPutApiResponse =
    /** status 200 The compliance entry was successfully updated and the updated version returned. */ ComplianceEntryRest;
export type CersUpdateEntryUsingPutApiArg = {
    /** The unique ID of the compliance entry. */
    entryId: number;
    /** UpdateComplianceEntryRest JSON data used to update the compliance entry. */
    updateComplianceEntryRest: UpdateComplianceEntryRest;
};
export type CersDeleteEntryUsingDeleteApiResponse = unknown;
export type CersDeleteEntryUsingDeleteApiArg = {
    /** The unique ID of the compliance entry. */
    entryId: number;
};
export type CersReassignEntryUsingPutApiResponse =
    /** status 200 The compliance entry was successfully reassigned and the updated version returned. */ ComplianceEntryRest;
export type CersReassignEntryUsingPutApiArg = {
    /** 	The unique ID of the compliance entry. */
    entryId: number;
    /** <b>ComplianceEntryRest</b> JSON data used to reassign the compliance entry. Only the <b>user</b> object is relevant to this operation and all other values are ignored. */
    complianceEntryRest: ComplianceEntryRest;
};
export type CqrsCreateComplianceQuestionUsingGetApiResponse =
    /** status 200 The newly created compliance question is returned in the response body. */ QuestionBaseRest;
export type CqrsCreateComplianceQuestionUsingGetApiArg = void;
export type CqrsDeleteComplianceQuestionUsingDeleteApiResponse = unknown;
export type CqrsDeleteComplianceQuestionUsingDeleteApiArg = {
    /** The unique IDs of the compliance questions to be deleted. */
    complianceQuestionIds: number[];
};
export type CqrsPurgeComplianceQuestionUsingDeleteApiResponse = unknown;
export type CqrsPurgeComplianceQuestionUsingDeleteApiArg = {
    /** The unique IDs of the compliance questions to be Purged. */
    complianceQuestionIds: number[];
};
export type CqrsRestoreComplianceQuestionUsingPostApiResponse = unknown;
export type CqrsRestoreComplianceQuestionUsingPostApiArg = {
    /** The unique ID of the compliance questions to be restored. */
    complianceQuestionIds: number[];
};
export type ComplianceQuestionsApiResponse = /** status 200 successful operation */ PaginRestResultQuestionBaseRest;
export type ComplianceQuestionsApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** Tag type ID used for grouping */
    tagType?: number;
    /** Type of the tag operator */
    tagOperator?: 'AND' | 'OR';
    /** Tag IDs used for filtering */
    tagIds?: number[];
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Status IDs used for filtering */
    statuses?: number[];
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type CqrsGetControlUsingGetApiResponse = /** status 200 The compliance question is returned in the response body. */ QuestionBaseRest;
export type CqrsGetControlUsingGetApiArg = {
    /** The unique ID of the compliance question. */
    complianceQuestionId: number;
};
export type CqrsUpdateComplianceQuestionUsingPutApiResponse =
    /** status 200 The updated compliance question data is returned in the response body. */ QuestionBaseRest;
export type CqrsUpdateComplianceQuestionUsingPutApiArg = {
    /** The unique ID of the compliance question to be updated. */
    complianceQuestionId: number;
    /** <b>QuestionBaseRest</b> JSON data used to update the compliance question. */
    questionBaseRest: QuestionBaseRest;
};
export type GetComplianceQuestionsHistoryApiResponse =
    /** status 200 The list of historical versions for the compliance question is returned. */ PaginRestResultQuestionBaseRest;
export type GetComplianceQuestionsHistoryApiArg = {
    /** The unique ID of the compliance question for which historical versions will be returned. */
    complianceQuestionId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type CrrsGetComplianceResponsesUsingGetApiResponse =
    /** status 200 Returns the list of compliance responses that match the provided control type or all compliance responses if no control type is provided. */ PaginRestResultComplianceResponseRest;
export type CrrsGetComplianceResponsesUsingGetApiArg = {
    /** The types of compliance responses defined by the system. For example: "CONTROL" or "QUESTION". */
    controlType?: string;
};
export type ComplianceFrequencyRest = {
    'Business unit ID'?: IdWithNameRest;
    'Control ID'?: IdWithNameRest;
    'Responsibility ID'?: IdWithNameRest;
    'Role ID'?: IdWithNameRest;
    'User ID'?: IdWithNameRest;
    /** Whether the compliance entry has been completed for the given period */
    completed?: boolean;
    /** How often a new entry is generated for the compliance question */
    complianceFrequency?:
        | 'On Demand(O)'
        | 'Daily (D)'
        | 'Weekly (W)'
        | 'Fortnightly (F)'
        | 'Monthly (M)'
        | 'Bimonthly (C)'
        | 'Quarterly(Q)'
        | 'Semi-Annually (S)'
        | 'Annually (A)'
        | 'Biennially (B)'
        | 'Triennially (3 Years) (T)'
        | 'Quinquennially (5 Years) (U)'
        | 'Never (N)'
        | 'Not Set (-)';
    /** The date that the compliance assignment was created - generated by the system */
    createDate?: string;
    /** The date when entries for the previous month are closed for updates */
    endDate?: string;
    /** The last day that data is relevant to this compliance question */
    endOfPeriod?: string;
    /** Compliance assignment unique ID */
    id?: number;
    /** Date the compliance assignment was last modified - generated by the the system */
    lastModified?: string;
    /** Column name that represents a register field containing a link to the compliance question */
    registerLink?: string;
    /** Unique ID for a link between the compliance question and a register */
    registerLinkId?: number;
    /** Table name that represents a register that contains a link to the compliance question */
    registerLinkTable?: string;
    /** Date when the compliance assignment was replaced */
    replacementDate?: string;
};
export type PaginRestResultComplianceFrequencyRest = {
    maxPage?: number;
    records?: ComplianceFrequencyRest[];
    response?: ComplianceFrequencyRest;
    totalCount?: number;
};
export type ComplianceFrequencyBulkCreateRest = {
    /** A key-value mapping of The business unit(key) to the responsible user(value) OR role(value). The 'isUserResponsible' flag determines whether the values are treated as users or roles */
    businesUnitResposibles: {
        [key: string]: number;
    };
    /** The last day that data is relevant to this compliance questions */
    endOfPeriod: string;
    /** The list of compliance question IDs that are being assigned */
    questions: number[];
    userResposible?: boolean;
};
export type SimpleAttachment = {
    /** Attachment Name */
    fileName?: string;
    /** Attachment unique identifier (UUID) */
    uuid?: string;
};
export type ComplianceEntryRest = {
    /** List of linked actions */
    actions?: ActionLinkRest[];
    /** How often a new entry is generated for the compliance question */
    attestationFrequency?:
        | 'On Demand(O)'
        | 'Daily (D)'
        | 'Weekly (W)'
        | 'Fortnightly (F)'
        | 'Monthly (M)'
        | 'Bimonthly (C)'
        | 'Quarterly(Q)'
        | 'Semi-Annually (S)'
        | 'Annually (A)'
        | 'Biennially (B)'
        | 'Triennially (3 Years) (T)'
        | 'Quinquennially (5 Years) (U)'
        | 'Never (N)'
        | 'Not Set (-)';
    businessUnit?: IdWithNameRest;
    /** Compliance entry category */
    category?: string;
    /** Whether the entry was changed after the cut off date */
    changedAfterCutOff?: boolean;
    /** Comment block for the compliance entry */
    comment?: string;
    /** Unique ID of the user who completed the entry */
    completedBy?: number;
    /** Date of completion for the entry */
    completedDate?: string;
    /** Whether or not the entry has been confirmed by an eligible supervisor or manager. Can only be toggled by a user with the COMPLIANCE.CONFIRM permission. */
    confirmed?: boolean;
    control?: IdWithNameRest;
    /** Confirmation of compliance entry control completion. (Example: 1 - to set the compliance entry response to "Yes", 2 - to set the compliance entry response to "No". See control responses.) */
    controlCompleted?: number;
    /** Description of the compliance question or control */
    controlDescription?: string;
    /** The compliance frequency of the linked control */
    controlFrequency?:
        | 'On Demand(O)'
        | 'Daily (D)'
        | 'Weekly (W)'
        | 'Fortnightly (F)'
        | 'Monthly (M)'
        | 'Bimonthly (C)'
        | 'Quarterly(Q)'
        | 'Semi-Annually (S)'
        | 'Annually (A)'
        | 'Biennially (B)'
        | 'Triennially (3 Years) (T)'
        | 'Quinquennially (5 Years) (U)'
        | 'Never (N)'
        | 'Not Set (-)';
    /** The text of the compliance question or control */
    controlName?: string;
    /** The type of compliance record (either "CONTROL" or "QUESTION") */
    controlType?: string;
    /** Date the compliance entry was created */
    createDate?: string;
    /** The end date of the attestation period */
    endOfPeriod?: string;
    /** Evidence attached to the compliance entry */
    evidence?: SimpleAttachment[];
    /** Compliance entry unique ID */
    id?: number;
    /** Unique ID of the user who last modified the compliance entry */
    lastModifiedBy?: number;
    /** Date the compliance entry was last modified */
    lastModifiedDate?: string;
    /** Response provided by user */
    response?: string;
    /** Name of the risk event that is linked to the compliance question */
    riskName?: string;
    /** Status ID of the entry */
    status?: number;
    user?: IdWithNameRest;
    /** Username of the user assigned to this compliance entry */
    userName?: string;
};
export type PaginRestResultComplianceEntryRest = {
    maxPage?: number;
    records?: ComplianceEntryRest[];
    response?: ComplianceEntryRest;
    totalCount?: number;
};
export type ComplianceKriFilterWrapperRest = {
    /** Business unit unique ID */
    businessUnits?: number[];
    /** List of expressions used to further filter the data set */
    viewExpressions?: ViewExpressionRest[];
};
export type BulkComplianceEntryAttachment = {
    fileId?: string;
    fileName?: string;
};
export type UpdateComplianceEntryRest = {
    /** Comment block for the compliance entry */
    comment?: string;
    /** Whether or not the entry has been confirmed by an eligible supervisor or manager. Can only be toggled by a user with the COMPLIANCE.CONFIRM permission. */
    confirmed?: boolean;
    /** Confirmation of compliance entry control completion. (Example: 1 - to set the compliance entry response to "Yes", 2 - to set the compliance entry response to "No". See control responses.) */
    controlCompleted?: number;
};
export type BulkComplianceEntryComplete = {
    attachments?: BulkComplianceEntryAttachment[];
    complianceId?: number;
    entry?: UpdateComplianceEntryRest;
};
export type BulkComplianceEntryCompleteEnvelope = {
    records?: BulkComplianceEntryComplete[];
};
export type IdWithNameAndStatusRest = {
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
    /** Entity status */
    status?: number;
};
export type QuestionBaseRest = {
    controlCategory?: IdWithNameRest;
    controls?: IdWithNameRest[];
    createDate?: string;
    createdBy?: IdWithNameRest;
    /** The generation frequency for the question */
    defaultFrequency?: string;
    /** Description of the risk control */
    description?: string;
    /** Unique entity ID */
    id?: number;
    lastModifiedBy?: IdWithNameRest;
    lastModifiedDate?: string;
    /** Entity name */
    name?: string;
    /** Names of the risk events linked to the risk control */
    riskEvents?: IdWithNameAndStatusRest[];
    /** Entity status */
    status?: number;
    /** Names of the tags linked to the risk control */
    tags?: IdWithNameRest[];
};
export type PaginRestResultQuestionBaseRest = {
    maxPage?: number;
    records?: QuestionBaseRest[];
    response?: QuestionBaseRest;
    totalCount?: number;
};
export type ComplianceResponseItem = {
    /** Compliance response background color */
    bgColour?: string;
    /** Compliance response text color */
    fgColour?: string;
    /** A flag to determine whether users are required to create at least one action for the entry if they have selected that response */
    forceAction?: boolean;
    /** A flag to determine whether users are required to enter a comment before their response can be saved */
    forceComment?: boolean;
    /** Icon unique ID */
    iconId?: number;
    /** Compliance response unique ID */
    id?: number;
    /** Compliance response name */
    name?: string;
};
export type ComplianceResponseRest = {
    /** List of compliance responses that belong to this compliance control type */
    complianceResponseItems?: ComplianceResponseItem[];
    /** Compliance control type */
    controlType?: string;
    /** Compliance control type unique ID */
    id?: number;
};
export type PaginRestResultComplianceResponseRest = {
    maxPage?: number;
    records?: ComplianceResponseRest[];
    response?: ComplianceResponseRest;
    totalCount?: number;
};
export type CrcChatUsingPostApiResponse = /** status 200 successful operation */ string;
export type CrcChatUsingPostApiArg = {
    copilotChatRequest: CopilotChatRequest;
};
export type CrcChatStreamUsingPostApiResponse = /** status 200 successful operation */ FluxString;
export type CrcChatStreamUsingPostApiArg = {
    authorization: string;
    body: {
        request: any;
        files?: any[];
    };
};
export type CopilotChatRequest = {
    /** The prompt to send to the agent. Typically, a question, query or directive. */
    text: string;
};
export type FluxString = {
    prefetch?: number;
};
export type GetExportContentGetApiResponse = unknown;
export type GetExportContentGetApiArg = {
    /** The unique finished export process identifier */
    exportid: string;
};
export type GetExportProgressGetApiResponse = /** status 200 Current export progress response. */ ExportProgressResponse;
export type GetExportProgressGetApiArg = {
    /** The unique running export process identifier */
    exportid: string;
};
export type ImportRegisterEntriesPostApiResponse = /** status 200 Import process identificator. */ string;
export type ImportRegisterEntriesPostApiArg = {
    /** Data set import request containing import file and import configuration */
    dataSetImportRequest: DataSetImportRequest;
};
export type GetImportProgressGetApiResponse = /** status 200 Current import progress response. */ ProgressResponse;
export type GetImportProgressGetApiArg = {
    /** The unique runnging import process identifier */
    importid: string;
};
export type ValidateImportRegisterEntriesPostApiResponse = /** status 200 Validation process identificator. */ string;
export type ValidateImportRegisterEntriesPostApiArg = {
    /** Data set validation request containing import file and import configuration */
    dataSetImportRequest: DataSetImportRequest;
};
export type ExportRegisterEntriesPostApiResponse = /** status 200 Export process identificator. */ string;
export type ExportRegisterEntriesPostApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** If only entries for My Tasks should be returned, the list of requested states should be provided. (1 - OPEN, 2 - DUENOW, 3 - OVERDUE) */
    myTaskStates?: number[];
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** True if result is used for the suggestions */
    isSuggestion?: boolean;
    /** Register state ID to filter entries by */
    stateId?: number;
    /** Flag to determine whether system fields will be included in export or not. */
    excludeSystemFields?: boolean;
    /** Data set export request containing view epressions and export configuration */
    dataSetExportRequest: DataSetExportRequest;
};
export type ImportRegisterEntriesPost1ApiResponse = /** status 200 Import process identificator. */ string;
export type ImportRegisterEntriesPost1ApiArg = {
    /** The unique ID of the register */
    regId: number;
    /** Data set import request containing import file and import configuration */
    dataSetImportRequest: DataSetImportRequest;
};
export type GetTemplateGetApiResponse = unknown;
export type GetTemplateGetApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** Suggested file name */
    suggestedFileName?: string;
};
export type ValidateImportRegisterEntriesPost1ApiResponse = /** status 200 Validation process identificator. */ string;
export type ValidateImportRegisterEntriesPost1ApiArg = {
    /** The unique ID of the register */
    regId: number;
    /** Data set validation request containing import file and import configuration */
    dataSetImportRequest: DataSetImportRequest;
};
export type ExportProgressResponse = {
    exportId?: string;
    messages?: string[];
    params?: {
        [key: string]: string;
    };
    processedCount?: number;
    status?: 'WORKING' | 'FAILED' | 'DONE' | 'NOTREADY' | 'CANCELED';
    suggestedFilename?: string;
    totalCount?: number;
    userId?: number;
};
export type Resource = {
    description?: string;
    file?: Blob;
    filename?: string;
    inputStream?: InputStream;
    open?: boolean;
    readable?: boolean;
    uri?: string;
    url?: string;
};
export type MultipartFile = {
    bytes?: string[];
    contentType?: string;
    empty?: boolean;
    inputStream?: InputStream;
    name?: string;
    originalFilename?: string;
    resource?: Resource;
    size?: number;
};
export type DataSetImportRequest = {
    /** Allow required fields to be empty */
    allowEmptyRequired?: boolean;
    /** Custom char set */
    customCharset?: string;
    /** Custom date format (e.g MM/dd/yyyy) */
    customDateFormat?: string;
    /** Custom date time format (e.g dd/MM/yyyy hh:mm:ss a) */
    customDateTimeFormat?: string;
    /** Data orientation of the imported file, default one is ROW */
    dataOrientation?: 'ROW' | 'COLUMN';
    /** Import duplicate worklogs */
    duplicateWorklogs?: boolean;
    /** Force new records into initial state */
    forceInitialState?: boolean;
    importFile?: MultipartFile;
    /** Format of the imported file, default one is CSV */
    importFileFormat?: 'CSV' | 'EXCEL';
    /** Suppress workflow notification */
    suppressWorkfloNotification?: boolean;
    /** Try to use internal ids instead names */
    useInternalIds?: boolean;
    /** List of fields, for which to match during the import, internal id should be used instead of name  */
    useInternalIdsFor?: string;
};
export type ProcessingError = {
    errorDetails?: string;
    errorMessage?: string;
    row?: number;
    type?: 'ERROR' | 'WARN' | 'INFO';
};
export type StackTraceElement = {
    classLoaderName?: string;
    className?: string;
    fileName?: string;
    lineNumber?: number;
    methodName?: string;
    moduleName?: string;
    moduleVersion?: string;
    nativeMethod?: boolean;
};
export type Throwable = {
    cause?: Throwable;
    localizedMessage?: string;
    message?: string;
    stackTrace?: StackTraceElement[];
    suppressed?: Throwable[];
};
export type ImportException = {
    cause?: Throwable;
    errorDetails?: string;
    errorMessage?: string;
    localizedMessage?: string;
    message?: string;
    row?: number;
    suppressed?: Throwable[];
    type?: 'ERROR' | 'WARN' | 'INFO';
};
export type ProcessingErrorWithStageTypeImportException = {
    error?: ImportException;
    stageType?: 'VALIDATION' | 'IMPORT' | 'INFO';
};
export type ImportWarningMessage = {
    cause?: Throwable;
    errorDetails?: string;
    errorMessage?: string;
    localizedMessage?: string;
    message?: string;
    row?: number;
    suppressed?: Throwable[];
    type?: 'ERROR' | 'WARN' | 'INFO';
};
export type ProcessingErrorWithStageTypeImportWarningMessage = {
    error?: ImportWarningMessage;
    stageType?: 'VALIDATION' | 'IMPORT' | 'INFO';
};
export type RunningProcess = {
    processId?: string;
    processType?: 'DATA_IMPORT' | 'STRUCTURE_IMPORT' | 'BULK_UPDATE';
    startTime?: string;
    tableName?: string;
    userId?: number;
    userName?: string;
};
export type RunningProcessProgress = {
    runningProcess?: RunningProcess;
    subTotalItemCount?: number;
    totalItemCount?: number;
};
export type ValidationException = {
    cause?: Throwable;
    errorDetails?: string;
    errorMessage?: string;
    fieldName?: string;
    fieldValue?: string;
    localizedMessage?: string;
    message?: string;
    row?: number;
    suppressed?: Throwable[];
    type?: 'ERROR' | 'WARN' | 'INFO';
};
export type ProcessingErrorWithStageTypeValidationException = {
    error?: ValidationException;
    stageType?: 'VALIDATION' | 'IMPORT' | 'INFO';
};
export type ValidationInfoException = {
    cause?: Throwable;
    errorDetails?: string;
    errorMessage?: string;
    fieldName?: string;
    fieldValue?: string;
    localizedMessage?: string;
    message?: string;
    row?: number;
    suppressed?: Throwable[];
    type?: 'ERROR' | 'WARN' | 'INFO';
};
export type ProcessingErrorWithStageTypeValidationInfoException = {
    error?: ValidationInfoException;
    stageType?: 'VALIDATION' | 'IMPORT' | 'INFO';
};
export type ProgressResponse = {
    autoCanceled?: boolean;
    blankRecordCount?: number;
    canceled?: boolean;
    currentRecord?: number;
    failure?: string;
    importErrors?: ProcessingError[];
    importExceptions?: ProcessingErrorWithStageTypeImportException[];
    importFailed?: boolean;
    importFinished?: boolean;
    importWarningMessages?: ProcessingErrorWithStageTypeImportWarningMessage[];
    importedRecordCount?: number;
    resourceName?: string;
    runningProcessProgress?: RunningProcessProgress;
    totalRecordsCount?: number;
    validRecordCount?: number;
    validationErrors?: ProcessingError[];
    validationExceptions?: ProcessingErrorWithStageTypeValidationException[];
    validationFinished?: boolean;
    validationInfoExceptions?: ProcessingErrorWithStageTypeValidationInfoException[];
    validationInfoMessages?: ProcessingError[];
};
export type DataSetExportConfiguration = {
    /** Set of objects, that will be exported with theirs internal ids, not names */
    columnsUsingInternalIds?: string[];
    /** Export Type */
    exportType?: 'EXPORT_ALL' | 'EXPORT_SELECTED' | 'EXPORT_FILTERED';
    /** If true, also deleted entries are exported */
    includeDeleted?: boolean;
    /** If true, HTML tags are removed */
    stripTags?: boolean;
    /** If true, export only fields (columns) used in the current view */
    useCurrentView?: boolean;
};
export type DataSetExportRequest = {
    exportConfiguration?: DataSetExportConfiguration;
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in) */
    viewExpressions?: ViewExpressionRest[];
};
export type RdrsvCreateNewEntryUsingPostApiResponse = /** status 200 successful operation */ RegisterDataRest;
export type RdrsvCreateNewEntryUsingPostApiArg = {
    /** A flag to indicate whether the new register entry will be created as an <b>incomplete</b> entry. Incomplete register entries are not visible on the user interface and are periodically removed by the system. By default, this value is <b>false</b>, meaning that the new entry will be created as <b>complete</b>. */
    initial?: boolean;
    /** <b>RegisterDataRest</b> JSON data used to create the register entry. */
    registerDataRest: RegisterDataRest;
};
export type RdrsvGetLinkedEntriesDataUsingGetApiResponse = /** status 200 The list of related entry links is returned in the response body. */ LinkData[];
export type RdrsvGetLinkedEntriesDataUsingGetApiArg = {
    /** The table name that represents the register. For example: "table_xxxxxx". */
    tableName: string;
    /** The unique ID of the register entry. */
    entryId: number;
    /** This field allows filtering for link type. If this is not provided only DYNAMIC links will be returned. <ul><li><b>RELATED</b>: User defined links between related entries within the same register.</li> <li><b>DYNAMIC</b>: System defined links between a library/private register and its parent (primary) register. Dynamic links are generated when the primary register entry is saved.</li></ul> */
    linkType?: 'DYNAMIC' | 'RELATED';
};
export type RdrsvUpdateRelatedLinkedEntriesUsingPutApiResponse = unknown;
export type RdrsvUpdateRelatedLinkedEntriesUsingPutApiArg = {
    /** The table name that represents the register. For example: "table_xxxxxx". */
    tableName: string;
    /** The unique ID of the register entry. */
    entryId: number;
    /** <b>RelatedEntryLinkRest</b> JSON data used to set the related entry links. */
    body: RelatedEntryLinkRest[];
};
export type GetRegisterEntriesPostApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultRegisterDataRest;
export type GetRegisterEntriesPostApiArg = {
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** If only entries for My Tasks should be returned, the list of requested states should be provided. (1 - OPEN, 2 - DUENOW, 3 - OVERDUE) */
    myTaskStates?: number[];
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** True if result is used for the suggestions */
    isSuggestion?: boolean;
    /** Register state ID to filter entries by */
    stateId?: number;
    /** Metric ID to filter entries by */
    metricId: number;
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RdrsvGetEntryRelationTypesUsingGetApiResponse = /** status 200 The list of relation types is returned in the response body. */ RelationTypeRest[];
export type RdrsvGetEntryRelationTypesUsingGetApiArg = void;
export type RdrsvGetEntryByIdUsingGetApiResponse = /** status 200 The register entry is returned in the response body. */ RegisterDataRest;
export type RdrsvGetEntryByIdUsingGetApiArg = {
    /** The table name that represents the register. For example: "table_xxxxxx". */
    tableName: string;
    /** The unique ID of the register entry. */
    entryId: number;
};
export type GetRegisterEntriesGetApiResponse =
    /** status 200 The register entry matching the search criteria is returned in the response body. */ RegisterDataRest;
export type GetRegisterEntriesGetApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** Label of the field as it appears in the register entry form on the user interface. This is either the Field Label (an optional register field attribute) or if Field Label isn't used, it's the Field Name (a mandatory register field attribute). */
    keys: string[];
    /** The exact value of the field identified by the keys parameter. For Status fields, provide the ID for the state you want to find (this is provided in the Register Configuration resource). */
    values: string[];
};
export type RdrsvGetEntriesByIdsUsingGetApiResponse = /** status 200 The list of register entries is returned. */ PaginRestResultRegisterDataRest;
export type RdrsvGetEntriesByIdsUsingGetApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The unique IDs of the register entries to include in the list. */
    entryIds: number[];
};
export type RdrsvGetAvailableFrameworkLevelsUsingGetApiResponse = /** status 200 The framework levels successfully retrieved and returned. */ FrameworkLevel[];
export type RdrsvGetAvailableFrameworkLevelsUsingGetApiArg = {
    /** The unique ID of the register. */
    regId: number;
};
export type GetRegisterEntriesSearchPostApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultRegisterDataRest;
export type GetRegisterEntriesSearchPostApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** If only entries for My Tasks should be returned, the list of requested states should be provided. (1 - OPEN, 2 - DUENOW, 3 - OVERDUE) */
    myTaskStates?: number[];
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** True if result is used for the suggestions */
    isSuggestion?: boolean;
    /** Register state ID to filter entries by */
    stateId?: number;
    /** Register parent entry ID */
    parentEntryId?: number;
    /** Subtable column name */
    subtableColumn?: string;
    /** Parent table name */
    parentTableName?: string;
    /** Framework level grouping configuration. Will be considered only if grouping by Framework Link field applied as well */
    fwLevelGrouping?: string;
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RdrsvGetEntriesGetUsingGetApiResponse = /** status 200 successful operation */ PaginRestResultRegisterDataRest;
export type RdrsvGetEntriesGetUsingGetApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** If only entries for My Tasks should be returned, the list of requested states should be provided. (1 - OPEN, 2 - DUENOW, 3 - OVERDUE) */
    myTaskStates?: number[];
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** True if result is used for the suggestions */
    isSuggestion?: boolean;
    /** Payload */
    payload?: string;
    /** Register state ID to filter entries by */
    stateId?: number;
    /** Register parent entry ID */
    parentEntryId?: number;
    /** Subtable column name */
    subtableColumn?: string;
    /** Parent table name */
    parentTableName?: string;
    /** Framework level grouping configuration */
    fwLevelGrouping?: string;
};
export type RdrsvGetEntryByIdUsingGet1ApiResponse = /** status 200 The register entry is returned in the response body. */ RegisterDataRest;
export type RdrsvGetEntryByIdUsingGet1ApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The unique ID of the register entry. */
    entryId: number;
};
export type RdrsvUpdateEntryUsingPutApiResponse = /** status 200 successful operation */ RegisterDataRest;
export type RdrsvUpdateEntryUsingPutApiArg = {
    /** The unique ID of the register entry to be updated. */
    entryId: number;
    /** The unique ID of the register. */
    regId: number;
    /** A flag to determine whether all formula fields in the register entry will be evaluated when the entry is updated. */
    evalFormulas?: boolean;
    /** A lock ID is only required if the editLock property in the register is set to true. */
    lockId?: number;
    /** ID of a device to identify offline lock. */
    deviceId?: string;
    /** Flag used to skip offline lock check. */
    ignoreOfflineLock?: boolean;
    /** A flag to control the format of field validation errors.
    If <b>true</b>,  then each field validation error will be shown as an individual node of the response body.
    If <b>false</b>, the field validation errors will be compressed into a single line message. */
    showAdvancedFieldValidationErrors?: boolean;
    /** <b>RegisterDataRest</b> JSON data used to update the register entry. */
    registerDataRest: RegisterDataRest;
};
export type RdrsvDeleteRegisterEntryUsingDeleteApiResponse = unknown;
export type RdrsvDeleteRegisterEntryUsingDeleteApiArg = {
    /** The unique ID of the register entry to be deleted. */
    entryId: number;
    /** The unique ID of the register. */
    regId: number;
};
export type RdrsvCreateEntryCopyUsingPostApiResponse = /** status 200 successful operation */ RegisterDataRest;
export type RdrsvCreateEntryCopyUsingPostApiArg = {
    /** System register identifier */
    regId: number;
    /** Entry ID */
    entryId: number;
};
export type RdrsvCreateAndLinkUsingPostApiResponse = /** status 200 successful operation */ RegisterDataRest;
export type RdrsvCreateAndLinkUsingPostApiArg = {
    /** The unique ID of the primary register. */
    regId: number;
    /** The unique ID of the register entry that the provided entry ID(s) will be linked to. */
    entryId: number;
    /** The column ID that represents the sub-table field in the primary register. */
    linkColumn: number;
    /** A flag to determine whether all formula fields in newly the created register entry will be evaluated upon creation. */
    evaluateSubtableFormulas?: boolean;
    /** A flag to determine whether all formula fields in the existing primary register entry will be re-evaluated after linkage. */
    evaluatePrimaryFormulas?: boolean;
    /** <b>RegisterDataRest</b> JSON data used to create the register entry. */
    registerDataRest: RegisterDataRest;
};
export type RdrsvAddLinksUsingPutApiResponse = unknown;
export type RdrsvAddLinksUsingPutApiArg = {
    /** The unique ID of the primary register. */
    regId: number;
    /** The unique ID of the register entry that the provided entry ID(s) will be linked to. */
    entryId: number;
    /** The column ID that represents the sub-table field in the primary register. */
    linkColumn: number;
    /** The unique ID(s) of the sub-table entries to be selected in the primary register entry. */
    body: number[];
};
export type RdrsvUpdateStatusUsingPutApiResponse = unknown;
export type RdrsvUpdateStatusUsingPutApiArg = {
    /** The unique ID of the register entry to be updated. */
    entryId: number;
    /** The unique ID of the register. */
    regId: number;
    /** The new status for the register entry. */
    status: string;
};
export type RdrsvTransitionEntryUsingPutApiResponse = /** status 200 successful operation */ RegisterStateRest;
export type RdrsvTransitionEntryUsingPutApiArg = {
    /** The unique ID of the register entry to be updated. */
    entryId: number;
    /** The unique ID of the register. */
    regId: number;
    /** A flag to determine if workflow rules associated with any of the state transitions performed should trigger.
    If <b>true</b>, associated rules will trigger (default).
    If <b>false</b>, workflow rule triggering will be suppressed during the API call. */
    triggerWorkflows?: boolean;
    /** A list of register state names to apply to a register entry. The order that the state transition names appear in the request body dictates the order the states will be applied.
    The state transitions must be defined in an order that is reachable as per the defined state definitions.
    
    For example, if the following was supplied:
    <pre> <code>
    [
        "Open",
        "Under Review",
        "Escalate to manager"
    ]
    </code></pre>
    The register would need to have  <b>Open -> Under Review</b> and <b>Under Review -> Escalate to manager</b> transitions defined in the register configuration.
     */
    body: string[];
};
export type RdrsvGetTableEntriesUsingGetApiResponse = /** status 200 The list of register entries is returned. */ PaginRestResultRegisterDataRest;
export type RdrsvGetTableEntriesUsingGetApiArg = {
    /** The unique ID of the primary register. */
    regId: number;
    /** The unique ID of the primary register entry. The sub-table records will be retrieved from this entry. */
    entryId: number;
    /** The column name that represents the sub-table field in the primary register. For example: "col_xxxxxx". */
    columnName: string;
};
export type RdrsvRemoveLinksUsingPutApiResponse = unknown;
export type RdrsvRemoveLinksUsingPutApiArg = {
    /** The unique ID of the primary register. */
    regId: number;
    /** The unique ID of the primary register entry. The sub-table records will be removed from this entry. */
    entryId: number;
    /** The column name that represents the sub-table field in the primary register. For example: "col_xxxxxx". */
    linkColumn: number;
    /** The unique ID(s) of the sub-table entries to be removed from the primary register entry. */
    body: number[];
};
export type OfflineLockRest = {
    /** The point in time of when the offline lock was created */
    createDate?: string;
    creator?: IdWithNameRest;
    /** The unique ID of the device which created the lock */
    deviceId?: string;
    /** The unique ID of the offline lock object */
    id?: number;
    /** The unique ID of the register entry that is being locked */
    linkedId?: number;
    /** The table name that represents the register */
    linkedTable?: string;
};
export type Property = {
    name: string;
    value?: string;
};
export type Properties = {
    property?: Property[];
};
export type Field = {
    /** The unique ID of a field within a register */
    fieldId?: string;
    /** The column name that represents the field within a register. For example:col_xxxxx */
    fieldName?: string;
    /** Display name of a field */
    label?: string;
    /** A key-value mapping that provides support for additional properties for a register field. This field can include extrapolated data or a display value when the 'simpleValue' is unique system ID. For example, in a CORE section the 'simpleValue' of a 'Created By' field will be the user ID - a parameter will be added for 'displayValues':[USER_DISPLAY_NAME] */
    parameters?: {
        [key: string]: string[];
    };
    /** The data within a field for simple types (Number, Text, Date) */
    simpleValue?: string[];
};
export type Section = {
    /** A flag to determine if the fields within this section are editable */
    editable?: boolean;
    /** A list of fields which contain the field data */
    fields?: Field[];
    /** Display name of a section */
    label?: string;
};
export type UniqueKeys = {
    key?: string[];
};
export type Register = {
    /** The unique application name */
    applicationName?: string;
    /** Indicates whether the record is deleted */
    deleted?: boolean;
    /** The unique ID of the register entry */
    id?: number;
    /** Display name of a register */
    label?: string;
    offlineLock?: OfflineLockRest;
    properties?: Properties;
    /** A list of register sections containing section data */
    sections?: Section[];
    /** The current state of the register. This property will only be present if a register's 'stateful' property is set to true. */
    status?: string;
    uniqueKeys?: UniqueKeys;
};
export type RegisterDataRest = {
    completed?: boolean;
    /** If true, current user has edit access to given entry, otherwise has only view access */
    editableByCurrentUser?: boolean;
    /** Group number */
    group?: number;
    /** Optional group name provided only if grouping by framework field */
    groupName?: string;
    record?: Register;
    /** If true, the register entry will skip system dates */
    skipSystemDates?: boolean;
};
export type LinkData = {
    /** The 'widget' URL query parameter that is used to resolve a system library's URL. This property will only be present when the register entry links are from a system library e.g. Compliance Entry, KRI Entry, Audit Questions.  */
    action?: 'MyKRIEntry' | 'MyComplianceEntry' | 'AuditExecution' | 'IncidentReview';
    /** A flag that indicates whether related entry linking is allowed in the register. A link will be flagged as disabled if the 'targetType' is UNKNOWN or if the user does not have access to the parent register entries. */
    linkEnabled?: boolean;
    /** The unique ID of linked record this link represents. */
    linkId?: number;
    /** The link name as the application name appended with the register label and entry ID. For example - [APPLICATION_NAME] - [REGISTER_NAME] Entry with id: [ENTRY_ID] */
    linkName?: string;
    /** The URL link to the linked entry. This link will open to the linked register entry in Protecht.ERM */
    linkUrl?: string;
    /** Link properties for dynamic and related link types. Related links will have parameters for relation name, relation ID and whether the relation is a reverse relation. Dynamic links will have parameters for application ID, register label, table name and parent register entry ID. */
    params?: {
        [key: string]: string;
    };
    /** The data from the configured 'Display Field' of a register entry. This property uses a register's 'identityColumn' to provide a more human readable identifier for a register entry. To check what field is used as the 'targetAlias' for a register you can view the 'Display Field' in the register designer. Alternatively you can query the Register Configuration endpoint for a register's 'identityColumn'. */
    targetAlias?: string;
    /** The column name that represents the linkage. For example: "col_xxxxxx". */
    targetColumn?: string;
    /** The unique ID of the register entry. If the 'linkType' is RELATED, targetId will be the ID of the related entries. If the 'linkType' is DYNAMIC, targetId will be the primary register entry ID. */
    targetId?: number;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    targetTable?: string;
    /** Target table type */
    targetType?: 'KRI_ENTRY' | 'COMPLIANCE_ENTRY' | 'AUDIT' | 'APPLICATION' | 'UNKNOWN' | 'LINK_HEADER' | 'BOW_TIE_DIAGRAM';
};
export type RelatedEntryLinkRest = {
    /** The unique ID for the parent register entry */
    childId?: number;
    /** The unique ID for the register entry to be included in the Linked To field of the parent entry */
    parentId?: number;
    /** The relation type ID */
    relationId?: number;
};
export type PaginRestResultRegisterDataRest = {
    maxPage?: number;
    records?: RegisterDataRest[];
    response?: RegisterDataRest;
    totalCount?: number;
};
export type RelationTypeRest = {
    /** Relationship ID */
    id?: number;
    /** Relationship name */
    relationName?: string;
    /** A flag that indicates whether this is a reverse relationship */
    reverse?: boolean;
    /** Reverse relationship name */
    reverseRelationName?: string;
};
export type FrameworkLevel = {
    frameworkId?: number;
    frameworkName?: string;
    level?: number;
    levelFullName?: string;
    levelIdentification?: string;
    name?: string;
};
export type RegisterStateRest = {
    /** State unique ID */
    id?: number;
    /** If true, this state will be regarded as the final state. Once a register entry is in the final state, it can no longer be edited unless the register has a reopen transition. To be able to reopen closed entries, a register must have at least three states and three transitions. */
    isFinal?: boolean;
    /** If true, this state will be regarded as the initial state that an entry will be in when a new entry is created. Only one initial state can exist per state definition. */
    isInitial?: boolean;
    /** State name */
    name?: string;
    /** Overdue flag tells if overdue processing is enabled in this state. */
    overdueProcessing?: boolean;
    /** State unique ID - this ID is randomly generated and unique for every state in the system */
    uuid?: string;
};
export type ErsRefreshUsingPutApiResponse = unknown;
export type ErsRefreshUsingPutApiArg = void;
export type ErsSynchronizeEntriesUsingPostApiResponse = /** status 200 Register entries are created or updated as applicable. */ RegisterSynchronizeResponse;
export type ErsSynchronizeEntriesUsingPostApiArg = {
    /** The unique ID of the custom register action. */
    registerActionId: number;
    /** JSON data that describes how responses from the external API are handled. */
    registerSynchronizeRequest: RegisterSynchronizeRequest;
};
export type Failure = {
    details?: string;
    message?: string;
};
export type RegisterRowFailure = {
    failures?: Failure[];
    row?: number;
    type?: string;
};
export type RegisterSynchronizeResponse = {
    /** The list of rows that failed to synchronize */
    failures?: RegisterRowFailure[];
    /** A list of fields defined in the custom action and their corresponding values extracted from the external API response data. */
    partialResponse?: {
        [key: string]: object;
    };
    /** Register entries that were successfully synchronized, listed by ID */
    synchronizedEntriesIds?: number[];
    totalCount?: number;
};
export type SynchronizationContext = {
    linkedRecords?: number[];
    linkingColumnName?: string;
    parentRecordId?: number;
};
export type RegisterSynchronizeRequest = {
    context?: SynchronizationContext;
    filterMap?: {
        [key: string]: object;
    };
    jsonRecord?: string;
};
export type FersGetAllowedExtensionsUsingGetApiResponse = /** status 200 successful operation */ PaginRestResultFileExtensionRest;
export type FersGetAllowedExtensionsUsingGetApiArg = void;
export type FileExtensionRest = {
    /** FileExtension allowed */
    allowed?: boolean;
    /** FileExtension description */
    description?: string;
    /** FileExtension system ID */
    id?: number;
    /** FileExtension name */
    name?: string;
    /** FileExtension supportedTypes */
    supportedTypes?: string;
    /** FileExtension system */
    system?: boolean;
};
export type PaginRestResultFileExtensionRest = {
    maxPage?: number;
    records?: FileExtensionRest[];
    response?: FileExtensionRest;
    totalCount?: number;
};
export type FrsvEvaluateAllFormulasUsingPostApiResponse = /** status 200 The register entry is returned with formula fields evaluated. */ RegisterDataRest;
export type FrsvEvaluateAllFormulasUsingPostApiArg = {
    /** <b>TableMetadataRest</b> JSON data used to identify the register entry. Provide the entry's unique ID at <b>record.id</b> and the register's label at <b>record.label</b>. */
    registerDataRest: RegisterDataRest;
};
export type FrsvEvaluateFormulaColumnUsingPostApiResponse = /** status 200 The register entry is returned with the formula field evaluated. */ object;
export type FrsvEvaluateFormulaColumnUsingPostApiArg = {
    /** The column name that represents the formula field within the register. For example: "col_xxxxxx". */
    colName: string;
    /** JSON data used to identify the register entry. Provide the entry's unique ID at <b>record.id</b> as well as the register label at <b>record.label</b>. */
    registerDataRest: RegisterDataRest;
};
export type FcGetFrameworksUsingGetApiResponse = /** status 200 successful operation */ FrameworkViewRead[];
export type FcGetFrameworksUsingGetApiArg = {
    category?: number;
};
export type FcCreateFrameworkUsingPostApiResponse = /** status 200 successful operation */ FrameworkViewRead;
export type FcCreateFrameworkUsingPostApiArg = {
    body: {
        [key: string]: string[];
    };
};
export type FcGetFrameworkCategoriesUsingGetApiResponse = /** status 200 successful operation */ CategoryViewRead[];
export type FcGetFrameworkCategoriesUsingGetApiArg = void;
export type FcExportDataRegisterUsingGetApiResponse = /** status 200 successful operation */ string;
export type FcExportDataRegisterUsingGetApiArg = {
    /** Table name of register which is set as data register. */
    tableName: string;
};
export type FcImportDataRegisterUsingPostApiResponse = /** status 200 successful operation */ string;
export type FcImportDataRegisterUsingPostApiArg = {
    /** Table name of register which is set as data register. */
    tableName: string;
    /** Framework id to which data will be imported. */
    frameworkId: number;
    body: {
        file: Blob;
    };
};
export type FcImportVerifyDataRegisterUsingPostApiResponse = /** status 200 successful operation */ string;
export type FcImportVerifyDataRegisterUsingPostApiArg = {
    /** Table name of register which is set as data register. */
    tableName: string;
    /** Framework id to which data will be verified for import. */
    frameworkId: number;
    body: {
        file: Blob;
    };
};
export type FcGetFrameworkStatusUsingGetApiResponse = /** status 200 successful operation */ FrameworkStatusViewRead;
export type FcGetFrameworkStatusUsingGetApiArg = void;
export type FcImportFrameworkUsingPostApiResponse = unknown;
export type FcImportFrameworkUsingPostApiArg = {
    body: {
        file: Blob;
    };
};
export type FcGetFrameworkListUsingGetApiResponse = /** status 200 successful operation */ FrameworkStubRead[];
export type FcGetFrameworkListUsingGetApiArg = void;
export type FcGetMetadataRegistersListUsingGetApiResponse = /** status 200 successful operation */ FrameworkRegisterRead[];
export type FcGetMetadataRegistersListUsingGetApiArg = {
    search?: string;
};
export type FcCreateNodeUsingPostApiResponse = /** status 200 successful operation */ number;
export type FcCreateNodeUsingPostApiArg = {
    nodeDetail: NodeDetail;
};
export type FcGetNodeUsingGetApiResponse = /** status 200 successful operation */ NodeDetailRead;
export type FcGetNodeUsingGetApiArg = {
    id: number;
};
export type FcUpdateNodeUsingPutApiResponse = /** status 200 successful operation */ NodeDetailRead;
export type FcUpdateNodeUsingPutApiArg = {
    id: number;
    nodeDetail: NodeDetail;
};
export type FcDeleteNodeUsingDeleteApiResponse = unknown;
export type FcDeleteNodeUsingDeleteApiArg = {
    id: number;
};
export type FcGetFrameworkUsingGetApiResponse = /** status 200 successful operation */ FrameworkDetailRead;
export type FcGetFrameworkUsingGetApiArg = {
    id: number;
};
export type FcUpdateFrameworkDetailsUsingPostApiResponse = /** status 200 successful operation */ FrameworkDetailRead;
export type FcUpdateFrameworkDetailsUsingPostApiArg = {
    id: number;
    body: {
        data: any;
        fileList?: any[];
    };
};
export type FcDeleteFrameworkUsingDeleteApiResponse = unknown;
export type FcDeleteFrameworkUsingDeleteApiArg = {
    id: number;
};
export type FcDuplicateFrameworkUsingPostApiResponse = /** status 200 successful operation */ FrameworkViewRead;
export type FcDuplicateFrameworkUsingPostApiArg = {
    id: number;
    body: {
        [key: string]: string[];
    };
};
export type FcExportFrameworkUsingGetApiResponse = /** status 200 successful operation */ HttpEntityObject;
export type FcExportFrameworkUsingGetApiArg = {
    id: number;
    type?: 'SIMPLE' | 'ALL';
    internal?: boolean;
};
export type FcGetFrameworkNodesHierarchyUsingGetApiResponse = /** status 200 successful operation */ HierarchicalNodeRead[];
export type FcGetFrameworkNodesHierarchyUsingGetApiArg = {
    id: number;
};
export type FcUpdateNodesHierarchyUsingPutApiResponse = /** status 200 successful operation */ HierarchicalNodeRead[];
export type FcUpdateNodesHierarchyUsingPutApiArg = {
    id: number;
    body: HierarchicalNode[];
};
export type FcGetFrameworkNodesUsingGetApiResponse = /** status 200 successful operation */ SimpleNodeRead[];
export type FcGetFrameworkNodesUsingGetApiArg = {
    id: number;
};
export type FcUpdateStatusUsingPutApiResponse = /** status 200 successful operation */ FrameworkDetailRead;
export type FcUpdateStatusUsingPutApiArg = {
    id: number;
    newStatusId: number;
};
export type CategoryView = {};
export type CategoryViewRead = {
    id?: number;
    name?: string;
};
export type FrameworkRegister = {};
export type FrameworkRegisterRead = {
    appId?: number;
    id?: number;
    readOnly?: boolean;
    registerName?: string;
    tableName?: string;
};
export type UserViewShared = {
    id?: number;
    name?: string;
    username?: string;
};
export type FrameworkView = {
    category?: CategoryView;
    metadataRegister?: FrameworkRegister;
    owner?: UserViewShared;
    reviewer?: UserViewShared;
};
export type FrameworkViewRead = {
    category?: CategoryViewRead;
    description?: string;
    id?: number;
    lastReviewDate?: string;
    metadataRegister?: FrameworkRegisterRead;
    name?: string;
    owner?: UserViewShared;
    reviewer?: UserViewShared;
    status?: string;
    version?: string;
};
export type FrameworkStatusView = {};
export type FrameworkStatusViewRead = {
    id?: number;
    name?: string;
};
export type FrameworkStub = {};
export type FrameworkStubRead = {
    id?: number;
    name?: string;
    status?: string;
    version?: string;
};
export type NodeDetail = {};
export type NodeLinkDetail = {};
export type NodeLinkDetailRead = {
    frameworkName?: string;
    frameworkVersion?: string;
    linkId?: number;
    nodeName?: string;
};
export type NodeLinkTarget = {};
export type NodeLinkTargetRead = {
    operation?: 'NEW' | 'VIEW' | 'DELETE';
    targetFramework?: number;
    targetNode?: number;
};
export type NodeLink = {
    details?: NodeLinkDetail;
    target?: NodeLinkTarget;
};
export type NodeLinkRead = {
    details?: NodeLinkDetailRead;
    linkId?: number;
    status?: 'NEW' | 'VIEW' | 'DELETE';
    target?: NodeLinkTargetRead;
};
export type NodeDetailRead = {
    content?: string;
    enableLinks?: boolean;
    frameworkId?: number;
    id?: number;
    links?: NodeLinkRead[];
    nodeOrder?: number;
    parentNode?: number;
    title?: string;
};
export type FrameworkDetail = {
    category?: CategoryView;
    metadataRegister?: FrameworkRegister;
    owner?: UserViewShared;
    reviewer?: UserViewShared;
    status?: FrameworkStatusView;
};
export type FrameworkFile = {};
export type FrameworkFileRead = {
    name?: string;
    uid?: string;
    url?: string;
};
export type WorklogShared = {
    createDate?: string;
    id?: number;
    log?: string;
    sourceId?: number;
    userId?: number;
    userName?: string;
};
export type FrameworkDetailRead = {
    allowedRegisters?: FrameworkRegisterRead[];
    category?: CategoryViewRead;
    description?: string;
    docLinks?: string[];
    fileLinkList?: FrameworkFileRead[];
    id?: number;
    lastReviewDate?: string;
    metadataRegister?: FrameworkRegisterRead;
    name?: string;
    owner?: UserViewShared;
    reviewer?: UserViewShared;
    status?: FrameworkStatusViewRead;
    version?: string;
    worklog?: WorklogShared[];
};
export type HttpEntityObject = {
    body?: object;
    headers?: {
        [key: string]: string[];
    };
};
export type HierarchicalNode = {
    childNodes?: HierarchicalNode[];
    id?: number;
    name?: string;
    order?: number;
};
export type HierarchicalNodeRead = {
    childNodes?: HierarchicalNodeRead[];
    id?: number;
    name?: string;
    order?: number;
    registerEntryId?: number;
    registerName?: string;
};
export type SimpleNode = {};
export type SimpleNodeRead = {
    id?: number;
    name?: string;
    parentId?: number;
};
export type ArsiUploadAttachmentUsingPostApiResponse = /** status 200 The attachment was  added successfully. */ AttachmentsRestInternal;
export type ArsiUploadAttachmentUsingPostApiArg = {
    /** The unique ID of the register entry where the attachment is to be added. */
    linkedId: number;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    linkedTable: string;
    /** The column name that represents the attachment field within the register. For example: "col_xxxxxx". */
    linkedColumn?: string;
    /** The attachment to be added to the register entry. */
    httpServletRequest: HttpServletRequest;
};
export type AttachmentsRestInternal = {
    /** Attachment category */
    category?: string;
    /** Attachment creation date */
    createDate?: string;
    /** Unique ID of the user who created the attachment */
    createdBy?: number;
    /** Name of the user who created the attachment */
    createdByName?: string;
    /** Attachment description */
    description?: string;
    /** Attachment name */
    fileName?: string;
    /** Attachment size */
    fileSize?: number;
    /** Attachment system ID */
    id?: number;
    /** If true, the attachment is accessible and useable within a register entry. If false, the attachment will remain in Protecht.ERM but be unavilable for use in register entries. */
    isActive?: boolean;
    /**  Unique ID of the user who last modified the attachment */
    lastModifiedBy?: number;
    /** Attachment last modified date */
    lastModifiedDate?: string;
    /** Column name that represents the field the attachment is added to */
    linkColumn?: string;
    /** Unique ID of the register entry where the attachment is added */
    linkId?: number;
    /** Table name that represents the register where the attachment is added to an entry */
    linkTable?: string;
    /** Attachment unique ID - this ID is randomly generated and unique for every attachment uploaded to the system */
    uuid?: string;
    /** Attachment version */
    version?: number;
};
export type BtciGetDiagramsUsingPostApiResponse = /** status 200 successful operation */ PaginRestResultBowTieDiagramRest;
export type BtciGetDiagramsUsingPostApiArg = {
    page?: number;
    size?: number;
    viewId?: number;
    'sort-by'?: string;
    'sort-dir'?: 'ASC' | 'DESC';
    'group-by'?: string;
    'tag-type'?: number;
    'filter-by'?: string;
    'filter-value'?: string;
    tags?: number[];
    'tag-operator'?: string;
    filterContextRest: FilterContextRest;
};
export type BursiGetBusinessUnitsTreeUsingGetApiResponse =
    /** status 200 Returns business units tree matching the search criteria in the response body. */ BusinessUnitSimpleTreeFilteredRestInternal;
export type BursiGetBusinessUnitsTreeUsingGetApiArg = {
    /** Search string to filter business units by name */
    value: string;
    /** Use this parameter to include archived business units in the results */
    includeArchived?: boolean;
};
export type BursiGetRootUsingGetApiResponse =
    /** status 200 Returns the root business unit and its immediate child units in the response body. */ BusinessUnitRestInternal[];
export type BursiGetRootUsingGetApiArg = {
    /** Use this parameter to include archived children of the business unit. */
    includeArchived?: boolean;
};
export type BursiGetRootSimpleUsingGetApiResponse =
    /** status 200 Returns the root business unit and its immediate child units in the response body. */ BusinessUnitSimpleRestInternal[];
export type BursiGetRootSimpleUsingGetApiArg = {
    /** Use this parameter to include archived children of the business unit. */
    includeArchived?: boolean;
};
export type BursiGetRootTreeUsingGetApiResponse =
    /** status 200 Returns the root business unit with all child units as a tree hierarchy in the response body. */ BusinessUnitSimpleRestInternal[];
export type BursiGetRootTreeUsingGetApiArg = {
    /** Use this parameter to include archived children of the business unit. */
    includeArchived?: boolean;
};
export type BursiGetBuUsingGetApiResponse = /** status 200 The business unit matching the unique ID provided is returned. */ BusinessUnitRestInternal;
export type BursiGetBuUsingGetApiArg = {
    /** The unique ID of the business unit. */
    businessUnitId: number;
    /** Use this parameter to include archived children of the business unit. */
    includeArchived?: boolean;
};
export type BursiDeleteBuUsingDeleteApiResponse = unknown;
export type BursiDeleteBuUsingDeleteApiArg = {
    /** The unique ID of the business unit to be deleted. */
    businessUnitId: number;
};
export type BursiGetBuSimpleUsingGetApiResponse =
    /** status 200 The business unit matching the unique ID provided is returned. */ BusinessUnitSimpleRestInternal;
export type BursiGetBuSimpleUsingGetApiArg = {
    /** The unique ID of the business unit. */
    businessUnitId: number;
    /** Use this parameter to include archived children of the business unit. */
    includeArchived?: boolean;
};
export type BursiGetBuTreeUsingGetApiResponse =
    /** status 200 Returns the matching business unit and all child units as a tree hierarchy in the response body. */ BusinessUnitSimpleRestInternal;
export type BursiGetBuTreeUsingGetApiArg = {
    /** The unique ID of the business unit. */
    businessUnitId: number;
    /** Use this parameter to include archived children of the business unit. */
    includeArchived?: boolean;
};
export type BusinessUnitSimpleRestInternal = {
    /** Indicates if the business unit is archived */
    archived?: boolean;
    /** A list of child units (simple model) that belong to this business unit */
    children?: BusinessUnitSimpleRestInternal[];
    /** Business unit ID */
    id?: number;
    /** Business unit name */
    name: string;
    /** A number of children for business unit */
    numberOfChildren?: number;
    /** Business unit parent ID */
    parentId: number;
};
export type BusinessUnitSimpleTreeFilteredRestInternal = {
    /** A list of business units (internal simple model) */
    businessUnitSimpleRestInternals?: BusinessUnitSimpleRestInternal[];
    /** Business unit matching IDs */
    matchingBusinessunits?: number[];
};
export type BusinessUnitRestInternal = {
    /** Indicates if the business unit is archived */
    archived?: boolean;
    calendar?: IdWithNameRest;
    /** A list of child units that belong to this business unit */
    children?: BusinessUnitRestInternal[];
    /** Business unit city */
    city?: string;
    country?: IdWithNameRest;
    /** Business unit ID */
    id?: number;
    /** Business unit latitude */
    lat?: number;
    /** Business unit longtitude */
    lon?: number;
    manager?: IdWithNameRest;
    /** Business unit name */
    name: string;
    /** A number of children for business unit */
    numberOfChildren?: number;
    parent: IdWithNameRest;
    state?: IdWithNameRest;
    /** Business unit street */
    street?: string;
    /** A list of tags that belong to this business unit */
    tags?: TagRest[];
    /** A Business unit type. Default is STANDARD. */
    type?: string;
    /** Business unit postal code */
    zip?: string;
};
export type CcciCreateCategoryUsingPostApiResponse = /** status 200 successful operation */ ControlCategoryRestRead;
export type CcciCreateCategoryUsingPostApiArg = {
    /** Control category data */
    controlCategoryRest: ControlCategoryRest;
};
export type CcciGetTreeUsingGetApiResponse = /** status 200 successful operation */ ControlCategoryTreeNodeRestRead;
export type CcciGetTreeUsingGetApiArg = {
    /** Control category ID. Default is root category. */
    id?: number;
    /** Maximum depth to traverse (0 = no limit as default value) */
    depth?: number;
    /** Include controls in result */
    includeControls?: boolean;
    /** Include questions in result */
    includeQuestions?: boolean;
};
export type CcciGetCategoryUsingGetApiResponse = /** status 200 successful operation */ ControlCategoryRestRead;
export type CcciGetCategoryUsingGetApiArg = {
    /** Category ID */
    id: number;
    /** Include controls */
    includeControls?: boolean;
    /** Include questions */
    includeQuestions?: boolean;
};
export type CcciUpdateCategoryUsingPutApiResponse = /** status 200 successful operation */ ControlCategoryRestRead;
export type CcciUpdateCategoryUsingPutApiArg = {
    /** Control category ID */
    id: number;
    /** Updated control category data */
    controlCategoryRest: ControlCategoryRest;
};
export type CcciDeleteCategoryUsingDeleteApiResponse = unknown;
export type CcciDeleteCategoryUsingDeleteApiArg = {
    /** Control category ID */
    id: number;
    /** Force delete even if category has children or controls */
    force?: boolean;
};
export type CcciGetChildrenUsingGetApiResponse = /** status 200 successful operation */ ControlCategoryRestRead[];
export type CcciGetChildrenUsingGetApiArg = {
    /** Parent category ID */
    id: number;
    /** Include controls */
    includeControls?: boolean;
    /** Include questions */
    includeQuestions?: boolean;
};
export type ControlCategoryRest = {
    /** Control category type (e.g., LEGISLATION) */
    categoryType?: string;
    /** Control category description */
    description?: string;
    /** Control category name */
    name: string;
    /** Parent ID associated with this category */
    parentId?: number;
    /** Risk event ID associated with this category */
    riskEventId?: number;
};
export type ControlRest = {
    /** Category ID this control belongs to */
    categoryId?: number;
    /** Control type (CONTROL/QUESTION) - distinguishes between standard controls and compliance questions */
    controlType?: string;
    /** Created by ID */
    createdById?: number;
    /** Creation date */
    creationDate?: string;
    /** Default frequency (M=Monthly, etc.) */
    defaultFrequency?: string;
    /** Control description */
    description?: string;
    /** End date */
    endDate?: string;
    /** Last modified by ID */
    lastModifiedBy?: number;
    /** Last modified date */
    lastModifiedDate?: string;
    /** Control name */
    name?: string;
    /** Risk event IDs associated with this control */
    riskEventIds?: number[];
};
export type ControlRestRead = {
    /** Category ID this control belongs to */
    categoryId?: number;
    /** Control type (CONTROL/QUESTION) - distinguishes between standard controls and compliance questions */
    controlType?: string;
    /** Created by ID */
    createdById?: number;
    /** Creation date */
    creationDate?: string;
    /** Default frequency (M=Monthly, etc.) */
    defaultFrequency?: string;
    /** Control description */
    description?: string;
    /** End date */
    endDate?: string;
    /** Control ID */
    id?: number;
    /** Last modified by ID */
    lastModifiedBy?: number;
    /** Last modified date */
    lastModifiedDate?: string;
    /** Control name */
    name?: string;
    /** Risk event IDs associated with this control */
    riskEventIds?: number[];
};
export type ControlCategoryRestRead = {
    /** Control category type (e.g., LEGISLATION) */
    categoryType?: string;
    /** IDs of child categories */
    childIds?: number[];
    /** Controls in this category (if requested) */
    controls?: ControlRestRead[];
    /** Control category description */
    description?: string;
    /** Whether this category has children */
    hasChildren?: boolean;
    /** Control category ID */
    id?: number;
    /** Control category name */
    name: string;
    /** Parent ID associated with this category */
    parentId?: number;
    /** Questions in this category (if requested) */
    questions?: ControlRestRead[];
    /** Risk event ID associated with this category */
    riskEventId?: number;
};
export type ControlCategoryTreeNodeRest = {
    /** Control category type (e.g., LEGISLATION) */
    categoryType?: string;
    /** Child category nodes */
    children?: ControlCategoryTreeNodeRest[];
    /** Control category description */
    description?: string;
    /** Control category name */
    name: string;
    /** Parent ID associated with this category */
    parentId?: number;
    /** Risk event ID associated with this category */
    riskEventId?: number;
};
export type ControlCategoryTreeNodeRestRead = {
    /** Control category type (e.g., LEGISLATION) */
    categoryType?: string;
    /** IDs of child categories */
    childIds?: number[];
    /** Child category nodes */
    children?: ControlCategoryTreeNodeRestRead[];
    /** Controls in this category (if requested) */
    controls?: ControlRestRead[];
    /** Control category description */
    description?: string;
    /** Whether this category has children */
    hasChildren?: boolean;
    /** Control category ID */
    id?: number;
    /** Control category name */
    name: string;
    /** Parent ID associated with this category */
    parentId?: number;
    /** Questions in this category (if requested) */
    questions?: ControlRestRead[];
    /** Risk event ID associated with this category */
    riskEventId?: number;
};
export type RdrsiCreateEntryFromTemplateUsingPostApiResponse = /** status 200 successful operation */ RegisterDataRestInternal;
export type RdrsiCreateEntryFromTemplateUsingPostApiArg = {
    /** The unique ID of the template. */
    templateId: string;
};
export type RdrsiGetFrameworkLinkedRegisterEntriesUsingPostApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultRegisterDataRestInternal;
export type RdrsiGetFrameworkLinkedRegisterEntriesUsingPostApiArg = {
    /** The unique ID of the framework node. */
    frameworkNodeId: number;
    /** The unique ID of the register. */
    registerId: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** If only entries for My Tasks should be returned, the list of requested states should be provided. (1 - OPEN, 2 - DUENOW, 3 - OVERDUE) */
    myTaskStates?: number[];
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** True if result is used for the suggestions */
    isSuggestion?: boolean;
    /** Register state ID to filter entries by */
    stateId?: number;
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RdrsiGetEntryByIdUsingGetApiResponse = /** status 200 The register entry is returned in the response body. */ RegisterDataRestInternal;
export type RdrsiGetEntryByIdUsingGetApiArg = {
    /** The table name that represents the register. For example: "table_xxxxxx". */
    tableName: string;
    /** The unique ID of the register entry. */
    entryId: number;
};
export type GetRegisterEntriesSearchPost1ApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultRegisterDataRestInternal;
export type GetRegisterEntriesSearchPost1ApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** If only entries for My Tasks should be returned, the list of requested states should be provided. (1 - OPEN, 2 - DUENOW, 3 - OVERDUE) */
    myTaskStates?: number[];
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** True if result is used for the suggestions */
    isSuggestion?: boolean;
    /** Register state ID to filter entries by */
    stateId?: number;
    /** Register parent entry ID */
    parentEntryId?: number;
    /** Subtable column name */
    subtableColumn?: string;
    /** Parent table name */
    parentTableName?: string;
    /** Framework level grouping configuration. Will be considered only if grouping by Framework Link field applied as well */
    fwLevelGrouping?: string;
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RegisterDataRestInternal = {
    completed?: boolean;
    /** If true, current user has edit access to given entry, otherwise has only view access */
    editableByCurrentUser?: boolean;
    /** Indicates the current status of the entry schedule for this register entry */
    entryScheduleStatus?: 'FullFilled' | 'None' | 'Off' | 'Scheduled';
    /** Group number */
    group?: number;
    /** Optional group name provided only if grouping by framework field */
    groupName?: string;
    record?: Register;
    /** If true, the register entry will skip system dates */
    skipSystemDates?: boolean;
};
export type PaginRestResultRegisterDataRestInternal = {
    maxPage?: number;
    records?: RegisterDataRestInternal[];
    response?: RegisterDataRestInternal;
    totalCount?: number;
};
export type EsrsiGetEntryScheduleUsingGetApiResponse = /** status 200 The entry schedule is returned in the response body. */ EntryScheduleRestInternal;
export type EsrsiGetEntryScheduleUsingGetApiArg = {
    /** The unique ID of the entry schedule to be returned. */
    entryScheduleId: number;
    /** Name of table for entry schedule to be returned. */
    tableName: string;
};
export type EsrsiCreateEntryScheduleUsingPostApiResponse = /** status 200 successful operation */ EntryScheduleRestInternal;
export type EsrsiCreateEntryScheduleUsingPostApiArg = {
    /** The unique ID of the entry schedule to be updated. */
    entryScheduleId: number;
    /** Name of table for entry schedule to be returned. */
    tableName: string;
    /** <b>EntryScheduleRestInternal</b> JSON data used to create the entry schedule. */
    entryScheduleRestInternal: EntryScheduleRestInternal;
};
export type EsrsiUpdateEntryScheduleUsingPutApiResponse = /** status 200 successful operation */ EntryScheduleRestInternal;
export type EsrsiUpdateEntryScheduleUsingPutApiArg = {
    /** The unique ID of the entry schedule to be updated. */
    entryScheduleId: number;
    /** Name of table for entry schedule to be returned. */
    tableName: string;
    /** <b>EntryScheduleRestInternal</b> JSON data used to update the entry schedule. */
    entryScheduleRestInternal: EntryScheduleRestInternal;
};
export type ParentEntryRestInternal = {
    /** The name of the column in the parent entry */
    columnName?: string;
    /** The unique identifier of the parent entry */
    entryId?: number;
    /** The name of the register containing the parent entry */
    register?: string;
};
export type EntryScheduleConfigRestInternal = {
    /** List of field names to be copied from the parent entry */
    copyFields?: string[];
    /** Flag indicating whether to copy links from parent entries */
    copyParentsLinks?: boolean;
    parentEntry?: ParentEntryRestInternal;
    /** Identifier of the template to be used for entry generation */
    templateId?: string;
};
export type PeriodRest = {
    /** The time unit of the period (e.g., DAY, WEEK, MONTH, YEAR) */
    unit?: 'DAY' | 'WEEK' | 'MONTH' | 'YEAR';
    /** The numeric value representing the length of the period */
    value?: number;
};
export type EntryScheduleRestInternal = {
    /** Flag indicating if the entry schedule is active */
    active: boolean;
    config: EntryScheduleConfigRestInternal;
    /** Unique identifier of the associated entry */
    entryId: number;
    /** Mode determining how entries are generated */
    generationMode: number;
    periodicity: PeriodRest;
    preGenerationPeriod: PeriodRest;
    /** Flag indicating if the schedule has been processed */
    processed?: boolean;
    referenceDateColumnMetaData: IdWithNameRest;
    tableMetaData: IdWithNameRest;
};
export type FrsiEvaluateFormulaColumnUsingPostApiResponse = /** status 200 The register entry is returned with the formula field evaluated. */ RegisterDataRest;
export type FrsiEvaluateFormulaColumnUsingPostApiArg = {
    /** The column name that represents the formula field within the register. For example: "col_xxxxxx". */
    colName: string;
    /** JSON data used to identify the register entry. Provide the entry's unique ID at <b>record.id</b> as well as the register label at <b>record.label</b>. */
    registerDataRest: RegisterDataRest;
};
export type GetRolesApiResponse = /** status 200 The list of roles is returned. */ PaginRestResultRoleRestInternal;
export type GetRolesApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** Name of role */
    name?: string;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RrsiGetRolesUsingPostApiResponse = /** status 200 Returns a list of roles in the response body. */ PaginRestResultRoleRestInternal;
export type RrsiGetRolesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RoleRestInternal = {
    /** Set true if role is administrative */
    administrative?: boolean;
    /** The home dashboard for the role */
    dashboard?: string;
    /** Role description */
    description?: string;
    /** The homepage for the role */
    homepage?: string;
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
    /** Count of permissions assigned to this role */
    permissionCount?: number;
    /** Count of users assigned by this role */
    userCount?: number;
    /** The user type for the role */
    userType?: string;
};
export type PaginRestResultRoleRestInternal = {
    maxPage?: number;
    records?: RoleRestInternal[];
    response?: RoleRestInternal;
    totalCount?: number;
};
export type PrsiGetStatesUsingPostApiResponse = /** status 200 Returns a list of states in the response body. */ PaginRestResultStateRest;
export type PrsiGetStatesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type StateRest = {
    /** Country Name */
    countryName?: string;
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
};
export type PaginRestResultStateRest = {
    maxPage?: number;
    records?: StateRest[];
    response?: StateRest;
    totalCount?: number;
};
export type TmrsiGetRegistersSummaryUsingGetApiResponse = /** status 200 Success */ RegisterSummaryDto[];
export type TmrsiGetRegistersSummaryUsingGetApiArg = {
    /** The unique ID of an application. */
    applicationId?: number;
};
export type RegisterSummaryDto = {
    favourite?: boolean;
    /** Register unique ID */
    id?: string;
    /** Display name of a register */
    label?: string;
    /** Last Modified Date */
    lastModifiedDate?: string;
    /** There are 2 possible types of registers. Primary: the most commonly used and the default type. Library/Shared: a sub-table whose entries can be used in multiple registers (many to many relationship). */
    registerType?: 'Primary' | 'Private' | 'Shared' | 'Questionnaire' | 'CentralLibrary' | 'Framework';
    /** Application scope setting relevant only to library registers. Options are Application (only available to be used within that specific application) or Global (can be shared across other applications). */
    scope?: 'Application' | 'Global';
    /** The table name that represents the register */
    tableName?: string;
    /** Visibility setting relevant only to library registers. Options are Default (visible only with other library registers) and Always (visible to all other registers) */
    visibility?: 'Default' | 'Always' | 'Hidden';
};
export type HttpNotFound = {
    cause?: Throwable;
    clientMessage?: string;
    localizedMessage?: string;
    message?: string;
    severe?: boolean;
    stackTrace?: StackTraceElement[];
    status?: number;
    suppressed?: Throwable[];
};
export type Forbidden = {
    cause?: Throwable;
    clientMessage?: string;
    localizedMessage?: string;
    message?: string;
    severe?: boolean;
    stackTrace?: StackTraceElement[];
    status?: number;
    suppressed?: Throwable[];
};
export type InternalServerError = {
    cause?: Throwable;
    clientMessage?: string;
    localizedMessage?: string;
    message?: string;
    severe?: boolean;
    stackTrace?: StackTraceElement[];
    status?: number;
    suppressed?: Throwable[];
};
export type RcGetRolesUsingGetApiResponse = /** status 200 successful operation */ PagingResponseRoleListView;
export type RcGetRolesUsingGetApiArg = {
    query?: string;
    type?: string;
    'page-size'?: number;
    'page-number'?: number;
    sort?: string;
    dir?: string;
};
export type RcCreateRoleUsingPostApiResponse = /** status 200 successful operation */ RoleWithPermissions;
export type RcCreateRoleUsingPostApiArg = {
    roleWithPermissions: RoleWithPermissions;
};
export type RcGetRoleCountUsingGetApiResponse = /** status 200 successful operation */ number;
export type RcGetRoleCountUsingGetApiArg = void;
export type RcGetRepositoryHierarchyUsingGetApiResponse = /** status 200 successful operation */ RepositoryNode[];
export type RcGetRepositoryHierarchyUsingGetApiArg = void;
export type RcGetHomepageTypesUsingGetApiResponse = /** status 200 successful operation */ string[];
export type RcGetHomepageTypesUsingGetApiArg = void;
export type RcImportRoleUsingPostApiResponse = /** status 200 successful operation */ RoleWithPermissions;
export type RcImportRoleUsingPostApiArg = {
    role: Role;
};
export type RcGetApplicationModulesUsingGetApiResponse = /** status 200 successful operation */ ApplicationModule[];
export type RcGetApplicationModulesUsingGetApiArg = void;
export type RcGetApplicationModulesUsingGet1ApiResponse = /** status 200 successful operation */ PermissionsHierarchy;
export type RcGetApplicationModulesUsingGet1ApiArg = {
    hash?: string;
};
export type RcGetRegisterModulesUsingGetApiResponse = /** status 200 successful operation */ RegisterModule[];
export type RcGetRegisterModulesUsingGetApiArg = {
    appId: number;
};
export type RcGetRegisterModuleContentUsingGetApiResponse = /** status 200 successful operation */ RegisterModuleContent;
export type RcGetRegisterModuleContentUsingGetApiArg = {
    registerId: number;
};
export type RcHelloUsingGetApiResponse = unknown;
export type RcHelloUsingGetApiArg = void;
export type RcGetUserTypesUsingGetApiResponse = /** status 200 successful operation */ UserType[];
export type RcGetUserTypesUsingGetApiArg = void;
export type RcGetRoleByIdUsingGetApiResponse = /** status 200 successful operation */ RoleView;
export type RcGetRoleByIdUsingGetApiArg = {
    id: number;
    loadPermissions?: boolean;
};
export type RcExportRoleUsingGetApiResponse = /** status 200 successful operation */ HttpEntityObject;
export type RcExportRoleUsingGetApiArg = {
    id: number;
};
export type RcUpdateRoleUsingPutApiResponse = /** status 200 successful operation */ RoleWithPermissions;
export type RcUpdateRoleUsingPutApiArg = {
    roleId: number;
    roleWithPermissions: RoleWithPermissions;
};
export type RcDeleteRoleUsingDeleteApiResponse = unknown;
export type RcDeleteRoleUsingDeleteApiArg = {
    roleId: number;
    force?: boolean;
};
export type RcDuplicateRoleUsingPostApiResponse = /** status 200 successful operation */ RoleWithPermissions;
export type RcDuplicateRoleUsingPostApiArg = {
    sourceId: number;
    body: string;
};
export type RoleListView = {
    id?: number;
    name?: string;
    userCount?: number;
    userTypeLevel?: number;
};
export type PagingResponseRoleListView = {
    results?: RoleListView[];
    totalCount?: number;
};
export type PermissionKey = {
    code?: string;
    id?: number;
};
export type RoleWithPermissions = {
    active?: boolean;
    addedPermissions?: number[];
    dashboard?: string;
    description?: string;
    editable?: boolean;
    homepage?: string;
    id?: number;
    name?: string;
    permissions?: PermissionKey[];
    removedPermissions?: number[];
    roleType?: string;
    userCount?: number;
    userTypeLevel?: number;
};
export type RepositoryNode = {
    label?: string;
    path?: string;
    subnodes?: RepositoryNode[];
    type?: string;
};
export type Permission = {
    active?: boolean;
    application?: string;
    code?: string;
    description?: string;
    id?: number;
    identityType?: string;
    name?: string;
    objectIdentity?: number;
    visible?: boolean;
};
export type Role = {
    dashboard?: string;
    description?: string;
    homepage?: string;
    id?: number;
    name?: string;
    permissions?: Permission[];
};
export type ApplicationModule = {
    name?: string;
    staticModule?: boolean;
};
export type PermissionsHierarchy = {
    hash?: string;
    modules?: ApplicationModule[];
};
export type PermissionPlaceholder = {
    code?: string;
    name?: string;
    permissionId?: number;
};
export type PermissionGroup = {
    groupName?: string;
    permissions?: PermissionPlaceholder[];
};
export type SubtablePlaceholder = {
    content?: RegisterModuleContent;
    fieldName?: string;
    registerId?: number;
    registerName?: string;
    registerType?: 'PRIMARY' | 'LIBRARY' | 'PRIVATE';
};
export type SectionPlaceholder = {
    id?: number;
    name?: string;
    permissions?: PermissionPlaceholder[];
    subtables?: SubtablePlaceholder[];
};
export type RegisterModuleContent = {
    permissionGroups?: PermissionGroup[];
    registerId?: number;
    sections?: SectionPlaceholder[];
    transitions?: PermissionPlaceholder[];
};
export type RegisterModule = {
    content?: RegisterModuleContent;
    name?: string;
    registerId?: number;
    registerType?: 'PRIMARY' | 'LIBRARY' | 'PRIVATE';
};
export type UserType = {
    description?: string;
    iconColor?: string;
    iconName?: string;
    id?: number;
    level?: number;
    licenses?: number;
    licensingUsed?: boolean;
    name?: string;
    permissions?: string[];
    userCount?: number;
};
export type RoleView = {
    active?: boolean;
    dashboard?: string;
    description?: string;
    editable?: boolean;
    homepage?: string;
    id?: number;
    name?: string;
    roleType?: string;
    userCount?: number;
    userTypeLevel?: number;
};
export type RtciGetTemplatesUsingGetApiResponse = /** status 200 successful operation */ RegisterTemplateRestListResponseRead;
export type RtciGetTemplatesUsingGetApiArg = {
    registerId?: number;
    page?: number;
    limit?: number;
};
export type RtciCreateTemplateUsingPostApiResponse = /** status 200 successful operation */ RegisterTemplateRestRead;
export type RtciCreateTemplateUsingPostApiArg = {
    /** The template to create. */
    registerTemplateRest: RegisterTemplateRest;
};
export type RtciGetTemplateByIdUsingGetApiResponse = /** status 200 successful operation */ RegisterTemplateRestRead;
export type RtciGetTemplateByIdUsingGetApiArg = {
    /** The unique ID of the template. */
    templateId: number;
};
export type RtciUpdateTemplateUsingPutApiResponse = /** status 200 successful operation */ RegisterTemplateRestRead;
export type RtciUpdateTemplateUsingPutApiArg = {
    /** The unique ID of the template. */
    templateId: number;
    /** The updated template data. */
    registerTemplateRest: RegisterTemplateRest;
};
export type RtciDeleteTemplateUsingDeleteApiResponse = unknown;
export type RtciDeleteTemplateUsingDeleteApiArg = {
    /** The unique ID of the template. */
    templateId: number;
};
export type RegisterTemplateRest = {
    /** Description of the template */
    description?: string;
    /** Unique identifier of the template */
    id?: number;
    /** JSON value containing template data */
    jsonValue?: string;
    /** Name of the template */
    name: string;
    /** ID of the register this template belongs to */
    registerId: number;
};
export type RegisterTemplateRestRead = {
    /** Creation date of the template */
    createDate?: string;
    /** ID of the user who created the template */
    createdBy?: number;
    /** Description of the template */
    description?: string;
    /** Unique identifier of the template */
    id?: number;
    /** JSON value containing template data */
    jsonValue?: string;
    /** ID of the user who last modified the template */
    lastModifiedBy?: number;
    /** Last modification date of the template */
    lastModifiedDate?: string;
    /** Name of the template */
    name: string;
    /** ID of the register this template belongs to */
    registerId: number;
};
export type RegisterTemplateRestListResponse = {
    /** Current page number (0-based) */
    page?: number;
    /** Number of records per page */
    pageSize?: number;
    /** List of templates for the current page */
    records?: RegisterTemplateRest[];
    /** Total number of templates matching the query */
    totalCount?: number;
    /** Total number of pages */
    totalPages?: number;
};
export type RegisterTemplateRestListResponseRead = {
    /** Current page number (0-based) */
    page?: number;
    /** Number of records per page */
    pageSize?: number;
    /** List of templates for the current page */
    records?: RegisterTemplateRestRead[];
    /** Total number of templates matching the query */
    totalCount?: number;
    /** Total number of pages */
    totalPages?: number;
};
export type PursiCreateUserUsingPostApiResponse = /** status 200 successful operation */ ProtechtUserRestInternal;
export type PursiCreateUserUsingPostApiArg = {
    /** <b>ProtechtUserRest</b> JSON data used to create the user. */
    protechtUserRestInternal: ProtechtUserRestInternal;
};
export type PursiGetCurrentUserInternalUsingGetApiResponse = /** status 200 The user data is returned in the response body. */ ProtechtUserRestInternal;
export type PursiGetCurrentUserInternalUsingGetApiArg = void;
export type PursiUpdateCurrentUserUsingPutApiResponse = /** status 200 The updated user data is returned in the response body. */ ProtechtUserRestInternal;
export type PursiUpdateCurrentUserUsingPutApiArg = {
    /** Use this parameter to update also Additional Business Units which are ignored by default. */
    includeBUs?: boolean;
    /** <b>ProtechtUserRest</b> JSON data used to update the user. */
    protechtUserRestInternal: ProtechtUserRestInternal;
};
export type PursiGetCurrentUserMobileQrCodeUsingGetApiResponse = unknown;
export type PursiGetCurrentUserMobileQrCodeUsingGetApiArg = {
    /** Size of the image. */
    size: number;
};
export type PursiGetUserUsingGetApiResponse = /** status 200 The user data is returned in the response body. */ ProtechtUserRestInternal;
export type PursiGetUserUsingGetApiArg = {
    /** The unique ID of the user. */
    userId: number;
};
export type PursiUpdateUserUsingPutApiResponse = /** status 200 The updated user data is returned in the response body. */ ProtechtUserRestInternal;
export type PursiUpdateUserUsingPutApiArg = {
    /** The unique ID of the user to be updated. */
    userId: number;
    /** Use this parameter to update also Additional Business Units which are ignored by default. */
    includeBUs?: boolean;
    /** <b>ProtechtUserRest</b> JSON data used to update the user. */
    protechtUserRestInternal: ProtechtUserRestInternal;
};
export type PursiDeleteUserUsingDeleteApiResponse = unknown;
export type PursiDeleteUserUsingDeleteApiArg = {
    /** The unique ID of the user to be deleted. */
    userId: number;
};
export type PursiGetUserDashboardsUsingGetApiResponse = /** status 200 The base64 image data are returned in response. */ DashboardsRest;
export type PursiGetUserDashboardsUsingGetApiArg = {
    /** The unique ID of the user. */
    userId: number;
};
export type PursiGetUserHistoryUsingPostApiResponse =
    /** status 200 The list of historical versions for the user record is returned. */ PaginRestResultProtechtUserRestInternal;
export type PursiGetUserHistoryUsingPostApiArg = {
    /** The unique ID of the user record for which historical versions will be returned. */
    userId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type PursiGetUserVersionUsingGetApiResponse = /** status 200 The specific historical version of the user record is returned. */ ProtechtUserRestInternal;
export type PursiGetUserVersionUsingGetApiArg = {
    /** The unique ID of the user record. */
    userId: number;
    /** The unique ID of the historical version. */
    versionId: number;
};
export type PursiLockUserUsingPutApiResponse = /** status 200 The matching user record was successfully locked. */ ProtechtUserRestInternal;
export type PursiLockUserUsingPutApiArg = {
    /** The unique ID of the user to be locked. */
    userId: number;
};
export type PursiPurgeUserUsingDeleteApiResponse = unknown;
export type PursiPurgeUserUsingDeleteApiArg = {
    /** The unique ID of the user to be permanently deleted. */
    userId: number;
};
export type PursiRestoreUserUsingPutApiResponse = /** status 200 The matching user record was successfully restored. */ ProtechtUserRestInternal;
export type PursiRestoreUserUsingPutApiArg = {
    /** The unique ID of the user to be restored. */
    userId: number;
};
export type PursiGetSecurityTokenForUserUsingGetApiResponse = unknown;
export type PursiGetSecurityTokenForUserUsingGetApiArg = {
    /** The unique ID of the user. */
    userId: number;
};
export type PursiUnlockUserUsingPutApiResponse = /** status 200 The matching user record was successfully unlocked. */ ProtechtUserRestInternal;
export type PursiUnlockUserUsingPutApiArg = {
    /** The unique ID of the user to be unlocked. */
    userId: number;
};
export type ProtechtUserRestInternal = {
    businessUnit?: IdWithNameRest;
    /** Users business units */
    businessUnits?: IdWithNameRest[];
    /** Category of the user for Vendor Risk Management module: 1 = INTERNAL, 2 = VENDOR */
    categoryVector?: number;
    /** Contact phone number */
    contactNumber?: string;
    createDate?: Timestamp;
    /** Name of the user who created this record */
    createdBy?: string;
    /** Date format preference */
    dateFormat?: string;
    /** Disable user access flag */
    disableAccess?: boolean;
    /** The user's email address */
    email?: string;
    /** Set Email Digest flag */
    emailDigest?: boolean;
    /** Employee ID */
    employeeId?: string;
    /** URL to home dashboard */
    homeDashboardUrl?: string;
    /** Unique entity ID */
    id?: number;
    /** External instant messaging contact details */
    instantMessenger?: string;
    /** Enable if the user is a Protecht internal user to access special configuration UIs */
    isInternal?: boolean;
    /** Enable if the user account is used for API access only */
    isService?: boolean;
    lastLoginDate?: Timestamp;
    /** Name of the user who last modified this record */
    lastModifiedBy?: string;
    lastModifiedDate?: Timestamp;
    lastPasswordChangeDate?: Timestamp;
    /** The username used for logging in to Protecht.ERM */
    loginId?: string;
    manager?: IdWithNameRest;
    /** Set vertical menu orientation */
    menuOrientation?: boolean;
    /** Mobile phone number */
    mobile?: string;
    /** Entity name */
    name?: string;
    /** Set navigation menu icons in horizontal menu */
    navigationMenuIcons?: boolean;
    /** Position title */
    position?: string;
    /** List of roles the user has */
    roles?: IdWithNameRest[];
    /** Status of the user's account: 0 = ACTIVE, 1 = DISABLED, 2 = DELETED, 3 = PASSWORD_EXPIRED, 4 = PASSWORD_INVALID_ATTEMPTS */
    status?: 0 | 1 | 2 | 3 | 4;
    /** Time format preference */
    timeFormatPattern?: string;
    /** Set 24 hours time format */
    use24HourTime?: boolean;
    /** Use Two-Factor Authentication flag */
    useTwoFactorAuthentication?: boolean;
    /** User type */
    userType?: string;
    /** Set Vendor Administrator flag */
    vendorAdministrator?: boolean;
};
export type DashboardsRest = {
    /** List of dashboards. */
    dashboards?: string[];
};
export type PaginRestResultProtechtUserRestInternal = {
    maxPage?: number;
    records?: ProtechtUserRestInternal[];
    response?: ProtechtUserRestInternal;
    totalCount?: number;
};
export type KriersGetAllKeyRiskIndicatorEntriesUsingGetApiResponse =
    /** status 200 Returns a list of KRI entries in the response body. */ PaginRestResultKeyRiskIndicatorEntryRest;
export type KriersGetAllKeyRiskIndicatorEntriesUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** A flag that, if true, will return completed KRI entries. If false, completed entries will be excluded from the results. */
    showCompletedEntries?: boolean;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the KRI entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth?: boolean;
    /** Filter the results by a provided keyword. */
    filter?: string;
};
export type KriersGetAllKeyRiskIndicatorEntriesUsingPostApiResponse =
    /** status 200 Returns a list of KRI entries in the response body. */ PaginRestResultKeyRiskIndicatorEntryRest;
export type KriersGetAllKeyRiskIndicatorEntriesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** A flag that, if true, will return completed KRI entries. If false, completed entries will be excluded from the results. */
    showCompletedEntries?: boolean;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the KRI entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth?: boolean;
    /** JSON data objects used to identify the business unit or filter the data. */
    complianceKriFilterWrapperRest: ComplianceKriFilterWrapperRest;
};
export type KriersGetMyKeyRiskIndicatorEntriesUsingGetApiResponse =
    /** status 200 Returns a list of KRI entries in the response body. */ PaginRestResultKeyRiskIndicatorEntryRest;
export type KriersGetMyKeyRiskIndicatorEntriesUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** A flag that, if true, will return completed KRI entries. If false, completed entries will be excluded from the results. */
    showCompletedEntries?: boolean;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the KRI entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth?: boolean;
    /** Filter the results by a provided keyword. */
    filter?: string;
};
export type KriersGetMyKeyRiskIndicatorEntriesUsingPostApiResponse =
    /** status 200 Returns a list of KRI entries in the response body. */ PaginRestResultKeyRiskIndicatorEntryRest;
export type KriersGetMyKeyRiskIndicatorEntriesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Start date of the attestation period. */
    startDate?: string;
    /** End date of the attestation period. */
    endDate?: string;
    /** A flag that, if true, will return only the KRI entries in current reporting period. If false, then all results (within the bounds of the offset and limit) are returned. */
    isReportMonth?: boolean;
    /** JSON data objects used to identify the business unit or filter the data. */
    complianceKriFilterWrapperRest: ComplianceKriFilterWrapperRest;
};
export type KriersGetEntryByIdentifierUsingGetApiResponse =
    /** status 200 The KRI entry matching the unique ID provided is returned. */ KeyRiskIndicatorEntryRest;
export type KriersGetEntryByIdentifierUsingGetApiArg = {
    /** The unique ID of the KRI entry. */
    entryId: number;
};
export type KriersUpdateEntryUsingPutApiResponse =
    /** status 200 The KRI entry was successfully updated and the updated version returned. */ KeyRiskIndicatorEntryRest;
export type KriersUpdateEntryUsingPutApiArg = {
    /** The unique ID of the KRI entry. */
    entryId: number;
    /** KeyRiskIndicatorEntryRest JSON data used to update the KRI entry. Non-mandatory fields that are left blank will be updated to an empty value in the record. */
    keyRiskIndicatorEntryRest: KeyRiskIndicatorEntryRest;
};
export type KriersDeleteEntryUsingDeleteApiResponse = unknown;
export type KriersDeleteEntryUsingDeleteApiArg = {
    /** The unique ID of the KRI entry. */
    entryId: number;
};
export type KrirsCreateRiskLibraryUsingGetApiResponse = /** status 200 The newly created KRI library is returned in the response body. */ KeyRiskIndicatorRest;
export type KrirsCreateRiskLibraryUsingGetApiArg = void;
export type KrirsDeleteRiskLibraryUsingDeleteApiResponse = unknown;
export type KrirsDeleteRiskLibraryUsingDeleteApiArg = {
    /** The unique IDs of the KRIs to be deleted from the KRI library. */
    kriLibraryIds: number[];
};
export type KrirsGetRiskLibraryUsingGetApiResponse = /** status 200 The KRI is returned in the response body. */ KeyRiskIndicatorRest;
export type KrirsGetRiskLibraryUsingGetApiArg = {
    /** The unique ID of the KRI in the KRI library. */
    kriLibraryId: number;
};
export type KrirsUpdateRiskLibraryUsingPutApiResponse = /** status 200 The updated KRI data is returned in the response body. */ KeyRiskIndicatorRest;
export type KrirsUpdateRiskLibraryUsingPutApiArg = {
    /** The unique ID of the KRI to be updated. */
    kriLibraryId: number;
    /** <b>KeyRiskIndicatorRest</b> JSON data used to update the KRI. */
    keyRiskIndicatorRest: KeyRiskIndicatorRest;
};
export type GetKeyRiskIndicatorHistoryApiResponse =
    /** status 200 The list of historical versions for the KRI is returned. */ PaginRestResultKeyRiskIndicatorRest;
export type GetKeyRiskIndicatorHistoryApiArg = {
    /** The unique ID of the KRI for which historical versions will be returned. */
    kriLibraryId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type KrirsGetKriResponsesUsingGetApiResponse = /** status 200 Returns a list of qualitative KRI responses. */ PaginRestResultQualitativeListRest;
export type KrirsGetKriResponsesUsingGetApiArg = void;
export type KrirsGetKrisUsingPostApiResponse = /** status 200 successful operation */ PaginRestResultKeyRiskIndicatorRest;
export type KrirsGetKrisUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** Tag type ID used for grouping */
    tagType?: number;
    /** Type of the tag operator */
    tagOperator?: 'AND' | 'OR';
    /** Tag IDs used for filtering */
    tagIds?: number[];
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type KeyRiskIndicatorEntryRest = {
    /** List of actions linked to the KRI */
    actions?: ActionLinkRest[];
    /** Background color of a KRI entry. This is set automatically according to the value of the entry against defined color scales. */
    bgColor?: string;
    businessUnit?: IdWithNameRest;
    /** Text color of the KRI entry. This is set automatically according to the value of the entry against defined color scales. */
    color?: string;
    /** User provided text comment on the KRI entry */
    comment?: string;
    /** Unique ID of the user who completed the entry */
    completedBy?: number;
    /** Date of completion for the entry */
    completedDate?: string;
    completionBusinessUnit?: IdWithNameRest;
    completionRole?: IdWithNameRest;
    /** Whether or not the KRI has been comfirmed by an eligible supervisor or manager. Can only be toggled by a user with the KRI.CONFIRM permission. */
    confirmed?: boolean;
    /** KRI description */
    description?: string;
    /** The last day that data is relevant to this KRI entry */
    endOfPeriod?: string;
    /** List of attached evidence */
    evidence?: SimpleAttachment[];
    /** How often the KRI is generated (uses frequency label, not letter code) */
    frequency?: string;
    /** KRI entry unique ID */
    id?: number;
    intelligentKri?: IdWithNameRest;
    keyRiskIndicator?: IdWithNameRest;
    kriCategory?: IdWithNameRest;
    /** Unique ID of the user who last modified the KRI entry */
    lastModifiedBy?: number;
    /** Date that this KRI entry was last modified */
    lastModifiedDate?: string;
    managedKri?: IdWithNameRest;
    /** KRI name */
    name?: string;
    /** Indicates whether the entry's value exceeds the upper or lower limit set by the scales:-1 = OVERFLOW_LOWER, 0 = OVERFLOW_NONE, 1 = OVERFLOW_UPPER */
    overFlow?: -1 | 0 | 1;
    /** Qualitative list label. A qualitative list is a set of pre-defined responses for non-numeric KRI values. */
    qualitativeLabel?: string;
    /** Qualitative list unique ID */
    qualitativeListId?: number;
    /** Name of the risk event that is linked to the KRI */
    riskName?: string;
    /** Lower limit (green) KRI scale value. This is the most desirable value that the KRI is expected to reach. */
    scale1?: number;
    /** Green/amber crossover KRI scale value. This is the last value before the KRI crosses out of the green (desirable) zone. */
    scale2?: number;
    /** Amber/red crossover KRI scale value. This is the last value before the KRI crosses into the red (undesirable) zone. */
    scale3?: number;
    /** Upper limit (red) KRI scale value. This is the least desirable value the KRI is expected to reach. */
    scale4?: number;
    /** The scaled score value for this KRI */
    scaled?: number;
    /** Status of the KRI entry: 0 = DUE, 1 = UNCONFIRMED, 2 = TOO_LATE, 3 = COMPLETED, 4 = ERROR */
    status?: 0 | 1 | 2 | 3 | 4;
    /** Intelligent - KRI values are derived from other KRIs. Managed - KRI values are manually entered.  */
    type?: string;
    /** Value entered by user in the response field. Is not present if KRI entry is not completed */
    value?: number;
    /** The importance of the KRI, represented on a simple numeric weighting range of 1-9 */
    weight?: number;
};
export type PaginRestResultKeyRiskIndicatorEntryRest = {
    maxPage?: number;
    records?: KeyRiskIndicatorEntryRest[];
    response?: KeyRiskIndicatorEntryRest;
    totalCount?: number;
};
export type KeyRiskIndicatorRest = {
    createDate?: string;
    createdBy?: IdWithNameRest;
    /** KRI description */
    description?: string;
    /** KRI unique ID */
    id?: number;
    kriCategory?: IdWithNameRest;
    lastModifiedBy?: IdWithNameRest;
    lastModifiedDate?: string;
    /** KRI name */
    name?: string;
    /** Names of the risk events linked to the KRI */
    riskEvents?: IdWithNameAndStatusRest[];
    /** Names of the tags linked to the KRI */
    tags?: IdWithNameRest[];
};
export type PaginRestResultKeyRiskIndicatorRest = {
    maxPage?: number;
    records?: KeyRiskIndicatorRest[];
    response?: KeyRiskIndicatorRest;
    totalCount?: number;
};
export type QualitativeListItemRest = {
    /** Background color */
    bgColour?: string;
    /** Text color */
    fgColour?: string;
    /** If true, the linked action must be completed before finalizing the entry. If false the linked action is optional. */
    forceAction?: boolean;
    /** If true, user comment is enforced. If false, the user can optionally provide a comment. */
    forceComment?: boolean;
    /** The unique ID for the icon displayed on the list item */
    iconId?: number;
    /** Qualitative list item unique ID */
    id?: number;
    /** Name of the list item */
    name?: string;
};
export type QualitativeListRest = {
    /** Qualitative list unique ID */
    id?: number;
    /** Qualitative list items */
    qualitativeListItems?: QualitativeListItemRest[];
};
export type PaginRestResultQualitativeListRest = {
    maxPage?: number;
    records?: QualitativeListRest[];
    response?: QualitativeListRest;
    totalCount?: number;
};
export type FlcGetFrameworkNodeLinksCountDetailUsingGetApiResponse = /** status 200 successful operation */ FrameworkNodeLinksCountDetailRead;
export type FlcGetFrameworkNodeLinksCountDetailUsingGetApiArg = {
    id: number;
    register?: number;
};
export type FlcGetLinkedEntityCountsUsingGetApiResponse = /** status 200 successful operation */ LinkedDetailsRead[];
export type FlcGetLinkedEntityCountsUsingGetApiArg = {
    id: number;
    register?: string;
    framework?: number;
};
export type FlcUpdateFrameworkExternalLinkingUsingPutApiResponse = unknown;
export type FlcUpdateFrameworkExternalLinkingUsingPutApiArg = {
    id: number;
    body: ExternalNodeLink[];
};
export type FlcGetFrameworkInternalLinkingUsingGetApiResponse = /** status 200 successful operation */ NodeLinkDetailRead[];
export type FlcGetFrameworkInternalLinkingUsingGetApiArg = {
    id: number;
};
export type FlcCreateFrameworkInternalLinkingUsingPostApiResponse = unknown;
export type FlcCreateFrameworkInternalLinkingUsingPostApiArg = {
    id: number;
    body: NodeLinkTarget[];
};
export type FlcDeleteFrameworkInternalLinkingUsingDeleteApiResponse = unknown;
export type FlcDeleteFrameworkInternalLinkingUsingDeleteApiArg = {
    id: number;
};
export type FlcGetLinkedNodesUsingGetApiResponse = /** status 200 successful operation */ string[];
export type FlcGetLinkedNodesUsingGetApiArg = {
    id: number;
    register?: string;
    framework?: number;
};
export type FlcGetLinkedEntityListUsingGetApiResponse = /** status 200 successful operation */ LinkedEntityRead[];
export type FlcGetLinkedEntityListUsingGetApiArg = {
    id: number;
};
export type FlcGetFrameworkNodesLinksCountsUsingGetApiResponse = /** status 200 successful operation */ FrameworkNodesLinksCountsRead;
export type FlcGetFrameworkNodesLinksCountsUsingGetApiArg = {
    id: number;
    register?: number;
};
export type FrameworkNodeLinksCountDetail = {};
export type NodeLinkCountDetail = {};
export type NodeLinkCountDetailRead = {
    linkDisplayBreadcrumbs?: string[];
    linkDisplayName?: string;
    linkId?: number;
    parentDisplayName?: string;
    parentId?: number;
    parentName?: string;
};
export type FrameworkNodeLinksCountDetailRead = {
    nodeId?: number;
    nodeLinksDetail?: NodeLinkCountDetailRead[];
    registerId?: number;
};
export type LinkedDetails = {};
export type LinkedDetailsRead = {
    linkedEntityName?: string;
    linkedRecordCount?: number;
    linkedRecordIdentity?: string;
    nodeId?: number;
};
export type ExternalNodeLink = {};
export type ExternalNodeLinkRead = {
    nodeId?: number;
    recordId?: number;
    status?: 'NEW' | 'VIEW' | 'DELETE';
    tableName?: string;
};
export type LinkedEntity = {
    framework?: FrameworkStub;
    register?: FrameworkRegister;
};
export type LinkedEntityRead = {
    framework?: FrameworkStubRead;
    register?: FrameworkRegisterRead;
};
export type FrameworkNodesLinksCounts = {};
export type NodeLinksCount = object;
export type FrameworkNodesLinksCountsRead = {
    frameworkId?: number;
    nodeLinksCounts?: NodeLinksCount[];
    registerId?: number;
    uniqueLinksCount?: number;
};
export type RdlrsvLockRecordUsingPostApiResponse = /** status 200 Register entry was locked successfully. */ ObjectLockRest;
export type RdlrsvLockRecordUsingPostApiArg = {
    /** The unique ID of the register entry to be locked. */
    entryId: number;
    /** The table name that represents the register. For example: table_xxxxx */
    tableName: string;
    /** <b>ObjectLockRest</b> JSON data used to lock the register entry. The operation will fail if CORE section data (including last modified date) are not provided. */
    registerDataRest: RegisterDataRest;
};
export type RdlrsvDeleteLoggedUserLocksUsingDeleteApiResponse = unknown;
export type RdlrsvDeleteLoggedUserLocksUsingDeleteApiArg = void;
export type GetLockApiResponse = /** status 200 The register entry lock data is returned in the response body. */ ObjectLockRest;
export type GetLockApiArg = {
    /** The unique ID of the register entry lock */
    lockId: number;
    /** The unique ID of the register entry. */
    entryId: number;
    /** The table name that represents the register. For example: table_xxxxx */
    tableName: string;
};
export type RdlrsvRenewalRecordLockUsingPutApiResponse = /** status 200 Register entry lock was renewed successfully. */ ObjectLockRest;
export type RdlrsvRenewalRecordLockUsingPutApiArg = {
    /** The unique ID of the register entry lock */
    lockId: number;
    /** The unique ID of the register entry being locked. */
    entryId: number;
    /** The table name that represents the register. For example: table_xxxxx */
    tableName: string;
};
export type DeleteLockApiResponse = unknown;
export type DeleteLockApiArg = {
    /** The unique ID of the register entry lock */
    lockId: number;
    /** The unique ID of the register entry being unlocked. */
    entryId: number;
    /** The table name that represents the register. For example: table_xxxxx */
    tableName: string;
};
export type ObjectLockRest = {
    /** The creation date of the register entry lock */
    createDate?: string;
    creator?: IdWithNameRest;
    /** Register entry lock unique ID */
    id?: number;
    /** The last renewed date of the register entry lock */
    lastRenewalDate?: string;
    /** The unique ID of the register entry that has been put into a locked state */
    linkedId?: number;
    /** Table name that represents the register that contains the locked entry. For example: table_xxxxx. */
    linkedTable?: string;
    /** The duration of the register entry lock in minutes */
    lockRenewalInterval?: number;
    stateless?: boolean;
};
export type MrsGetUsedCategoriesUsingGetApiResponse = /** status 200 successful operation */ string[];
export type MrsGetUsedCategoriesUsingGetApiArg = void;
export type MrsGetPackageConflictsUsingGetApiResponse = /** status 200 successful operation */ StructureRegisterDetailDto[];
export type MrsGetPackageConflictsUsingGetApiArg = {
    version: string;
    uuid: string;
};
export type MrsGetFrameworkLinkPackageConflictsUsingGetApiResponse = /** status 200 successful operation */ FrameworkLinkDetailDto[];
export type MrsGetFrameworkLinkPackageConflictsUsingGetApiArg = {
    version: string;
    uuid: string;
};
export type MrsGetActionRegistersUsingGetApiResponse = /** status 200 successful operation */ SimpleRegisterDto[];
export type MrsGetActionRegistersUsingGetApiArg = void;
export type MrsGetInstallProgressUsingGetApiResponse = /** status 200 successful operation */ MultiImportStatusDto;
export type MrsGetInstallProgressUsingGetApiArg = {
    processId: string;
};
export type MrsInstallPackageUsingPutApiResponse = unknown;
export type MrsInstallPackageUsingPutApiArg = {
    version: string;
    uuid: string;
    registerMappingData: RegisterMappingData;
};
export type MrsGetAllInstalledPackagesUsingGetApiResponse = /** status 200 successful operation */ PackageDetailsDto[];
export type MrsGetAllInstalledPackagesUsingGetApiArg = {
    category?: string;
    'sort-by'?: 'NAME' | 'RELEASE';
    'sort-dir'?: 'ASC' | 'DESC';
};
export type MrsGetAllInstalledPackagesUsingGet1ApiResponse = /** status 200 successful operation */ number;
export type MrsGetAllInstalledPackagesUsingGet1ApiArg = void;
export type MrsGetInstalledContentUsingGetApiResponse = /** status 200 successful operation */ PackageDetailsDto;
export type MrsGetInstalledContentUsingGetApiArg = {
    version: string;
    uuid: string;
};
export type MrsGetExistingInstallItemUsingGetApiResponse = /** status 200 successful operation */ IdWithNameRest;
export type MrsGetExistingInstallItemUsingGetApiArg = {
    label: string;
    installItemType: 'Application' | 'Register' | 'Section';
};
export type MrsGetUninstallProgressUsingGetApiResponse = /** status 200 successful operation */ MultiImportStatusDto;
export type MrsGetUninstallProgressUsingGetApiArg = {
    processId: string;
};
export type MrsUninstallPackageUsingPutApiResponse = unknown;
export type MrsUninstallPackageUsingPutApiArg = {
    uuid: string;
};
export type MrsGetSubscriptionPlanUsingGetApiResponse = /** status 200 successful operation */ {
    [key: string]: string;
};
export type MrsGetSubscriptionPlanUsingGetApiArg = void;
export type CentralLibraryFieldDetailDto = {
    contractType?:
        | 'UNSPECIFIED'
        | 'RESILIENCE_CRITICAL_SERVICE'
        | 'RESILIENCE_PROCESS'
        | 'RESILIENCE_RESOURCE'
        | 'RESILIENCE_PLAUSIBLE_SCEN'
        | 'VRM_QUESTIONNAIRE_METADATA'
        | 'ACTIONS'
        | 'VRM_VENDOR_ACTIONS'
        | 'VRM_VENDOR_REGISTER'
        | 'VRM_FINDINGS_AND_ISSUES'
        | 'VRM_MONITORING_REVIEWS'
        | 'VRM_RISK_ASSESSMENTS'
        | 'VRM_CONTRACTS'
        | 'VRM_DOCUMENTS'
        | 'VRM_CYBER_SECURITY_SCORE'
        | 'FRAMEWORK_BASIC'
        | 'CENTRAL_LIBRARIES'
        | 'BOWTIE_RISK_EVENT'
        | 'BOWTIE_RISK_CAUSE'
        | 'BOWTIE_RISK_IMPACT'
        | 'BOWTIE_RISK_CONTROL'
        | 'COPILOT_RISK_CONTROL'
        | 'VRM_PRODUCT'
        | 'CYBER_ACTIONS'
        | 'CYBER_CONTROLS'
        | 'CYBER_FRAMEWORK_ASSESSMENT'
        | 'CYBER_RISKS'
        | 'CYBER_THREATS'
        | 'CYBER_VULNERABILITIES'
        | 'CYBER_ASSETS'
        | 'CYBER_ASSET_RISK'
        | 'CYBER_INCIDENTS'
        | 'CYBER_POLICIES'
        | 'CYBER_ASSURANCE'
        | 'CYBER_CALENDAR';
    fieldLabel?: string;
    fieldName?: string;
    targetRegisterUUID?: string;
};
export type ScaleDetailDto = {
    inConflict?: boolean;
    scaleName?: string;
};
export type SectionDetailDto = {
    inConflict?: boolean;
    sectionName?: string;
};
export type StructureRegisterDetailDto = {
    actionRegister?: boolean;
    appId?: number;
    applicationName?: string;
    centralLibraryFieldDetails?: CentralLibraryFieldDetailDto[];
    existingApplication?: boolean;
    fieldsCount?: number;
    forcedRegister?: boolean;
    inConflict?: boolean;
    permissionsCount?: number;
    recordCount?: number;
    registerLabel?: string;
    replacedLabel?: string;
    scaleDetails?: ScaleDetailDto[];
    sectionDetails?: SectionDetailDto[];
    stateTransitionsCount?: number;
    subtableDetails?: StructureRegisterDetailDto[];
    subtableType?: string;
    tableName?: string;
    uuid?: string;
    workflowRulesCount?: number;
};
export type DataProcessingMessage = {
    errorDetails?: string;
    errorMessage?: string;
    messageType?: 'ERROR' | 'WARN' | 'INFO';
    row?: number;
};
export type FrameworkLinkDetailDto = {
    frameworkSourceName?: string;
    linkRecordTargetName?: string;
    linkTargetName?: string;
    nodeSourceName?: string;
    processingMessages?: DataProcessingMessage[];
    registerLink?: boolean;
};
export type SimpleRegisterDto = {
    /** Register unique ID */
    id?: number;
    /** Display name of a register */
    label?: string;
    /** There are 3 possible types of registers. Primary: the most commonly used and the default type. Private Table: a sub-table whose entries relate to the primary record (one to many relationship) and is not shared between registers. Library/Shared: a sub-table whose entries can be used in multiple registers (many to many relationship). */
    registerType?: 'Primary' | 'Private' | 'Shared';
    /** The table name that represents the register */
    tableName?: string;
};
export type ProcessingMessage = {
    messageType?: 'ERROR' | 'WARN' | 'INFO';
};
export type ImportedItemInfoDto = {
    dataImport?: boolean;
    detailedProgresSupported?: boolean;
    processingMessages?: ProcessingMessage[];
    progressStatus?:
        | 'NOT_STARTED'
        | 'STARTED'
        | 'VALIDATION_FINISHED'
        | 'IMPORT_VALIDATION_FAILED'
        | 'IMPORT_IN_PROGRESS'
        | 'IMPORT_FINISHED'
        | 'SYNC_IN_PROGRESS'
        | 'ROLLBACK_IN_PROGRESS'
        | 'FAILED';
    subTotalItemCount?: number;
    tableName?: string;
    totalItemCount?: number;
};
export type ImportedModuleInfoDto = {
    items?: number;
    label?: string;
    module?: string;
};
export type MultiImportStatusDto = {
    currentItem?: ImportedItemInfoDto;
    finishedItems?: ImportedItemInfoDto[];
    importInfos?: ImportedModuleInfoDto[];
    name?: string;
    progressStatus?:
        | 'NOT_STARTED'
        | 'STARTED'
        | 'VALIDATION_FINISHED'
        | 'IMPORT_VALIDATION_FAILED'
        | 'IMPORT_IN_PROGRESS'
        | 'IMPORT_FINISHED'
        | 'SYNC_IN_PROGRESS'
        | 'ROLLBACK_IN_PROGRESS'
        | 'FAILED';
    subTotalItemCount?: number;
    totalItemCount?: number;
};
export type RegisterMappingData = {
    actionsRegisterForceImport?: string[];
    centralFieldsRegistersToReplace?: {
        [key: string]: string;
    };
    customActionsRegister?: string;
    handleDuplicityWithExistingStructure?: boolean;
    registerApplications?: {
        [key: string]: string;
    };
    registerApplicationsToReplace?: {
        [key: string]: string;
    };
    registers?: {
        [key: string]: string;
    };
    registersToReplace?: {
        [key: string]: string;
    };
    scaleSets?: {
        [key: string]: string;
    };
    scaleSetsToReplace?: {
        [key: string]: string;
    };
    sections?: {
        [key: string]: string;
    };
    sectionsToReplace?: {
        [key: string]: string;
    };
};
export type AnalyticsAssetDto = {
    path?: string;
    type?: string;
};
export type RepositoryDto = {
    name?: string;
    nicePath?: string;
    path?: string;
    type?: string;
};
export type FrameworkDetailDto = {
    frameworkName?: string;
};
export type RegisterDetailDto = {
    appId?: number;
    recordCount?: number;
    registerLabel?: string;
    replacedLabel?: string;
    tableName?: string;
    uuid?: string;
};
export type PackageContentDto = {
    analyticsDetails?: RepositoryDto[];
    frameworksDetails?: FrameworkDetailDto[];
    frameworksLinksDetails?: FrameworkLinkDetailDto[];
    itemCount?: number;
    module?:
        | 'REGISTER_DATA'
        | 'REGISTER'
        | 'RISK_CAUSE'
        | 'RISK_EVENT'
        | 'CONTROL'
        | 'QUESTION'
        | 'KRI'
        | 'TAG'
        | 'ANALYTICS'
        | 'FRAMEWORK'
        | 'FRAMEWORK_LINKS'
        | 'UNDEFINED';
    moduleIcon?: string;
    registerDetails?: RegisterDetailDto[];
    registerStructureDetails?: StructureRegisterDetailDto[];
};
export type ScreenshotDto = {
    description?: string;
    main?: boolean;
    name?: string;
    pid?: string;
    previewUrl?: string;
    screenshotUrl?: string;
    thumbnailUrl?: string;
    type?: string;
    uuid?: string;
};
export type SubscriptionDto = {
    label?: string;
    level?: number;
};
export type PackageDetailsDto = {
    analyticsAssetList?: AnalyticsAssetDto[];
    categories?: string[];
    contentList?: PackageContentDto[];
    description?: string;
    ermVersion?: string;
    icon?: string;
    imageID?: string;
    imageUrl?: string;
    installable?: boolean;
    installation?: string;
    isUninstallable?: boolean;
    latestVersion?: string;
    maturity?: number;
    module?: string;
    name?: string;
    overview?: string;
    packageType?: 'STANDARD' | 'FRAMEWORK' | 'FRAMEWORK_LINKS';
    released?: string;
    screenshotList?: ScreenshotDto[];
    subscription?: SubscriptionDto;
    testPackage?: boolean;
    usedModules?: (
        | 'REGISTER_DATA'
        | 'REGISTER'
        | 'RISK_CAUSE'
        | 'RISK_EVENT'
        | 'CONTROL'
        | 'QUESTION'
        | 'KRI'
        | 'TAG'
        | 'ANALYTICS'
        | 'FRAMEWORK'
        | 'FRAMEWORK_LINKS'
        | 'UNDEFINED'
    )[];
    uuid?: string;
    version?: string;
};
export type UmrsGetMenuUsingGetApiResponse = /** status 200 Returns the menu configuration in the response body. */ Menu;
export type UmrsGetMenuUsingGetApiArg = {
    /** Flag for retrieving menu configuration with default menu icons from new theme */
    fillDefaultIcons?: boolean;
};
export type UmrsSetMenuUsingPutApiResponse = /** status 200 Menu Configuration was saved successfully. */ Menu;
export type UmrsSetMenuUsingPutApiArg = {
    menu: Menu;
};
export type UmrsGetAvailableActionsUsingGetApiResponse = /** status 200 The list of menu item with actions is returned in the response body */ Item[];
export type UmrsGetAvailableActionsUsingGetApiArg = void;
export type UmrsActivateTemplateUsingPutApiResponse = /** status 200 Current Menu Configuration was successfully removed. */ Menu;
export type UmrsActivateTemplateUsingPutApiArg = {
    body: string;
};
export type UmrsSaveDefaultDashboardUsingPutApiResponse = unknown;
export type UmrsSaveDefaultDashboardUsingPutApiArg = {
    body: string;
};
export type UmrsDeleteTemplateUsingDeleteApiResponse = unknown;
export type UmrsDeleteTemplateUsingDeleteApiArg = {
    body: string;
};
export type UmrsDeleteMenuItemUsingDeleteApiResponse = /** status 200 Menu Item was deleted successfully. */ RestMessage;
export type UmrsDeleteMenuItemUsingDeleteApiArg = {
    itemId: string;
};
export type UmrsGetMenuForManagerUsingGetApiResponse = /** status 200 Returns the menu configuration in the response body. */ Menu;
export type UmrsGetMenuForManagerUsingGetApiArg = void;
export type UmrsGetMenuTemplatesUsingGetApiResponse = /** status 200 Returns menu configuration templates. */ {
    [key: string]: object;
}[];
export type UmrsGetMenuTemplatesUsingGetApiArg = void;
export type UmrsSaveNewTemplateUsingPostApiResponse = unknown;
export type UmrsSaveNewTemplateUsingPostApiArg = {
    body: string;
};
export type UmrsGetRegistersForApplicationUsingGetApiResponse =
    /** status 200 The list of register actions for the application is returned in the response body */ RegisterAction[];
export type UmrsGetRegistersForApplicationUsingGetApiArg = {
    appId: number;
};
export type UmrsResetMenuUsingPutApiResponse = /** status 200 Returns current menu configuration in the response body. */ Menu;
export type UmrsResetMenuUsingPutApiArg = void;
export type Configuration = {
    dashboards?: string[];
    defaultDashboard?: string;
    location: 'TOP' | 'LEFT';
    style: 'HORIZONTAL';
    theme: 'BLUE' | 'GRAY' | 'NEPTUNE';
};
export type LocalizedString = {
    lang?: string;
    value?: string;
};
export type ParamsType = {
    key?: string;
    value?: string;
};
export type Item = {
    action?: string;
    delete?: boolean;
    edit?: boolean;
    hyperlink?: string;
    icon?: string;
    icon40?: string;
    icon80?: string;
    iconSVG?: string;
    id?: string;
    items?: Item[];
    labels: LocalizedString[];
    params?: ParamsType[];
    titles: LocalizedString[];
    tooltips?: LocalizedString[];
};
export type Menu = {
    configuration: Configuration;
    items: Item[];
};
export type RestMessage = {
    message?: string;
    requestId?: string;
};
export type RegisterAction = {
    applicationId?: number;
    label?: string;
    tableName?: string;
};
export type MrsFindByIdsUsingGetApiResponse = /** status 200 successful operation */ MetricRestResponse[];
export type MrsFindByIdsUsingGetApiArg = {
    /** Metric ids or names. */
    idsOrNames: string[];
    eval?: boolean;
    computeStyle?: boolean;
};
export type MrsCreateUsingPostApiResponse = /** status 200 successful operation */ MetricRestResponse;
export type MrsCreateUsingPostApiArg = {
    metricRest: MetricRest;
};
export type MrsDeleteMetricUsingDeleteApiResponse = unknown;
export type MrsDeleteMetricUsingDeleteApiArg = {
    /** The unique id of the metric to be deleted. */
    metricId: number;
};
export type MrsEvalUsingGetApiResponse = /** status 200 successful operation */ MetricRestResponse[];
export type MrsEvalUsingGetApiArg = {
    /** Metric ids or names. */
    idsOrNames: string[];
    /** Core BU id to narrow appropriate results. */
    buId?: number;
    computeStyle?: boolean;
};
export type MrsSearchUsingPostApiResponse = /** status 200 successful operation */ PaginRestResultMetricRest;
export type MrsSearchUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Name of the module used to get data relevant to the module. */
    moduleName?: string;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type MrsGetDataSourcesUsingPostApiResponse = /** status 200 successful operation */ PaginRestResultTableMetadataRest;
export type MrsGetDataSourcesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Module name. */
    module: string;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type MrsGetDataSourcesGetUsingGetApiResponse = /** status 200 successful operation */ PaginRestResultTableMetadataRest;
export type MrsGetDataSourcesGetUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Module name. */
    module: string;
    /** Payload */
    payload?: string;
};
export type MdrsGetDisplayConfigurationUsingGetApiResponse = /** status 200 successful operation */ MetricDisplayConfig;
export type MdrsGetDisplayConfigurationUsingGetApiArg = {
    /** Display configuration module. */
    module: 'vrm' | 'resilience' | 'controls';
};
export type MdrsSetDisplayConfigurationUsingPostApiResponse = unknown;
export type MdrsSetDisplayConfigurationUsingPostApiArg = {
    module: 'vrm' | 'resilience' | 'controls';
    metricDisplayConfig: MetricDisplayConfig;
};
export type MrsFindByIdUsingGetApiResponse = /** status 200 successful operation */ MetricRestResponse;
export type MrsFindByIdUsingGetApiArg = {
    /** Metric id or name. */
    idOrName: string;
    eval?: boolean;
    computeStyle?: boolean;
};
export type MrsUpdateUsingPutApiResponse = /** status 200 successful operation */ MetricRestResponse;
export type MrsUpdateUsingPutApiArg = {
    metricId: number;
    metricRest: MetricRest;
};
export type MComputedStyle = {
    color?: string;
    error?: boolean;
    present?: string;
    size?: string;
};
export type MEvalResult = {
    explain?: string[];
    formattedEval?: string;
    multiResult?: {
        [key: string]: string;
    };
    rawVal?: string;
    simpleEval?: string;
    simpleEvalWithSymbol?: string;
    symbol?: string;
    symbolBefore?: boolean;
};
export type Condition = {
    groupPrevious?: boolean;
    lpar?: boolean;
    operator?: string;
    or?: boolean;
    property?: string;
    rpar?: boolean;
    type?: string;
    value?: string;
};
export type ConditionConfig = {
    conditions?: Condition[];
};
export type ViewCondition = {
    conditionConfig?: ConditionConfig;
};
export type ViewConditionConfig = {
    internal?: ViewCondition[];
    metricExpression?: string;
};
export type MScaleBool = {
    no?: string;
    unset?: string;
    yes?: string;
};
export type MScaleTxtBracket = {
    color?: string;
    others?: boolean;
    txt?: string;
};
export type MScaleNumBracket = {
    color?: string;
    greaterThan?: number;
    high?: number;
    lessThan?: number;
    low?: number;
};
export type MScaleOverdue = {
    due?: string;
    open?: string;
    overdue?: string;
};
export type MStyleConfig = {
    color?: string;
    greyIfEmpty?: boolean;
    params?: {
        [key: string]: string;
    };
    scalesBool?: MScaleBool;
    scalesList?: MScaleTxtBracket[];
    scalesNum?: MScaleNumBracket[];
    scalesOverdue?: MScaleOverdue;
    scalesTxt?: MScaleTxtBracket[];
    size?: number;
};
export type MetricRest = {
    configModel?: ViewConditionConfig;
    context?: string;
    feature?: number;
    func?: number;
    id?: number;
    name?: string;
    prefix?: string;
    present?: number;
    registerName?: string;
    scope?: number;
    source?: string;
    sourceField?: string;
    styleModel?: MStyleConfig;
    suffix?: string;
    type?: number;
    uuid?: string;
};
export type MetricRestResponse = {
    applyStyle?: MComputedStyle;
    eval?: MEvalResult;
    evaluationError?: boolean;
    idOrName?: string;
    metric?: MetricRest;
};
export type PaginRestResultMetricRest = {
    maxPage?: number;
    records?: MetricRest[];
    response?: MetricRest;
    totalCount?: number;
};
export type ContractMappingRestField = {
    column?: string;
    dataType?: number;
    name?: string;
    type?: string;
};
export type ContractMappingRest = {
    mappings?: ContractMappingRestField[];
    stateful?: boolean;
    type?: string;
};
export type GlobalListRest = {
    definition?: string;
    id?: number;
    name?: string;
    uuid?: string;
};
export type FormulaComponentRest = {
    /** A flag marking given formula component as a core column reference. */
    coreColumn?: boolean;
    /** Formula component unique ID */
    id?: number;
    /** The label (name) of the field - this value should match with the field name in the register section. */
    label?: string;
    /** A name of physical database column representing given CORE section field (optional, required only in case of core column reference). */
    name?: string;
};
export type FieldRest = {
    /** A flag to indicate whether the field is archived. An archived field is hidden on the register form. */
    archived?: boolean;
    /** The column name that represents the field within the register */
    columnName?: string;
    /** The data type of the field */
    columnType?:
        | 'ACTIONS'
        | 'WORKLOG'
        | 'STATETRANSITION'
        | 'PRIORITY'
        | 'MULTILINE_TEXT'
        | 'SINGLELINE_TEXT'
        | 'STATIC_TEXT'
        | 'PASSWORD'
        | 'BLOB'
        | 'INTEGER'
        | 'REAL'
        | 'NUMERIC'
        | 'DOUBLE'
        | 'TIMESTAMP'
        | 'DATE'
        | 'NULL'
        | 'BUSINESS_UNIT'
        | 'EMAIL'
        | 'QUESTION'
        | 'CONTROL'
        | 'RISK_EVENT'
        | 'RISK_CAUSE'
        | 'GROUP'
        | 'USER'
        | 'STATE'
        | 'COUNTRY'
        | 'ATTACHMENT'
        | 'LINK'
        | 'LIST'
        | 'MULTISELECT_LIST'
        | 'CURRENCY'
        | 'OBJREF'
        | 'BOOLEAN'
        | 'SIMPLE_FORMULA'
        | 'COMPLEX_FORMULA'
        | 'MULTISELECT_LIBRARY'
        | 'TABLE'
        | 'SCALE_LIKELIHOOD'
        | 'SCALE_CONSEQUENCE'
        | 'TAGS'
        | 'SLIDER'
        | 'DUE_DATE'
        | 'REGISTER_STATE'
        | 'LINKED_TO'
        | 'IMAGES'
        | 'REPORT_ATTACHMENT'
        | 'RICH_TEXT'
        | 'DATETIME_FORMULA'
        | 'CONTROL_ATTESTATION'
        | 'STRING_FORMULA'
        | 'WEB_SERVICE_TABLE'
        | 'SIGN_OFF'
        | 'USER_INFO'
        | 'REGISTER_ENTRY_LINK'
        | 'USER_AGGREGATION'
        | 'SCHEDULE'
        | 'REGISTER_DESIGNER'
        | 'RISK_MATRIX'
        | 'RISK_RATING'
        | 'REGISTER_LAYOUT'
        | 'SPACER'
        | 'ROLE'
        | 'HYPERLINK'
        | 'HINT'
        | 'GPS_POSITION'
        | 'TIMESTAMP_WITH_TIMEZONE'
        | 'CUSTOM_ID'
        | 'CENTRAL_LIBRARY'
        | 'BIDIRECTIONAL_MULTISELECT'
        | 'FRAMEWORK_LINKS'
        | 'V_AUDIT_QUESTION'
        | 'V_BOWTIE'
        | 'V_KRI';
    /** The list of properties defined by the system for the field - only available for certain field types. */
    constraintProperties?: {
        [key: string]: {
            [key: string]: string;
        };
    };
    /** The default value of the field */
    defaultValue?: string;
    /** The description of the field */
    description?: string;
    /** Optional alternative to field name. When provided, the field label is displayed on the register form instead of the field name */
    fieldLabel?: string;
    /** The filter type applied to certain dependent actions */
    filterType?: string;
    /** The list of variables required for date or simple formula */
    formulaVariables?: {
        [key: string]: FormulaComponentRest;
    };
    /** A flag to indicate whether the label of the field is hidden or not */
    hideLabel?: boolean;
    /** Field unique ID */
    id?: number;
    /** Field name (a mandatory field attribute) */
    label: string;
    /** The position of a field to be displayed in a row */
    layoutColumn?: 'left' | 'right' | 'both';
    /** The row number where the field will be displayed within a section */
    layoutRow?: number;
    /** The possible layout strategy for a position on a row */
    layoutStrategy?: 'Insert' | 'Default';
    /** A flag to indicate whether the field is a required field in a register */
    required?: boolean;
    /** Field unique ID - this ID is randomly generated and unique for every field */
    uuid?: string;
};
export type FieldActionRest = {
    /** The field or section in which the action will be performed once the condition is met. For a field, the format is "sectionid:col_xxxxx". For a section, the format is "Section:xxxxx". */
    field?: string;
    /** The available values that can be selected once the condition is met */
    fieldActionValues?: string[];
    /** The action to be performed when condition(s) is/are met */
    fieldRuleAction?: 'SHOW' | 'HIDE' | 'ENABLE_FLAG' | 'DISABLE_FLAG' | 'REQUIRED' | 'NOT_REQUIRED' | 'FILTER' | 'FILTER_EXACT' | 'STYLE' | 'CLEAR';
    /** If this action is configured for a library field such as "Controls" or "Risk Event" this value indicates which property is used for filtering. For example: field "name" in "Controls". */
    filteredField?: string;
    /** The unique field name of the field that is being used for filtering. For example: "col_xxxxxx". */
    sourceFilteredByField?: string;
};
export type FieldConditionRest = {
    /** A flag to indicate whether the condition needs closing parenthesis */
    closeBracket?: boolean;
    /** The list of field types that the conditional field rule supports */
    dataType?:
        | 'ATTACH'
        | 'GPS'
        | 'BOOLEAN'
        | 'BUSINESS_UNIT'
        | 'CLEARABLE'
        | 'CONTROL'
        | 'CENTRAL_LIBRARY_SINGLE'
        | 'COUNTRY'
        | 'DATE'
        | 'DECIMAL'
        | 'EMAIL'
        | 'HINT'
        | 'HYPERLINK'
        | 'IMAGE'
        | 'LINKED_TO'
        | 'LIST'
        | 'QUESTIONLIST'
        | 'MULTI'
        | 'LOOKUP'
        | 'MS_LIST'
        | 'MULTISELECT_LIBRARY'
        | 'NUMBER'
        | 'NUMBER_WITH_ANYTHING'
        | 'NUMBER_FOR_DATE_SHIFT'
        | 'OBJECT'
        | 'QUESTION'
        | 'REGISTER_STATE'
        | 'REGISTER_STATE_TRANSITION'
        | 'RISK_CAUSE'
        | 'RISK_EVENT'
        | 'RISK_MATRIX'
        | 'RISK_RATING'
        | 'ROLE'
        | 'SCALE_ITEM'
        | 'SLIDER'
        | 'STATE'
        | 'STATIC_TEXT'
        | 'STRING'
        | 'SUBTABLE'
        | 'TABLE'
        | 'TAGS'
        | 'TAGS_CHILD'
        | 'USER'
        | 'BUSINESS_UNIT_MANAGER'
        | 'VOID'
        | 'SPACER'
        | 'DATE_MACRO'
        | 'CUSTOM_ID'
        | 'FRAMEWORK_LINKS';
    /** The evaluator of the condition */
    evaluator?:
        | 'equals'
        | 'not equals'
        | 'less than'
        | 'greater than'
        | 'before'
        | 'after'
        | 'on'
        | 'is null'
        | 'is not null'
        | 'before now'
        | 'after now'
        | 'contains'
        | 'not contains'
        | 'in'
        | 'not in'
        | 'starts with'
        | 'not starts with'
        | 'ends with'
        | 'not ends with';
    /** The ID of the field or section that the conditional rule applies to. For a field, the format is "sectionid:col_xxxxx". For a section, the format is "Section:xxxxx". */
    field?: string;
    /** The logical operator of the condition */
    logicOperator?: string;
    /** A flag to indicate whether the condition needs opening parenthesis */
    openBracket?: boolean;
    /** The list of text values for the operation to compare against */
    value?: string;
};
export type FieldRuleStatusRest = {
    /** Information about any errors related to the field rule */
    errorMessage?: string;
    /** A flag to indicate whether one or more properties of the field rule are unresolved */
    unresolvedProperties?: boolean;
};
export type FieldRuleRest = {
    /** The list of actions to be performed when conditions are met */
    actions?: FieldActionRest[];
    /** A flag to indicate whether the conditional rule contains any broken conditions - this value is determined by the system. */
    broken?: boolean;
    /** The list of conditions that define the rule */
    conditions?: FieldConditionRest[];
    /** Conditional rule creation date */
    createdDate?: string;
    /** Conditional rule description */
    description?: string;
    /** A flag to indicate whether the conditional rule is in effect or not */
    enabled?: boolean;
    /** Conditional rule unique ID */
    id?: number;
    /** Conditional rule last modified date */
    lastModifiedDate?: string;
    /** Conditional rule name */
    name?: string;
    /** The order of the conditional rule compared to other conditional rules */
    order?: number;
    /** The table name that represents the register where the conditional rule is defined. For example: "table_xxxxxx". */
    registerTable?: string;
    /** The list of postfix expressions of the conditional rule - these values are generated by the system. */
    rulePostfixExpression?: string[];
    status?: FieldRuleStatusRest;
    /** A map of unresolved properties. The key indicates whether the value is in the 1 = Condition, 2 = Action part of the conditional rule definition. The value is a list of field names that are unresolved. */
    unresolvedProperties?: {
        [key: string]: string[];
    };
    /** Conditional rule unique ID - this ID is randomly generated and unique for every conditional rule */
    uuid?: string;
};
export type FieldRuleSetRest = {
    broken?: boolean;
    description?: string;
    enabled?: boolean;
    id?: number;
    name?: string;
    order?: number;
    registerTable?: string;
    rules?: FieldRuleRest[];
    uuid?: string;
};
export type RiskMatrixRatingRest = {
    /** The background color applied to this risk scale appetite rating */
    color?: string;
    /** The unique ID of the risk scale appetite rating */
    id?: number;
    /** The label of the risk scale appetite rating */
    label?: string;
    /** The display order of the risk scale appetite rating in the Ratings table */
    order?: number;
};
export type RiskMatrixCellRest = {
    /** The value of the risk matrix cell */
    cellValue?: number;
    /** The background color applied to the risk matrix cell */
    color?: string;
    /** The consequence value of the risk matrix cell */
    consequenceValue?: number;
    /** The unique ID of the risk matrix cell */
    id?: number;
    /** The likelihood value of the risk matrix cell */
    likelihoodValue?: number;
    ratingId?: RiskMatrixRatingRest;
};
export type ScaleItemRest = {
    /** The order of this scale item in the risk scale */
    baseScale?: number;
    /** The unique ID of the scale item */
    id?: number;
    /** The label of the scale item */
    label?: string;
    /** The value of the scale item */
    value?: number;
};
export type ScaleRest = {
    /** The risk scale type */
    context?: 'LIKELIHOOD' | 'CONSEQUENCE';
    /** The unique ID of the risk scale */
    id?: number;
    /** The label of the risk scale */
    name?: string;
    /** The list of scale items */
    scaleItems?: ScaleItemRest[];
    /** Scale unique ID - this ID is randomly generated and unique for every scale */
    uuid?: string;
};
export type ScaleSetRest = {
    /** A list of risk matrix cells that belong to the scale set */
    cells?: RiskMatrixCellRest[];
    /** A list of consequence scales for the scale set */
    consequences?: ScaleRest[];
    /** The horizontal axis of the risk matrix. For example: Likelihood or Consequence */
    getxAxis?: string;
    /** The unique ID of the scale set */
    id?: number;
    likelihood?: ScaleRest;
    /** The display name of the scale set */
    name?: string;
    /** The list of ratings that can be applied to risk matrix cells */
    ratings?: RiskMatrixRatingRest[];
    /** The formula to calculate total score if the score type is "FORMULA" */
    scoreFormula?: string;
    /** Method to calculate the total score */
    scoreType?: 'MULTIPLICATION' | 'ADDITION' | 'MANUAL' | 'FORMULA';
    /** Scale set unique ID - this ID is randomly generated and unique for every scale set */
    uuid?: string;
};
export type StyleRest = {
    /** The color of the field labels */
    fieldLabelColor?: string;
    /** The size of the field labels */
    fieldLabelSize?: 'TINY' | 'SMALL' | 'MEDIUM' | 'LARGE' | 'HUGE';
    /** Styles applied to the field labels */
    fieldLabelStyles?: ('REGULAR' | 'BOLD' | 'ITALIC' | 'UNDERLINE')[];
    /** Field layout configuration in a JSON format */
    fieldLayout?: {
        [key: string]: object;
    };
    /** The amount of spacing provided around fields */
    fieldSpacing?: 'USE_SYSTEM_SETTINGS' | 'MINIMAL' | 'SMALL' | 'MEDIUM' | 'LARGE' | 'GLOBAL';
    /** If true, will hide the section borders. Otherwise the section borders will be visible */
    hideSectionBorders?: boolean;
    /** If true, will hide the section label. Otherwise the section label will be visible */
    hideSectionLabel?: boolean;
    /** Style unique ID */
    id?: number;
    /** The unique register ID that the style is applied to. This property is only present when the linkTable value is tablemetadata or sectionmetadata */
    linkId?: number;
    /** Indicates whether the style is applied to an individual register (tablemetadata), all registers (globaldesign) or section(sectionmetadata). */
    linkTable?: 'tablemetadata' | 'globaldesign' | 'sectionmetadata';
    /** The background color of the sections */
    sectionBackgroundColor?: string;
    /** The color of the section labels */
    sectionLabelColor?: string;
    /** The size of the section labels */
    sectionLabelSize?: 'TINY' | 'SMALL' | 'MEDIUM' | 'LARGE' | 'HUGE';
    /** Text styles applied to section labels */
    sectionLabelStyles?: ('REGULAR' | 'BOLD' | 'ITALIC' | 'UNDERLINE')[];
    /** If true, the field width will become dynamic and will only be large enough to fit the data it holds. Otherwise the field width will be static and fill the provided space. */
    widthToFitData?: boolean;
};
export type SectionRest = {
    /** The unique ID of the application in Protecht.ERM where the section is located. An application is a logical grouping of registers. */
    applicationId?: number;
    /** The unique system generated ID of the application in Protecht.ERM where the section is located */
    applicationUuid?: string;
    /** The flag to indicate whether to collapse section by default. This flag can not be changed. */
    collapseByDefault?: boolean;
    /** The description of the section */
    description?: string;
    /** The flag to indicate whether a section is editable or not. This flag can not be changed. */
    editable?: boolean;
    /** A list of fields defined in the section */
    fields?: FieldRest[];
    /** Section unique ID */
    id?: number;
    /** Section name */
    label?: string;
    /** The order of the section in a register */
    order?: number;
    /** The Scope of the section */
    scope?: 'Application' | 'Global';
    style?: StyleRest;
    /** The name of the tab that contains the section */
    tabName?: string;
    /** The section type. */
    type?: number;
    /** Section unique ID - this ID is randomly generated and unique for every section */
    uuid?: string;
};
export type PermissionRest = {
    /** The name of the module that the permission is relevant for */
    applicationName?: string;
    /** Permission code */
    code?: string;
    /** Permission description */
    description?: string;
    /** Permission unique ID */
    id?: number;
    /** Permission name */
    name?: string;
    /** The identifier of the object that the permission is relevant for */
    relatedObjectId?: number;
};
export type RoleRest = {
    /** Set true if role is administrative */
    administrative?: boolean;
    /** The home dashboard for the role */
    dashboard?: string;
    /** Role description */
    description?: string;
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
    /** List of permissions assigned to this role */
    permissions?: PermissionRest[];
    /** List of users assigned by this role */
    users?: IdWithNameAndStatusRest[];
};
export type RegisterStateTransitionRest = {
    /** Close entry, can be null, default value is false. */
    closeEntry?: boolean;
    /** Starting state */
    currentState?: string;
    /** State transition unique ID */
    id?: number;
    /** State transition name */
    name?: string;
    /** Destination state  */
    newState?: string;
    /** State transition prompt */
    prompt?: string;
    /** The roles that can make this state transition */
    roles?: RoleRest[];
    /** The order in which the state transitions are displayed */
    transitionOrder?: number;
    /** Transition type, can be null */
    type?: number;
    /** State transition unique ID - this ID is randomly generated and unique for every state transition in the system */
    uuid?: string;
};
export type RegisterStateDefinitionRest = {
    /** State definition unique ID */
    id?: number;
    /** A flag indicating whether this register's state definition has been customized. The default state transitions are: Draft -> Open, Open -> Closed, Closed -> Open. Once any changes are made to the register's state definition, this flag is set to false. */
    isDefault?: boolean;
    /** State definition name */
    name?: string;
    /** A list of state transitions that are defined in this register state definition. A transition is the movement between states (such as close, submit to manager, or re-open). */
    stateTransitions?: RegisterStateTransitionRest[];
    /** A list of states that are defined in this register state definition. A state is the stage or status of the entry (for example Open, Closed, or Under Review). */
    states?: RegisterStateRest[];
};
export type TaskColumnRest = {
    /** The column name that represents a My Tasks date field */
    dateColumn?: string;
    /** The unique My Tasks column ID */
    id?: number;
    /** The column name that represents a My Tasks user field */
    userColumn?: string;
};
export type RestParamRest = {
    /** The unique identifier for the parameter */
    id?: number;
    idParam?: boolean;
    /** A readable display label which can help describe the parameter */
    label?: string;
    /** A flag that determines if the field is a secret property and its value requires encryption and masking. */
    masked?: boolean;
    /** The module to which the parameter belongs */
    module?: string;
    /** The name/key of the parameter */
    name?: string;
    /** The order in which the parameter is to be used */
    paramOrder?: number;
    /** The API REST action that this parameter model belongs to. This will only be populated when used in custom register actions. */
    restActionId?: number;
    /** The type of the parameter */
    type?: 'REQUEST' | 'RESPONSE' | 'SELECTOR' | 'REQUEST_ACTION' | 'HEADER';
    /** The value of the parameter */
    value?: string;
    /** The workflow rule that this parameter model belongs to. This will only be populated when used in a workflow rule as a request header. */
    workflowRuleId?: number;
};
export type RestResponseActionConditionRest = {
    closeParens?: boolean;
    /** The JSON path expression used to search a JSON structure */
    jsonPath?: string;
    /** The value which is used to compare to the value returned by the JSON path expression */
    jsonValue?: string;
    /** The logical operator that can be used to combine other expressions before or after this expression. */
    logicalOperator?: 'AND' | 'OR';
    openParens?: boolean;
    /** The value comparison operator */
    operator?:
        | 'CONTAINS'
        | 'CONTAINS_IGNORE_CASE'
        | 'NOT_CONTAINS'
        | 'NOT_CONTAINS_IGNORE_CASE'
        | 'EXISTS'
        | 'EQUALS'
        | 'EQUALS_IGNORE_CASE'
        | 'NOT_EQUALS'
        | 'NOT_EQUALS_IGNORE_CASE'
        | 'STARTS_WITH'
        | 'STARTS_WITH_IGNORE_CASE'
        | 'ENDS_WITH'
        | 'ENDS_WITH_IGNORE_CASE';
    /** The evaluation order of the expression. */
    order?: number;
};
export type RestResponseTranslationMapRest = {
    /** The response action field map's unique ID */
    id?: number;
    /** A flag that controls whether the translation action's placeholder will match with case sensitivity. */
    matchCase?: boolean;
    /** The placeholder in the response message to search for possible replacement. */
    placeholder?: string;
    /** The order of which translations are applied. */
    processOrder?: number;
    /** A flag to determine whether only the first occurrence of the placeholder is replaced. */
    replaceOnlyFirstMatch?: boolean;
    /** The textual value to replace the "placeholder" with. */
    replacement?: string;
    /** The response action field map that this translation configuration is used in. */
    responseActionFieldMapId?: number;
};
export type RestResponseActionFieldMapRest = {
    /** The register field ID to which the response action's payload is reflected */
    columnName?: string;
    /** An optional format specifier that is applied to the object path */
    format?: string;
    /** The response action field map's unique ID */
    id?: number;
    /** A message that in the elected register field in the scenario where no object path is specified or the object path is not found in the response action's payload */
    message?: string;
    /** The JSON path to the node in response action's payload to reflect into a register field */
    objectPath?: string;
    /** A list of translation configurations which can re-write response message terminology with more desirable wording for end users. */
    translationMaps?: RestResponseTranslationMapRest[];
};
export type RestResponseActionRest = {
    /** The type of action to be performed */
    actionType?: string;
    /** The response action's unique ID */
    id?: number;
    /** The list of conditions which can determine whether a response action is executed */
    restResponseActionConditions?: RestResponseActionConditionRest[];
    /** The possible field mapping actions that can be performed by this action */
    restResponseActionFieldMap?: RestResponseActionFieldMapRest[];
};
export type RestResponseHandlerRest = {
    /** The response handler's unique ID */
    id?: number;
    /** The response handler's name */
    name?: string;
    /** The HTTP status codes which the handler acts upon */
    responseCodes?: number[];
    /** The underlying action to be performed by the handler */
    restResponseAction?: RestResponseActionRest[];
    /** An optional sample payload to be used for testing the handler's action */
    samplePayload?: string;
    /** The workflow rule ID to which the handler is associated */
    workflowRuleId?: number;
};
export type WorkflowTemplateRest = {
    /** The unique identifier for the workflow template. */
    id?: number;
    /** The field in a primary register that facilitates the linkage to another table. This sub-table becomes the source of this template's macro resolution. */
    linkColumn?: string;
    /** The name of the workflow template. Templates have unique names per workflow rule. */
    name?: string;
    /** The contents of the workflow template. */
    value?: string;
    /** The workflow rule that this workflow template belongs to. */
    workflowId?: number;
};
export type WorkFlowApiAction = {
    actionType?: 'EMAIL' | 'CONDITIONAL_EMAIL' | 'STATE_TRANSITION' | 'SMS' | 'CALENDAR_EVENT' | 'API' | 'APIRESPONSE';
    /** The Workflow API configuration in use for this workflow rule. This can be configured in System Configuration. */
    apiConfigurationId?: number;
    /** The endpoint specification with some optional macros to be resolved during runtime. */
    endpoint?: string;
    /** A flag to exclude the fields which have empty value. */
    excludeEmptyFields?: boolean;
    /** A flag to exclude the fields marked as hidden. */
    excludeHiddenFields?: boolean;
    /** A collection of formatting rules  to apply to listed field types when invoking given API endpoint. */
    formats?: {
        [key: string]: string;
    };
    /** The actual API request payload to use when invoking given API endpoint. */
    payload?: string;
    /** A collection of parameters to be used when invoking given API endpoint. */
    restParams?: RestParamRest[];
    /** A collection of response handlers to be used when invoking given API endpoint. */
    restResponseHandlers?: RestResponseHandlerRest[];
    /** A collection of workflow templates to be used when invoking given API endpoint. Workflow templates are used to repeat a block of macro-able text per sub-table entry. */
    workflowTemplates?: WorkflowTemplateRest[];
};
export type WorkflowCalendarEventAction = {
    actionType?: 'EMAIL' | 'CONDITIONAL_EMAIL' | 'STATE_TRANSITION' | 'SMS' | 'CALENDAR_EVENT' | 'API' | 'APIRESPONSE';
    /** The date formatter. */
    dateFormat?: string;
    /** The beginning and ending date for this event. */
    eventDate?: string;
    /** The number of days to send out invite before the actual event. */
    eventDaysOffset?: number;
    /** The description about this calendar event. */
    eventDescription?: string;
    /** The end time of the calendar event. */
    eventEndTime?: string;
    /** The start time of the calendar event. */
    eventStartTime?: string;
    /** The display title of this calendar event. */
    eventTitle?: string;
    /** The organizer email address of this calendar event. */
    organizerEmailAddress?: string;
    /** The organizer name of this calendar event. */
    organizerName?: string;
    /** The list of recipients who will receive this calendar event invite. We recommend populating all recipient fields with macros to select from system lists. An example of a valid user macro is "${1:createdby::Details:Email}". This will resolve to the user who created the register entry. */
    recipients?: string;
};
export type WorkFlowConditionField = {
    /** The data type of the field that is evaluated */
    dataType?:
        | 'DATE'
        | 'NUMBER'
        | 'DECIMAL'
        | 'STRING'
        | 'EMAIL'
        | 'BOOLEAN'
        | 'STATE'
        | 'OBJECT'
        | 'VOID'
        | 'TAG'
        | 'TABLE'
        | 'MULTI'
        | 'ATTACH'
        | 'SUBTABLE'
        | 'LOOKUP'
        | 'NUMBER_WITH_ANYTHING';
    /** If the workflow rule operates on a register, this is an identifier that combines the register section name with the field data type. Otherwise, this is the column name that represents the field being evaluated */
    label?: string;
    /** If the workflow rule operates on a register, this is an identifier that combines the section ID and the column name that represents the field. Otherwise, this is the column name that represents the field being evaluated */
    name?: string;
    /** A list of possible values that could be provided as user input. This list could contain possible states, risk ratings, consequences, or other multi-select list options. */
    possibleStringValues?: string[];
    /** A flag that indicates whether the "name" and "label" properties are valid within the system */
    resolved?: boolean;
};
export type WorkFlowCondition = {
    /** A flag that indicates whether the condition is followed by a closed bracket */
    closeBracket?: boolean;
    /** The operation used to evaluate the workflow rule. This will vary depending on the field's data type. */
    evaluator?: string;
    field?: WorkFlowConditionField;
    /** The starting value of the register field - this is only required when the "evaluator" property is set to "changes" */
    fromValue?: string;
    /** The logical operator that precedes this workflow condition. */
    logicOperator?: 'or' | 'and';
    /** The module or table name (for registers) where the workflow rule is triggered */
    module?: string;
    /** A flag that indicates whether the condition is preceded by an open bracket. By default, the system processes conditions one by one, from top to bottom. A parentheses convention can be used to process the condition in an order that meets your requirements, for example to treat multiple conditions as one unit. */
    openBracket?: boolean;
    /** The value that the field must be changed to in order to meet the condition - this is only required when the "evaluator" property is set to "changes" */
    toValueModel?: string;
};
export type WorkFlowEmailAction = {
    actionType?: 'EMAIL' | 'CONDITIONAL_EMAIL' | 'STATE_TRANSITION' | 'SMS' | 'CALENDAR_EVENT' | 'API' | 'APIRESPONSE';
    allowEmptyRecipients?: boolean;
    /** The column name of an attachment field (where the workflow rule operates on a register that has an attachment field). Attachments from the field are included as part of the email notification. */
    attachmentColumns?: string;
    /** BCC field for an email notification */
    bccs?: string;
    /** CC field for an email notification */
    ccs?: string;
    /** By default, notifications are sent to only the first matching recipient ("first-matching"). To send to all matching recipients, set this property to "all-matching". */
    conditionMatchType?: 'first-matching' | 'all-matching';
    /** The frequency with which the workflow email notifications are to be sent out (when the conditions are met). This setting ensures that only a single workflow event will be triggered within the period, even if the conditions are met multiple times. This field is only available for workflow rules that operate from date conditions, live compliance entry, and live KRI entry workflow rules. The repeat frequency is symbolised by single letter codes (M,D,Y etc.). */
    emailRepetition?:
        | 'On Demand(O)'
        | 'Daily (D)'
        | 'Weekly (W)'
        | 'Fortnightly (F)'
        | 'Monthly (M)'
        | 'Bimonthly (C)'
        | 'Quarterly(Q)'
        | 'Semi-Annually (S)'
        | 'Annually (A)'
        | 'Biennially (B)'
        | 'Triennially (3 Years) (T)'
        | 'Quinquennially (5 Years) (U)'
        | 'Never (N)'
        | 'Not Set (-)';
    /** The message in the body of the email. This can contain a combination of macros and text and can include hyperlinks. Macros allow you to include information in the email that uses current or conditional data from specific fields, rather than hard-coded text that doesn't change. */
    message?: string;
    /** The mailing list of recipients who will receive this email. We recommend populating all recipient fields with macros to select from system lists. An example of a valid user macro is "${1:createdby::Details:Email}". This will resolve to the user who created the register entry. */
    recipients?: string;
    /** An email address to which replies will be directed. If left blank, replies will <NAME_EMAIL> */
    replytos?: string;
    /** The email subject */
    subject?: string;
    /** The table name that represents a register where the workflow rule is triggered */
    tableName?: string;
};
export type WorkFlowSmsAction = {
    actionType?: 'EMAIL' | 'CONDITIONAL_EMAIL' | 'STATE_TRANSITION' | 'SMS' | 'CALENDAR_EVENT' | 'API' | 'APIRESPONSE';
    /** The message in the body of the SMS message. This can contain a combination of macros and text and can include hyperlinks. Macros allow you to include information in the SMS that uses current or conditional data from specific fields, rather than hard-coded text that doesn't change. */
    message?: string;
    /** The list of recipients who will receive this SMS. We recommend populating all recipient fields with macros to select from system lists. An example of a valid user macro is "${1:createdby::Details:Email}". This will resolve to the user who created the register entry. */
    recipients?: string;
    /** The SMS configuration in use for this workflow rule. This can be configured in System Configuration. */
    smsConfigurationId?: number;
    /** The frequency with which the workflow SMS notifications are to be sent out (when the conditions are met). This setting ensures that only a single workflow event will be triggered within the period, even if the conditions are met multiple times. This field is only available for workflow rules that operate from date conditions, live compliance entry, and live KRI entry workflow rules. The repeat frequency is symbolised by single letter codes (M,D,Y etc.). */
    smsRepetition?:
        | 'On Demand(O)'
        | 'Daily (D)'
        | 'Weekly (W)'
        | 'Fortnightly (F)'
        | 'Monthly (M)'
        | 'Bimonthly (C)'
        | 'Quarterly(Q)'
        | 'Semi-Annually (S)'
        | 'Annually (A)'
        | 'Biennially (B)'
        | 'Triennially (3 Years) (T)'
        | 'Quinquennially (5 Years) (U)'
        | 'Never (N)'
        | 'Not Set (-)';
};
export type WorkFlowStatus = {
    /** A flag that indicates whether a workflow rule is enabled. If this value is false, workflow notifications won't be sent. By default, this is set to "true". */
    enabled?: boolean;
    /** The error message that is passed to the client when a workflow rule is broken */
    errorMessage?: string;
    /** A flag indicating that a system error has occurred due to a broken workflow rule. When this flag is true, the workflow rule will not be performed and will be flagged as disabled. */
    systemError?: boolean;
    /** A key-value mapping of a unique property ID (key) to an unresolved property value (value). The list of property IDs are as follows: <ul><li>1 - Condition</li><li>2 - Recipient</li> <li>3 - CC</li><li>4 - BCC</li><li>5 - Subject</li><li>6 - Message</li><li>7 - ReplyTo</li><li>8 - Attachment</li><li>9 - Condition Field</li><li>10 - Condition Recipient</li><li>11 - Repeat Period</li></ul> */
    unresolvedProperties?: {
        [key: string]: string[];
    };
};
export type WorkFlowRuleRest = {
    /** The type of workflow action that will be triggered. <li>EMAIL: A notification is sent to the list of recipients.</li><li>CONDITIONAL_EMAIL: A notification is sent to recipients based on defined conditions</li><li>CALENDAR_EVENT: A calendar invite is sent to recipients based on defined conditions.</li><li>STATE_TRANSITION: The state of a register entry is changed based on the defined conditions. This is only available for registers that have states enabled - we recommend only using this option with rules that are based around dates to avoid creating circular references.<ul></li></ul> */
    actionType?: 'EMAIL' | 'CONDITIONAL_EMAIL' | 'CALENDAR_EVENT' | 'STATE_TRANSITION';
    apiAction?: WorkFlowApiAction;
    /** A flag that indicates whether the workflow rule is resolvable. A workflow rule may be broken due to an unresolvable recipient or invalid condition syntax. To view a full list of possible causes for broken workflow rules, see the description for the "WorkFlowRuleRest.status.unresolvedProperties". */
    broken?: boolean;
    calendarEventAction?: WorkflowCalendarEventAction;
    /** A list of conditions that trigger the workflow rule */
    conditions?: WorkFlowCondition[];
    /** A summary of the rule. This could include what the rule does, who is notified, the time constraints that apply, and explanations of any macros used. */
    description?: string;
    emailAction?: WorkFlowEmailAction;
    /** A flag that indicates whether a workflow rule is enabled. If this value is false, workflow notifications won't be sent. By default, this is set to "true". */
    enable?: boolean;
    /** Workflow unique ID */
    id?: number;
    /** Workflow name */
    name?: string;
    smsAction?: WorkFlowSmsAction;
    /** The table name that represents a register where the workflow rule is triggered */
    sourceTable?: string;
    status?: WorkFlowStatus;
    /** The unique state ID to which a workflow rule will transition an entry when triggered. This is only relevant when "actionType" is "STATE_TRANSITION". */
    targetStateId?: string;
    /** The type of rule engine that a workflow rule will be evaluated against. The rule engine is responsible for loading the procedures that will add, remove, and update a workflow. The two types of rule engines are: <ul> <li>BATCH - Batch rules are date-driven and run according to their defined frequency. The system evaluates batch rules once per day, but they can also be triggered manually </li> <li> LIVE - Live rules are event-driven and run whenever their conditions are met. </li></ul>   */
    type?: 'LIVE' | 'BATCH';
    /** The workflow rule unique ID - this ID is system generated and assigned to workflow rule when it is created in Protecht.ERM. */
    uuid?: string;
};
export type TableMetadataRest = {
    /** If true, register entries are displayed in the Registers Review screen with a preview pane. */
    allowPreviewPanel?: boolean;
    /** If true, new entries can be created in the register without logging in to Protecht ERM. */
    anonymousAccess?: boolean;
    /** The unique ID of the application where this register is located */
    applicationId?: number;
    /** The name of the application where this register is located */
    applicationName?: string;
    /** Application configuration unique ID - this ID is system generated and assigned to an application when it is created. */
    applicationUuid?: string;
    /** A key-value mapping between a columnID (key) and the column name (value), for example: [ { 1 : "col_xxxxx" } ] */
    columns?: {
        [key: string]: string;
    };
    /** If true, commenting is allowed for this register. */
    comments?: boolean;
    contractMappings?: ContractMappingRest[];
    /** If tabLayout is true, the tab name provided in defaultTabName will be active when a user creates a new entry or opens an existing entry. */
    defaultTabName?: string;
    /** Register description */
    description?: string;
    /** If true, turns on pessimistic locking, which locks the register while a user is editing a record. */
    editLock?: boolean;
    /** Number of entries in this register */
    entriesCount?: number;
    favourite?: boolean;
    /** Global Lists used in the register */
    globalLists?: GlobalListRest[];
    /** If true, reporting is available for previous versions of entries in the register. */
    historicalReports?: boolean;
    /** Unique ID for the icon that appears on the register tile in the user interface */
    iconId?: string;
    /** Register unique ID */
    id?: number;
    identityColumn?: FieldRest;
    /** When <b>schedulerState</b> is also set to true, "Auto Create" column is displayed in the register. */
    isAutoCreateVisible?: boolean;
    /** Display name of a register */
    label?: string;
    /** If true, prevents the CORE Business Unit field automatically populating with the current user's Primary Business Unit. */
    leaveCoreBUBlank?: boolean;
    /** Visibility setting relevant only to library registers. Options are Default (visible only with other library registers), Always (visible to all other registers), or Hidden (hidden from other registers). */
    libVisibility?: 'Default' | 'Always' | 'Hidden';
    /** If true, all fields within a register will be locked. Users are then unable to edit or add entries to the register. */
    lockRecords?: boolean;
    /** If true, users can create register entries in the Protecht.ERM mobile app while offline. */
    offline?: boolean;
    /** When <b>offline</b> is also set to true, allows mobile app users to download register entries, edit them while offline, and upload changes to the server when back online. Once downloaded, each original entry on the server is locked to prevent changes until the user who downloaded it has uploaded their changes or released the entry. */
    offlineLock?: boolean;
    /** List of parent registers where this register is a sub-table */
    parentTableNames?: string[];
    properties?: Properties;
    /** There are 3 possible types of registers. Primary: the most commonly used and the default type. Private Table: a sub-table whose entries relate to the primary record (one to many relationship) and is not shared between registers. Library/Shared: a sub-table whose entries can be used in multiple registers (many to many relationship). */
    registerType?: 'Primary' | 'Private' | 'Shared' | 'Questionnaire' | 'CentralLibrary' | 'Framework';
    /** A list of conditional rule sets defined in the register. A conditional rule is a combination of an expression and an action that can be used to control visibility, edit access, validation, or filter selection in fields or sections of registers. */
    ruleSets?: FieldRuleSetRest[];
    /** A list of all risk or self assessment scale sets used in the register. Scales are used for measuring and setting risk appetite and may be used in a register as a list or a visual risk matrix field. */
    scaleSets?: ScaleSetRest[];
    /** If true, users can set an entry to automatically duplicate and create a new entry based on defined parameters. */
    schedulerState?: boolean;
    /** Application scope setting relevant only to library registers. Options are Application (only available to be used within that specific application) or Global (can be shared across other applications). */
    scope?: 'Application' | 'Global';
    /** Sections contained within the register, in which fields are laid out for data entry and display. */
    sections?: SectionRest[];
    /** Applicable only for global registers. An ID of application a global register belonged to before it was made global. Use null value or application ID in case of application scoped registers. */
    sourceApplicationId?: number;
    stateDefinition?: RegisterStateDefinitionRest;
    /** If true, users will have the ability to move register entries through a series of defined states (for example Open, Under Review and Closed) and trigger specific workflows and reporting responses that are conditional to the current state. */
    stateful?: boolean;
    statusColumn?: FieldRest;
    style?: StyleRest;
    /** If true, sections may be displayed on tabs in the register. The tabName property in the SectionRest object determines which tab a section appears on. */
    tabLayout?: boolean;
    /** The table name that represents the register */
    tableName?: string;
    /** A list of fields that can be linked to My Tasks. For a register to be available in My Tasks, it must use states and have at least one user field. */
    taskColumns?: TaskColumnRest[];
    /** Number of templates used in the register. */
    templateCount?: number;
    uniqueColumn?: FieldRest;
    userPermissionCodesForRegister?: string[];
    /** Register configuration unique ID - this ID is randomly generated and unique for every user-created register in the system */
    uuid?: string;
    /** A set of conditions based on the register that, when met, trigger an automated workflow action. */
    workFlowRules?: WorkFlowRuleRest[];
};
export type PaginRestResultTableMetadataRest = {
    maxPage?: number;
    records?: TableMetadataRest[];
    response?: TableMetadataRest;
    totalCount?: number;
};
export type BaseMetricDisplay = {
    metricId?: number;
    metricName?: string;
    metricOrder?: number;
};
export type MetricContext = {
    context?: string;
    metricDisplays?: BaseMetricDisplay[];
};
export type MetricModule = {
    metricContexts?: MetricContext[];
    module?: string;
};
export type MetricDisplayConfig = {
    metricModule?: MetricModule;
};
export type OrsGetEntriesForOfflineUseUsingGetApiResponse =
    /** status 200 The entries are returned as a list of RegisterDataRest objects in the response body. */ RegisterDataRest[];
export type OrsGetEntriesForOfflineUseUsingGetApiArg = {
    /** The unique ID of the device where the entries have been saved for offline use. */
    deviceId: string;
};
export type OrsLockEntryForOfflineUseUsingPutApiResponse = /** status 200 successful operation */ OfflineLockRest;
export type OrsLockEntryForOfflineUseUsingPutApiArg = {
    /** The unique ID of the register entry being locked. */
    entryId: number;
    /** The table name that represents the register. For example: table_xxxxx */
    tableName: string;
    /** The unique device ID bound to the register entry lock. */
    deviceId: string;
};
export type DeleteOfflineLockApiResponse = unknown;
export type DeleteOfflineLockApiArg = {
    /** The unique ID of the register entry to be unlocked. */
    entryId: number;
    /** The table name that represents the register. For example: table_xxxxx */
    tableName: string;
    /** The unique ID of the register entry lock */
    lockId: number;
};
export type OrsGetBulkEntitiesUsingGetApiResponse = /** status 200 The data is returned as a BulkEntitiesRest object in the response body. */ BulkEntitiesRest;
export type OrsGetBulkEntitiesUsingGetApiArg = {
    /** A list of system libraries from which data will be retrieved. The allowable values of system libraries are listed below. <ul><li>BU, COUNTRY, STATE, ROLE, USER, TAG, QUESTION, RISK_CAUSE, RISK_EVENT, AUDIT_QUESTION, CONTROL, CONSEQUENCE, LIKELIHOOD, KRI, BOW_TIE.</li></ul> */
    context?: string[];
};
export type OrsGetRegistersUsingGetApiResponse =
    /** status 200 The data is returned as a list of TableMetadataRest objects in the response body. */ UserTableMetadataRest[];
export type OrsGetRegistersUsingGetApiArg = void;
export type BusinessUnitCore = {
    children?: BusinessUnitCore[];
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
    parent?: string;
};
export type RoleBaseRest = {
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
    permissions?: IdWithNameRest[];
};
export type ProtechtUserRest = {
    businessUnit?: IdWithNameRest;
    /** Users business units */
    businessUnits?: IdWithNameRest[];
    /** Category of the user for Vendor Risk Management module: 1 = INTERNAL, 2 = VENDOR */
    categoryVector?: number;
    /** Contact phone number */
    contactNumber?: string;
    /** Date format preference */
    dateFormatPattern?: string;
    /** Disable user access flag */
    disableAccess?: boolean;
    /** The user's email address */
    email?: string;
    /** Employee ID */
    employeeId?: string;
    /** URL to home dashboard */
    homeDashboardUrl?: string;
    /** Unique entity ID */
    id?: number;
    /** External instant messaging contact details */
    instantMessenger?: string;
    lastLoginDate?: Timestamp;
    /** The username used for logging in to Protecht.ERM */
    loginId?: string;
    manager?: IdWithNameRest;
    /** Mobile phone number */
    mobile?: string;
    /** Entity name */
    name?: string;
    /** Position title */
    position?: string;
    /** List of roles the user has */
    roles?: IdWithNameRest[];
    /** Status of the user's account: 0 = ACTIVE, 1 = DISABLED, 2 = DELETED, 3 = PASSWORD_EXPIRED, 4 = PASSWORD_INVALID_ATTEMPTS */
    status?: 0 | 1 | 2 | 3 | 4;
    /** Time format preference */
    timeFormatPattern?: string;
    /** User type */
    userType?: string;
    /** Set Vendor Administrator flag */
    vendorAdministrator?: boolean;
};
export type BulkEntitiesRest = {
    /** The list of available audit questions in the system */
    auditQuestions?: IdWithNameAndStatusRest[];
    /** The list of available bow ties in the system */
    bowTies?: IdWithNameRest[];
    /** The list of available business units in the system */
    businessUnits?: BusinessUnitCore[];
    /** The list of available consequences in the system */
    consequences?: {
        [key: string]: {
            [key: string]: IdWithNameRest[];
        };
    };
    /** The list of available controls in the system */
    controls?: IdWithNameAndStatusRest[];
    /** The list of available countries in the system */
    countries?: IdWithNameRest[];
    /** The list of available key risk indicators in the system */
    keyRiskIndicators?: IdWithNameAndStatusRest[];
    /** The list of available likelihoods in the system */
    likelihoods?: {
        [key: string]: IdWithNameRest[];
    };
    /** The list of available questions in the system */
    questions?: IdWithNameAndStatusRest[];
    /** The list of available risk causes in the system */
    riskCauses?: IdWithNameAndStatusRest[];
    /** The list of available risk events in the system */
    riskEvents?: IdWithNameAndStatusRest[];
    /** The list of available roles in the system */
    roles?: RoleBaseRest[];
    /** The list of available states in the system */
    states?: IdWithNameRest[];
    /** The list of available tags in the system */
    tags?: IdWithNameRest[];
    /** The list of available users in the system */
    users?: ProtechtUserRest[];
};
export type UserTableMetadataRest = {
    /** If true, register entries are displayed in the Registers Review screen with a preview pane. */
    allowPreviewPanel?: boolean;
    /** If true, new entries can be created in the register without logging in to Protecht ERM. */
    anonymousAccess?: boolean;
    /** The unique ID of the application where this register is located */
    applicationId?: number;
    /** The name of the application where this register is located */
    applicationName?: string;
    /** Application configuration unique ID - this ID is system generated and assigned to an application when it is created. */
    applicationUuid?: string;
    /** If true, user has permission to create new entry for this register */
    canBeCreated?: boolean;
    /** If true, user has permission to delete entry for this register */
    canBeDeleted?: boolean;
    /** A key-value mapping between a columnID (key) and the column name (value), for example: [ { 1 : "col_xxxxx" } ] */
    columns?: {
        [key: string]: string;
    };
    /** If true, commenting is allowed for this register. */
    comments?: boolean;
    contractMappings?: ContractMappingRest[];
    /** If tabLayout is true, the tab name provided in defaultTabName will be active when a user creates a new entry or opens an existing entry. */
    defaultTabName?: string;
    /** Register description */
    description?: string;
    /** If true, turns on pessimistic locking, which locks the register while a user is editing a record. */
    editLock?: boolean;
    /** Number of entries in this register */
    entriesCount?: number;
    favourite?: boolean;
    /** Global Lists used in the register */
    globalLists?: GlobalListRest[];
    /** If true, reporting is available for previous versions of entries in the register. */
    historicalReports?: boolean;
    /** Unique ID for the icon that appears on the register tile in the user interface */
    iconId?: string;
    /** Register unique ID */
    id?: number;
    identityColumn?: FieldRest;
    /** When <b>schedulerState</b> is also set to true, "Auto Create" column is displayed in the register. */
    isAutoCreateVisible?: boolean;
    /** Display name of a register */
    label?: string;
    /** If true, prevents the CORE Business Unit field automatically populating with the current user's Primary Business Unit. */
    leaveCoreBUBlank?: boolean;
    /** Visibility setting relevant only to library registers. Options are Default (visible only with other library registers), Always (visible to all other registers), or Hidden (hidden from other registers). */
    libVisibility?: 'Default' | 'Always' | 'Hidden';
    /** If true, all fields within a register will be locked. Users are then unable to edit or add entries to the register. */
    lockRecords?: boolean;
    /** If true, users can create register entries in the Protecht.ERM mobile app while offline. */
    offline?: boolean;
    /** When <b>offline</b> is also set to true, allows mobile app users to download register entries, edit them while offline, and upload changes to the server when back online. Once downloaded, each original entry on the server is locked to prevent changes until the user who downloaded it has uploaded their changes or released the entry. */
    offlineLock?: boolean;
    /** List of parent registers where this register is a sub-table */
    parentTableNames?: string[];
    properties?: Properties;
    /** There are 3 possible types of registers. Primary: the most commonly used and the default type. Private Table: a sub-table whose entries relate to the primary record (one to many relationship) and is not shared between registers. Library/Shared: a sub-table whose entries can be used in multiple registers (many to many relationship). */
    registerType?: 'Primary' | 'Private' | 'Shared' | 'Questionnaire' | 'CentralLibrary' | 'Framework';
    /** A list of conditional rule sets defined in the register. A conditional rule is a combination of an expression and an action that can be used to control visibility, edit access, validation, or filter selection in fields or sections of registers. */
    ruleSets?: FieldRuleSetRest[];
    /** A list of all risk or self assessment scale sets used in the register. Scales are used for measuring and setting risk appetite and may be used in a register as a list or a visual risk matrix field. */
    scaleSets?: ScaleSetRest[];
    /** If true, users can set an entry to automatically duplicate and create a new entry based on defined parameters. */
    schedulerState?: boolean;
    /** Application scope setting relevant only to library registers. Options are Application (only available to be used within that specific application) or Global (can be shared across other applications). */
    scope?: 'Application' | 'Global';
    /** Sections contained within the register, in which fields are laid out for data entry and display. */
    sections?: SectionRest[];
    /** Applicable only for global registers. An ID of application a global register belonged to before it was made global. Use null value or application ID in case of application scoped registers. */
    sourceApplicationId?: number;
    stateDefinition?: RegisterStateDefinitionRest;
    /** If true, users will have the ability to move register entries through a series of defined states (for example Open, Under Review and Closed) and trigger specific workflows and reporting responses that are conditional to the current state. */
    stateful?: boolean;
    statusColumn?: FieldRest;
    style?: StyleRest;
    /** If true, sections may be displayed on tabs in the register. The tabName property in the SectionRest object determines which tab a section appears on. */
    tabLayout?: boolean;
    /** The table name that represents the register */
    tableName?: string;
    /** A list of fields that can be linked to My Tasks. For a register to be available in My Tasks, it must use states and have at least one user field. */
    taskColumns?: TaskColumnRest[];
    /** Number of templates used in the register. */
    templateCount?: number;
    uniqueColumn?: FieldRest;
    userPermissionCodesForRegister?: string[];
    /** Register configuration unique ID - this ID is randomly generated and unique for every user-created register in the system */
    uuid?: string;
    /** A set of conditions based on the register that, when met, trigger an automated workflow action. */
    workFlowRules?: WorkFlowRuleRest[];
};
export type PrsGetPermissionsUsingGetApiResponse = /** status 200 Returns a list of permissions in the response body. */ PaginRestResultPermissionRest;
export type PrsGetPermissionsUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
};
export type PrsGetPermissionUsingGetApiResponse = /** status 200 The permission matching the unique ID provided is returned. */ PermissionRest;
export type PrsGetPermissionUsingGetApiArg = {
    /** The unique ID of the permission. */
    permissionId: number;
};
export type PaginRestResultPermissionRest = {
    maxPage?: number;
    records?: PermissionRest[];
    response?: PermissionRest;
    totalCount?: number;
};
export type PrsGetConsequencesUsingGetApiResponse = /** status 200 Returns a list of consequence values in the response body. */ ScaleItemRest[];
export type PrsGetConsequencesUsingGetApiArg = {
    /** The column name that represents the consequence field within the register, for example "col_xxxxxx". */
    columnName: string;
};
export type PrsGetCountriesUsingPostApiResponse = /** status 200 Returns a list of countries in the response body. */ PaginRestResultIdWithNameRest;
export type PrsGetCountriesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type PrsGetLikelihoodsUsingGetApiResponse = /** status 200 Returns a list of likelihood values in the response body. */ ScaleItemRest[];
export type PrsGetLikelihoodsUsingGetApiArg = {
    /** The column name that represents the consequence field within the register, for example "col_xxxxxx". */
    columnName: string;
};
export type PrsGetStatesUsingPostApiResponse = /** status 200 Returns a list of states in the response body. */ PaginRestResultIdWithNameRest;
export type PrsGetStatesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type PaginRestResultIdWithNameRest = {
    maxPage?: number;
    records?: IdWithNameRest[];
    response?: IdWithNameRest;
    totalCount?: number;
};
export type GetIdPsApiResponse =
    /** status 200 The enabled external configured <b>ExternalSamlConfigurationRest</b> will be returned. */ ExternalSamlConfigurationRest;
export type GetIdPsApiArg = void;
export type ExternalIdpConfigurationRest = {
    default?: boolean;
    name?: string;
    redirectionUrl?: string;
    type?: string;
};
export type ExternalSamlConfigurationRest = {
    externalIdps?: ExternalIdpConfigurationRest[];
};
export type QrsGetQuestionnairesByMetricUsingPostApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultQuestionnaireDataRest;
export type QrsGetQuestionnairesByMetricUsingPostApiArg = {
    /** The unique ID of vendor. */
    vendorId?: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** Metric ID to filter entries by */
    metricId: number;
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type QrsGetQuestionnaireRegistersUsingPostApiResponse = /** status 200 The list of registers is returned. */ PaginRestResultTableMetadataRest;
export type QrsGetQuestionnaireRegistersUsingPostApiArg = {
    /** The unique ID of the questionnaire group. */
    questionnaireGroupId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    skipSectionData?: boolean;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type QrsGetQuestionnaireRegistersGetUsingGetApiResponse = /** status 200 The list of registers is returned. */ PaginRestResultTableMetadataRest;
export type QrsGetQuestionnaireRegistersGetUsingGetApiArg = {
    /** The unique ID of the questionnaire group. */
    questionnaireGroupId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    skipSectionData?: boolean;
    /** Payload */
    payload?: string;
};
export type QrsAssigneMultipleQuestionnairesToVendorUsingPostApiResponse = /** status 200 successful operation */ QuestionnaireDataRest[];
export type QrsAssigneMultipleQuestionnairesToVendorUsingPostApiArg = {
    /** Vendor ID for which questionnaire will be assigned */
    vendorId: number;
    /** List of questionnaire register ids with due dates. */
    body: IdWithDueDate[];
};
export type QrsAssignMultipleQuestionnairesToMultipleVendorsUsingPostApiResponse = /** status 200 successful operation */ QuestionnaireDataRest[];
export type QrsAssignMultipleQuestionnairesToMultipleVendorsUsingPostApiArg = {
    /** <b>QuestionnairesVendorsDataRest</b> JSON data used to assign multiple questionnaires to multiple vendors. */
    questionnairesVendorsDataRest: QuestionnairesVendorsDataRest;
};
export type QrsAssignQuestionnaireUsingPostApiResponse = /** status 200 successful operation */ QuestionnaireDataRest;
export type QrsAssignQuestionnaireUsingPostApiArg = {
    /** Questionnaire register id. */
    registerId: number;
    questionnaireAssignment: QuestionnaireAssignment;
};
export type QrsGetQuestionnairesUsingPostApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultQuestionnaireDataRest;
export type QrsGetQuestionnairesUsingPostApiArg = {
    /** The unique ID of the register. */
    registerId: number;
    /** The unique ID of vendor. */
    vendorId?: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** Status IDs used for filtering. Valid values are ACTIVE(0), DELETED(2). */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type QrsGetQuestionnairesGetUsingGetApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultQuestionnaireDataRest;
export type QrsGetQuestionnairesGetUsingGetApiArg = {
    /** The unique ID of the register. */
    registerId: number;
    /** The unique ID of vendor. */
    vendorId?: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** Status IDs used for filtering */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** Payload */
    payload?: string;
};
export type QrsBulkAssignQuestionnaireUsingPostApiResponse = /** status 200 successful operation */ QuestionnaireDataRest[];
export type QrsBulkAssignQuestionnaireUsingPostApiArg = {
    /** Questionnaire register id. */
    registerId: number;
    /** List of vendor ids with due dates. */
    body: IdWithDueDate[];
};
export type QrsDeleteQuestionnaireUsingDeleteApiResponse = unknown;
export type QrsDeleteQuestionnaireUsingDeleteApiArg = {
    registerId: number;
    entryId: number;
};
export type QrsGetQuestionnairesForVendorUserUsingPostApiResponse =
    /** status 200 The register entries matching the search criteria are returned in the response body. */ PaginRestResultQuestionnaireVendorUserDataRest;
export type QrsGetQuestionnairesForVendorUserUsingPostApiArg = {
    /** The unique ID of the register. */
    registerId: number;
    /** The unique ID of the vendor user. */
    vendorUserId: number;
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** The column name that represents the field you want to search, for example: "col_xxxxxx". */
    keys?: string[];
    /** The exact value to search for in the field identified by the keys parameter. */
    values?: string[];
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** Status IDs used for filtering */
    statuses?: number[];
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** ViewExpressionRest JSON data object used to filter the data using property and value pairs with comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type QuestionnaireDataRest = {
    questionnaireEntryId?: number;
    questionnaireMetaDataEntry?: RegisterDataRest;
    questionnaireRegisterId?: number;
    questionnaireRegisterLabel?: string;
    questionnaireRegisterName?: string;
};
export type PaginRestResultQuestionnaireDataRest = {
    maxPage?: number;
    records?: QuestionnaireDataRest[];
    response?: QuestionnaireDataRest;
    totalCount?: number;
};
export type IdWithDueDate = {
    /** Due date value which will be used as default due date value in meta data register. Expected format 'yyyy-MM-dd'. */
    dueDate?: string;
    /** ID */
    id?: number;
};
export type QuestionnairesVendorsDataRest = {
    /** questionnairesWithDueDates */
    questionnairesWithDueDates?: IdWithDueDate[];
    /** vendors */
    vendors?: number[];
};
export type QuestionnaireAssignment = {
    /** Due date value which will be used as default due date value in meta data register. Expected format 'yyyy-MM-dd'. */
    dueDate?: string;
    /** Vendor ID */
    vendorId?: number;
};
export type QuestionnaireVendorUserDataRest = {
    fields?: Field[];
    questionnaireEntryId?: number;
    questionnaireRegisterId?: number;
    questionnaireRegisterLabel?: string;
    questionnaireRegisterName?: string;
};
export type PaginRestResultQuestionnaireVendorUserDataRest = {
    maxPage?: number;
    records?: QuestionnaireVendorUserDataRest[];
    response?: QuestionnaireVendorUserDataRest;
    totalCount?: number;
};
export type GetRegisterConfigurationsApiResponse =
    /** status 200 Returns a list of register configurations in the response body. */ PaginRestResultTableMetadataRest;
export type GetRegisterConfigurationsApiArg = {
    /** The unique ID of an application. An application is a logical grouping of related registers with its own menu. For specific type of register it is required to provide application id. */
    applicationId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** The name of the field used to sort the list of entries */
    orderBy?: string;
    /** Sorting order (ascending or descending) */
    orderType?: 'asc' | 'desc';
    /** The name of a register as displayed on the user interface. */
    label?: string;
    /** A register's description - typically used to describe the context of use or other supplementary information. */
    description?: string;
    /** A flag to match registers that have states and state transitions enabled */
    stateful?: boolean;
    /** A filter to match and return registers based on whether they are short-listed as favorites on the Register Entry and Register Review screens for the current user:<ul> <li> <b>TOP</b> returns favorite registers first, followed by all other registers.</li> <li> <b>ONLY</b> returns favorite registers only.</li> <li> <b>ALL</b> returns all registers, regardless of favorite list status.</li></ul> */
    favouriteDisplayChoice?: 'TOP' | 'ONLY' | 'ALL';
    /** A filter to match and return specific register types:<ul> <li> A <b>Primary Register</b> is a parent level register which is typically the entry point for a user entry.</li> <li> A <b>Library Register</b> is a register that can be shared among multiple primary registers.</li> </ul> REGISTERS_ONLY returns primary registers as well as library registers defined as always visible. If this parameter is <b>null</b>, all register types will be returned. */
    libraryDisplayChoice?: 'REGISTERS_ONLY' | 'LIBRARY_ONLY' | 'LIBRARIES_INCLUDED';
    /** A flag to determine whether section and column (field) metadata is returned for each register, or just the register metadata.<br><br> Select true to limit the response to only register-level data. If false, the metadata for all of a register's sections and columns (fields) will be returned, as well as the register-level data. */
    skipSectionData?: boolean;
    /** Optional flag to include register template count. */
    includeTemplateCount?: boolean;
};
export type TmrsCreateRegisterConfigUsingPostApiResponse =
    /** status 200 The newly created register configuration is returned in the response body. */ TableMetadataRest;
export type TmrsCreateRegisterConfigUsingPostApiArg = {
    /** The <b>TableMetadataRest</b> JSON data used to create the register configuration. */
    tableMetadataRest: TableMetadataRest;
};
export type TmrsGetActionRegistersUsingGetApiResponse = /** status 200 Returns a list of register configurations in the response body. */ {
    [key: string]: TableMetadataRest[];
};
export type TmrsGetActionRegistersUsingGetApiArg = {
    /** A list of modules where actions are linked to entries. The actions registers linked to entries in these modules will be returned. <br><br>There are three possible options:<ul> <li> 		KRI entries (krientry)</li>	<li> 		Audit question assignments (auditquestionassignment)	</li>	<li>		Compliance entries (complianceentry) 	</li></ul>If you provide a key that doesn't match one of the modules listed above, actions registers linked to all three modules will be returned. */
    linkTables: ('krientry' | 'auditquestionassignment' | 'complianceentry')[];
};
export type TmrsGetAllRegistersWithBowTieMslUsingGetApiResponse = /** status 200 successful operation */ RegisterBowTieRest[];
export type TmrsGetAllRegistersWithBowTieMslUsingGetApiArg = void;
export type FrirsGetRegisterRulesByTableNamesUsingGetApiResponse =
    /** status 200 The list of conditional rules is returned in the response body, grouped by register. */ BulkFieldRuleRest[];
export type FrirsGetRegisterRulesByTableNamesUsingGetApiArg = {
    /** List of table names that represent the registers. For example: "table_xxxxxx". */
    tableNames?: string[];
};
export type TmrsGetRegistersUsingPostApiResponse = /** status 200 The list of registers is returned. */ PaginRestResultTableMetadataRest;
export type TmrsGetRegistersUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Contract Types */
    contractTypes?: number[];
    /** A filter to match and return specific register types:<ul> <li> A <b>Primary Register</b> is a parent level register which is typically the entry point for a user entry.</li> <li> A <b>Library Register</b> is a register that can be shared among multiple primary registers.</li> </ul> If this field is <b>null</b>, all register types will be returned. */
    libraryDisplayChoice?: 'REGISTERS_ONLY' | 'LIBRARY_ONLY' | 'LIBRARIES_INCLUDED';
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type TmrsGetRegistersGetUsingGetApiResponse = /** status 200 The list of registers is returned. */ PaginRestResultTableMetadataRest;
export type TmrsGetRegistersGetUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Contract Types */
    contractTypes?: number[];
    /** A filter to match and return specific register types:<ul> <li> A <b>Primary Register</b> is a parent level register which is typically the entry point for a user entry.</li> <li> A <b>Library Register</b> is a register that can be shared among multiple primary registers.</li> </ul> If this field is <b>null</b>, all register types will be returned. */
    libraryDisplayChoice?: 'REGISTERS_ONLY' | 'LIBRARY_ONLY' | 'LIBRARIES_INCLUDED';
    /** Payload */
    payload?: string;
};
export type TmrsGetRegistersStatsUsingGetApiResponse = /** status 200 Returns a list of register statistics in the response body. */ RegisterStatsDto[];
export type TmrsGetRegistersStatsUsingGetApiArg = {
    /** The list of registers for which we want to retrieve statistics */
    registers: string[];
    /** The unique ID of an Business Unit. Filter entries by given business unit. */
    businessUnitFilter?: number;
};
export type TmrsGetRegisterConfigUsingGetApiResponse = /** status 200 The register configuration is returned in the response body. */ TableMetadataRest;
export type TmrsGetRegisterConfigUsingGetApiArg = {
    /** The unique table name that represents the register. Example: table_xxxxx */
    tableName: string;
};
export type TmrsGetRegisterConfigsUsingGetApiResponse =
    /** status 200 The register configurations are returned in the response body. */ PaginRestResultTableMetadataRest;
export type TmrsGetRegisterConfigsUsingGetApiArg = {
    /** The unique ID of the application. */
    applicationId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
};
export type TmrsGetRegisterConfigUsingGet1ApiResponse = /** status 200 The register configuration is returned in the response body. */ TableMetadataRest;
export type TmrsGetRegisterConfigUsingGet1ApiArg = {
    /** The unique ID of the register. */
    id: number;
};
export type UpdateRegisterApiResponse = unknown;
export type UpdateRegisterApiArg = {
    /** The unique ID of the register */
    regId: number;
    /** The <b>TableMetadataRest</b> JSON data used to update the register configuration. */
    tableMetadataRest: TableMetadataRest;
};
export type TmrsDeleteRegisterConfigUsingDeleteApiResponse = unknown;
export type TmrsDeleteRegisterConfigUsingDeleteApiArg = {
    /** The unique ID of the register */
    regId: number;
};
export type TmrsToggleRegisterFavouriteUsingPutApiResponse = unknown;
export type TmrsToggleRegisterFavouriteUsingPutApiArg = {
    /** The unique ID of the register. */
    regId: number;
};
export type TmrsGetRegisterFieldConfigByLabelUsingGetApiResponse = /** status 200 The field configuration is returned in the response body. */ FieldRest;
export type TmrsGetRegisterFieldConfigByLabelUsingGetApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The label of the field as displayed in a register section */
    'field-label'?: string;
    /** The label of the section (as displayed in the register) that contains the field. */
    'section-label'?: string;
    /** The column name of the field for a register section */
    'column-name'?: string;
};
export type SetRegisterLayoutApiResponse = unknown;
export type SetRegisterLayoutApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The RegisterLayout JSON data used to set the layout of register sections or tabs. <b>Note:</b><ul><li>The sections will be displayed in the same order that section IDs are listed in the request body.</li><li> Set tabName value to an empty string to remove a defined tab. The sections inside this tab will be merged to the Main tab by default.</li></ul> */
    body: RegisterLayout[];
};
export type AddSectionApiResponse = unknown;
export type AddSectionApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The unique ID of the section to be added. */
    body: number;
};
export type DeleteSectionApiResponse = unknown;
export type DeleteSectionApiArg = {
    /** The unique ID of the register. */
    regId: number;
    /** The unique ID of the section to be removed from the register. */
    body: number;
};
export type TmrsSetRegisterStateUsingPutApiResponse = unknown;
export type TmrsSetRegisterStateUsingPutApiArg = {
    /** The unique ID of the register */
    regId: number;
    /** The RegisterStateDefinitionRest JSON data used to apply the states and transitions to the register. */
    registerStateDefinitionRest: RegisterStateDefinitionRest;
};
export type TmrsCancelRegisterStateUsingDeleteApiResponse = unknown;
export type TmrsCancelRegisterStateUsingDeleteApiArg = {
    /** The unique ID of the register. */
    regId: number;
};
export type TmrsSetDefaultRegisterStateUsingPutApiResponse = unknown;
export type TmrsSetDefaultRegisterStateUsingPutApiArg = {
    /** The unique ID of the register. */
    regId: number;
};
export type FrirsGetRulesUsingGetApiResponse = /** status 200 The list of conditional rules is returned in the response body. */ FieldRuleRest[];
export type FrirsGetRulesUsingGetApiArg = {
    /** The unique ID of the register. */
    registerId: number;
};
export type FrirsAddRuleUsingPutApiResponse = unknown;
export type FrirsAddRuleUsingPutApiArg = {
    /** The unique ID of the register. */
    registerId: number;
    /** JSON data used to add the conditional rule to the register. */
    fieldRuleRest: FieldRuleRest;
};
export type FrirsDeleteRuleUsingDeleteApiResponse = unknown;
export type FrirsDeleteRuleUsingDeleteApiArg = {
    /** The unique ID of the register. */
    registerId: number;
    /** The unique ID of the rule to be deleted. */
    ruleId: number;
};
export type RegisterBowTieRest = {
    /** List of register fields which represents bow ties multi select library fields. */
    fields?: FieldBowTieRest[];
    /** Register configuration ID. */
    id?: number;
    /** Register label. */
    label?: string;
    /** Register table name. */
    tableName?: string;
};
export type BulkFieldRuleRest = {
    /** The list of available conditional rules defined in the register */
    rules?: FieldRuleRest[];
    /** The table name that represents the register where the conditional rules are defined */
    tableName?: string;
};
export type RegisterStatsDto = {
    entriesDueNow?: number;
    entriesInFinalState?: number;
    entriesInInitialState?: number;
    entriesNotDue?: number;
    entriesOverdue?: number;
    openEntries?: number;
    registerId?: number;
    tableName?: string;
};
export type RegisterLayout = {
    /** A list of section unique IDs */
    sectionIds?: number[];
    /** The name of the tab that contains the sections listed at sectionIds */
    tabName?: string;
};
export type RrsGetLinksUsingPostApiResponse = /** status 200 Associated Linked Object Ids */ LinkRequestResponse[];
export type RrsGetLinksUsingPostApiArg = {
    reverse?: boolean;
    filterIds?: number[];
    body: LinkRequestResponse[];
};
export type RrsGetProcessesUsingPostApiResponse = /** status 200 Associated processes */ ProcResultWrapperRest;
export type RrsGetProcessesUsingPostApiArg = {
    primaryId: number;
    body: ResourceRequest[];
};
export type RrsGetResourcesUsingPostApiResponse = /** status 200 Associated Resources */ ResourcesResultWrapperRest;
export type RrsGetResourcesUsingPostApiArg = {
    primaryId: number;
    body: ProcessRequest[];
};
export type RrsGetAffectedEntriesUsingGetApiResponse = /** status 200 Associated Linked Object Ids */ IdWithNameRest[];
export type RrsGetAffectedEntriesUsingGetApiArg = {
    processId: number;
};
export type RrsGetDefinitionUsingGetApiResponse = /** status 200 successful operation */ OperationalResilienceDefinition;
export type RrsGetDefinitionUsingGetApiArg = {
    extended?: boolean;
};
export type RrsUpdateDiagramDefinitionUsingPutApiResponse = unknown;
export type RrsUpdateDiagramDefinitionUsingPutApiArg = {
    operationalResilienceDefinition: OperationalResilienceDefinition;
};
export type RrsGetDiagramUsingGetApiResponse = /** status 200 successful operation */ ResilienceDiagramRest;
export type RrsGetDiagramUsingGetApiArg = {
    diagramId?: number;
    primaryId?: number;
};
export type RrsCreateDiagramUsingPostApiResponse = /** status 200 successful operation */ ResilienceDiagramRest;
export type RrsCreateDiagramUsingPostApiArg = {
    resilienceDiagramRest: ResilienceDiagramRest;
};
export type RrsDeleteDiagramUsingDeleteApiResponse = unknown;
export type RrsDeleteDiagramUsingDeleteApiArg = {
    diagramId?: number;
    primaryId?: number;
};
export type RrsCopyDiagramUsingPutApiResponse = /** status 200 successful operation */ ResilienceDiagramRest;
export type RrsCopyDiagramUsingPutApiArg = {
    diagramId?: number;
    primaryId?: number;
    body: string;
};
export type RrsUpdateDiagramUsingPutApiResponse = /** status 200 successful operation */ ResilienceDiagramRest;
export type RrsUpdateDiagramUsingPutApiArg = {
    diagramId: number;
    resilienceDiagramUpdate: ResilienceDiagramUpdate;
};
export type RrsGetTerminologyUsingGetApiResponse = /** status 200 successful operation */ ModuleTerminology;
export type RrsGetTerminologyUsingGetApiArg = void;
export type LinkRequestResponse = {
    column?: string;
    filterIds?: number[];
    ids?: number[];
    subtable?: string;
    table?: string;
};
export type IsUsingResInfo = {
    resCol?: string;
    resId?: number;
    resTable?: string;
};
export type ProcInfoRest = {
    isUsing?: IsUsingResInfo[];
    procId?: number;
};
export type ProcResultRest = {
    info?: ProcInfoRest[];
    type?: string;
};
export type ProcResultWrapperRest = {
    processes?: ProcResultRest[];
};
export type ResourceRequest = {
    ids?: number[];
    resourceTable?: string;
};
export type UsedByProcessInfo = {
    procId?: number;
    procTable?: string;
    resCol?: string;
};
export type ResourceInfoRest = {
    resId?: number;
    usedIn?: UsedByProcessInfo[];
};
export type ResourcesResultRest = {
    info?: ResourceInfoRest[];
    type?: string;
};
export type ResourcesResultWrapperRest = {
    resources?: ResourcesResultRest[];
};
export type ProcessRequest = {
    ids?: number[];
    processTable?: string;
};
export type OperationalResilienceRegister = {
    applicationId?: number;
    displayColumn?: string;
    name?: string;
    primary?: boolean;
    registerId?: number;
    registerTable?: string;
};
export type ResilienceColumnDef = {
    col?: string;
    table?: string;
};
export type OperationalResilienceResourceRegister = {
    applicationId?: number;
    customName?: string;
    displayColumn?: string;
    name?: string;
    primary?: boolean;
    registerId?: number;
    registerTable?: string;
    resourceTypeField?: string;
};
export type OperationalResilienceDefinition = {
    criticalServiceMapRegister?: OperationalResilienceRegister;
    plausibleScenariosRegister?: OperationalResilienceRegister;
    processRegister?: OperationalResilienceRegister;
    resourcesInProcess?: ResilienceColumnDef[];
    resourcesInScenario?: ResilienceColumnDef[];
    resourcesRegisters?: OperationalResilienceResourceRegister[];
};
export type ProcessResource = {
    ids?: number[];
    resourceType?: string;
};
export type StructureProcess = {
    processId?: number;
    resources?: ProcessResource[];
};
export type DiagramStructure = {
    processes?: StructureProcess[];
};
export type ResilienceDiagramRest = {
    automaticLayoutEnabled?: boolean;
    diagramImage?: string;
    diagramImageUuid?: string;
    diagramModel?: JsonNode;
    diagramRegister?: RegisterDataRest;
    displayOption?: string;
    id?: number;
    name?: string;
    showLegendEnabled?: boolean;
    structure?: DiagramStructure;
};
export type ResilienceDiagramUpdate = {
    automaticLayoutEnabled?: boolean;
    description?: string;
    diagramImage?: string;
    diagramImageUuid?: string;
    diagramModel?: JsonNode;
    diagramRegister?: RegisterDataRest;
    displayOption?: string;
    id?: number;
    name?: string;
    showLegendEnabled?: boolean;
    structure?: DiagramStructure;
};
export type ModuleTerminology = {
    language?: string;
    localizedMessages?: {
        [key: string]: string;
    };
    module?: 'RESILIENCE' | 'BOWTIE';
    region?: string;
};
export type RcrsCreateRiskCauseUsingGetApiResponse = /** status 200 The newly created risk cause is returned in the response body. */ RiskCauseBaseRest;
export type RcrsCreateRiskCauseUsingGetApiArg = void;
export type RcrsDeleteRiskCauseUsingDeleteApiResponse = unknown;
export type RcrsDeleteRiskCauseUsingDeleteApiArg = {
    /** The unique IDs of the risk causes to be deleted. */
    riskCauseIds: number[];
    /** A flag to determine whether the operation is stopped in the case of any errors. If true, the operation won't be stopped, all risk causes that can be deleted will be deleted, and information about failed deletions will be returned. If false, execution of the operation is stopped and all changes rolled back. <br><i>Default value</i> : false */
    allowPartialDelete?: boolean;
};
export type ExportRegisterEntriesPost1ApiResponse = /** status 200 Export process identificator. */ string;
export type ExportRegisterEntriesPost1ApiArg = {
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** Flag to determine whether system fields will be included in export or not. */
    excludeSystemFields?: boolean;
    /** Data set export request containing view expressions and export configuration */
    dataSetExportRequest: DataSetExportRequest;
};
export type GetExportContentGet1ApiResponse = unknown;
export type GetExportContentGet1ApiArg = {
    /** The unique finished export process identifier */
    exportid: string;
};
export type GetExportProgressGet1ApiResponse = /** status 200 Current export progress response. */ ExportProgressResponse;
export type GetExportProgressGet1ApiArg = {
    /** The unique running export process identifier */
    exportid: string;
};
export type GetRiskCausesApiResponse = /** status 200 The list of risk causes is returned. */ PaginRestResultRiskCauseBaseRest;
export type GetRiskCausesApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** Tag type ID used for grouping */
    tagType?: number;
    /** Type of the tag operator */
    tagOperator?: 'AND' | 'OR';
    /** Tag IDs used for filtering */
    tagIds?: number[];
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Status IDs used for filtering */
    statuses?: number[];
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type RcrsGetRiskCauseUsingGetApiResponse = /** status 200 The risk cause is returned in the response body. */ RiskCauseBaseRest;
export type RcrsGetRiskCauseUsingGetApiArg = {
    /** The unique ID of the risk cause. */
    riskCauseId: number;
};
export type RcrsUpdateRiskCauseUsingPutApiResponse = /** status 200 The updated risk cause data is returned in the response body. */ RiskCauseBaseRest;
export type RcrsUpdateRiskCauseUsingPutApiArg = {
    /** The unique ID of the risk cause to be updated. */
    riskCauseId: number;
    /** <b>RiskCauseBaseRest</b> JSON data used to update the risk cause. */
    riskCauseBaseRest: RiskCauseBaseRest;
};
export type GetRiskCausesHistoryApiResponse =
    /** status 200 The list of historical versions for the risk cause is returned. */ PaginRestResultRiskCauseBaseRest;
export type GetRiskCausesHistoryApiArg = {
    /** The unique ID of the risk cause for which historical versions will be returned. */
    riskCauseId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type CrsCreateRiskControlUsingGetApiResponse = /** status 200 The newly created risk control is returned in the response body. */ ControlBaseRest;
export type CrsCreateRiskControlUsingGetApiArg = void;
export type CrsDeleteRiskControlUsingDeleteApiResponse = unknown;
export type CrsDeleteRiskControlUsingDeleteApiArg = {
    /** The unique IDs of the risk controls to be deleted. */
    riskControlIds: number[];
};
export type ExportRegisterEntriesPost12ApiResponse = /** status 200 Export process identificator. */ string;
export type ExportRegisterEntriesPost12ApiArg = {
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** Flag to determine whether system fields will be included in export or not. */
    excludeSystemFields?: boolean;
    /** Data set export request containing view expressions and export configuration */
    dataSetExportRequest: DataSetExportRequest;
};
export type GetExportContentGet12ApiResponse = unknown;
export type GetExportContentGet12ApiArg = {
    /** The unique finished export process identifier */
    exportid: string;
};
export type GetExportProgressGet12ApiResponse = /** status 200 Current export progress response. */ ExportProgressResponse;
export type GetExportProgressGet12ApiArg = {
    /** The unique running export process identifier */
    exportid: string;
};
export type GetRiskControlApiResponse = /** status 200 The list of risk controls is returned. */ PaginRestResultControlBaseRest;
export type GetRiskControlApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** Tag type ID used for grouping */
    tagType?: number;
    /** Type of the tag operator */
    tagOperator?: 'AND' | 'OR';
    /** Tag IDs used for filtering */
    tagIds?: number[];
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Status IDs used for filtering */
    statuses?: number[];
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type CrsGetControlUsingGetApiResponse = /** status 200 The risk control is returned in the response body. */ ControlBaseRest;
export type CrsGetControlUsingGetApiArg = {
    /** The unique ID of the risk control. */
    riskControlId: number;
};
export type CrsUpdateRiskControlUsingPutApiResponse = /** status 200 The updated risk control data is returned in the response body. */ ControlBaseRest;
export type CrsUpdateRiskControlUsingPutApiArg = {
    /** The unique ID of the risk control to be updated. */
    riskControlId: number;
    /** <b>ControlBaseRest</b> JSON data used to update the risk control. */
    controlBaseRest: ControlBaseRest;
};
export type GetRiskControlHistoryApiResponse =
    /** status 200 The list of historical versions for the risk control is returned. */ PaginRestResultControlBaseRest;
export type GetRiskControlHistoryApiArg = {
    /** The unique ID of the risk control for which historical versions will be returned. */
    riskControlId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RersCreateRiskEventUsingGetApiResponse = /** status 200 The newly created risk event is returned in the response body. */ RiskEventBaseRest;
export type RersCreateRiskEventUsingGetApiArg = void;
export type RersDeleteRiskEventUsingDeleteApiResponse = unknown;
export type RersDeleteRiskEventUsingDeleteApiArg = {
    /** The unique IDs of the risk events to be deleted. */
    riskEventIds: number[];
    /** A flag to determine whether the operation is stopped in the case of any errors. If true, the operation won't be stopped, all risk events that can be deleted will be deleted, and information about failed deletions will be returned. If false, execution of the operation is stopped and all changes rolled back. <br><i>Default value</i> : false */
    allowPartialDelete?: boolean;
};
export type ExportRegisterEntriesPost123ApiResponse = /** status 200 Export process identificator. */ string;
export type ExportRegisterEntriesPost123ApiArg = {
    /** The ID of the applied view to map only view fields to final response. In case of viewId = -1, only core section fields are mapped into our response.  */
    viewId?: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list of entries. */
    orderBy?: string;
    /** Sorting order (either "asc" or "desc"). */
    orderType?: string;
    /** Name of the field used to group the list of entries. */
    groupBy?: string;
    /** The unique IDs of the register entries to include in the list. */
    selectedItems?: number[];
    /** Flag to determine whether system fields will be included in export or not. */
    excludeSystemFields?: boolean;
    /** Data set export request containing view expressions and export configuration */
    dataSetExportRequest: DataSetExportRequest;
};
export type GetExportContentGet123ApiResponse = unknown;
export type GetExportContentGet123ApiArg = {
    /** The unique finished export process identifier */
    exportid: string;
};
export type GetExportProgressGet123ApiResponse = /** status 200 Current export progress response. */ ExportProgressResponse;
export type GetExportProgressGet123ApiArg = {
    /** The unique running export process identifier */
    exportid: string;
};
export type GetRiskEventsApiResponse = /** status 200 The list of risk events is returned. */ PaginRestResultRiskEventBaseRest;
export type GetRiskEventsApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** Tag type ID used for grouping */
    tagType?: number;
    /** Type of the tag operator */
    tagOperator?: 'AND' | 'OR';
    /** Tag IDs used for filtering */
    tagIds?: number[];
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Status IDs used for filtering */
    statuses?: number[];
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type RersGetRiskEventUsingGetApiResponse = /** status 200 The risk event is returned in the response body. */ RiskEventBaseRest;
export type RersGetRiskEventUsingGetApiArg = {
    /** The unique ID of the risk event. */
    riskEventId: number;
};
export type RersUpdateRiskEventUsingPutApiResponse = /** status 200 The updated risk event data is returned in the response body. */ RiskEventBaseRest;
export type RersUpdateRiskEventUsingPutApiArg = {
    /** The unique ID of the risk event to be updated. */
    riskEventId: number;
    /** <b>RiskEventBaseRest</b> JSON data used to update the risk event. */
    riskEventBaseRest: RiskEventBaseRest;
};
export type GetRiskEventsHistoryApiResponse =
    /** status 200 The list of historical versions for the risk event is returned. */ PaginRestResultRiskEventBaseRest;
export type GetRiskEventsHistoryApiArg = {
    /** The unique ID of the risk event for which historical versions will be returned. */
    riskEventId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RiskCauseBaseRest = {
    completed?: boolean;
    createDate?: string;
    createdBy?: IdWithNameRest;
    /** Description of the risk cause */
    description?: string;
    /** Unique entity ID */
    id?: number;
    lastModifiedBy?: IdWithNameRest;
    lastModifiedDate?: string;
    /** Entity name */
    name?: string;
    /** Entity status */
    status?: number;
    /** Names of the tags linked to the risk cause */
    tags?: IdWithNameRest[];
};
export type PaginRestResultRiskCauseBaseRest = {
    maxPage?: number;
    records?: RiskCauseBaseRest[];
    response?: RiskCauseBaseRest;
    totalCount?: number;
};
export type ControlBaseRest = {
    createDate?: string;
    createdBy?: IdWithNameRest;
    /** The generation frequency for the question */
    defaultFrequency?: string;
    /** Description of the risk control */
    description?: string;
    /** Unique entity ID */
    id?: number;
    lastModifiedBy?: IdWithNameRest;
    lastModifiedDate?: string;
    /** Entity name */
    name?: string;
    /** Names of the risk events linked to the risk control */
    riskEvents?: IdWithNameAndStatusRest[];
    /** Entity status */
    status?: number;
    /** Names of the tags linked to the risk control */
    tags?: IdWithNameRest[];
};
export type PaginRestResultControlBaseRest = {
    maxPage?: number;
    records?: ControlBaseRest[];
    response?: ControlBaseRest;
    totalCount?: number;
};
export type RiskEventBaseRest = {
    completed?: boolean;
    createDate?: string;
    createdBy?: IdWithNameRest;
    /** Description of the risk event */
    description?: string;
    /** Unique entity ID */
    id?: number;
    lastModifiedBy?: IdWithNameRest;
    lastModifiedDate?: string;
    /** Entity name */
    name?: string;
    owner?: IdWithNameRest;
    /** Names of the risk causes linked to the risk event */
    riskCauses?: IdWithNameAndStatusRest[];
    /** Entity status */
    status?: number;
    /** Names of the tags linked to the risk event */
    tags?: IdWithNameRest[];
};
export type PaginRestResultRiskEventBaseRest = {
    maxPage?: number;
    records?: RiskEventBaseRest[];
    response?: RiskEventBaseRest;
    totalCount?: number;
};
export type UcGetUsersWithRoleUsingGetApiResponse = /** status 200 successful operation */ PagingResponseUserView;
export type UcGetUsersWithRoleUsingGetApiArg = {
    roleId: number;
    query?: string;
    'page-size'?: number;
    'page-number'?: number;
    sort?: string;
    dir?: string;
};
export type UcGetAvailableUsersForRoleUsingGetApiResponse = /** status 200 successful operation */ PagingResponseUserView;
export type UcGetAvailableUsersForRoleUsingGetApiArg = {
    roleId: number;
    query?: string;
    'page-size'?: number;
    'page-number'?: number;
    sort?: string;
    dir?: string;
};
export type UcMoveUsersUsingPatchApiResponse = unknown;
export type UcMoveUsersUsingPatchApiArg = {
    userMoveRequest: UserMoveRequest;
};
export type UcGetRelevantUsersUsingGetApiResponse = /** status 200 successful operation */ UserViewShared[];
export type UcGetRelevantUsersUsingGetApiArg = void;
export type UserView = {
    businessUnit?: string;
    id?: number;
    level?: number;
    name?: string;
    position?: string;
    username?: string;
};
export type PagingResponseUserView = {
    results?: UserView[];
    totalCount?: number;
};
export type UserMoveRequest = {
    movedUsers?: number[];
    sourceRoleId: number;
    targetRoleId: number;
};
export type GetRoles1ApiResponse = /** status 200 The list of roles is returned. */ PaginRestResultRoleRest;
export type GetRoles1ApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of role */
    name?: string;
};
export type RrsCreateRoleUsingPostApiResponse = /** status 200 successful operation */ RoleRest;
export type RrsCreateRoleUsingPostApiArg = {
    /** <b>RoleRest</b> JSON data used to create the role. */
    roleRest: RoleRest;
};
export type RrsGetRolesUsingPostApiResponse = /** status 200 Returns a list of roles in the response body. */ PaginRestResultRoleRest;
export type RrsGetRolesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type RrsGetRoleUsingGetApiResponse = /** status 200 The role is returned in the response body. */ RoleRest;
export type RrsGetRoleUsingGetApiArg = {
    /** The unique ID of the role. */
    roleId: number;
};
export type RrsUpdateRoleUsingPutApiResponse = /** status 200 The updated role data is returned in the response body. */ RoleRest;
export type RrsUpdateRoleUsingPutApiArg = {
    /** The unique ID of the role to be updated. */
    roleId: number;
    /** <b>RoleRest</b> JSON data used to update the role. */
    roleRest: RoleRest;
};
export type RrsDeleteRoleUsingDeleteApiResponse = unknown;
export type RrsDeleteRoleUsingDeleteApiArg = {
    /** The unique ID of the role to be deleted. */
    roleId: number;
};
export type RrsAddPermissionUsingPutApiResponse = /** status 200 The updated role data is returned in the response body. */ RoleRest;
export type RrsAddPermissionUsingPutApiArg = {
    /** The unique ID of the role to be updated. */
    roleId: number;
    /** The unique ID of the permission to be added to the role. */
    permissionId: number;
};
export type RrsRemovePermissionUsingDeleteApiResponse = /** status 200 The updated role data is returned in the response body. */ RoleRest;
export type RrsRemovePermissionUsingDeleteApiArg = {
    /** The unique ID of the role that the permission will be deleted from. */
    roleId: number;
    /** The unique ID of the permission to be deleted. */
    permissionId: number;
};
export type PaginRestResultRoleRest = {
    maxPage?: number;
    records?: RoleRest[];
    response?: RoleRest;
    totalCount?: number;
};
export type SrsGetScaleSetsUsingGetApiResponse = /** status 200 Returns a list of risk scale models in the response body. */ PaginRestResultScaleSetRest;
export type SrsGetScaleSetsUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. Limit is ignored if <b>pagingDisabled</b> is set to true. */
    limit?: number;
    /** If true, the response will contain all of the defined risk scales, up to a maximum of 50. If false, a limited number of risk scales will be included in the response, according to the value provided by the <b>limit</b> parameter. By default, limit is set to 10 and pagingDisabled is set to false, meaning 10 records will be returned. */
    pagingDisabled?: boolean;
};
export type SrsCreateScaleSetUsingPostApiResponse = /** status 200 Returns the newly created risk scale model in the response body. */ ScaleSetRest;
export type SrsCreateScaleSetUsingPostApiArg = {
    /** ScaleSetRest JSON data used to create the risk scale. */
    scaleSetRest: ScaleSetRest;
};
export type SrsUpdateScaleSetUsingPutApiResponse = /** status 200 Returns the updated risk scale model in the response body. */ ScaleSetRest;
export type SrsUpdateScaleSetUsingPutApiArg = {
    /** ScaleSetRest JSON data used to update the risk scale. */
    scaleSetRest: ScaleSetRest;
};
export type SrsDeleteScaleSetUsingDeleteApiResponse = unknown;
export type SrsDeleteScaleSetUsingDeleteApiArg = {
    /** The unique ID of the risk scale to be deleted. */
    scaleSetId: number;
};
export type SrsGetScaleSetUsingGetApiResponse = /** status 200 Returns the risk scale model that matches the unique ID provided. */ ScaleSetRest;
export type SrsGetScaleSetUsingGetApiArg = {
    /** The unique ID of the risk scale. */
    scaleSetId: number;
};
export type PaginRestResultScaleSetRest = {
    maxPage?: number;
    records?: ScaleSetRest[];
    response?: ScaleSetRest;
    totalCount?: number;
};
export type SrsCreateSectionUsingPostApiResponse =
    /** status 200 The newly created section is returned as a SectionRest object in the response body. */ SectionRest;
export type SrsCreateSectionUsingPostApiArg = {
    /** <b>SectionRest</b> JSON data used to create the section */
    sectionRest: SectionRest;
};
export type GetSectionsApiResponse =
    /** status 200 Returns a list of sections as <b>SectionRest</b> objects in the response body. */ PaginRestResultSectionRest;
export type GetSectionsApiArg = {
    /** The unique ID of the application. */
    applicationId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
};
export type SrsGetSectionUsingGetApiResponse = /** status 200 Returns the section as a <b>SectionRest</b> object in the response body. */ SectionRest;
export type SrsGetSectionUsingGetApiArg = {
    /** The unique ID of the section. */
    sectionId: number;
};
export type SrsUpdateSectionUsingPutApiResponse =
    /** status 200 Returns the updated section as a <b>SectionRest</b> object in the response body. */ SectionRest;
export type SrsUpdateSectionUsingPutApiArg = {
    /** The unique ID of the section. */
    sectionId: number;
    /** SectionRest JSON data used to update the section. */
    sectionRest: SectionRest;
};
export type SrsDeleteSectionUsingDeleteApiResponse = unknown;
export type SrsDeleteSectionUsingDeleteApiArg = {
    /** The unique ID of the section. */
    sectionId: number;
};
export type AddFieldToSectionApiResponse = unknown;
export type AddFieldToSectionApiArg = {
    /** The unique ID of the section. */
    sectionId: number;
    /** FieldRest JSON data used to create the field */
    fieldRest: FieldRest;
};
export type SrsUpdateFieldUsingPutApiResponse =
    /** status 200 Returns the newly updated field configuration as a <b>FieldRest</b> object in the response body. */ FieldRest;
export type SrsUpdateFieldUsingPutApiArg = {
    /** The unique ID of the field to be updated. */
    fieldId: number;
    /** The unique ID of the section. */
    sectionId: number;
    /** FieldRest JSON data used to update the field */
    fieldRest: FieldRest;
};
export type SrsDeleteFieldFromSectionUsingDeleteApiResponse = unknown;
export type SrsDeleteFieldFromSectionUsingDeleteApiArg = {
    /** The unique ID of the field to be deleted. */
    fieldId: number;
    /** The unique ID of the section. */
    sectionId: number;
};
export type PaginRestResultSectionRest = {
    maxPage?: number;
    records?: SectionRest[];
    response?: SectionRest;
    totalCount?: number;
};
export type SsrsGetVendorScoreSyncStatusUsingGetApiResponse = /** status 200 successful operation */ GenericCyberSecurityScoreResult;
export type SsrsGetVendorScoreSyncStatusUsingGetApiArg = {
    buId: number;
};
export type SsrsTestSscTokenUsingPostApiResponse = /** status 200 successful operation */ GenericCyberSecurityScoreResult;
export type SsrsTestSscTokenUsingPostApiArg = {
    body: string;
};
export type GenericCyberSecurityScoreResult = {
    message?: string;
    statusCode?: string;
    websiteDomain?: string;
};
export type SrsGetAllSettingsUsingGetApiResponse = /** status 200 Returns the settings for the category.  */ ProtechtDetailsRest[];
export type SrsGetAllSettingsUsingGetApiArg = {
    category: string;
};
export type SrsUpdateSettingUsingPatchApiResponse =
    /** status 200 The setting has been updated and the new setting properties are returned.  */ ProtechtDetailsRest;
export type SrsUpdateSettingUsingPatchApiArg = {
    /** The key of the setting to be updated. */
    key: string;
    /** Only the <i>value</i> property is required as part of the JSON blob, all other properties are ignored if they are included (can be excluded from the request). */
    protechtDetailsRest: ProtechtDetailsRest;
};
export type ProtechtDetailsRest = {
    category?: string;
    description?: string;
    editable?: boolean;
    enableToggle?: boolean;
    internal?: boolean;
    label?: string;
    name?: string;
    toggleValue?: boolean;
    type?: string;
    value?: string;
};
export type SrsCreateStyleUsingPostApiResponse =
    /** status 200 The new style configuration was created successfully. The created style model is returned. */ StyleRest;
export type SrsCreateStyleUsingPostApiArg = {
    /** The <b>StyleRest</b> JSON data used to create the style configuration. */
    styleRest: StyleRest;
};
export type StylesSearchApiResponse = /** status 200 The style configuration matching the unique ID provided is returned. */ StyleRest;
export type StylesSearchApiArg = {
    /** Indicates whether the style is applied to an individual register or all registers. <br><br>Available options:<ul>    <li>        <b>tablemetadata</b> - a specific register style    </li>    <li>        <b>globaldesign</b> - default style applied to all registers (unless overridden by a specific register style)    </li></ul> */
    linkTable: 'tablemetadata' | 'globaldesign';
    /** The unique register ID that the style is applied to. This value is not required when the <b>linkTable</b> parameter is set to <b>globaldesign</b>. */
    linkId?: number;
};
export type GetStyleApiResponse = /** status 200 The style configuration matching the unique ID provided is returned. */ StyleRest;
export type GetStyleApiArg = {
    /** The unique ID of the style configuration. */
    styleId: number;
};
export type SrsUpdateStyleUsingPutApiResponse =
    /** status 200 The style configuration was successfully updated and the updated version is returned. */ StyleRest;
export type SrsUpdateStyleUsingPutApiArg = {
    /** The unique ID of the style configuration to be updated. */
    styleId: number;
    /** The <b>StyleRest</b> JSON data used to update the style configuration. */
    styleRest: StyleRest;
};
export type SrsSuggestObjectNamesUsingGetApiResponse = /** status 200 Returns a list of suggestions. */ PaginRestResultSuggestionStubRest;
export type SrsSuggestObjectNamesUsingGetApiArg = {
    /** The table name that represents the register. For example: "table_xxxxxx". */
    tableName: string;
    /** The column name that represents the field in the register. The field must be a user, business unit, role, sign-off, state, or country field. For example: "col_xxxxxx". */
    columnName: string;
    /** The input that will be used to match to Protecht.ERM library data. For example, this could be the start of a user's name. */
    searchingText: string;
};
export type SuggestionStubRest = {
    /** Suggestion unique ID */
    id?: number;
    /** The suggested text that partially matches the searchingText provided */
    label?: string;
};
export type PaginRestResultSuggestionStubRest = {
    maxPage?: number;
    records?: SuggestionStubRest[];
    response?: SuggestionStubRest;
    totalCount?: number;
};
export type ScrsGetPropertiesUsingGetApiResponse = /** status 200 Returns a list of system configurations. */ {
    [key: string]: string;
};
export type ScrsGetPropertiesUsingGetApiArg = {
    /** The name of the property that is configured within Protecht.ERM. See the table above for the names of properties that can be queried using this operation. */
    key: string[];
};
export type ScrsGetTimezonesUsingGetApiResponse = /** status 200 Successfully fetched timezones. */ TimezonesRest;
export type ScrsGetTimezonesUsingGetApiArg = void;
export type TimezonesRest = {
    /** Available timezones */
    availableTimezones?: string[];
    /** System preferred timezones */
    systemTimezones?: string[];
};
export type TrsGetTagsUsingGetApiResponse = /** status 200 Returns a list of tags. */ TagRest[];
export type TrsGetTagsUsingGetApiArg = void;
export type TrsCreateTagUsingPostApiResponse = /** status 200 The new tag was created successfully. The created tag model is returned. */ TagRest;
export type TrsCreateTagUsingPostApiArg = {
    /** <b>TagRest </b> JSON data used to create the tag. For this operation to work, the JSON request object must have at least one <b>value</b> and one <b>type</b> property defined, where type.id is a valid tag category ID. */
    tagRest: TagRest;
};
export type TrsGetTagTypesUsingGetApiResponse = /** status 200 Successfully retrieved list of tag categories */ TagTypeRest[];
export type TrsGetTagTypesUsingGetApiArg = void;
export type TrsCreateTagTypeUsingPostApiResponse =
    /** status 200 The new tag category was created successfully. The created tag category model is returned. */ TagTypeRest;
export type TrsCreateTagTypeUsingPostApiArg = {
    /** <b>TagTypeRest  </b> JSON data used to create the tag category.For this operation to work, the JSON request object must have at least one <b>name</b> property defined. */
    tagTypeRest: TagTypeRest;
};
export type TrsGetTagTypesUsingPostApiResponse = /** status 200 The list of tag categories is returned. */ PaginRestResultTagTypeRest;
export type TrsGetTagTypesUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type TrsUpdateTagTypeUsingPutApiResponse = /** status 200 successful operation */ TagTypeRest;
export type TrsUpdateTagTypeUsingPutApiArg = {
    /** The unique ID of the tag category. */
    id: number;
    /** <b>TagTypeRest  </b> JSON data used to update the tag category. For this operation to work, the JSON request object must have at least one <b>name</b> property defined. */
    tagTypeRest: TagTypeRest;
};
export type TrsRemoveTagTypeUsingDeleteApiResponse = unknown;
export type TrsRemoveTagTypeUsingDeleteApiArg = {
    /** The unique ID of the tag category to be deleted. */
    id: number;
};
export type TrsGetTagsUsingPostApiResponse = /** status 200 Returns a list of tags in the response body. */ PaginRestResultTagRest;
export type TrsGetTagsUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type TrsSuggestTagsUsingPostApiResponse = /** status 200 Returns a list of tags in the response body. */ PaginRestResultTagRest;
export type TrsSuggestTagsUsingPostApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** Searching text */
    searchingText: string;
};
export type TrsGetTagTreeUsingGetApiResponse = /** status 200 The list of tags is returned in the response body. */ TagTreeItemRest[];
export type TrsGetTagTreeUsingGetApiArg = {
    /** The library module where the tags are applied (control, riskcause, riskevent, or keyriskindicator). */
    tagContext: string;
    /** A comma-separated list of tag category IDs that define the hierarchy order of the tag tree to return, for example: "id1,id2,id3".  */
    tagHierarchy?: string;
};
export type TrsUpdateTagUsingPutApiResponse = /** status 200 successful operation */ TagRest;
export type TrsUpdateTagUsingPutApiArg = {
    /** The unique ID of the tag to be updated. */
    id: number;
    /** <b>TagRest </b> JSON data used to update the tag. For this operation to work, the JSON request object must have at least one <b>value</b> and one <b>type</b> property defined, where type.id is a valid tag category ID. */
    tagRest: TagRest;
};
export type TrsRemoveTagUsingDeleteApiResponse = unknown;
export type TrsRemoveTagUsingDeleteApiArg = {
    /** The unique ID of the tag to be deleted */
    id: number;
};
export type PaginRestResultTagTypeRest = {
    maxPage?: number;
    records?: TagTypeRest[];
    response?: TagTypeRest;
    totalCount?: number;
};
export type PaginRestResultTagRest = {
    maxPage?: number;
    records?: TagRest[];
    response?: TagRest;
    totalCount?: number;
};
export type TagTreeItemRest = {
    children?: TagTreeItemRest[];
    /** The library module where the tag or tag category is applied */
    context?: string;
    /** Tag or tag category ID */
    id?: number;
    /** Tag or tag category name */
    name?: string;
    /** Whether the item is a tag or tag category */
    nodeType?: string;
    parent?: TagTreeItemRest;
    tagType?: TagTypeRest;
};
export type MtrsGetMyTasksUsingGetApiResponse = /** status 200 Returns list of My Tasks entries. */ MyTasksDataRest[];
export type MtrsGetMyTasksUsingGetApiArg = void;
export type MyTasksDataRest = {
    /** The unique ID of the application where the task is located */
    appId?: number;
    /** The number of current tasks that have been either completed or are due for this module or register */
    currentCount?: number;
    /** The number of open tasks that have a due date within 30 days */
    dueCount?: number;
    /** The number of current tasks that have been completed for this module or register */
    finishedCount?: number;
    /** Unique icon ID */
    iconId?: string;
    /** Display name of the register or module */
    name?: string;
    /** The total number of open tasks for this module or register */
    openedCount?: number;
    /** The number of open tasks that have a past-due date */
    overdueCount?: number;
    /** The table name that represents the register where the task is located. This is relevant only for register or actions tasks. */
    tableName?: string;
    /** The type of module that the task is from */
    type?: string;
};
export type PursGetUsersUsingGetApiResponse = unknown;
export type PursGetUsersUsingGetApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** The unique ID of the applied permission filter. */
    permissionFilterId?: number;
    /** The unique ID of the applied role filter. */
    roleFilterId?: number;
};
export type PursCreateUserUsingPostApiResponse = /** status 200 successful operation */ ProtechtUserRest;
export type PursCreateUserUsingPostApiArg = {
    /** <b>ProtechtUserRest</b> JSON data used to create the user. */
    protechtUserRest: ProtechtUserRest;
};
export type PursGetCurrentUserUsingGetApiResponse = /** status 200 The user data is returned in the response body. */ ProtechtUserRest;
export type PursGetCurrentUserUsingGetApiArg = void;
export type PursUpdateCurrentUserUsingPutApiResponse = /** status 200 The updated user data is returned in the response body. */ ProtechtUserRest;
export type PursUpdateCurrentUserUsingPutApiArg = {
    /** Use this parameter to update also Additional Business Units which are ignored by default. */
    includeBUs?: boolean;
    /** <b>ProtechtUserRest</b> JSON data used to update the user. */
    protechtUserRest: ProtechtUserRest;
};
export type PursGetUserPermissionsUsingGetApiResponse = /** status 200 The list of permissions is returned. */ PermissionRest[];
export type PursGetUserPermissionsUsingGetApiArg = {
    /** A list of permission codes used to filter the response. If the permission codes provided do not appear in the response body, the user does not have that permission.</br>A permission code is expressed as: [APPLICATION].[NOUN].[VERB]</br>e.g. PROFILE.PASSWORD.CHANGE */
    filter?: string[];
};
export type GetUsersApiResponse = /** status 200 The list of users is returned. */ PaginRestResultProtechtUserRest;
export type GetUsersApiArg = {
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** The unique ID of the applied permission filter. */
    permissionFilterId?: number;
    /** The unique ID of the applied role filter. */
    roleFilterId?: number;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    userFilterContext: UserFilterContext;
};
export type PursGetUserUsingGetApiResponse = /** status 200 The user data is returned in the response body. */ ProtechtUserRest;
export type PursGetUserUsingGetApiArg = {
    /** The unique ID of the user. */
    userId: number;
};
export type PursUpdateUserUsingPutApiResponse = /** status 200 The updated user data is returned in the response body. */ ProtechtUserRest;
export type PursUpdateUserUsingPutApiArg = {
    /** The unique ID of the user to be updated. */
    userId: number;
    /** Use this parameter to update also Additional Business Units which are ignored by default. */
    includeBUs?: boolean;
    /** <b>ProtechtUserRest</b> JSON data used to update the user. */
    protechtUserRest: ProtechtUserRest;
};
export type PursDeleteUserUsingDeleteApiResponse = unknown;
export type PursDeleteUserUsingDeleteApiArg = {
    /** The unique ID of the user to be deleted. */
    userId: number;
};
export type PursGetUserHistoryUsingPostApiResponse =
    /** status 200 The list of historical versions for the user record is returned. */ PaginRestResultProtechtUserRest;
export type PursGetUserHistoryUsingPostApiArg = {
    /** The unique ID of the user record for which historical versions will be returned. */
    userId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** An expression that filters the data set by relating a property and value through using standard comparison operators (=, <>, in). */
    body: ViewExpressionRest[];
};
export type PursLockUserUsingPutApiResponse = unknown;
export type PursLockUserUsingPutApiArg = {
    /** The unique ID of the user to be locked. */
    userId: number;
};
export type PursDeleteManagerFromUserUsingDeleteApiResponse = unknown;
export type PursDeleteManagerFromUserUsingDeleteApiArg = {
    /** The unique ID of the user. */
    userId: number;
};
export type PursGetUserPermissionsUsingGet1ApiResponse = /** status 200 The list of permissions is returned. */ PermissionRest[];
export type PursGetUserPermissionsUsingGet1ApiArg = {
    /** The unique ID for the user. */
    userId: number;
    /** A list of permission codes used to filter the response. If the permission codes provided do not appear in the response body, the user does not have that permission.</br>A permission code is expressed as: [APPLICATION].[NOUN].[VERB]</br>e.g. PROFILE.PASSWORD.CHANGE */
    filter?: string[];
};
export type PursRestoreUserUsingPostApiResponse =
    /** status 200 User has been successfully restored. The restored user data is returned in the response body. */ ProtechtUserRest;
export type PursRestoreUserUsingPostApiArg = {
    /** The unique ID of the user to restore. */
    userId: number;
};
export type PursAddRoleToUserUsingPutApiResponse = unknown;
export type PursAddRoleToUserUsingPutApiArg = {
    /** The unique ID of the user. */
    userId: number;
    /** The unique ID of the role to be assigned. */
    roleId: number;
};
export type PursDeleteRoleFromUserUsingDeleteApiResponse = unknown;
export type PursDeleteRoleFromUserUsingDeleteApiArg = {
    /** The unique ID of the user. */
    userId: number;
    /** The unique ID of the role to be deleted from the user record. */
    roleId: number;
};
export type PursAddBuToUserUsingPutApiResponse = unknown;
export type PursAddBuToUserUsingPutApiArg = {
    /** The unique ID of the user. */
    userId: number;
    /** The unique ID of the business unit to be added. */
    unitId: number;
    /** An optional list of business unit unique IDs to be added to the user in a batch. */
    body: number[];
};
export type PursDeleteBuFromUserUsingDeleteApiResponse = unknown;
export type PursDeleteBuFromUserUsingDeleteApiArg = {
    /** The unique ID of the user. */
    userId: number;
    /** The unique ID of the business unit to be deleted from the user record. */
    unitId: number;
    /** An optional list of business unit unique IDs to be deleted from the user in a batch. */
    body: number[];
};
export type PursUnlockUserUsingPutApiResponse = unknown;
export type PursUnlockUserUsingPutApiArg = {
    /** The unique ID of the user to be unlocked. */
    userId: number;
};
export type PaginRestResultProtechtUserRest = {
    maxPage?: number;
    records?: ProtechtUserRest[];
    response?: ProtechtUserRest;
    totalCount?: number;
};
export type UserFilterContext = {
    businessUnitIds?: number[];
    /** An expression that filters the data set by relating a property and value through using standard comparison operators */
    expressions?: ViewExpressionRest[];
    /** Entry ids for filter */
    ids?: number[];
    roleIds?: number[];
};
export type VmrsGetRepositoryHierarchyUsingGetApiResponse = /** status 200 successful operation */ RepositoryRest[];
export type VmrsGetRepositoryHierarchyUsingGetApiArg = void;
export type VmrsGetDefinitionUsingGetApiResponse = /** status 200 successful operation */ VendorManagementDefinition;
export type VmrsGetDefinitionUsingGetApiArg = {
    extended?: boolean;
};
export type VmrsUpdateDiagramDefinitionUsingPutApiResponse = unknown;
export type VmrsUpdateDiagramDefinitionUsingPutApiArg = {
    vendorManagementDefinition: VendorManagementDefinition;
};
export type VmrsGetVendorRegisterPermissionsUsingGetApiResponse = unknown;
export type VmrsGetVendorRegisterPermissionsUsingGetApiArg = {
    /** The unique ID of the register. */
    regId: number;
};
export type VmrsUpdateVendorRegisterPermissionsUsingPutApiResponse = unknown;
export type VmrsUpdateVendorRegisterPermissionsUsingPutApiArg = {
    /** The unique ID of the register. */
    regId: number;
    vendorManagementRegisterPermissions: VendorManagementRegisterPermissions;
};
export type VrsCreateVendorEntryUsingPostApiResponse = /** status 200 successful operation */ RegisterDataRest;
export type VrsCreateVendorEntryUsingPostApiArg = {
    /** <b>String</b> The Vendor name to create. */
    body: string;
};
export type VrsGetVendorEntryUsingGetApiResponse = /** status 200 successful operation */ RegisterDataRest;
export type VrsGetVendorEntryUsingGetApiArg = {
    /** <b>String</b> The Vendor name to check. */
    vendorId: number;
    /** <b>String</b> The Vendor register table name. */
    tableName?: string;
};
export type VrsUpdateVendorNameUsingPutApiResponse = unknown;
export type VrsUpdateVendorNameUsingPutApiArg = {
    /** The vendorIdName object contains id of vendor (Business Unit ID) and new vendor name. */
    idWithNameRest: IdWithNameRest;
};
export type VrsCreateRegisterEntryUsingPostApiResponse = /** status 200 successful operation */ RegisterDataRest;
export type VrsCreateRegisterEntryUsingPostApiArg = {
    /** This function expects 2 json properties [tableName, vendorId]. Providing anything else will fail. */
    body: {
        [key: string]: string;
    };
};
export type VrsCheckUniqueVendorUsingPostApiResponse = /** status 200 The vendor is unique. */ IdWithNameRest;
export type VrsCheckUniqueVendorUsingPostApiArg = {
    /** <b>String</b> The Vendor name to check. */
    body: string;
};
export type VursCreateVendorUserUsingPostApiResponse = /** status 200 successful operation */ ProtechtUserRest;
export type VursCreateVendorUserUsingPostApiArg = {
    /** <b>ProtechtUserRest</b> JSON data used to create the vendor user. */
    protechtUserRest: ProtechtUserRest;
};
export type VursUpdateLimitedCurrentUserPropertiesUsingPutApiResponse = /** status 200 The provided properties are updated successfully. */ ProtechtUserRest;
export type VursUpdateLimitedCurrentUserPropertiesUsingPutApiArg = {
    /** The limited set of user properties to set. */
    limitedProtechtUserRest: LimitedProtechtUserRest;
};
export type VursUpdateCurrentUserPasswordUsingPutApiResponse = unknown;
export type VursUpdateCurrentUserPasswordUsingPutApiArg = {
    /** The existing password and new password. */
    oldAndNewPasswordsRest: OldAndNewPasswordsRest;
};
export type VursSearchUsersUsingPostApiResponse = /** status 200 The list of Vendor users is returned. */ PaginRestResultProtechtUserRest;
export type VursSearchUsersUsingPostApiArg = {
    /** Vendor ID to get the users for. */
    vendorId: number;
    /** The number of records to skip before returning the first record. */
    offset?: number;
    /** The number of records returned in the payload. */
    limit?: number;
    /** Name of the field used to sort the list. */
    orderBy?: string;
    /** Sorting order (ascending or descending). */
    orderType?: 'asc' | 'desc';
    /** Name of the field used to group the list. */
    groupBy?: string;
    /** The unique ID of the applied view. */
    viewId?: number;
    /** The unique ID of the applied permission filter. */
    permissionFilterId?: number;
    /** The unique ID of the applied role filter. */
    roleFilterId?: number;
    /** Wrapper for a list of IDs and a <b>ViewExpressionRest</b> JSON data object that filters the data using property and value pairs with comparison operators (=, <>, in) */
    filterContextRest: FilterContextRest;
};
export type VursGetUserUsingGetApiResponse = /** status 200 The user data is returned in the response body. */ ProtechtUserRest;
export type VursGetUserUsingGetApiArg = {
    /** The unique ID of the user. */
    userId: number;
};
export type VursUpdateUserUsingPutApiResponse = /** status 200 The updated vendor user data is returned in the response body. */ ProtechtUserRest;
export type VursUpdateUserUsingPutApiArg = {
    /** The unique ID of the user to be updated. */
    userId: number;
    /** <b>ProtechtUserRest</b> JSON data used to update the user. */
    protechtUserRest: ProtechtUserRest;
};
export type VursDeleteUserUsingDeleteApiResponse = unknown;
export type VursDeleteUserUsingDeleteApiArg = {
    /** The unique ID of the user to be deleted. */
    userId: number;
};
export type VursLockUserUsingPutApiResponse = unknown;
export type VursLockUserUsingPutApiArg = {
    /** The unique ID of the user to be locked. */
    userId: number;
};
export type VursUnlockUserUsingPutApiResponse = unknown;
export type VursUnlockUserUsingPutApiArg = {
    /** The unique ID of the user to be unlocked. */
    userId: number;
};
export type RepositoryRest = {
    label?: string;
    path?: string;
    type?: string;
};
export type VendorManagementRegister = {
    applicationId?: number;
    contractName?: string;
    contractType?: number;
    displayColumn?: string;
    name?: string;
    primary?: boolean;
    registerForVendor?: boolean;
    registerId?: number;
    registerTable?: string;
};
export type VendorManagementDefinition = {
    actionsRegister?: VendorManagementRegister;
    contractsRegister?: VendorManagementRegister;
    cyberSecurityScoreRegister?: VendorManagementRegister;
    documentsRegister?: VendorManagementRegister;
    findingAndIssuesRegister?: VendorManagementRegister;
    incidentsRegister?: VendorManagementRegister;
    internalQuestionnaireMetadataRegister?: VendorManagementRegister;
    monitoringReviewsRegister?: VendorManagementRegister;
    productRegister?: VendorManagementRegister;
    riskAssessmentsRegister?: VendorManagementRegister;
    vendorActionsRegister?: VendorManagementRegister;
    vendorQuestionnaireMetadataRegister?: VendorManagementRegister;
    vendorRegister?: VendorManagementRegister;
};
export type SectionPermission = {
    hidden?: boolean;
    permEdit?: boolean;
    permView?: boolean;
    sectionId?: number;
    sectionName?: string;
};
export type TransitionPermission = {
    enabled?: boolean;
    stateFrom?: string;
    stateTo?: string;
    transitionId?: number;
    transitionName?: string;
};
export type VendorManagementRegisterPermissions = {
    permEntriesAdd?: boolean;
    permEntriesEdit?: boolean;
    permEntriesView?: boolean;
    registerLabel?: string;
    registerName?: string;
    sectionsPermissions?: SectionPermission[];
    transitionsPermissions?: TransitionPermission[];
};
export type LimitedProtechtUserRest = {
    /** The user's email address */
    email?: string;
    /** Unique entity ID */
    id?: number;
    /** The username used for logging in to Protecht.ERM */
    loginId?: string;
    /** Mobile phone number */
    mobile?: string;
    /** Entity name */
    name?: string;
    /** Position title */
    position?: string;
};
export type OldAndNewPasswordsRest = {
    newPassword?: string;
    oldPassword?: string;
};
export type VrsGetCurrentUserViewsUsingGetApiResponse = /** status 200 The list of views is returned. */ ViewRestResponse;
export type VrsGetCurrentUserViewsUsingGetApiArg = {
    /** The context (screen, library, or register) where a view applies. For a register, use the table name, for example: "table_xxxxxx". */
    viewContext?: string;
    /** If true, all global views are returned in the response body. If false, only the private views defined by this user are returned. */
    global?: boolean;
};
export type VrsCreateViewUsingPostApiResponse = /** status 200 Returns the newly created view model in the response body. */ ViewRest;
export type VrsCreateViewUsingPostApiArg = {
    /** ViewRest JSON data used to create the view. */
    viewRest: ViewRest;
};
export type VrsGetExpressionContextUsingGetApiResponse = /** status 200 Returns a list of available view expressions. */ {
    [key: string]: string[];
};
export type VrsGetExpressionContextUsingGetApiArg = void;
export type VrsGetViewUsingGetApiResponse = /** status 200 The view matching the unique ID provided is returned. */ ViewRest;
export type VrsGetViewUsingGetApiArg = {
    /** The unique ID of the view. */
    viewId: number;
};
export type VrsUpdateViewUsingPutApiResponse = /** status 200 Returns the updated view model in the response body. */ ViewRest;
export type VrsUpdateViewUsingPutApiArg = {
    /** The unique ID of the view to be updated. */
    viewId: number;
    /** ViewRest JSON data used to update the view. */
    viewRest: ViewRest;
};
export type VrsDeleteViewUsingDeleteApiResponse = unknown;
export type VrsDeleteViewUsingDeleteApiArg = {
    /** The unique ID of the view to be deleted. */
    viewId: number;
};
export type ViewRest = {
    /** The library, register, or other screen where the view applies */
    context?: string;
    /** The column name of the field that is used by default in the search filter */
    defaultFilter?: string;
    /** If true, the sorting order is ascending. Otherwise if false, the sorting order is descending. */
    direction?: boolean;
    /** A list of conditional filter expressions that are applied to the view */
    expressions?: ViewExpressionRest[];
    /** A comma separated list of column names for the fields that are displayed in this view. The order of the items reflects the order of the fields shown in the view. */
    fields?: string;
    /** The column name of the field used to group the entries */
    grouping?: string;
    /** View unique ID */
    id?: number;
    /** If true, info bubble is shown for selected record. */
    infoView?: boolean;
    /** If true, the view is the default view for the screen where it applies. There can only be one default view defined for a screen. */
    isDefault?: boolean;
    /** The display name of the view */
    name?: string;
    /** The column name of the field that is used for ordering the filtered entries in the view */
    ordering?: string;
    /** If scope is null, the view is global and is available for use by all users. If scope value is a unique user ID, the view is private and belongs to that user. */
    scope?: number;
    /** View unique ID - this ID is system generated and unique for every view defined in the system */
    uuid?: string;
    xmlProperties?: string;
};
export type ViewRestResponse = {
    /** If true, the user has sufficient permission to manage the views */
    hasManagePermission?: boolean;
    /** The list of register views defined by the user */
    views?: ViewRest[];
};
export type WfrsCreateWorkFlowRuleUsingPostApiResponse = /** status 200 The newly created workflow rule is returned in the response body. */ WorkFlowRuleRest;
export type WfrsCreateWorkFlowRuleUsingPostApiArg = {
    /** <b>WorkFlowRuleRest</b> JSON data used to create the workflow rule. */
    workFlowRuleRest: WorkFlowRuleRest;
};
export type WfrsGetWorkFlowRuleUsingGetApiResponse = /** status 200 The workflow rule matching the unique ID provided is returned. */ WorkFlowRuleRest;
export type WfrsGetWorkFlowRuleUsingGetApiArg = {
    /** The unique ID of the workflow rule. */
    workflowId: number;
};
export type WfrsUpdateWorkFlowRuleUsingPutApiResponse = /** status 200 The updated workflow rule data is returned in the response body. */ WorkFlowRuleRest;
export type WfrsUpdateWorkFlowRuleUsingPutApiArg = {
    /** The unique ID of the workflow rule to be updated. */
    workflowId: number;
    /** <b>WorkFlowRuleRest</b> JSON data used to update the workflow rule. */
    workFlowRuleRest: WorkFlowRuleRest;
};
export type WfrsDeleteWorkFlowRuleUsingDeleteApiResponse = unknown;
export type WfrsDeleteWorkFlowRuleUsingDeleteApiArg = {
    /** The unique ID of the workflow to be deleted */
    workflowId: number;
};
export type WlrsvGetWorkLogUsingGetApiResponse = /** status 200 Returns a list of worklogs. */ WorkLogRest[];
export type WlrsvGetWorkLogUsingGetApiArg = {
    /** The table name that represents the register. For example: "table_xxxxxx". */
    tableName: string;
    /** The column name that represents the worklog field. For example: "col_xxxxxx". */
    columnName: string;
    /** The unique ID of the register entry. */
    entryId: string;
};
export type WorkLogRest = {
    /** A flag that, when false, indicates that a worklog is part of a historical record */
    active?: boolean;
    /** The creation date of the worklog entry */
    createDate?: string;
    /** Worklog unique ID */
    id?: number;
    /** The text content of the worklog */
    log?: string;
    /** The column name that represents the worklog field. For example: "col_xxxxxx". */
    sourceColumnName?: string;
    /** The unique ID of the register entry */
    sourceId?: string;
    /** The table name that represents the register. For example: "table_xxxxxx". */
    sourceTableName?: string;
    /** The unique ID of the user who created the worklog entry */
    userId?: number;
    /** The username of the user who created the worklog entry */
    userName?: string;
};
export type WrsGetRegisterColumnsByTypesUsingGetApiResponse = /** status 200 The all register columns matching given column types */ ColumnRest[];
export type WrsGetRegisterColumnsByTypesUsingGetApiArg = {
    /** The unique ID of the register. */
    registerId: number;
    /** Column types */
    columnTypes: (
        | 'ACTIONS'
        | 'WORKLOG'
        | 'STATETRANSITION'
        | 'PRIORITY'
        | 'MULTILINE_TEXT'
        | 'SINGLELINE_TEXT'
        | 'STATIC_TEXT'
        | 'PASSWORD'
        | 'BLOB'
        | 'INTEGER'
        | 'REAL'
        | 'NUMERIC'
        | 'DOUBLE'
        | 'TIMESTAMP'
        | 'DATE'
        | 'NULL'
        | 'BUSINESS_UNIT'
        | 'EMAIL'
        | 'QUESTION'
        | 'CONTROL'
        | 'RISK_EVENT'
        | 'RISK_CAUSE'
        | 'GROUP'
        | 'USER'
        | 'STATE'
        | 'COUNTRY'
        | 'ATTACHMENT'
        | 'LINK'
        | 'LIST'
        | 'MULTISELECT_LIST'
        | 'CURRENCY'
        | 'OBJREF'
        | 'BOOLEAN'
        | 'SIMPLE_FORMULA'
        | 'COMPLEX_FORMULA'
        | 'MULTISELECT_LIBRARY'
        | 'TABLE'
        | 'SCALE_LIKELIHOOD'
        | 'SCALE_CONSEQUENCE'
        | 'TAGS'
        | 'SLIDER'
        | 'DUE_DATE'
        | 'REGISTER_STATE'
        | 'LINKED_TO'
        | 'IMAGES'
        | 'REPORT_ATTACHMENT'
        | 'RICH_TEXT'
        | 'DATETIME_FORMULA'
        | 'CONTROL_ATTESTATION'
        | 'STRING_FORMULA'
        | 'WEB_SERVICE_TABLE'
        | 'SIGN_OFF'
        | 'USER_INFO'
        | 'REGISTER_ENTRY_LINK'
        | 'USER_AGGREGATION'
        | 'SCHEDULE'
        | 'REGISTER_DESIGNER'
        | 'RISK_MATRIX'
        | 'RISK_RATING'
        | 'REGISTER_LAYOUT'
        | 'SPACER'
        | 'ROLE'
        | 'HYPERLINK'
        | 'HINT'
        | 'GPS_POSITION'
        | 'TIMESTAMP_WITH_TIMEZONE'
        | 'CUSTOM_ID'
        | 'CENTRAL_LIBRARY'
        | 'BIDIRECTIONAL_MULTISELECT'
        | 'FRAMEWORK_LINKS'
        | 'V_AUDIT_QUESTION'
        | 'V_BOWTIE'
        | 'V_KRI'
    )[];
};
export type WrsUpdateConfigUsingPutApiResponse = /** status 200 Returns the workspace configuration in the response body. */ WorkspaceRest;
export type WrsUpdateConfigUsingPutApiArg = {
    /** <b>WorkspaceConfig</b> JSON data used to update the workspace configuration. */
    workspaceConfig: WorkspaceConfig;
};
export type WrsGetFullConfigForEditUsingGetApiResponse =
    /** status 200 The workspace configuration matching the unique workspace module returned. */ WorkspaceConfig;
export type WrsGetFullConfigForEditUsingGetApiArg = {
    /** The unique workspace module. */
    workspaceModule: 'CYBER_RISK' | 'VRM';
};
export type WrsDeleteConfigUsingDeleteApiResponse = /** status 200 Returns the workspace menu configuration in the response body. */ boolean;
export type WrsDeleteConfigUsingDeleteApiArg = {
    /** <b>WorkspaceRest</b> Id of the workspace to be deleted. */
    workspaceId: number;
};
export type WrsGetUserFilteredWorkspaceUsingGetApiResponse = /** status 200 The workspace matching the unique workspace module is returned. */ WorkspaceRest;
export type WrsGetUserFilteredWorkspaceUsingGetApiArg = {
    /** The unique workspace module. */
    workspaceModule: 'CYBER_RISK' | 'VRM';
};
export type WrsGetRepositoryHierarchyUsingGetApiResponse = /** status 200 The dashboards matching the workspace module and parent paths. */ RepositoryRest[];
export type WrsGetRepositoryHierarchyUsingGetApiArg = {
    /** The unique ID of the workspace. */
    workspaceModule: 'CYBER_RISK' | 'VRM';
    /** Parent paths */
    parentPaths: string[];
};
export type WrsGetDisplayConfigurationUsingGetApiResponse = /** status 200 successful operation */ MetricDisplayConfig;
export type WrsGetDisplayConfigurationUsingGetApiArg = {
    /** The unique workspace module. */
    workspaceModule: 'CYBER_RISK' | 'VRM';
};
export type WrsSetDisplayConfigurationUsingPostApiResponse = unknown;
export type WrsSetDisplayConfigurationUsingPostApiArg = {
    /** The unique workspace module. */
    workspaceModule: 'CYBER_RISK' | 'VRM';
    metricDisplayConfig: MetricDisplayConfig;
};
export type WrsFindByGroupItemUuidUsingGetApiResponse = /** status 200 successful operation */ MetricRestResponse[];
export type WrsFindByGroupItemUuidUsingGetApiArg = {
    /** The unique workspace module. */
    workspaceModule: 'CYBER_RISK' | 'VRM';
    /** Item uuid. */
    itemUuid: string;
    eval?: boolean;
    computeStyle?: boolean;
};
export type WrsGetRegistersStatsUsingGetApiResponse = /** status 200 Returns a list of register statistics in the response body. */ RegisterStatsDto[];
export type WrsGetRegistersStatsUsingGetApiArg = {
    /** The unique workspace module. */
    workspaceModule: 'CYBER_RISK' | 'VRM';
    /** The unique ID of an Business Unit. Filter entries by given business unit. */
    businessUnitFilter?: number;
};
export type ColumnRest = {
    /** Unique column ID */
    id?: number;
    /** Entity name */
    name?: string;
    /** Section name */
    sectionName?: string;
};
export type WorkspaceGroupItemBase = {
    alias?: string;
    aliasRole?: 'NO_ONE' | 'INTERNAL' | 'EVERYONE';
    id?: string;
    itemType?: 'RegisterItem' | 'DashboardItem' | 'FrameworkItem';
    name?: string;
    order: number;
    presetId?: number;
};
export type DashboardItem = {
    itemType: 'DashboardItem';
} & WorkspaceGroupItemBase & {
        path?: string;
    };
export type FrameworkItem = {
    itemType: 'FrameworkItem';
} & WorkspaceGroupItemBase & {
        frameworkId?: number;
    };
export type RegisterItemContractType = {
    category?: 'RESILIENCE' | 'VRM' | 'FRAMEWORK' | 'CENTRAL_LIBRARIES' | 'BOWTIE' | 'COPILOT' | 'CYBER_RISK';
    code?: number;
    label?: string;
    name?: string;
};
export type RegisterItem = {
    itemType: 'RegisterItem';
} & WorkspaceGroupItemBase & {
        appId?: number;
        contractType?: RegisterItemContractType;
        description?: string;
        hideMetrics?: boolean;
        registerId?: number;
        selectedDateColumn?: number;
        tableName?: string;
        viewId?: number;
    };
export type WorkspaceGroupItem = DashboardItem | FrameworkItem | RegisterItem;
export type WorkspaceGroup = {
    contentRole?: 'NO_ONE' | 'INTERNAL' | 'EVERYONE';
    fixedLevel?: number;
    id?: string;
    itemList?: WorkspaceGroupItem[];
    itemTypesRestriction?: ('RegisterItem' | 'DashboardItem' | 'FrameworkItem')[];
    labelRole?: 'NO_ONE' | 'INTERNAL' | 'EVERYONE';
    name?: string;
    order?: number;
};
export type WorkspaceConfig = {
    contentRole?: 'NO_ONE' | 'INTERNAL' | 'EVERYONE';
    fixedLevel?: number;
    groups?: WorkspaceGroup[];
    id?: string;
};
export type WorkspaceRest = {
    config?: WorkspaceConfig;
    /** Unique entity ID */
    id?: number;
    /** Entity name */
    name?: string;
    type?: 'CYBER_RISK' | 'VRM';
};

export type ConfigMenuItemBase = {
    itemType?: 'REGISTER' | 'DASHBOARD' | 'FRAMEWORK';
};

export type DashboardMenuItem = ConfigMenuItemBase & {
    id?: number;
    alias?: string;
    order?: number;
    name?: string;
    path?: string;
};

export type FrameworkMenuItem = ConfigMenuItemBase & {
    id?: number;
    tableName?: string;
    name?: string;
    description?: string;
    alias?: string;
    order?: number;
    appId?: number;
    viewId?: number;
};

export type RegisterMenuItem = ConfigMenuItemBase & {
    id?: number;
    tableName?: string;
    name?: string;
    description?: string;
    alias?: string;
    order?: number;
    appId?: number;
    viewId?: number;
};

export type ConfigMenuItem = DashboardMenuItem & FrameworkMenuItem & RegisterMenuItem;

export type BowTieDiagramRest = {
    id?: number;
    name?: string;
    description?: string;
    status?: string;
    createdBy?: string;
    lastModifiedBy?: string;
    createDate?: string;
    lastModifiedDate?: string;
    createDateFormatted?: string;
    lastModifiedDateFormatted?: string;
    centralRiskName?: string;
    centralRiskId?: number;
    diagramModel?: JsonNode;
    libraryLinkNotifications?: BowTieLinkNotificationRest[];
    tags?: IdWithNameRest[];
    businessUnits?: IdWithNameRest[];
    libraryLinkEnabled?: boolean;
    showLegendEnabled?: boolean;
    showControlsEnabled?: boolean;
    locked?: boolean;
};
