import { ErsSynchronizeEntriesUsingPostApiResponse, ErsSynchronizeEntriesUsingPostApiArg, Failure, RegisterRowFailure, RegisterSynchronizeResponse, SynchronizationContext, RegisterSynchronizeRequest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        ersSynchronizeEntriesUsingPost: build.mutation<ErsSynchronizeEntriesUsingPostApiResponse, ErsSynchronizeEntriesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/external/entries/synchronize`,
                method: 'POST',
                body: queryArg.registerSynchronizeRequest,
                params: {
                    registerActionId: queryArg.registerActionId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
