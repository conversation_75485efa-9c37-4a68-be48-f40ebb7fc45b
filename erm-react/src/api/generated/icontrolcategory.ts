import { CcciCreateCategoryUsingPostApiResponse, CcciCreateCategoryUsingPostApiArg, CcciGetTreeUsingGetApiResponse, CcciGetTreeUsingGetApiArg, CcciGetCategoryUsingGetApiResponse, CcciGetCategoryUsingGetApiArg, CcciUpdateCategoryUsingPutApiResponse, CcciUpdateCategoryUsingPutApiArg, CcciDeleteCategoryUsingDeleteApiResponse, CcciDeleteCategoryUsingDeleteApiArg, CcciGetChildrenUsingGetApiResponse, CcciGetChildrenUsingGetApiArg, ControlCategoryRest, ControlRest, ControlRestRead, ControlCategoryRestRead, ControlCategoryTreeNodeRest, ControlCategoryTreeNodeRestRead } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        ccciCreateCategoryUsingPost: build.mutation<CcciCreateCategoryUsingPostApiResponse, CcciCreateCategoryUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/icontrolcategory`, method: 'POST', body: queryArg.controlCategoryRest }),
        }),
        ccciGetTreeUsingGet: build.query<CcciGetTreeUsingGetApiResponse, CcciGetTreeUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/icontrolcategory/tree`,
                params: {
                    id: queryArg.id,
                    depth: queryArg.depth,
                    includeControls: queryArg.includeControls,
                    includeQuestions: queryArg.includeQuestions,
                },
            }),
        }),
        ccciGetCategoryUsingGet: build.query<CcciGetCategoryUsingGetApiResponse, CcciGetCategoryUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/icontrolcategory/${queryArg.id}`,
                params: {
                    includeControls: queryArg.includeControls,
                    includeQuestions: queryArg.includeQuestions,
                },
            }),
        }),
        ccciUpdateCategoryUsingPut: build.mutation<CcciUpdateCategoryUsingPutApiResponse, CcciUpdateCategoryUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/icontrolcategory/${queryArg.id}`, method: 'PUT', body: queryArg.controlCategoryRest }),
        }),
        ccciDeleteCategoryUsingDelete: build.mutation<CcciDeleteCategoryUsingDeleteApiResponse, CcciDeleteCategoryUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/icontrolcategory/${queryArg.id}`,
                method: 'DELETE',
                params: {
                    force: queryArg.force,
                },
            }),
        }),
        ccciGetChildrenUsingGet: build.query<CcciGetChildrenUsingGetApiResponse, CcciGetChildrenUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/icontrolcategory/${queryArg.id}/children`,
                params: {
                    includeControls: queryArg.includeControls,
                    includeQuestions: queryArg.includeQuestions,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
