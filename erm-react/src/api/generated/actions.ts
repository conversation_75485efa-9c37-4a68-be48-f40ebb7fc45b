import { ArsCreateActionLinkUsingPostApiResponse, ArsCreateActionLinkUsingPostApiArg, ArsDeleteActionLinkUsingDeleteApiResponse, ArsDeleteActionLinkUsingDeleteApiArg, ActionLinkModel, ActionLinkRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        arsCreateActionLinkUsingPost: build.mutation<ArsCreateActionLinkUsingPostApiResponse, ArsCreateActionLinkUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/actions/link`, method: 'POST', body: queryArg.actionLinkRest }),
        }),
        arsDeleteActionLinkUsingDelete: build.mutation<ArsDeleteActionLinkUsingDeleteApiResponse, ArsDeleteActionLinkUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/actions/link/${queryArg.actionLinkId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
