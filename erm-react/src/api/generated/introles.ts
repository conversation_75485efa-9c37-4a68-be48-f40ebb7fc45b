import { GetRolesApiResponse, GetRolesApiArg, RrsiGetRolesUsingPostApiResponse, RrsiGetRolesUsingPostApiArg, RoleRestInternal, PaginRestResultRoleRestInternal, ViewExpressionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        getRoles: build.query<GetRolesApiResponse, GetRolesApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/introles`,
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    viewId: queryArg.viewId,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    name: queryArg.name,
                },
            }),
        }),
        rrsiGetRolesUsingPost: build.query<RrsiGetRolesUsingPostApiResponse, RrsiGetRolesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/introles/search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
