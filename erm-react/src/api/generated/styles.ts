import { SrsCreateStyleUsingPostApiResponse, SrsCreateStyleUsingPostApiArg, StylesSearchApiResponse, StylesSearchApiArg, GetStyleApiResponse, GetStyleApiArg, SrsUpdateStyleUsingPutApiResponse, SrsUpdateStyleUsingPutApiArg, StyleRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        srsCreateStyleUsingPost: build.mutation<SrsCreateStyleUsingPostApiResponse, SrsCreateStyleUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/styles`, method: 'POST', body: queryArg.styleRest }),
        }),
        stylesSearch: build.query<StylesSearchApiResponse, StylesSearchApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/styles/${queryArg.linkTable}/search`,
                params: {
                    linkId: queryArg.linkId,
                },
            }),
        }),
        getStyle: build.query<GetStyleApiResponse, GetStyleApiArg>({
            query: (queryArg) => ({ url: `/v1/api/styles/${queryArg.styleId}` }),
        }),
        srsUpdateStyleUsingPut: build.mutation<SrsUpdateStyleUsingPutApiResponse, SrsUpdateStyleUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/styles/${queryArg.styleId}`, method: 'PUT', body: queryArg.styleRest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
