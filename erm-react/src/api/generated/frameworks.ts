import { FcGetFrameworksUsingGetApiResponse, FcGetFrameworksUsingGetApiArg, FcCreateFrameworkUsingPostApiResponse, FcCreateFrameworkUsingPostApiArg, FcGetFrameworkCategoriesUsingGetApiResponse, FcGetFrameworkCategoriesUsingGetApiArg, FcExportDataRegisterUsingGetApiResponse, FcExportDataRegisterUsingGetApiArg, FcImportDataRegisterUsingPostApiResponse, FcImportDataRegisterUsingPostApiArg, FcImportVerifyDataRegisterUsingPostApiResponse, FcImportVerifyDataRegisterUsingPostApiArg, FcGetFrameworkStatusUsingGetApiResponse, FcGetFrameworkStatusUsingGetApiArg, FcImportFrameworkUsingPostApiResponse, FcImportFrameworkUsingPostApiArg, FcGetFrameworkListUsingGetApiResponse, FcGetFrameworkListUsingGetApiArg, FcGetMetadataRegistersListUsingGetApiResponse, FcGetMetadataRegistersListUsingGetApiArg, FcCreateNodeUsingPostApiResponse, FcCreateNodeUsingPostApiArg, FcGetNodeUsingGetApiResponse, FcGetNodeUsingGetApiArg, FcUpdateNodeUsingPutApiResponse, FcUpdateNodeUsingPutApiArg, FcDeleteNodeUsingDeleteApiResponse, FcDeleteNodeUsingDeleteApiArg, FcGetFrameworkUsingGetApiResponse, FcGetFrameworkUsingGetApiArg, FcUpdateFrameworkDetailsUsingPostApiResponse, FcUpdateFrameworkDetailsUsingPostApiArg, FcDeleteFrameworkUsingDeleteApiResponse, FcDeleteFrameworkUsingDeleteApiArg, FcDuplicateFrameworkUsingPostApiResponse, FcDuplicateFrameworkUsingPostApiArg, FcExportFrameworkUsingGetApiResponse, FcExportFrameworkUsingGetApiArg, FcGetFrameworkNodesHierarchyUsingGetApiResponse, FcGetFrameworkNodesHierarchyUsingGetApiArg, FcUpdateNodesHierarchyUsingPutApiResponse, FcUpdateNodesHierarchyUsingPutApiArg, FcGetFrameworkNodesUsingGetApiResponse, FcGetFrameworkNodesUsingGetApiArg, FcUpdateStatusUsingPutApiResponse, FcUpdateStatusUsingPutApiArg, CategoryView, CategoryViewRead, FrameworkRegister, FrameworkRegisterRead, UserViewShared, FrameworkView, FrameworkViewRead, FrameworkStatusView, FrameworkStatusViewRead, FrameworkStub, FrameworkStubRead, NodeDetail, NodeLinkDetail, NodeLinkDetailRead, NodeLinkTarget, NodeLinkTargetRead, NodeLink, NodeLinkRead, NodeDetailRead, FrameworkDetail, FrameworkFile, FrameworkFileRead, WorklogShared, FrameworkDetailRead, HttpEntityObject, HierarchicalNode, HierarchicalNodeRead, SimpleNode, SimpleNodeRead } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        fcGetFrameworksUsingGet: build.query<FcGetFrameworksUsingGetApiResponse, FcGetFrameworksUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/frameworks`,
                params: {
                    category: queryArg.category,
                },
            }),
        }),
        fcCreateFrameworkUsingPost: build.mutation<FcCreateFrameworkUsingPostApiResponse, FcCreateFrameworkUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks`, method: 'POST', body: queryArg.body }),
        }),
        fcGetFrameworkCategoriesUsingGet: build.query<FcGetFrameworkCategoriesUsingGetApiResponse, FcGetFrameworkCategoriesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/frameworks/categories` }),
        }),
        fcExportDataRegisterUsingGet: build.query<FcExportDataRegisterUsingGetApiResponse, FcExportDataRegisterUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/frameworks/dataregister/export`,
                params: {
                    tableName: queryArg.tableName,
                },
            }),
        }),
        fcImportDataRegisterUsingPost: build.mutation<FcImportDataRegisterUsingPostApiResponse, FcImportDataRegisterUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/frameworks/dataregister/import`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    tableName: queryArg.tableName,
                    frameworkId: queryArg.frameworkId,
                },
            }),
        }),
        fcImportVerifyDataRegisterUsingPost: build.mutation<FcImportVerifyDataRegisterUsingPostApiResponse, FcImportVerifyDataRegisterUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/frameworks/dataregister/import/verify`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    tableName: queryArg.tableName,
                    frameworkId: queryArg.frameworkId,
                },
            }),
        }),
        fcGetFrameworkStatusUsingGet: build.query<FcGetFrameworkStatusUsingGetApiResponse, FcGetFrameworkStatusUsingGetApiArg>({
            query: () => ({ url: `/v1/api/frameworks/framework-status` }),
        }),
        fcImportFrameworkUsingPost: build.mutation<FcImportFrameworkUsingPostApiResponse, FcImportFrameworkUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/import`, method: 'POST', body: queryArg.body }),
        }),
        fcGetFrameworkListUsingGet: build.query<FcGetFrameworkListUsingGetApiResponse, FcGetFrameworkListUsingGetApiArg>({
            query: () => ({ url: `/v1/api/frameworks/list` }),
        }),
        fcGetMetadataRegistersListUsingGet: build.query<FcGetMetadataRegistersListUsingGetApiResponse, FcGetMetadataRegistersListUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/frameworks/metadata`,
                params: {
                    search: queryArg.search,
                },
            }),
        }),
        fcCreateNodeUsingPost: build.mutation<FcCreateNodeUsingPostApiResponse, FcCreateNodeUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/node`, method: 'POST', body: queryArg.nodeDetail }),
        }),
        fcGetNodeUsingGet: build.query<FcGetNodeUsingGetApiResponse, FcGetNodeUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/node/${queryArg.id}` }),
        }),
        fcUpdateNodeUsingPut: build.mutation<FcUpdateNodeUsingPutApiResponse, FcUpdateNodeUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/node/${queryArg.id}`, method: 'PUT', body: queryArg.nodeDetail }),
        }),
        fcDeleteNodeUsingDelete: build.mutation<FcDeleteNodeUsingDeleteApiResponse, FcDeleteNodeUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/node/${queryArg.id}`, method: 'DELETE' }),
        }),
        fcGetFrameworkUsingGet: build.query<FcGetFrameworkUsingGetApiResponse, FcGetFrameworkUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/${queryArg.id}` }),
        }),
        fcUpdateFrameworkDetailsUsingPost: build.mutation<FcUpdateFrameworkDetailsUsingPostApiResponse, FcUpdateFrameworkDetailsUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/${queryArg.id}`, method: 'POST', body: queryArg.body }),
        }),
        fcDeleteFrameworkUsingDelete: build.mutation<FcDeleteFrameworkUsingDeleteApiResponse, FcDeleteFrameworkUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/${queryArg.id}`, method: 'DELETE' }),
        }),
        fcDuplicateFrameworkUsingPost: build.mutation<FcDuplicateFrameworkUsingPostApiResponse, FcDuplicateFrameworkUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/${queryArg.id}/duplicate`, method: 'POST', body: queryArg.body }),
        }),
        fcExportFrameworkUsingGet: build.query<FcExportFrameworkUsingGetApiResponse, FcExportFrameworkUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/frameworks/${queryArg.id}/export`,
                params: {
                    type: queryArg['type'],
                    internal: queryArg.internal,
                },
            }),
        }),
        fcGetFrameworkNodesHierarchyUsingGet: build.query<FcGetFrameworkNodesHierarchyUsingGetApiResponse, FcGetFrameworkNodesHierarchyUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/${queryArg.id}/nodes` }),
        }),
        fcUpdateNodesHierarchyUsingPut: build.mutation<FcUpdateNodesHierarchyUsingPutApiResponse, FcUpdateNodesHierarchyUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/${queryArg.id}/nodes`, method: 'PUT', body: queryArg.body }),
        }),
        fcGetFrameworkNodesUsingGet: build.query<FcGetFrameworkNodesUsingGetApiResponse, FcGetFrameworkNodesUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/frameworks/${queryArg.id}/nodes/list` }),
        }),
        fcUpdateStatusUsingPut: build.mutation<FcUpdateStatusUsingPutApiResponse, FcUpdateStatusUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/frameworks/${queryArg.id}/updateStatus`,
                method: 'PUT',
                params: {
                    newStatusId: queryArg.newStatusId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
