import { EsrsiGetEntryScheduleUsingGetApiResponse, EsrsiGetEntryScheduleUsingGetApiArg, EsrsiCreateEntryScheduleUsingPostApiResponse, EsrsiCreateEntryScheduleUsingPostApiArg, EsrsiUpdateEntryScheduleUsingPutApiResponse, EsrsiUpdateEntryScheduleUsingPutApiArg, ParentEntryRestInternal, EntryScheduleConfigRestInternal, PeriodRest, IdWithNameRest, EntryScheduleRestInternal } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        esrsiGetEntryScheduleUsingGet: build.query<EsrsiGetEntryScheduleUsingGetApiResponse, EsrsiGetEntryScheduleUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/ientryschedule/${queryArg.tableName}/${queryArg.entryScheduleId}` }),
        }),
        esrsiCreateEntryScheduleUsingPost: build.mutation<EsrsiCreateEntryScheduleUsingPostApiResponse, EsrsiCreateEntryScheduleUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ientryschedule/${queryArg.tableName}/${queryArg.entryScheduleId}`,
                method: 'POST',
                body: queryArg.entryScheduleRestInternal,
            }),
        }),
        esrsiUpdateEntryScheduleUsingPut: build.mutation<EsrsiUpdateEntryScheduleUsingPutApiResponse, EsrsiUpdateEntryScheduleUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ientryschedule/${queryArg.tableName}/${queryArg.entryScheduleId}/update`,
                method: 'PUT',
                body: queryArg.entryScheduleRestInternal,
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
