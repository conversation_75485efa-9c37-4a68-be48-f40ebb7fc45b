import { SrsGetScaleSetsUsingGetApiResponse, SrsGetScaleSetsUsingGetApiArg, SrsCreateScaleSetUsingPostApiResponse, SrsCreateScaleSetUsingPostApiArg, SrsUpdateScaleSetUsingPutApiResponse, SrsUpdateScaleSetUsingPutApiArg, SrsDeleteScaleSetUsingDeleteApiResponse, SrsDeleteScaleSetUsingDeleteApiArg, SrsGetScaleSetUsingGetApiResponse, SrsGetScaleSetUsingGetApiArg, RiskMatrixRatingRest, RiskMatrixCellRest, ScaleItemRest, ScaleRest, ScaleSetRest, PaginRestResultScaleSetRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        srsGetScaleSetsUsingGet: build.query<SrsGetScaleSetsUsingGetApiResponse, SrsGetScaleSetsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/scalesets`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    pagingDisabled: queryArg.pagingDisabled,
                },
            }),
        }),
        srsCreateScaleSetUsingPost: build.mutation<SrsCreateScaleSetUsingPostApiResponse, SrsCreateScaleSetUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/scalesets`, method: 'POST', body: queryArg.scaleSetRest }),
        }),
        srsUpdateScaleSetUsingPut: build.mutation<SrsUpdateScaleSetUsingPutApiResponse, SrsUpdateScaleSetUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/scalesets`, method: 'PUT', body: queryArg.scaleSetRest }),
        }),
        srsDeleteScaleSetUsingDelete: build.mutation<SrsDeleteScaleSetUsingDeleteApiResponse, SrsDeleteScaleSetUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/scalesets/scale/${queryArg.scaleSetId}`, method: 'DELETE' }),
        }),
        srsGetScaleSetUsingGet: build.query<SrsGetScaleSetUsingGetApiResponse, SrsGetScaleSetUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/scalesets/${queryArg.scaleSetId}` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
