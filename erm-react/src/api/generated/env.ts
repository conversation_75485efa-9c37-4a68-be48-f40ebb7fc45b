import { ErsRefreshUsingPutApiResponse, ErsRefreshUsingPutApiArg } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        ersRefreshUsingPut: build.mutation<ErsRefreshUsingPutApiResponse, ErsRefreshUsingPutApiArg>({
            query: () => ({ url: `/v1/api/env/refresh`, method: 'PUT' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
