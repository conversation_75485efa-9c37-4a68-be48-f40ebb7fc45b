import { WfrsCreateWorkFlowRuleUsingPostApiResponse, WfrsCreateWorkFlowRuleUsingPostApiArg, WfrsGetWorkFlowRuleUsingGetApiResponse, WfrsGetWorkFlowRuleUsingGetApiArg, WfrsUpdateWorkFlowRuleUsingPutApiResponse, WfrsUpdateWorkFlowRuleUsingPutApiArg, WfrsDeleteWorkFlowRuleUsingDeleteApiResponse, WfrsDeleteWorkFlowRuleUsingDeleteApiArg, RestParamRest, RestResponseActionConditionRest, RestResponseTranslationMapRest, RestResponseActionFieldMapRest, RestResponseActionRest, RestResponseHandlerRest, WorkflowTemplateRest, WorkFlowApiAction, WorkflowCalendarEventAction, WorkFlowConditionField, WorkFlowCondition, WorkFlowEmailAction, WorkFlowSmsAction, WorkFlowStatus, WorkFlowRuleRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        wfrsCreateWorkFlowRuleUsingPost: build.mutation<WfrsCreateWorkFlowRuleUsingPostApiResponse, WfrsCreateWorkFlowRuleUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workflows`, method: 'POST', body: queryArg.workFlowRuleRest }),
        }),
        wfrsGetWorkFlowRuleUsingGet: build.query<WfrsGetWorkFlowRuleUsingGetApiResponse, WfrsGetWorkFlowRuleUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workflows/${queryArg.workflowId}` }),
        }),
        wfrsUpdateWorkFlowRuleUsingPut: build.mutation<WfrsUpdateWorkFlowRuleUsingPutApiResponse, WfrsUpdateWorkFlowRuleUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workflows/${queryArg.workflowId}`, method: 'PUT', body: queryArg.workFlowRuleRest }),
        }),
        wfrsDeleteWorkFlowRuleUsingDelete: build.mutation<WfrsDeleteWorkFlowRuleUsingDeleteApiResponse, WfrsDeleteWorkFlowRuleUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workflows/${queryArg.workflowId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
