import { MrsFindByIdsUsingGetApiResponse, MrsFindByIdsUsingGetApiArg, Mrs<PERSON>reateUsingPostApiResponse, MrsCreateUsingPostApiArg, MrsDeleteMetricUsingDeleteApiResponse, MrsDeleteMetricUsingDeleteApiArg, MrsEvalUsingGetApiResponse, MrsEvalUsingGetApiArg, MrsSearchUsingPostApiResponse, MrsSearchUsingPostApiArg, MrsGetDataSourcesUsingPostApiResponse, MrsGetDataSourcesUsingPostApiArg, MrsGetDataSourcesGetUsingGetApiResponse, MrsGetDataSourcesGetUsingGetApiArg, MdrsGetDisplayConfigurationUsingGetApiResponse, MdrsGetDisplayConfigurationUsingGetApiArg, MdrsSetDisplayConfigurationUsingPostApiResponse, MdrsSetDisplayConfigurationUsingPostApiArg, MrsFindByIdUsingGetApiResponse, MrsFindByIdUsingGetApiArg, MrsUpdateUsingPutApiResponse, MrsUpdateUsingPutApiArg, MComputedStyle, MEvalResult, Condition, ConditionConfig, ViewCondition, ViewConditionConfig, MScaleBool, MScaleTxtBracket, MScaleNumBracket, MScaleOverdue, MStyleConfig, MetricRest, MetricRestResponse, PaginRestResultMetricRest, ViewExpressionRest, FilterContextRest, ContractMappingRestField, ContractMappingRest, GlobalListRest, FormulaComponentRest, FieldRest, Property, Properties, FieldActionRest, FieldConditionRest, FieldRuleStatusRest, FieldRuleRest, FieldRuleSetRest, RiskMatrixRatingRest, RiskMatrixCellRest, ScaleItemRest, ScaleRest, ScaleSetRest, StyleRest, SectionRest, PermissionRest, IdWithNameAndStatusRest, RoleRest, RegisterStateTransitionRest, RegisterStateRest, RegisterStateDefinitionRest, TaskColumnRest, RestParamRest, RestResponseActionConditionRest, RestResponseTranslationMapRest, RestResponseActionFieldMapRest, RestResponseActionRest, RestResponseHandlerRest, WorkflowTemplateRest, WorkFlowApiAction, WorkflowCalendarEventAction, WorkFlowConditionField, WorkFlowCondition, WorkFlowEmailAction, WorkFlowSmsAction, WorkFlowStatus, WorkFlowRuleRest, TableMetadataRest, PaginRestResultTableMetadataRest, BaseMetricDisplay, MetricContext, MetricModule, MetricDisplayConfig } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        mrsFindByIdsUsingGet: build.query<MrsFindByIdsUsingGetApiResponse, MrsFindByIdsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics`,
                params: {
                    idsOrNames: queryArg.idsOrNames,
                    eval: queryArg.eval,
                    computeStyle: queryArg.computeStyle,
                },
            }),
        }),
        mrsCreateUsingPost: build.mutation<MrsCreateUsingPostApiResponse, MrsCreateUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/metrics`, method: 'POST', body: queryArg.metricRest }),
        }),
        mrsDeleteMetricUsingDelete: build.mutation<MrsDeleteMetricUsingDeleteApiResponse, MrsDeleteMetricUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics`,
                method: 'DELETE',
                params: {
                    metricId: queryArg.metricId,
                },
            }),
        }),
        mrsEvalUsingGet: build.query<MrsEvalUsingGetApiResponse, MrsEvalUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics/action/eval`,
                params: {
                    idsOrNames: queryArg.idsOrNames,
                    buId: queryArg.buId,
                    computeStyle: queryArg.computeStyle,
                },
            }),
        }),
        mrsSearchUsingPost: build.mutation<MrsSearchUsingPostApiResponse, MrsSearchUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics/action/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    moduleName: queryArg.moduleName,
                },
            }),
        }),
        mrsGetDataSourcesUsingPost: build.mutation<MrsGetDataSourcesUsingPostApiResponse, MrsGetDataSourcesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics/datasources/${queryArg['module']}`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        mrsGetDataSourcesGetUsingGet: build.query<MrsGetDataSourcesGetUsingGetApiResponse, MrsGetDataSourcesGetUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics/datasources/${queryArg['module']}/all`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    payload: queryArg.payload,
                },
            }),
        }),
        mdrsGetDisplayConfigurationUsingGet: build.query<MdrsGetDisplayConfigurationUsingGetApiResponse, MdrsGetDisplayConfigurationUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics/display`,
                params: {
                    module: queryArg['module'],
                },
            }),
        }),
        mdrsSetDisplayConfigurationUsingPost: build.mutation<MdrsSetDisplayConfigurationUsingPostApiResponse, MdrsSetDisplayConfigurationUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/metrics/display/${queryArg['module']}`, method: 'POST', body: queryArg.metricDisplayConfig }),
        }),
        mrsFindByIdUsingGet: build.query<MrsFindByIdUsingGetApiResponse, MrsFindByIdUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/metrics/${queryArg.idOrName}`,
                params: {
                    eval: queryArg.eval,
                    computeStyle: queryArg.computeStyle,
                },
            }),
        }),
        mrsUpdateUsingPut: build.mutation<MrsUpdateUsingPutApiResponse, MrsUpdateUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/metrics/${queryArg.metricId}`, method: 'PUT', body: queryArg.metricRest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
