import { AqrsGetAuditQuestionsUsingPostApiResponse, AqrsGetAuditQuestionsUsingPostApiArg, AqrsCreateQuestionUsingPostApiResponse, AqrsCreateQuestionUsingPostApiArg, AqrsGetQuestionUsingGetApiResponse, AqrsGetQuestionUsingGetApiArg, AqrsUpdateQuestionUsingPutApiResponse, AqrsUpdateQuestionUsingPutApiArg, IdWithNameRest, AuditQuestionBaseRest, PaginRestResultAuditQuestionBaseRest, ViewExpressionRest, FilterContextRest, AuditQuestionResponseRest, AuditQuestionStepRest, AuditQuestionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        aqrsGetAuditQuestionsUsingPost: build.mutation<AqrsGetAuditQuestionsUsingPostApiResponse, AqrsGetAuditQuestionsUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/audit/auditquestions`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        aqrsCreateQuestionUsingPost: build.mutation<AqrsCreateQuestionUsingPostApiResponse, AqrsCreateQuestionUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/audit/questions`, method: 'POST', body: queryArg.auditQuestionRest }),
        }),
        aqrsGetQuestionUsingGet: build.query<AqrsGetQuestionUsingGetApiResponse, AqrsGetQuestionUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/audit/questions/${queryArg.questionId}` }),
        }),
        aqrsUpdateQuestionUsingPut: build.mutation<AqrsUpdateQuestionUsingPutApiResponse, AqrsUpdateQuestionUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/audit/questions/${queryArg.questionId}`, method: 'PUT', body: queryArg.auditQuestionRest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
