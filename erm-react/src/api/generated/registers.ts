import { GetRegisterConfigurationsApiResponse, GetRegisterConfigurationsApiArg, TmrsCreateRegisterConfigUsingPostApiResponse, TmrsCreateRegisterConfigUsingPostApiArg, TmrsGetActionRegistersUsingGetApiResponse, TmrsGetActionRegistersUsingGetApiArg, TmrsGetAllRegistersWithBowTieMslUsingGetApiResponse, TmrsGetAllRegistersWithBowTieMslUsingGetApiArg, FrirsGetRegisterRulesByTableNamesUsingGetApiResponse, FrirsGetRegisterRulesByTableNamesUsingGetApiArg, TmrsGetRegistersUsingPostApiResponse, TmrsGetRegistersUsingPostApiArg, TmrsGetRegistersGetUsingGetApiResponse, TmrsGetRegistersGetUsingGetApiArg, TmrsGetRegistersStatsUsingGetApiResponse, TmrsGetRegistersStatsUsingGetApiArg, TmrsGetRegisterConfigUsingGetApiResponse, TmrsGetRegisterConfigUsingGetApiArg, TmrsGetRegisterConfigsUsingGetApiResponse, TmrsGetRegisterConfigsUsingGetApiArg, TmrsGetRegisterConfigUsingGet1ApiResponse, TmrsGetRegisterConfigUsingGet1ApiArg, UpdateRegisterApiResponse, UpdateRegisterApiArg, TmrsDeleteRegisterConfigUsingDeleteApiResponse, TmrsDeleteRegisterConfigUsingDeleteApiArg, TmrsToggleRegisterFavouriteUsingPutApiResponse, TmrsToggleRegisterFavouriteUsingPutApiArg, TmrsGetRegisterFieldConfigByLabelUsingGetApiResponse, TmrsGetRegisterFieldConfigByLabelUsingGetApiArg, SetRegisterLayoutApiResponse, SetRegisterLayoutApiArg, AddSectionApiResponse, AddSectionApiArg, DeleteSectionApiResponse, DeleteSectionApiArg, TmrsSetRegisterStateUsingPutApiResponse, TmrsSetRegisterStateUsingPutApiArg, TmrsCancelRegisterStateUsingDeleteApiResponse, TmrsCancelRegisterStateUsingDeleteApiArg, TmrsSetDefaultRegisterStateUsingPutApiResponse, TmrsSetDefaultRegisterStateUsingPutApiArg, FrirsGetRulesUsingGetApiResponse, FrirsGetRulesUsingGetApiArg, FrirsAddRuleUsingPutApiResponse, FrirsAddRuleUsingPutApiArg, FrirsDeleteRuleUsingDeleteApiResponse, FrirsDeleteRuleUsingDeleteApiArg, ContractMappingRestField, ContractMappingRest, GlobalListRest, FormulaComponentRest, FieldRest, Property, Properties, FieldActionRest, FieldConditionRest, FieldRuleStatusRest, FieldRuleRest, FieldRuleSetRest, RiskMatrixRatingRest, RiskMatrixCellRest, ScaleItemRest, ScaleRest, ScaleSetRest, StyleRest, SectionRest, PermissionRest, IdWithNameAndStatusRest, RoleRest, RegisterStateTransitionRest, RegisterStateRest, RegisterStateDefinitionRest, TaskColumnRest, RestParamRest, RestResponseActionConditionRest, RestResponseTranslationMapRest, RestResponseActionFieldMapRest, RestResponseActionRest, RestResponseHandlerRest, WorkflowTemplateRest, WorkFlowApiAction, WorkflowCalendarEventAction, WorkFlowConditionField, WorkFlowCondition, WorkFlowEmailAction, WorkFlowSmsAction, WorkFlowStatus, WorkFlowRuleRest, TableMetadataRest, PaginRestResultTableMetadataRest, FieldBowTieRest, RegisterBowTieRest, BulkFieldRuleRest, ViewExpressionRest, FilterContextRest, RegisterStatsDto, RegisterLayout } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        getRegisterConfigurations: build.query<GetRegisterConfigurationsApiResponse, GetRegisterConfigurationsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/registers`,
                params: {
                    applicationId: queryArg.applicationId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    label: queryArg.label,
                    description: queryArg.description,
                    stateful: queryArg.stateful,
                    favouriteDisplayChoice: queryArg.favouriteDisplayChoice,
                    libraryDisplayChoice: queryArg.libraryDisplayChoice,
                    skipSectionData: queryArg.skipSectionData,
                    includeTemplateCount: queryArg.includeTemplateCount,
                },
            }),
        }),
        tmrsCreateRegisterConfigUsingPost: build.mutation<TmrsCreateRegisterConfigUsingPostApiResponse, TmrsCreateRegisterConfigUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers`, method: 'POST', body: queryArg.tableMetadataRest }),
        }),
        tmrsGetActionRegistersUsingGet: build.query<TmrsGetActionRegistersUsingGetApiResponse, TmrsGetActionRegistersUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/registers/actions`,
                params: {
                    linkTables: queryArg.linkTables,
                },
            }),
        }),
        tmrsGetAllRegistersWithBowTieMslUsingGet: build.query<
            TmrsGetAllRegistersWithBowTieMslUsingGetApiResponse,
            TmrsGetAllRegistersWithBowTieMslUsingGetApiArg
        >({
            query: () => ({ url: `/v1/api/registers/bowties` }),
        }),
        frirsGetRegisterRulesByTableNamesUsingGet: build.query<
            FrirsGetRegisterRulesByTableNamesUsingGetApiResponse,
            FrirsGetRegisterRulesByTableNamesUsingGetApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/registers/rules`,
                params: {
                    tableNames: queryArg.tableNames,
                },
            }),
        }),
        tmrsGetRegistersUsingPost: build.query<TmrsGetRegistersUsingPostApiResponse, TmrsGetRegistersUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/registers/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    contractTypes: queryArg.contractTypes,
                    libraryDisplayChoice: queryArg.libraryDisplayChoice,
                },
            }),
        }),
        tmrsGetRegistersGetUsingGet: build.query<TmrsGetRegistersGetUsingGetApiResponse, TmrsGetRegistersGetUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/registers/search/all`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                    contractTypes: queryArg.contractTypes,
                    libraryDisplayChoice: queryArg.libraryDisplayChoice,
                    payload: queryArg.payload,
                },
            }),
        }),
        tmrsGetRegistersStatsUsingGet: build.query<TmrsGetRegistersStatsUsingGetApiResponse, TmrsGetRegistersStatsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/registers/stats`,
                params: {
                    registers: queryArg.registers,
                    businessUnitFilter: queryArg.businessUnitFilter,
                },
            }),
        }),
        tmrsGetRegisterConfigUsingGet: build.query<TmrsGetRegisterConfigUsingGetApiResponse, TmrsGetRegisterConfigUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/tableName/${queryArg.tableName}` }),
        }),
        tmrsGetRegisterConfigsUsingGet: build.query<TmrsGetRegisterConfigsUsingGetApiResponse, TmrsGetRegisterConfigsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/registers/${queryArg.applicationId}/list`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                },
            }),
        }),
        tmrsGetRegisterConfigUsingGet1: build.query<TmrsGetRegisterConfigUsingGet1ApiResponse, TmrsGetRegisterConfigUsingGet1ApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.id}` }),
        }),
        updateRegister: build.mutation<UpdateRegisterApiResponse, UpdateRegisterApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}`, method: 'PUT', body: queryArg.tableMetadataRest }),
        }),
        tmrsDeleteRegisterConfigUsingDelete: build.mutation<TmrsDeleteRegisterConfigUsingDeleteApiResponse, TmrsDeleteRegisterConfigUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}`, method: 'DELETE' }),
        }),
        tmrsToggleRegisterFavouriteUsingPut: build.mutation<TmrsToggleRegisterFavouriteUsingPutApiResponse, TmrsToggleRegisterFavouriteUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}/favourite`, method: 'PUT' }),
        }),
        tmrsGetRegisterFieldConfigByLabelUsingGet: build.query<
            TmrsGetRegisterFieldConfigByLabelUsingGetApiResponse,
            TmrsGetRegisterFieldConfigByLabelUsingGetApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/registers/${queryArg.regId}/field`,
                params: {
                    'field-label': queryArg['field-label'],
                    'section-label': queryArg['section-label'],
                    'column-name': queryArg['column-name'],
                },
            }),
        }),
        setRegisterLayout: build.mutation<SetRegisterLayoutApiResponse, SetRegisterLayoutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}/layout`, method: 'PUT', body: queryArg.body }),
        }),
        addSection: build.mutation<AddSectionApiResponse, AddSectionApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}/sections`, method: 'PUT', body: queryArg.body }),
        }),
        deleteSection: build.mutation<DeleteSectionApiResponse, DeleteSectionApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}/sections`, method: 'DELETE', body: queryArg.body }),
        }),
        tmrsSetRegisterStateUsingPut: build.mutation<TmrsSetRegisterStateUsingPutApiResponse, TmrsSetRegisterStateUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}/state`, method: 'PUT', body: queryArg.registerStateDefinitionRest }),
        }),
        tmrsCancelRegisterStateUsingDelete: build.mutation<TmrsCancelRegisterStateUsingDeleteApiResponse, TmrsCancelRegisterStateUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}/state`, method: 'DELETE' }),
        }),
        tmrsSetDefaultRegisterStateUsingPut: build.mutation<TmrsSetDefaultRegisterStateUsingPutApiResponse, TmrsSetDefaultRegisterStateUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.regId}/state/default`, method: 'PUT' }),
        }),
        frirsGetRulesUsingGet: build.query<FrirsGetRulesUsingGetApiResponse, FrirsGetRulesUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.registerId}/rules` }),
        }),
        frirsAddRuleUsingPut: build.mutation<FrirsAddRuleUsingPutApiResponse, FrirsAddRuleUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.registerId}/rules`, method: 'PUT', body: queryArg.fieldRuleRest }),
        }),
        frirsDeleteRuleUsingDelete: build.mutation<FrirsDeleteRuleUsingDeleteApiResponse, FrirsDeleteRuleUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/registers/${queryArg.registerId}/rules/${queryArg.ruleId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
