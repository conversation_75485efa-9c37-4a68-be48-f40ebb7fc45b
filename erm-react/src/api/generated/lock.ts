import { RdlrsvLockRecordUsingPostApiResponse, RdlrsvLockRecordUsingPostApiArg, RdlrsvDeleteLoggedUserLocksUsingDeleteApiResponse, RdlrsvDeleteLoggedUserLocksUsingDeleteApiArg, GetLockApiResponse, GetLockApiArg, RdlrsvRenewalRecordLockUsingPutApiResponse, RdlrsvRenewalRecordLockUsingPutApiArg, DeleteLockApiResponse, DeleteLockApiArg, IdWithNameRest, ObjectLockRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        rdlrsvLockRecordUsingPost: build.mutation<RdlrsvLockRecordUsingPostApiResponse, RdlrsvLockRecordUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/lock/entry/${queryArg.tableName}/${queryArg.entryId}`, method: 'POST', body: queryArg.registerDataRest }),
        }),
        rdlrsvDeleteLoggedUserLocksUsingDelete: build.mutation<RdlrsvDeleteLoggedUserLocksUsingDeleteApiResponse, RdlrsvDeleteLoggedUserLocksUsingDeleteApiArg>(
            {
                query: () => ({ url: `/v1/api/lock/lock`, method: 'DELETE' }),
            },
        ),
        getLock: build.query<GetLockApiResponse, GetLockApiArg>({
            query: (queryArg) => ({ url: `/v1/api/lock/${queryArg.lockId}/entry/${queryArg.tableName}/${queryArg.entryId}` }),
        }),
        rdlrsvRenewalRecordLockUsingPut: build.mutation<RdlrsvRenewalRecordLockUsingPutApiResponse, RdlrsvRenewalRecordLockUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/lock/${queryArg.lockId}/entry/${queryArg.tableName}/${queryArg.entryId}`, method: 'PUT' }),
        }),
        deleteLock: build.mutation<DeleteLockApiResponse, DeleteLockApiArg>({
            query: (queryArg) => ({ url: `/v1/api/lock/${queryArg.lockId}/entry/${queryArg.tableName}/${queryArg.entryId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
