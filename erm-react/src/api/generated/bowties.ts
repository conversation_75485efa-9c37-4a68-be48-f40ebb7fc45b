import { BtrsCreateDiagramUsingPostApiResponse, BtrsCreateDiagramUsingPostApiArg, BtrsGetDefinitionUsingGetApiResponse, BtrsGetDefinitionUsingGetApiArg, BtrsUpdateDiagramDefinitionUsingPutApiResponse, BtrsUpdateDiagramDefinitionUsingPutApiArg, BtrsGetRecentDiagramsUsingGetApiResponse, BtrsGetRecentDiagramsUsingGetApiArg, BtrsGetDiagramStyleUsingGetApiResponse, BtrsGetDiagramStyleUsingGetApiArg, BtrsValidateBowtieNameUsingGetApiResponse, BtrsValidateBowtieNameUsingGetApiArg, BtrsVerifyUserBUsUsingPutApiResponse, BtrsVerifyUserBUsUsingPutApiArg, BtrsLinkBowTieUsingPutApiResponse, BtrsLinkBowTieUsingPutApiArg, BtrsUnlinkUsingPutApiResponse, BtrsUnlinkUsingPutApiArg, BtrsGetDiagramUsingGetApiResponse, BtrsGetDiagramUsingGetApiArg, BtrsUpdateDiagramUsingPutApiResponse, BtrsUpdateDiagramUsingPutApiArg, BtrsDeleteDiagramUsingDeleteApiResponse, BtrsDeleteDiagramUsingDeleteApiArg, BtrsGetDiagramCopyUsingPutApiResponse, BtrsGetDiagramCopyUsingPutApiArg, BtrsGetEntriesUsingGetApiResponse, BtrsGetEntriesUsingGetApiArg, BtrsChangeDiagramLockStatusUsingPutApiResponse, BtrsChangeDiagramLockStatusUsingPutApiArg, BtrsPublishDiagramUsingPutApiResponse, BtrsPublishDiagramUsingPutApiArg, BtrsPurgeDiagramUsingDeleteApiResponse, BtrsPurgeDiagramUsingDeleteApiArg, BtrsRenameDiagramUsingPutApiResponse, BtrsRenameDiagramUsingPutApiArg, BtrsRestoreDiagramUsingPutApiResponse, BtrsRestoreDiagramUsingPutApiArg, BtrsUnpublishDiagramUsingPutApiResponse, BtrsUnpublishDiagramUsingPutApiArg, BtrsGetDiagramsUsingGetApiResponse, BtrsGetDiagramsUsingGetApiArg, BtrsGetDiagramsUsingPostApiResponse, BtrsGetDiagramsUsingPostApiArg, IdWithNameRest, Timestamp, JsonNode, BowTieLinkNotificationRest, BowTieDiagramRest, BowTieCreateDiagramRest, BowTieRegister, BowTieDefinition, ObjectNode, FieldBowTieRest, RegisterDataBowTiesRest, PaginRestResultBowTieDiagramRest, BowTieListRequest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        btrsCreateDiagramUsingPost: build.mutation<BtrsCreateDiagramUsingPostApiResponse, BtrsCreateDiagramUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties`,
                method: 'POST',
                body: queryArg.bowTieCreateDiagramRest,
                params: {
                    withDiagram: queryArg.withDiagram,
                },
            }),
        }),
        btrsGetDefinitionUsingGet: build.query<BtrsGetDefinitionUsingGetApiResponse, BtrsGetDefinitionUsingGetApiArg>({
            query: () => ({ url: `/v1/api/bowties/definition` }),
        }),
        btrsUpdateDiagramDefinitionUsingPut: build.mutation<BtrsUpdateDiagramDefinitionUsingPutApiResponse, BtrsUpdateDiagramDefinitionUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/definition`, method: 'PUT', body: queryArg.bowTieDefinition }),
        }),
        btrsGetRecentDiagramsUsingGet: build.query<BtrsGetRecentDiagramsUsingGetApiResponse, BtrsGetRecentDiagramsUsingGetApiArg>({
            query: () => ({ url: `/v1/api/bowties/recent` }),
        }),
        btrsGetDiagramStyleUsingGet: build.query<BtrsGetDiagramStyleUsingGetApiResponse, BtrsGetDiagramStyleUsingGetApiArg>({
            query: () => ({ url: `/v1/api/bowties/style` }),
        }),
        btrsValidateBowtieNameUsingGet: build.query<BtrsValidateBowtieNameUsingGetApiResponse, BtrsValidateBowtieNameUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/validateBowtieName`,
                params: {
                    diagramName: queryArg.diagramName,
                },
            }),
        }),
        btrsVerifyUserBUsUsingPut: build.mutation<BtrsVerifyUserBUsUsingPutApiResponse, BtrsVerifyUserBUsUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/verify`, method: 'PUT', body: queryArg.body }),
        }),
        btrsLinkBowTieUsingPut: build.mutation<BtrsLinkBowTieUsingPutApiResponse, BtrsLinkBowTieUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/${queryArg.bowTieId}/entries/${queryArg.entryId}/link`,
                method: 'PUT',
                body: queryArg.fieldBowTieRest,
                params: {
                    regId: queryArg.regId,
                },
            }),
        }),
        btrsUnlinkUsingPut: build.mutation<BtrsUnlinkUsingPutApiResponse, BtrsUnlinkUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/${queryArg.bowTieId}/entries/${queryArg.entryId}/unlink`,
                method: 'PUT',
                body: queryArg.fieldBowTieRest,
                params: {
                    regId: queryArg.regId,
                },
            }),
        }),
        btrsGetDiagramUsingGet: build.query<BtrsGetDiagramUsingGetApiResponse, BtrsGetDiagramUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/${queryArg.id}`,
                params: {
                    withDiagram: queryArg.withDiagram,
                },
            }),
        }),
        btrsUpdateDiagramUsingPut: build.mutation<BtrsUpdateDiagramUsingPutApiResponse, BtrsUpdateDiagramUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/${queryArg.id}`,
                method: 'PUT',
                body: queryArg.bowTieDiagramRest,
                params: {
                    withDiagram: queryArg.withDiagram,
                },
            }),
        }),
        btrsDeleteDiagramUsingDelete: build.mutation<BtrsDeleteDiagramUsingDeleteApiResponse, BtrsDeleteDiagramUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.id}`, method: 'DELETE' }),
        }),
        btrsGetDiagramCopyUsingPut: build.mutation<BtrsGetDiagramCopyUsingPutApiResponse, BtrsGetDiagramCopyUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/${queryArg.id}/copy`,
                method: 'PUT',
                body: queryArg.body,
                params: {
                    withDiagram: queryArg.withDiagram,
                },
            }),
        }),
        btrsGetEntriesUsingGet: build.query<BtrsGetEntriesUsingGetApiResponse, BtrsGetEntriesUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.id}/entries` }),
        }),
        btrsChangeDiagramLockStatusUsingPut: build.mutation<BtrsChangeDiagramLockStatusUsingPutApiResponse, BtrsChangeDiagramLockStatusUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/${queryArg.id}/lock`,
                method: 'PUT',
                params: {
                    lock: queryArg.lock,
                },
            }),
        }),
        btrsPublishDiagramUsingPut: build.mutation<BtrsPublishDiagramUsingPutApiResponse, BtrsPublishDiagramUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.id}/publish`, method: 'PUT', body: queryArg.body }),
        }),
        btrsPurgeDiagramUsingDelete: build.mutation<BtrsPurgeDiagramUsingDeleteApiResponse, BtrsPurgeDiagramUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.id}/purge`, method: 'DELETE' }),
        }),
        btrsRenameDiagramUsingPut: build.mutation<BtrsRenameDiagramUsingPutApiResponse, BtrsRenameDiagramUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.id}/rename`, method: 'PUT', body: queryArg.body }),
        }),
        btrsRestoreDiagramUsingPut: build.mutation<BtrsRestoreDiagramUsingPutApiResponse, BtrsRestoreDiagramUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.id}/restore`, method: 'PUT' }),
        }),
        btrsUnpublishDiagramUsingPut: build.mutation<BtrsUnpublishDiagramUsingPutApiResponse, BtrsUnpublishDiagramUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.id}/unpublish`, method: 'PUT' }),
        }),
        btrsGetDiagramsUsingGet: build.query<BtrsGetDiagramsUsingGetApiResponse, BtrsGetDiagramsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/bowties/${queryArg.status}`,
                params: {
                    page: queryArg.page,
                    size: queryArg.size,
                    view: queryArg.view,
                    'sort-by': queryArg['sort-by'],
                    'sort-dir': queryArg['sort-dir'],
                    'group-by': queryArg['group-by'],
                    'tag-type': queryArg['tag-type'],
                    'filter-by': queryArg['filter-by'],
                    'filter-value': queryArg['filter-value'],
                    tags: queryArg.tags,
                    'tag-operator': queryArg['tag-operator'],
                },
            }),
        }),
        btrsGetDiagramsUsingPost: build.mutation<BtrsGetDiagramsUsingPostApiResponse, BtrsGetDiagramsUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/bowties/${queryArg.status}`, method: 'POST', body: queryArg.bowTieListRequest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
