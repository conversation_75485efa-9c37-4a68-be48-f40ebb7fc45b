import { SsrsGetVendorScoreSyncStatusUsingGetApiResponse, SsrsGetVendorScoreSyncStatusUsingGetApiArg, SsrsTestSscTokenUsingPostApiResponse, SsrsTestSscTokenUsingPostApiArg, GenericCyberSecurityScoreResult } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        ssrsGetVendorScoreSyncStatusUsingGet: build.query<SsrsGetVendorScoreSyncStatusUsingGetApiResponse, SsrsGetVendorScoreSyncStatusUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/securityscore/status/${queryArg.buId}` }),
        }),
        ssrsTestSscTokenUsingPost: build.mutation<SsrsTestSscTokenUsingPostApiResponse, SsrsTestSscTokenUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/securityscore/verifytoken`, method: 'POST', body: queryArg.body }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
