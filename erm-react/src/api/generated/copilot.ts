import { CrcChatUsingPostApiResponse, CrcChatUsingPostApiArg, CrcChatStreamUsingPostApiResponse, CrcChatStreamUsingPostApiArg, CopilotChatRequest, FluxString } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        crcChatUsingPost: build.mutation<CrcChatUsingPostApiResponse, CrcChatUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/copilot/chat/prompt`, method: 'POST', body: queryArg.copilotChatRequest }),
        }),
        crcChatStreamUsingPost: build.mutation<CrcChatStreamUsingPostApiResponse, CrcChatStreamUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/copilot/chat/stream`,
                method: 'POST',
                body: queryArg.body,
                headers: {
                    Authorization: queryArg.authorization,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
