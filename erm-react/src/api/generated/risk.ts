import { RcrsCreateRiskCauseUsingGetApiResponse, RcrsCreateRiskCauseUsingGetApiArg, RcrsDeleteRiskCauseUsingDeleteApiResponse, RcrsDeleteRiskCauseUsingDeleteApiArg, ExportRegisterEntriesPost1ApiResponse, ExportRegisterEntriesPost1ApiArg, GetExportContentGet1ApiResponse, GetExportContentGet1ApiArg, GetExportProgressGet1ApiResponse, GetExportProgressGet1ApiArg, GetRiskCausesApiResponse, GetRiskCausesApiArg, RcrsGetRiskCauseUsingGetApiResponse, RcrsGetRiskCauseUsingGetApiArg, RcrsUpdateRiskCauseUsingPutApiResponse, RcrsUpdateRiskCauseUsingPutApiArg, GetRiskCausesHistoryApiResponse, GetRiskCausesHistoryApiArg, CrsCreateRiskControlUsingGetApiResponse, CrsCreateRiskControlUsingGetApiArg, CrsDeleteRiskControlUsingDeleteApiResponse, CrsDeleteRiskControlUsingDeleteApiArg, ExportRegisterEntriesPost12ApiResponse, ExportRegisterEntriesPost12ApiArg, GetExportContentGet12ApiResponse, GetExportContentGet12ApiArg, GetExportProgressGet12ApiResponse, GetExportProgressGet12ApiArg, GetRiskControlApiResponse, GetRiskControlApiArg, CrsGetControlUsingGetApiResponse, CrsGetControlUsingGetApiArg, CrsUpdateRiskControlUsingPutApiResponse, CrsUpdateRiskControlUsingPutApiArg, GetRiskControlHistoryApiResponse, GetRiskControlHistoryApiArg, RersCreateRiskEventUsingGetApiResponse, RersCreateRiskEventUsingGetApiArg, RersDeleteRiskEventUsingDeleteApiResponse, RersDeleteRiskEventUsingDeleteApiArg, ExportRegisterEntriesPost123ApiResponse, ExportRegisterEntriesPost123ApiArg, GetExportContentGet123ApiResponse, GetExportContentGet123ApiArg, GetExportProgressGet123ApiResponse, GetExportProgressGet123ApiArg, GetRiskEventsApiResponse, GetRiskEventsApiArg, RersGetRiskEventUsingGetApiResponse, RersGetRiskEventUsingGetApiArg, RersUpdateRiskEventUsingPutApiResponse, RersUpdateRiskEventUsingPutApiArg, GetRiskEventsHistoryApiResponse, GetRiskEventsHistoryApiArg, IdWithNameRest, RiskCauseBaseRest, DataSetExportConfiguration, ViewExpressionRest, DataSetExportRequest, ExportProgressResponse, PaginRestResultRiskCauseBaseRest, FilterContextRest, IdWithNameAndStatusRest, ControlBaseRest, PaginRestResultControlBaseRest, RiskEventBaseRest, PaginRestResultRiskEventBaseRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        rcrsCreateRiskCauseUsingGet: build.query<RcrsCreateRiskCauseUsingGetApiResponse, RcrsCreateRiskCauseUsingGetApiArg>({
            query: () => ({ url: `/v1/api/risk/causes` }),
        }),
        rcrsDeleteRiskCauseUsingDelete: build.mutation<RcrsDeleteRiskCauseUsingDeleteApiResponse, RcrsDeleteRiskCauseUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/causes`,
                method: 'DELETE',
                params: {
                    riskCauseIds: queryArg.riskCauseIds,
                    allowPartialDelete: queryArg.allowPartialDelete,
                },
            }),
        }),
        exportRegisterEntriesPost1: build.mutation<ExportRegisterEntriesPost1ApiResponse, ExportRegisterEntriesPost1ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/causes/export`,
                method: 'POST',
                body: queryArg.dataSetExportRequest,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    selectedItems: queryArg.selectedItems,
                    excludeSystemFields: queryArg.excludeSystemFields,
                },
            }),
        }),
        getExportContentGet1: build.query<GetExportContentGet1ApiResponse, GetExportContentGet1ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/causes/export/content`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        getExportProgressGet1: build.query<GetExportProgressGet1ApiResponse, GetExportProgressGet1ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/causes/export/progress`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        getRiskCauses: build.mutation<GetRiskCausesApiResponse, GetRiskCausesApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/causes/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
        }),
        rcrsGetRiskCauseUsingGet: build.query<RcrsGetRiskCauseUsingGetApiResponse, RcrsGetRiskCauseUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/risk/causes/${queryArg.riskCauseId}` }),
        }),
        rcrsUpdateRiskCauseUsingPut: build.mutation<RcrsUpdateRiskCauseUsingPutApiResponse, RcrsUpdateRiskCauseUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/risk/causes/${queryArg.riskCauseId}`, method: 'PUT', body: queryArg.riskCauseBaseRest }),
        }),
        getRiskCausesHistory: build.mutation<GetRiskCausesHistoryApiResponse, GetRiskCausesHistoryApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/causes/${queryArg.riskCauseId}/history`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        crsCreateRiskControlUsingGet: build.query<CrsCreateRiskControlUsingGetApiResponse, CrsCreateRiskControlUsingGetApiArg>({
            query: () => ({ url: `/v1/api/risk/controls` }),
        }),
        crsDeleteRiskControlUsingDelete: build.mutation<CrsDeleteRiskControlUsingDeleteApiResponse, CrsDeleteRiskControlUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/controls`,
                method: 'DELETE',
                params: {
                    riskControlIds: queryArg.riskControlIds,
                },
            }),
        }),
        exportRegisterEntriesPost12: build.mutation<ExportRegisterEntriesPost12ApiResponse, ExportRegisterEntriesPost12ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/controls/export`,
                method: 'POST',
                body: queryArg.dataSetExportRequest,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    selectedItems: queryArg.selectedItems,
                    excludeSystemFields: queryArg.excludeSystemFields,
                },
            }),
        }),
        getExportContentGet12: build.query<GetExportContentGet12ApiResponse, GetExportContentGet12ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/controls/export/content`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        getExportProgressGet12: build.query<GetExportProgressGet12ApiResponse, GetExportProgressGet12ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/controls/export/progress`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        getRiskControl: build.mutation<GetRiskControlApiResponse, GetRiskControlApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/controls/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
        }),
        crsGetControlUsingGet: build.query<CrsGetControlUsingGetApiResponse, CrsGetControlUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/risk/controls/${queryArg.riskControlId}` }),
        }),
        crsUpdateRiskControlUsingPut: build.mutation<CrsUpdateRiskControlUsingPutApiResponse, CrsUpdateRiskControlUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/risk/controls/${queryArg.riskControlId}`, method: 'PUT', body: queryArg.controlBaseRest }),
        }),
        getRiskControlHistory: build.mutation<GetRiskControlHistoryApiResponse, GetRiskControlHistoryApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/controls/${queryArg.riskControlId}/history`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        rersCreateRiskEventUsingGet: build.query<RersCreateRiskEventUsingGetApiResponse, RersCreateRiskEventUsingGetApiArg>({
            query: () => ({ url: `/v1/api/risk/events` }),
        }),
        rersDeleteRiskEventUsingDelete: build.mutation<RersDeleteRiskEventUsingDeleteApiResponse, RersDeleteRiskEventUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/events`,
                method: 'DELETE',
                params: {
                    riskEventIds: queryArg.riskEventIds,
                    allowPartialDelete: queryArg.allowPartialDelete,
                },
            }),
        }),
        exportRegisterEntriesPost123: build.mutation<ExportRegisterEntriesPost123ApiResponse, ExportRegisterEntriesPost123ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/events/export`,
                method: 'POST',
                body: queryArg.dataSetExportRequest,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    selectedItems: queryArg.selectedItems,
                    excludeSystemFields: queryArg.excludeSystemFields,
                },
            }),
        }),
        getExportContentGet123: build.query<GetExportContentGet123ApiResponse, GetExportContentGet123ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/events/export/content`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        getExportProgressGet123: build.query<GetExportProgressGet123ApiResponse, GetExportProgressGet123ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/events/export/progress`,
                params: {
                    exportid: queryArg.exportid,
                },
            }),
        }),
        getRiskEvents: build.mutation<GetRiskEventsApiResponse, GetRiskEventsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/events/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
        }),
        rersGetRiskEventUsingGet: build.query<RersGetRiskEventUsingGetApiResponse, RersGetRiskEventUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/risk/events/${queryArg.riskEventId}` }),
        }),
        rersUpdateRiskEventUsingPut: build.mutation<RersUpdateRiskEventUsingPutApiResponse, RersUpdateRiskEventUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/risk/events/${queryArg.riskEventId}`, method: 'PUT', body: queryArg.riskEventBaseRest }),
        }),
        getRiskEventsHistory: build.mutation<GetRiskEventsHistoryApiResponse, GetRiskEventsHistoryApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/risk/events/${queryArg.riskEventId}/history`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
