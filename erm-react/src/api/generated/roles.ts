import { GetRoles1ApiResponse, GetRoles1ApiArg, RrsCreateRoleUsingPostApiResponse, RrsCreateRoleUsingPostApiArg, RrsGetRolesUsingPostApiResponse, RrsGetRolesUsingPostApiArg, RrsGetRoleUsingGetApiResponse, RrsGetRoleUsingGetApiArg, RrsUpdateRoleUsingPutApiResponse, RrsUpdateRoleUsingPutApiArg, RrsDeleteRoleUsingDeleteApiResponse, RrsDeleteRoleUsingDeleteApiArg, RrsAddPermissionUsingPutApiResponse, RrsAddPermissionUsingPutApiArg, RrsRemovePermissionUsingDeleteApiResponse, RrsRemovePermissionUsingDeleteApiArg, PermissionRest, IdWithNameAndStatusRest, RoleRest, PaginRestResultRoleRest, ViewExpressionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        getRoles1: build.query<GetRoles1ApiResponse, GetRoles1ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/roles`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    name: queryArg.name,
                },
            }),
        }),
        rrsCreateRoleUsingPost: build.mutation<RrsCreateRoleUsingPostApiResponse, RrsCreateRoleUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/roles`, method: 'POST', body: queryArg.roleRest }),
        }),
        rrsGetRolesUsingPost: build.mutation<RrsGetRolesUsingPostApiResponse, RrsGetRolesUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/roles/search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        rrsGetRoleUsingGet: build.query<RrsGetRoleUsingGetApiResponse, RrsGetRoleUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/roles/${queryArg.roleId}` }),
        }),
        rrsUpdateRoleUsingPut: build.mutation<RrsUpdateRoleUsingPutApiResponse, RrsUpdateRoleUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/roles/${queryArg.roleId}`, method: 'PUT', body: queryArg.roleRest }),
        }),
        rrsDeleteRoleUsingDelete: build.mutation<RrsDeleteRoleUsingDeleteApiResponse, RrsDeleteRoleUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/roles/${queryArg.roleId}`, method: 'DELETE' }),
        }),
        rrsAddPermissionUsingPut: build.mutation<RrsAddPermissionUsingPutApiResponse, RrsAddPermissionUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/roles/${queryArg.roleId}/permission/${queryArg.permissionId}`, method: 'PUT' }),
        }),
        rrsRemovePermissionUsingDelete: build.mutation<RrsRemovePermissionUsingDeleteApiResponse, RrsRemovePermissionUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/roles/${queryArg.roleId}/permission/${queryArg.permissionId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
