import { VrsGetCurrentUserViewsUsingGetApiResponse, VrsGetCurrentUserViewsUsingGetApiArg, VrsCreateViewUsingPostApiResponse, VrsCreateViewUsingPostApiArg, VrsGetExpressionContextUsingGetApiResponse, VrsGetExpressionContextUsingGetApiArg, VrsGetViewUsingGetApiResponse, VrsGetViewUsingGetApiArg, VrsUpdateViewUsingPutApiResponse, VrsUpdateViewUsingPutApiArg, VrsDeleteViewUsingDeleteApiResponse, VrsDeleteViewUsingDeleteApiArg, ViewExpressionRest, ViewRest, ViewRestResponse } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        vrsGetCurrentUserViewsUsingGet: build.query<VrsGetCurrentUserViewsUsingGetApiResponse, VrsGetCurrentUserViewsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/views`,
                params: {
                    viewContext: queryArg.viewContext,
                    global: queryArg['global'],
                },
            }),
        }),
        vrsCreateViewUsingPost: build.mutation<VrsCreateViewUsingPostApiResponse, VrsCreateViewUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/views`, method: 'POST', body: queryArg.viewRest }),
        }),
        vrsGetExpressionContextUsingGet: build.query<VrsGetExpressionContextUsingGetApiResponse, VrsGetExpressionContextUsingGetApiArg>({
            query: () => ({ url: `/v1/api/views/expression/context` }),
        }),
        vrsGetViewUsingGet: build.query<VrsGetViewUsingGetApiResponse, VrsGetViewUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/views/${queryArg.viewId}` }),
        }),
        vrsUpdateViewUsingPut: build.mutation<VrsUpdateViewUsingPutApiResponse, VrsUpdateViewUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/views/${queryArg.viewId}`, method: 'PUT', body: queryArg.viewRest }),
        }),
        vrsDeleteViewUsingDelete: build.mutation<VrsDeleteViewUsingDeleteApiResponse, VrsDeleteViewUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/views/${queryArg.viewId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
