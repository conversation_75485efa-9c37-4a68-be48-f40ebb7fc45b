import { RdrsvCreateNewEntryUsingPostApiResponse, RdrsvCreateNewEntryUsingPostApiArg, RdrsvGetLinkedEntriesDataUsingGetApiResponse, RdrsvGetLinkedEntriesDataUsingGetApiArg, RdrsvUpdateRelatedLinkedEntriesUsingPutApiResponse, RdrsvUpdateRelatedLinkedEntriesUsingPutApiArg, GetRegisterEntriesPostApiResponse, GetRegisterEntriesPostApiArg, RdrsvGetEntryRelationTypesUsingGetApiResponse, RdrsvGetEntryRelationTypesUsingGetApiArg, RdrsvGetEntryByIdUsingGetApiResponse, RdrsvGetEntryByIdUsingGetApiArg, GetRegisterEntriesGetApiResponse, GetRegisterEntriesGetApiArg, RdrsvGetEntriesByIdsUsingGetApiResponse, RdrsvGetEntriesByIdsUsingGetApiArg, RdrsvGetAvailableFrameworkLevelsUsingGetApiResponse, RdrsvGetAvailableFrameworkLevelsUsingGetApiArg, GetRegisterEntriesSearchPostApiResponse, GetRegisterEntriesSearchPostApiArg, RdrsvGetEntriesGetUsingGetApiResponse, RdrsvGetEntriesGetUsingGetApiArg, RdrsvGetEntryByIdUsingGet1ApiResponse, RdrsvGetEntryByIdUsingGet1ApiArg, RdrsvUpdateEntryUsingPutApiResponse, RdrsvUpdateEntryUsingPutApiArg, RdrsvDeleteRegisterEntryUsingDeleteApiResponse, RdrsvDeleteRegisterEntryUsingDeleteApiArg, RdrsvCreateEntryCopyUsingPostApiResponse, RdrsvCreateEntryCopyUsingPostApiArg, RdrsvCreateAndLinkUsingPostApiResponse, RdrsvCreateAndLinkUsingPostApiArg, RdrsvAddLinksUsingPutApiResponse, RdrsvAddLinksUsingPutApiArg, RdrsvUpdateStatusUsingPutApiResponse, RdrsvUpdateStatusUsingPutApiArg, RdrsvTransitionEntryUsingPutApiResponse, RdrsvTransitionEntryUsingPutApiArg, RdrsvGetTableEntriesUsingGetApiResponse, RdrsvGetTableEntriesUsingGetApiArg, RdrsvRemoveLinksUsingPutApiResponse, RdrsvRemoveLinksUsingPutApiArg, IdWithNameRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest, LinkData, RelatedEntryLinkRest, PaginRestResultRegisterDataRest, ViewExpressionRest, RelationTypeRest, FrameworkLevel, RegisterStateRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        rdrsvCreateNewEntryUsingPost: build.mutation<RdrsvCreateNewEntryUsingPostApiResponse, RdrsvCreateNewEntryUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries`,
                method: 'POST',
                body: queryArg.registerDataRest,
                params: {
                    initial: queryArg.initial,
                },
            }),
        }),
        rdrsvGetLinkedEntriesDataUsingGet: build.query<RdrsvGetLinkedEntriesDataUsingGetApiResponse, RdrsvGetLinkedEntriesDataUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/links/${queryArg.tableName}/${queryArg.entryId}/data`,
                params: {
                    linkType: queryArg.linkType,
                },
            }),
        }),
        rdrsvUpdateRelatedLinkedEntriesUsingPut: build.mutation<
            RdrsvUpdateRelatedLinkedEntriesUsingPutApiResponse,
            RdrsvUpdateRelatedLinkedEntriesUsingPutApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/entries/links/${queryArg.tableName}/${queryArg.entryId}/related`, method: 'PUT', body: queryArg.body }),
        }),
        getRegisterEntriesPost: build.query<GetRegisterEntriesPostApiResponse, GetRegisterEntriesPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/metric-search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    myTaskStates: queryArg.myTaskStates,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    isSuggestion: queryArg.isSuggestion,
                    stateId: queryArg.stateId,
                    metricId: queryArg.metricId,
                },
            }),
        }),
        rdrsvGetEntryRelationTypesUsingGet: build.query<RdrsvGetEntryRelationTypesUsingGetApiResponse, RdrsvGetEntryRelationTypesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/entries/relations` }),
        }),
        rdrsvGetEntryByIdUsingGet: build.query<RdrsvGetEntryByIdUsingGetApiResponse, RdrsvGetEntryByIdUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/entries/tableName/${queryArg.tableName}/${queryArg.entryId}` }),
        }),
        getRegisterEntriesGet: build.query<GetRegisterEntriesGetApiResponse, GetRegisterEntriesGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}`,
                params: {
                    keys: queryArg.keys,
                    values: queryArg.values,
                },
            }),
        }),
        rdrsvGetEntriesByIdsUsingGet: build.query<RdrsvGetEntriesByIdsUsingGetApiResponse, RdrsvGetEntriesByIdsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/find`,
                params: {
                    entryIds: queryArg.entryIds,
                },
            }),
        }),
        rdrsvGetAvailableFrameworkLevelsUsingGet: build.query<
            RdrsvGetAvailableFrameworkLevelsUsingGetApiResponse,
            RdrsvGetAvailableFrameworkLevelsUsingGetApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/entries/${queryArg.regId}/fw-levels` }),
        }),
        getRegisterEntriesSearchPost: build.query<GetRegisterEntriesSearchPostApiResponse, GetRegisterEntriesSearchPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    myTaskStates: queryArg.myTaskStates,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    isSuggestion: queryArg.isSuggestion,
                    stateId: queryArg.stateId,
                    parentEntryId: queryArg.parentEntryId,
                    subtableColumn: queryArg.subtableColumn,
                    parentTableName: queryArg.parentTableName,
                    fwLevelGrouping: queryArg.fwLevelGrouping,
                },
            }),
        }),
        rdrsvGetEntriesGetUsingGet: build.query<RdrsvGetEntriesGetUsingGetApiResponse, RdrsvGetEntriesGetUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/search/all`,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    myTaskStates: queryArg.myTaskStates,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    isSuggestion: queryArg.isSuggestion,
                    payload: queryArg.payload,
                    stateId: queryArg.stateId,
                    parentEntryId: queryArg.parentEntryId,
                    subtableColumn: queryArg.subtableColumn,
                    parentTableName: queryArg.parentTableName,
                    fwLevelGrouping: queryArg.fwLevelGrouping,
                },
            }),
        }),
        rdrsvGetEntryByIdUsingGet1: build.query<RdrsvGetEntryByIdUsingGet1ApiResponse, RdrsvGetEntryByIdUsingGet1ApiArg>({
            query: (queryArg) => ({ url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}` }),
        }),
        rdrsvUpdateEntryUsingPut: build.mutation<RdrsvUpdateEntryUsingPutApiResponse, RdrsvUpdateEntryUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}`,
                method: 'PUT',
                body: queryArg.registerDataRest,
                params: {
                    evalFormulas: queryArg.evalFormulas,
                    lockId: queryArg.lockId,
                    deviceId: queryArg.deviceId,
                    ignoreOfflineLock: queryArg.ignoreOfflineLock,
                    showAdvancedFieldValidationErrors: queryArg.showAdvancedFieldValidationErrors,
                },
            }),
        }),
        rdrsvDeleteRegisterEntryUsingDelete: build.mutation<RdrsvDeleteRegisterEntryUsingDeleteApiResponse, RdrsvDeleteRegisterEntryUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}`, method: 'DELETE' }),
        }),
        rdrsvCreateEntryCopyUsingPost: build.mutation<RdrsvCreateEntryCopyUsingPostApiResponse, RdrsvCreateEntryCopyUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}/copy`, method: 'POST' }),
        }),
        rdrsvCreateAndLinkUsingPost: build.mutation<RdrsvCreateAndLinkUsingPostApiResponse, RdrsvCreateAndLinkUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}/link`,
                method: 'POST',
                body: queryArg.registerDataRest,
                params: {
                    linkColumn: queryArg.linkColumn,
                    evaluateSubtableFormulas: queryArg.evaluateSubtableFormulas,
                    evaluatePrimaryFormulas: queryArg.evaluatePrimaryFormulas,
                },
            }),
        }),
        rdrsvAddLinksUsingPut: build.mutation<RdrsvAddLinksUsingPutApiResponse, RdrsvAddLinksUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}/link`,
                method: 'PUT',
                body: queryArg.body,
                params: {
                    linkColumn: queryArg.linkColumn,
                },
            }),
        }),
        rdrsvUpdateStatusUsingPut: build.mutation<RdrsvUpdateStatusUsingPutApiResponse, RdrsvUpdateStatusUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}/status`,
                method: 'PUT',
                params: {
                    status: queryArg.status,
                },
            }),
        }),
        rdrsvTransitionEntryUsingPut: build.mutation<RdrsvTransitionEntryUsingPutApiResponse, RdrsvTransitionEntryUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}/status/transition`,
                method: 'PUT',
                body: queryArg.body,
                params: {
                    triggerWorkflows: queryArg.triggerWorkflows,
                },
            }),
        }),
        rdrsvGetTableEntriesUsingGet: build.query<RdrsvGetTableEntriesUsingGetApiResponse, RdrsvGetTableEntriesUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}/table/${queryArg.columnName}` }),
        }),
        rdrsvRemoveLinksUsingPut: build.mutation<RdrsvRemoveLinksUsingPutApiResponse, RdrsvRemoveLinksUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/entries/${queryArg.regId}/${queryArg.entryId}/unlink`,
                method: 'PUT',
                body: queryArg.body,
                params: {
                    linkColumn: queryArg.linkColumn,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
