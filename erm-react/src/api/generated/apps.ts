import { GetApplicationsApiResponse, GetApplicationsApiArg, ArsCreateNewUsingPostApiResponse, ArsCreateNewUsingPostApiArg, ArsFindyIdUsingGetApiResponse, ArsFindyIdUsingGetApiArg, ArsUpdateUsingPutApiResponse, ArsUpdateUsingPutApiArg, ArsDeleteUsingDeleteApiResponse, ArsDeleteUsingDeleteApiArg, ApplicationRest, PaginRestResultApplicationRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        getApplications: build.query<GetApplicationsApiResponse, GetApplicationsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/apps`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                },
            }),
        }),
        arsCreateNewUsingPost: build.mutation<ArsCreateNewUsingPostApiResponse, ArsCreateNewUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/apps`, method: 'POST', body: queryArg.applicationRest }),
        }),
        arsFindyIdUsingGet: build.query<ArsFindyIdUsingGetApiResponse, ArsFindyIdUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/apps/${queryArg.appId}` }),
        }),
        arsUpdateUsingPut: build.mutation<ArsUpdateUsingPutApiResponse, ArsUpdateUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/apps/${queryArg.appId}`, method: 'PUT', body: queryArg.applicationRest }),
        }),
        arsDeleteUsingDelete: build.mutation<ArsDeleteUsingDeleteApiResponse, ArsDeleteUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/apps/${queryArg.appId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
