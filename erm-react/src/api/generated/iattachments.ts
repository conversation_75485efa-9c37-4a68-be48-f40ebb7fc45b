import { ArsiUploadAttachmentUsingPostApiResponse, ArsiUploadAttachmentUsingPostApiArg, AttachmentsRestInternal, EnumerationString, ServletInputStream, Locale, EnumerationLocale, BufferedReader, Annotation, Package, ModuleDescriptor, ModuleLayer, Module, ClassLoader, FilterRegistration, JspPropertyGroupDescriptor, TaglibDescriptor, JspConfigDescriptor, ServletRegistration, EnumerationServlet, SessionCookieConfig, ServletContext, ServletRequest, ServletOutputStream, PrintWriter, ServletResponse, AsyncContext, Cookie, HttpServletMapping, InputStream, Part, StringBuffer, HttpSessionContext, HttpSession, Principal, HttpServletRequest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        arsiUploadAttachmentUsingPost: build.mutation<ArsiUploadAttachmentUsingPostApiResponse, ArsiUploadAttachmentUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iattachments`,
                method: 'POST',
                body: queryArg.httpServletRequest,
                params: {
                    linkedId: queryArg.linkedId,
                    linkedTable: queryArg.linkedTable,
                    linkedColumn: queryArg.linkedColumn,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
