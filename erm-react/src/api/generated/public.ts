import { GetIdPsApiResponse, GetIdPsApiArg, ExternalIdpConfigurationRest, ExternalSamlConfigurationRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        getIdPs: build.query<GetIdPsApiResponse, GetIdPsApiArg>({
            query: () => ({ url: `/v1/api/public/saml/idps` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
