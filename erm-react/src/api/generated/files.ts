import { FersGetAllowedExtensionsUsingGetApiResponse, FersGetAllowedExtensionsUsingGetApiArg, FileExtensionRest, PaginRestResultFileExtensionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        fersGetAllowedExtensionsUsingGet: build.query<FersGetAllowedExtensionsUsingGetApiResponse, FersGetAllowedExtensionsUsingGetApiArg>({
            query: () => ({ url: `/v1/api/files/extensions/allowed` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
