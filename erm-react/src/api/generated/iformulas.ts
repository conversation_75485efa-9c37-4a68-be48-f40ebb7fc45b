import { FrsiEvaluateFormulaColumnUsingPostApiResponse, FrsiEvaluateFormulaColumnUsingPostApiArg, IdWithNameRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        frsiEvaluateFormulaColumnUsingPost: build.mutation<FrsiEvaluateFormulaColumnUsingPostApiResponse, FrsiEvaluateFormulaColumnUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iformulas/${queryArg.colName}`, method: 'POST', body: queryArg.registerDataRest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
