import { FrsvEvaluateAllFormulasUsingPostApiResponse, FrsvEvaluateAllFormulasUsingPostApiArg, FrsvEvaluateFormulaColumnUsingPostApiResponse, FrsvEvaluateFormulaColumnUsingPostApiArg, IdWithNameRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        frsvEvaluateAllFormulasUsingPost: build.mutation<FrsvEvaluateAllFormulasUsingPostApiResponse, FrsvEvaluateAllFormulasUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/formulas`, method: 'POST', body: queryArg.registerDataRest }),
        }),
        frsvEvaluateFormulaColumnUsingPost: build.mutation<FrsvEvaluateFormulaColumnUsingPostApiResponse, FrsvEvaluateFormulaColumnUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/formulas/${queryArg.colName}`, method: 'POST', body: queryArg.registerDataRest }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
