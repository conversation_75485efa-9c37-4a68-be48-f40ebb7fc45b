import { RcGetRolesUsingGetApiResponse, RcGetRolesUsingGetApiArg, RcCreateRoleUsingPostApiResponse, RcCreateRoleUsingPostApiArg, RcGetRoleCountUsingGetApiResponse, RcGetRoleCountUsingGetApiArg, RcGetRepositoryHierarchyUsingGetApiResponse, RcGetRepositoryHierarchyUsingGetApiArg, RcGetHomepageTypesUsingGetApiResponse, RcGetHomepageTypesUsingGetApiArg, RcImportRoleUsingPostApiResponse, RcImportRoleUsingPostApiArg, RcGetApplicationModulesUsingGetApiResponse, RcGetApplicationModulesUsingGetApiArg, RcGetApplicationModulesUsingGet1ApiResponse, RcGetApplicationModulesUsingGet1ApiArg, RcGetRegisterModulesUsingGetApiResponse, RcGetRegisterModulesUsingGetApiArg, RcGetRegisterModuleContentUsingGetApiResponse, RcGetRegisterModuleContentUsingGetApiArg, RcHelloUsingGetApiResponse, RcHelloUsingGetApiArg, RcGetUserTypesUsingGetApiResponse, RcGetUserTypesUsingGetApiArg, RcGetRoleByIdUsingGetApiResponse, RcGetRoleByIdUsingGetApiArg, RcExportRoleUsingGetApiResponse, RcExportRoleUsingGetApiArg, RcUpdateRoleUsingPutApiResponse, RcUpdateRoleUsingPutApiArg, RcDeleteRoleUsingDeleteApiResponse, RcDeleteRoleUsingDeleteApiArg, RcDuplicateRoleUsingPostApiResponse, RcDuplicateRoleUsingPostApiArg, RoleListView, PagingResponseRoleListView, PermissionKey, RoleWithPermissions, RepositoryNode, Permission, Role, ApplicationModule, PermissionsHierarchy, PermissionPlaceholder, PermissionGroup, SubtablePlaceholder, SectionPlaceholder, RegisterModuleContent, RegisterModule, UserType, RoleView, HttpEntityObject } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        rcGetRolesUsingGet: build.query<RcGetRolesUsingGetApiResponse, RcGetRolesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iroles`,
                params: {
                    query: queryArg.query,
                    type: queryArg['type'],
                    'page-size': queryArg['page-size'],
                    'page-number': queryArg['page-number'],
                    sort: queryArg.sort,
                    dir: queryArg.dir,
                },
            }),
        }),
        rcCreateRoleUsingPost: build.mutation<RcCreateRoleUsingPostApiResponse, RcCreateRoleUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iroles`, method: 'POST', body: queryArg.roleWithPermissions }),
        }),
        rcGetRoleCountUsingGet: build.query<RcGetRoleCountUsingGetApiResponse, RcGetRoleCountUsingGetApiArg>({
            query: () => ({ url: `/v1/api/iroles/count` }),
        }),
        rcGetRepositoryHierarchyUsingGet: build.query<RcGetRepositoryHierarchyUsingGetApiResponse, RcGetRepositoryHierarchyUsingGetApiArg>({
            query: () => ({ url: `/v1/api/iroles/dashboards` }),
        }),
        rcGetHomepageTypesUsingGet: build.query<RcGetHomepageTypesUsingGetApiResponse, RcGetHomepageTypesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/iroles/homepages` }),
        }),
        rcImportRoleUsingPost: build.mutation<RcImportRoleUsingPostApiResponse, RcImportRoleUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iroles/import`, method: 'POST', body: queryArg.role }),
        }),
        rcGetApplicationModulesUsingGet: build.query<RcGetApplicationModulesUsingGetApiResponse, RcGetApplicationModulesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/iroles/modules` }),
        }),
        rcGetApplicationModulesUsingGet1: build.query<RcGetApplicationModulesUsingGet1ApiResponse, RcGetApplicationModulesUsingGet1ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iroles/modules/all`,
                params: {
                    hash: queryArg.hash,
                },
            }),
        }),
        rcGetRegisterModulesUsingGet: build.query<RcGetRegisterModulesUsingGetApiResponse, RcGetRegisterModulesUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iroles/modules/${queryArg.appId}` }),
        }),
        rcGetRegisterModuleContentUsingGet: build.query<RcGetRegisterModuleContentUsingGetApiResponse, RcGetRegisterModuleContentUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iroles/modules/${queryArg.registerId}/content` }),
        }),
        rcHelloUsingGet: build.query<RcHelloUsingGetApiResponse, RcHelloUsingGetApiArg>({
            query: () => ({ url: `/v1/api/iroles/ping` }),
        }),
        rcGetUserTypesUsingGet: build.query<RcGetUserTypesUsingGetApiResponse, RcGetUserTypesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/iroles/types` }),
        }),
        rcGetRoleByIdUsingGet: build.query<RcGetRoleByIdUsingGetApiResponse, RcGetRoleByIdUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iroles/${queryArg.id}`,
                params: {
                    loadPermissions: queryArg.loadPermissions,
                },
            }),
        }),
        rcExportRoleUsingGet: build.query<RcExportRoleUsingGetApiResponse, RcExportRoleUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iroles/${queryArg.id}/export` }),
        }),
        rcUpdateRoleUsingPut: build.mutation<RcUpdateRoleUsingPutApiResponse, RcUpdateRoleUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iroles/${queryArg.roleId}`, method: 'PUT', body: queryArg.roleWithPermissions }),
        }),
        rcDeleteRoleUsingDelete: build.mutation<RcDeleteRoleUsingDeleteApiResponse, RcDeleteRoleUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iroles/${queryArg.roleId}`,
                method: 'DELETE',
                params: {
                    force: queryArg.force,
                },
            }),
        }),
        rcDuplicateRoleUsingPost: build.mutation<RcDuplicateRoleUsingPostApiResponse, RcDuplicateRoleUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iroles/${queryArg.sourceId}/duplicate`, method: 'POST', body: queryArg.body }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
