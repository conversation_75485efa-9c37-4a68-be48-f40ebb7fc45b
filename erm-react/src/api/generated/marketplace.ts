import { MrsGetUsedCategoriesUsingGetApiRespo<PERSON>, MrsGetUsedCategoriesUsingGetApiArg, MrsGetPackageConflictsUsingGetApiResponse, MrsGetPackageConflictsUsingGetApiArg, MrsGetFrameworkLinkPackageConflictsUsingGetApiResponse, MrsGetFrameworkLinkPackageConflictsUsingGetApiArg, MrsGetActionRegistersUsingGetApiResponse, MrsGetActionRegistersUsingGetApiArg, MrsGetInstallProgressUsingGetApiResponse, MrsGetInstallProgressUsingGetApiArg, MrsInstallPackageUsingPutApiResponse, MrsInstallPackageUsingPutApiArg, MrsGetAllInstalledPackagesUsingGetApiResponse, MrsGetAllInstalledPackagesUsingGetApiArg, MrsGetAllInstalledPackagesUsingGet1ApiResponse, MrsGetAllInstalledPackagesUsingGet1<PERSON><PERSON><PERSON><PERSON>, MrsGetInstalledContentUsingGetApiResponse, MrsGetInstalledContentUsingGetApiArg, MrsGetExistingInstallItemUsingGetApiResponse, MrsGetExistingInstallItemUsingGetApiArg, MrsGetUninstallProgressUsingGetApiResponse, MrsGetUninstallProgressUsingGetApiArg, MrsUninstallPackageUsingPutApiResponse, MrsUninstallPackageUsingPutApiArg, MrsGetSubscriptionPlanUsingGetApiResponse, MrsGetSubscriptionPlanUsingGetApiArg, CentralLibraryFieldDetailDto, ScaleDetailDto, SectionDetailDto, StructureRegisterDetailDto, DataProcessingMessage, FrameworkLinkDetailDto, SimpleRegisterDto, ProcessingMessage, ImportedItemInfoDto, ImportedModuleInfoDto, MultiImportStatusDto, RegisterMappingData, AnalyticsAssetDto, RepositoryDto, FrameworkDetailDto, RegisterDetailDto, PackageContentDto, ScreenshotDto, SubscriptionDto, PackageDetailsDto, IdWithNameRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        mrsGetUsedCategoriesUsingGet: build.query<MrsGetUsedCategoriesUsingGetApiResponse, MrsGetUsedCategoriesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/marketplace/categories` }),
        }),
        mrsGetPackageConflictsUsingGet: build.query<MrsGetPackageConflictsUsingGetApiResponse, MrsGetPackageConflictsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/marketplace/packages/conflict/${queryArg.uuid}`,
                params: {
                    version: queryArg.version,
                },
            }),
        }),
        mrsGetFrameworkLinkPackageConflictsUsingGet: build.query<
            MrsGetFrameworkLinkPackageConflictsUsingGetApiResponse,
            MrsGetFrameworkLinkPackageConflictsUsingGetApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/marketplace/packages/frameworklink/conflict/${queryArg.uuid}`,
                params: {
                    version: queryArg.version,
                },
            }),
        }),
        mrsGetActionRegistersUsingGet: build.query<MrsGetActionRegistersUsingGetApiResponse, MrsGetActionRegistersUsingGetApiArg>({
            query: () => ({ url: `/v1/api/marketplace/packages/install/actionregisters` }),
        }),
        mrsGetInstallProgressUsingGet: build.query<MrsGetInstallProgressUsingGetApiResponse, MrsGetInstallProgressUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/marketplace/packages/install/${queryArg.processId}` }),
        }),
        mrsInstallPackageUsingPut: build.mutation<MrsInstallPackageUsingPutApiResponse, MrsInstallPackageUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/marketplace/packages/install/${queryArg.uuid}`,
                method: 'PUT',
                body: queryArg.registerMappingData,
                params: {
                    version: queryArg.version,
                },
            }),
        }),
        mrsGetAllInstalledPackagesUsingGet: build.query<MrsGetAllInstalledPackagesUsingGetApiResponse, MrsGetAllInstalledPackagesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/marketplace/packages/installed`,
                params: {
                    category: queryArg.category,
                    'sort-by': queryArg['sort-by'],
                    'sort-dir': queryArg['sort-dir'],
                },
            }),
        }),
        mrsGetAllInstalledPackagesUsingGet1: build.query<MrsGetAllInstalledPackagesUsingGet1ApiResponse, MrsGetAllInstalledPackagesUsingGet1ApiArg>({
            query: () => ({ url: `/v1/api/marketplace/packages/installed/count` }),
        }),
        mrsGetInstalledContentUsingGet: build.query<MrsGetInstalledContentUsingGetApiResponse, MrsGetInstalledContentUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/marketplace/packages/installed/${queryArg.uuid}`,
                params: {
                    version: queryArg.version,
                },
            }),
        }),
        mrsGetExistingInstallItemUsingGet: build.query<MrsGetExistingInstallItemUsingGetApiResponse, MrsGetExistingInstallItemUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/marketplace/packages/item`,
                params: {
                    label: queryArg.label,
                    installItemType: queryArg.installItemType,
                },
            }),
        }),
        mrsGetUninstallProgressUsingGet: build.query<MrsGetUninstallProgressUsingGetApiResponse, MrsGetUninstallProgressUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/marketplace/packages/uninstall/${queryArg.processId}` }),
        }),
        mrsUninstallPackageUsingPut: build.mutation<MrsUninstallPackageUsingPutApiResponse, MrsUninstallPackageUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/marketplace/packages/uninstall/${queryArg.uuid}`, method: 'PUT' }),
        }),
        mrsGetSubscriptionPlanUsingGet: build.query<MrsGetSubscriptionPlanUsingGetApiResponse, MrsGetSubscriptionPlanUsingGetApiArg>({
            query: () => ({ url: `/v1/api/marketplace/subscription` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
