import { UmrsGetMenuUsingGetApiResponse, UmrsGetMenuUsingGetApiArg, UmrsSetMenuUsingPutApiResponse, UmrsSetMenuUsingPutApiArg, UmrsGetAvailableActionsUsingGetApiResponse, UmrsGetAvailableActionsUsingGetApiArg, UmrsActivateTemplateUsingPutApiResponse, UmrsActivateTemplateUsingPutApiArg, UmrsSaveDefaultDashboardUsingPutApiResponse, UmrsSaveDefaultDashboardUsingPutApiArg, UmrsDeleteTemplateUsingDeleteApiResponse, UmrsDeleteTemplateUsingDeleteApiArg, UmrsDeleteMenuItemUsingDeleteApiResponse, UmrsDeleteMenuItemUsingDeleteApiArg, UmrsGetMenuForManagerUsingGetApiResponse, UmrsGetMenuForManagerUsingGetApiArg, UmrsGetMenuTemplatesUsingGetApiResponse, UmrsGetMenuTemplatesUsingGetApiArg, UmrsSaveNewTemplateUsingPostApiResponse, UmrsSaveNewTemplateUsingPostApiArg, UmrsGetRegistersForApplicationUsingGetApiResponse, UmrsGetRegistersForApplicationUsingGetApiArg, UmrsResetMenuUsingPutApiResponse, UmrsResetMenuUsingPutApiArg, Configuration, LocalizedString, ParamsType, Item, Menu, RestMessage, RegisterAction } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        umrsGetMenuUsingGet: build.query<UmrsGetMenuUsingGetApiResponse, UmrsGetMenuUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/menu`,
                params: {
                    fillDefaultIcons: queryArg.fillDefaultIcons,
                },
            }),
        }),
        umrsSetMenuUsingPut: build.mutation<UmrsSetMenuUsingPutApiResponse, UmrsSetMenuUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/menu`, method: 'PUT', body: queryArg.menu }),
        }),
        umrsGetAvailableActionsUsingGet: build.query<UmrsGetAvailableActionsUsingGetApiResponse, UmrsGetAvailableActionsUsingGetApiArg>({
            query: () => ({ url: `/v1/api/menu/actions` }),
        }),
        umrsActivateTemplateUsingPut: build.mutation<UmrsActivateTemplateUsingPutApiResponse, UmrsActivateTemplateUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/menu/activate-template`, method: 'PUT', body: queryArg.body }),
        }),
        umrsSaveDefaultDashboardUsingPut: build.mutation<UmrsSaveDefaultDashboardUsingPutApiResponse, UmrsSaveDefaultDashboardUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/menu/dashboard`, method: 'PUT', body: queryArg.body }),
        }),
        umrsDeleteTemplateUsingDelete: build.mutation<UmrsDeleteTemplateUsingDeleteApiResponse, UmrsDeleteTemplateUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/menu/delete-template`, method: 'DELETE', body: queryArg.body }),
        }),
        umrsDeleteMenuItemUsingDelete: build.mutation<UmrsDeleteMenuItemUsingDeleteApiResponse, UmrsDeleteMenuItemUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/menu/item/${queryArg.itemId}`, method: 'DELETE' }),
        }),
        umrsGetMenuForManagerUsingGet: build.query<UmrsGetMenuForManagerUsingGetApiResponse, UmrsGetMenuForManagerUsingGetApiArg>({
            query: () => ({ url: `/v1/api/menu/manager` }),
        }),
        umrsGetMenuTemplatesUsingGet: build.query<UmrsGetMenuTemplatesUsingGetApiResponse, UmrsGetMenuTemplatesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/menu/menu-templates` }),
        }),
        umrsSaveNewTemplateUsingPost: build.mutation<UmrsSaveNewTemplateUsingPostApiResponse, UmrsSaveNewTemplateUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/menu/new-template`, method: 'POST', body: queryArg.body }),
        }),
        umrsGetRegistersForApplicationUsingGet: build.query<UmrsGetRegistersForApplicationUsingGetApiResponse, UmrsGetRegistersForApplicationUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/menu/register/actions/${queryArg.appId}` }),
        }),
        umrsResetMenuUsingPut: build.mutation<UmrsResetMenuUsingPutApiResponse, UmrsResetMenuUsingPutApiArg>({
            query: () => ({ url: `/v1/api/menu/reset`, method: 'PUT' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
