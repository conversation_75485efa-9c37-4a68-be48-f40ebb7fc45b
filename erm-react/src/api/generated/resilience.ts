import { RrsGetLinksUsingPostApiResponse, RrsGetLinksUsingPostApiArg, RrsGetProcessesUsingPostApiResponse, RrsGetProcessesUsingPostApiArg, RrsGetResourcesUsingPostApiResponse, RrsGetResourcesUsingPostApiArg, RrsGetAffectedEntriesUsingGetApiResponse, RrsGetAffectedEntriesUsingGetApiArg, RrsGetDefinitionUsingGetApiResponse, RrsGetDefinitionUsingGetApiArg, RrsUpdateDiagramDefinitionUsingPutApiResponse, RrsUpdateDiagramDefinitionUsingPutApiArg, RrsGetDiagramUsingGetApiResponse, RrsGetDiagramUsingGetApiArg, RrsCreateDiagramUsingPostApiResponse, RrsCreateDiagramUsingPostApiArg, RrsDeleteDiagramUsingDeleteApiResponse, RrsDeleteDiagramUsingDeleteApiArg, RrsCopyDiagramUsingPutApiResponse, RrsCopyDiagramUsingPutApiArg, RrsUpdateDiagramUsingPutApiResponse, RrsUpdateDiagramUsingPutApiArg, RrsGetTerminologyUsingGetApiResponse, RrsGetTerminologyUsingGetApiArg, LinkRequestResponse, IsUsingResInfo, ProcInfoRest, ProcResultRest, ProcResultWrapperRest, ResourceRequest, UsedByProcessInfo, ResourceInfoRest, ResourcesResultRest, ResourcesResultWrapperRest, ProcessRequest, IdWithNameRest, OperationalResilienceRegister, ResilienceColumnDef, OperationalResilienceResourceRegister, OperationalResilienceDefinition, JsonNode, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest, ProcessResource, StructureProcess, DiagramStructure, ResilienceDiagramRest, ResilienceDiagramUpdate, ModuleTerminology } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        rrsGetLinksUsingPost: build.mutation<RrsGetLinksUsingPostApiResponse, RrsGetLinksUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/resilience/data/links`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    reverse: queryArg.reverse,
                    filterIds: queryArg.filterIds,
                },
            }),
        }),
        rrsGetProcessesUsingPost: build.mutation<RrsGetProcessesUsingPostApiResponse, RrsGetProcessesUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/resilience/data/${queryArg.primaryId}/processes`, method: 'POST', body: queryArg.body }),
        }),
        rrsGetResourcesUsingPost: build.mutation<RrsGetResourcesUsingPostApiResponse, RrsGetResourcesUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/resilience/data/${queryArg.primaryId}/resources`, method: 'POST', body: queryArg.body }),
        }),
        rrsGetAffectedEntriesUsingGet: build.query<RrsGetAffectedEntriesUsingGetApiResponse, RrsGetAffectedEntriesUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/resilience/data/${queryArg.processId}/impact` }),
        }),
        rrsGetDefinitionUsingGet: build.query<RrsGetDefinitionUsingGetApiResponse, RrsGetDefinitionUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/resilience/definition`,
                params: {
                    extended: queryArg.extended,
                },
            }),
        }),
        rrsUpdateDiagramDefinitionUsingPut: build.mutation<RrsUpdateDiagramDefinitionUsingPutApiResponse, RrsUpdateDiagramDefinitionUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/resilience/definition`, method: 'PUT', body: queryArg.operationalResilienceDefinition }),
        }),
        rrsGetDiagramUsingGet: build.query<RrsGetDiagramUsingGetApiResponse, RrsGetDiagramUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/resilience/diagram`,
                params: {
                    diagramId: queryArg.diagramId,
                    primaryId: queryArg.primaryId,
                },
            }),
        }),
        rrsCreateDiagramUsingPost: build.mutation<RrsCreateDiagramUsingPostApiResponse, RrsCreateDiagramUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/resilience/diagram`, method: 'POST', body: queryArg.resilienceDiagramRest }),
        }),
        rrsDeleteDiagramUsingDelete: build.mutation<RrsDeleteDiagramUsingDeleteApiResponse, RrsDeleteDiagramUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/resilience/diagram`,
                method: 'DELETE',
                params: {
                    diagramId: queryArg.diagramId,
                    primaryId: queryArg.primaryId,
                },
            }),
        }),
        rrsCopyDiagramUsingPut: build.mutation<RrsCopyDiagramUsingPutApiResponse, RrsCopyDiagramUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/resilience/diagram/copy`,
                method: 'PUT',
                body: queryArg.body,
                params: {
                    diagramId: queryArg.diagramId,
                    primaryId: queryArg.primaryId,
                },
            }),
        }),
        rrsUpdateDiagramUsingPut: build.mutation<RrsUpdateDiagramUsingPutApiResponse, RrsUpdateDiagramUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/resilience/diagram/${queryArg.diagramId}`, method: 'PUT', body: queryArg.resilienceDiagramUpdate }),
        }),
        rrsGetTerminologyUsingGet: build.query<RrsGetTerminologyUsingGetApiResponse, RrsGetTerminologyUsingGetApiArg>({
            query: () => ({ url: `/v1/api/resilience/terminology` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
