import { BursiGetBusinessUnitsTreeUsingGetApiResponse, BursiGetBusinessUnitsTreeUsingGetApiArg, BursiGetRootUsingGetApiResponse, BursiGetRootUsingGetApiArg, BursiGetRootSimpleUsingGetApiResponse, BursiGetRootSimpleUsingGetApiArg, BursiGetRootTreeUsingGetApiResponse, BursiGetRootTreeUsingGetApiArg, BursiGetBuUsingGetApiResponse, BursiGetBuUsingGetApiArg, BursiDeleteBuUsingDeleteApiResponse, BursiDeleteBuUsingDeleteApiArg, BursiGetBuSimpleUsingGetApiResponse, BursiGetBuSimpleUsingGetApiArg, BursiGetBuTreeUsingGetApiResponse, BursiGetBuTreeUsingGetApiArg, BusinessUnitSimpleRestInternal, BusinessUnitSimpleTreeFilteredRestInternal, IdWithNameRest, TagTypeRest, TagRest, BusinessUnitRestInternal } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        bursiGetBusinessUnitsTreeUsingGet: build.query<BursiGetBusinessUnitsTreeUsingGetApiResponse, BursiGetBusinessUnitsTreeUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibusinessunits`,
                params: {
                    value: queryArg.value,
                    includeArchived: queryArg.includeArchived,
                },
            }),
        }),
        bursiGetRootUsingGet: build.query<BursiGetRootUsingGetApiResponse, BursiGetRootUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibusinessunits/root`,
                params: {
                    includeArchived: queryArg.includeArchived,
                },
            }),
        }),
        bursiGetRootSimpleUsingGet: build.query<BursiGetRootSimpleUsingGetApiResponse, BursiGetRootSimpleUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibusinessunits/root/simple`,
                params: {
                    includeArchived: queryArg.includeArchived,
                },
            }),
        }),
        bursiGetRootTreeUsingGet: build.query<BursiGetRootTreeUsingGetApiResponse, BursiGetRootTreeUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibusinessunits/root/tree`,
                params: {
                    includeArchived: queryArg.includeArchived,
                },
            }),
        }),
        bursiGetBuUsingGet: build.query<BursiGetBuUsingGetApiResponse, BursiGetBuUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibusinessunits/${queryArg.businessUnitId}`,
                params: {
                    includeArchived: queryArg.includeArchived,
                },
            }),
        }),
        bursiDeleteBuUsingDelete: build.mutation<BursiDeleteBuUsingDeleteApiResponse, BursiDeleteBuUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/ibusinessunits/${queryArg.businessUnitId}`, method: 'DELETE' }),
        }),
        bursiGetBuSimpleUsingGet: build.query<BursiGetBuSimpleUsingGetApiResponse, BursiGetBuSimpleUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibusinessunits/${queryArg.businessUnitId}/simple`,
                params: {
                    includeArchived: queryArg.includeArchived,
                },
            }),
        }),
        bursiGetBuTreeUsingGet: build.query<BursiGetBuTreeUsingGetApiResponse, BursiGetBuTreeUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ibusinessunits/${queryArg.businessUnitId}/tree`,
                params: {
                    includeArchived: queryArg.includeArchived,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
