import { RtciGetTemplatesUsingGetApiResponse, RtciGetTemplatesUsingGetApiArg, RtciCreateTemplateUsingPostApiResponse, RtciCreateTemplateUsingPostApiArg, RtciGetTemplateByIdUsingGetApiResponse, RtciGetTemplateByIdUsingGetApiArg, RtciUpdateTemplateUsingPutApiResponse, RtciUpdateTemplateUsingPutApiArg, RtciDeleteTemplateUsingDeleteApiResponse, RtciDeleteTemplateUsingDeleteApiArg, RegisterTemplateRest, RegisterTemplateRestRead, RegisterTemplateRestListResponse, RegisterTemplateRestListResponseRead } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        rtciGetTemplatesUsingGet: build.query<RtciGetTemplatesUsingGetApiResponse, RtciGetTemplatesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/itemplates`,
                params: {
                    registerId: queryArg.registerId,
                    page: queryArg.page,
                    limit: queryArg.limit,
                },
            }),
        }),
        rtciCreateTemplateUsingPost: build.mutation<RtciCreateTemplateUsingPostApiResponse, RtciCreateTemplateUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/itemplates`, method: 'POST', body: queryArg.registerTemplateRest }),
        }),
        rtciGetTemplateByIdUsingGet: build.query<RtciGetTemplateByIdUsingGetApiResponse, RtciGetTemplateByIdUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/itemplates/${queryArg.templateId}` }),
        }),
        rtciUpdateTemplateUsingPut: build.mutation<RtciUpdateTemplateUsingPutApiResponse, RtciUpdateTemplateUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/itemplates/${queryArg.templateId}`, method: 'PUT', body: queryArg.registerTemplateRest }),
        }),
        rtciDeleteTemplateUsingDelete: build.mutation<RtciDeleteTemplateUsingDeleteApiResponse, RtciDeleteTemplateUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/itemplates/${queryArg.templateId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
