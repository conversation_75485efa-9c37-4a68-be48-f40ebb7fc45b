import { RdrsiCreateEntryFromTemplateUsingPostApiResponse, RdrsiCreateEntryFromTemplateUsingPostApiArg, RdrsiGetFrameworkLinkedRegisterEntriesUsingPostApiResponse, RdrsiGetFrameworkLinkedRegisterEntriesUsingPostApiArg, RdrsiGetEntryByIdUsingGetApiResponse, RdrsiGetEntryByIdUsingGetApiArg, GetRegisterEntriesSearchPost1ApiResponse, GetRegisterEntriesSearchPost1ApiArg, IdWithNameRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRestInternal, PaginRestResultRegisterDataRestInternal, ViewExpressionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        rdrsiCreateEntryFromTemplateUsingPost: build.mutation<RdrsiCreateEntryFromTemplateUsingPostApiResponse, RdrsiCreateEntryFromTemplateUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/ientries/createFromTemplate/${queryArg.templateId}`, method: 'POST' }),
        }),
        rdrsiGetFrameworkLinkedRegisterEntriesUsingPost: build.mutation<
            RdrsiGetFrameworkLinkedRegisterEntriesUsingPostApiResponse,
            RdrsiGetFrameworkLinkedRegisterEntriesUsingPostApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/ientries/frameworks/${queryArg.frameworkNodeId}/${queryArg.registerId}/search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    myTaskStates: queryArg.myTaskStates,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    isSuggestion: queryArg.isSuggestion,
                    stateId: queryArg.stateId,
                },
            }),
        }),
        rdrsiGetEntryByIdUsingGet: build.query<RdrsiGetEntryByIdUsingGetApiResponse, RdrsiGetEntryByIdUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/ientries/tableName/${queryArg.tableName}/${queryArg.entryId}` }),
        }),
        getRegisterEntriesSearchPost1: build.mutation<GetRegisterEntriesSearchPost1ApiResponse, GetRegisterEntriesSearchPost1ApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/ientries/${queryArg.regId}/search`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    viewId: queryArg.viewId,
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    keys: queryArg.keys,
                    values: queryArg.values,
                    groupBy: queryArg.groupBy,
                    myTaskStates: queryArg.myTaskStates,
                    statuses: queryArg.statuses,
                    selectedItems: queryArg.selectedItems,
                    isSuggestion: queryArg.isSuggestion,
                    stateId: queryArg.stateId,
                    parentEntryId: queryArg.parentEntryId,
                    subtableColumn: queryArg.subtableColumn,
                    parentTableName: queryArg.parentTableName,
                    fwLevelGrouping: queryArg.fwLevelGrouping,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
