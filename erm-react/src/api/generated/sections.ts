import { SrsCreateSectionUsingPostApiResponse, SrsCreateSectionUsingPostApiArg, GetSectionsApiResponse, GetSectionsApiArg, SrsGetSectionUsingGetApiResponse, SrsGetSectionUsingGetApiArg, SrsUpdateSectionUsingPutApiResponse, SrsUpdateSectionUsingPutApiArg, SrsDeleteSectionUsingDeleteApiResponse, SrsDeleteSectionUsingDeleteApiArg, AddFieldToSectionApiResponse, AddFieldToSectionApiArg, SrsUpdateFieldUsingPutApiResponse, SrsUpdateFieldUsingPutApiArg, SrsDeleteFieldFromSectionUsingDeleteApiResponse, SrsDeleteFieldFromSectionUsingDeleteApiArg, FormulaComponentRest, FieldRest, StyleRest, SectionRest, PaginRestResultSectionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        srsCreateSectionUsingPost: build.mutation<SrsCreateSectionUsingPostApiResponse, SrsCreateSectionUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/sections`, method: 'POST', body: queryArg.sectionRest }),
        }),
        getSections: build.query<GetSectionsApiResponse, GetSectionsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/sections/${queryArg.applicationId}/list`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                },
            }),
        }),
        srsGetSectionUsingGet: build.query<SrsGetSectionUsingGetApiResponse, SrsGetSectionUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/sections/${queryArg.sectionId}` }),
        }),
        srsUpdateSectionUsingPut: build.mutation<SrsUpdateSectionUsingPutApiResponse, SrsUpdateSectionUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/sections/${queryArg.sectionId}`, method: 'PUT', body: queryArg.sectionRest }),
        }),
        srsDeleteSectionUsingDelete: build.mutation<SrsDeleteSectionUsingDeleteApiResponse, SrsDeleteSectionUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/sections/${queryArg.sectionId}`, method: 'DELETE' }),
        }),
        addFieldToSection: build.mutation<AddFieldToSectionApiResponse, AddFieldToSectionApiArg>({
            query: (queryArg) => ({ url: `/v1/api/sections/${queryArg.sectionId}/field`, method: 'PUT', body: queryArg.fieldRest }),
        }),
        srsUpdateFieldUsingPut: build.mutation<SrsUpdateFieldUsingPutApiResponse, SrsUpdateFieldUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/sections/${queryArg.sectionId}/field/${queryArg.fieldId}`, method: 'PUT', body: queryArg.fieldRest }),
        }),
        srsDeleteFieldFromSectionUsingDelete: build.mutation<SrsDeleteFieldFromSectionUsingDeleteApiResponse, SrsDeleteFieldFromSectionUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/sections/${queryArg.sectionId}/field/${queryArg.fieldId}`, method: 'DELETE' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
