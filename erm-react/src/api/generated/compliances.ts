import { GetComplianceAssignmentsApiResponse, GetComplianceAssignmentsApiArg, CqarsCreateComplianceFrequencyUsingPostApiResponse, CqarsCreateComplianceFrequencyUsingPostApiArg, UpdateComplianceAssignmentApiResponse, UpdateComplianceAssignmentApiArg, CqarsCreateComplianceFrequencyUsingPost1ApiResponse, CqarsCreateComplianceFrequencyUsingPost1ApiArg, CqarsDeleteComplianceFrequencyUsingDeleteApiResponse, CqarsDeleteComplianceFrequencyUsingDeleteApiArg, CqarsGetComplianceFrequencyUsingGetApiResponse, CqarsGetComplianceFrequencyUsingGetApiArg, CqarsDeleteComplianceFrequencyUsingDelete1ApiResponse, CqarsDeleteComplianceFrequencyUsingDelete1ApiArg, CersGetAllComplianceEntriesUsingGetApiResponse, CersGetAllComplianceEntriesUsingGetApiArg, CersGetAllViewComplianceEntriesUsingPostApiResponse, CersGetAllViewComplianceEntriesUsingPostApiArg, CersGetMyComplianceEntriesUsingGetApiResponse, CersGetMyComplianceEntriesUsingGetApiArg, CersGetMyViewComplianceEntriesUsingPostApiResponse, CersGetMyViewComplianceEntriesUsingPostApiArg, CersUpdateEntriesBulkUsingPutApiResponse, CersUpdateEntriesBulkUsingPutApiArg, CersGetEntryByIdentifierUsingGetApiResponse, CersGetEntryByIdentifierUsingGetApiArg, CersUpdateEntryUsingPutApiResponse, CersUpdateEntryUsingPutApiArg, CersDeleteEntryUsingDeleteApiResponse, CersDeleteEntryUsingDeleteApiArg, CersReassignEntryUsingPutApiResponse, CersReassignEntryUsingPutApiArg, CqrsCreateComplianceQuestionUsingGetApiResponse, CqrsCreateComplianceQuestionUsingGetApiArg, CqrsDeleteComplianceQuestionUsingDeleteApiResponse, CqrsDeleteComplianceQuestionUsingDeleteApiArg, CqrsPurgeComplianceQuestionUsingDeleteApiResponse, CqrsPurgeComplianceQuestionUsingDeleteApiArg, CqrsRestoreComplianceQuestionUsingPostApiResponse, CqrsRestoreComplianceQuestionUsingPostApiArg, ComplianceQuestionsApiResponse, ComplianceQuestionsApiArg, CqrsGetControlUsingGetApiResponse, CqrsGetControlUsingGetApiArg, CqrsUpdateComplianceQuestionUsingPutApiResponse, CqrsUpdateComplianceQuestionUsingPutApiArg, GetComplianceQuestionsHistoryApiResponse, GetComplianceQuestionsHistoryApiArg, CrrsGetComplianceResponsesUsingGetApiResponse, CrrsGetComplianceResponsesUsingGetApiArg, IdWithNameRest, ComplianceFrequencyRest, PaginRestResultComplianceFrequencyRest, ComplianceFrequencyBulkCreateRest, ActionLinkModel, ActionLinkRest, SimpleAttachment, ComplianceEntryRest, PaginRestResultComplianceEntryRest, ViewExpressionRest, ComplianceKriFilterWrapperRest, BulkComplianceEntryAttachment, UpdateComplianceEntryRest, BulkComplianceEntryComplete, BulkComplianceEntryCompleteEnvelope, IdWithNameAndStatusRest, QuestionBaseRest, PaginRestResultQuestionBaseRest, FilterContextRest, ComplianceResponseItem, ComplianceResponseRest, PaginRestResultComplianceResponseRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        getComplianceAssignments: build.query<GetComplianceAssignmentsApiResponse, GetComplianceAssignmentsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/compliances/assignments`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                },
            }),
        }),
        cqarsCreateComplianceFrequencyUsingPost: build.mutation<
            CqarsCreateComplianceFrequencyUsingPostApiResponse,
            CqarsCreateComplianceFrequencyUsingPostApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/compliances/assignments`, method: 'POST', body: queryArg.complianceFrequencyRest }),
        }),
        updateComplianceAssignment: build.mutation<UpdateComplianceAssignmentApiResponse, UpdateComplianceAssignmentApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/assignments`, method: 'PUT', body: queryArg.complianceFrequencyRest }),
        }),
        cqarsCreateComplianceFrequencyUsingPost1: build.mutation<
            CqarsCreateComplianceFrequencyUsingPost1ApiResponse,
            CqarsCreateComplianceFrequencyUsingPost1ApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/compliances/assignments/bulk`, method: 'POST', body: queryArg.complianceFrequencyBulkCreateRest }),
        }),
        cqarsDeleteComplianceFrequencyUsingDelete: build.mutation<
            CqarsDeleteComplianceFrequencyUsingDeleteApiResponse,
            CqarsDeleteComplianceFrequencyUsingDeleteApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/compliances/assignments/bulk`, method: 'DELETE', body: queryArg.body }),
        }),
        cqarsGetComplianceFrequencyUsingGet: build.query<CqarsGetComplianceFrequencyUsingGetApiResponse, CqarsGetComplianceFrequencyUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/assignments/${queryArg.id}` }),
        }),
        cqarsDeleteComplianceFrequencyUsingDelete1: build.mutation<
            CqarsDeleteComplianceFrequencyUsingDelete1ApiResponse,
            CqarsDeleteComplianceFrequencyUsingDelete1ApiArg
        >({
            query: (queryArg) => ({ url: `/v1/api/compliances/assignments/${queryArg.id}`, method: 'DELETE' }),
        }),
        cersGetAllComplianceEntriesUsingGet: build.query<CersGetAllComplianceEntriesUsingGetApiResponse, CersGetAllComplianceEntriesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/compliances/entries/all`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                    filter: queryArg.filter,
                },
            }),
        }),
        cersGetAllViewComplianceEntriesUsingPost: build.mutation<
            CersGetAllViewComplianceEntriesUsingPostApiResponse,
            CersGetAllViewComplianceEntriesUsingPostApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/compliances/entries/all`,
                method: 'POST',
                body: queryArg.complianceKriFilterWrapperRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                },
            }),
        }),
        cersGetMyComplianceEntriesUsingGet: build.query<CersGetMyComplianceEntriesUsingGetApiResponse, CersGetMyComplianceEntriesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/compliances/entries/my`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                    filter: queryArg.filter,
                    onePage: queryArg.onePage,
                },
            }),
        }),
        cersGetMyViewComplianceEntriesUsingPost: build.mutation<
            CersGetMyViewComplianceEntriesUsingPostApiResponse,
            CersGetMyViewComplianceEntriesUsingPostApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/compliances/entries/my`,
                method: 'POST',
                body: queryArg.complianceKriFilterWrapperRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                },
            }),
        }),
        cersUpdateEntriesBulkUsingPut: build.mutation<CersUpdateEntriesBulkUsingPutApiResponse, CersUpdateEntriesBulkUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/entries/my/bulk-complete`, method: 'PUT', body: queryArg.bulkComplianceEntryCompleteEnvelope }),
        }),
        cersGetEntryByIdentifierUsingGet: build.query<CersGetEntryByIdentifierUsingGetApiResponse, CersGetEntryByIdentifierUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/entries/${queryArg.entryId}` }),
        }),
        cersUpdateEntryUsingPut: build.mutation<CersUpdateEntryUsingPutApiResponse, CersUpdateEntryUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/entries/${queryArg.entryId}`, method: 'PUT', body: queryArg.updateComplianceEntryRest }),
        }),
        cersDeleteEntryUsingDelete: build.mutation<CersDeleteEntryUsingDeleteApiResponse, CersDeleteEntryUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/entries/${queryArg.entryId}`, method: 'DELETE' }),
        }),
        cersReassignEntryUsingPut: build.mutation<CersReassignEntryUsingPutApiResponse, CersReassignEntryUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/entries/${queryArg.entryId}/reassignment`, method: 'PUT', body: queryArg.complianceEntryRest }),
        }),
        cqrsCreateComplianceQuestionUsingGet: build.query<CqrsCreateComplianceQuestionUsingGetApiResponse, CqrsCreateComplianceQuestionUsingGetApiArg>({
            query: () => ({ url: `/v1/api/compliances/questions` }),
        }),
        cqrsDeleteComplianceQuestionUsingDelete: build.mutation<
            CqrsDeleteComplianceQuestionUsingDeleteApiResponse,
            CqrsDeleteComplianceQuestionUsingDeleteApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/compliances/questions`,
                method: 'DELETE',
                params: {
                    complianceQuestionIds: queryArg.complianceQuestionIds,
                },
            }),
        }),
        cqrsPurgeComplianceQuestionUsingDelete: build.mutation<CqrsPurgeComplianceQuestionUsingDeleteApiResponse, CqrsPurgeComplianceQuestionUsingDeleteApiArg>(
            {
                query: (queryArg) => ({
                    url: `/v1/api/compliances/questions/purge`,
                    method: 'DELETE',
                    params: {
                        complianceQuestionIds: queryArg.complianceQuestionIds,
                    },
                }),
            },
        ),
        cqrsRestoreComplianceQuestionUsingPost: build.mutation<CqrsRestoreComplianceQuestionUsingPostApiResponse, CqrsRestoreComplianceQuestionUsingPostApiArg>(
            {
                query: (queryArg) => ({
                    url: `/v1/api/compliances/questions/restore`,
                    method: 'POST',
                    params: {
                        complianceQuestionIds: queryArg.complianceQuestionIds,
                    },
                }),
            },
        ),
        complianceQuestions: build.mutation<ComplianceQuestionsApiResponse, ComplianceQuestionsApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/compliances/questions/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                    statuses: queryArg.statuses,
                },
            }),
        }),
        cqrsGetControlUsingGet: build.query<CqrsGetControlUsingGetApiResponse, CqrsGetControlUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/questions/${queryArg.complianceQuestionId}` }),
        }),
        cqrsUpdateComplianceQuestionUsingPut: build.mutation<CqrsUpdateComplianceQuestionUsingPutApiResponse, CqrsUpdateComplianceQuestionUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/compliances/questions/${queryArg.complianceQuestionId}`, method: 'PUT', body: queryArg.questionBaseRest }),
        }),
        getComplianceQuestionsHistory: build.mutation<GetComplianceQuestionsHistoryApiResponse, GetComplianceQuestionsHistoryApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/compliances/questions/${queryArg.complianceQuestionId}/history`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        crrsGetComplianceResponsesUsingGet: build.query<CrrsGetComplianceResponsesUsingGetApiResponse, CrrsGetComplianceResponsesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/compliances/responses`,
                params: {
                    controlType: queryArg.controlType,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
