import { KriersGetAllKeyRiskIndicatorEntriesUsingGetApiResponse, KriersGetAllKeyRiskIndicatorEntriesUsingGetApiArg, KriersGetAllKeyRiskIndicatorEntriesUsingPostApiResponse, KriersGetAllKeyRiskIndicatorEntriesUsingPostApiArg, KriersGetMyKeyRiskIndicatorEntriesUsingGetApiResponse, KriersGetMyKeyRiskIndicatorEntriesUsingGetApiArg, KriersGetMyKeyRiskIndicatorEntriesUsingPostApiResponse, KriersGetMyKeyRiskIndicatorEntriesUsingPostApiArg, KriersGetEntryByIdentifierUsingGetApiResponse, KriersGetEntryByIdentifierUsingGetApiArg, KriersUpdateEntryUsingPutApiResponse, KriersUpdateEntryUsingPutApiArg, KriersDeleteEntryUsingDeleteApiResponse, KriersDeleteEntryUsingDeleteApiArg, KrirsCreateRiskLibraryUsingGetApiResponse, KrirsCreateRiskLibraryUsingGetApiArg, KrirsDeleteRiskLibraryUsingDeleteApiResponse, KrirsDeleteRiskLibraryUsingDeleteApiArg, KrirsGetRiskLibraryUsingGetApiResponse, KrirsGetRiskLibraryUsingGetApiArg, KrirsUpdateRiskLibraryUsingPutApiResponse, KrirsUpdateRiskLibraryUsingPutApiArg, GetKeyRiskIndicatorHistoryApiResponse, GetKeyRiskIndicatorHistoryApiArg, KrirsGetKriResponsesUsingGetApiResponse, KrirsGetKriResponsesUsingGetApiArg, KrirsGetKrisUsingPostApiResponse, KrirsGetKrisUsingPostApiArg, ActionLinkModel, ActionLinkRest, IdWithNameRest, SimpleAttachment, KeyRiskIndicatorEntryRest, PaginRestResultKeyRiskIndicatorEntryRest, ViewExpressionRest, ComplianceKriFilterWrapperRest, IdWithNameAndStatusRest, KeyRiskIndicatorRest, PaginRestResultKeyRiskIndicatorRest, QualitativeListItemRest, QualitativeListRest, PaginRestResultQualitativeListRest, FilterContextRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        kriersGetAllKeyRiskIndicatorEntriesUsingGet: build.query<
            KriersGetAllKeyRiskIndicatorEntriesUsingGetApiResponse,
            KriersGetAllKeyRiskIndicatorEntriesUsingGetApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/kris/entries/all`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    showCompletedEntries: queryArg.showCompletedEntries,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                    filter: queryArg.filter,
                },
            }),
        }),
        kriersGetAllKeyRiskIndicatorEntriesUsingPost: build.mutation<
            KriersGetAllKeyRiskIndicatorEntriesUsingPostApiResponse,
            KriersGetAllKeyRiskIndicatorEntriesUsingPostApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/kris/entries/all`,
                method: 'POST',
                body: queryArg.complianceKriFilterWrapperRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    showCompletedEntries: queryArg.showCompletedEntries,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                },
            }),
        }),
        kriersGetMyKeyRiskIndicatorEntriesUsingGet: build.query<
            KriersGetMyKeyRiskIndicatorEntriesUsingGetApiResponse,
            KriersGetMyKeyRiskIndicatorEntriesUsingGetApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/kris/entries/my`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    showCompletedEntries: queryArg.showCompletedEntries,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                    filter: queryArg.filter,
                },
            }),
        }),
        kriersGetMyKeyRiskIndicatorEntriesUsingPost: build.mutation<
            KriersGetMyKeyRiskIndicatorEntriesUsingPostApiResponse,
            KriersGetMyKeyRiskIndicatorEntriesUsingPostApiArg
        >({
            query: (queryArg) => ({
                url: `/v1/api/kris/entries/my`,
                method: 'POST',
                body: queryArg.complianceKriFilterWrapperRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    startDate: queryArg.startDate,
                    endDate: queryArg.endDate,
                    isReportMonth: queryArg.isReportMonth,
                },
            }),
        }),
        kriersGetEntryByIdentifierUsingGet: build.query<KriersGetEntryByIdentifierUsingGetApiResponse, KriersGetEntryByIdentifierUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/kris/entries/${queryArg.entryId}` }),
        }),
        kriersUpdateEntryUsingPut: build.mutation<KriersUpdateEntryUsingPutApiResponse, KriersUpdateEntryUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/kris/entries/${queryArg.entryId}`, method: 'PUT', body: queryArg.keyRiskIndicatorEntryRest }),
        }),
        kriersDeleteEntryUsingDelete: build.mutation<KriersDeleteEntryUsingDeleteApiResponse, KriersDeleteEntryUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/kris/entries/${queryArg.entryId}`, method: 'DELETE' }),
        }),
        krirsCreateRiskLibraryUsingGet: build.query<KrirsCreateRiskLibraryUsingGetApiResponse, KrirsCreateRiskLibraryUsingGetApiArg>({
            query: () => ({ url: `/v1/api/kris/library` }),
        }),
        krirsDeleteRiskLibraryUsingDelete: build.mutation<KrirsDeleteRiskLibraryUsingDeleteApiResponse, KrirsDeleteRiskLibraryUsingDeleteApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/kris/library`,
                method: 'DELETE',
                params: {
                    kriLibraryIds: queryArg.kriLibraryIds,
                },
            }),
        }),
        krirsGetRiskLibraryUsingGet: build.query<KrirsGetRiskLibraryUsingGetApiResponse, KrirsGetRiskLibraryUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/kris/library/${queryArg.kriLibraryId}` }),
        }),
        krirsUpdateRiskLibraryUsingPut: build.mutation<KrirsUpdateRiskLibraryUsingPutApiResponse, KrirsUpdateRiskLibraryUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/kris/library/${queryArg.kriLibraryId}`, method: 'PUT', body: queryArg.keyRiskIndicatorRest }),
        }),
        getKeyRiskIndicatorHistory: build.mutation<GetKeyRiskIndicatorHistoryApiResponse, GetKeyRiskIndicatorHistoryApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/kris/library/${queryArg.kriLibraryId}/history`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        krirsGetKriResponsesUsingGet: build.query<KrirsGetKriResponsesUsingGetApiResponse, KrirsGetKriResponsesUsingGetApiArg>({
            query: () => ({ url: `/v1/api/kris/responses` }),
        }),
        krirsGetKrisUsingPost: build.mutation<KrirsGetKrisUsingPostApiResponse, KrirsGetKrisUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/kris/search`,
                method: 'POST',
                body: queryArg.filterContextRest,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    tagType: queryArg.tagType,
                    tagOperator: queryArg.tagOperator,
                    tagIds: queryArg.tagIds,
                    viewId: queryArg.viewId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
