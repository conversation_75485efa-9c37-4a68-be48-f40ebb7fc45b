import { WlrsvGetWorkLogUsingGetApiResponse, WlrsvGetWorkLogUsingGetApiArg, WorkLogRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        wlrsvGetWorkLogUsingGet: build.query<WlrsvGetWorkLogUsingGetApiResponse, WlrsvGetWorkLogUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/worklogs`,
                params: {
                    tableName: queryArg.tableName,
                    columnName: queryArg.columnName,
                    entryId: queryArg.entryId,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
