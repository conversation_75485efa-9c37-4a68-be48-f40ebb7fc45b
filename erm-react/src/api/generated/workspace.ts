import { WrsGetRegisterColumnsByTypesUsingGetApiResponse, WrsGetRegisterColumnsByTypesUsingGetApiArg, WrsUpdateConfigUsingPutApiResponse, WrsUpdateConfigUsingPutApiArg, WrsGetFullConfigForEditUsingGetApiResponse, WrsGetFullConfigForEditUsingGetApiArg, WrsDeleteConfigUsingDeleteApiResponse, WrsDeleteConfigUsingDeleteApiArg, WrsGetUserFilteredWorkspaceUsingGetApiResponse, WrsGetUserFilteredWorkspaceUsingGetApiArg, WrsGetRepositoryHierarchyUsingGetApiResponse, WrsGetRepositoryHierarchyUsingGetApiArg, WrsGetDisplayConfigurationUsingGetApiResponse, WrsGetDisplayConfigurationUsingGetApiArg, WrsSetDisplayConfigurationUsingPostApiResponse, WrsSetDisplayConfigurationUsingPostApiArg, WrsFindByGroupItemUuidUsingGetApiResponse, WrsFindByGroupItemUuidUsingGetApiArg, WrsGetRegistersStatsUsingGetApiResponse, WrsGetRegistersStatsUsingGetApiArg, ColumnRest, WorkspaceGroupItemBase, DashboardItem, FrameworkItem, RegisterItemContractType, RegisterItem, WorkspaceGroupItem, WorkspaceGroup, WorkspaceConfig, WorkspaceRest, RepositoryRest, BaseMetricDisplay, MetricContext, MetricModule, MetricDisplayConfig, MComputedStyle, MEvalResult, Condition, ConditionConfig, ViewCondition, ViewConditionConfig, MScaleBool, MScaleTxtBracket, MScaleNumBracket, MScaleOverdue, MStyleConfig, MetricRest, MetricRestResponse, RegisterStatsDto } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        wrsGetRegisterColumnsByTypesUsingGet: build.query<WrsGetRegisterColumnsByTypesUsingGetApiResponse, WrsGetRegisterColumnsByTypesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/workspace/columns/${queryArg.registerId}`,
                params: {
                    columnTypes: queryArg.columnTypes,
                },
            }),
        }),
        wrsUpdateConfigUsingPut: build.mutation<WrsUpdateConfigUsingPutApiResponse, WrsUpdateConfigUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workspace/config`, method: 'PUT', body: queryArg.workspaceConfig }),
        }),
        wrsGetFullConfigForEditUsingGet: build.query<WrsGetFullConfigForEditUsingGetApiResponse, WrsGetFullConfigForEditUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workspace/config/full/${queryArg.workspaceModule}` }),
        }),
        wrsDeleteConfigUsingDelete: build.mutation<WrsDeleteConfigUsingDeleteApiResponse, WrsDeleteConfigUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workspace/config/${queryArg.workspaceId}`, method: 'DELETE' }),
        }),
        wrsGetUserFilteredWorkspaceUsingGet: build.query<WrsGetUserFilteredWorkspaceUsingGetApiResponse, WrsGetUserFilteredWorkspaceUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workspace/config/${queryArg.workspaceModule}` }),
        }),
        wrsGetRepositoryHierarchyUsingGet: build.query<WrsGetRepositoryHierarchyUsingGetApiResponse, WrsGetRepositoryHierarchyUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/workspace/dashboards/${queryArg.workspaceModule}`,
                params: {
                    parentPaths: queryArg.parentPaths,
                },
            }),
        }),
        wrsGetDisplayConfigurationUsingGet: build.query<WrsGetDisplayConfigurationUsingGetApiResponse, WrsGetDisplayConfigurationUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workspace/metrics/display/${queryArg.workspaceModule}` }),
        }),
        wrsSetDisplayConfigurationUsingPost: build.mutation<WrsSetDisplayConfigurationUsingPostApiResponse, WrsSetDisplayConfigurationUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/workspace/metrics/display/${queryArg.workspaceModule}`, method: 'POST', body: queryArg.metricDisplayConfig }),
        }),
        wrsFindByGroupItemUuidUsingGet: build.query<WrsFindByGroupItemUuidUsingGetApiResponse, WrsFindByGroupItemUuidUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/workspace/metrics/${queryArg.workspaceModule}/${queryArg.itemUuid}`,
                params: {
                    eval: queryArg.eval,
                    computeStyle: queryArg.computeStyle,
                },
            }),
        }),
        wrsGetRegistersStatsUsingGet: build.query<WrsGetRegistersStatsUsingGetApiResponse, WrsGetRegistersStatsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/workspace/registers/stats/${queryArg.workspaceModule}`,
                params: {
                    businessUnitFilter: queryArg.businessUnitFilter,
                },
            }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
