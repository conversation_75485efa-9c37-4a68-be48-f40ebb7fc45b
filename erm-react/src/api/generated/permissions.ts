import { PrsGetPermissionsUsingGetApiResponse, PrsGetPermissionsUsingGetApiArg, PrsGetPermissionUsingGetApiResponse, PrsGetPermissionUsingGetApiArg, PermissionRest, PaginRestResultPermissionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        prsGetPermissionsUsingGet: build.query<PrsGetPermissionsUsingGetApiResponse, PrsGetPermissionsUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/permissions`,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                },
            }),
        }),
        prsGetPermissionUsingGet: build.query<PrsGetPermissionUsingGetApiResponse, PrsGetPermissionUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/permissions/${queryArg.permissionId}` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
