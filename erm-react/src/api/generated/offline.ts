import { OrsGetEntriesForOfflineUseUsingGetApiResponse, OrsGetEntriesForOfflineUseUsingGetApiArg, OrsLockEntryForOfflineUseUsingPutApiResponse, OrsLockEntryForOfflineUseUsingPutApiArg, DeleteOfflineLockApiResponse, DeleteOfflineLockApiArg, OrsGetBulkEntitiesUsingGetApiResponse, OrsGetBulkEntitiesUsingGetApiArg, OrsGetRegistersUsingGetApiResponse, OrsGetRegistersUsingGetApiArg, IdWithNameRest, OfflineLockRest, Property, Properties, Field, Section, UniqueKeys, Register, RegisterDataRest, IdWithNameAndStatusRest, BusinessUnitCore, RoleBaseRest, Timestamp, ProtechtUserRest, BulkEntitiesRest, ContractMappingRestField, ContractMappingRest, GlobalListRest, FormulaComponentRest, FieldRest, FieldActionRest, FieldConditionRest, FieldRuleStatusRest, FieldRuleRest, FieldRuleSetRest, RiskMatrixRatingRest, RiskMatrixCellRest, ScaleItemRest, ScaleRest, ScaleSetRest, StyleRest, SectionRest, PermissionRest, RoleRest, RegisterStateTransitionRest, RegisterStateRest, RegisterStateDefinitionRest, TaskColumnRest, RestParamRest, RestResponseActionConditionRest, RestResponseTranslationMapRest, RestResponseActionFieldMapRest, RestResponseActionRest, RestResponseHandlerRest, WorkflowTemplateRest, WorkFlowApiAction, WorkflowCalendarEventAction, WorkFlowConditionField, WorkFlowCondition, WorkFlowEmailAction, WorkFlowSmsAction, WorkFlowStatus, WorkFlowRuleRest, UserTableMetadataRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        orsGetEntriesForOfflineUseUsingGet: build.query<OrsGetEntriesForOfflineUseUsingGetApiResponse, OrsGetEntriesForOfflineUseUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/offline/entries`,
                params: {
                    deviceId: queryArg.deviceId,
                },
            }),
        }),
        orsLockEntryForOfflineUseUsingPut: build.mutation<OrsLockEntryForOfflineUseUsingPutApiResponse, OrsLockEntryForOfflineUseUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/offline/entries/${queryArg.tableName}/${queryArg.entryId}/lock`,
                method: 'PUT',
                params: {
                    deviceId: queryArg.deviceId,
                },
            }),
        }),
        deleteOfflineLock: build.mutation<DeleteOfflineLockApiResponse, DeleteOfflineLockApiArg>({
            query: (queryArg) => ({ url: `/v1/api/offline/entries/${queryArg.tableName}/${queryArg.entryId}/lock/${queryArg.lockId}`, method: 'DELETE' }),
        }),
        orsGetBulkEntitiesUsingGet: build.query<OrsGetBulkEntitiesUsingGetApiResponse, OrsGetBulkEntitiesUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/offline/pickers`,
                params: {
                    context: queryArg.context,
                },
            }),
        }),
        orsGetRegistersUsingGet: build.query<OrsGetRegistersUsingGetApiResponse, OrsGetRegistersUsingGetApiArg>({
            query: () => ({ url: `/v1/api/offline/registers` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
