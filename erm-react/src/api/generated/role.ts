import { UcGetUsersWithRoleUsingGetApiResponse, UcGetUsersWithRoleUsingGetApiArg, UcGetAvailableUsersForRoleUsingGetApiResponse, UcGetAvailableUsersForRoleUsingGetApiArg, UcMoveUsersUsingPatchApiResponse, UcMoveUsersUsingPatchApiArg, UcGetRelevantUsersUsingGetApiResponse, UcGetRelevantUsersUsingGetApiArg, UserView, PagingResponseUserView, UserMoveRequest, UserViewShared } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        ucGetUsersWithRoleUsingGet: build.query<UcGetUsersWithRoleUsingGetApiResponse, UcGetUsersWithRoleUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/role/users`,
                params: {
                    roleId: queryArg.roleId,
                    query: queryArg.query,
                    'page-size': queryArg['page-size'],
                    'page-number': queryArg['page-number'],
                    sort: queryArg.sort,
                    dir: queryArg.dir,
                },
            }),
        }),
        ucGetAvailableUsersForRoleUsingGet: build.query<UcGetAvailableUsersForRoleUsingGetApiResponse, UcGetAvailableUsersForRoleUsingGetApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/role/users/available`,
                params: {
                    roleId: queryArg.roleId,
                    query: queryArg.query,
                    'page-size': queryArg['page-size'],
                    'page-number': queryArg['page-number'],
                    sort: queryArg.sort,
                    dir: queryArg.dir,
                },
            }),
        }),
        ucMoveUsersUsingPatch: build.mutation<UcMoveUsersUsingPatchApiResponse, UcMoveUsersUsingPatchApiArg>({
            query: (queryArg) => ({ url: `/v1/api/role/users/move`, method: 'PATCH', body: queryArg.userMoveRequest }),
        }),
        ucGetRelevantUsersUsingGet: build.query<UcGetRelevantUsersUsingGetApiResponse, UcGetRelevantUsersUsingGetApiArg>({
            query: () => ({ url: `/v1/api/role/users/relevant` }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
