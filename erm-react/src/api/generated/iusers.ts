import { PursiCreateUserUsingPostApiResponse, PursiCreateUserUsingPostApiArg, PursiGetCurrentUserInternalUsingGetApiResponse, PursiGetCurrentUserInternalUsingGetApiArg, PursiUpdateCurrentUserUsingPutApiResponse, PursiUpdateCurrentUserUsingPutApiArg, PursiGetCurrentUserMobileQrCodeUsingGetApiResponse, PursiGetCurrentUserMobileQrCodeUsingGetApiArg, PursiGetUserUsingGetApiResponse, PursiGetUserUsingGetApiArg, PursiUpdateUserUsingPutApiResponse, PursiUpdateUserUsingPutApiArg, PursiDeleteUserUsingDeleteApiResponse, PursiDeleteUserUsingDeleteApiArg, PursiGetUserDashboardsUsingGetApiResponse, PursiGetUserDashboardsUsingGetApiArg, PursiGetUserHistoryUsingPostApiResponse, PursiGetUserHistoryUsingPostApiArg, PursiGetUserVersionUsingGetApiResponse, PursiGetUserVersionUsingGetApiArg, PursiLockUserUsingPutApiResponse, PursiLockUserUsingPutApiArg, PursiPurgeUserUsingDeleteApiResponse, PursiPurgeUserUsingDeleteApiArg, PursiRestoreUserUsingPutApiResponse, PursiRestoreUserUsingPutApiArg, PursiGetSecurityTokenForUserUsingGetApiResponse, PursiGetSecurityTokenForUserUsingGetApiArg, PursiUnlockUserUsingPutApiResponse, PursiUnlockUserUsingPutApiArg, IdWithNameRest, Timestamp, ProtechtUserRestInternal, DashboardsRest, PaginRestResultProtechtUserRestInternal, ViewExpressionRest } from './types';
import { baseApi as api } from '../baseApi';
const injectedRtkApi = api.injectEndpoints({
    endpoints: (build) => ({
        pursiCreateUserUsingPost: build.mutation<PursiCreateUserUsingPostApiResponse, PursiCreateUserUsingPostApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers`, method: 'POST', body: queryArg.protechtUserRestInternal }),
        }),
        pursiGetCurrentUserInternalUsingGet: build.query<PursiGetCurrentUserInternalUsingGetApiResponse, PursiGetCurrentUserInternalUsingGetApiArg>({
            query: () => ({ url: `/v1/api/iusers/me` }),
        }),
        pursiUpdateCurrentUserUsingPut: build.mutation<PursiUpdateCurrentUserUsingPutApiResponse, PursiUpdateCurrentUserUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iusers/me`,
                method: 'PUT',
                body: queryArg.protechtUserRestInternal,
                params: {
                    includeBUs: queryArg.includeBUs,
                },
            }),
        }),
        pursiGetCurrentUserMobileQrCodeUsingGet: build.query<PursiGetCurrentUserMobileQrCodeUsingGetApiResponse, PursiGetCurrentUserMobileQrCodeUsingGetApiArg>(
            {
                query: (queryArg) => ({
                    url: `/v1/api/iusers/me/mobile/qrcode`,
                    params: {
                        size: queryArg.size,
                    },
                }),
            },
        ),
        pursiGetUserUsingGet: build.query<PursiGetUserUsingGetApiResponse, PursiGetUserUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}` }),
        }),
        pursiUpdateUserUsingPut: build.mutation<PursiUpdateUserUsingPutApiResponse, PursiUpdateUserUsingPutApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iusers/${queryArg.userId}`,
                method: 'PUT',
                body: queryArg.protechtUserRestInternal,
                params: {
                    includeBUs: queryArg.includeBUs,
                },
            }),
        }),
        pursiDeleteUserUsingDelete: build.mutation<PursiDeleteUserUsingDeleteApiResponse, PursiDeleteUserUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}`, method: 'DELETE' }),
        }),
        pursiGetUserDashboardsUsingGet: build.query<PursiGetUserDashboardsUsingGetApiResponse, PursiGetUserDashboardsUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}/dashboards` }),
        }),
        pursiGetUserHistoryUsingPost: build.mutation<PursiGetUserHistoryUsingPostApiResponse, PursiGetUserHistoryUsingPostApiArg>({
            query: (queryArg) => ({
                url: `/v1/api/iusers/${queryArg.userId}/history`,
                method: 'POST',
                body: queryArg.body,
                params: {
                    offset: queryArg.offset,
                    limit: queryArg.limit,
                    orderBy: queryArg.orderBy,
                    orderType: queryArg.orderType,
                    groupBy: queryArg.groupBy,
                    viewId: queryArg.viewId,
                },
            }),
        }),
        pursiGetUserVersionUsingGet: build.query<PursiGetUserVersionUsingGetApiResponse, PursiGetUserVersionUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}/history/${queryArg.versionId}` }),
        }),
        pursiLockUserUsingPut: build.mutation<PursiLockUserUsingPutApiResponse, PursiLockUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}/lock`, method: 'PUT' }),
        }),
        pursiPurgeUserUsingDelete: build.mutation<PursiPurgeUserUsingDeleteApiResponse, PursiPurgeUserUsingDeleteApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}/purge`, method: 'DELETE' }),
        }),
        pursiRestoreUserUsingPut: build.mutation<PursiRestoreUserUsingPutApiResponse, PursiRestoreUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}/restore`, method: 'PUT' }),
        }),
        pursiGetSecurityTokenForUserUsingGet: build.query<PursiGetSecurityTokenForUserUsingGetApiResponse, PursiGetSecurityTokenForUserUsingGetApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}/security/token` }),
        }),
        pursiUnlockUserUsingPut: build.mutation<PursiUnlockUserUsingPutApiResponse, PursiUnlockUserUsingPutApiArg>({
            query: (queryArg) => ({ url: `/v1/api/iusers/${queryArg.userId}/unlock`, method: 'PUT' }),
        }),
    }),
    overrideExisting: false,
});
export { injectedRtkApi as baseInjectedApi };
