import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_BASE_URL, ERM_CONTEXT } from '../config';

export interface LoginResponse {
    token: string;
    psk: string;
}

export interface RefreshTokenResponse {
    jwt: string;
}

const customBaseQuery = async (args, api, extraOptions) => {
    let baseUrl;
    if (args.url.startsWith('/login')) {
        baseUrl = ERM_CONTEXT;
    } else if (args.url.startsWith('/v1/api/get_token')) {
        baseUrl = API_BASE_URL;
    }

    const baseQuery = fetchBaseQuery({ baseUrl });
    return baseQuery(args, api, extraOptions);
};

export const authApi = createApi({
    reducerPath: 'combinedApi',
    baseQuery: customBaseQuery,
    endpoints: () => ({}),
});
