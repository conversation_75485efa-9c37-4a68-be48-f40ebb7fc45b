export const cacheProfiles = {
    // keep caching for 30 minutes for data that does not change frequently
    staticData: {
        keepUnusedDataFor: 30 * 60,
        refetchOnMountOrArgChange: false,
    },
    // keep caching for 5 seconds when user goes back and forth between components
    // invalidate cache after 5 seconds to ensure that new updates are reflected on the ui
    backAndForthNavigation: {
        keepUnusedDataFor: 5,
        refetchOnMountOrArgChange: false,
    },
    // stale data is never displayed to the user, it's immediately removed from the cache when subscribers count hits 0
    // data refetch happens on every mount or argument change
    noCache: {
        keepUnusedDataFor: 0,
        refetchOnMountOrArgChange: true,
    },
} as const;
