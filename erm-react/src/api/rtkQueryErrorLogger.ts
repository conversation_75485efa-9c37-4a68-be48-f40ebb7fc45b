import { Middleware, PayloadAction } from '@reduxjs/toolkit';
import { isRejectedWithValue } from '@reduxjs/toolkit';

export const rtkQueryErrorLogger: Middleware = () => (next) => (action: PayloadAction<{ data; error }>) => {
    if (isRejectedWithValue(action)) {
        console.warn('We got a rejected action!');

        let errorMessage = 'An error occurred';
        let errorDetails = {};
        if (action.payload) {
            if (action.payload.data && action.payload.data.message) {
                errorDetails = action.payload.data;
                errorMessage = action.payload.data.message;
            } else if (action.payload.error && action.payload.error.message) {
                errorDetails = action.payload.error;
                errorMessage = action.payload.error.message;
            }
        }

        console.warn({
            title: 'Error!',
            details: errorDetails,
            message: errorMessage,
        });
    }

    return next(action);
};
