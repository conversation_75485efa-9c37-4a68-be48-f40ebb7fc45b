import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_BASE_URL } from '../config';
import { refreshTokenIfNeeded } from 'common/api/utils/token';

const customBaseQuery = fetchBaseQuery({ baseUrl: API_BASE_URL, credentials: 'include' });

const baseQueryWithTokenRefresh = async (args, api, extraOptions) => {
    args.headers = args.headers || {};

    try {
        const token = await refreshTokenIfNeeded();
        if (token) {
            args.headers['Authorization'] = `Bearer ${token}`;
        }
        return customBaseQuery(args, api, extraOptions);
    } catch (error) {
        return { error: { status: 'CUSTOM_ERROR', error: error.message } };
    }
};

export const baseApi = createApi({
    reducerPath: 'api',
    baseQuery: baseQueryWithTokenRefresh,
    endpoints: () => ({}),
});
