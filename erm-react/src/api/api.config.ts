import { ConfigFile } from '@rtk-query/codegen-openapi';
import * as path from 'path';
import { OutputFileOptions } from '@rtk-query/codegen-openapi/src/types';
import * as fs from 'fs';
import { promisify } from 'util';

interface SwaggerOperation {
    operationId?: string;
}

const swaggerFilePath = path.resolve(__dirname, './swagger.json');
const fixedSwaggerFilePath = path.resolve(__dirname, './swagger_fixed.json');
const swagger = JSON.parse(fs.readFileSync(swaggerFilePath, 'utf-8'));
const writeFileAsync = promisify(fs.writeFile);

const baseUrls = ['/v1/api/'];

function extractUppercase(input) {
    return input
        .split('')
        .filter((char) => char === char.toUpperCase() && char.match(/[A-Z]/))
        .join('');
}

// Ensures unique operation IDs across all operations in a Swagger document
async function ensureUniqueOperationIds(swagger: any) {
    const operationIdSet = new Set<string>();

    for (const [, methods] of Object.entries(swagger.paths)) {
        for (const [, operation] of Object.entries(methods as Record<string, SwaggerOperation>)) {
            if (operation) {
                const baseOperationId = operation.operationId || '';
                const split = baseOperationId.split('__');
                let finalOperationId = split[1] ? `${extractUppercase(split[0])}_${split[1]}` : split[0];
                let counter = 1;

                while (operationIdSet.has(finalOperationId)) {
                    finalOperationId = `${finalOperationId}${counter++}`;
                }

                operationIdSet.add(finalOperationId);
                operation.operationId = finalOperationId;
            }
        }
    }
}

async function writeFile(_swagger) {
    try {
        await writeFileAsync(fixedSwaggerFilePath, JSON.stringify(_swagger, null, 2));
    } catch (error) {
        console.error('Error writing file:', error);
    }
}

void ensureUniqueOperationIds(swagger);
void writeFile(swagger);

function extractNextWord(url: string, prefixes: string[]): string | null {
    // Escape special characters in prefixes and join them into a single regex pattern
    const escapedPrefixes = prefixes.map((prefix) => prefix.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')).join('|');
    // Create a regular expression to find a prefix followed by the next word
    const regex = new RegExp(`(${escapedPrefixes})([^/]+)`);
    const match = url.match(regex);

    // If a match is found, return the word immediately following the prefix
    if (match && match[2]) {
        return match[2];
    }

    // If no match is found, return null
    return null;
}

const apis: Record<string, Omit<OutputFileOptions, 'outputFile'>> = {};
Object.keys(swagger.paths).forEach((path) => {
    const module = extractNextWord(path, baseUrls);
    if (!module) {
        return;
    }
    apis[`./generated/${module}.ts`] = {
        filterEndpoints: (_, definition) => {
            const path = definition.path.toLowerCase();

            return baseUrls.some((url) => {
                const prefix = `${url}${module}`;
                return path === prefix || path.startsWith(`${prefix}/`);
            });
        },
    };
});
// create folder if non existent
const dir = path.resolve(path.join(__dirname, 'generated'));
if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
}
// https://github.com/reduxjs/redux-toolkit/issues/2836
const config: ConfigFile = {
    apiFile: './baseApi.ts',
    apiImport: 'baseApi',
    exportName: 'baseInjectedApi',
    schemaFile: fixedSwaggerFilePath,
    outputFiles: apis,
    endpointOverrides: [
        {
            pattern: 'getRegisterEntriesPost',
            type: 'query',
        },
        {
            pattern: 'getRegisterEntriesSearchPost',
            type: 'query',
        },
        {
            pattern: 'tmrsGetRegistersUsingPost',
            type: 'query',
        },
        {
            pattern: 'getUsers',
            type: 'query',
        },
        {
            pattern: 'rrsiGetRolesUsingPost',
            type: 'query',
        },
    ],
};

export default config;
