import ts from 'typescript';
import * as path from 'path';
import * as fs from 'fs';

const directoryPath = path.resolve(__dirname, 'generated');
const outputFile = path.resolve(__dirname, 'generated/types.ts');
const extractedTypes = new Set();
function visitNode(node, sourceFilename, typesInFile) {
    if (ts.isTypeAliasDeclaration(node) || ts.isInterfaceDeclaration(node)) {
        const typeName = node.name.getText();
        typesInFile.push(typeName);
        extractedTypes.add(node.getText());
    }
    ts.forEachChild(node, (childNode) => visitNode(childNode, sourceFilename, typesInFile));
}
// Extract types from each file
fs.readdirSync(directoryPath).forEach((file) => {
    if (path.extname(file) === '.ts' && path.basename(file) !== 'types.ts') {
        const filePath = path.join(directoryPath, file);
        const content = fs.readFileSync(filePath, 'utf8');
        const sourceFile = ts.createSourceFile(file, content, ts.ScriptTarget.Latest, true);
        const typesInFile = [];
        visitNode(sourceFile, file, typesInFile);
        // Remove extracted types from the original file
        let modifiedContent = content;
        if (typesInFile.length) {
            const importTypes = typesInFile.map((type) => type);
            const importStatement = `import { ${importTypes.join(', ')} } from './types';\n`;
            modifiedContent = importStatement + modifiedContent;
        }
        fs.writeFileSync(filePath, modifiedContent);
    }
});
function removeTypeDefinitions(content) {
    // The regex starts capturing from "export type" and goes up until (but not including) the next "export".
    // Positive lookahead is used to ensure the next "export" isn't consumed by the regex match.
    const typePattern = /(export type[\s\S]*)/;
    return content.replace(typePattern, '');
}
fs.readdirSync(directoryPath).forEach((file) => {
    if (path.extname(file) === '.ts' && path.basename(file) !== 'types.ts') {
        const filePath = path.join(directoryPath, file);
        let content = fs.readFileSync(filePath, 'utf8');
        content = removeTypeDefinitions(content);
        fs.writeFileSync(filePath, content);
    }
});
// Write all extracted types to types.ts
fs.writeFileSync(outputFile, Array.from(extractedTypes).join('\n'));

// New type definitions to replace the old ones
const newTypeDefinitions = `

export type ConfigMenuItemBase = {
    itemType?: 'REGISTER' | 'DASHBOARD' | 'FRAMEWORK';
};

export type DashboardMenuItem = ConfigMenuItemBase & {
    id?: number;
    alias?: string;
    order?: number;
    name?: string;
    path?: string;
};

export type FrameworkMenuItem = ConfigMenuItemBase & {
    id?: number;
    tableName?: string;
    name?: string;
    description?: string;
    alias?: string;
    order?: number;
    appId?: number;
    viewId?: number;
};

export type RegisterMenuItem = ConfigMenuItemBase & {
    id?: number;
    tableName?: string;
    name?: string;
    description?: string;
    alias?: string;
    order?: number;
    appId?: number;
    viewId?: number;
};

export type ConfigMenuItem = DashboardMenuItem & FrameworkMenuItem & RegisterMenuItem;

export type BowTieDiagramRest = {
    id?: number;
    name?: string;
    description?: string;
    status?: string;
    createdBy?: string;
    lastModifiedBy?: string;
    createDate?: string;
    lastModifiedDate?: string;
    createDateFormatted?: string;
    lastModifiedDateFormatted?: string;
    centralRiskName?: string;
    centralRiskId?: number;
    diagramModel?: JsonNode;
    libraryLinkNotifications?: BowTieLinkNotificationRest[];
    tags?: IdWithNameRest[];
    businessUnits?: IdWithNameRest[];
    libraryLinkEnabled?: boolean;
    showLegendEnabled?: boolean;
    showControlsEnabled?: boolean;
    locked?: boolean;
};
`;

// Ensure the types.ts file exists
if (!fs.existsSync(outputFile)) {
    fs.writeFileSync(outputFile, ''); // Create the file if it does not exist
}

// Read the existing content of the types.ts file
let typesContent = fs.readFileSync(outputFile, 'utf8');

// Specify the types to remove, using a regex that captures everything up to the next type definition or file end
const typesToRemove = ['DashboardMenuItem', 'FrameworkMenuItem', 'RegisterMenuItem', 'ConfigMenuItem', 'BowTieDiagramRest'];
typesToRemove.forEach((type) => {
    // Improved regex to capture the entire type definition, handling nested objects and multiline
    const regex = new RegExp(`export type ${type}[\\s\\S]+?(?=(export type|\\z))`, 'g');
    typesContent = typesContent.replace(regex, '');
});

// Append the new type definitions to the cleaned content
typesContent += newTypeDefinitions;

// Write the updated content back to the types.ts file
fs.writeFileSync(outputFile, typesContent);
console.log('Updated types.ts with new type definitions.');
