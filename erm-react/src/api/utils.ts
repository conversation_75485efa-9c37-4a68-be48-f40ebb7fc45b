// checks if error returned from backend has message e.g. error.data.message
export const isErrorDataWithMessage = (error: unknown): error is { data: { message: string } } => {
    return typeof error === 'object' && error != null && 'data' in error && error.data !== null && typeof (error.data as any).message === 'string';
};

export const getErrorCode = (error: unknown): number | undefined => {
    if (error && typeof error === 'object' && 'status' in error) {
        return error.status as number;
    }
    return;
};
