import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
    useFcDeleteFrameworkUsingDeleteMutation,
    useFcGetFrameworkCategoriesUsingGetQuery,
    useFcGetFrameworksUsingGetQuery,
    useImportFrameworkUsingPostMutation,
    useLazyFcExportFrameworkUsingGet1Query,
} from '../rtkApi';
import Typography from '@mui/material/Typography';
import Card from '@mui/material/Card';
import CardHeaderWithAction from 'ui/components/Cards/CardHeaderWithAction';
import Box from '@mui/material/Box';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import { ProgressVariant, UploadDialog } from '@protecht/ui-library/library/components/FileDropzone';
import { MenuItemType } from '@protecht/ui-library/library/types';
import MultiSelect from '@protecht/ui-library/library/components/MultiSelect';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileExport } from '@fortawesome/pro-regular-svg-icons';
import { strings } from 'common/utils/i18n';
import { useTheme } from '@mui/material/styles';
import { generatePath, useLocation, useNavigate } from 'react-router';
import { FrameworkPath } from '../routes';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import Grid from '@mui/material/Grid';
import useMediaQuery from '@mui/material/useMediaQuery';
import Divider from '@mui/material/Divider';
import { setLayoutOption } from 'app/reducer';
import { LayoutButton } from 'app/components/buttons/LayoutButton';
import store, { useDispatch, useSelector } from 'store';
import { getLayoutOption } from 'app/selectors';
import { LayoutOption } from 'common/types';
import { Table } from '@protecht/ui-library/library/components/Table';
import { FrameworkColDef, labels } from 'frameworks/const';
import { DEFAULT_ORDER_FIELD } from 'library/components/Control/ControlDefinitions';
import { FrameworkStubRead, FrameworkViewRead } from 'api/generated/types';
import { Add, Alert, Delete, Export, FrameworkNodes as FrameworkNodesIcon, InfoOutlined, View } from '@protecht/ui-library/library/components/SVGIcons';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { GridPaginationModel, GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import useSnackbar from 'common/hooks/useSnackbar';
import { ScrollableFitContainer } from 'common/components/ScrollableFitContainer';
import { FrameworkStatus } from 'frameworks/FrameworkState/FrameworkState';
import { initiateFileDownload } from 'app/utils';
import { DateTime } from 'luxon';
import { PROTECHT_DATE_TIME_FORMAT } from 'common/constants';
import LoadingOverlay from 'common/components/LoadingOverlay';
import { getFrameworkAppIdFromDictionary, hasDesignPermission, hasManagePermission } from 'frameworks/selectors';
import { usersApi } from 'user/rtkApi';
import _ from 'lodash';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { SortType } from 'ui/types';
import { FileItem } from '@protecht/ui-library/library/components/FileDropzone';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';

export type FrameworkViewReadWithId = Omit<FrameworkViewRead, 'id'> & { id: number };

const frameworkStates: { id: FrameworkStatus | null; label: string }[] = [
    { id: FrameworkStatus.ARCHIVED, label: strings(`frameworks:states.${FrameworkStatus.ARCHIVED}`) },
    { id: FrameworkStatus.DRAFT, label: strings(`frameworks:states.${FrameworkStatus.DRAFT}`) },
    { id: FrameworkStatus.PUBLISHED, label: strings(`frameworks:states.${FrameworkStatus.PUBLISHED}`) },
];

const Frameworks = () => {
    const { data: frameworks, isLoading } = useFcGetFrameworksUsingGetQuery({ category: undefined });
    const { data: categories } = useFcGetFrameworkCategoriesUsingGetQuery();
    const [deleteFramework] = useFcDeleteFrameworkUsingDeleteMutation();
    const navigate = useNavigate();
    const location = useLocation();
    const theme = useTheme();
    const isSmallerScreen = useMediaQuery('(max-width:1200px)');
    const dispatch = useDispatch();
    const layoutOption = useSelector(getLayoutOption);
    const [selectedStates, setSelectedStates] = useState<string[]>([]);
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const [currentSelection, setCurrentSelection] = useState<FrameworkViewRead | undefined>();
    const [filesToUpload, setFilesToUpload] = useState<FileItem[]>([]);
    const [importDialogVisible, setImportDialogVisible] = useState(false);
    const { showConfirmationAlert } = useConfirmationAlert();
    const [requestParams, setRequestParams] = useState<SearchRequestParams>({
        offset: 0,
        orderBy: 'name',
        orderType: SortType.ASC,
        limit: 10,
    });
    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({ page: 0, pageSize: 10 });
    const [exportFramework] = useLazyFcExportFrameworkUsingGet1Query();
    const [importFramework, { isLoading: isImporting }] = useImportFrameworkUsingPostMutation();
    const { enqueueSuccess } = useSnackbar();

    const canOpenDesigner = useSelector(hasDesignPermission);
    const canManage = useSelector(hasManagePermission);
    const frameworkAppId = useSelector(getFrameworkAppIdFromDictionary);

    const fileInputRef = useRef<HTMLInputElement | null>(null);

    const handleImport = useCallback(() => {
        fileInputRef.current?.click();
    }, [fileInputRef]);

    const handleFileChange = useCallback(
        async (event: React.ChangeEvent<HTMLInputElement>) => {
            const file = event.target.files?.[0];
            if (file) {
                setFilesToUpload([{ file }]);
                setImportDialogVisible(true);
                // Reset file input value to allow selecting the same file again
                event.target.value = '';
            }
        },
        [importFramework],
    );

    const frameworkComparator = useCallback((a: FrameworkViewRead, b: FrameworkViewRead) => {
        const statusOrder = ['PUBLISHED', 'DRAFT', 'ARCHIVED'];
        const statusComparison = statusOrder.indexOf(a.status!) - statusOrder.indexOf(b.status!);

        if (statusComparison === 0) {
            return a.name!.localeCompare(b.name!);
        }

        return statusComparison;
    }, []);

    const filteredFrameworks = useMemo(() => {
        if (!frameworks?.length) {
            return [];
        }

        const sortedFrameworks = [...frameworks].sort(frameworkComparator);

        return sortedFrameworks.filter(
            (framework) =>
                (!selectedStates?.length || selectedStates.includes(FrameworkStatus[framework.status!].toString())) &&
                (!selectedCategories?.length ||
                    (selectedCategories.includes('-2') && !framework.category) ||
                    selectedCategories.includes(framework.category?.id ? framework.category?.id.toString().toLowerCase() : '')),
        );
    }, [frameworks, selectedStates, selectedCategories]);

    const onEdit = (_event, data) => {
        void navigate(generatePath(`${FrameworkPath.HOME}/:id`, { id: data.id }), { state: { from: location.pathname } });
    };

    const onDelete = async (id: number): Promise<void> => {
        void deleteFramework({ id: id ?? currentSelection?.id })
            .unwrap()
            .then(() => {
                enqueueSuccess(strings('frameworks:messages.fwDeleted'));
                setCurrentSelection(undefined);
            });
    };

    const confirmDelete = (_event, data) => {
        showConfirmationAlert({
            onConfirm: onDelete.bind(null, data.id),
            icon: <Alert />,
            title: strings('frameworks:title.delete'),
            contentText: strings('frameworks:label.delete', { name: data.name }),
            confirmButtonLabel: strings('common:button.delete'),
            cancelButtonLabel: strings('common:button.cancel'),
            isCancelPrimary: true,
        });
    };

    const openFramework = useCallback(
        (_event, framework: FrameworkStubRead) => {
            void navigate(generatePath(`${FrameworkPath.HOME}/:id/nodes`, { id: framework.id!.toString() }));
        },
        [navigate],
    );

    const onExportFramework = useCallback(async (_event, framework: FrameworkStubRead) => {
        void exportFramework({ id: framework.id!, type: 'ALL' })
            .unwrap()
            .then((data) => {
                let fileName = framework.name!;
                if (!fileName.endsWith('.zip')) {
                    fileName += '.zip';
                }

                initiateFileDownload(fileName, data as Blob);
            });
    }, []);

    const selectFwStates = useCallback(
        (event) => {
            setSelectedStates(event.target.value);
        },
        [setSelectedStates],
    );

    const selectCategories = useCallback(
        (event) => {
            setSelectedCategories(event.target.value);
        },
        [setSelectedCategories],
    );

    const renderValueState = useCallback(
        (selected: string[]) => {
            const finalString =
                selected.length === 0 || selected.length === frameworkStates.length
                    ? `${strings('common:label.any', { entity: 'state' })}`
                    : selected.length === 1
                    ? frameworkStates?.find((entity) => entity.id?.toString() === selected[0])?.label
                    : `${selected.length} ${strings('common:label.selectedEntities', { entity: 'states' })}`;
            return (
                <>
                    <View
                        color={theme.palette.primary.main}
                        width={20}
                        height={20}
                    />
                    <Typography
                        variant="body2"
                        color={theme.palette.primary.main}
                        sx={{
                            marginLeft: '5px',
                        }}
                    >
                        {finalString}
                    </Typography>
                </>
            );
        },
        [frameworkStates],
    );

    const renderValueCategory = useCallback(
        (selected: string[]) => {
            const finalString =
                selected.length === 0 || selected.length === categories?.length
                    ? `${strings('common:label.any', { entity: 'category' })}`
                    : selected.length === 1
                    ? categories?.find((entity) => entity.id?.toString() === selected[0])?.name
                    : `${selected.length} ${strings('common:label.selectedEntities', { entity: 'categories' })}`;
            return (
                <>
                    <View
                        color={theme.palette.primary.main}
                        width={20}
                        height={20}
                    />
                    <Typography
                        variant="body2"
                        color={theme.palette.primary.main}
                        sx={{
                            marginLeft: '5px',
                        }}
                    >
                        {finalString}
                    </Typography>
                </>
            );
        },
        [categories],
    );

    const handleSelection = (selectionModel: GridRowSelectionModel) => {
        const selectedId = selectionModel[selectionModel.length - 1];
        setCurrentSelection(frameworks?.find((item) => selectedId === item.id));
    };

    const stateMenuItems: MenuItemType<string>[] = useMemo(() => {
        const states = frameworkStates.map((state) => ({
            value: state.id?.toString() ?? '',
            label: state.label,
        }));

        return [
            {
                value: 'checkAll',
                label: strings('common:label.checkAll'),
                action: () => setSelectedStates(states?.map((state) => state.value.toString())),
            },
            {
                value: 'uncheckAll',
                label: strings('common:label.uncheckAll'),
                action: () => setSelectedStates([]),
            },
            {
                divider: true,
                value: '-1',
                label: '',
            },
            ...states,
        ];
    }, [frameworkStates, setSelectedStates]);

    const categoriesMenuItems: MenuItemType<string>[] = useMemo(() => {
        const cats =
            categories?.map((category) => ({
                value: category.id?.toString() ?? '',
                label: category.name ?? '',
            })) || [];

        return [
            {
                value: 'checkAll',
                label: 'Check All',
                action: () => setSelectedCategories(cats?.map((category) => category.value.toString())),
            },
            {
                value: 'uncheckAll',
                label: 'Uncheck All',
                action: () => setSelectedCategories([]),
            },
            {
                divider: true,
                value: '-1',
                label: '',
            },
            ...cats,
            {
                divider: true,
                value: '-1',
                label: '',
            },
            {
                value: '-2',
                label: strings('common:dropdown.no_selection'),
            },
        ];
    }, [categories, setSelectedCategories]);

    const contextMenuItems: ContextMenuItem[] = useMemo(() => {
        return [
            {
                icon: <FrameworkNodesIcon />,
                label: strings('frameworks:button.open'),
                action: openFramework,
            },
            {
                icon: <InfoOutlined />,
                label: strings('frameworks:button.edit'),
                action: onEdit,
            },
            { divider: true },
            {
                icon: <Export />,
                disabled: !canManage,
                label: strings('common:button.export'),
                action: onExportFramework,
            },
            {
                icon: <Delete color={theme.palette.accentColors?.red} />,
                disabled: !canManage,
                label: strings('common:button.delete'),
                action: confirmDelete,
            },
        ];
    }, [canManage]);

    const handleParamsChanged = useCallback(
        (params) => {
            const newParams = { ...requestParams, ...params };
            if (!_.isEqual(newParams, requestParams)) {
                setRequestParams(newParams);
            }
        },
        [dispatch, requestParams],
    );

    const uploadHandler = useCallback(
        (file: File, { setAbortHandler }) => {
            const formData = new FormData();
            formData.append('file', file);
            const response = importFramework({ file: formData });
            setAbortHandler({ abort: response.abort });
            return response.unwrap();
        },
        [importFramework],
    );

    const handleOnUploadClose = useCallback(() => {
        setFilesToUpload([]);
        setImportDialogVisible(false);
        void store.dispatch(usersApi.util.invalidateTags(['permissions']));
    }, [setImportDialogVisible]);

    return (
        <>
            <ToolbarContainer
                disableGutters={false}
                variant="regular"
                sx={{ borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_231}` }}
            >
                <ToolbarGroup
                    flex={1}
                    justifyContent="space-between"
                >
                    <ToolbarGroup>
                        <Typography
                            variant="h1"
                            noWrap
                            sx={{ flexGrow: 1 }}
                            data-testid="frameworks-overview-heading"
                        >
                            {strings('frameworks:title.frameworks')}
                        </Typography>
                    </ToolbarGroup>
                    <ToolbarGroup>
                        {canOpenDesigner && (
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                variant="outlined"
                                onClick={() =>
                                    (window.location.href = `${ProtechtDictionary.siteUrl}/worms/client/app/widget.html?appId=${frameworkAppId}&widget=IncidentRegister`)
                                }
                            >
                                {strings('register:label.registerDesigner')}
                            </Button>
                        )}
                        <Button
                            {...ButtonStyles.pageToolbarButton}
                            variant="outlined"
                            disabled={!canManage}
                            startIcon={<FontAwesomeIcon icon={faFileExport} />}
                            onClick={handleImport}
                        >
                            {strings('common:button.import')}
                        </Button>
                        <input
                            type="file"
                            ref={fileInputRef}
                            style={{ display: 'none' }}
                            onChange={handleFileChange}
                        />
                    </ToolbarGroup>
                </ToolbarGroup>
            </ToolbarContainer>
            <Box
                sx={{
                    margin: 0,
                    height: '100%',
                    width: '100%',
                    padding: isSmallerScreen ? '10px 24px 24px' : '10px 92px 24px',
                    backgroundColor: theme.palette.protechtGrey?.grey_245,
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <ToolbarContainer sx={{ flex: 1, justifyContent: 'space-between', minHeight: '48px' }}>
                    <ToolbarGroup sx={{ minWidth: '48px' }}>
                        <Typography
                            noWrap
                            variant="body2"
                        >
                            <Tooltip
                                key={'lightTooltip-tableName'}
                                leaveTouchDelay={1000}
                                title={strings('frameworks:label.frameworks')}
                                aria-label={strings('frameworks:label.frameworks')}
                            >
                                <Box component={'span'}>{strings('frameworks:label.frameworks')}</Box>
                            </Tooltip>
                        </Typography>
                    </ToolbarGroup>
                    <ToolbarGroup>
                        <LayoutButton
                            key="bowtie-layout-options"
                            value={layoutOption}
                            updateValue={(value) => dispatch(setLayoutOption(value))}
                        />
                        <MultiSelect
                            options={categoriesMenuItems}
                            onChange={selectCategories}
                            renderValue={renderValueCategory}
                            value={selectedCategories}
                            dataTestId={'fw-category-select'}
                            sx={{ height: '28px' }}
                        />
                        <MultiSelect
                            options={stateMenuItems}
                            onChange={selectFwStates}
                            renderValue={renderValueState}
                            value={selectedStates}
                            dataTestId={'fw-state-select'}
                            sx={{ height: '28px' }}
                        />

                        <Divider
                            orientation="vertical"
                            sx={{ height: '20px' }}
                        />
                        {layoutOption === LayoutOption.LIST && (
                            <>
                                <IconButton
                                    size="small"
                                    disabled={!currentSelection}
                                    color={'primary'}
                                    data-testid={'framework-delete'}
                                    onClick={() => confirmDelete(null, currentSelection!)}
                                >
                                    <Delete fill={theme.palette.accentColors?.red} />
                                </IconButton>
                                <Button
                                    {...ButtonStyles.tableToolbarButton}
                                    disabled={!currentSelection || !canManage}
                                    variant={'secondary'}
                                    startIcon={<FontAwesomeIcon icon={faFileExport} />}
                                    dataTestId="button-export"
                                    onClick={() => onExportFramework(null, currentSelection!)}
                                >
                                    {strings('common:button.export')}
                                </Button>
                                <Button
                                    disabled={!currentSelection}
                                    {...ButtonStyles.tableToolbarButton}
                                    variant={'secondary'}
                                    dataTestId="button-details"
                                    onClick={() => onEdit(null, currentSelection!)}
                                >
                                    {strings('common:label.definition')}
                                </Button>
                                <Button
                                    disabled={!currentSelection}
                                    {...ButtonStyles.tableToolbarButton}
                                    variant={'secondary'}
                                    dataTestId="button-open"
                                    onClick={() => openFramework(null, currentSelection!)}
                                >
                                    {strings('common:button.open')}
                                </Button>
                            </>
                        )}
                        <Button
                            {...ButtonStyles.tableToolbarButton}
                            variant="outlined"
                            disabled={!canManage}
                            onClick={() => navigate(generatePath(`${FrameworkPath.HOME}/new`))}
                            startIcon={<Add />}
                        >
                            {strings('frameworks:button.newFramework')}
                        </Button>
                    </ToolbarGroup>
                </ToolbarContainer>
                {layoutOption === LayoutOption.CARD && (
                    <Box>
                        <Divider
                            orientation="horizontal"
                            sx={{
                                marginBottom: '10px',
                            }}
                        />
                    </Box>
                )}
                <ScrollableFitContainer $barHeights={190}>
                    {layoutOption === LayoutOption.CARD && (
                        <Grid>
                            <Grid
                                container
                                spacing={3}
                            >
                                {filteredFrameworks?.map((framework) => (
                                    <Grid
                                        key={framework.id}
                                        item
                                        xs={12}
                                        sm={12}
                                        md={12}
                                        lg={6}
                                    >
                                        <Card onClick={() => openFramework(null, framework)}>
                                            <CardHeaderWithAction
                                                sx={{ maxWidth: '270px' }}
                                                titleEl={
                                                    <>
                                                        <Typography
                                                            variant="body2"
                                                            display="inline-block"
                                                        >
                                                            {framework.name} {framework.version}
                                                        </Typography>
                                                        <Typography
                                                            paddingLeft="1rem"
                                                            color={theme.palette.protechtGrey?.grey_146}
                                                            variant="body2"
                                                            display="inline-block"
                                                            textTransform="capitalize"
                                                        >
                                                            {framework.status?.toLowerCase()}
                                                        </Typography>
                                                    </>
                                                }
                                                contextMenuItems={contextMenuItems}
                                                contextData={framework}
                                            />
                                            <Box paddingTop={'10px'}>
                                                <Typography
                                                    color={theme.palette.protechtGrey?.grey_128}
                                                    variant="body2"
                                                    fontWeight="600"
                                                >
                                                    {framework.category?.name ?? strings('frameworks:label.unknown')}
                                                </Typography>
                                                <Typography
                                                    sx={{ display: 'inline-block' }}
                                                    color={theme.palette.protechtGrey?.grey_128}
                                                >
                                                    {strings('frameworks:label.owner')}: {framework.owner?.name}
                                                </Typography>
                                                <Typography
                                                    sx={{ display: 'inline-block', marginLeft: '10px' }}
                                                    color={theme.palette.protechtGrey?.grey_128}
                                                >
                                                    {strings('frameworks:label.lastReviewDate')}:{' '}
                                                    {framework.lastReviewDate
                                                        ? DateTime.fromFormat(framework.lastReviewDate, PROTECHT_DATE_TIME_FORMAT).toFormat('dd/MM/yyyy')
                                                        : '-'}
                                                </Typography>
                                            </Box>
                                        </Card>
                                    </Grid>
                                ))}
                            </Grid>
                        </Grid>
                    )}
                    {layoutOption === LayoutOption.LIST && frameworks && requestParams && (
                        <Box sx={{ flex: 1, backgroundColor: theme.palette.protechtGrey?.white }}>
                            <Table<FrameworkViewReadWithId>
                                multiselect={false}
                                paginationMode={'client'}
                                autoPageSize
                                disableColumnMenu
                                hideFooterSelectedRowCount
                                columnHeaderHeight={ThemeConfig.rowHeight}
                                rowHeight={ThemeConfig.rowHeight}
                                columns={FrameworkColDef.map((column) =>
                                    column.field === 'name' ? { ...column, headerName: strings('frameworks:label.framework') } : column,
                                )}
                                params={requestParams}
                                onParamsChanged={handleParamsChanged}
                                paginationModel={paginationModel}
                                onPaginationModelChange={setPaginationModel}
                                sortModel={[{ field: requestParams?.orderBy || DEFAULT_ORDER_FIELD, sort: requestParams?.orderType }]}
                                rows={filteredFrameworks as FrameworkViewReadWithId[]}
                                loading={isLoading}
                                rowSelectionModel={currentSelection ? [currentSelection.id as Required<number>] : []}
                                totalCount={filteredFrameworks?.length || 0}
                                onRowSelectionModelChange={handleSelection}
                                loadingMessage={strings('common:message.loading')}
                                onRowDoubleClick={(params) => openFramework(null, params.row)}
                            />
                        </Box>
                    )}
                </ScrollableFitContainer>
            </Box>
            <LoadingOverlay open={isImporting || isLoading} />

            {importDialogVisible && (
                <UploadDialog
                    visible={importDialogVisible}
                    files={filesToUpload}
                    uploadHandler={uploadHandler}
                    labels={labels}
                    onClose={handleOnUploadClose}
                    closeDialogOnSuccess={false}
                    loaderVariant={ProgressVariant.INDETERMINATE}
                />
            )}
        </>
    );
};

export default Frameworks;
