// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Frameworks was rendered 1`] = `
<DocumentFragment>
  <div
    class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-k4nx0l-MuiToolbar-root"
  >
    <div
      class="MuiBox-root css-qjw807"
    >
      <div
        class="MuiBox-root css-m39v42"
      >
        <h1
          class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1j8qahh-MuiTypography-root"
          data-testid="frameworks-overview-heading"
        >
          Frameworks
        </h1>
      </div>
      <div
        class="MuiBox-root css-m39v42"
      >
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root Mu<PERSON><PERSON>utton-outlined MuiButton-outlinedPrimary Mui<PERSON>utton-sizeLarge <PERSON>on-outlinedSizeLarge <PERSON>on-colorPrimary MuiButton-disableElevation css-pfg0lb-MuiButtonBase-root-MuiButton-root"
          data-testid="button-Import"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <span
            class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-file-export "
              data-icon="file-export"
              data-prefix="far"
              focusable="false"
              role="img"
              viewBox="0 0 576 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M336 448c0 8.8-7.2 16-16 16L64 464c-8.8 0-16-7.2-16-16L48 64c0-8.8 7.2-16 16-16l160 0 0 80c0 17.7 14.3 32 32 32l80 0 0 96 48 0 0-101.5c0-17-6.7-33.3-18.7-45.3L274.7 18.7C262.7 6.7 246.5 0 229.5 0L64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-80-48 0 0 80zM489 215c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l39 39L216 288c-13.3 0-24 10.7-24 24s10.7 24 24 24l278.1 0-39 39c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9l-80-80z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span
            class="css-qv0y8m"
          >
            Import
          </span>
        </button>
        <input
          style="display: none;"
          type="file"
        />
      </div>
    </div>
  </div>
  <div
    class="MuiBox-root css-1wax8gi"
  >
    <div
      class="MuiToolbar-root MuiToolbar-dense css-1gsqdv3-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-1ykmyo8"
      >
        <p
          class="MuiTypography-root MuiTypography-body2 MuiTypography-noWrap css-pg0sua-MuiTypography-root"
        >
          <span
            aria-label="Frameworks / Standards / Regulations"
            class="css-2wjxxz"
            data-mui-internal-clone-element="true"
          >
            <span
              class="MuiBox-root css-0"
            >
              Frameworks / Standards / Regulations
            </span>
          </span>
        </p>
      </div>
      <div
        class="MuiBox-root css-m39v42"
      >
        <div
          aria-label="layout options"
          class="MuiToggleButtonGroup-root css-bhledj-MuiToggleButtonGroup-root"
          role="group"
        >
          <button
            aria-label="card layout"
            aria-pressed="true"
            class="MuiButtonBase-root MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButton-root Mui-selected MuiToggleButton-sizeMedium MuiToggleButton-primary MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButtonGroup-firstButton css-flw2de-MuiButtonBase-root-MuiToggleButton-root"
            tabindex="0"
            type="button"
            value="0"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-table-cells "
              data-icon="table-cells"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zm88 64l0 64-88 0 0-64 88 0zm56 0l88 0 0 64-88 0 0-64zm240 0l0 64-88 0 0-64 88 0zM64 224l88 0 0 64-88 0 0-64zm232 0l0 64-88 0 0-64 88 0zm64 0l88 0 0 64-88 0 0-64zM152 352l0 64-88 0 0-64 88 0zm56 0l88 0 0 64-88 0 0-64zm240 0l0 64-88 0 0-64 88 0z"
                fill="currentColor"
              />
            </svg>
          </button>
          <button
            aria-label="list layout"
            aria-pressed="false"
            class="MuiButtonBase-root MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButton-root MuiToggleButton-sizeMedium MuiToggleButton-primary MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButtonGroup-lastButton css-flw2de-MuiButtonBase-root-MuiToggleButton-root"
            tabindex="0"
            type="button"
            value="1"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-list "
              data-icon="list"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M40 48C26.7 48 16 58.7 16 72l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24L40 48zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM16 232l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0c-13.3 0-24 10.7-24 24zM40 368c-13.3 0-24 10.7-24 24l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0z"
                fill="currentColor"
              />
            </svg>
          </button>
        </div>
        <div
          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-1m9myrc-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
          data-testid="fw-category-select"
        >
          <div
            aria-expanded="false"
            aria-haspopup="listbox"
            class="MuiSelect-select MuiSelect-outlined MuiSelect-multiple MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
            role="combobox"
            tabindex="0"
          >
            <svg
              color="#1B4AD5"
              data-icon="view"
              fill="currentColor"
              height="20"
              viewBox="0 0 24 24"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.313 4H4.688C3.738 4 3 4.786 3 5.714v12.572C3 19.25 3.738 20 4.688 20h14.625c.914 0 1.687-.75 1.687-1.714V5.714C21 4.786 20.227 4 19.313 4m-8.157 14.286H4.898q-.21 0-.21-.215V13.43h6.468zm0-6.572H4.687V6.857h6.47zm7.946 6.572h-6.258v-4.857h6.469v4.642a.204.204 0 0 1-.211.215m.21-6.572h-6.468V6.857h6.469z"
                fill="currentColor"
              />
            </svg>
            <p
              class="MuiTypography-root MuiTypography-body2 css-zqmoyf-MuiTypography-root"
            >
              Any category
            </p>
          </div>
          <input
            aria-hidden="true"
            aria-invalid="false"
            class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
            tabindex="-1"
            value=""
          />
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
            data-testid="KeyboardArrowDownIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
            />
          </svg>
          <fieldset
            aria-hidden="true"
            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
          >
            <legend
              class="css-13wgbfv"
            >
              <span
                class="notranslate"
              >
                ​
              </span>
            </legend>
          </fieldset>
        </div>
        <div
          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-1m9myrc-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
          data-testid="fw-state-select"
        >
          <div
            aria-expanded="false"
            aria-haspopup="listbox"
            class="MuiSelect-select MuiSelect-outlined MuiSelect-multiple MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
            role="combobox"
            tabindex="0"
          >
            <svg
              color="#1B4AD5"
              data-icon="view"
              fill="currentColor"
              height="20"
              viewBox="0 0 24 24"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.313 4H4.688C3.738 4 3 4.786 3 5.714v12.572C3 19.25 3.738 20 4.688 20h14.625c.914 0 1.687-.75 1.687-1.714V5.714C21 4.786 20.227 4 19.313 4m-8.157 14.286H4.898q-.21 0-.21-.215V13.43h6.468zm0-6.572H4.687V6.857h6.47zm7.946 6.572h-6.258v-4.857h6.469v4.642a.204.204 0 0 1-.211.215m.21-6.572h-6.468V6.857h6.469z"
                fill="currentColor"
              />
            </svg>
            <p
              class="MuiTypography-root MuiTypography-body2 css-zqmoyf-MuiTypography-root"
            >
              Any state
            </p>
          </div>
          <input
            aria-hidden="true"
            aria-invalid="false"
            class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
            tabindex="-1"
            value=""
          />
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
            data-testid="KeyboardArrowDownIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
            />
          </svg>
          <fieldset
            aria-hidden="true"
            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
          >
            <legend
              class="css-13wgbfv"
            >
              <span
                class="notranslate"
              >
                ​
              </span>
            </legend>
          </fieldset>
        </div>
        <hr
          class="MuiDivider-root MuiDivider-fullWidth MuiDivider-vertical css-lqeocn-MuiDivider-root"
        />
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-1dp5r6u-MuiButtonBase-root-MuiButton-root"
          data-testid="button-New Framework"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <span
            class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-MuiButton-startIcon"
          >
            <svg
              data-icon="add"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.5 10.75h15v2.5h-15z"
                fill="currentColor"
              />
              <path
                d="M10.75 19.5v-15h2.5v15z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span
            class="css-qv0y8m"
          >
            New Framework
          </span>
        </button>
      </div>
    </div>
    <div
      class="MuiBox-root css-0"
    >
      <hr
        class="MuiDivider-root MuiDivider-fullWidth css-o9b5bv-MuiDivider-root"
      />
    </div>
    <div
      class="MuiBox-root css-1yl3cye"
    >
      <div
        class="MuiGrid-root css-vj1n65-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-3 css-zow5z4-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 MuiGrid-grid-md-12 MuiGrid-grid-lg-6 css-1qi3mhz-MuiGrid-root"
          >
            <div
              class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiCard-root css-1770fvb-MuiPaper-root-MuiCard-root"
            >
              <div
                class="MuiCardHeader-root css-ohs746-MuiCardHeader-root"
              >
                <div
                  class="MuiCardHeader-content css-1qbkelo-MuiCardHeader-content"
                >
                  <span
                    class="MuiTypography-root MuiTypography-h5 MuiCardHeader-title css-d750rg-MuiTypography-root"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body2 css-2va513-MuiTypography-root"
                    >
                      Framework 1 
                    </p>
                    <p
                      class="MuiTypography-root MuiTypography-body2 css-ggm716-MuiTypography-root"
                    />
                  </span>
                </div>
                <div
                  class="MuiCardHeader-action css-sgoict-MuiCardHeader-action"
                >
                  <div>
                    <div>
                      <button
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-1ie4cnm-MuiButtonBase-root-MuiIconButton-root"
                        data-testid="ui-CardHeaderWithAction-IconButton"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="svg-inline--fa fa-chevron-down "
                          data-icon="chevron-down"
                          data-prefix="fas"
                          focusable="false"
                          role="img"
                          viewBox="0 0 512 512"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
                            fill="currentColor"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiBox-root css-542elh"
              >
                <p
                  class="MuiTypography-root MuiTypography-body2 css-j8ndm8-MuiTypography-root"
                >
                  Unknown
                </p>
                <p
                  class="MuiTypography-root MuiTypography-body1 css-mu4abx-MuiTypography-root"
                >
                  Owner: 
                </p>
                <p
                  class="MuiTypography-root MuiTypography-body1 css-v2vgmu-MuiTypography-root"
                >
                  Last Reviewed: -
                </p>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 MuiGrid-grid-md-12 MuiGrid-grid-lg-6 css-1qi3mhz-MuiGrid-root"
          >
            <div
              class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiCard-root css-1770fvb-MuiPaper-root-MuiCard-root"
            >
              <div
                class="MuiCardHeader-root css-ohs746-MuiCardHeader-root"
              >
                <div
                  class="MuiCardHeader-content css-1qbkelo-MuiCardHeader-content"
                >
                  <span
                    class="MuiTypography-root MuiTypography-h5 MuiCardHeader-title css-d750rg-MuiTypography-root"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body2 css-2va513-MuiTypography-root"
                    >
                      Framework 2 
                    </p>
                    <p
                      class="MuiTypography-root MuiTypography-body2 css-ggm716-MuiTypography-root"
                    />
                  </span>
                </div>
                <div
                  class="MuiCardHeader-action css-sgoict-MuiCardHeader-action"
                >
                  <div>
                    <div>
                      <button
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-1ie4cnm-MuiButtonBase-root-MuiIconButton-root"
                        data-testid="ui-CardHeaderWithAction-IconButton"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="svg-inline--fa fa-chevron-down "
                          data-icon="chevron-down"
                          data-prefix="fas"
                          focusable="false"
                          role="img"
                          viewBox="0 0 512 512"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
                            fill="currentColor"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiBox-root css-542elh"
              >
                <p
                  class="MuiTypography-root MuiTypography-body2 css-j8ndm8-MuiTypography-root"
                >
                  Unknown
                </p>
                <p
                  class="MuiTypography-root MuiTypography-body1 css-mu4abx-MuiTypography-root"
                >
                  Owner: 
                </p>
                <p
                  class="MuiTypography-root MuiTypography-body1 css-v2vgmu-MuiTypography-root"
                >
                  Last Reviewed: -
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
   
</DocumentFragment>
`;
