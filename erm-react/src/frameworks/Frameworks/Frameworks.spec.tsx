import React from 'react';
import { render } from 'test/utils';
import Frameworks from './Frameworks';
import { mockFrameworkCategories, mockFrameworks } from 'frameworks/mock';

jest.mock('frameworks/selectors', () => ({
    hasDesignPermission: jest.fn(),
    hasManagePermission: jest.fn(),
    getFrameworkAppIdFromDictionary: jest.fn(),
}));

jest.mock('frameworks/rtkApi', () => {
    const useFcGetFrameworksUsingGetQuery = jest.fn(() => ({
        data: mockFrameworks,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }));

    const useFcGetFrameworkCategoriesUsingGetQuery = jest.fn(() => ({
        data: mockFrameworkCategories,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }));

    const useFcDeleteFrameworkUsingDeleteMutation = jest.fn(() => [jest.fn(), { isLoading: false, isSuccess: true, isError: false, error: null }]);

    const useLazyFcExportFrameworkUsingGet1Query = jest.fn(() => [jest.fn(), { isLoading: false, isSuccess: true, data: null, error: null }]);

    const useImportFrameworkUsingPostMutation = jest.fn(() => [jest.fn(), { isLoading: false, isSuccess: true, data: null, error: null }]);

    return {
        useFcGetFrameworksUsingGetQuery,
        useFcGetFrameworkCategoriesUsingGetQuery,
        useFcDeleteFrameworkUsingDeleteMutation,
        useLazyFcExportFrameworkUsingGet1Query,
        useImportFrameworkUsingPostMutation,
    };
});

describe('Frameworks', () => {
    const setup = () => {
        return render(<Frameworks />);
    };

    it('was rendered', () => {
        const { asFragment } = setup();
        const clonedFragment = asFragment().cloneNode(true);

        function removeDynamicAttributes(node) {
            if (node.getAttribute) {
                node.removeAttribute('id');
                node.removeAttribute('aria-controls');
                node.removeAttribute('aria-labelledby');
            }
            node.childNodes.forEach((child) => removeDynamicAttributes(child));
        }

        removeDynamicAttributes(clonedFragment);
        expect(clonedFragment).toMatchSnapshot();
    });
});
