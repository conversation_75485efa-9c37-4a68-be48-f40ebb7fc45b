import React, { useCallback, useEffect, useState } from 'react';
import { strings } from 'common/utils/i18n';
import { Table } from '@protecht/ui-library/library/components/Table';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { GridPaginationModel, GridRowSelectionModel } from '@mui/x-data-grid-pro';
import Grid from '@mui/material/Grid';
import { useFcGetMetadataRegistersListUsingGetQuery } from 'frameworks/rtkApi';
import { FrameworkRegisterRead, FrameworkViewRead } from 'api/generated/types';
import Search from '@protecht/ui-library/library/components/Inputs/Search';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import { MetadataRegisterColDef } from '../const';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';

export type FrameworkViewReadWithId = Omit<FrameworkViewRead, 'id'> & { id: number };

type Props = {
    selected?: FrameworkRegisterRead;
    onClose: () => void;
    onSelect: (selected: FrameworkRegisterRead) => void;
    setOpen?: (boolean) => void;
};

const MetadataRegisterSelector: React.FC<Props> = ({ selected, onSelect, setOpen, onClose }) => {
    const [currentSelection, setCurrentSelection] = useState<FrameworkRegisterRead>(selected as FrameworkViewReadWithId);
    const [searchValue, setSearchValue] = useState('');
    const [filteredItems, setFilteredItems] = useState<FrameworkRegisterRead[]>();
    const [params, setParams] = useState<SearchRequestParams | undefined>(undefined);
    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({ page: 0, pageSize: 10 });

    const { data } = useFcGetMetadataRegistersListUsingGetQuery({ search: undefined }, { refetchOnMountOrArgChange: true });
    const handleSelection = (selectionModel: GridRowSelectionModel) => {
        if (selectionModel.length) {
            const selectedOne = data?.find((item) => selectionModel[0] === item.id) as FrameworkViewReadWithId;
            setCurrentSelection(selectedOne);
        }
    };

    const onValueChanged = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchValue(event.target.value);
    }, []);

    useEffect(() => {
        if (searchValue?.length) {
            setParams((params) => ({ ...params, page: 0 }));
            const filtered = data?.filter((row) => row['registerName']?.toLowerCase().includes(searchValue.toLowerCase()));

            setFilteredItems(filtered);
        } else {
            setFilteredItems(data);
        }
    }, [searchValue, data, setFilteredItems]);

    const confirmSelection = () => {
        onSelect(currentSelection);
        setOpen && setOpen(false);
    };

    return (
        <Dialog
            title={strings('frameworks:label.registerSelector')}
            visible={true}
            width={720}
            height={674}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>

                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!currentSelection}
                        onClick={confirmSelection}
                    >
                        {strings('common:button.ok')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                direction="column"
                sx={{ height: '100%', width: '100%', overflow: 'hidden', flexWrap: 'nowrap' }}
            >
                <Grid
                    item
                    sx={{ flex: '0 0 auto' }}
                >
                    <ToolbarContainer>
                        <Search
                            sx={{ maxWidth: 254 }}
                            searchValue={searchValue}
                            onValueChanged={onValueChanged}
                            searchPlaceholder={strings('common:placeholder.search')}
                        />
                    </ToolbarContainer>
                </Grid>
                <Grid
                    item
                    sx={{
                        flex: '1 1 auto',
                        overflow: 'auto',
                    }}
                >
                    <Table<FrameworkViewReadWithId>
                        paginationMode={'client'}
                        autoPageSize
                        disableColumnMenu
                        multiselect={false}
                        hideFooterSelectedRowCount
                        columnHeaderHeight={ThemeConfig.rowHeight}
                        rowHeight={ThemeConfig.rowHeight}
                        columns={MetadataRegisterColDef}
                        rows={(filteredItems as FrameworkViewReadWithId[]) || []}
                        totalCount={filteredItems?.length || 0}
                        params={params}
                        onParamsChanged={setParams}
                        paginationModel={paginationModel}
                        onPaginationModelChange={setPaginationModel}
                        selected={currentSelection ? ([currentSelection] as FrameworkViewReadWithId[]) : []}
                        onRowSelectionModelChange={handleSelection}
                        loading={!filteredItems}
                        loadingMessage={strings('common:message.loading')}
                    />
                </Grid>
            </Grid>
        </Dialog>
    );
};

export default MetadataRegisterSelector;
