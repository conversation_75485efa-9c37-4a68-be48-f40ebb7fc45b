import Typography from '@mui/material/Typography';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { MoveRight } from '@protecht/ui-library/library/components/SVGIcons';
import { strings } from 'common/utils/i18n';
import React, { FC, useEffect, useState } from 'react';
import useTheme from '@mui/system/useTheme';
import { FrameworkStatusViewRead } from 'api/generated/types';
import { useFcUpdateStatusUsingPutMutation } from 'frameworks/rtkApi';
import { useSelector } from 'store';
import { hasEditPermission } from 'frameworks/selectors';

type FrameworkStateProps = {
    status: FrameworkStatusViewRead;
    frameworkId: number;
};

export enum FrameworkStatus {
    ARCHIVED = 0,
    DRAFT = 1,
    PUBLISHED = 2,
}

export const frameworkStates: FrameworkStatusViewRead[] = [
    { id: FrameworkStatus.ARCHIVED, name: strings(`frameworks:statusChange.${FrameworkStatus.ARCHIVED}`) },
    { id: FrameworkStatus.DRAFT, name: strings(`frameworks:statusChange.${FrameworkStatus.DRAFT}`) },
    { id: FrameworkStatus.PUBLISHED, name: strings(`frameworks:statusChange.${FrameworkStatus.PUBLISHED}`) },
];

const FrameworkState: FC<FrameworkStateProps> = ({ status: currentStatus, frameworkId }) => {
    const [possibleStatuses, setPossibleStatuses] = useState(frameworkStates.filter((_status) => currentStatus?.id !== _status.id));
    const theme = useTheme();
    const [updateStatus] = useFcUpdateStatusUsingPutMutation();

    const canEdit = useSelector((state) => hasEditPermission(state, frameworkId));

    useEffect(() => {
        if (currentStatus) {
            setPossibleStatuses(frameworkStates.filter((_status) => currentStatus?.id !== _status.id));
        }
    }, [currentStatus]);

    return (
        <>
            <Typography
                variant="h4"
                fontSize="22px"
                fontWeight="600"
                sx={{ textTransform: 'capitalize' }}
                color={theme.palette.protechtGrey?.grey_146}
            >
                {currentStatus?.name?.toLowerCase()}
            </Typography>
            {possibleStatuses.map((status: Required<FrameworkStatusViewRead>) => (
                <Button
                    {...ButtonStyles.pageToolbarButton}
                    disabled={!canEdit}
                    variant="outlined"
                    startIcon={<MoveRight />}
                    key={status.id}
                    onClick={() => updateStatus({ id: frameworkId, newStatusId: status.id })}
                >
                    {status.name}
                </Button>
            ))}
        </>
    );
};

export default FrameworkState;
