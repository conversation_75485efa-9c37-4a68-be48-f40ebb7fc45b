import { FrameworkDetailRead } from 'api/generated/types';
import { FrameworkForm } from 'frameworks/Framework/types';

export const mapToFormValues = (initialValues: FrameworkForm, data: FrameworkDetailRead): Partial<FrameworkForm> => {
    return {
        ...initialValues,
        ...data,
        category: data.category?.id || '',
        attachments:
            data.fileLinkList?.map(({ name, url, uid }) => ({
                name,
                url,
                file: new Blob([]),
                size: 1,
                uuid: uid,
            })) || null,
        worklog:
            data.worklog?.reduce(
                (latestLog, currentLog) => {
                    return new Date(currentLog.createDate || 0) > new Date(latestLog.createDate || 0) ? currentLog : latestLog;
                },
                { log: '' },
            )?.log || '',
    } as Partial<FrameworkForm>;
};
