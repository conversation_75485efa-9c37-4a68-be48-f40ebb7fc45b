import { createValidationSchema } from './frameworkSchema';
import * as yup from 'yup';

const categories = ['categoryID1', 'categoryID2'];
const frameworks = ['fwName1', 'fwName2'];

describe('Validation Schema Tests', () => {
    const validData = {
        name: 'Test Name',
        version: '1.0',
        description: 'Test Description',
        category: 'categoryID1',
        metadataRegister: {
            id: 1,
            registerName: 'Test Register',
            tableName: 'Test Table',
        },
        allowedRegisters: [],
        lastReviewDate: null,
        owner: {
            id: 1,
            name: 'Owner Name',
            username: 'owner.username',
            businessUnit: 'Owner BU',
        },
        reviewer: {
            id: 2,
            name: 'Reviewer Name',
            username: 'reviewer.username',
            businessUnit: 'Reviewer BU',
        },
    };

    let validationSchema: yup.BaseSchema;

    beforeAll(() => {
        validationSchema = createValidationSchema(categories, frameworks);
    });

    it('should validate a correct data set', async () => {
        await expect(validationSchema.isValid(validData)).resolves.toBe(true);
    });

    describe('name validations', () => {
        it('should fail validation when name is missing', async () => {
            const invalidData = { ...validData, name: undefined };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });

        it('should fail validation when name is short', async () => {
            const invalidData = { ...validData, name: '' };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });

        it('should fail validation when name is too long', async () => {
            const invalidData = {
                ...validData,
                name: 'Name that is too long and should fail validation because it is too long and should fail validation because it is too long',
            };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });

        it('should fail validation when name is taken', async () => {
            const invalidData = { ...validData, name: frameworks[0] };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });
    });

    describe('description validations', () => {
        it('should pass validation when description is missing', async () => {
            const invalidData = { ...validData, description: undefined };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(true);
        });
    });

    describe('allowedRegisters validations', () => {
        it('should pass validation when allowedRegisters is an empty array', async () => {
            await expect(validationSchema.isValid(validData)).resolves.toBe(true);
        });

        it('should pass validation when allowedRegisters is an array of mixed values', async () => {
            const validDataWithAllowedRegisters = { ...validData, allowedRegisters: [1, 'test'] };
            await expect(validationSchema.isValid(validDataWithAllowedRegisters)).resolves.toBe(true);
        });
    });

    describe('lastReviewDate validations', () => {
        it('should pass validation when lastReviewDate is null', async () => {
            await expect(validationSchema.isValid(validData)).resolves.toBe(true);
        });

        it('should pass validation when lastReviewDate is a valid string', async () => {
            const validDataWithLastReviewDate = { ...validData, lastReviewDate: '2023-01-01' };
            await expect(validationSchema.isValid(validDataWithLastReviewDate)).resolves.toBe(true);
        });
    });

    describe('metadataRegister validations', () => {
        it('should pass validation when metadataRegister is provided', async () => {
            await expect(validationSchema.isValid(validData)).resolves.toBe(true);
        });

        it('should fail validation when metadataRegister is not provided', async () => {
            const invalidData = { ...validData, metadataRegister: undefined };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });
    });

    describe('owner validations', () => {
        it('should fail validation when owner has missing required fields', async () => {
            const invalidData = { ...validData, owner: { id: 1, name: '', businessUnit: '' } };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });
    });

    describe('reviewer validations', () => {
        it('should fail validation when reviewer is not provided', async () => {
            const invalidData = { ...validData, reviewer: undefined };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });

        it('should fail validation when reviewer has missing required fields', async () => {
            const invalidData = { ...validData, reviewer: { id: 2, name: '', businessUnit: '' } };
            await expect(validationSchema.isValid(invalidData)).resolves.toBe(false);
        });
    });
});
