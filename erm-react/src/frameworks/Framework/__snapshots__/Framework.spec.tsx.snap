// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Framework was rendered 1`] = `
<DocumentFragment>
   
  <div>
    <div>
      <div>
        <button
          data-testid="button-undefined"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            color="#1B4AD5"
            data-icon="arrow-left"
            data-prefix="fas"
            focusable="false"
            role="img"
            style="font-size: 22px;"
            viewBox="0 0 448 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"
              fill="currentColor"
            />
          </svg>
        </button>
        <h1
          data-testid="frameworks-detail-heading"
        >
          Example Framework 1.0
        </h1>
        <h4>
          active
        </h4>
        <button
          data-testid="button-Archive"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <span>
            <svg
              data-icon="move-right"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.5 6.516v2.281h-4a1.5 1.5 0 0 0-1.5 1.5v3.406a1.5 1.5 0 0 0 1.5 1.5h4v2.28c0 1.344 1.593 2.032 2.53 1.063l5.5-5.5a1.453 1.453 0 0 0 0-2.093l-5.5-5.499c-.937-.969-2.53-.281-2.53 1.062m6.998 5.468L12 17.484v-3.781H6.5v-3.406H12V6.485z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span>
            Archive
          </span>
        </button>
        <button
          data-testid="button-Publish"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <span>
            <svg
              data-icon="move-right"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.5 6.516v2.281h-4a1.5 1.5 0 0 0-1.5 1.5v3.406a1.5 1.5 0 0 0 1.5 1.5h4v2.28c0 1.344 1.593 2.032 2.53 1.063l5.5-5.5a1.453 1.453 0 0 0 0-2.093l-5.5-5.499c-.937-.969-2.53-.281-2.53 1.062m6.998 5.468L12 17.484v-3.781H6.5v-3.406H12V6.485z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span>
            Publish
          </span>
        </button>
      </div>
      <div>
        <button
          data-testid="button-Share"
          tabindex="0"
          type="button"
        >
          <span>
            <svg
              aria-hidden="true"
              data-icon="link"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 640 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M579.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L422.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C206.5 251.2 213 330 263 380c56.5 56.5 148 56.5 204.5 0L579.8 267.7zM60.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C74 372 74 321 105.5 289.5L217.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C433.5 260.8 427 182 377 132c-56.5-56.5-148-56.5-204.5 0L60.2 244.3z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span>
            Share
          </span>
        </button>
        <button
          data-testid="button-Close"
          tabindex="0"
          type="button"
        >
          <span>
            Close
          </span>
        </button>
        <button
          data-testid="button-Save"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <span>
            Save
          </span>
        </button>
      </div>
    </div>
  </div>
  <div>
    <div>
      <div>
        <div>
          <div>
            <div
              style="overflow: hidden; margin-bottom: 0px;"
            >
              <div
                aria-label="permission and user tabs"
                role="tablist"
              >
                <button
                  aria-selected="true"
                  role="tab"
                  tabindex="0"
                  type="button"
                >
                  Framework Definition
                </button>
              </div>
              <span
                style="left: 0px; width: 0px;"
              />
            </div>
          </div>
        </div>
        <div
          role="tabpanel"
        >
          <div>
            <div
              aria-expanded="true"
              role="button"
              tabindex="0"
            >
              <div>
                <h5>
                  Framework Details
                </h5>
              </div>
              <div>
                <button
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="chevron-down"
                    data-prefix="fas"
                    focusable="false"
                    role="img"
                    viewBox="0 0 512 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div
              style="min-height: 0px;"
            >
              <div>
                <div>
                  <div
                    role="region"
                  >
                    <div>
                      <div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="name"
                                    >
                                      Framework Title
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div>
                                  <input
                                    aria-invalid="false"
                                    aria-label="name"
                                    autocomplete="off"
                                    data-lpignore="true"
                                    name="name"
                                    placeholder="enter the title of the framework here"
                                    tabindex="0"
                                    type="text"
                                    value=""
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                  >
                                    <legend>
                                      <span>
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="version"
                                    >
                                      Version
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div>
                                  <input
                                    aria-invalid="false"
                                    aria-label="version"
                                    autocomplete="off"
                                    data-lpignore="true"
                                    name="version"
                                    placeholder="enter the version of the framework here"
                                    tabindex="0"
                                    type="text"
                                    value=""
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                  >
                                    <legend>
                                      <span>
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="description"
                                    >
                                      Description
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div>
                                  <div
                                    inputmode="text"
                                  >
                                    <div>
                                      <textarea
                                        aria-invalid="false"
                                        aria-label="Description"
                                        name="description"
                                        placeholder="enter a description of the Framework here"
                                        style="height: 0px; overflow: hidden;"
                                        tabindex="0"
                                      />
                                      <textarea
                                        aria-hidden="true"
                                        readonly=""
                                        style="visibility: hidden; position: absolute; overflow: hidden; height: 0px; top: 0px; left: 0px; transform: translateZ(0); padding-top: 0px; padding-bottom: 0px; width: 100%;"
                                        tabindex="-1"
                                      />
                                      <fieldset
                                        aria-hidden="true"
                                      >
                                        <legend>
                                          <span>
                                            ​
                                          </span>
                                        </legend>
                                      </fieldset>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="category"
                                    >
                                      Framework Category
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div
                                data-testid="category"
                              >
                                <div
                                  aria-expanded="false"
                                  aria-haspopup="listbox"
                                  aria-label="Framework Category"
                                  role="combobox"
                                  tabindex="0"
                                >
                                  <div>
                                    <p>
                                      No Selection
                                    </p>
                                  </div>
                                </div>
                                <input
                                  aria-hidden="true"
                                  aria-invalid="false"
                                  name="category"
                                  tabindex="-1"
                                  value=""
                                />
                                <svg
                                  aria-hidden="true"
                                  data-testid="KeyboardArrowDownIcon"
                                  focusable="false"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                  />
                                </svg>
                                <fieldset
                                  aria-hidden="true"
                                >
                                  <legend>
                                    <span>
                                      ​
                                    </span>
                                  </legend>
                                </fieldset>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="metadataRegister"
                                    >
                                      Framework Data Register
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div
                                  data-testid="input-metadata-register"
                                >
                                  <div>
                                    <input
                                      aria-invalid="false"
                                      aria-label="Framework Data Register"
                                      name="metadataRegister"
                                      placeholder="select a register for this framework where the data for each node will be stored"
                                      readonly=""
                                      tabindex="-1"
                                      type="text"
                                      value=""
                                    />
                                    <fieldset
                                      aria-hidden="true"
                                    >
                                      <legend>
                                        <span>
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                                <button
                                  data-testid="button-Select"
                                  tabindex="0"
                                  type="button"
                                >
                                  <span>
                                    Select
                                  </span>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="owner"
                                    >
                                      Owner responsible for this Framework
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div>
                                  <div>
                                    <div>
                                      <input
                                        aria-invalid="false"
                                        placeholder="select user who owns responsibility for this framework"
                                        tabindex="0"
                                        type="text"
                                        value=""
                                      />
                                      <fieldset
                                        aria-hidden="true"
                                      >
                                        <legend>
                                          <span>
                                            ​
                                          </span>
                                        </legend>
                                      </fieldset>
                                    </div>
                                  </div>
                                </div>
                                <div>
                                  <button
                                    data-testid="button-select"
                                    tabindex="0"
                                    type="button"
                                  >
                                    <span>
                                      Select
                                    </span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="attachments"
                                    >
                                      Framework source document file(s)
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <section
                                  style="flex-grow: 1; overflow: hidden;"
                                >
                                  <div
                                    role="presentation"
                                    tabindex="-1"
                                  >
                                    <input
                                      data-testid="file-dropzone-input"
                                      multiple=""
                                      style="border: 0px; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(50%); height: 1px; margin: 0px -1px -1px 0px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
                                      tabindex="-1"
                                      type="file"
                                    />
                                    <div>
                                      <span
                                        aria-label="drop or add attachments here (maximum 10MB)"
                                        data-mui-internal-clone-element="true"
                                        style="overflow: hidden;"
                                      >
                                        <p>
                                          drop or add attachments here (maximum 10MB)
                                        </p>
                                      </span>
                                    </div>
                                  </div>
                                </section>
                                <div>
                                  <button
                                    data-testid="button-add"
                                    style="width: 80px; min-width: 80px;"
                                    tabindex="0"
                                    type="button"
                                  >
                                    <span>
                                      Add
                                    </span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div />
                        <div>
                          <div>
                            <p>
                              Additional Linked Items
                            </p>
                          </div>
                          <div>
                            <div>
                              <div>
                                <input
                                  aria-invalid="false"
                                  disabled=""
                                  placeholder="add an item"
                                  tabindex="-1"
                                  type="text"
                                  value=""
                                />
                                <fieldset
                                  aria-hidden="true"
                                >
                                  <legend>
                                    <span>
                                      ​
                                    </span>
                                  </legend>
                                </fieldset>
                              </div>
                            </div>
                            <button
                              data-testid="button-Add"
                              tabindex="0"
                              type="button"
                            >
                              <span>
                                Add
                              </span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div
              aria-expanded="true"
              role="button"
              tabindex="0"
            >
              <div>
                <h5>
                  Review
                </h5>
              </div>
              <div>
                <button
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="chevron-down"
                    data-prefix="fas"
                    focusable="false"
                    role="img"
                    viewBox="0 0 512 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div
              style="min-height: 0px;"
            >
              <div>
                <div>
                  <div
                    role="region"
                  >
                    <div>
                      <div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="reviewer"
                                    >
                                      Reviewer
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div>
                                  <div>
                                    <div>
                                      <input
                                        aria-invalid="false"
                                        placeholder="select the reviewer of this framework"
                                        tabindex="0"
                                        type="text"
                                        value=""
                                      />
                                      <fieldset
                                        aria-hidden="true"
                                      >
                                        <legend>
                                          <span>
                                            ​
                                          </span>
                                        </legend>
                                      </fieldset>
                                    </div>
                                  </div>
                                </div>
                                <div>
                                  <button
                                    data-testid="button-select"
                                    tabindex="0"
                                    type="button"
                                  >
                                    <span>
                                      Select
                                    </span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="lastReviewDate"
                                    >
                                      Last Reviewed
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div>
                                  <div>
                                    <div>
                                      <input
                                        aria-invalid="false"
                                        aria-label="Last Reviewed"
                                        autocomplete="off"
                                        inputmode="text"
                                        name="lastReviewDate"
                                        placeholder="select the last review date of this framework"
                                        type="text"
                                        value=""
                                      />
                                      <fieldset
                                        aria-hidden="true"
                                      >
                                        <legend>
                                          <span>
                                            ​
                                          </span>
                                        </legend>
                                      </fieldset>
                                    </div>
                                  </div>
                                </div>
                                <div>
                                  <span
                                    aria-label="Select date"
                                    data-mui-internal-clone-element="true"
                                  >
                                    <button
                                      data-testid="calendar-button"
                                      tabindex="0"
                                      type="button"
                                    >
                                      <svg
                                        data-icon="calendar"
                                        fill="currentColor"
                                        height="22"
                                        viewBox="0 0 24 24"
                                        width="22"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <path
                                          d="M14.85 11.575a.35.35 0 0 0-.35.35V13.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zM11.1 11.575a.35.35 0 0 0-.35.35V13.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zM7.35 11.575a.35.35 0 0 0-.35.35V13.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zM7.35 15.575a.35.35 0 0 0-.35.35V17.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zM11.096 15.575a.35.35 0 0 0-.35.35V17.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zM14.85 15.575a.35.35 0 0 0-.35.35V17.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35z"
                                          fill="currentColor"
                                        />
                                        <path
                                          clip-rule="evenodd"
                                          d="M14.85 11.575a.35.35 0 0 0-.35.35V13.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zm-3.75 0a.35.35 0 0 0-.35.35V13.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zm-3.75 0a.35.35 0 0 0-.35.35V13.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zm0 4a.35.35 0 0 0-.35.35V17.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zm3.746 0a.35.35 0 0 0-.35.35V17.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35zm3.754 0a.35.35 0 0 0-.35.35V17.7c0 .*************.35h1.796a.35.35 0 0 0 .35-.35v-1.775a.35.35 0 0 0-.35-.35z"
                                          fill="currentColor"
                                          fill-rule="evenodd"
                                        />
                                        <path
                                          clip-rule="evenodd"
                                          d="M15.464 4.275H8.527V1.8H6.729v2.475H4.26c-.704 0-1.275.57-1.275 1.275v15.325c0 .704.57 1.275 1.275 1.275h15.47c.705 0 1.276-.57 1.276-1.275V5.55c0-.704-.571-1.275-1.275-1.275h-2.47V1.8h-1.797zm3.744 5.62a.5.5 0 0 0-.5-.5H5.283a.5.5 0 0 0-.5.5v9.968a.5.5 0 0 0 .5.5h13.425a.5.5 0 0 0 .5-.5z"
                                          fill="currentColor"
                                          fill-rule="evenodd"
                                        />
                                      </svg>
                                    </button>
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div>
                            <div>
                              <div>
                                <div>
                                  <p>
                                    <label
                                      for="worklog"
                                    >
                                      Review Comments
                                    </label>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <div>
                                  <div
                                    inputmode="text"
                                  >
                                    <div>
                                      <textarea
                                        aria-invalid="false"
                                        aria-label="Review Comments"
                                        name="worklog"
                                        placeholder="enter any review comments for this framework"
                                        style="height: 0px; overflow: hidden;"
                                        tabindex="0"
                                      />
                                      <textarea
                                        aria-hidden="true"
                                        readonly=""
                                        style="visibility: hidden; position: absolute; overflow: hidden; height: 0px; top: 0px; left: 0px; transform: translateZ(0); padding-top: 0px; padding-bottom: 0px; width: 100%;"
                                        tabindex="-1"
                                      />
                                      <fieldset
                                        aria-hidden="true"
                                      >
                                        <legend>
                                          <span>
                                            ​
                                          </span>
                                        </legend>
                                      </fieldset>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
