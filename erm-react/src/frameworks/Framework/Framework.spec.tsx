import React from 'react';
import { render, screen } from 'test/utils';
import Framework from './Framework';
import { act, renderHook, waitFor } from '@testing-library/react';
import { mockFrameworkCategories, mockFrameworkData, mockFrameworkList } from 'frameworks/mock';
import { strings } from 'common/utils/i18n';
import { useForm, useWatch } from 'react-hook-form';

jest.mock('common/hooks/useUnsavedChangesAlert');

jest.mock('app/rtkApi', () => {
    const actualRtkApi = jest.requireActual('app/rtkApi');
    return {
        ...actualRtkApi,
        useGetSystemConfigurationQuery: () => ({
            data: {
                max_attach_size_bytes: 10,
            },
        }),
    };
});

jest.mock('common/api/files', () => {
    const actualRtkApi = jest.requireActual('common/api/files');

    return {
        ...actualRtkApi,
        useFersGetAllowedExtensionsUsingGetQuery: () => ({ data: [], isLoading: false, isSuccess: true, isError: false }),
    };
});

jest.mock('frameworks/selectors', () => ({
    hasEditPermission: jest.fn(),
    hasMetaDataRegisterPermission: jest.fn(),
}));

const mockUnwrapFunction = jest.fn(() => Promise.resolve(mockFrameworkData));

const mockCreateFramework = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve(mockFrameworkData),
}));

jest.mock('frameworks/rtkApi', () => {
    const useFcDeleteFrameworkUsingDeleteMutation = jest.fn(() => [
        jest.fn(),
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
            error: null,
        },
    ]);

    const useLazyFcExportFrameworkUsingGet1Query = jest.fn(() => [jest.fn(), { isLoading: false, isSuccess: true, data: null, error: null }]);

    const useImportFrameworkUsingPostMutation = jest.fn(() => [jest.fn(), { isLoading: false, isSuccess: true, data: null, error: null }]);

    const useFcGetFrameworkListUsingGetQuery = jest.fn(() => ({
        data: mockFrameworkList,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }));

    const useLazyFcGetFrameworkUsingGetQuery = jest.fn(() => [
        jest.fn(),
        {
            data: mockFrameworkData,
            isLoading: false,
            isSuccess: true,
            isError: false,
            unwrap: mockUnwrapFunction,
        },
    ]);

    const useFcGetFrameworkCategoriesUsingGetQuery = jest.fn(() => ({
        data: mockFrameworkCategories,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }));

    const useFcCreateDraftFrameworkMutation = jest.fn(() => [
        mockCreateFramework,
        {
            isLoading: false,
            isSuccess: true,
            data: mockFrameworkData,
            error: null,
        },
    ]);

    const useFcUpdateFrameworkDetailsUsingPost1Mutation = jest.fn(() => [
        jest.fn(),
        { isLoading: false, isSuccess: true, data: mockFrameworkData, error: null, unwrap: mockUnwrapFunction },
    ]);

    const useFcUpdateStatusUsingPutMutation = jest.fn(() => [jest.fn(), { isLoading: false, error: null }]);

    return {
        useFcDeleteFrameworkUsingDeleteMutation,
        useLazyFcExportFrameworkUsingGet1Query,
        useImportFrameworkUsingPostMutation,
        useFcGetFrameworkListUsingGetQuery,
        useLazyFcGetFrameworkUsingGetQuery,
        useFcGetFrameworkCategoriesUsingGetQuery,
        useFcCreateDraftFrameworkMutation,
        useFcUpdateFrameworkDetailsUsingPost1Mutation,
        useFcUpdateStatusUsingPutMutation,
    };
});

describe('Framework', () => {
    const setup = () => {
        return render(<Framework />);
    };

    it('was rendered', async () => {
        const { asFragment } = setup();
        await waitFor(() => {
            expect(screen.getByText(`${strings('frameworks:label.fwDetails')}`)).toBeInTheDocument();
        });

        const clonedFragment = asFragment().cloneNode(true);

        function removeDynamicAttributes(node) {
            if (node.getAttribute) {
                node.removeAttribute('id');
                node.removeAttribute('aria-controls');
                node.removeAttribute('aria-labelledby');
                node.removeAttribute('class');
            }
            node.childNodes.forEach((child) => removeDynamicAttributes(child));
        }

        removeDynamicAttributes(clonedFragment);
        expect(clonedFragment).toMatchSnapshot();
    });

    it('should return default value in useWatch for form fields', () => {
        const { result } = renderHook(() => {
            const methods = useForm<{ name: string; version: string }>({
                defaultValues: { name: 'Test Framework', version: '1.0' },
            });

            const nameValue = useWatch({
                control: methods.control,
                name: 'name',
            });

            return { nameValue };
        });

        expect(result.current.nameValue).toEqual('Test Framework');
    });

    it('should watch changes in the form fields', async () => {
        const { result } = renderHook(() => {
            const methods = useForm<{ description: string }>({
                defaultValues: { description: '' },
            });

            const descriptionValue = useWatch({
                control: methods.control,
                name: 'description',
            });

            return { descriptionValue, methods };
        });

        expect(result.current.descriptionValue).toBe('');

        const { methods } = result.current;
        act(() => {
            methods.setValue('description', 'Updated description');
        });

        await waitFor(() => {
            expect(result.current.descriptionValue).toBe('Updated description');
        });
    });
});
