import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { generatePath, useLocation, useNavigate } from 'react-router';
import {
    useFcCreateDraftFrameworkMutation,
    useFcDeleteFrameworkUsingDeleteMutation,
    useFcGetFrameworkCategoriesUsingGetQuery,
    useFcGetFrameworkListUsingGetQuery,
    useFcUpdateFrameworkDetailsUsingPost1Mutation,
    useLazyFcGetFrameworkUsingGetQuery,
} from '../rtkApi';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import { faLink } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import useTheme from '@mui/system/useTheme';
import ContentLayout from 'common/layouts/ContentLayout';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { Attachment } from '@protecht/ui-library/library/components/FileDropzone';
import MainLayout from 'common/layouts/MainLayout';
import Box from '@mui/material/Box';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import Tab from '@mui/material/Tab';
import Grid from '@mui/material/Grid';
import { Accordion, AccordionDetails, AccordionSummary } from '@protecht/ui-library/library/components/Accordion';
import FormField, { SelectField } from '@protecht/ui-library/library/components/FormFields';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import { FormProvider, useFieldArray } from 'react-hook-form';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import { useNumericParam } from 'common/hooks/useNumericParam';
import useSnackbar from 'common/hooks/useSnackbar';
import SpaceBetween from '@protecht/ui-library/library/components/SpaceBetween';
import { FrameworkPath } from '../routes';
import { createValidationSchema } from './frameworkSchema';
import { useUnsavedChangesAlert } from 'common/hooks/useUnsavedChangesAlert';
import RegisterSelector from 'common/components/RegisterSelector/RegisterSelector';
import { RegisterSettingsColDef } from 'cyberrisk/constants';
import { RegisterRest } from 'register/types';
import { FrameworkDetailRead, FrameworkRegisterRead, ProtechtUserRest } from 'api/generated/types';
import useForm from 'common/hooks/forms/useForm';
import * as yup from 'yup';
import LoadingOverlay from 'common/components/LoadingOverlay';
import AttachmentField from 'common/components/Form/FormFields/AttachmentField';
import DialogSelectorField from 'common/components/Form/FormFields/DialogSelectorField';
import MetadataRegisterSelector from 'frameworks/MetadataRegisterSelector';
import DateField from '@protecht/ui-library/library/components/FormFields/DateField';
import { NO_SELECTED_VALUE } from '@protecht/ui-library/library/constants';
import { DateTime } from 'luxon';
import { PROTECHT_DATE_TIME_FORMAT } from 'common/constants';
import LinksList, { LinkListItemTitle } from '@protecht/ui-library/library/components/LinksList';
import { AlertType, DialogType } from 'common/types';
import store, { useSelector } from 'store';
import { hasEditPermission } from 'frameworks/selectors';
import { usersApi } from 'user/rtkApi';
import { useGetSystemConfigurationQuery } from 'app/rtkApi';
import FrameworkToolbar from 'frameworks/FrameworkToolbar';
import { mapToFormValues } from './utils';
import { FrameworkForm } from './types';
import { initialValues } from './initial';
import UserField from 'common/components/Form/FormFields/UserField';

const Framework: FC = () => {
    const theme = useTheme();
    const location = useLocation();
    const navigate = useNavigate();
    const existingId = useNumericParam('id');
    const [draftId, setDraftId] = useState<number | undefined>();
    const [registerSelectorVisible, setRegisterSelectorVisible] = useState(false);
    const [isFormReady, setIsFormReady] = useState(false);

    const { data: frameworks } = useFcGetFrameworkListUsingGetQuery();
    const [triggerFrameworkFetch, { data: framework, isFetching: isFrameworkFetching }] = useLazyFcGetFrameworkUsingGetQuery();
    const { data: categories, isFetching: isCategoriesFetching } = useFcGetFrameworkCategoriesUsingGetQuery();
    const [createDraftFramework, { isLoading: isCreatingDraft }] = useFcCreateDraftFrameworkMutation();
    const [updateFramework, updateResponse] = useFcUpdateFrameworkDetailsUsingPost1Mutation();
    const [deleteFramework] = useFcDeleteFrameworkUsingDeleteMutation();
    const { data: systemConfiguration } = useGetSystemConfigurationQuery();
    const [currentName, setCurrentName] = useState<string | undefined>();
    const { enqueueSuccess } = useSnackbar();

    const isArchived = framework?.status?.name === 'ARCHIVED';

    const canEdit = useSelector((state) => (!existingId ? true : hasEditPermission(state, existingId)));

    const categoryOptions = useMemo(() => {
        const options =
            categories?.map((cat) => ({
                value: cat.id!.toString(),
                label: cat.name,
            })) || [];

        options.unshift({
            value: NO_SELECTED_VALUE,
            label: strings('common:dropdown.no_selection'),
        });

        return options;
    }, [categories, framework, isFormReady, strings]);

    const loading = useMemo(() => !isFormReady || isCategoriesFetching || isFrameworkFetching, [isFormReady, isCategoriesFetching, isFrameworkFetching]);

    const schema = useMemo((): yup.AnyObjectSchema => {
        const existingNames = frameworks?.map((fw) => fw.name!).filter((name) => name !== currentName) || [];
        return (
            categories?.length &&
            (createValidationSchema(
                categories.map((cat) => cat.id!.toString()),
                existingNames,
            ) as any)
        );
    }, [categories, frameworks, currentName]);

    const formMethods = useForm<Partial<FrameworkForm>>({
        mode: 'onChange',
        schema,
        defaultValues: async () => {
            if (existingId) {
                return triggerFrameworkFetch({ id: existingId })
                    .unwrap()
                    .then((data) => {
                        setCurrentName(data.name);
                        setIsFormReady(true);
                        return mapToFormValues(initialValues, data);
                    })
                    .catch(() => {
                        setIsFormReady(true);
                        return initialValues;
                    });
            }
            setIsFormReady(true);
            return initialValues;
        },
    });

    const { handleSubmit, setValue, watch, reset, register, control, formState } = formMethods;
    const { isDirty, isValid } = formState;

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'allowedRegisters',
        keyName: 'formId',
    });

    const metadataRegister = watch('metadataRegister');

    useEffect(() => {
        if (!existingId) {
            void createDraftFramework()
                .unwrap()
                .then((data) => {
                    setDraftId(data!.id!);
                    setCurrentName(data!.name!);
                    setValue('name', data!.name, { shouldDirty: false });
                    setValue('metadataRegister', data!.metadataRegister, { shouldDirty: false });
                });
        }
    }, [existingId]);

    useEffect(() => {
        void formMethods.trigger('metadataRegister');
    }, [metadataRegister]);

    const hasErrors = !isValid;

    const updateFrameworkWithId = useCallback(
        async (id: number, values: FrameworkForm) => {
            let lastReviewDate;
            if (values.lastReviewDate) {
                lastReviewDate = values.lastReviewDate;
                const parsedDate = DateTime.fromISO(values.lastReviewDate);
                if (parsedDate.isValid) {
                    lastReviewDate = parsedDate.toFormat(PROTECHT_DATE_TIME_FORMAT);
                }
            }
            const attachments = values.attachments && values.attachments?.filter((attachment) => !attachment.uuid);
            const fileLinkList =
                values.attachments &&
                values.attachments
                    ?.filter((attachment) => attachment.uuid)
                    .map(({ uuid, name, url }) => ({
                        uid: uuid,
                        name,
                        url,
                    }));

            const category = values.category ? categories?.find((category) => JSON.parse(values.category) === category.id) : undefined;

            const body = getFormData(attachments, {
                name: values.name,
                version: values.version,
                description: values.description,
                category: category,
                allowedRegisters: values.allowedRegisters,
                metadataRegister: values.metadataRegister || undefined,
                owner: values.owner || undefined,
                reviewer: values.reviewer || undefined,
                fileLinkList: fileLinkList || undefined,
                lastReviewDate,
                worklog: [
                    {
                        userId: Number(ProtechtDictionary.loggedUserId),
                        log: values.worklog,
                    },
                ],
            });

            void updateFramework({
                id,
                body,
            })
                .unwrap()
                .then((data) => {
                    enqueueSuccess(strings(existingId ? 'frameworks:label.updated' : 'frameworks:label.created'));
                    if (!existingId) {
                        // if new framework reset the form
                        void store.dispatch(usersApi.util.invalidateTags(['permissions']));
                        reset();
                    } else {
                        reset(mapToFormValues(initialValues, data));
                        setCurrentName(data?.name);
                    }
                    window.GwtBridge?.FrameworkConfiguration?.onFrameworkSave();
                });
        },
        [existingId, updateFramework, enqueueSuccess, categories, navigate, window],
    );

    const saveFramework = useCallback(
        (values: FrameworkForm) => {
            void updateFrameworkWithId(existingId || draftId!, values);
            setDraftId(undefined);
        },
        [existingId, draftId, updateFrameworkWithId],
    );

    const handleBackNavigation = useCallback(() => {
        if (location.state?.from) {
            if (location.state.fromId) {
                void navigate(generatePath(FrameworkPath.NODES, { id: location.state.fromId }));
            } else {
                void navigate(location.state.from);
            }
        } else {
            void navigate(-1);
        }
    }, [location, navigate]);

    const onBack = useCallback(() => {
        if (draftId) {
            void deleteFramework({ id: draftId });
            setDraftId(undefined);
        } else {
            handleBackNavigation();
        }
    }, [draftId, handleBackNavigation, deleteFramework]);

    const onConfirm = useCallback(() => {
        if (draftId) {
            void deleteFramework({ id: draftId });
            setDraftId(undefined);
        } else {
            void handleSubmit(saveFramework)();
        }
    }, [draftId, handleSubmit, saveFramework, deleteFramework]);

    useUnsavedChangesAlert({
        blockNavigation: isDirty || Boolean(draftId),
        type: draftId ? AlertType.Error : AlertType.Warning,
        onConfirm: onConfirm,
        onBack: onBack,
        confirmButtonLabel: !existingId ? strings('common:button.delete') : hasErrors ? strings('common:button.discard') : strings('common:button.save'),
        title: !existingId ? strings('common:button.delete') : hasErrors ? strings('common:title.discardChanges') : strings('common:title.saveChanges'),
        contentText: !existingId
            ? strings('common:message.discardNewChanges')
            : hasErrors
            ? strings('common:message.discardChanges')
            : strings('common:message.unsavedChanges'),
        dialogType: draftId ? DialogType.DISCARD : DialogType.UNSAVED,
    });

    useEffect(() => {
        // Navigate to nodes on new FW creation success
        if (updateResponse.data && !existingId && !isDirty) {
            void navigate(generatePath(`${FrameworkPath.HOME}/:id/nodes`, { id: updateResponse.data.id!.toString() }));
        }
    }, [updateResponse, existingId, formState]);

    const getFormData = (attachments: Attachment[] | null, data: FrameworkDetailRead) => {
        const formData = new FormData();

        if (attachments?.length && attachments.length > 0) {
            attachments.forEach((attachment) => {
                formData.append('fileList', attachment.file);
            });
        }
        formData.append(
            'data',
            new Blob(
                [
                    JSON.stringify({
                        ...data,
                    }),
                ],
                { type: 'application/json' },
            ),
        );

        return formData;
    };

    const onSelection = useCallback(
        (selection: RegisterRest[]) => {
            append(selection.map((register) => ({ id: register.id, registerName: register.label, tableName: register.tableName })));
            setRegisterSelectorVisible(false);
        },
        [append, setRegisterSelectorVisible],
    );

    const onRegisterSelection = useCallback(
        (selection: FrameworkRegisterRead) => {
            setValue('metadataRegister', selection, { shouldDirty: true, shouldValidate: true });
        },
        [setValue],
    );

    const onItemRemove = useCallback(
        (item: FrameworkRegisterRead) => {
            remove(fields.findIndex((field) => field.id === item.id));
        },
        [remove, fields],
    );

    const share = useCallback(async () => {
        await navigator.clipboard.writeText(window.location.href).then(() => {
            enqueueSuccess(strings('common:message.linkCopied'));
        });
    }, []);

    const mapSelectedUser = useCallback((user: ProtechtUserRest) => {
        return {
            id: user.id,
            name: user.name,
            username: user.loginId,
        };
    }, []);

    return (
        <>
            <LoadingOverlay open={isCreatingDraft || loading} />
            {!loading && (
                <>
                    <FrameworkToolbar
                        title={`${currentName || ''} ${framework?.version ? `${framework?.version}` : ''}`}
                        onBack={handleBackNavigation}
                        showShareButton={true}
                        onShare={share}
                        shareIcon={<FontAwesomeIcon icon={faLink} />}
                        additionalButtons={
                            <>
                                <Button
                                    {...ButtonStyles.pageToolbarButton}
                                    variant="outlined"
                                    onClick={() => navigate(generatePath(`${FrameworkPath.HOME}`))}
                                    disabled={isArchived}
                                >
                                    {isDirty ? strings('common:button.cancel') : strings('common:button.close')}
                                </Button>
                                <Button
                                    {...ButtonStyles.pageToolbarButton}
                                    variant="primary"
                                    onClick={handleSubmit(saveFramework)}
                                    disabled={!isValid || !isDirty || !canEdit || isArchived}
                                >
                                    {strings('common:button.save')}
                                </Button>
                            </>
                        }
                        variant="regular"
                        stateIndicator={framework ? { status: framework.status!, frameworkId: framework.id! } : undefined}
                    />
                    <MainLayout>
                        <FormProvider {...formMethods}>
                            <ContentLayout
                                sx={{ margin: 0 }}
                                disableScroll
                            >
                                <TabContext value={'1'}>
                                    <Box
                                        sx={{
                                            padding: '0px 24px',
                                            borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_231}`,
                                        }}
                                    >
                                        <TabList aria-label="permission and user tabs">
                                            <Tab
                                                label={strings('frameworks:button.edit')}
                                                value="1"
                                            />
                                        </TabList>
                                    </Box>
                                    <TabPanel
                                        value="1"
                                        sx={{
                                            p: 0,
                                            pt: '20px',
                                            padding: '0px 24px',
                                            backgroundColor: theme.palette.protechtGrey?.grey_250,
                                            overflowY: 'auto',
                                        }}
                                    >
                                        <Accordion defaultExpanded={true}>
                                            <AccordionSummary
                                                aria-controls={'accordion-header'}
                                                id={'accordion-header'}
                                            >
                                                <Typography variant="h5">{strings('frameworks:label.fwDetails')}</Typography>
                                            </AccordionSummary>
                                            <AccordionDetails sx={!canEdit ? { opacity: 0.7, pointerEvents: 'none' } : {}}>
                                                <Grid
                                                    container
                                                    spacing={3}
                                                >
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <FormField
                                                            name="name"
                                                            label={strings('frameworks:label.fwTitle')}
                                                            renderField={(field) => (
                                                                <Input
                                                                    {...field}
                                                                    type="text"
                                                                    placeholder={strings('frameworks:label.fwTitlePlaceholder')}
                                                                    autoComplete={'off'}
                                                                    disabled={isArchived}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <FormField
                                                            name="version"
                                                            label={strings('frameworks:label.version')}
                                                            renderField={(field) => (
                                                                <Input
                                                                    {...field}
                                                                    type="text"
                                                                    autoComplete={'off'}
                                                                    placeholder={strings('frameworks:label.versionPlaceholder')}
                                                                    disabled={isArchived}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <InputField
                                                            name="description"
                                                            label={strings('common:title.description')}
                                                            multiline
                                                            placeholder={strings('frameworks:label:descriptionPlaceholder')}
                                                            disabled={isArchived}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <SelectField
                                                            value={framework?.category?.id}
                                                            name="category"
                                                            variant="outlined"
                                                            label={strings('frameworks:label:category')}
                                                            options={categoryOptions}
                                                            disabled={isArchived}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        container
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <DialogSelectorField
                                                            name="metadataRegister"
                                                            displayValue={metadataRegister?.registerName}
                                                            placeholder={strings('frameworks:label.metadataRegisterPlaceholder')}
                                                            disabled={isArchived || metadataRegister?.readOnly}
                                                            label={strings('frameworks:label.metadataRegister')}
                                                            dataTestId="input-metadata-register"
                                                            renderDialog={({ ref: _ref, ...props }) => (
                                                                <MetadataRegisterSelector
                                                                    {...props}
                                                                    onSelect={onRegisterSelection}
                                                                    selected={metadataRegister}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <UserField
                                                            label={strings('frameworks:label.frameworkOwner')}
                                                            placeholder={strings('frameworks:label.ownerPlaceholder')}
                                                            name="owner"
                                                            disabled={isArchived}
                                                            valueMapping={mapSelectedUser}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <AttachmentField
                                                            formFieldProps={{
                                                                name: 'attachments',
                                                                label: strings('frameworks:label.fileInput'),
                                                                readOnly: isArchived,
                                                            }}
                                                            multiple={true}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    ></Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <SpaceBetween
                                                            sx={{
                                                                padding: '3px 0 8px',
                                                            }}
                                                        >
                                                            <Typography
                                                                sx={{ display: 'inline-block' }}
                                                                variant="body2"
                                                            >
                                                                {strings('frameworks:label.availableItems')}
                                                            </Typography>
                                                        </SpaceBetween>
                                                        <Box sx={{ display: 'flex', gap: '8px' }}>
                                                            {fields.length === 0 && (
                                                                <Input
                                                                    disabled
                                                                    placeholder={strings('frameworks:label.addItemPlaceholder')}
                                                                />
                                                            )}
                                                            <LinksList
                                                                keyExtractor={(item) => item.id!}
                                                                items={fields}
                                                                removeType="clear"
                                                                clearTooltip={strings('common:button.clear')}
                                                                onItemRemove={onItemRemove}
                                                                readonly={isArchived}
                                                                renderContent={(item: FrameworkRegisterRead, index: number) => (
                                                                    <>
                                                                        <LinkListItemTitle
                                                                            {...register(`allowedRegisters.${index}`)}
                                                                            title={item.registerName}
                                                                        ></LinkListItemTitle>
                                                                    </>
                                                                )}
                                                            />
                                                            <Button
                                                                {...ButtonStyles.inputButton}
                                                                variant="outlined"
                                                                onClick={() => setRegisterSelectorVisible(true)}
                                                                disabled={isArchived}
                                                            >
                                                                {strings('common:button.add')}
                                                            </Button>
                                                        </Box>
                                                    </Grid>
                                                </Grid>
                                            </AccordionDetails>
                                        </Accordion>
                                        <Accordion
                                            defaultExpanded={true}
                                            sx={{ marginTop: '28px' }}
                                        >
                                            <AccordionSummary
                                                aria-controls={'accordion-header'}
                                                id={'accordion-header'}
                                            >
                                                <Typography variant="h5">{strings('frameworks:label.review')}</Typography>
                                            </AccordionSummary>
                                            <AccordionDetails sx={!canEdit ? { opacity: 0.7, pointerEvents: 'none' } : {}}>
                                                <Grid
                                                    container
                                                    spacing={3}
                                                >
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <UserField
                                                            label={strings('frameworks:label.reviewer')}
                                                            placeholder={strings('frameworks:label.reviewerPlaceholder')}
                                                            name="reviewer"
                                                            disabled={isArchived}
                                                            valueMapping={mapSelectedUser}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <DateField
                                                            label={strings('frameworks:label.lastReviewDate')}
                                                            placeholder={strings('frameworks:label.lastReviewDatePlaceholder')}
                                                            name="lastReviewDate"
                                                            dateFormat={systemConfiguration?.global_date_format}
                                                            disabled={isArchived}
                                                        />
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={12}
                                                        md={6}
                                                    >
                                                        <InputField
                                                            name="worklog"
                                                            label={strings('frameworks:label.reviewComments')}
                                                            multiline
                                                            placeholder={strings('frameworks:label:reviewCommentsPlaceholder')}
                                                            disabled={isArchived}
                                                        />
                                                    </Grid>
                                                </Grid>
                                            </AccordionDetails>
                                        </Accordion>
                                    </TabPanel>
                                </TabContext>
                            </ContentLayout>
                        </FormProvider>
                    </MainLayout>
                    {registerSelectorVisible && (
                        <RegisterSelector
                            columns={RegisterSettingsColDef}
                            onClose={() => setRegisterSelectorVisible(false)}
                            onSelect={onSelection}
                            excludedIds={fields?.map((field) => field.id!)}
                            staticExpressions={[
                                {
                                    id: 0,
                                    expression: 'not in',
                                    property: 'subtableType',
                                    type: 'STRING',
                                    value: 'Private;Framework;Questionnaire',
                                },
                            ]}
                        />
                    )}
                </>
            )}
        </>
    );
};

export default Framework;
