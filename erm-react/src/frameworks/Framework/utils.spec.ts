import { FrameworkDetailRead } from 'api/generated/types';
import { mapToFormValues } from 'frameworks/Framework/utils';
import { initialValues } from 'frameworks/Framework/initial';

const sharedMockData: FrameworkDetailRead = {
    id: 12647,
    name: 'Lasto FW 1',
    description: '123',
    version: '123',
    category: {
        id: 2,
        name: 'Enterprise Risk',
    },
    status: {
        id: 1,
        name: 'DRAFT',
    },
    fileLinkList: [
        {
            name: '2.pdf',
            url: 'https://example.com/file1',
            uid: '0db1f585-1551-44f9-a8ea-c7f8b9e050a3',
        },
    ],
    allowedRegisters: [
        {
            id: 128,
            tableName: 'table_101590',
            registerName: 'Actions',
            appId: 3,
        },
    ],
    metadataRegister: {
        id: 3566,
        tableName: 'table_115700',
        registerName: 'Default Framework',
        readOnly: false,
        appId: 3700,
    },
    owner: {
        id: 100761,
        name: 'API-lowPermUser',
        username: 'API-lowPermUser',
    },
    reviewer: {
        id: 100040,
        name: 'Content Integration',
        username: 'content.integration',
    },
    lastReviewDate: '2024-10-03 00:00:00.000',
    worklog: [
        {
            id: 113891,
            sourceId: 12647,
            userId: 2220,
            userName: 'protecht.support',
            createDate: '2024-10-04 13:33:10.634',
            log: 'asd',
        },
    ],
};

const createMockBlob = () => {
    return new Blob([], { type: '' });
};

describe('mapToFormValues', () => {
    it('should map all fields correctly when data is provided', () => {
        const expected = {
            allowedRegisters: [
                {
                    appId: 3,
                    id: 128,
                    registerName: 'Actions',
                    tableName: 'table_101590',
                },
            ],
            attachments: [
                {
                    file: createMockBlob(),
                    name: '2.pdf',
                    size: 1,
                    url: 'https://example.com/file1',
                    uuid: '0db1f585-1551-44f9-a8ea-c7f8b9e050a3',
                },
            ],
            category: 2,
            description: '123',
            fileLinkList: [
                {
                    name: '2.pdf',
                    uid: '0db1f585-1551-44f9-a8ea-c7f8b9e050a3',
                    url: 'https://example.com/file1',
                },
            ],
            id: 12647,
            lastReviewDate: '2024-10-03 00:00:00.000',
            metadataRegister: {
                appId: 3700,
                id: 3566,
                readOnly: false,
                registerName: 'Default Framework',
                tableName: 'table_115700',
            },
            name: 'Lasto FW 1',
            owner: {
                id: 100761,
                name: 'API-lowPermUser',
                username: 'API-lowPermUser',
            },
            reviewer: {
                id: 100040,
                name: 'Content Integration',
                username: 'content.integration',
            },
            status: {
                id: 1,
                name: 'DRAFT',
            },
            version: '123',
            worklog: 'asd',
        };

        const result = mapToFormValues(initialValues, sharedMockData);
        expect(result).toEqual(expected);
    });

    it('should return initial values when no data is provided', () => {
        const expected = initialValues;
        const result = mapToFormValues(initialValues, {});
        expect(result).toEqual(expected);
    });
});
