import { Attachment } from '@protecht/ui-library/library/components/FileDropzone';
import { FrameworkRegisterRead, UserViewShared } from 'api/generated/types';

export type FrameworkForm = {
    name: string;
    version: string;
    description: string;
    category: string;
    attachments: Attachment[] | null;
    allowedRegisters: FrameworkRegisterRead[];
    metadataRegister: FrameworkRegisterRead | null;
    owner: UserViewShared | null;
    reviewer: UserViewShared | null;
    lastReviewDate: string | null;
    worklog?: string;
};
