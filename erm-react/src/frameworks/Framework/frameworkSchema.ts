import * as yup from 'yup';
import { strings } from 'common/utils/i18n';
import { NO_SELECTED_VALUE } from '@protecht/ui-library/library/constants';

export const createValidationSchema = (categories: string[], frameworks: string[]): yup.AnyObjectSchema => {
    const schema = yup.object().shape({
        name: yup.string().required(strings('common:validators.requiredSimple')).min(1).max(100).notOneOf(frameworks, strings('frameworks:messages.fwExists')),
        version: yup.string().required(strings('common:validators.requiredSimple')).min(1),
        category: yup.string().oneOf([...categories, NO_SELECTED_VALUE]),
        file: yup.array().of(yup.mixed()),
        allowedRegisters: yup.array().of(yup.mixed()),
        metadataRegister: yup
            .object()
            .shape({
                id: yup.number().required(strings('common:validators.requiredSimple')),
                registerName: yup.string().required(strings('common:validators.requiredSimple')),
                tableName: yup.string().required(strings('common:validators.requiredSimple')),
            })
            .required(strings('common:validators.requiredSimple'))
            .nullable(),
        lastReviewDate: yup.string().nullable(),
        owner: yup
            .object()
            .shape({
                id: yup.number().required(strings('common:validators.requiredSimple')),
                name: yup.string().required(strings('common:validators.requiredSimple')),
                username: yup.string(),
            })
            .nullable(),
        reviewer: yup
            .object()
            .shape({
                id: yup.number().required(strings('common:validators.requiredSimple')),
                name: yup.string().required(strings('common:validators.requiredSimple')),
                username: yup.string(),
            })
            .nullable(),
    });
    return schema;
};
