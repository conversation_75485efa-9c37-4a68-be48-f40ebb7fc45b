import { fileSchema } from './NodesIOSchema';

// Mock the strings function
jest.mock('common/utils/i18n', () => ({
  strings: jest.fn((key, params) => {
    if (key === 'common:validators.requiredSimple') return 'This field is required';
    if (key === 'common:validation.fileType') {
      // Handle parameter interpolation
      return `File must be of type ${params?.type || ''}`;
    }
    return key;
  }),
}));

describe('NodesIOSchema', () => {
  describe('fileSchema', () => {
    it('should validate a valid CSV file', async () => {
      // Create a mock file object with a CSV extension
      const mockFile = {
        name: 'test.csv',
        file: new File([], 'test.csv'),
      };

      // Validate the schema with the mock file
      const result = await fileSchema.isValid({ importFile: mockFile });
      
      expect(result).toBe(true);
    });

    it('should reject a non-CSV file', async () => {
      // Create a mock file object with a non-CSV extension
      const mockFile = {
        name: 'test.txt',
        file: new File([], 'test.txt'),
      };

      // Validate the schema with the mock file
      const result = await fileSchema.isValid({ importFile: mockFile });
      
      expect(result).toBe(false);

      // Test the error message using rejects matcher
      await expect(fileSchema.validate({ importFile: mockFile }))
        .rejects.toHaveProperty('message', 'File must be of type .csv');
    });

    it('should require a file to be provided', async () => {
      // Validate the schema with no file
      const result = await fileSchema.isValid({ importFile: null });
      
      expect(result).toBe(false);

      // Test the error message using rejects matcher
      await expect(fileSchema.validate({ importFile: null }))
        .rejects.toHaveProperty('message', 'This field is required');
    });

    it('should handle undefined values', async () => {
      // Validate the schema with undefined
      const result = await fileSchema.isValid({ importFile: undefined });
      
      expect(result).toBe(false);

      // Test the error message using rejects matcher
      await expect(fileSchema.validate({ importFile: undefined }))
        .rejects.toHaveProperty('message', 'This field is required');
    });

    it('should validate case-insensitive CSV extension', async () => {
      // Create a mock file object with uppercase CSV extension
      const mockFile = {
        name: 'test.CSV',
        file: new File([], 'test.CSV'),
      };

      // Validate the schema with the mock file
      const result = await fileSchema.isValid({ importFile: mockFile });
      
      expect(result).toBe(true);
    });
  });
});