import React, { FC, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { generatePath, useNavigate } from 'react-router';
import {
    useFcGetFrameworkUsingGetQuery,
    useFcGetFrameworkNodesHierarchyUsingGetQuery,
    useFcUpdateNodesHierarchyUsingPutMutation,
    frameworksApi,
    linksApi,
} from '../rtkApi';
import { strings } from 'common/utils/i18n';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import MainLayout from 'common/layouts/MainLayout';
import { TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { applyLinksToTree, hierarchyToTreeNodes, treeItemsToHierarchy } from './utils';
import { FrameworkPath } from '../routes';
import { useNumericParam } from 'common/hooks/useNumericParam';
import { Link as SvgLink } from '@protecht/ui-library/library/components/SVGIcons';
import _ from 'lodash';
import store, { useSelector, useThunkDispatch } from 'store';
import { useUnsavedChangesAlert } from 'common/hooks/useUnsavedChangesAlert';
import useSnackbar from 'common/hooks/useSnackbar';
import TreeWrapper from 'frameworks/FrameworkNodes/SortableTree/TreeWrapper';
import ContentLayout from 'common/layouts/ContentLayout';
import { ArrowNavigation } from 'common/hooks/arrowNavigationTyped';
import { FrameworkStatus } from 'frameworks/FrameworkState/FrameworkState';
import { FrameworkNodesLinksCounts, LinksMap } from 'frameworks/types';
import LoadingOverlay from 'common/components/LoadingOverlay';
import { FrameworkContext } from 'frameworks/ContextProvider';
import { DialogType } from 'common/types';
import { hasEditPermission } from 'frameworks/selectors';
import FrameworkToolbar from 'frameworks/FrameworkToolbar';
import { PARENT_ID } from 'frameworks/const';
import NodesIO from 'frameworks/FrameworkNodes/NodesIO';
import ToolbarDivider, { ToolbarDividerType } from 'common/components/ToolbarSpacing/ToolbarDivider';

export interface FrameworkNodesProps {
    disableToolbar?: boolean;
}

const FrameworkNodes: FC<FrameworkNodesProps> = ({ disableToolbar }) => {
    const navigate = useNavigate();
    const id = useNumericParam('id');
    const dispatch = useThunkDispatch();
    const { collapsedMap, selectedNode } = useContext(FrameworkContext);
    const { data: framework, isLoading: isLoadingFramework } = useFcGetFrameworkUsingGetQuery({ id: id! });
    const { data: frameworkNodesHierarchy, isLoading: isLoadingHierarchy } = useFcGetFrameworkNodesHierarchyUsingGetQuery({ id: id! });
    const [updateNodeHierarchy] = useFcUpdateNodesHierarchyUsingPutMutation();

    const canEdit = useSelector((state) => hasEditPermission(state, id!));

    const [treeNodeItems, setTreeNodeItems] = useState<TreeItems>();
    const [treeNodeItemsWithLinks, setTreeNodeItemsWithLinks] = useState<TreeItems>();
    const [linksCounts, setLinksCounts] = useState<FrameworkNodesLinksCounts>();
    const { enqueueSuccess } = useSnackbar();

    useEffect(() => {
        window.ReactBridge!.FrameworkConfiguration!.refresh = () => {
            void store.dispatch(frameworksApi.util.invalidateTags(['nodes']));
            void store.dispatch(linksApi.util.invalidateTags(['linksCounts', 'nodeDetailedCounts']));
        };
    }, [selectedNode]);

    const hierarchyToSave = useMemo(() => {
        return treeNodeItems?.[0].children?.length ? treeItemsToHierarchy(treeNodeItems[0].children) : [];
    }, [treeNodeItems]);

    useEffect(() => {
        if (frameworkNodesHierarchy) {
            const frameWorkCollapsedMap = collapsedMap[id!];
            const tree = hierarchyToTreeNodes(frameworkNodesHierarchy, PARENT_ID, frameWorkCollapsedMap);
            setTreeNodeItems([
                {
                    id: PARENT_ID,
                    label: framework?.name,
                    collapsed: false,
                    children: tree,
                    links: { linksCount: linksCounts?.uniqueLinksCount || 0, linksIds: [], linksNames: [], nodeId: 0 },
                },
            ]);
        }
    }, [frameworkNodesHierarchy]);

    useEffect(() => {
        const linksMap: LinksMap = {};
        linksCounts?.nodeLinksCounts?.forEach((item) => {
            linksMap[item.nodeId] = item;
        });
        if (treeNodeItems && treeNodeItems?.length > 0) {
            setTreeNodeItemsWithLinks([
                {
                    id: PARENT_ID,
                    label: framework?.name,
                    collapsed: false,
                    children: treeNodeItems[0].children ? applyLinksToTree(treeNodeItems[0].children, linksMap) : [],
                    links: { linksCount: linksCounts?.uniqueLinksCount || 0, linksIds: [], linksNames: [], nodeId: 0 },
                },
            ]);
        }
    }, [framework, treeNodeItems, linksCounts]);

    const hierarchiesEqual = useMemo(() => {
        const simplify = (items) =>
            items?.map(({ id, name, order, childNodes }) => ({
                id: String(id),
                name,
                order,
                childNodes: simplify(childNodes),
            }));
        return _.isEqual(simplify(frameworkNodesHierarchy), simplify(hierarchyToSave));
    }, [frameworkNodesHierarchy, hierarchyToSave]);

    const saveNodeHierarchy = useCallback(async () => {
        const response = await updateNodeHierarchy({ id: id!, body: hierarchyToSave }).unwrap();
        if (response) {
            enqueueSuccess(strings('ermMessages:saving_success'));
            dispatch(
                frameworksApi.util.updateQueryData('fcGetFrameworkNodesHierarchyUsingGet', { id: id! }, (draft) => {
                    return Object.assign(draft, response);
                }),
            );
        }
    }, [updateNodeHierarchy, enqueueSuccess, hierarchyToSave, id, frameworksApi]);

    useUnsavedChangesAlert({
        blockNavigation: !hierarchiesEqual,
        onConfirm: saveNodeHierarchy,
        dialogType: DialogType.UNSAVED,
    });

    const share = useCallback(async () => {
        await navigator.clipboard.writeText(window.location.href).then(() => {
            enqueueSuccess(strings('common:message.linkCopied'));
        });
    }, []);

    return (
        <>
            {!disableToolbar && (
                <FrameworkToolbar
                    title={`${framework?.name || ''} ${framework?.version ? `${framework?.version}` : ''}`}
                    onBack={() => navigate(generatePath(`${FrameworkPath.HOME}`))}
                    showShareButton={true}
                    onShare={share}
                    shareIcon={<SvgLink />}
                    additionalButtons={
                        <>
                            {framework && (
                                <NodesIO
                                    framework={framework}
                                    selectedNode={selectedNode}
                                    isHierarchyDirty={!hierarchiesEqual}
                                    saveNodeHierarchy={saveNodeHierarchy}
                                />
                            )}
                            <ToolbarDivider type={ToolbarDividerType.DIVIDER} />
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                variant="outlined"
                                onClick={() => navigate(generatePath(`${FrameworkPath.HOME}`))}
                            >
                                {!hierarchiesEqual ? strings('common:button.cancel') : strings('common:button.close')}
                            </Button>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                variant="primary"
                                disabled={hierarchiesEqual}
                                onClick={saveNodeHierarchy}
                            >
                                {strings('common:button.save')}
                            </Button>
                        </>
                    }
                    variant="regular"
                    stateIndicator={framework ? { status: framework.status!, frameworkId: framework.id! } : undefined}
                />
            )}
            <MainLayout>
                <ContentLayout disableScroll>
                    {treeNodeItemsWithLinks && framework && (
                        <ArrowNavigation
                            mode={'bounded'}
                            initialIndex={[1, 0]}
                            reInitOnDeactivate={false}
                        >
                            <TreeWrapper
                                readOnly={framework.status?.id === FrameworkStatus.ARCHIVED || !canEdit || disableToolbar}
                                framework={framework}
                                treeNodeItems={treeNodeItemsWithLinks}
                                setTreeNodeItems={setTreeNodeItems}
                                setLinksCounts={setLinksCounts}
                                isHierarchyDirty={!hierarchiesEqual}
                                saveNodeHierarchy={saveNodeHierarchy}
                            />
                        </ArrowNavigation>
                    )}
                </ContentLayout>
            </MainLayout>
            <LoadingOverlay open={isLoadingFramework || isLoadingHierarchy} />
        </>
    );
};

export default FrameworkNodes;
