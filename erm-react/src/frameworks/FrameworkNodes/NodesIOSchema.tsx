import * as yup from 'yup';
import { strings } from 'common/utils/i18n';

// Define schema for file validation
export const fileSchema = yup.object({
    importFile: yup.mixed()
        .required(strings('common:validators.requiredSimple'))
        .test('fileType', strings('common:validation.fileType', { type: '.csv' }),
            (file) => {
                // Check if file exists before accessing properties
                if (!file) {
                    return true;
                }
                return file.name.toLowerCase().endsWith('.csv');
            })
});

// Type definition for form values
export type FileFormValues = yup.InferType<typeof fileSchema>;
