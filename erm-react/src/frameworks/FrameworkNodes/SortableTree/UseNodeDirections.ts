import { TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { useDeferredValue, useMemo } from 'react';
import { findNodeDirections } from 'frameworks/FrameworkNodes/SortableTree/utils/arrow';
import { PARENT_ID } from 'frameworks/const';
import { SelectedNode } from 'frameworks/ContextProvider';

export const useNodeDirections = (treeNodeItems: TreeItems, searchValue: string, selectedNode?: SelectedNode) => {
    const selectedNodeDeferred = useDeferredValue(selectedNode);

    return useMemo(() => {
        const result = findNodeDirections(treeNodeItems, treeNodeItems, selectedNodeDeferred?.id);

        if (searchValue.length || !selectedNodeDeferred?.id || selectedNodeDeferred?.id === PARENT_ID || !result) {
            return { canMoveUp: false, canMoveDown: false, canNest: false, canElevate: false };
        }

        return result;
    }, [selectedNodeDeferred, treeNodeItems, findNodeDirections, searchValue]);
};
