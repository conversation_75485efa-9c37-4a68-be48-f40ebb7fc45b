import React from 'react';
import { render, screen } from 'test/utils';
import TreeItemContent from './TreeItemContent';
import { PARENT_ID } from 'frameworks/const';
import { emitNotification } from 'frameworks/NotificationService';
import { FlattenedItem } from './types';
import { FrameworkRegisterRead } from 'api/generated/types';
import { NodeLinksCount } from 'frameworks/types';

jest.mock('frameworks/NotificationService', () => ({
    emitNotification: jest.fn(),
}));

const mockEmitNotification = emitNotification as jest.MockedFunction<typeof emitNotification>;

const mockSetSelectedNode = jest.fn();
const mockOnCollapse = jest.fn();
const mockSetTotalNodeLinkCount = jest.fn();
const mockSetNodeId = jest.fn();

const defaultProps = {
    id: '1',
    childCount: 0,
    clone: false,
    collapsed: false,
    disableInteraction: false,
    disableSelection: false,
    readOnly: false,
    onCollapse: mockOnCollapse,
    setSelectedNode: mockSetSelectedNode,
    setTotalNodeLinkCount: mockSetTotalNodeLinkCount,
    setNodeId: mockSetNodeId,
    tabIndex: 0,
};

const mockNodeLinksCount: NodeLinksCount = {
    nodeId: 1,
    linksCount: 5,
    linksIds: ['1', '2', '3', '4', '5'],
    linksNames: ['Link 1', 'Link 2', 'Link 3', 'Link 4', 'Link 5'],
};

const mockNestedLinksCount1: NodeLinksCount = {
    nodeId: 2,
    linksCount: 2,
    linksIds: ['6', '7'],
    linksNames: ['Link 6', 'Link 7'],
};

const mockNestedLinksCount2: NodeLinksCount = {
    nodeId: 3,
    linksCount: 3,
    linksIds: ['8', '9', '10'],
    linksNames: ['Link 8', 'Link 9', 'Link 10'],
};

const mockItem: Partial<FlattenedItem> = {
    id: '1',
    label: 'Test Item',
    registerEntryId: 123,
    absoluteIndex: 0,
    links: mockNodeLinksCount,
    nestedLinks: [mockNestedLinksCount1, mockNestedLinksCount2],
};

const mockSelectedRegister: FrameworkRegisterRead = {
    id: 1,
    registerName: 'Test Register',
    appId: 1,
};

const mockStore = {
    frameworks: {
        flattenedTree: [],
        searchValue: '',
    },
};

describe('TreeItemContent', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders item label correctly', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
            />,
            { preloadedState: mockStore },
        );

        expect(screen.getByText('Test Item')).toBeInTheDocument();
        const buttons = screen.getAllByLabelText('triangle-down');
        expect(buttons).toHaveLength(2); // One for drag handle, one for collapse
        expect(buttons[0]).toBeEnabled();
        expect(buttons[1]).toBeDisabled();
    });

    it('renders highlighted text when search value is present', () => {
        const storeWithSearch = {
            frameworks: {
                flattenedTree: [],
                searchValue: 'Test',
            },
        };

        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
            />,
            { preloadedState: storeWithSearch },
        );

        // When search is present, text is split into highlighted and non-highlighted parts
        expect(screen.getByText('Test')).toBeInTheDocument();
        expect(
            screen.getByText((_, element) => {
                return element?.textContent === ' Item';
            }),
        ).toBeInTheDocument();
    });

    it('does not render drag handle when readOnly is true', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                readOnly={true}
            />,
            { preloadedState: mockStore },
        );

        // When readOnly is true, only the disabled triangle button should be present
        const buttons = screen.getAllByLabelText('triangle-down');
        expect(buttons).toHaveLength(1);
        expect(buttons[0]).toBeDisabled();
    });

    it('does not render drag handle for parent item', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={{ ...mockItem, id: PARENT_ID }}
            />,
            { preloadedState: mockStore },
        );

        const dragButtons = screen.queryAllByLabelText('triangle-down');
        expect(dragButtons).toHaveLength(1); // Only the collapse button
    });

    it('renders collapse button when childCount > 0', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                childCount={3}
            />,
            { preloadedState: mockStore },
        );

        const collapseButtons = screen.getAllByRole('button');
        expect(collapseButtons.length).toBeGreaterThan(0);
    });

    it('renders disabled triangle when childCount is 0', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                childCount={0}
            />,
            { preloadedState: mockStore },
        );

        const buttons = screen.getAllByRole('button');
        const disabledButton = buttons.find((button) => button.hasAttribute('disabled'));
        expect(disabledButton).toBeDisabled();
    });

    it('calls setSelectedNode and onCollapse when collapse button is clicked', async () => {
        const { user } = render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                childCount={3}
            />,
            { preloadedState: mockStore },
        );

        const collapseButton = screen.getAllByRole('button')[1]; // Second button is collapse
        await user.click(collapseButton);

        expect(mockSetSelectedNode).toHaveBeenCalledWith({
            id: '1',
            registerEntryId: 123,
            collapsed: true,
            hasChildren: true,
            absoluteIndex: 0,
        });
        expect(mockOnCollapse).toHaveBeenCalledWith('1');
        expect(mockEmitNotification).toHaveBeenCalledWith('1');
    });

    it('does not call onCollapse for parent item', async () => {
        const { user } = render(
            <TreeItemContent
                {...defaultProps}
                item={{ ...mockItem, id: PARENT_ID }}
                childCount={3}
                id={PARENT_ID}
            />,
            { preloadedState: mockStore },
        );

        // Find the collapse button (not the drag handle)
        const buttons = screen.getAllByRole('button');
        const collapseButton = buttons.find((button) => !button.hasAttribute('disabled') && button.getAttribute('aria-label') !== 'triangle-down');

        if (collapseButton) {
            await user.click(collapseButton);
        }

        expect(mockSetSelectedNode).toHaveBeenCalled();
        expect(mockOnCollapse).not.toHaveBeenCalled();
        expect(mockEmitNotification).toHaveBeenCalledWith(PARENT_ID);
    });

    it('renders expanded link when not collapsed and has links', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                collapsed={false}
                selectedRegister={mockSelectedRegister}
            />,
            { preloadedState: mockStore },
        );

        const item = screen.getByText('5 Test Register');
        expect(item).toBeInTheDocument();
        expect(item.onclick).toBeDefined();
    });

    it('does not render expanded link when collapsed', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                collapsed={true}
                selectedRegister={mockSelectedRegister}
            />,
            { preloadedState: mockStore },
        );

        // When collapsed, it should show the collapsed link instead
        const item = screen.getByText('5 Test Register');
        expect(item).toBeInTheDocument();
        expect(item.onclick).toBeNull();
    });

    it('calls handleOpenLinksDialog when expanded link is clicked', async () => {
        const { user } = render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                collapsed={false}
                selectedRegister={mockSelectedRegister}
            />,
            { preloadedState: mockStore },
        );

        const link = screen.getByText('5 Test Register');
        await user.click(link);

        expect(mockSetTotalNodeLinkCount).toHaveBeenCalledWith(5);
        expect(mockSetNodeId).toHaveBeenCalledWith(1);
    });

    it('calls handleOpenLinksDialog when Enter key is pressed on expanded link', async () => {
        const { user } = render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                collapsed={false}
                selectedRegister={mockSelectedRegister}
            />,
            { preloadedState: mockStore },
        );

        const link = screen.getByText('5 Test Register');
        link.focus();
        await user.keyboard('{Enter}');

        expect(mockSetTotalNodeLinkCount).toHaveBeenCalledWith(5);
        expect(mockSetNodeId).toHaveBeenCalledWith(1);
    });

    it('renders child count when clone is true and childCount > 1', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                clone={true}
                childCount={3}
            />,
            { preloadedState: mockStore },
        );

        expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('does not render child count when clone is false', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                clone={false}
                childCount={3}
            />,
            { preloadedState: mockStore },
        );

        expect(screen.queryByText('3')).not.toBeInTheDocument();
    });

    it('does not render child count when childCount <= 1', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                clone={true}
                childCount={1}
            />,
            { preloadedState: mockStore },
        );

        expect(screen.queryByText('1')).not.toBeInTheDocument();
    });

    it('handles missing item gracefully', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={undefined}
            />,
            { preloadedState: mockStore },
        );

        expect(screen.queryByText('Test Item')).not.toBeInTheDocument();
    });

    it('handles missing links gracefully', () => {
        const itemWithoutLinks = {
            ...mockItem,
            links: undefined,
            nestedLinks: undefined,
        };

        render(
            <TreeItemContent
                {...defaultProps}
                item={itemWithoutLinks}
                selectedRegister={mockSelectedRegister}
            />,
            { preloadedState: mockStore },
        );

        expect(screen.queryByText('Test Register')).not.toBeInTheDocument();
    });

    it('calculates nested links count correctly', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                collapsed={true}
                selectedRegister={mockSelectedRegister}
            />,
            { preloadedState: mockStore },
        );

        // nestedLinks: [{ linksCount: 2 }, { linksCount: 3 }] = 5 total
        expect(screen.getByText('5 Test Register')).toBeInTheDocument();
    });

    it('handles empty nested links array', () => {
        const itemWithEmptyNestedLinks = {
            ...mockItem,
            nestedLinks: [],
        };

        render(
            <TreeItemContent
                {...defaultProps}
                item={itemWithEmptyNestedLinks}
                collapsed={true}
                selectedRegister={mockSelectedRegister}
            />,
            { preloadedState: mockStore },
        );

        expect(screen.queryByText('Test Register')).not.toBeInTheDocument();
    });

    it('applies correct tabIndex to links', () => {
        render(
            <TreeItemContent
                {...defaultProps}
                item={mockItem}
                collapsed={false}
                selectedRegister={mockSelectedRegister}
                tabIndex={0}
            />,
            { preloadedState: mockStore },
        );

        const link = screen.getByText('5 Test Register');
        expect(link).toHaveAttribute('tabindex', '0');
    });
});
