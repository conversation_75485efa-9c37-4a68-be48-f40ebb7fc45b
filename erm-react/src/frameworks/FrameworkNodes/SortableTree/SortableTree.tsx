import {
    closestCenter,
    DndContext,
    DragEndEvent,
    DragMoveEvent,
    DragOverEvent,
    DragOverlay,
    DragStartEvent,
    MeasuringStrategy,
    Modifier,
    PointerSensor,
    UniqueIdentifier,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import SortableTreeItem from 'frameworks/FrameworkNodes/SortableTree/SortableTreeItem';
import React, { FC, useCallback, useContext, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { styled } from '@mui/material/styles';
import { buildTree, flattenTree, getChildCount, getProjection, removeChildrenOf } from 'frameworks/FrameworkNodes/SortableTree/utils/tree';
import { FlattenedItem, TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { dropAnimationConfig } from 'frameworks/FrameworkNodes/SortableTree/dropAnimation';
import { FrameworkContext } from 'frameworks/ContextProvider';
import { Virtuoso, VirtuosoHandle } from 'react-virtuoso';
import { INDENTATION_WIDTH } from 'frameworks/const';
import { useDispatch } from 'store';
import { setFlattenedTree } from 'frameworks/reducer';

const StyledVirtuoso = styled(Virtuoso)(({ theme }) => ({
    border: '1px solid',
    borderColor: theme.palette.protechtGrey?.grey_238,
    ...theme.mixins?.scrollbar?.(true),
}));

const measuring = {
    droppable: {
        strategy: MeasuringStrategy.Always,
    },
};

const sensorOptions = {
    activationConstraint: {
        delay: 10,
        tolerance: 5,
    },
};

type SortableTreeProps = {
    items: TreeItems;
    onItemsSorted(arg: TreeItems): void;
    collapsible?: boolean;
    indicator?: boolean;
    removable?: boolean;
    dragDisabled?: boolean;
    readOnly?: boolean;
    handleOpenDetail: (id: string, registerEntryId?: number) => void;
    onCollapse(nodeId: string): void;
};

const SortableTree: FC<SortableTreeProps> = ({
    items,
    onItemsSorted,
    collapsible,
    indicator = false,
    dragDisabled,
    readOnly,
    onCollapse,
    handleOpenDetail,
}) => {
    const dispatch = useDispatch();
    const listRef = useRef<VirtuosoHandle>(null);
    const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
    const [overId, setOverId] = useState<UniqueIdentifier | null>(null);
    const [offsetLeft, setOffsetLeft] = useState(0);
    const { selectedNode, arrowsBlocked, setSelectedNode, selectedRegister, setNodeId, setTotalNodeLinkCount } = useContext(FrameworkContext);

    const [flattenedItems, setFlattenedItems] = useState<FlattenedItem[]>([]);

    useLayoutEffect(() => {
        const initialFlattenedTree = flattenTree(items);
        const collapsedItems = initialFlattenedTree.reduce((acc, { children, collapsed, id }) => {
            return collapsed && children?.length ? [...acc, id] : acc;
        }, [] as UniqueIdentifier[]);

        const idsToRemove = activeId ? [activeId, ...collapsedItems] : collapsedItems;
        const newFlattenedItems = removeChildrenOf(initialFlattenedTree, idsToRemove);

        dispatch(setFlattenedTree(newFlattenedItems));
        setFlattenedItems(newFlattenedItems);
    }, [activeId, items]);

    const projected = activeId && overId ? getProjection(flattenedItems, activeId, overId, offsetLeft, INDENTATION_WIDTH) : undefined;

    const sensors = useSensors(useSensor(PointerSensor, sensorOptions));

    const sortedIds = useMemo(() => flattenedItems.map(({ id }) => id), [flattenedItems]);
    const activeItem = activeId ? flattenedItems.find(({ id }) => id === activeId) : null;

    const handleDragStart = useCallback(({ active: { id: activeId } }: DragStartEvent) => {
        setActiveId(activeId);
        setOverId(activeId);

        document.body.style.setProperty('cursor', 'grabbing');
    }, []);

    const handleDragMove = useCallback(({ delta }: DragMoveEvent) => {
        setOffsetLeft(delta.x);
    }, []);

    const handleDragOver = useCallback(({ over }: DragOverEvent) => {
        setOverId(over?.id ?? null);
    }, []);

    const resetState = useCallback(() => {
        setOverId(null);
        setActiveId(null);
        setOffsetLeft(0);

        document.body.style.setProperty('cursor', '');
    }, []);

    const handleDragEnd = useCallback(
        ({ active, over }: DragEndEvent) => {
            resetState();

            if (projected && over) {
                const { depth, parentId } = projected;
                const clonedItems: FlattenedItem[] = JSON.parse(JSON.stringify(flattenTree(items)));
                const overIndex = clonedItems.findIndex(({ id }) => id === over.id);
                const activeIndex = clonedItems.findIndex(({ id }) => id === active.id);
                const activeTreeItem = clonedItems[activeIndex];

                clonedItems[activeIndex] = { ...activeTreeItem, depth, parentId };

                const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);
                const newItems = buildTree(sortedItems);

                onItemsSorted(newItems);
            }
        },
        [projected, onItemsSorted, items, resetState],
    );

    const handleDragCancel = useCallback(() => {
        resetState();
    }, [resetState]);

    const adjustTranslate: Modifier = useCallback(({ transform }) => {
        return {
            ...transform,
            y: transform.y - 25,
        };
    }, []);

    const memoItem = useCallback(
        (index, item: FlattenedItem) => {
            return (
                <SortableTreeItem
                    index={index}
                    arrowsBlocked={arrowsBlocked}
                    isSelected={selectedNode?.id === item.id}
                    disableInteraction={dragDisabled}
                    readOnly={readOnly}
                    item={item}
                    key={item.id}
                    id={item.id.toString()}
                    depth={item.id === activeId && projected ? projected.depth : item.depth}
                    indicator={indicator}
                    childCount={item.children?.length || 0}
                    collapsed={Boolean(item.collapsed && item.children?.length)}
                    disableSelection={false}
                    setSelectedNode={setSelectedNode}
                    setNodeId={setNodeId}
                    setTotalNodeLinkCount={setTotalNodeLinkCount}
                    openDetail={handleOpenDetail}
                    selectedRegister={selectedRegister}
                    onCollapse={onCollapse}
                />
            );
        },
        [
            arrowsBlocked,
            selectedNode,
            dragDisabled,
            readOnly,
            collapsible,
            indicator,
            projected,
            setSelectedNode,
            selectedRegister,
            handleOpenDetail,
            setNodeId,
            setTotalNodeLinkCount,
        ],
    );

    return (
        <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            measuring={measuring}
            onDragStart={handleDragStart}
            onDragMove={handleDragMove}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
            onDragCancel={handleDragCancel}
        >
            <SortableContext
                disabled={dragDisabled}
                items={sortedIds}
                strategy={verticalListSortingStrategy}
            >
                <StyledVirtuoso
                    data={flattenedItems}
                    itemContent={memoItem}
                    ref={listRef}
                    increaseViewportBy={30}
                />
                <DragOverlay
                    dropAnimation={dropAnimationConfig}
                    modifiers={indicator ? [adjustTranslate] : undefined}
                >
                    {activeId && activeItem ? (
                        <SortableTreeItem
                            id={activeId.toString()}
                            depth={activeItem.depth}
                            clone
                            childCount={getChildCount(items, activeId) + 1}
                        />
                    ) : null}
                </DragOverlay>
            </SortableContext>
        </DndContext>
    );
};

export default React.memo(SortableTree);
