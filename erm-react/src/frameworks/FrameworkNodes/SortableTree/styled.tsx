import { styled } from '@mui/material/styles';
import { defaultStyledOptions } from '@protecht/ui-library/library/utils/defaultStyledOptions';

export const StyledChildCount = styled(
    'span',
    defaultStyledOptions,
)<{ $clone: boolean; $disableSelection?: boolean }>(({ $clone, $disableSelection }) => ({
    position: 'absolute',
    top: '-10px',
    right: '-10px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '24px',
    height: '24px',
    borderRadius: '50%',
    backgroundColor: '#2389ff',
    fontSize: '0.8rem',
    fontWeight: 600,
    color: '#fff',
    ...($disableSelection || $clone
        ? {
              userSelect: 'none',
              WebkitUserSelect: 'none',
          }
        : {}),
}));

export const StyledListItem = styled(
    'li',
    defaultStyledOptions,
)<{
    $even?: boolean;
    $clone?: boolean;
    $ghost?: boolean;
    $indicator;
    $disableSelection?: boolean;
    $disableInteraction?: boolean;
    $selected?: boolean;
}>((props) => {
    const { theme, $even, $clone, $ghost, $indicator, $selected } = props;

    return {
        listStyle: 'none',
        boxSizing: 'border-box',
        position: 'relative',
        backgroundColor: $even ? `${theme.palette.protechtGrey?.grey_250}` : 'transparent',
        '&:hover': {
            '&::before': {
                content: '" "',
                background: $selected ? 'rgba(0, 0, 0, 0.16)' : 'rgba(0, 0, 0, 0.085)',
                position: 'absolute',
                width: '100%',
                height: '100%',
                left: '0px',
            },
        },
        ...($selected && {
            '&::before': {
                content: '" "',
                background: 'rgba(0, 0, 0, 0.16)',
                position: 'absolute',
                width: '100%',
                height: '100%',
                left: '0px',
            },
        }),
        ...($clone && {
            display: 'inline-block',
            pointerEvents: 'none',
            padding: '0 10px 5px 10px',
        }),
        ...($ghost && {
            color: 'black',
            opacity: $indicator ? 1 : 0.5,
            position: $indicator ? 'relative' : 'unset',
            zIndex: $indicator ? 1 : 'auto',
            marginBottom: $indicator ? '-1px' : 'auto',
        }),
    };
});

export const StyledTreeItem = styled('div')<{ $ghost?: boolean; $clone?: boolean; $indicator?: boolean }>(({ $ghost, $clone, $indicator }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: '34px',
    position: 'relative',
    color: '#222',
    boxSizing: 'border-box',
    ...($ghost && {
        '> *': {
            boxShadow: 'none',
            backgroundColor: 'transparent',
        },
    }),
    ...($clone && {
        border: '1px solid #dedede',
        '--vertical-padding': '5px',
        paddingRight: '24px',
        borderRadius: '4px',
        boxShadow: '0px 15px 15px 0 rgba(34, 33, 81, 0.1)',
    }),
    ...($ghost &&
        $indicator && {
            position: 'relative',
            padding: 0,
            height: '8px',
            '&:before': {
                position: 'absolute',
                left: '-8px',
                top: '-4px',
                display: 'block',
                content: '""',
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: '1px solid #2389ff',
            },
            '> *': {
                opacity: 0,
                height: 0,
            },
        }),
}));
