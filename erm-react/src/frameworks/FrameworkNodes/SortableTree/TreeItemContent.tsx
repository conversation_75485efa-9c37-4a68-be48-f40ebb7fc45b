import React, { HTMLAttributes, useCallback, useMemo } from 'react';
import useTheme from '@mui/system/useTheme';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { BaseTreeItemProps } from 'frameworks/FrameworkNodes/SortableTree/types';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import { TriangleDown } from '@protecht/ui-library/library/components/SVGIcons';
import { StyledChildCount } from 'frameworks/FrameworkNodes/SortableTree/styled';
import { Drag } from '@protecht/ui-library/library/components/SVGIcons';
import { PARENT_ID } from 'frameworks/const';
import { emitNotification } from 'frameworks/NotificationService';
import Highlighted from 'common/components/Highlighted';
import { useSelector } from 'store';
import { getSearchValue } from 'frameworks/selectors';
import { alpha } from '@mui/material/styles';

type TreeItemContentProps = Omit<HTMLAttributes<HTMLLIElement>, 'id'> &
    BaseTreeItemProps & {
        handleProps?: any;
        tabIndex?: number;
    };

const TreeItemContent: React.FC<TreeItemContentProps> = ({
    id,
    item,
    childCount,
    clone,
    disableSelection,
    disableInteraction,
    handleProps,
    collapsed,
    onCollapse,
    readOnly,
    setSelectedNode,
    selectedRegister,
    setTotalNodeLinkCount,
    setNodeId,
    tabIndex,
}) => {
    const theme = useTheme();
    const nestedLinksCount = useMemo(
        () => item?.nestedLinks?.flatMap((link) => link.linksCount).reduce((accumulator, currentValue) => accumulator + currentValue, 0),
        [item?.nestedLinks],
    );
    const searchValue = useSelector(getSearchValue);

    const handleOpenLinksDialog = useCallback(
        (e) => {
            e.stopPropagation();
            setTotalNodeLinkCount?.(item?.links?.linksCount);
            setNodeId?.(parseInt(id));
        },
        [setTotalNodeLinkCount, setNodeId, id, item?.links?.linksCount],
    );

    const ChildCount = useCallback(() => {
        if (clone && childCount && childCount > 1) {
            return (
                <StyledChildCount
                    $clone={clone}
                    $disableSelection={disableSelection}
                >
                    {childCount}
                </StyledChildCount>
            );
        }
    }, [clone, childCount, disableSelection]);

    const ExpandedLink = useCallback(() => {
        if (!collapsed && item?.links?.linksCount && item?.links?.linksCount > 0) {
            return (
                <Link
                    onKeyDown={(event) => {
                        if (event.key === 'Enter') {
                            handleOpenLinksDialog(event);
                            event.stopPropagation();
                        }
                    }}
                    noWrap
                    tabIndex={tabIndex}
                    variant="body1"
                    sx={{
                        '&:focus': { textDecoration: 'underline' },
                        lineHeight: 2,
                        paddingRight: '13px',
                        textDecoration: 'none',
                        cursor: 'pointer',
                        display: 'block',
                    }}
                    onClick={handleOpenLinksDialog}
                >
                    {item?.links?.linksCount} {selectedRegister?.registerName}
                </Link>
            );
        }
    }, [collapsed, handleOpenLinksDialog, item?.links?.linksCount, selectedRegister?.registerName, tabIndex]);

    const CollapsedLink = useCallback(() => {
        if (collapsed && Boolean(nestedLinksCount && nestedLinksCount > 0)) {
            return (
                <Link
                    tabIndex={tabIndex}
                    variant="body1"
                    color="textPrimary"
                    noWrap
                    sx={{
                        lineHeight: 2,
                        paddingRight: '13px',
                        textDecoration: 'none',
                        cursor: 'pointer',
                        display: 'block',
                    }}
                >
                    {nestedLinksCount} {selectedRegister?.registerName}
                </Link>
            );
        }
        return null;
    }, [collapsed, nestedLinksCount, selectedRegister?.registerName, tabIndex]);

    return (
        <>
            <Box sx={{ display: 'flex', width: 'calc(100% - 200px)' }}>
                {!readOnly && item?.id !== PARENT_ID && (
                    <IconButton
                        sx={{ cursor: disableInteraction ? 'not-allowed' : 'auto', zIndex: 100 }}
                        {...handleProps}
                        tabIndex={-1}
                        aria-label="triangle-down"
                        aria-hidden={false}
                    >
                        <Drag
                            tabIndex={-1}
                            color={theme.palette.protechtGrey.grey_128}
                        />
                    </IconButton>
                )}
                {childCount > 0 && (
                    <IconButton
                        tabIndex={-1}
                        onClick={(event) => {
                            event.stopPropagation();
                            setSelectedNode?.({
                                id,
                                registerEntryId: item?.registerEntryId,
                                collapsed: !collapsed,
                                hasChildren: Boolean(childCount),
                                absoluteIndex: item?.absoluteIndex || 0,
                            });
                            emitNotification(id);
                            id !== PARENT_ID && onCollapse?.(id);
                        }}
                    >
                        <TriangleDown
                            className={collapsed ? 'rotate-270' : 'rotate-0'}
                            color={theme.palette.primary.main}
                        />
                    </IconButton>
                )}
                {!childCount && (
                    <IconButton
                        disabled={true}
                        tabIndex={-1}
                        aria-label="triangle-down"
                        aria-hidden={false}
                    >
                        <TriangleDown
                            className={'rotate-270'}
                            color={theme.palette.protechtGrey.grey_220}
                        />
                    </IconButton>
                )}
                <Typography
                    noWrap
                    variant="body1"
                    sx={{
                        display: 'inline-block',
                        lineHeight: 2.2,
                    }}
                >
                    {item?.label && searchValue && (
                        <Highlighted
                            text={item?.label}
                            highlight={searchValue}
                            highlightStyling={{
                                background: alpha(theme.palette?.metricColors.yellow, 0.3),
                            }}
                        ></Highlighted>
                    )}
                    {!searchValue && item?.label}
                </Typography>
            </Box>

            <Box
                sx={{
                    minWidth: '200px',
                    textAlign: 'end',
                    overflow: 'hidden',
                }}
            >
                {ExpandedLink()}
                {CollapsedLink()}
            </Box>
            {ChildCount()}
        </>
    );
};

TreeItemContent.displayName = 'TreeItemContent';

export default React.memo(TreeItemContent);
