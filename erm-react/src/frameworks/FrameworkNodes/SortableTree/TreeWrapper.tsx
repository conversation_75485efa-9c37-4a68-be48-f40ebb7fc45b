import { ScrollableFitContainer } from 'common/components/ScrollableFitContainer';
import SortableTree from 'frameworks/FrameworkNodes/SortableTree/SortableTree';
import React, { FC, useCallback, useContext, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { buildNodesMap, filterTree, findInTree } from 'frameworks/FrameworkNodes/utils';
import { TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { useNumericParam } from 'common/hooks/useNumericParam';
import { FrameworkDetailRead } from 'api/generated/types';
import { useFlcGetFrameworkNodesLinksCountsUsingGet1Query } from 'frameworks/rtkApi';
import { FrameworkNodesLinksCounts } from 'frameworks/types';
import { FrameworkContext, SelectedNode } from 'frameworks/ContextProvider';
import { PARENT_ID } from 'frameworks/const';
import { emitNotification } from 'frameworks/NotificationService';
import { useSelector } from 'react-redux';
import { getFrameworkAppId, getSearchValue } from 'frameworks/selectors';
import NodesActions, { OTHER_FRAMEWORKS } from 'frameworks/FrameworkNodes/SortableTree/NodesActions';
import debounce from 'lodash/debounce';
import { strings } from 'common/utils/i18n';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import { AlertType, DialogType } from 'common/types';
import { setGwtOverlayOpen } from 'app/reducer';
import { useDispatch } from 'store';

type Props = {
    framework: FrameworkDetailRead;
    treeNodeItems: TreeItems;
    setTreeNodeItems: React.Dispatch<React.SetStateAction<TreeItems>>;
    setLinksCounts?: (register: FrameworkNodesLinksCounts) => void;
    readOnly?: boolean;
    isHierarchyDirty?: boolean;
    saveNodeHierarchy?: () => void;
};

const TreeWrapper: FC<Props> = ({ framework, treeNodeItems, setTreeNodeItems, readOnly, setLinksCounts, isHierarchyDirty, saveNodeHierarchy }) => {
    const id = useNumericParam('id');
    const [treeNodeItemsFiltered, setTreeNodeItemsFiltered] = useState<TreeItems>();
    const treeNodeItemsFilteredCurrent = useRef<TreeItems>();

    const searchValue = useSelector(getSearchValue);
    const { selectedRegister, setSelectedRegister, setCollapsedMap, arrowsBlocked, setArrowsBlocked, selectedNode, setSelectedNode, openDetail } =
        useContext(FrameworkContext);
    const dispatch = useDispatch();
    const { showConfirmationAlert } = useConfirmationAlert();
    const isFramework = useMemo(() => selectedRegister?.id === -1, [selectedRegister]);
    const { data: linksCounts } = useFlcGetFrameworkNodesLinksCountsUsingGet1Query(
        { id: framework.id!, register: isFramework ? undefined : (selectedRegister?.id?.toString() as string) },
        { skip: !selectedRegister, refetchOnMountOrArgChange: true },
    );
    const frameworkAppId = useSelector(getFrameworkAppId);

    const [selectedNodeDebounced, setSelectedNodeDebounced] = useState<SelectedNode | undefined>();

    const debouncedAction = useCallback(
        debounce((newValue) => {
            setSelectedNodeDebounced(newValue);
        }, 100),
        [setSelectedNodeDebounced],
    );

    useEffect(() => {
        debouncedAction(selectedNode);

        return () => {
            debouncedAction.cancel();
        };
    }, [selectedNode, debouncedAction]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            emitNotification(PARENT_ID);
        }, 500);
        return () => {
            clearTimeout(timeout);
        };
    }, []);

    useEffect(() => {
        if (linksCounts) {
            setLinksCounts && setLinksCounts(linksCounts);
        }
    }, [linksCounts]);

    useEffect(() => {
        if (framework.allowedRegisters) {
            setSelectedRegister(framework.allowedRegisters[0]);
        } else {
            setSelectedRegister(OTHER_FRAMEWORKS);
        }
    }, [framework]);

    useEffect(() => {
        if (searchValue && treeNodeItems) {
            const filtered = filterTree(treeNodeItems, searchValue);
            setTreeNodeItemsFiltered(filtered);
        }
        if (!searchValue && treeNodeItems) {
            setTreeNodeItemsFiltered(treeNodeItems);
        }
        setArrowsBlocked(false);
    }, [searchValue, treeNodeItems, selectedNode]);

    useLayoutEffect(() => {
        if (selectedNode && treeNodeItemsFiltered?.length && JSON.stringify(treeNodeItemsFilteredCurrent.current) !== JSON.stringify(treeNodeItemsFiltered)) {
            treeNodeItemsFilteredCurrent.current = treeNodeItemsFiltered;
            setTimeout(() => {
                if (!findInTree(treeNodeItemsFiltered, selectedNode?.id)) {
                    emitNotification(PARENT_ID);
                } else {
                    emitNotification(selectedNode.id as string);
                }
            }, 200);
        }
    }, [treeNodeItemsFiltered, selectedNode, treeNodeItemsFilteredCurrent]);

    const openNewNodeOverlay = useCallback(() => {
        dispatch(setGwtOverlayOpen(true));
        window.GwtBridge?.FrameworkConfiguration?.showOverScreenWidget?.(
            null,
            frameworkAppId?.toString() || '',
            framework.metadataRegister!.tableName!,
            framework.id?.toString() || '',
            selectedNodeDebounced?.id || '',
            'true',
            null,
        );
    }, [frameworkAppId, framework, selectedNodeDebounced]);

    const newNode = useCallback(() => {
        if (isHierarchyDirty) {
            showConfirmationAlert({
                onConfirm: () => {
                    saveNodeHierarchy?.();
                    setTimeout(() => {
                        openNewNodeOverlay();
                    }, 300);
                },
                onBack: () => {
                    setTimeout(() => {
                        openNewNodeOverlay();
                    }, 300);
                },
                contentText: strings('common:message.unsavedChanges'),
                type: AlertType.Warning,
                title: strings('common:title.saveChanges'),
                dialogType: DialogType.UNSAVED,
                cancelButtonLabel: strings('common:button.cancel'),
                confirmButtonLabel: strings('common:button.save'),
            });
        } else {
            openNewNodeOverlay();
        }
    }, [frameworkAppId, framework, selectedNodeDebounced, openNewNodeOverlay, isHierarchyDirty, saveNodeHierarchy]);

    const handleOpenDetail = useCallback(
        (id: string, registerEntryId?: number) => {
            if (isHierarchyDirty) {
                showConfirmationAlert({
                    onConfirm: () => {
                        saveNodeHierarchy?.();
                        openDetail(id, registerEntryId);
                    },
                    onBack: () => {
                        openDetail(id, registerEntryId);
                    },
                    contentText: strings('common:message.unsavedChanges'),
                    type: AlertType.Warning,
                    title: strings('common:title.saveChanges'),
                    dialogType: DialogType.UNSAVED,
                    cancelButtonLabel: strings('common:button.cancel'),
                    confirmButtonLabel: strings('common:button.save'),
                });
            } else {
                openDetail(id, registerEntryId);
            }
        },
        [isHierarchyDirty, showConfirmationAlert, showConfirmationAlert, openDetail],
    );

    const toggleExpanded = useCallback(
        (nodeId?: string) => {
            const toggleNodeExpanded = (items: TreeItems, nodeId?: string) => {
                return items.map((item) => {
                    if (item.id === nodeId) {
                        if (nodeId === selectedNode?.id) {
                            setSelectedNode({ ...selectedNode, collapsed: !item.collapsed });
                        }
                        setCollapsedMap((prevState) => ({
                            ...prevState,
                            [id!]: { ...prevState[id!], [nodeId]: !item.collapsed },
                        }));
                        return { ...item, collapsed: !item.collapsed };
                    } else if (item.children) {
                        return { ...item, children: toggleNodeExpanded(item.children, nodeId) };
                    }
                    return item;
                });
            };

            setTreeNodeItems((prevItems) => {
                const newItems = toggleNodeExpanded(prevItems, nodeId);
                return newItems;
            });
        },
        [JSON.stringify(treeNodeItems), selectedNode, setTreeNodeItems, id, setCollapsedMap],
    );

    const handleItemsSorted = useCallback(
        (items: TreeItems) => {
            if (items.length !== 1 || items[0].id !== PARENT_ID) {
                return;
            }
            setTreeNodeItems(items);
            const nodesMap = buildNodesMap(items);
            setCollapsedMap((prevState) => ({ ...prevState, [id!]: { ...prevState[id!], ...nodesMap } }));
        },
        [setTreeNodeItems, setCollapsedMap, id],
    );

    return (
        <>
            <NodesActions
                selectedNode={selectedNodeDebounced}
                setSelectedNode={setSelectedNode}
                framework={framework}
                treeNodeItems={treeNodeItems}
                setTreeNodeItems={setTreeNodeItems}
                selectedRegister={selectedRegister}
                setSelectedRegister={setSelectedRegister}
                openDetail={handleOpenDetail}
                newNode={newNode}
                readOnly={readOnly}
                toggleExpanded={toggleExpanded}
                arrowsBlocked={arrowsBlocked}
                setArrowsBlocked={setArrowsBlocked}
                isHierarchyDirty={isHierarchyDirty}
                saveNodeHierarchy={saveNodeHierarchy}
            />
            {treeNodeItems && (
                <ScrollableFitContainer
                    sx={{ opacity: arrowsBlocked ? 0.7 : 1 }}
                    $barHeights={170}
                >
                    <SortableTree
                        collapsible
                        items={treeNodeItemsFiltered || []}
                        dragDisabled={Boolean(searchValue?.length) || readOnly}
                        readOnly={readOnly}
                        onItemsSorted={handleItemsSorted}
                        onCollapse={toggleExpanded}
                        handleOpenDetail={handleOpenDetail}
                    />
                </ScrollableFitContainer>
            )}
        </>
    );
};

export default TreeWrapper;
