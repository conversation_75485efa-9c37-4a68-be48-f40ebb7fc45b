import ContextMenu from '@protecht/ui-library/library/components/ContextMenu';
import Button from '@protecht/ui-library/library/components/Button';
import { Add, ChevronDown, MoveDown, MoveLeft, MoveRight, MoveUp, View } from '@protecht/ui-library/library/components/SVGIcons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/pro-solid-svg-icons';
import { strings } from 'common/utils/i18n';
import { getReactRoot } from 'config';
import Divider from '@mui/material/Divider';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { DeleteIcon } from 'common/icons/DeleteIcon';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useDispatch } from 'store';
import { getFlattenedTree, getSearchValue, hasMetaDataRegisterPermission } from 'frameworks/selectors';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import useTheme from '@mui/system/useTheme';
import { FrameworkDetailRead, FrameworkRegisterRead } from 'api/generated/types';
import { useNodeDirections } from 'frameworks/FrameworkNodes/SortableTree/UseNodeDirections';
import { removeNodeById } from 'frameworks/FrameworkNodes/utils';
import { elevate, moveNodeRecursive, nestNodeRecursive } from 'frameworks/FrameworkNodes/SortableTree/utils/arrow';
import { emitNotification } from 'frameworks/NotificationService';
import { TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { SelectedNode } from 'frameworks/ContextProvider';
import { PARENT_ID } from 'frameworks/const';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import Box from '@mui/material/Box';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import { setGwtOverlayOpen } from 'app/reducer';
import { getGwtOverlayOpen } from 'app/selectors';
import { findNextLowerAbsoluteIndex } from 'frameworks/FrameworkNodes/SortableTree/utils/tree';
import debounce from 'lodash/debounce';
import { setSearchValue } from 'frameworks/reducer';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';

const linkedRequirementLabel = strings('frameworks:label.other');
export const OTHER_FRAMEWORKS = { id: -1, registerName: strings('frameworks:label.otherLink') };

export type Props = {
    framework: FrameworkDetailRead;
    treeNodeItems: TreeItems;
    setTreeNodeItems: (items: TreeItems) => void;
    selectedNode?: SelectedNode;
    setSelectedNode: React.Dispatch<React.SetStateAction<SelectedNode | undefined>>;
    selectedRegister?: FrameworkRegisterRead;
    setSelectedRegister: React.Dispatch<React.SetStateAction<FrameworkRegisterRead | undefined>>;
    openDetail: (id: string, registerEntryId?: number) => void;
    newNode: () => void;
    readOnly?: boolean;
    toggleExpanded: (nodeId?: string) => void;
    arrowsBlocked: boolean;
    setArrowsBlocked: React.Dispatch<React.SetStateAction<boolean>>;
    isHierarchyDirty?: boolean;
    saveNodeHierarchy?: () => void;
};

const NodesActions: FC<Props> = ({
    framework,
    treeNodeItems,
    selectedNode,
    setSelectedNode,
    selectedRegister,
    setSelectedRegister,
    openDetail,
    newNode,
    readOnly,
    toggleExpanded,
    setTreeNodeItems,
    arrowsBlocked,
    setArrowsBlocked,
}) => {
    const theme = useTheme();
    useConfirmationAlert();
    const canCreateNode = useSelector((state: RootState) => hasMetaDataRegisterPermission(state, framework.metadataRegister?.id));
    const [isSearchFocused, setIsSearchFocused] = useState(false);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const dispatch = useDispatch();
    const isGwtOverlayOpen = useSelector(getGwtOverlayOpen);
    const searchValue = useSelector(getSearchValue);
    const flattenedTree = useSelector(getFlattenedTree);

    const { canMoveUp, canMoveDown, canNest, canElevate } = useNodeDirections(treeNodeItems, searchValue, selectedNode);

    useEffect(() => {
        let timeout: ReturnType<typeof setTimeout>;
        window.ReactBridge!.onGwtOverlayClosed = () => {
            dispatch(setGwtOverlayOpen(false));
            timeout = setTimeout(() => {
                emitNotification(selectedNode?.id as string);
            }, 100);
        };

        return () => {
            if (timeout) {
                clearTimeout(timeout);
            }
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [selectedNode]);

    const registerContextMenuItems: ContextMenuItem[] = useMemo(() => {
        const otherFrameworks = {
            label: linkedRequirementLabel,
            action: setSelectedRegister.bind(null, OTHER_FRAMEWORKS),
        };
        const menuItems = framework.allowedRegisters?.map((register) => ({
            label: register.registerName,
            action: setSelectedRegister!.bind(null, register),
        })) as ContextMenuItem[];
        return menuItems?.length ? [...menuItems, { divider: true }, otherFrameworks] : [otherFrameworks];
    }, [framework, setSelectedRegister]);

    const handleNewItems = useCallback(
        (newItems: TreeItems) => {
            if (!arrowsBlocked) {
                setArrowsBlocked(true);
                const timeoutId = setTimeout(() => {
                    emitNotification(selectedNode?.id as string);
                    setSelectedNode(selectedNode);
                }, 100);
                setTreeNodeItems(newItems);

                timeoutRef.current = timeoutId;
            }
        },
        [setArrowsBlocked, setTreeNodeItems, selectedNode, arrowsBlocked],
    );

    const removeNode = useCallback(() => {
        if (selectedNode?.id && selectedNode.id !== PARENT_ID) {
            // Find the node to be removed in the flattened tree
            const nodeToRemove = flattenedTree.find((item) => item.id === selectedNode.id);

            if (!nodeToRemove) {
                return;
            }

            const previousItem = findNextLowerAbsoluteIndex(flattenedTree, nodeToRemove.absoluteIndex);

            // Remove the node
            setTreeNodeItems(removeNodeById(treeNodeItems, selectedNode.id));

            // Select the previous item if available
            if (previousItem?.id) {
                emitNotification(previousItem.id as string);
            }
        }
    }, [selectedNode, treeNodeItems, setTreeNodeItems, flattenedTree]);

    const moveNode = useCallback(
        (direction: 'up' | 'down') => {
            const newItems = moveNodeRecursive(treeNodeItems, selectedNode?.id, direction);
            handleNewItems(newItems);
        },
        [treeNodeItems, selectedNode, handleNewItems],
    );

    const elevateNode = useCallback(() => {
        if (selectedNode?.id && treeNodeItems) {
            const newItems = elevate(treeNodeItems, selectedNode);
            handleNewItems(newItems);
        }
    }, [selectedNode, treeNodeItems, handleNewItems]);

    const nestNodeUnderPreviousSibling = useCallback(() => {
        if (selectedNode?.id) {
            const newItems = nestNodeRecursive([...treeNodeItems], selectedNode.id);
            handleNewItems(newItems);
        }
    }, [selectedNode, treeNodeItems, handleNewItems]);

    const onKeyDown = useCallback(
        (event: KeyboardEvent) => {
            if (isSearchFocused || arrowsBlocked || isGwtOverlayOpen) {
                return;
            }
            switch (event.key) {
                case '+':
                    if (selectedNode && canCreateNode) {
                        newNode();
                    }
                    event.stopPropagation();
                    break;
                case 'ArrowLeft':
                    if (selectedNode?.hasChildren && !selectedNode?.collapsed) {
                        toggleExpanded(selectedNode?.id);
                    }
                    event.stopPropagation();
                    break;
                case 'ArrowRight':
                    if (selectedNode?.hasChildren && selectedNode?.collapsed) {
                        toggleExpanded(selectedNode?.id);
                    }
                    event.stopPropagation();
                    break;
                case 'Delete':
                    !readOnly && removeNode();
                    event.stopPropagation();
                    break;
                case 'w':
                    !readOnly && canMoveUp && moveNode('up');
                    event.stopPropagation();
                    break;
                case 's':
                    !readOnly && canMoveDown && moveNode('down');
                    event.stopPropagation();
                    break;
                case 'a':
                    !readOnly && canElevate && elevateNode();
                    event.stopPropagation();
                    break;
                case 'd':
                    !readOnly && canNest && nestNodeUnderPreviousSibling();
                    event.stopPropagation();
                    break;
            }
        },
        [
            toggleExpanded,
            selectedNode,
            canNest,
            canElevate,
            canMoveDown,
            canMoveUp,
            canCreateNode,
            nestNodeUnderPreviousSibling,
            elevateNode,
            moveNode,
            removeNode,
            newNode,
            readOnly,
            isGwtOverlayOpen,
            isSearchFocused,
            arrowsBlocked,
        ],
    );

    useEffect(() => {
        document.addEventListener('keydown', onKeyDown);
        return () => {
            document.removeEventListener('keydown', onKeyDown);
        };
    }, [onKeyDown]);

    return (
        <ToolbarContainer
            disableGutters={true}
            variant="dense"
        >
            <ToolbarGroup
                flex={1}
                justifyContent="space-between"
            >
                <ToolbarGroup>
                    {!readOnly && (
                        <Input
                            clearable
                            leftBorder={false}
                            sx={{ maxWidth: '330px', height: '28px', minWidth: '204px' }}
                            InputProps={{
                                endAdornment: (
                                    <Box mr={1}>
                                        <FontAwesomeIcon
                                            icon={faSearch}
                                            color={theme.palette.primary.main}
                                        />
                                    </Box>
                                ),
                            }}
                            placeholder={strings('common:placeholder.search')}
                            value={searchValue}
                            onChange={debounce((event) => dispatch(setSearchValue(event.target.value)), 1000)}
                            onFocus={() => setIsSearchFocused(true)}
                            onBlur={() => setIsSearchFocused(false)}
                        />
                    )}
                </ToolbarGroup>
                <ToolbarGroup>
                    <ContextMenu
                        key={'context-menu-register'}
                        dividerWithMargin
                        baseElement={
                            <Button
                                variant="secondary"
                                size="medium"
                                startIcon={
                                    <View
                                        width="20px"
                                        height="20px"
                                    />
                                }
                                endIcon={<ChevronDown />}
                            >
                                {selectedRegister?.id !== -1 ? (selectedRegister?.registerName || linkedRequirementLabel) : linkedRequirementLabel}
                            </Button>
                        }
                        menuAnchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                        menuTransformOrigin={{ vertical: 'top', horizontal: 'right' }}
                        items={registerContextMenuItems}
                        showCheckMarks={true}
                        selectedIndex={framework.allowedRegisters?.findIndex((state) => state.id === selectedRegister?.id) ?? 0}
                        rootContainer={getReactRoot()}
                    />
                    <Divider
                        sx={{ height: '20px' }}
                        orientation="vertical"
                    />
                    {!readOnly && (
                        <>
                            <IconButton
                                style={{ color: !selectedNode?.id || selectedNode?.id === PARENT_ID ? 'grey' : 'red' }}
                                size="small"
                                color={'primary'}
                                data-testid={'framework-node-delete'}
                                disabled={!selectedNode?.id || selectedNode?.id === PARENT_ID}
                                onClick={removeNode}
                            >
                                <DeleteIcon $disabled={!selectedNode?.id || selectedNode?.id === PARENT_ID} />
                            </IconButton>
                            <IconButton
                                size="small"
                                color={'primary'}
                                data-testid={'framework-node-up'}
                                disabled={!canMoveUp}
                                onClick={() => moveNode('up')}
                            >
                                <MoveUp />
                            </IconButton>
                            <IconButton
                                size="small"
                                color={'primary'}
                                data-testid={'framework-node-down'}
                                disabled={!canMoveDown}
                                onClick={() => moveNode('down')}
                            >
                                <MoveDown />
                            </IconButton>
                            <IconButton
                                size="small"
                                color={'primary'}
                                data-testid={'framework-node-left'}
                                disabled={!canElevate}
                                onClick={elevateNode}
                            >
                                <MoveLeft />
                            </IconButton>
                            <IconButton
                                size="small"
                                color={'primary'}
                                data-testid={'framework-node-right'}
                                disabled={!canNest}
                                onClick={nestNodeUnderPreviousSibling}
                            >
                                <MoveRight />
                            </IconButton>
                        </>
                    )}
                    <Button
                        variant="outlined"
                        size="medium"
                        sx={{
                            minWidth: 'auto',
                        }}
                        disabled={selectedNode?.id === PARENT_ID || !selectedNode?.hasChildren}
                        onClick={() => {
                            toggleExpanded(selectedNode?.id);
                        }}
                    >
                        {selectedNode?.collapsed ? strings('frameworks:button.expand') : strings('frameworks:button.collapse')}
                    </Button>
                    <Button
                        disabled={!selectedNode?.id}
                        onClick={() => openDetail(selectedNode?.id ?? '', selectedNode?.registerEntryId)}
                        variant="outlined"
                        size="medium"
                        sx={{
                            minWidth: 'auto',
                        }}
                    >
                        {strings('common:button.open')}
                    </Button>
                    {!readOnly && (
                        <Button
                            variant="outlined"
                            size="medium"
                            disabled={!canCreateNode || !selectedNode?.id}
                            onClick={() => newNode()}
                            startIcon={<Add />}
                        >
                            {strings('frameworks:button.newNode')}
                        </Button>
                    )}
                </ToolbarGroup>
            </ToolbarGroup>
        </ToolbarContainer>
    );
};

export default React.memo(NodesActions);
