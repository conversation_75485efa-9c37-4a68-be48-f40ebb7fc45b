import type { UniqueIdentifier } from '@dnd-kit/core';
import { NodeLinksCount } from 'frameworks/types';
import { SelectedNode } from 'frameworks/ContextProvider';
import { FrameworkRegisterRead } from 'api/generated/types';

export type TreeItem = {
    id: UniqueIdentifier;
    children?: TreeItem[];
    label?: string;
    disabled?: boolean;
    collapsible?: boolean;
    collapsed?: boolean;
    parentId?: UniqueIdentifier;
    registerEntryId?: number;
    registerName?: string;
    links?: NodeLinksCount;
    nestedLinks?: NodeLinksCount[];
};

export type TreeItems = TreeItem[];

export type FlattenedItem = TreeItem & {
    parentId?: UniqueIdentifier;
    absoluteIndex: number;
    depth: number;
    index: number;
};

export type TreeMoves = { canMoveUp: boolean; canMoveDown: boolean; canNest: boolean; canElevate: boolean };

export type BaseTreeItemProps = {
    id: string;
    item?: Partial<FlattenedItem>;
    childCount: number;
    clone?: boolean;
    collapsed?: boolean;
    disableInteraction?: boolean;
    disableSelection?: boolean;
    readOnly?: boolean;
    onCollapse?: (id: UniqueIdentifier) => void;
    onRemove?(): void;
    setSelectedNode?: (node: SelectedNode) => void;
    selectedRegister?: FrameworkRegisterRead;
    setTotalNodeLinkCount?: (count?: number) => void;
    setNodeId?: (id: number) => void;
};
