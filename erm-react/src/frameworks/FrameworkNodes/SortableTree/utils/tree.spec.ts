import {
    buildTree,
    findItem,
    findItemDeep,
    findNextLowerAbsoluteIndex,
    flattenTree,
    getChildCount,
    removeChildrenOf,
    removeItem,
    setProperty,
} from 'frameworks/FrameworkNodes/SortableTree/utils/tree';
import { FlattenedItem } from 'frameworks/FrameworkNodes/SortableTree/types';

describe('flattenTree', () => {
    it('flattens the tree', () => {
        const items = [{ id: '1', children: [{ id: '2', children: [] }] }];
        const result = flattenTree(items);
        expect(result).toHaveLength(2);
        expect(result[1].id).toBe('2');
    });
});

describe('buildTree', () => {
    it('builds the tree', () => {
        const items = [
            { id: '1', parentId: 'root', depth: 0, index: 0, absoluteIndex: 0 },
            { id: '2', parentId: '1', depth: 1, index: 1, absoluteIndex: 1 },
        ];
        const result = buildTree(items);
        expect(result).toHaveLength(1);
        expect(result[0].children).toHaveLength(1);
        expect(result[0].children?.[0].id).toBe('2');
    });
});

describe('findItem', () => {
    it('finds the item', () => {
        const items = [
            { id: '1', children: [] },
            { id: '2', children: [] },
        ];
        const result = findItem(items, '2');
        expect(result?.id).toBe('2');
    });
});

describe('findItemDeep', () => {
    it('finds the item deeply', () => {
        const items = [{ id: '1', children: [{ id: '2', children: [] }] }];
        const result = findItemDeep(items, '2');
        expect(result?.id).toBe('2');
    });
});

describe('removeItem', () => {
    it('removes the item', () => {
        const items = [{ id: '1', children: [{ id: '2', children: [] }] }];
        const result = removeItem(items, '2');
        expect(result[0].children).toHaveLength(0);
    });
});

describe('setProperty', () => {
    it('sets the property', () => {
        const items = [{ id: '1', children: [{ id: '2', children: [], collapsed: false }] }];
        const result = setProperty(items, '2', 'collapsed', () => true);
        expect(result[0].children?.[0].collapsed).toBe(true);
    });
});

describe('getChildCount', () => {
    it('counts the children', () => {
        const items = [{ id: '1', children: [{ id: '2', children: [{ id: '3', children: [] }] }] }];
        const result = getChildCount(items, '1');
        expect(result).toBe(2);
    });
});

describe('removeChildrenOf', () => {
    it('removes children of specified items', () => {
        const items = [
            { id: '1', parentId: undefined, children: [], depth: 0, index: 0, absoluteIndex: 0 },
            { id: '2', parentId: '1', children: [], depth: 1, index: 1, absoluteIndex: 1 },
            { id: '3', parentId: '1', children: [], depth: 1, index: 2, absoluteIndex: 2 },
        ];
        const result = removeChildrenOf(items, ['1']);
        expect(result).toHaveLength(1);
        expect(result[0].id).toBe('1');
    });
});

describe('findNextLowerAbsoluteIndex', () => {
    // Sample test data
    const testData: FlattenedItem[] = [
        {
            id: '305',
            label: 'Item 1',
            collapsed: true,
            parentId: '298',
            registerEntryId: 1000300,
            registerName: 'table_110420',
            nestedLinks: [],
            depth: 3,
            index: 3,
            absoluteIndex: 106,
        },
        {
            id: '306',
            label: 'Item 2',
            collapsed: false,
            parentId: '298',
            registerEntryId: 1000301,
            registerName: 'table_110420',
            nestedLinks: [],
            depth: 3,
            index: 4,
            absoluteIndex: 95,
        },
        {
            id: '307',
            label: 'Item 3',
            collapsed: true,
            parentId: '298',
            registerEntryId: 1000302,
            registerName: 'table_110420',
            nestedLinks: [],
            depth: 3,
            index: 5,
            absoluteIndex: 120,
        },
        {
            id: '308',
            label: 'Item 4',
            collapsed: true,
            parentId: '298',
            registerEntryId: 1000303,
            registerName: 'table_110420',
            nestedLinks: [],
            depth: 3,
            index: 6,
            absoluteIndex: 85,
        },
    ];

    it('should return the next lowest item when one exists', () => {
        const result = findNextLowerAbsoluteIndex(testData, 106);
        expect(result).toBeTruthy();
        expect(result?.absoluteIndex).toBe(95);
        expect(result?.id).toBe('306');
    });

    it('should return the highest available item when current index is higher than all items', () => {
        const result = findNextLowerAbsoluteIndex(testData, 200);
        expect(result).toBeTruthy();
        expect(result?.absoluteIndex).toBe(120);
        expect(result?.id).toBe('307');
    });

    it('should return null when no lower index exists', () => {
        const result = findNextLowerAbsoluteIndex(testData, 85);
        expect(result).toBeNull();
    });

    it('should handle when current index is between existing indexes', () => {
        const result = findNextLowerAbsoluteIndex(testData, 96);
        expect(result).toBeTruthy();
        expect(result?.absoluteIndex).toBe(95);
        expect(result?.id).toBe('306');
    });

    it('should work with empty array', () => {
        const result = findNextLowerAbsoluteIndex([], 100);
        expect(result).toBeNull();
    });

    it('should work with single item array', () => {
        const singleItemArray = [testData[0]];
        const result = findNextLowerAbsoluteIndex(singleItemArray, 200);
        expect(result).toBeTruthy();
        expect(result?.absoluteIndex).toBe(106);
    });

    it('should handle items with same absoluteIndex', () => {
        const dataWithDuplicates = [
            ...testData,
            {
                ...testData[0],
                id: '309',
                absoluteIndex: 95, // Same as item with id "306"
            },
        ];
        const result = findNextLowerAbsoluteIndex(dataWithDuplicates, 106);
        expect(result?.absoluteIndex).toBe(95);
        // Should return the first item with that index after sorting
        expect(['306', '309']).toContain(result?.id);
    });

    it('should maintain all properties of the returned object', () => {
        const result = findNextLowerAbsoluteIndex(testData, 106);
        expect(result).toMatchObject({
            id: '306',
            label: 'Item 2',
            collapsed: false,
            parentId: '298',
            registerEntryId: 1000301,
            registerName: 'table_110420',
            nestedLinks: [],
            depth: 3,
            index: 4,
            absoluteIndex: 95,
        });
    });
});
