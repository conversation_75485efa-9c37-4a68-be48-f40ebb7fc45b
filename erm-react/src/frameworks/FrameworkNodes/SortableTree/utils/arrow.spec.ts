import { TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { elevate, findNodeDirections, moveNodeRecursive, nestNodeRecursive } from 'frameworks/FrameworkNodes/SortableTree/utils/arrow';

describe('nestNodeRecursive', () => {
    it('nests the node under the previous sibling', () => {
        const items: TreeItems = [
            { id: '1', children: [] },
            { id: '2', children: [] },
        ];
        const result = nestNodeRecursive(items, '2');
        expect(result[0].children).toHaveLength(1);
        expect(result[0].children?.[0].id).toBe('2');
    });
});

describe('moveNodeRecursive', () => {
    it('moves the node up', () => {
        const items: TreeItems = [
            { id: '1', children: [] },
            { id: '2', children: [] },
        ];
        const result = moveNodeRecursive(items, '2', 'up');
        expect(result[0].id).toBe('2');
        expect(result[1].id).toBe('1');
    });

    it('moves the node down', () => {
        const items: TreeItems = [
            { id: '1', children: [] },
            { id: '2', children: [] },
        ];
        const result = moveNodeRecursive(items, '1', 'down');
        expect(result[0].id).toBe('2');
        expect(result[1].id).toBe('1');
    });
});

describe('elevate', () => {
    it('elevates a node', () => {
        const items: TreeItems = [{ id: '1', children: [{ id: '2', children: [], parentId: '1' }] }];
        const selected = { id: '2' };
        const result = elevate(items, selected);
        expect(result).toHaveLength(2);
        expect(result[1].id).toBe('2');
    });
});

describe('findNodeDirections', () => {
    it('finds node directions', () => {
        const items: TreeItems = [
            { id: '1', children: [] },
            { id: '2', children: [] },
        ];
        const result = findNodeDirections(items, items, '2');
        expect(result).toEqual({
            canMoveUp: true,
            canMoveDown: false,
            canNest: true,
            canElevate: false,
        });
    });
});
