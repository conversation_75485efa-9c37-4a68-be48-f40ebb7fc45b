import { swapItems } from 'app/utils';
import { TreeItem, TreeItems, TreeMoves } from 'frameworks/FrameworkNodes/SortableTree/types';
import { findAndReplaceNode, findInTree } from 'frameworks/FrameworkNodes/utils';
import { SelectedNode } from 'frameworks/ContextProvider';
import { PARENT_ID } from 'frameworks/const';

export const nestNodeRecursive = (_items: TreeItems, nodeId: string): TreeItems => {
    const items = _items.map((item) => ({
        ...item,
        children: item.children ? [...item.children] : undefined,
    }));

    const targetIndex = items.findIndex((item) => item.id === nodeId);

    if (targetIndex > 0) {
        const previousSibling = items[targetIndex - 1];
        const targetNode = items[targetIndex];

        const updatedPreviousSibling = {
            ...previousSibling,
            children: [
                ...(previousSibling.children || []),
                {
                    ...targetNode,
                    parentId: previousSibling.id,
                },
            ],
            collapsed: false,
        };

        return [...items.slice(0, targetIndex - 1), updatedPreviousSibling, ...items.slice(targetIndex + 1)];
    }

    return items.map((item) => {
        if (!item.children) {
            return item;
        }

        const updatedChildren = nestNodeRecursive(item.children, nodeId);

        if (updatedChildren === item.children) {
            return item;
        }

        return {
            ...item,
            children: updatedChildren,
        };
    });
};

export const moveNodeRecursive = (items: TreeItems, nodeId, direction) => {
    for (let i = 0; i < items.length; i++) {
        if (items[i].id === nodeId) {
            const newIndex = direction === 'up' ? i - 1 : i + 1;
            if (newIndex >= 0 && newIndex < items.length) {
                return swapItems(items, i, newIndex);
            }
            return items;
        } else if (items[i].children) {
            const newChildren = moveNodeRecursive(items[i].children!, nodeId, direction);
            if (newChildren !== items[i].children) {
                return [...items.slice(0, i), { ...items[i], children: newChildren }, ...items.slice(i + 1)];
            }
        }
    }
    return items;
};

export const elevate = (treeNodeItems: TreeItems, selected: SelectedNode): TreeItems => {
    const selectedNode = findInTree(treeNodeItems, selected.id);
    const parent = findInTree(treeNodeItems, selectedNode?.parentId);
    const grandParent = parent?.parentId ? findInTree(treeNodeItems, parent?.parentId) : undefined;

    if (selectedNode && parent) {
        const parentIndex: number =
            grandParent?.children?.findIndex((item) => item.id === parent?.id) ?? treeNodeItems.findIndex((item) => item.id === parent?.id);
        const newParent: TreeItem = { ...parent, children: [...(parent?.children || []).filter((child) => child.id !== selected.id)] };
        if (grandParent && parentIndex !== undefined) {
            const updatedChildren = grandParent.children
                ? [
                      ...grandParent.children.slice(0, parentIndex),
                      newParent,
                      { ...selectedNode, parentId: parent.parentId },
                      ...grandParent.children.slice(parentIndex + 1),
                  ]
                : [newParent, selectedNode];
            const newItems = findAndReplaceNode(treeNodeItems, grandParent.id, { ...grandParent, children: updatedChildren });
            return newItems;
        } else if (!parent.parentId) {
            const updatedItems = [...treeNodeItems];
            if (parentIndex !== -1) {
                updatedItems[parentIndex] = newParent;
                updatedItems.splice(parentIndex + 1, 0, selectedNode);
            }
            return updatedItems;
        }
    }
    return treeNodeItems;
};

export const findNodeDirections = (items: TreeItems, treeNodeItems: TreeItems, nodeId?: string): TreeMoves | undefined => {
    for (let i = 0; i < items.length; i++) {
        if (items[i].id === nodeId) {
            return {
                canMoveUp: i > 0,
                canMoveDown: i < items.length - 1,
                canNest: i > 0,
                canElevate: items[i].parentId !== PARENT_ID && !treeNodeItems.some((item) => item.id === nodeId),
            };
        } else if (items[i].children) {
            const result = findNodeDirections(items[i].children!, treeNodeItems, nodeId);
            if (result) {
                return result;
            }
        }
    }
};
