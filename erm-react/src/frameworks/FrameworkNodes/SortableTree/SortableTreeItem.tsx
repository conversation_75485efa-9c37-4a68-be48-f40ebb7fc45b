import React, { CSSProperties, useCallback, useEffect, useMemo } from 'react';
import { AnimateLayoutChanges, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import TreeItemContent from 'frameworks/FrameworkNodes/SortableTree/TreeItemContent';
import { StyledListItem, StyledTreeItem } from 'frameworks/FrameworkNodes/SortableTree/styled';
import { useArrowNavigationWithFocusState } from 'common/hooks/arrowNavigationTyped';
import { notificationObservable } from 'frameworks/NotificationService';
import Box from '@mui/material/Box';
import { INDENTATION_WIDTH } from 'frameworks/const';
import { BaseTreeItemProps } from 'frameworks/FrameworkNodes/SortableTree/types';

type TreeItemProps = BaseTreeItemProps & {
    index?: number;
    depth: number;
    ghost?: boolean;
    indicator?: boolean;
    isSelected?: boolean;
    arrowsBlocked?: boolean;
    openDetail?: (id: string, registerEntryId?: number) => void;
};

const animateLayoutChanges: AnimateLayoutChanges = ({ isDragging }) => isDragging;

const SortableTreeItem = ({
    id,
    index,
    depth,
    onCollapse,
    isSelected,
    openDetail,
    setSelectedNode,
    item,
    collapsed,
    childCount,
    disableSelection,
    disableInteraction,
    clone,
    ghost,
    indicator,
    arrowsBlocked,
    selectedRegister,
    setNodeId,
    setTotalNodeLinkCount,
}: TreeItemProps) => {
    const { attributes, isSorting, listeners, setDraggableNodeRef, setDroppableNodeRef, transform, transition } = useSortable({
        id,
        animateLayoutChanges,
    });

    const style: CSSProperties = useMemo(
        () => ({
            transform: CSS.Translate.toString(transform),
            transition,
            zIndex: clone ? 9999 : 1,
        }),
        [transition, transform],
    );

    const handleProps = useMemo(
        () => ({
            ...attributes,
            ...listeners,
        }),
        [attributes, listeners],
    );

    const {
        selected: selectedArrowNavigation,
        focusProps: { ref: navRef, onClick, tabIndex },
        setFocusFromOutside,
    } = useArrowNavigationWithFocusState(0, (index as number) + 1);

    useEffect(() => {
        if (selectedArrowNavigation && !arrowsBlocked) {
            navRef.current?.focus();
            setSelectedNode?.({ id, registerEntryId: item?.registerEntryId, collapsed, hasChildren: Boolean(childCount), absoluteIndex: index });
        }
    }, [setSelectedNode, selectedArrowNavigation, id, arrowsBlocked]);

    useEffect(() => {
        const subscription = notificationObservable.subscribe((event: { id: string; timestamp: number }) => {
            if (event.id === id) {
                setFocusFromOutside(0, (index || 0) + 1);
                navRef.current?.focus();
            }
        });

        return () => {
            subscription.unsubscribe();
        };
    }, [id, index, navRef]);

    const handleDoubleClick = useCallback(() => {
        openDetail?.(id, item?.registerEntryId);
    }, [id, item?.registerEntryId, openDetail]);

    const handleOnClick = useCallback(() => {
        onClick();
        setSelectedNode?.({ id, registerEntryId: item?.registerEntryId, collapsed, hasChildren: Boolean(childCount), absoluteIndex: index });
    }, [setSelectedNode, onClick, id, item?.registerEntryId, collapsed, childCount]);

    const handleKeyDown = useCallback(
        (event) => {
            if (event.key === 'Enter') {
                openDetail?.(id, item?.registerEntryId);
                event.stopPropagation();
            }
        },
        [id, item?.registerEntryId, openDetail],
    );

    const isEven = useMemo(() => Boolean(index === 0 || (index && index % 2 === 0)), [index]);

    return (
        <StyledListItem
            $even={isEven}
            onClick={handleOnClick}
            $selected={isSelected}
            $clone={clone}
            $ghost={ghost}
            $indicator={indicator}
            $disableSelection={disableSelection}
            $disableInteraction={disableInteraction}
            ref={setDroppableNodeRef}
            sx={{
                paddingLeft: `${INDENTATION_WIDTH * depth}px`,
            }}
            onDoubleClick={handleDoubleClick}
        >
            <StyledTreeItem
                $clone={clone}
                $ghost={ghost}
                $indicator={indicator}
                ref={setDraggableNodeRef}
                sx={style}
            >
                <Box
                    sx={{
                        '&:focus': { outline: 'none' },
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        height: '100%',
                        justifyContent: 'space-between',
                    }}
                    ref={navRef}
                    tabIndex={tabIndex}
                    onKeyDown={handleKeyDown}
                >
                    <TreeItemContent
                        id={id}
                        style={style}
                        clone={clone}
                        childCount={childCount}
                        disableInteraction={isSorting || disableInteraction}
                        handleProps={handleProps}
                        onCollapse={onCollapse}
                        item={item}
                        tabIndex={tabIndex}
                        selectedRegister={selectedRegister}
                        setNodeId={setNodeId}
                        setTotalNodeLinkCount={setTotalNodeLinkCount}
                        collapsed={collapsed}
                    />
                </Box>
            </StyledTreeItem>
        </StyledListItem>
    );
};

export default React.memo(SortableTreeItem);
