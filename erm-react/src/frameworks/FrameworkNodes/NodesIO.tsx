import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { ChevronDown } from '@protecht/ui-library/library/components/SVGIcons';
import { strings } from 'common/utils/i18n';
import { getReactRoot } from 'config';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { fileSchema, FileFormValues } from './NodesIOSchema';
import store from 'store';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import useTheme from '@mui/system/useTheme';
import { FrameworkDetailRead } from 'api/generated/types';
import { Import } from '@protecht/ui-library/library/components/SVGIcons';
import { frameworksApi, linksApi, useFcImportDataRegisterUsingPost1Mutation, useFcImportVerifyDataRegisterUsingPost1Mutation } from 'frameworks/rtkApi';
import { useLazyGetImportProgressGetQuery, useLazyGetTemplateFileQuery } from 'dataset/rtkApi';
import { initiateFileDownload } from 'app/utils';
import useSnackbar from 'common/hooks/useSnackbar';
import { AlertType, ApplicationName, PermissionCodes } from 'common/types';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import { hasAllPermissions } from 'common/components/ProtectedContent/ProtectedContent';
import { usersApi } from 'user/rtkApi';
import Grid from '@mui/material/Grid';
import ContextMenu from '@protecht/ui-library/library/components/ContextMenu';
import type { ProgressResult } from '@protecht/ui-library/library/components/FileDropzone/types';
import { SelectedNode } from 'frameworks/ContextProvider';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import { FormProvider } from 'react-hook-form';
import useForm from 'common/hooks/forms/useForm';
import { AttachmentField } from '@protecht/ui-library/library/components/FormFields';
import ProgressDialog, { ProgressType } from 'common/components/ProgressDialog';

type ProgressResult_Temp = ProgressResult & {
    failure?: string;
};

export type NodesIOProps = {
    framework: FrameworkDetailRead;
    selectedNode?: SelectedNode;
    isHierarchyDirty?: boolean;
    saveNodeHierarchy?: () => void;
};

const NodesIO: FC<NodesIOProps> = ({ framework, selectedNode, isHierarchyDirty, saveNodeHierarchy }) => {
    const theme = useTheme();
    const { showConfirmationAlert } = useConfirmationAlert();
    const [fileSelectorVisible, setFileSelectorVisible] = useState(false);
    const [progressType, setProgressType] = useState<ProgressType>(ProgressType.Import);
    const [progressDialogVisible, setProgressDialogVisible] = useState(false);
    const [importProgress, setImportProgress] = useState<ProgressResult_Temp | null>(null);
    const [importFrameworkNodes] = useFcImportDataRegisterUsingPost1Mutation();
    const [verifyImport] = useFcImportVerifyDataRegisterUsingPost1Mutation();
    const [getTemplateFile] = useLazyGetTemplateFileQuery();
    const { enqueueError } = useSnackbar();
    const [getImportProgress] = useLazyGetImportProgressGetQuery();

    // References to store active requests for cancellation
    const activeRequestRef = useRef<{ abort?: () => void } | null>(null);
    const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

    // Schema is imported from NodesIOSchema.tsx

    // Initialize form methods
    const formMethods = useForm<FileFormValues>({
        schema: fileSchema,
        mode: 'onChange',
        defaultValues: {
            importFile: null,
        },
        reValidateMode: 'onChange',
    });

    const {
        reset,
        handleSubmit,
        setError,
        formState: { isValid },
    } = formMethods;

    const isHierarchyDirtyRef = useRef(isHierarchyDirty);

    useEffect(() => {
        isHierarchyDirtyRef.current = isHierarchyDirty;
    }, [isHierarchyDirty]);

    const { data: userPermissions } = useSelector(usersApi.endpoints.pursGetUserPermissionsUsingGet.select({}));
    const canImport = useMemo(
        () =>
            userPermissions &&
            hasAllPermissions(
                [
                    { code: PermissionCodes.FRAMEWORK_VIEW, applicationName: ApplicationName.FRAMEWORK }, // framework view permission n1
                    { code: PermissionCodes.FRAMEWORK_EDIT, applicationName: ApplicationName.FRAMEWORK, relatedObjectId: framework.id }, // particular framework edit permission n2
                    { code: PermissionCodes.REGISTER_DATA_ADD, applicationName: ApplicationName.FRAMEWORK, relatedObjectId: framework.metadataRegister?.id }, // particular metadata register add permission n3
                    { code: PermissionCodes.REGISTER_DATA_IMPORT, applicationName: ApplicationName.FRAMEWORK, relatedObjectId: framework.metadataRegister?.id }, // particular metadata register import permission n4
                ],
                userPermissions,
            ),
        [framework, userPermissions],
    );

    const handleImport = useCallback(() => {
        if (isHierarchyDirtyRef.current) {
            showConfirmationAlert({
                onConfirm: async () => {
                    await saveNodeHierarchy?.();
                    setFileSelectorVisible(true);
                },
                contentText: strings('common:message.unsavedChanges'),
                type: AlertType.Warning,
                title: strings('common:title.saveChanges'),
                cancelButtonLabel: strings('common:button.cancel'),
                confirmButtonLabel: strings('common:button.save'),
            });
        } else {
            setFileSelectorVisible(true);
        }
    }, [isHierarchyDirtyRef.current, saveNodeHierarchy, showConfirmationAlert]);

    const onFinished = useCallback(() => {
        void store.dispatch(frameworksApi.util.invalidateTags(['nodes']));
        void store.dispatch(linksApi.util.invalidateTags(['linksCounts', 'nodeDetailedCounts']));
    }, [enqueueError, selectedNode]);

    const exportCsv = useCallback(async () => {
        const file = (await getTemplateFile({ regId: framework.metadataRegister!.id!, suggestedFileName: `${framework.name}_template.csv` }).unwrap()) as Blob;

        initiateFileDownload(`${framework.name}_template.csv`, file);
    }, [framework]);

    const contextMenuItems: ContextMenuItem[] = useMemo(() => {
        return [
            {
                label: strings('frameworks:import.cta'),
                action: handleImport,
            },
            {
                label: strings('frameworks:label.template'),
                action: exportCsv,
            },
        ];
    }, [framework, exportCsv, handleImport, reset, setError, strings]);

    const handleCloseFileSelector = useCallback(() => {
        setFileSelectorVisible(false);
        // Reset form when closing dialog
        reset({
            importFile: null,
        });
    }, [reset]);

    const handleProgress = useCallback(
        async (uuid: string, progress: ProgressType, updateState: boolean) => {
            try {
                const checkProgress = (): Promise<ProgressResult_Temp | { error?: any; timeout?: boolean }> => {
                    return new Promise<ProgressResult_Temp | { error?: any; timeout?: boolean }>((resolve) => {
                        // Store the interval reference so it can be cleared when cancelling
                        const interval = setInterval(() => {
                            void (async () => {
                                try {
                                    const { data } = await getImportProgress({ importid: uuid });

                                    // Update the progress state with real data
                                    if (data && updateState) {
                                        setImportProgress(data as ProgressResult_Temp);
                                    }

                                    if (
                                        (progress == ProgressType.Import && data?.importFinished) ||
                                        (data?.validationFinished && progress == ProgressType.VERIFICATION)
                                    ) {
                                        clearInterval(interval);
                                        progressIntervalRef.current = null;
                                        resolve(data as ProgressResult_Temp);
                                    }
                                } catch (error) {
                                    console.error('Progress check error:', error);
                                    clearInterval(interval);
                                    progressIntervalRef.current = null;
                                    resolve({ error });
                                }
                            })();
                        }, 1000);

                        // Store the interval reference
                        progressIntervalRef.current = interval;
                    });
                };

                const result = await checkProgress();
                return result as ProgressResult_Temp;
            } catch (error) {
                console.error('Progress handler error:', error);
                return null;
            }
        },
        [getImportProgress, progressType],
    );

    const handleCloseProgressDialog = useCallback(() => {
        // Cancel the active request if it exists
        if (activeRequestRef.current && activeRequestRef.current.abort) {
            activeRequestRef.current.abort();
            activeRequestRef.current = null;
        }

        // Clear the progress checking interval if it exists
        if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current);
            progressIntervalRef.current = null;
        }

        setProgressDialogVisible(false);
        setImportProgress(null);
    }, []);

    const handleVerify = useCallback(
        (data: FileFormValues) => {
            if (data.importFile) {
                const formData = new FormData();
                formData.append('file', data.importFile.file);

                setProgressType(ProgressType.VERIFICATION);
                setProgressDialogVisible(true);

                const response = verifyImport({
                    fd: formData,
                    tableName: framework.metadataRegister!.tableName!,
                    frameworkId: framework.id!,
                });

                // Store the response for potential cancellation
                activeRequestRef.current = { abort: response.abort };

                response
                    .unwrap()
                    .then((responseData) => {
                        void handleProgress(responseData, ProgressType.VERIFICATION, true);
                    })
                    .catch((error) => {
                        console.error('Verification error:', error);
                    });
            }
        },
        [framework, verifyImport, strings, handleProgress],
    );

    const handleImportSubmit = useCallback(
        (data: FileFormValues) => {
            // Handle form submission
            if (data.importFile) {
                setProgressType(ProgressType.Import);
                setProgressDialogVisible(true);
                setFileSelectorVisible(false);

                // Create FormData and submit import
                const formData = new FormData();
                formData.append('file', data.importFile.file);

                const verifyResponse = verifyImport({
                    fd: formData,
                    tableName: framework.metadataRegister!.tableName!,
                    frameworkId: framework.id!,
                });

                activeRequestRef.current = { abort: verifyResponse.abort };

                reset({
                    importFile: null,
                });

                void verifyResponse.unwrap().then(async (verifyResponse) => {
                    const progressResult = await handleProgress(verifyResponse, ProgressType.VERIFICATION, false);

                    if (progressResult?.validationErrors?.length || progressResult?.failure) {
                        setProgressType(ProgressType.VERIFICATION);
                        setImportProgress(progressResult);
                        return;
                    }

                    const response = importFrameworkNodes({
                        fd: formData,
                        tableName: framework.metadataRegister!.tableName!,
                        frameworkId: framework.id!,
                    });

                    // Store the response for potential cancellation
                    activeRequestRef.current = { abort: response.abort };

                    response
                        .unwrap()
                        .then(async (responseData) => {
                            await handleProgress(responseData, ProgressType.Import, true);
                            onFinished();
                        })
                        .catch((error) => {
                            console.error('Import error:', error);
                        });
                });
            }
        },
        [framework, importFrameworkNodes, handleProgress, onFinished, strings],
    );

    const getInfoMessage = useMemo(() => {
        if (importProgress?.failure) {
            return importProgress?.failure;
        }
        if (progressType === ProgressType.VERIFICATION) {
            if (importProgress?.validationErrors?.length) {
                return strings('frameworks:import.validationFailed');
            }
            if (importProgress?.validationFinished) {
                return strings('frameworks:import.validationSuccess');
            }
            return strings('frameworks:import.verifying');
        }
        if (progressType === ProgressType.Import) {
            if (importProgress?.validationErrors?.length) {
                return strings('frameworks:import.failed');
            }
            if (importProgress?.importFinished) {
                return strings('frameworks:import.success');
            }
            return strings('frameworks:import.importing');
        }
        return '';
    }, [importProgress, progressType, theme]);

    return (
        <>
            {canImport && (
                <ContextMenu
                    items={contextMenuItems}
                    baseElement={
                        <Button
                            size="large"
                            variant={'secondary'}
                            startIcon={<Import />}
                            endIcon={<ChevronDown />}
                            dataTestId="nodes-import-export-button"
                        >
                            {strings('common:button.import')}
                        </Button>
                    }
                    menuAnchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                    menuTransformOrigin={{ vertical: 'top', horizontal: 'right' }}
                    rootContainer={getReactRoot()}
                />
            )}

            <Dialog
                visible={fileSelectorVisible}
                title={strings('register:dataImportDialog.importData')}
                width={663}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={handleCloseFileSelector}
                            dataTestId="button-cancel"
                        >
                            {strings('common:button.cancel')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            disabled={!isValid}
                            variant="secondary"
                            onClick={handleSubmit(handleVerify)}
                            dataTestId="button-verify"
                        >
                            {strings('ermConstants:verify')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            disabled={!isValid}
                            onClick={handleSubmit(handleImportSubmit)}
                            dataTestId="button-confirm"
                        >
                            {strings('common:button.import')}
                        </Button>
                    </DialogActions>
                }
            >
                <Grid
                    sx={{ display: 'flex', alignItems: 'end' }}
                    container
                    gap={1}
                >
                    <FormProvider {...formMethods}>
                        <AttachmentField
                            multiple={false}
                            accept={{ 'text/csv': ['.csv'] }}
                            formFieldProps={{ name: 'importFile', label: strings('register:dataImportDialog.fileToImport') }}
                        />
                    </FormProvider>
                </Grid>
            </Dialog>
            {progressDialogVisible && (
                <ProgressDialog
                    visible={progressDialogVisible}
                    title={progressType === ProgressType.Import ? strings('frameworks:import.titleImport') : strings('frameworks:import.titleVerify')}
                    progressType={progressType}
                    infoMessage={getInfoMessage}
                    totalCount={importProgress?.totalRecordsCount || 0}
                    processedCount={importProgress?.validRecordCount || 0}
                    validationMessages={importProgress?.validationErrors}
                    processFinished={progressType === ProgressType.Import ? importProgress?.importFinished : importProgress?.validationFinished}
                    progressVariant="determinate"
                    onClose={handleCloseProgressDialog}
                    entityName={framework.name}
                    failure={importProgress?.failure}
                />
            )}
        </>
    );
};

export default NodesIO;
