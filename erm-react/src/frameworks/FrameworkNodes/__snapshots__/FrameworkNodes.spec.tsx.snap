// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FrameworkNodes renders with the disabled toolbar 1`] = `
<DocumentFragment>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
  >
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-18q8d33-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-1c3mlp4-MuiGrid-root"
      >
        <div
          style="margin: 2px;"
          tabindex="0"
        >
          <div
            class="MuiToolbar-root MuiToolbar-dense css-17sj562-MuiToolbar-root"
          >
            <div
              class="MuiBox-root css-qjw807"
            >
              <div
                class="MuiBox-root css-m39v42"
              />
              <div
                class="MuiBox-root css-m39v42"
              >
                <div>
                  <div>
                    <button
                      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-1wrdo5k-MuiButtonBase-root-MuiButton-root"
                      data-testid="button-Linked requirements"
                      tabindex="0"
                      type="button"
                    >
                      <span
                        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-MuiButton-startIcon"
                      >
                        <svg
                          data-icon="view"
                          fill="currentColor"
                          height="20px"
                          viewBox="0 0 24 24"
                          width="20px"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M19.313 4H4.688C3.738 4 3 4.786 3 5.714v12.572C3 19.25 3.738 20 4.688 20h14.625c.914 0 1.687-.75 1.687-1.714V5.714C21 4.786 20.227 4 19.313 4m-8.157 14.286H4.898q-.21 0-.21-.215V13.43h6.468zm0-6.572H4.687V6.857h6.47zm7.946 6.572h-6.258v-4.857h6.469v4.642a.204.204 0 0 1-.211.215m.21-6.572h-6.468V6.857h6.469z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                      <span
                        class="css-qv0y8m"
                      >
                        Linked requirements
                      </span>
                      <span
                        class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeMedium css-1gnd1fd-MuiButton-endIcon"
                      >
                        <svg
                          data-icon="chevron-down"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M12.026 12.914 6.601 7.49 4.639 9.452l7.386 7.386 7.389-7.389-1.962-1.962z"
                            fill="currentColor"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </button>
                  </div>
                </div>
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth MuiDivider-vertical css-lqeocn-MuiDivider-root"
                />
                <button
                  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-863il4-MuiButtonBase-root-MuiButton-root"
                  data-testid="button-Collapse"
                  disabled=""
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="css-1d0doyg"
                  >
                    Collapse
                  </span>
                </button>
                <button
                  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-863il4-MuiButtonBase-root-MuiButton-root"
                  data-testid="button-Open"
                  disabled=""
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="css-1d0doyg"
                  >
                    Open
                  </span>
                </button>
              </div>
            </div>
          </div>
          <div
            class="MuiBox-root css-1ypw3u6"
          >
            <div
              class="css-19s4rfn"
              data-testid="virtuoso-scroller"
              data-virtuoso-scroller="true"
              style="height: 100%; outline: none; overflow-y: auto; position: relative;"
              tabindex="0"
            >
              <div
                data-viewport-type="element"
                style="width: 100%; height: 100%; position: absolute; top: 0px;"
              >
                <div
                  data-testid="virtuoso-item-list"
                  style="box-sizing: border-box; margin-top: 0px; padding-top: 0px; padding-bottom: 0px;"
                />
              </div>
            </div>
            <div
              style="display: none;"
            >
              
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
            </div>
            <div
              aria-atomic="true"
              aria-live="assertive"
              role="status"
              style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
   
</DocumentFragment>
`;

exports[`FrameworkNodes was rendered 1`] = `
<DocumentFragment>
  <div
    class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1uxsbqa-MuiToolbar-root"
  >
    <div
      class="MuiBox-root css-1uax0l7"
    >
      <div
        class="MuiBox-root css-m39v42"
      >
        <button
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorSecondary MuiIconButton-sizeMedium css-enqixr-MuiButtonBase-root-MuiIconButton-root"
          data-testid="button-undefined"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-arrow-left "
            color="#1B4AD5"
            data-icon="arrow-left"
            data-prefix="fas"
            focusable="false"
            role="img"
            style="font-size: 22px;"
            viewBox="0 0 448 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"
              fill="currentColor"
            />
          </svg>
        </button>
        <h1
          class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1256jyt-MuiTypography-root"
          data-testid="frameworks-detail-heading"
        >
          Example Framework 1.0
        </h1>
        <h4
          class="MuiTypography-root MuiTypography-h4 css-1ds858y-MuiTypography-root"
        >
          active
        </h4>
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-pfg0lb-MuiButtonBase-root-MuiButton-root"
          data-testid="button-Archive"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <span
            class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
          >
            <svg
              data-icon="move-right"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.5 6.516v2.281h-4a1.5 1.5 0 0 0-1.5 1.5v3.406a1.5 1.5 0 0 0 1.5 1.5h4v2.28c0 1.344 1.593 2.032 2.53 1.063l5.5-5.5a1.453 1.453 0 0 0 0-2.093l-5.5-5.499c-.937-.969-2.53-.281-2.53 1.062m6.998 5.468L12 17.484v-3.781H6.5v-3.406H12V6.485z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span
            class="css-qv0y8m"
          >
            Archive
          </span>
        </button>
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-pfg0lb-MuiButtonBase-root-MuiButton-root"
          data-testid="button-Publish"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <span
            class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
          >
            <svg
              data-icon="move-right"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.5 6.516v2.281h-4a1.5 1.5 0 0 0-1.5 1.5v3.406a1.5 1.5 0 0 0 1.5 1.5h4v2.28c0 1.344 1.593 2.032 2.53 1.063l5.5-5.5a1.453 1.453 0 0 0 0-2.093l-5.5-5.499c-.937-.969-2.53-.281-2.53 1.062m6.998 5.468L12 17.484v-3.781H6.5v-3.406H12V6.485z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span
            class="css-qv0y8m"
          >
            Publish
          </span>
        </button>
      </div>
      <div
        class="MuiBox-root css-18wejz6"
      >
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-pfg0lb-MuiButtonBase-root-MuiButton-root"
          data-testid="button-Share"
          tabindex="0"
          type="button"
        >
          <span
            class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
          >
            <svg
              data-icon="link"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.0587 9.94135C13.6716 9.55425 13.2493 9.27273 12.7918 9.02639C12.6158 8.95601 12.4399 8.9912 12.2991 9.13196L12.1584 9.27273C11.8416 9.55425 11.6657 9.94135 11.6305 10.3284C11.5953 10.5044 11.7009 10.6804 11.8416 10.7859C12.088 10.8915 12.4751 11.1378 12.651 11.349C13.8123 12.5103 13.8123 14.3754 12.651 15.5367L10.0117 18.176C8.85044 19.3372 6.98534 19.3372 5.82405 18.176C4.66276 17.0147 4.66276 15.1496 5.82405 13.9883L7.44282 12.3695C7.54839 12.2639 7.58358 12.1232 7.54839 11.9824C7.47801 11.6305 7.40762 11.0323 7.37243 10.6452C7.37243 10.2933 6.91496 10.1173 6.66862 10.3636C6.24633 10.7859 5.57771 11.4545 4.45161 12.5806C2.51613 14.5161 2.51613 17.6481 4.45161 19.5484C6.35191 21.4839 9.48387 21.4839 11.4194 19.5484C14.305 16.6628 14.1642 16.8035 14.3754 16.522C15.9589 14.6217 15.8534 11.7361 14.0587 9.94135ZM19.5484 4.45161C17.6481 2.51613 14.5161 2.51613 12.5806 4.45161C9.69501 7.33724 9.83578 7.19648 9.62463 7.47801C8.04106 9.3783 8.14663 12.2639 9.94135 14.0587C10.3284 14.4457 10.7507 14.7273 11.2082 14.9736C11.3842 15.044 11.5601 15.0088 11.7009 14.868L11.8416 14.7273C12.1584 14.4457 12.3343 14.0587 12.3695 13.6716C12.4047 13.4956 12.2991 13.3196 12.1584 13.2141C11.912 13.1085 11.5249 12.8622 11.349 12.651C10.1877 11.4897 10.1877 9.62463 11.349 8.46334L13.9883 5.82405C15.1496 4.66276 17.0147 4.66276 18.176 5.82405C19.3372 6.98534 19.3372 8.85044 18.176 10.0117L16.5572 11.6305C16.4516 11.7361 16.4164 11.8768 16.4516 12.0176C16.522 12.3695 16.5924 12.9677 16.6276 13.3548C16.6276 13.7067 17.085 13.8827 17.3314 13.6364C17.7537 13.2141 18.4223 12.5455 19.5484 11.4194C21.4839 9.48387 21.4839 6.35191 19.5484 4.45161Z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span
            class="css-qv0y8m"
          >
            Share
          </span>
        </button>
        <div
          class="MuiBox-root css-0"
        >
          <hr
            class="MuiDivider-root MuiDivider-fullWidth MuiDivider-vertical css-ouhj77-MuiDivider-root"
          />
        </div>
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
          data-testid="button-Cancel"
          tabindex="0"
          type="button"
        >
          <span
            class="css-1d0doyg"
          >
            Cancel
          </span>
        </button>
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
          data-testid="button-Save"
          tabindex="0"
          type="button"
        >
          <span
            class="css-1d0doyg"
          >
            Save
          </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
  >
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-18q8d33-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-1c3mlp4-MuiGrid-root"
      >
        <div
          style="margin: 2px;"
          tabindex="0"
        >
          <div
            class="MuiToolbar-root MuiToolbar-dense css-17sj562-MuiToolbar-root"
          >
            <div
              class="MuiBox-root css-qjw807"
            >
              <div
                class="MuiBox-root css-m39v42"
              />
              <div
                class="MuiBox-root css-m39v42"
              >
                <div>
                  <div>
                    <button
                      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-1wrdo5k-MuiButtonBase-root-MuiButton-root"
                      data-testid="button-Linked requirements"
                      tabindex="0"
                      type="button"
                    >
                      <span
                        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-MuiButton-startIcon"
                      >
                        <svg
                          data-icon="view"
                          fill="currentColor"
                          height="20px"
                          viewBox="0 0 24 24"
                          width="20px"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M19.313 4H4.688C3.738 4 3 4.786 3 5.714v12.572C3 19.25 3.738 20 4.688 20h14.625c.914 0 1.687-.75 1.687-1.714V5.714C21 4.786 20.227 4 19.313 4m-8.157 14.286H4.898q-.21 0-.21-.215V13.43h6.468zm0-6.572H4.687V6.857h6.47zm7.946 6.572h-6.258v-4.857h6.469v4.642a.204.204 0 0 1-.211.215m.21-6.572h-6.468V6.857h6.469z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                      <span
                        class="css-qv0y8m"
                      >
                        Linked requirements
                      </span>
                      <span
                        class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeMedium css-1gnd1fd-MuiButton-endIcon"
                      >
                        <svg
                          data-icon="chevron-down"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M12.026 12.914 6.601 7.49 4.639 9.452l7.386 7.386 7.389-7.389-1.962-1.962z"
                            fill="currentColor"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </button>
                  </div>
                </div>
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth MuiDivider-vertical css-lqeocn-MuiDivider-root"
                />
                <button
                  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-863il4-MuiButtonBase-root-MuiButton-root"
                  data-testid="button-Collapse"
                  disabled=""
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="css-1d0doyg"
                  >
                    Collapse
                  </span>
                </button>
                <button
                  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-863il4-MuiButtonBase-root-MuiButton-root"
                  data-testid="button-Open"
                  disabled=""
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="css-1d0doyg"
                  >
                    Open
                  </span>
                </button>
              </div>
            </div>
          </div>
          <div
            class="MuiBox-root css-1ypw3u6"
          >
            <div
              class="css-19s4rfn"
              data-testid="virtuoso-scroller"
              data-virtuoso-scroller="true"
              style="height: 100%; outline: none; overflow-y: auto; position: relative;"
              tabindex="0"
            >
              <div
                data-viewport-type="element"
                style="width: 100%; height: 100%; position: absolute; top: 0px;"
              >
                <div
                  data-testid="virtuoso-item-list"
                  style="box-sizing: border-box; margin-top: 0px; padding-top: 0px; padding-bottom: 0px;"
                />
              </div>
            </div>
            <div
              style="display: none;"
            >
              
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
            </div>
            <div
              aria-atomic="true"
              aria-live="assertive"
              role="status"
              style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
   
</DocumentFragment>
`;
