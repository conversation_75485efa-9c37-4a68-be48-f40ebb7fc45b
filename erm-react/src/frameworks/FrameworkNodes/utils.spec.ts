import {
    applyLinksToTree,
    areTreesEqual,
    buildNodesMap,
    filterTree,
    findAndReplaceNode,
    findInTree,
    gatherNestedLinks,
    hierarchyToTreeNodes,
    removeNodeById,
    treeItemsToHierarchy,
} from './utils';
import { HierarchicalNodeRead } from 'api/generated/types';
import { TreeItem, TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { LinksMap, NodeLinksCount } from 'frameworks/types';

describe('filterTree', () => {
    it('should return an empty array for empty input', () => {
        expect(filterTree([], 'search')).toEqual([]);
    });

    it('should filter nodes correctly', () => {
        const nodes: TreeItems = [
            { id: 1, label: 'Node1', children: [] },
            { id: 2, label: 'searchNode', children: [] },
        ];
        expect(filterTree(nodes, 'search')).toEqual([nodes[1]]);
    });

    it('should filter nodes with case-insensitive matching', () => {
        const nodes: TreeItems = [
            { id: 1, label: 'Apple', children: [] },
            { id: 2, label: 'banana', children: [] },
        ];
        expect(filterTree(nodes, 'aPP')).toEqual([nodes[0]]);
        expect(filterTree(nodes, 'BAN')).toEqual([nodes[1]]);
    });

    it('should retain hierarchical structure after filtering', () => {
        const nodes: TreeItems = [
            {
                id: 1,
                label: 'Parent',
                children: [
                    { id: 1, label: 'Node1', children: [] },
                    { id: 2, label: 'searchNode', children: [] },
                ],
            },
        ];
        const expected: TreeItems = [
            {
                id: 1,
                label: 'Parent',
                children: [{ id: 2, label: 'searchNode', children: [] }],
            },
        ];
        expect(filterTree(nodes, 'search')).toEqual(expected);
    });

    it('should return an empty array when no nodes match', () => {
        const nodes: TreeItems = [
            { id: 1, label: 'Node1', children: [] },
            { id: 2, label: 'Node2', children: [] },
        ];
        expect(filterTree(nodes, 'xyz')).toEqual([]);
    });
});

describe('utils', () => {
    describe('hierarchy ->> TreeNodes and nestedLinks', () => {
        const mockHierarchy: HierarchicalNodeRead[] = [
            {
                id: 1,
                name: 'Node 1',
                registerEntryId: 101,
                childNodes: [
                    {
                        id: 2,
                        name: 'Child Node 1',
                        registerEntryId: 102,
                    },
                    {
                        id: 3,
                        name: 'Child Node 2',
                        registerEntryId: 103,
                    },
                ],
                registerName: 'register_1',
            },
        ];

        const mockNodes: TreeItems = [
            {
                id: 1,
                label: 'Node 1',
                registerEntryId: 101,
                children: [
                    {
                        id: 2,
                        label: 'Child Node 1',
                        registerEntryId: 102,
                    },
                    {
                        id: 3,
                        label: 'Child Node 2',
                        registerEntryId: 103,
                    },
                ],
                registerName: 'register_1',
            },
        ];

        const mockCountMap: LinksMap = {
            '1': { nodeId: 1, linksCount: 2, linksIds: ['123', '123'], linksNames: ['Link 1', 'Link 2'] },
            '2': { nodeId: 2, linksCount: 2, linksIds: ['123', '123'], linksNames: ['Link 1', 'Link 2'] },
            '3': { nodeId: 3, linksCount: 2, linksIds: ['123', '123'], linksNames: ['Link 1', 'Link 2'] },
        };

        describe('gatherNestedLinks', () => {
            it('should gather nested links correctly', () => {
                const node: TreeItem = mockNodes[0];
                const result: NodeLinksCount[] = gatherNestedLinks(node, mockCountMap);

                expect(result).toEqual([
                    { nodeId: 1, linksCount: 2, linksIds: ['123', '123'], linksNames: ['Link 1', 'Link 2'] },
                    { nodeId: 2, linksCount: 2, linksIds: ['123', '123'], linksNames: ['Link 1', 'Link 2'] },
                    { nodeId: 3, linksCount: 2, linksIds: ['123', '123'], linksNames: ['Link 1', 'Link 2'] },
                ]);
            });

            it('should return an empty array when countMap is undefined', () => {
                const node: TreeItem = mockNodes[0];
                const result: NodeLinksCount[] = gatherNestedLinks(node, undefined as any);

                expect(result).toEqual([]);
            });
        });

        describe('hierarchyToTreeNodes', () => {
            it('should convert hierarchy to tree nodes correctly', () => {
                const result: TreeItems = hierarchyToTreeNodes<HierarchicalNodeRead>(mockHierarchy, undefined);

                expect(result).toEqual([
                    {
                        id: '1',
                        label: 'Node 1',
                        collapsed: true,
                        parentId: undefined,
                        registerEntryId: 101,
                        registerName: 'register_1',
                        children: [
                            {
                                id: '2',
                                label: 'Child Node 1',
                                collapsed: true,
                                parentId: '1',
                                registerEntryId: 102,
                                registerName: undefined,
                                children: undefined,
                            },
                            {
                                id: '3',
                                label: 'Child Node 2',
                                collapsed: true,
                                parentId: '1',
                                registerEntryId: 103,
                                registerName: undefined,
                                children: undefined,
                            },
                        ],
                    },
                ]);
            });

            it('should return an empty array when nodes are empty', () => {
                const result: TreeItems = hierarchyToTreeNodes<HierarchicalNodeRead>([], undefined);
                expect(result).toEqual([]);
            });
        });
    });

    describe('treeItemsToHierarchy', () => {
        it('should convert flat list of nodes to hierarchical structure', () => {
            const input: TreeItem[] = [
                { id: JSON.stringify(1), label: 'Node 1', children: [{ id: JSON.stringify(2), label: 'Child Node 1' }] },
                { id: JSON.stringify(3), label: 'Node 2' },
            ];
            const expectedOutput: HierarchicalNodeRead[] = [
                { id: 1, name: 'Node 1', order: 1, childNodes: [{ id: 2, name: 'Child Node 1', order: 1 }] },
                { id: 3, name: 'Node 2', order: 2 },
            ];

            const result = treeItemsToHierarchy(input);
            expect(result).toEqual(expectedOutput);
        });

        it('should handle empty input', () => {
            const input: TreeItem[] = [];
            const expectedOutput: HierarchicalNodeRead[] = [];

            const result = treeItemsToHierarchy(input);
            expect(result).toEqual(expectedOutput);
        });

        describe('removeNodeById function', () => {
            const sampleNodes = [
                {
                    id: 1,
                    name: 'Node 1',
                    children: [
                        { id: 2, name: 'Node 1.1' },
                        { id: 3, name: 'Node 1.2' },
                    ],
                },
                {
                    id: 4,
                    name: 'Node 2',
                    children: [{ id: 5, name: 'Node 2.1' }],
                },
            ];

            it('should remove a node with a given ID', () => {
                const idToRemove = 3;
                const result = removeNodeById(sampleNodes, idToRemove);

                expect(result.some((node) => node.id === idToRemove)).toBeFalsy();
            });

            it('should remove a node recursively from the tree', () => {
                const idToRemove = 4;
                const result = removeNodeById(sampleNodes, idToRemove);

                expect(result.some((node) => node.id === idToRemove)).toBeFalsy();

                expect(result.every((node) => node.id !== 5)).toBeTruthy();
            });

            it('should return a new array without mutating the original nodes', () => {
                const idToRemove = 2;
                const result = removeNodeById(sampleNodes, idToRemove);

                expect(result).not.toBe(sampleNodes);
            });

            it('should return the same array if the node with the given ID is not found', () => {
                const idToRemove = 10;
                const result = removeNodeById(sampleNodes, idToRemove);

                expect(result).toEqual(sampleNodes);
            });
        });

        describe('findInTree', () => {
            const tree: TreeItems = [
                {
                    id: 1,
                    children: [
                        { id: 2 },
                        {
                            id: 3,
                            children: [{ id: 4 }],
                        },
                    ],
                },
                {
                    id: 5,
                    children: [{ id: 6 }, { id: 7 }],
                },
            ];

            it('should find the node with the given id', () => {
                expect(findInTree(tree, 3)).toEqual({ id: 3, children: [{ id: 4 }] });
                expect(findInTree(tree, 6)).toEqual({ id: 6 });
            });

            it('should return null if the node with the given id is not found', () => {
                expect(findInTree(tree, 999)).toBeNull();
            });

            it('should return null if the id is not provided', () => {
                expect(findInTree(tree)).toBeNull();
            });
        });

        describe('findAndReplaceNode', () => {
            const tree: TreeItems = [
                {
                    id: 1,
                    children: [
                        { id: 2 },
                        {
                            id: 3,
                            children: [{ id: 4 }],
                        },
                    ],
                },
                {
                    id: 5,
                    children: [{ id: 6 }, { id: 7 }],
                },
            ];

            it('should replace the node with the given id', () => {
                const newNode: TreeItem = { id: 3, children: [{ id: 8 }] };
                const updatedTree = findAndReplaceNode(tree, 3, newNode);
                expect(updatedTree).toEqual([
                    {
                        id: 1,
                        children: [
                            { id: 2 },
                            {
                                id: 3,
                                children: [{ id: 8 }],
                            },
                        ],
                    },
                    {
                        id: 5,
                        children: [{ id: 6 }, { id: 7 }],
                    },
                ]);
            });

            it('should return the same tree if the node with the given id is not found', () => {
                const newNode: TreeItem = { id: 999 };
                const updatedTree = findAndReplaceNode(tree, 999, newNode);
                expect(updatedTree).toEqual(tree);
            });
        });
    });

    describe('buildNodesMap', () => {
        it('should return an empty map for an empty input', () => {
            const items: TreeItems = [];
            const result = buildNodesMap(items);
            expect(result).toEqual({});
        });

        it('should correctly map a single item with no children', () => {
            const items: TreeItems = [{ id: '1', collapsed: true }];
            const result = buildNodesMap(items);
            expect(result).toEqual({ '1': true });
        });

        it('should correctly map multiple items with no children', () => {
            const items: TreeItems = [
                { id: '1', collapsed: true },
                { id: '2', collapsed: false },
                { id: '3' }, // collapsed is undefined
            ];
            const result = buildNodesMap(items);
            expect(result).toEqual({ '1': true, '2': false, '3': undefined });
        });

        it('should correctly map items with nested children', () => {
            const items: TreeItems = [
                {
                    id: '1',
                    collapsed: true,
                    children: [
                        {
                            id: '1.1',
                            collapsed: false,
                            children: [{ id: '1.1.1', collapsed: true }],
                        },
                        { id: '1.2', collapsed: true },
                    ],
                },
                { id: '2', collapsed: false },
            ];
            const result = buildNodesMap(items);
            expect(result).toEqual({
                '1': true,
                '1.1': false,
                '1.1.1': true,
                '1.2': true,
                '2': false,
            });
        });

        it('should handle items with missing collapsed values', () => {
            const items: TreeItems = [
                { id: '1', children: [{ id: '1.1' }] },
                { id: '2', collapsed: false },
            ];
            const result = buildNodesMap(items);
            expect(result).toEqual({
                '1': undefined,
                '1.1': undefined,
                '2': false,
            });
        });
    });

    describe('areTreesEqual', () => {
        it('should return true for equal simple trees', () => {
            const tree1: TreeItem[] = [
                { id: 1, label: 'Node 1' },
                { id: 2, label: 'Node 2' },
            ];
            const tree2: TreeItem[] = [
                { id: 2, label: 'Node 2' },
                { id: 1, label: 'Node 1' },
            ];

            expect(areTreesEqual(tree1, tree2)).toBe(true);
        });

        it('should return false for trees with different length', () => {
            const tree1: TreeItem[] = [{ id: 1, label: 'Node 1' }];
            const tree2: TreeItem[] = [
                { id: 1, label: 'Node 1' },
                { id: 2, label: 'Node 2' },
            ];

            expect(areTreesEqual(tree1, tree2)).toBe(false);
        });

        it('should return false for trees with different ids', () => {
            const tree1: TreeItem[] = [{ id: 1, label: 'Node 1' }];
            const tree2: TreeItem[] = [{ id: 2, label: 'Node 1' }];

            expect(areTreesEqual(tree1, tree2)).toBe(false);
        });

        it('should return true for deeply nested equal trees', () => {
            const tree1: TreeItem[] = [{ id: 1, label: 'Node 1', children: [{ id: 2, label: 'Node 2', children: [{ id: 3, label: 'Node 3' }] }] }];
            const tree2: TreeItem[] = [{ id: 1, label: 'Node 1', children: [{ id: 2, label: 'Node 2', children: [{ id: 3, label: 'Node 3' }] }] }];

            expect(areTreesEqual(tree1, tree2)).toBe(true);
        });

        it('should return false for deeply nested different trees', () => {
            const tree1: TreeItem[] = [{ id: 1, label: 'Node 1', children: [{ id: 2, label: 'Node 2' }] }];
            const tree2: TreeItem[] = [{ id: 1, label: 'Node 1', children: [{ id: 3, label: 'Node 3' }] }];

            expect(areTreesEqual(tree1, tree2)).toBe(false);
        });

        it('should return true for trees with same structure but different order', () => {
            const tree1: TreeItem[] = [
                {
                    id: 1,
                    label: 'Node 1',
                    children: [
                        { id: 2, label: 'Node 2' },
                        { id: 3, label: 'Node 3' },
                    ],
                },
            ];
            const tree2: TreeItem[] = [
                {
                    id: 1,
                    label: 'Node 1',
                    children: [
                        { id: 3, label: 'Node 3' },
                        { id: 2, label: 'Node 2' },
                    ],
                },
            ];

            expect(areTreesEqual(tree1, tree2)).toBe(true);
        });

        it('should return false for trees with different labels', () => {
            const tree1: TreeItem[] = [{ id: 1, label: 'Node 1' }];
            const tree2: TreeItem[] = [{ id: 1, label: 'Different Node' }];

            expect(areTreesEqual(tree1, tree2)).toBe(false);
        });

        it('should return true for empty trees', () => {
            const tree1: TreeItem[] = [];
            const tree2: TreeItem[] = [];

            expect(areTreesEqual(tree1, tree2)).toBe(true);
        });
    });
});

describe('applyLinksToTree and gatherNestedLinks with updated NodeLinksCount structure', () => {
    const tree: TreeItems = [
        {
            id: 'node1',
            label: 'Node 1',
            registerEntryId: 101,
            children: [
                {
                    id: 'node1_1',
                    label: 'Node 1.1',
                    parentId: 'node1',
                    registerEntryId: 102,
                    children: [],
                },
            ],
        },
        {
            id: 'node2',
            label: 'Node 2',
            registerEntryId: 103,
            children: [],
        },
    ];

    const linksCountsMap: LinksMap = {
        node1: {
            nodeId: 1,
            linksCount: 3,
            linksIds: ['link1', 'link2', 'link3'],
            linksNames: ['Link 1', 'Link 2', 'Link 3'],
        },
        node1_1: {
            nodeId: 2,
            linksCount: 5,
            linksIds: ['link4', 'link5'],
            linksNames: ['Link 4', 'Link 5'],
        },
        node2: {
            nodeId: 3,
            linksCount: 2,
            linksIds: ['link6', 'link7'],
            linksNames: ['Link 6', 'Link 7'],
        },
    };

    it('should apply the links and nestedLinks from linksCountsMap to the tree using gatherNestedLinks', () => {
        const result = applyLinksToTree(tree, linksCountsMap);

        expect(result).toEqual([
            {
                id: 'node1',
                label: 'Node 1',
                registerEntryId: 101,
                links: {
                    nodeId: 1,
                    linksCount: 3,
                    linksIds: ['link1', 'link2', 'link3'],
                    linksNames: ['Link 1', 'Link 2', 'Link 3'],
                },
                nestedLinks: [
                    {
                        nodeId: 1,
                        linksCount: 3,
                        linksIds: ['link1', 'link2', 'link3'],
                        linksNames: ['Link 1', 'Link 2', 'Link 3'],
                    },
                    {
                        nodeId: 2,
                        linksCount: 5,
                        linksIds: ['link4', 'link5'],
                        linksNames: ['Link 4', 'Link 5'],
                    },
                ],
                children: [
                    {
                        id: 'node1_1',
                        label: 'Node 1.1',
                        parentId: 'node1',
                        registerEntryId: 102,
                        links: {
                            nodeId: 2,
                            linksCount: 5,
                            linksIds: ['link4', 'link5'],
                            linksNames: ['Link 4', 'Link 5'],
                        },
                        nestedLinks: [
                            {
                                nodeId: 2,
                                linksCount: 5,
                                linksIds: ['link4', 'link5'],
                                linksNames: ['Link 4', 'Link 5'],
                            },
                        ],
                        children: [],
                    },
                ],
            },
            {
                id: 'node2',
                label: 'Node 2',
                registerEntryId: 103,
                links: {
                    nodeId: 3,
                    linksCount: 2,
                    linksIds: ['link6', 'link7'],
                    linksNames: ['Link 6', 'Link 7'],
                },
                nestedLinks: [
                    {
                        nodeId: 3,
                        linksCount: 2,
                        linksIds: ['link6', 'link7'],
                        linksNames: ['Link 6', 'Link 7'],
                    },
                ],
                children: [],
            },
        ]);
    });

    it('gatherNestedLinks should return an array of nested links correctly', () => {
        const node: TreeItem = {
            id: 'node1',
            children: [
                {
                    id: 'node1_1',
                    children: [],
                },
            ],
        };

        const countMap: LinksMap = {
            node1: {
                nodeId: 1,
                linksCount: 3,
                linksIds: ['link1', 'link2', 'link3'],
                linksNames: ['Link 1', 'Link 2', 'Link 3'],
            },
            node1_1: {
                nodeId: 2,
                linksCount: 5,
                linksIds: ['link4', 'link5'],
                linksNames: ['Link 4', 'Link 5'],
            },
        };

        const result = gatherNestedLinks(node, countMap);

        expect(result).toEqual([
            {
                nodeId: 1,
                linksCount: 3,
                linksIds: ['link1', 'link2', 'link3'],
                linksNames: ['Link 1', 'Link 2', 'Link 3'],
            },
            {
                nodeId: 2,
                linksCount: 5,
                linksIds: ['link4', 'link5'],
                linksNames: ['Link 4', 'Link 5'],
            },
        ]);
    });

    it('should handle cases with missing links in the linksCountsMap', () => {
        const partialLinksMap: LinksMap = {
            node1: {
                nodeId: 1,
                linksCount: 3,
                linksIds: ['link1', 'link2', 'link3'],
                linksNames: ['Link 1', 'Link 2', 'Link 3'],
            },
        };

        const result = applyLinksToTree(tree, partialLinksMap);

        expect(result).toEqual([
            {
                id: 'node1',
                label: 'Node 1',
                registerEntryId: 101,
                links: {
                    nodeId: 1,
                    linksCount: 3,
                    linksIds: ['link1', 'link2', 'link3'],
                    linksNames: ['Link 1', 'Link 2', 'Link 3'],
                },
                nestedLinks: [
                    {
                        nodeId: 1,
                        linksCount: 3,
                        linksIds: ['link1', 'link2', 'link3'],
                        linksNames: ['Link 1', 'Link 2', 'Link 3'],
                    },
                ],
                children: [
                    {
                        id: 'node1_1',
                        label: 'Node 1.1',
                        parentId: 'node1',
                        registerEntryId: 102,
                        links: undefined,
                        nestedLinks: [],
                        children: [],
                    },
                ],
            },
            {
                id: 'node2',
                label: 'Node 2',
                registerEntryId: 103,
                links: undefined,
                nestedLinks: [],
                children: [],
            },
        ]);
    });

    it('should return the original tree with undefined links and empty nestedLinks if no linksCountsMap is provided', () => {
        const result = applyLinksToTree(tree, {});

        expect(result).toEqual([
            {
                id: 'node1',
                label: 'Node 1',
                registerEntryId: 101,
                links: undefined,
                nestedLinks: [],
                children: [
                    {
                        id: 'node1_1',
                        label: 'Node 1.1',
                        parentId: 'node1',
                        registerEntryId: 102,
                        links: undefined,
                        nestedLinks: [],
                        children: [],
                    },
                ],
            },
            {
                id: 'node2',
                label: 'Node 2',
                registerEntryId: 103,
                links: undefined,
                nestedLinks: [],
                children: [],
            },
        ]);
    });
});
