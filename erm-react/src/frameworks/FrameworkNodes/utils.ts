import { HierarchicalNodeRead } from 'api/generated/types';
import { TreeItem, TreeItems } from 'frameworks/FrameworkNodes/SortableTree/types';
import { UniqueIdentifier } from '@dnd-kit/core';
import { LinksMap, NodeLinksCount } from 'frameworks/types';

export const filterTree = (nodes: TreeItems, searchValue: string) => {
    const getNodes = (result, node: TreeItem) => {
        if (node.label?.toLowerCase().includes(searchValue.toLowerCase())) {
            result.push(node);
            return result;
        }
        if (Array.isArray(node.children)) {
            const children = node.children.reduce(getNodes, []);
            if (children.length) {
                result.push({ ...node, children });
            }
        }
        return result;
    };

    return nodes.reduce(getNodes, []);
};

export const gatherNestedLinks = (node: TreeItem, countMap: LinksMap): NodeLinksCount[] => {
    const links: NodeLinksCount[] = countMap && countMap[node.id!] ? [countMap[node.id!]] : [];
    if (node.children) {
        for (const child of node.children) {
            links.push(...gatherNestedLinks(child, countMap));
        }
    }
    return links;
};

export const hierarchyToTreeNodes = <T extends HierarchicalNodeRead>(
    nodes: T[],
    parentNodeId: UniqueIdentifier | undefined = undefined,
    collapsedMap?: Record<string, boolean>,
): TreeItems => {
    return nodes?.map((node: T) => ({
        id: node.id?.toString() as UniqueIdentifier,
        label: node.name,
        collapsed: collapsedMap?.[node.id!] ?? true,
        parentId: parentNodeId?.toString(),
        registerEntryId: node.registerEntryId,
        registerName: node.registerName,
        children: node.childNodes ? hierarchyToTreeNodes(node.childNodes, node.id, collapsedMap) : undefined,
    }));
};

export const applyLinksToTree = (tree: TreeItems, linksCountsMap: LinksMap): TreeItems => {
    return tree.map((_node) => {
        const node = { ..._node };
        if (node.id) {
            node.links = linksCountsMap ? linksCountsMap[node.id!] : undefined;
            node.nestedLinks = gatherNestedLinks(node, linksCountsMap);
        }

        if (node.children) {
            node.children = applyLinksToTree(node.children, linksCountsMap);
        }

        return node;
    });
};

export const treeItemsToHierarchy = (nodes: TreeItems): HierarchicalNodeRead[] => {
    return nodes.map((node, index) => {
        const newNode: HierarchicalNodeRead = {
            id: JSON.parse(node.id as string),
            name: node.label,
            order: index + 1,
            ...(node.registerEntryId && { registerEntryId: node.registerEntryId }),
            ...(node.registerName && { registerName: node.registerName }),
        };
        if (node.children?.length) {
            newNode.childNodes = treeItemsToHierarchy(node.children);
        }
        return newNode;
    });
};

export const removeNodeById = (nodes: TreeItems, idToRemove: UniqueIdentifier): TreeItems => {
    const getNodesWithoutId = (result: TreeItems, _node: TreeItem): TreeItems => {
        const node = { ..._node };
        if (Array.isArray(node.children)) {
            node.children = node.children.reduce(getNodesWithoutId, []);
        }

        if (node.id !== idToRemove) {
            result.push({
                ...node,
                ...(Array.isArray(node.children) && node.children.length > 0 && { children: node.children }),
            });
        }

        return result;
    };

    return nodes.reduce(getNodesWithoutId, []);
};

export const findInTree = (tree: TreeItems, id?: UniqueIdentifier): TreeItem | null => {
    for (const node of tree) {
        if (node.id === id) {
            return node;
        }

        if (node.children) {
            const found = findInTree(node.children, id);
            if (found) {
                return found;
            }
        }
    }
    return null;
};

export const findAndReplaceNode = (tree: TreeItems, id: UniqueIdentifier, newNode: TreeItem): TreeItems => {
    return tree.map((_node) => {
        const node = { ..._node };
        if (node.id === id) {
            return newNode;
        }

        if (node.children) {
            node.children = findAndReplaceNode(node.children, id, newNode);
        }

        return node;
    });
};

export const buildNodesMap = (items: TreeItems): Record<string, boolean | undefined> => {
    const nodesMap: Record<string, boolean | undefined> = {};
    const buildMapRecursive = (items: TreeItems) => {
        items.map((item) => {
            nodesMap[item.id] = item.collapsed;
            if (item.children) {
                buildMapRecursive(item.children);
            }
        });
    };

    buildMapRecursive(items);

    return nodesMap;
};

export const areTreesEqual = (tree1: TreeItem[], tree2: TreeItem[]): boolean => {
    if (tree1.length !== tree2.length) {
        return false;
    }

    tree1.sort((a, b) => (a.id > b.id ? 1 : -1));
    tree2.sort((a, b) => (a.id > b.id ? 1 : -1));

    for (let i = 0; i < tree1.length; i++) {
        const node1 = tree1[i];
        const node2 = tree2[i];

        if (
            node1.id !== node2.id ||
            node1.label !== node2.label ||
            node1.disabled !== node2.disabled ||
            node1.collapsible !== node2.collapsible ||
            node1.collapsed !== node2.collapsed ||
            node1.parentId !== node2.parentId ||
            node1.registerEntryId !== node2.registerEntryId ||
            node1.registerName !== node2.registerName
        ) {
            return false;
        }

        const childNodes1 = node1.children || [];
        const childNodes2 = node2.children || [];

        if (!areTreesEqual(childNodes1, childNodes2)) {
            return false;
        }
    }

    return true;
};
