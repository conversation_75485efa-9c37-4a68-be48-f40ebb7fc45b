import React from 'react';
import { render } from 'test/utils';
import FrameworkNodes from './FrameworkNodes';
import { mockFrameworkData, mockFrameworkNodesHierarchyData, mockFrameworkNodesLinksCounts } from 'frameworks/mock';

jest.mock('common/hooks/useUnsavedChangesAlert');
jest.mock('frameworks/selectors', () => ({
    hasEditPermission: jest.fn(),
    hasMetaDataRegisterPermission: jest.fn(),
    getFrameworkAppId: jest.fn(),
    getFlattenedTree: jest.fn(),
    getSearchValue: jest.fn().mockReturnValue(''),
    getFrameworkAppIdFromDictionary: jest.fn(),
}));

jest.mock('app/selectors', () => ({
    getGwtOverlayOpen: jest.fn().mockReturnValue(false),
}));

jest.mock('frameworks/rtkApi', () => {
    const useFcGetFrameworkUsingGetQuery = jest.fn(() => ({
        data: mockFrameworkData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }));

    const useFcGetFrameworkNodesHierarchyUsingGetQuery = jest.fn(() => ({
        data: mockFrameworkNodesHierarchyData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }));

    const useFcUpdateNodesHierarchyUsingPutMutation = jest.fn(() => [jest.fn(), { isLoading: false, error: null }]);
    const useFcUpdateStatusUsingPutMutation = jest.fn(() => [jest.fn(), { isLoading: false, error: null }]);

    const useFlcGetFrameworkNodesLinksCountsUsingGet1Query = jest.fn(() => ({
        data: mockFrameworkNodesLinksCounts,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }));

    const mockedFramework = {
        select: jest.fn(() => () => mockFrameworkData),
    };

    const frameworksApi = {
        endpoints: {
            fcGetFrameworkUsingGet: mockedFramework,
        },
    };

    const useFcImportDataRegisterUsingPost1Mutation = jest.fn(() => [jest.fn(), { isLoading: false, error: null }]);
    const useFcImportVerifyDataRegisterUsingPost1Mutation = jest.fn(() => [jest.fn(), { isLoading: false, error: null }]);
    const useLazyFcExportDataRegisterUsingGetQuery = jest.fn(() => [jest.fn(), { isLoading: false, error: null }]);
    const useLazyGetExportContentCsvQuery = jest.fn(() => [jest.fn(), { isLoading: false, error: null }]);

    return {
        useFcGetFrameworkUsingGetQuery,
        useFcGetFrameworkNodesHierarchyUsingGetQuery,
        useFcUpdateNodesHierarchyUsingPutMutation,
        useFcUpdateStatusUsingPutMutation,
        useFlcGetFrameworkNodesLinksCountsUsingGet1Query,
        useFcImportDataRegisterUsingPost1Mutation,
        useFcImportVerifyDataRegisterUsingPost1Mutation,
        useLazyFcExportDataRegisterUsingGetQuery,
        useLazyGetExportContentCsvQuery,
        frameworksApi,
    };
});

// Mock useForm to prevent state updates that cause act() warnings
jest.mock('common/hooks/forms/useForm', () => {
    return {
        __esModule: true,
        default: jest.fn(() => ({
            formState: { isValid: true, errors: {} },
            handleSubmit: jest.fn(),
            reset: jest.fn(),
            setError: jest.fn(),
            getValues: jest.fn(),
            trigger: jest.fn(),
        })),
    };
});

describe('FrameworkNodes', () => {
    beforeEach(() => {
        window.ReactBridge = {
            FrameworkConfiguration: {
                refresh: jest.fn(),
            },
        };
    });

    const setup = (disableToolbar?: boolean) => {
        return render(<FrameworkNodes disableToolbar={disableToolbar} />);
    };

    it('was rendered', () => {
        const { asFragment } = setup();

        const clonedFragment = asFragment().cloneNode(true);

        function removeDynamicAttributes(node) {
            if (node.getAttribute) {
                node.removeAttribute('id');
                node.removeAttribute('aria-controls');
                node.removeAttribute('aria-labelledby');
            }
            node.childNodes.forEach((child) => removeDynamicAttributes(child));
        }

        removeDynamicAttributes(clonedFragment);
        expect(clonedFragment).toMatchSnapshot();
    });

    it('renders with the disabled toolbar', async () => {
        const { asFragment } = setup(true);

        const clonedFragment = asFragment().cloneNode(true);

        function removeDynamicAttributes(node) {
            if (node.getAttribute) {
                node.removeAttribute('id');
                node.removeAttribute('aria-controls');
                node.removeAttribute('aria-labelledby');
            }
            node.childNodes.forEach((child) => removeDynamicAttributes(child));
        }

        removeDynamicAttributes(clonedFragment);
        expect(clonedFragment).toMatchSnapshot();
    });
});
