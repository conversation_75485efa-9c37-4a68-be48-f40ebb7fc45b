import React, { FC, useCallback, useContext, useState } from 'react';
import { useRdrsiGetFrameworkLinkedRegisterEntriesUsingPostMutation } from 'cyberrisk/rtkApi';
import { FrameworkContext } from '../ContextProvider';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { Register, ViewExpressionRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import { getReactRoot } from 'config';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import RegisterEntriesSelector from 'common/components/RegisterEntriesSelector';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';

const DialogContentLayout = styled(Box)({
    position: 'absolute',
    left: 0,
    right: 0,
    top: '58px',
    bottom: '58px',
    display: 'flex',
    flex: 1,
});

const LinkedEntriesDetailDialog: FC = () => {
    const { selectedRegister, nodeId, setNodeId } = useContext(FrameworkContext);
    const [triggerSearch] = useRdrsiGetFrameworkLinkedRegisterEntriesUsingPostMutation();
    const [currentSelection, setCurrentSelection] = useState<Register | undefined>();

    const fetchData = useCallback(
        async (requestParams: SearchRequestParams, expressions?: ViewExpressionRest[]) => {
            if (!nodeId || !selectedRegister?.id) {
                return undefined;
            }

            return triggerSearch({
                frameworkNodeId: nodeId,
                registerId: selectedRegister.id,
                body: expressions || [],
                ...requestParams,
            }).unwrap();
        },
        [nodeId, selectedRegister, triggerSearch],
    );

    const handleConfirm = useCallback(() => {
        window.open(
            `${ProtechtDictionary.siteUrl}/worms/client/app/widget.html?tablename=${selectedRegister?.tableName}&incidentId=${currentSelection?.id}&widget=RegisterPage`,
        );
    }, [currentSelection, selectedRegister]);

    return (
        <Dialog
            visible={true}
            width={720}
            height={672}
            dialogContainer={getReactRoot()}
            title={strings('frameworks:label.workspaceLinkedFrameworks')}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={() => setNodeId(undefined)}
                        dataTestId="button-cancel"
                    >
                        {strings('ermMessages:btn_cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        onClick={() => handleConfirm()}
                        disabled={!currentSelection}
                        dataTestId="button-confirm"
                    >
                        {strings('frameworks:button.openTab')}
                    </Button>
                </DialogActions>
            }
        >
            <DialogContentLayout>
                {selectedRegister?.id && (
                    <RegisterEntriesSelector
                        fetchData={fetchData}
                        registerId={selectedRegister?.id}
                        onSelect={(_, selected) => setCurrentSelection(selected[0])}
                    />
                )}
            </DialogContentLayout>
        </Dialog>
    );
};

export default LinkedEntriesDetailDialog;
