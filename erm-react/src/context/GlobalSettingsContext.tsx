import React, { createContext, useEffect, useState } from 'react';
import { GlobalSettingsContextType } from 'app/types';

const GlobalSettingsContext = createContext<GlobalSettingsContextType>({} as GlobalSettingsContextType);
export default GlobalSettingsContext;

type Props = {
    children: React.ReactNode;
};

export const GlobalSettingsContextProvider = ({ children }: Props) => {
    const [disableEnforceFocus, setDisableEnforceFocus] = useState<boolean>(false);

    useEffect(() => {
        window.ReactBridge!.GlobalSettings!.setDisableEnforceFocus = setDisableEnforceFocus;
    }, []);

    return <GlobalSettingsContext.Provider value={{ disableEnforceFocus, setDisableEnforceFocus }}>{children}</GlobalSettingsContext.Provider>;
};
