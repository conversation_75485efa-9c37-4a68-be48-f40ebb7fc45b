import ConfirmationAlert, { ConfirmationAlertProps } from 'common/components/ConfirmationAlert/ConfirmationAlert';

import { createContext, type ReactNode, useCallback, useMemo, useState } from 'react';

import React from 'react';

type ConfirmationAlertData = ConfirmationAlertProps & { keepOpenAfterConfirm?: boolean; keepOpenAfterClose?: boolean };

type ConfirmContext = {
    showConfirmationAlert: (options: ConfirmationAlertData | null) => void;
};

export const ConfirmationAlertContext = createContext<ConfirmContext | null>(null);

type Props = {
    children: ReactNode;
};

export function ConfirmationAlertProvider({ children }: Props) {
    const [confirmAlertData, setConfirmAlertData] = useState<ConfirmationAlertData | null>(null);
    const [open, toggle] = useState(false);

    const showConfirmationAlert = useCallback(
        (confirmOptions: ConfirmationAlertProps | null) => {
            setConfirmAlertData(confirmOptions);
            toggle(true);
        },
        [toggle],
    );

    const onConfirm = useCallback(() => {
        void confirmAlertData?.onConfirm?.();
        if (!confirmAlertData?.keepOpenAfterConfirm) {
            toggle(false);
        }
    }, [confirmAlertData]);

    const onCancel = useCallback(() => {
        confirmAlertData?.onClose?.();
        if (!confirmAlertData?.keepOpenAfterClose) {
            toggle(false);
        }
    }, [confirmAlertData]);

    const onBack = useCallback(() => {
        void confirmAlertData?.onBack?.();
        if (!confirmAlertData?.keepOpenAfterConfirm) {
            toggle(false);
        }
    }, [confirmAlertData]);

    const value = useMemo(() => ({ showConfirmationAlert }), [showConfirmationAlert]);

    return (
        <ConfirmationAlertContext.Provider value={value}>
            <ConfirmationAlert
                {...confirmAlertData}
                onConfirm={onConfirm}
                onClose={onCancel}
                onBack={onBack}
                visible={open}
            />
            {children}
        </ConfirmationAlertContext.Provider>
    );
}
