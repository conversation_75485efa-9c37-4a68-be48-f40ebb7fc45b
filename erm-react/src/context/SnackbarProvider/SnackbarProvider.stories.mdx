import { <PERSON>vas, <PERSON>a, Story, Source, ArgsTable } from '@storybook/addon-docs';
import useSnackbar from 'common/hooks/useSnackbar';
import Button from '@protecht/ui-library/library/components/Button';
import { VariantType } from 'notistack';
import SnackbarProvider from './SnackbarProvider';

<Meta
    title="components/UI/Snackbar"
    decorators={[
        (Story) => (
            <SnackbarProvider>
                <Story />
            </SnackbarProvider>
        ),
    ]}
    parameters={{
        docs: {
            source: {
                code: null,
            },
        },
    }}
    argTypes={{
        message: {
            control: 'text',
        },
        variant: {
            options: ['default', 'error', 'success', 'warning', 'info'],
            control: { type: 'select' },
        },
    }}
    args={{
        message: 'example toast message',
    }}
/>

# Snackbar Provider

Click on button to check out different snackbar variants.

export const RenderSnackbarButton = ({ variant, message, buttonText }) => {
    const { enqueueSnackbar } = useSnackbar();
    return (
        <Button
            onClick={() =>
                enqueueSnackbar(`${message}`, {
                    variant,
                    persist: variant === 'error' ? true : false,
                    anchorOrigin: { horizontal: 'right', vertical: 'bottom' },
                })
            }
        >
            {buttonText || `Show ${variant} toast`}
        </Button>
    );
};

<Canvas withSource="none">
    <Story
        name="Default"
        args={{
            variant: 'default',
        }}
    >
        {(args) => <RenderSnackbarButton {...args} />}
    </Story>
    <Story
        name="Success"
        args={{
            variant: 'success',
        }}
    >
        {(args) => <RenderSnackbarButton {...args} />}
    </Story>
    <Story
        name="Info"
        args={{
            variant: 'info',
        }}
    >
        {(args) => <RenderSnackbarButton {...args} />}
    </Story>
    <Story
        name="Warning"
        args={{
            variant: 'warning',
        }}
    >
        {(args) => <RenderSnackbarButton {...args} />}
    </Story>
    <Story
        name="Error"
        args={{
            variant: 'error',

        }}
    >
        {(args) => <RenderSnackbarButton {...args} />}
    </Story>
    <Story
        name="Long Message"
        args={{
            variant: 'error',
            buttonText: 'Show Snackbar with long text',
            message:
                'Some nice long message. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin posuere pretium lacinia. In nec sem sit amet turpis feugiat semper vel id sem. Sed sed neque libero. Aliquam hendrerit nibh sit amet nibh tempor, sit amet iaculis quam venenatis. Cras varius pellentesque vehicula. Nullam accumsan accumsan vestibulum. Vestibulum bibendum viverra orci vitae venenatis. Etiam pharetra interdum nunc ac iaculis. Cras at est rutrum odio interdum pellentesque non quis libero. Quisque volutpat mattis quam, a viverra mauris feugiat id. Pellentesque dapibus vulputate eros in ornare.',
        }}
    >
        {(args) => <RenderSnackbarButton {...args} />}
    </Story>

</Canvas>

<ArgsTable story="Default" />

Example usage: (Check out **SnackbarProvider** or **useSnackbar** files to see which options are preconfigured and passed to enqueue functions)

<Source
    language="js"
    dark
    format={true}
    code={`
    const {enqueueSuccess, enqueueError} = useSnackbar();
    //... use functions from hook somewhere inside component
    enqueueSuccess('example success');
    enqueueError('example error', {variant: 'error', persist: true});
  `}
/>
