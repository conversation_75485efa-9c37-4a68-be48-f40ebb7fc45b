import React, { createContext, PropsWithChildren, useContext, useEffect, useRef, useState } from 'react';
import { Snackbar<PERSON>ey, SnackbarProvider as NotistackSnackbarProvider, MaterialDesignContent, EnqueueSnackbar } from 'notistack';
import Fade from '@mui/material/Fade';
import IconButton from '@mui/material/IconButton';
import useTheme from '@mui/system/useTheme';
import { styled } from '@mui/material/styles';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faExclamationCircle, faExclamationTriangle, faTimes } from '@fortawesome/pro-solid-svg-icons';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';

const StyledMaterialDesignContent = styled(MaterialDesignContent)(({ theme }) => ({
    '&.notistack-MuiContent-success, &.notistack-MuiContent-error, &.notistack-MuiContent-warning, &.notistack-MuiContent-info': {
        backgroundColor: theme.palette.protechtGrey?.grey_44,
        display: 'flex',
        flexWrap: 'nowrap',
    },
    // check if it is posilbe to use MUI typography instead
    fontFamily: 'Open Sans',
}));

type ContextProps = {
    enqueueSnackbar: EnqueueSnackbar;
    closeSnackbar: (key: SnackbarKey) => void;
};

// This will hold the instance of the SnackbarProvider that we want to share
let sharedNotistackRef: React.RefObject<NotistackSnackbarProvider> | null = null;

// Create a context for sharing the Snackbar instance
const SnackbarContext = createContext<ContextProps | null>(null);

// Custom hook to get access to shared snackbar instance
export const useSnackbar = (): ContextProps => {
    const context = useContext(SnackbarContext);
    if (!context) {
        throw new Error('useSnackbar must be used within a SnackbarProvider');
    }
    return context;
};

const SnackbarProvider: React.FC<PropsWithChildren<Record<never, any>>> = ({ children }) => {
    const [isSnackbarReady, setIsSnackbarReady] = useState(false);
    const notistackRef = useRef<NotistackSnackbarProvider>(null);

    const theme = useTheme();

    const onClickDismiss = (key: SnackbarKey) => () => {
        sharedNotistackRef?.current?.closeSnackbar(key);
    };

    useEffect(() => {
        if (notistackRef.current) {
            if (!sharedNotistackRef?.current) {
                sharedNotistackRef = notistackRef;
            }
            setIsSnackbarReady(true);
        }
    }, []);

    return (
        <NotistackSnackbarProvider
            ref={notistackRef}
            maxSnack={5}
            preventDuplicate={true}
            TransitionComponent={Fade}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'center',
            }}
            iconVariant={{
                success: (
                    <FontAwesomeIcon
                        icon={faCheck}
                        style={{ marginRight: '8px' }}
                        color={theme.palette.success.light}
                    />
                ),
                error: (
                    <FontAwesomeIcon
                        icon={faExclamationCircle}
                        style={{ marginRight: '8px' }}
                        color={theme.palette.accentColors?.red}
                    />
                ),
                warning: (
                    <FontAwesomeIcon
                        icon={faExclamationTriangle}
                        style={{ marginRight: '8px' }}
                        color={theme.palette.accentColors?.orange}
                    />
                ),
                info: (
                    <FontAwesomeIcon
                        icon={faInfoCircle}
                        style={{ marginRight: '8px' }}
                        color={'#4080FF'}
                    />
                ),
            }}
            Components={{
                success: StyledMaterialDesignContent,
                error: StyledMaterialDesignContent,
                warning: StyledMaterialDesignContent,
                info: StyledMaterialDesignContent,
            }}
            action={(key: SnackbarKey) => (
                <IconButton
                    color="secondary"
                    onClick={onClickDismiss(key)}
                    sx={{
                        '&.MuiButtonBase-root:hover': {
                            bgcolor: 'transparent',
                        },
                    }}
                >
                    <FontAwesomeIcon
                        icon={faTimes}
                        color="white"
                        opacity={0.6}
                    />
                </IconButton>
            )}
        >
            {isSnackbarReady && sharedNotistackRef?.current && (
                <SnackbarContext.Provider
                    value={{ enqueueSnackbar: sharedNotistackRef.current.enqueueSnackbar, closeSnackbar: sharedNotistackRef.current.closeSnackbar }}
                >
                    {children}
                </SnackbarContext.Provider>
            )}
        </NotistackSnackbarProvider>
    );
};

export default SnackbarProvider;
