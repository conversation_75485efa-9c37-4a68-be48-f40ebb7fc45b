import { useCallback, useMemo } from 'react';
import { FieldValues, useFormContext } from 'react-hook-form';
import { RegisterStateDefinition, RegisterEntryRest } from 'register/types';
import { RegisterStateTransitionRest, RegisterDataRest, TableMetadataRest } from 'api/generated/types';
import { useSnackbar } from 'notistack';
import { strings } from 'common/utils/i18n';
import { useRdrsvTransitionEntryUsingPutMutation } from 'register/rtkApi';

interface UseRegisterStateTransitionParams {
    entry: RegisterEntryRest;
    register: { stateful: boolean; stateDefinition?: RegisterStateDefinition };
    registerId?: number;
    onUpdateEntry?: (updatedEntry: RegisterEntryRest) => void;
    onError?: (error: string) => void;
    entrySave?: () => (values: FieldValues) => void | Promise<void>;
}

interface UseRegisterStateTransitionReturn {
    currentState: string | undefined;
    stateDefinition: RegisterStateDefinition | undefined;
    isStateful: boolean;
    availableTransitions: RegisterStateTransitionRest[];
    canTransition: boolean;
    executeTransition: (transition: RegisterStateTransitionRest) => Promise<void>;
    getStateInfo: (stateName: string) => { name: string; isInitial: boolean; isFinal: boolean } | undefined;
    isInitialState: boolean;
    isFinalState: boolean;
    isLoading: boolean;
}

export const useRegisterStateTransition = ({
    entry,
    register,
    registerId,
    onUpdateEntry,
    onError,
    entrySave,
}: UseRegisterStateTransitionParams): UseRegisterStateTransitionReturn => {
    const { enqueueSnackbar } = useSnackbar();
    const [rdrsvTransitionEntryUsingPut, { isLoading }] = useRdrsvTransitionEntryUsingPutMutation();
    
    // Optional form context - may not be available in all use cases
    let formContext;
    try {
        formContext = useFormContext();
    } catch {
        formContext = null;
    }
    
    const { handleSubmit, trigger } = formContext || { handleSubmit: undefined, trigger: undefined };

    const isStateful = register.stateful && !!register.stateDefinition;
    const stateDefinition = register.stateDefinition;

    const currentState = useMemo(() => {
        if (entry.status) {
            return entry.status;
        }
        const statusColumn = (register as any).statusColumn;
        if (statusColumn) {
            for (const section of entry.sections) {
                for (const field of section.fields) {
                    if (field.fieldName === statusColumn.columnName ||
                        field.fieldId === statusColumn.id?.toString()) {
                        const statusValue = field.simpleValue?.[0];
                        return statusValue;
                    }
                }
            }
        }
        return undefined;
    }, [entry, register]);

    const currentStateInfo = useMemo(() => {
        if (!stateDefinition || !currentState) {
return undefined;
}
        return stateDefinition.states.find(state => state.name === currentState);
    }, [stateDefinition, currentState]);

    const availableTransitions = useMemo(() => {
        if (!stateDefinition || !currentState) {
return [];
}

        return (stateDefinition.stateTransitions as RegisterStateTransitionRest[])
            .filter(transition =>
                transition.currentState === currentState &&
                (transition as any).visible !== false
            )
            .sort((a, b) => (a.transitionOrder || 0) - (b.transitionOrder || 0));
    }, [stateDefinition, currentState]);

    const canTransition = availableTransitions.length > 0;
    const isInitialState = currentStateInfo?.isInitial ?? false;
    const isFinalState = currentStateInfo?.isFinal ?? false;

    const getStateInfo = useCallback((stateName: string) => {
        if (!stateDefinition) {
return undefined;
}
        return stateDefinition.states.find(state => state.name === stateName);
    }, [stateDefinition]);

    const saveAndValidate = useCallback(async () => {
        // Ak je dostupný form context, vykonaj validáciu
        if (trigger) {
            const valid = await trigger();     // vyvolá RHF validáciu
            if (!valid) throw new Error('Form not valid');
        }
        
        // Uloží pomocou poskytnutej funkcie alebo form handleSubmit
        if (handleSubmit && entrySave) {
            // RHF odovzdá do entrySave celé formValues
            await handleSubmit(entrySave)();
        } else if (entrySave) {
            // voláme bez hodnot, ak nemáme handleSubmit
            await entrySave({} as FieldValues);
        } else if (handleSubmit) {
            // aspoň prázdny submit, aby sa spustili onSubmit side‑effecty
            await handleSubmit(() => {})();
        }
        // Ak nie je ani entrySave ani handleSubmit, pokračuj bez uloženia
    }, [trigger, handleSubmit, entrySave]);

    const executeTransition = useCallback(async (transition: RegisterStateTransitionRest) => {
        try {
            if (!entry || !stateDefinition || !registerId || !transition.newState) {
                throw new Error('Entry, state definition, register ID, or new state not available');
            }

            const targetState = stateDefinition.states.find(state => state.name === transition.newState);
            if (!targetState) {
                throw new Error(`Target state ${transition.newState} not found`);
            }

            await saveAndValidate();

            const result = await rdrsvTransitionEntryUsingPut({
                regId: registerId,
                entryId: entry.id,
                body: [transition.newState],
                triggerWorkflows: true,
            }).unwrap();

            if (result && result.name) {
                const updatedEntry: RegisterEntryRest = {
                    ...entry,
                    status: result.name,
                };

                const updatedSections = entry.sections.map(section => ({
                    ...section,
                    fields: section.fields.map(field => {
                        if (field.fieldName === 'status' || field.fieldName === 'state') {
                            return {
                                ...field,
                                simpleValue: [result.name as string],
                            };
                        }
                        return field;
                    }) as typeof section.fields,
                }));

                const finalUpdatedEntry = {
                    ...updatedEntry,
                    sections: updatedSections as typeof entry.sections,
                };

                onUpdateEntry?.(finalUpdatedEntry);

                enqueueSnackbar(
                    strings('register:stateTransition.transitionSuccess', {
                        transition: transition.name || 'Unnamed Transition',
                        newState: targetState.name,
                    }),
                    { variant: 'success' }
                );
            } else {
                throw new Error('State transition failed');
            }

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            onError?.(errorMessage);
            enqueueSnackbar(
                strings('register:stateTransition.transitionError', { error: errorMessage }),
                { variant: 'error' }
            );
        }
    }, [entry, stateDefinition, registerId, rdrsvTransitionEntryUsingPut, onUpdateEntry, onError, enqueueSnackbar, saveAndValidate]);

    return {
        currentState,
        stateDefinition,
        isStateful,
        availableTransitions,
        canTransition,
        executeTransition,
        getStateInfo,
        isInitialState,
        isFinalState,
        isLoading,
    };
};
