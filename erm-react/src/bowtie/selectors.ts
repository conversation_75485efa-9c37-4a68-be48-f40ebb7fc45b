import { RootState } from 'store';
import { createSelector } from '@reduxjs/toolkit';
import { RegisterRest } from 'register/types';
import { BowTieRegisterEnum } from './types';
import { AsyncThunkStatus } from '../common/types';

export const getBowTieState = (state: RootState) => state.bowtie;
export const getConfiguration = (state: RootState) => state.bowtie.configuration;
export const isBowtieDataLoading = (state: RootState) =>
    Object.values(state.bowtie.loading).some((loadingState) => loadingState === AsyncThunkStatus.IDLE || loadingState === AsyncThunkStatus.PENDING);

export const getRegisterById = createSelector(
    [getBowTieState, (state: RootState, registerId?: number | null) => registerId],
    (bowTieState, registerId): RegisterRest | null | undefined => {
        return Object.values(bowTieState.registers).find((register) => `${register?.id}` === `${registerId}`);
    },
);

export const getRegisterByType = (state: RootState, type: BowTieRegisterEnum, id?: number) => {
    const bowtieState = state.bowtie;
    const key = (id ? `${type}_${id}` : type) || '';
    return bowtieState.registers[key];
};

export const getRegisterError = createSelector(
    [getBowTieState, (state: RootState, type: BowTieRegisterEnum, id?: number) => [type, id]],
    (bowTieState, [type, id]): string => {
        const key = (id ? `${type}_${id}` : type) || '';
        return bowTieState.error[key] || '';
    },
);
