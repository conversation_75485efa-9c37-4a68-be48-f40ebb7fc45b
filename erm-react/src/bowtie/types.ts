import { ICommand, IEdge, INode, List, Point } from 'yfiles';
import { IdWithName } from 'app/types';
import { PickersType, RegisterRest } from 'register/types';
import { TagTable } from 'library/types';
import { BaseModel, BaseNodeData, BaseLinkData } from 'common/diagram/types';

export type BowTieDiagram = {
    id: number;
    name: string;
    description?: string;
    status: BowTieStatus;
    createdBy: string;
    createDateFormatted: string;
    libraryLinkNotifications: LinkUpdateNotification[];
    lastModifiedBy: string;
    lastModifiedDate: string;
    lastModifiedDateFormatted: string;
    centralRiskName?: string;
    centralRiskId?: number;
    libraryLinkEnabled: boolean;
    showLegendEnabled: boolean;
    showControlsEnabled: boolean;
    locked: boolean;
    diagramModel?: DiagramModel;
    businessUnits?: IdWithName[];
    tags?: TagTable[];
};

export type DataModel = {
    nodes: NodeModel[];
    links: LinkModel[];
};

export type DiagramModel = DataModel & {
    enumLikelihood?: Rating[];
    enumRiskAppetite?: Rating[];
    enumImpact?: Rating[];
    enumControlEffectiveness?: Rating[];
};

export type LinkUpdateNotification = {
    id: number;
    type: LinkUpdateType;
    name?: string;
};

export enum LinkUpdateType {
    Changed = 'CHANGED',
    Deleted = 'DELETED',
    Purged = 'PURGED',
}

export type Rating = {
    id: number;
    value: string;
    color: string;
};

export type RatingModel = {
    ratingType: RatingType;
    ratingLabel: RatingLabel;
    valueId: number;
};

export enum RatingType {
    EnumLikelihood = 'enumLikelihood',
    EnumRiskAppetite = 'enumRiskAppetite',
    EnumControlEffectiveness = 'enumControlEffectiveness',
    EnumImpact = 'enumImpact',
}

export enum RatingLabel {
    Likelihood = 'Likelihood',
    RiskAppetite = 'Risk Appetite',
    Impact = 'Impact',
    ControlEffectiveness = 'Control Effectiveness',
}

export type DiagramModelRef = {
    getDiagramModel: () => object;
    exportImage: () => void;
    exportPDF: () => void;
    printDiagram: () => void;
    executeCommand: (command: ICommand, parameter?: any) => void;
};

export type BaseBowtieModel = BaseModel & {
    template: string;
};

export type NodeModel = BaseBowtieModel & {
    name: string;
    nodeType: string;
    originType?: string;
    libraryLink?: IdWithName;
    rating?: RatingModel;
    position?: { x: number; y: number };
};

export type LinkModel = BaseBowtieModel & {
    from: number;
    to: number;
};

export enum LinkNameChangeOption {
    KEEP_LINK = 'link',
    KEEP_NAME = 'name',
}

export type Node = Omit<INode, 'tag'> & {
    tag: NodeData;
};

export type Edge = Omit<IEdge, 'tag'> & {
    tag: LinkData;
};

export type NodeData = BaseNodeData & {
    type: NodeType;
    originType?: NodeType;
    rating?: RatingModel;
    ratingOptions?: Rating[];
    libraryLink?: IdWithName;
    hidden?: boolean;
    loc?: Point;
};

export type LinkData = BaseLinkData & {
    type: EdgeType;
};

export interface DiagramPredicate {
    nodePredicate: (node: Node) => boolean;
    edgePredicate: (edge: IEdge) => boolean;
}

export enum NodeType {
    MainRiskEvent = 'MainRiskEvent',
    RiskEvent = 'RiskEvent',
    Cause = 'Cause',
    Impact = 'Impact',
    Control = 'Control',
    Blank = 'Blank',
}

export enum EdgeType {
    DEFAULT = 'DEFAULT',
    SECONDARY = 'SECONDARY',
}

export type BowTieStyle = {
    nodeStyle: Map<NodeType, NodeStyle>;
    linkStyle: {
        defult: LinkStyle;
        sublink: LinkStyle;
    };
};

export type LinkStyle = {
    strokeColor: string;
};

export type NodeStyle = {
    fillColor1: string;
    fillColor2: string;
    strokeColor: string;
    iconColor: string;
    iconWeight: number;
    iconContent: string;
};

export enum BowTieStatus {
    Draft = 'DRAFT',
    Published = 'PUBLISHED',
    Template = 'TEMPLATE',
    Deleted = 'DELETED',
}

export const BowTieStatusDisplay = {
    [BowTieStatus.Draft]: 'Draft',
    [BowTieStatus.Published]: 'Published',
    [BowTieStatus.Template]: 'Template',
};

export interface AnonymousRegistersContextType {
    registersContext: RegisterRest | undefined;
    pickersContext: PickersType | undefined;
    loadRegistersContext: (registers: RegisterRest) => void;
    clearRegistersContext: () => void;
    loadPickersContext: (pickers: PickersType) => void;
    clearPickersContext: () => void;
}

export type ClipboardSubtreeState = {
    nodes: List<INode>;
    edges: List<IEdge>;
};

export enum Position {
    Left,
    Right,
    Bottom,
    Top,
}

export type BowTieRegisterRest = {
    primary: boolean;
    registerId: number;
    name?: string;
    registerTable?: string;
    displayColumn?: string;
    applicationId?: number;
};

export type ConfigDefinitionRest = Record<BowTieRegister, Partial<BowTieRegisterRest>>;

export enum BowTieRegister {
    RISK_EVENT = 'riskEventRegister',
    RISK_CAUSE = 'riskCauseRegister',
    RISK_CONTROL = 'riskControlRegister',
    RISK_IMPACT = 'riskImpactRegister',
}

export enum BowTieRegisterEnum {
    RiskEventRegister = 'riskEventRegister',
    RiskCauseRegister = 'riskCauseRegister',
    RiskImpactRegister = 'riskImpactRegister',
    RiskControlRegister = 'riskControlRegister',
}
