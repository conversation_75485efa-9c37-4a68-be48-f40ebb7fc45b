import { BowTieStatus } from 'bowtie/types';
import React from 'react';
import { render, screen } from 'test/utils';
import BowTieTableContent from './BowTieTableContent';
import { mockBowTieEntry } from 'bowtie/api.mock';
import { <PERSON><PERSON>erRouter, generatePath } from 'react-router';
import { BowtiePath } from 'bowtie/routes';
import { TagTreeItemRest, TagTypeRest } from 'api/generated/types';

const TagTypeMockData = {
    id: 3333,
    name: 'test-value-2',
    context: 'test-context-2',
};

jest.spyOn(console, 'error').mockImplementation();

const mockTagTreeData: TagTreeItemRest[] = [
    {
        id: 10004,
        name: 'Causes',
        context: 'riskcause',
        nodeType: 'category',
        children: [
            {
                id: 10120,
                name: 'Data',
                tagType: {
                    id: 10004,
                    name: 'Causes',
                    context: 'riskcause',
                    tagIds: [10120, 10040, 10042, 10041, 10043, 12140],
                },
                nodeType: 'tag',
            },
        ],
    },
];

jest.mock('library/rtkApi', () => ({
    ...jest.requireActual('library/rtkApi'),
    useTrsGetTagTypesUsingGetQuery: jest.fn(() => ({
        ...(TagRestMockDataArray as TagTypeRest[]),
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
    useTrsGetTagTreeUsingGetQuery: jest.fn(() => ({
        data: mockTagTreeData,
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
}));

const diagramsData = {
    records: [mockBowTieEntry],
    totalCount: 1,
};

jest.mock('bowtie/rtkApi', () => ({
    ...jest.requireActual('bowtie/rtkApi'),
    useBtrsGetDiagramsUsingGetQuery: () => ({
        data: diagramsData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }),
}));

const TagRestMockDataArray = [TagTypeMockData];

const mockNavigate = jest.fn();

jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    useNavigate: () => mockNavigate,
}));

describe('<BowTieTableContent />', () => {
    const setup = () => {
        return render(
            <BrowserRouter>
                <BowTieTableContent
                    contextMenuItems={[{ label: 'test label text', action: () => void 0 }]}
                    contextMenuFilterCondition={() => false}
                    status={BowTieStatus.Draft}
                />
            </BrowserRouter>,
        );
    };

    it('was rendered', async () => {
        const view = setup();
        expect(view.container).toMatchSnapshot();
        // TODO: Uncomment this once the tags are re-implemented
        /* await waitFor(() => {
            expect(useTrsGetTagTypesUsingGetQuery).toHaveBeenCalled();
        }); */
    });

    it('navigates on double click', async () => {
        const { user } = setup();

        expect(await screen.findByText(mockBowTieEntry.name)).toBeInTheDocument();
        const row = await screen.findByText('Some text name');

        await user.dblClick(row);
        const expectedPath = generatePath(BowtiePath.DIAGRAM, { id: '100000000000000000000' });
        expect(mockNavigate).toHaveBeenCalledWith(expectedPath, expect.anything());
    });
});
