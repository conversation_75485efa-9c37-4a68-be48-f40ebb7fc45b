import React, { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { SYSTEM_COLUMN, TagContext } from 'common/types';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import { generatePath, useLocation, useNavigate } from 'react-router';
import { BowTieColDef } from './BowTieDefinitions';
import { GridPaginationModel, GridRowParams } from '@mui/x-data-grid-pro';
import { BowtiePath } from 'bowtie/routes';
import { useDispatch, useSelector } from 'store';
import { getRequestParams, getSearchProperty, getSearchValue } from 'app/selectors';
import { setRequestParams } from 'app/reducer';
import useSearchExpression from 'common/hooks/useSearchExpression';
import { Table } from '@protecht/ui-library/library/components/Table';
import { strings } from 'common/utils/i18n';
import useTags from 'common/hooks/useTags';
import { IdOnly, SearchRequestParams, TagFilterTreeItem } from '@protecht/ui-library/library/types';
import { useBtrsGetDiagramsUsingGetQuery } from 'bowtie/rtkApi';
import { BowTieDiagramRest } from 'api/generated/types';
import { BowTieStatus } from 'bowtie/types';
import { LAZY_LOAD_LIMIT } from 'common/constants';
import { isEqual } from 'lodash';

type Props = {
    contextMenuItems: ContextMenuItem[];
    contextMenuFilterCondition: (item: ContextMenuItem, data: BowTieDiagramRest) => boolean;
    status: BowTieStatus | null;
};

export type BowTieTableContentRef = {
    refresh: () => void;
};

const columnVisibilityModel = {
    [SYSTEM_COLUMN.ID]: false,
};

const BowTieTableContent = (props: Props, ref: ForwardedRef<BowTieTableContentRef | undefined>) => {
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();
    const requestParams = useSelector(getRequestParams);
    const searchValue = useSelector(getSearchValue);
    const searchProperty = useSelector(getSearchProperty);
    const searchExpression = useSearchExpression(searchValue, searchProperty);
    const [queryParams, setQueryParams] = useState<SearchRequestParams | undefined>(undefined);
    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({ page: 0, pageSize: 10 });

    const { status } = props;

    useEffect(() => {
        setQueryParams((previousValue) => {
            const searchExpressionChanged =
                previousValue?.['filter-value'] !== searchExpression?.value || previousValue?.['filter-by'] !== searchExpression?.property;

            const newValue = {
                ...requestParams,
                'filter-by': searchExpression?.property,
                'filter-value': searchExpression?.value,
                'sort-by': requestParams?.orderBy,
                'sort-dir': requestParams?.orderType?.toUpperCase(),
                limit: requestParams?.limit || LAZY_LOAD_LIMIT,
            };

            if (searchExpressionChanged) {
                newValue.offset = 0;
            }

            if (!previousValue) {
                newValue.offset = 0;
                newValue.limit = LAZY_LOAD_LIMIT;
            }
            if (!newValue.limit) {
                newValue.limit = LAZY_LOAD_LIMIT;
            }

            const shouldUpdate = !isEqual(previousValue, newValue);

            if (shouldUpdate) {
                return newValue;
            }

            return previousValue;
        });
    }, [requestParams, searchExpression]);

    useEffect(() => {
        if (queryParams) {
            dispatch(setRequestParams(queryParams));
        }
    }, [queryParams, dispatch]);

    const {
        data: diagramsData,
        isLoading: diagramsLoading,
        refetch,
    } = useBtrsGetDiagramsUsingGetQuery(
        {
            status: status!,
            page: requestParams?.offset && requestParams?.limit ? requestParams?.offset / requestParams?.limit : 0,
            size: requestParams?.limit,
            tags: requestParams?.tagIds,
            view: requestParams?.viewId,
            ...queryParams!,
        },
        {
            skip: !status,
        },
    );

    const { tagTree } = useTags({ tagContext: TagContext.BOWTIE });

    useImperativeHandle(ref, () => ({
        refresh: () => {
            void refetch();
        },
    }));

    const records: BowTieDiagramRest[] = diagramsData?.records ?? [];
    const totalCount: number = diagramsData?.totalCount ?? 0;

    return (
        <Table
            loading={diagramsLoading}
            loadingMessage={strings('common:message.loading')}
            multiselect={false}
            columns={BowTieColDef}
            columnVisibilityModel={columnVisibilityModel}
            rows={records.filter((record): record is BowTieDiagramRest & IdOnly => record.id !== undefined)}
            totalCount={totalCount}
            params={requestParams}
            paginationModel={paginationModel}
            onPaginationModelChange={setPaginationModel}
            onParamsChanged={(params) => {
                dispatch(setRequestParams({ ...requestParams, ...params }));
            }}
            onRowDoubleClick={(param: GridRowParams) =>
                navigate(generatePath(BowtiePath.DIAGRAM, { id: param.id.toString() }), {
                    state: { from: location },
                })
            }
            contextMenuItems={props.contextMenuItems}
            contextMenuFilterCondition={props.contextMenuFilterCondition}
            tagTree={tagTree as TagFilterTreeItem[]}
        />
    );
};

export default forwardRef(BowTieTableContent);
