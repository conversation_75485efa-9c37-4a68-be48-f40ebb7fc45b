import React from 'react';
import { GridValueFormatterParams } from '@mui/x-data-grid-pro';

import { DataGridColDef, DATA_SYSTEM_COLUMN, SYSTEM_COLUMN, NavigationMenuItem } from 'common/types';
import { IdWithName } from 'app/types';
import { strings } from 'common/utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClock, faFile, faUsersClass } from '@fortawesome/pro-regular-svg-icons';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { FilterType } from 'view/types';
import { CellRenderDefault } from 'common/components/Table/Cell/CellWithTooltip';
import { BowtiePath } from 'bowtie/routes';
import { generatePath } from 'react-router';
import { dateFormatter, getDateWithFormat } from 'common/utils/definitions';
import { getCurrentUser } from 'app/selectors';
import store from 'store';

export const DEFAULT_SEARCH_FIELD = 'name';

export const LAZY_INITIAL_LIMIT = 24;
export const LAZY_LOADING_LIMIT = 24;

export const BowTieColDef: DataGridColDef[] = [
    {
        field: SYSTEM_COLUMN.ID,
        headerName: strings('library:label.id'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: DATA_SYSTEM_COLUMN.FIELD_NAME,
        headerName: strings('library:label.name'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: DATA_SYSTEM_COLUMN.FIELD_DESCRIPTION,
        headerName: strings('library:label.description'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: DATA_SYSTEM_COLUMN.CENTRAL_RISK_EVENT_NAME,
        headerName: strings('library:label.centralRiskName'),
        filterType: FilterType.STRING,
        sortable: false,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        groupable: false,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: DATA_SYSTEM_COLUMN.FIELD_TAGS,
        headerName: strings('library:label.tags'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => (params.value ? (params.value as Array<IdWithName>) : []).map((item) => item.name).join(', '),
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: DATA_SYSTEM_COLUMN.FIELD_BUSINESS_UNITS,
        headerName: strings('library:label.businessUnits'),
        filterType: FilterType.STRING,
        sortable: false,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        valueFormatter: (params: GridValueFormatterParams) => (params.value ? (params.value as Array<IdWithName>) : []).map((item) => item.name).join(', '),
        groupable: false,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        // cannot use historical field as BowTie field does not match the value (createDate vs createdDate)
        field: DATA_SYSTEM_COLUMN.FIELD_CREATE_DATE,
        headerName: strings('library:label.createDate'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
        valueGetter: (params) => getDateWithFormat(params),
        valueFormatter: ({ value }) => dateFormatter(value, getCurrentUser(store.getState())),
    },
    {
        field: DATA_SYSTEM_COLUMN.FIELD_CREATED_BY,
        headerName: strings('library:label.createdBy'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
    {
        field: DATA_SYSTEM_COLUMN.FIELD_MODIFIED_DATE,
        headerName: strings('library:label.lastModifiedDate'),
        filterType: FilterType.DATE,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
        valueGetter: (params) => getDateWithFormat(params),
        valueFormatter: ({ value }) => dateFormatter(value, getCurrentUser(store.getState())),
    },
    {
        field: DATA_SYSTEM_COLUMN.FIELD_MODIFIED_BY,
        headerName: strings('library:label.lastModifiedBy'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        renderCell: (params) => (
            <CellRenderDefault
                value={params.value}
                formattedValue={params.formattedValue}
                computedCellWidth={params.colDef.computedWidth}
            />
        ),
    },
];

export const sideMenuItems: NavigationMenuItem[] = [
    {
        key: 'key-recent',
        icon: <FontAwesomeIcon icon={faClock} />,
        label: strings('bowtie:sideMenu.recent'),
        pathname: generatePath(BowtiePath.OVERVIEW, { status: 'recent' }),
    },
    {
        key: 'key-drafts',
        icon: <FontAwesomeIcon icon={faFile} />,
        label: strings('bowtie:sideMenu.drafts'),
        pathname: generatePath(BowtiePath.OVERVIEW, { status: 'drafts' }),
    },
    {
        key: 'key-published',
        icon: <FontAwesomeIcon icon={faUsersClass} />,
        label: strings('bowtie:sideMenu.published'),
        pathname: generatePath(BowtiePath.OVERVIEW, { status: 'published' }),
    },
];
