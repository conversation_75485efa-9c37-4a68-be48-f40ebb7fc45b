import React, { ForwardedRef, forwardRef, Ref, useCallback, useEffect, useImperativeHandle, useState } from 'react';

import CardContentStatus from 'ui/components/Cards/CardContentStatus';
import CardHeaderWithAction from 'ui/components/Cards/CardHeaderWithAction';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import { PagingResult } from 'common/api/types';
import { BowTieDiagram, BowTieStatus } from 'bowtie/types';
import { generatePath, useLocation, useNavigate } from 'react-router';

import { strings } from 'common/utils/i18n';
import Loading from 'common/components/Loading/Loading';
import EmptyCardMessage from './EmptyCardMessage';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { bowTieIcon } from 'common/utils/icons/bowTie';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { getDiagrams, getRecentDiagrams } from '../../api';
import { LoaderType } from 'common/components/Loading/Loading';
import { BowtiePath } from 'bowtie/routes';
import useAbortableQuery from 'common/hooks/useAbortableQuery/useAbortableQuery';
import useInfiniteScrollLoading from 'common/hooks/useInfiniteScrollLoading';
import { isEqual } from 'lodash';
import { LAZY_LOAD_LIMIT } from 'common/constants';
import { useDispatch, useSelector } from 'store';
import { getRequestParams, getSearchProperty, getSearchValue } from 'app/selectors';
import { setRequestParams } from 'app/reducer';
import useSearchExpression from 'common/hooks/useSearchExpression';
import { BOWTIE_CARD_TEST_ID_PREFIX } from 'bowtie/constants';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';

type Props = {
    contextMenuItems: ContextMenuItem[];
    contextMenuFilterCondition: (item: ContextMenuItem, data: BowTieDiagram) => boolean;
    onCreateNewClicked: () => void;
    status: BowTieStatus | null;
};

export type BowTieCardContentRef = {
    refresh: () => void;
};

const CenteredBox = styled(Box)({
    display: 'flex',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
});

const BowTieCardContent = (props: Props, ref: ForwardedRef<BowTieCardContentRef | undefined>) => {
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();
    const requestParams = useSelector(getRequestParams);

    const searchValue = useSelector(getSearchValue);
    const searchProperty = useSelector(getSearchProperty);
    const searchExpression = useSearchExpression(searchValue, searchProperty);

    const { status } = props;
    const [queryParams, setQueryParams] = useState<SearchRequestParams | undefined>(undefined);

    useEffect(() => {
        setQueryParams((previousValue) => {
            const newValue = { ...requestParams };
            // when changing from table view, reset paging and limit to default values
            if (!previousValue) {
                newValue.offset = 0;
                newValue.limit = LAZY_LOAD_LIMIT;
            }
            if (!newValue.limit) {
                newValue.limit = LAZY_LOAD_LIMIT;
            }
            const shouldUpdate = !isEqual(previousValue, newValue);

            if (shouldUpdate) {
                return newValue;
            }

            return previousValue;
        });
    }, [requestParams, searchExpression]);

    useEffect(() => {
        if (queryParams) {
            dispatch(setRequestParams(queryParams));
        }
    }, [queryParams, dispatch]);

    const loadFunction = useCallback(
        async (abortController: AbortController, page: number, limit?: number): Promise<PagingResult<BowTieDiagram>> => {
            if (status) {
                const res = await getDiagrams(
                    status,
                    { ...queryParams!, page, limit: limit ?? queryParams!.limit },
                    searchExpression?.property ?? '',
                    searchExpression?.value ?? '',
                    abortController,
                );
                return { totalCount: res?.totalCount || 0, records: res?.records || [] };
            } else {
                const res = await getRecentDiagrams(abortController);
                return { totalCount: res?.length || 0, records: res || [] };
            }
        },
        [searchExpression, status, queryParams],
    );

    const abortableLoadingFunction = useAbortableQuery(queryParams ? loadFunction : undefined);

    const {
        loadedItems: records,
        totalCount,
        isLoading: loading,
        intersectionObserver,
        refresh,
        hasMoreToLoad,
    } = useInfiniteScrollLoading<BowTieDiagram>(abortableLoadingFunction);

    useImperativeHandle(ref, () => ({
        refresh() {
            refresh(queryParams?.limit);
        },
    }));

    const loadErrorMessage = (
        <Typography
            variant="h7"
            sx={{
                whiteSpace: 'pre-wrap',
                textAlign: 'center',
                width: '100%',
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
            }}
            color="textSecondary"
        >
            {strings('bowtie:card.message.loadDataFailed')}
        </Typography>
    );

    const renderItems = () => {
        return records?.map((bowTie: BowTieDiagram) => (
            <Grid
                key={'bowtieCard' + bowTie.id}
                item
                xs={12}
                sm={12}
                md={6}
                lg={4}
            >
                <Card
                    data-testid={`${BOWTIE_CARD_TEST_ID_PREFIX}-${bowTie.id}`}
                    onClick={() => navigate(generatePath(BowtiePath.DIAGRAM, { id: bowTie.id.toString() }), { state: { from: location } })}
                    style={{ height: '130px', borderBottom: `3px solid ${ProtechtDictionary.accentColor}` }}
                >
                    <CardHeaderWithAction
                        avatar={
                            <Avatar style={{ width: 40, height: 40 }}>
                                <FontAwesomeIcon
                                    icon={bowTieIcon}
                                    style={{ color: ProtechtDictionary.accentColor }}
                                    size="lg"
                                />
                            </Avatar>
                        }
                        title={bowTie.name}
                        contextMenuItems={props.contextMenuItems.filter((item: ContextMenuItem) => props.contextMenuFilterCondition(item, bowTie))}
                        contextData={bowTie}
                    />
                    <CardContentStatus
                        status={bowTie.status}
                        modifiedBy={bowTie.lastModifiedBy}
                        modifiedDate={bowTie.lastModifiedDate}
                        locked={bowTie?.locked}
                    />
                </Card>
            </Grid>
        ));
    };

    const getRef = () => {
        if (status) {
            return intersectionObserver as Ref<HTMLDivElement>;
        }
        return null;
    };

    if (loading) {
        return (
            <CenteredBox>
                <Loading inline />
            </CenteredBox>
        );
    }

    if (totalCount === -1) {
        return <CenteredBox>{loadErrorMessage}</CenteredBox>;
    }

    if (records?.length === 0) {
        return (
            <CenteredBox>
                <EmptyCardMessage
                    statusFilter={status}
                    onCreateNewClicked={props.onCreateNewClicked}
                />
            </CenteredBox>
        );
    }

    return (
        <Grid
            container
            spacing={2}
            justifyContent="flex-start"
            alignContent="flex-start"
        >
            {renderItems()}
            {hasMoreToLoad && (
                <Grid
                    ref={getRef()}
                    item
                    xs={12}
                    sm={12}
                    md={12}
                    lg={12}
                    xl={12}
                >
                    <Loading
                        loaderType={LoaderType.Slim}
                        inline={true}
                    />
                </Grid>
            )}
        </Grid>
    );
};

export const ForwardedBowTieCardContent = forwardRef(BowTieCardContent);
export default ForwardedBowTieCardContent;
