import React from 'react';
import { render, screen } from 'test/utils';
import user from '@testing-library/user-event';

import EmptyCardMessage from './EmptyCardMessage';
import { BowTieStatus } from 'bowtie/types';

describe('<EmptyCardMessage />', () => {
    const onCreateNewBowTie = jest.fn();

    const setup = () => {
        return render(
            <EmptyCardMessage
                statusFilter={BowTieStatus.Draft}
                onCreateNewClicked={onCreateNewBowTie}
            />,
        );
    };

    it('was rendered', () => {
        const view = setup();
        expect(view.container).toMatchSnapshot();
    });

    it('open a new dialog for creating a New Bow Tie', async () => {
        setup();
        const newBowTieBtn = screen.getByTestId('button-New Bow Tie');
        expect(newBowTieBtn).toBeInTheDocument();
        expect(newBowTieBtn).toBeEnabled();
        expect(newBowTieBtn).toBeVisible();
        await user.click(newBowTieBtn);
        expect(onCreateNewBowTie).toHaveBeenCalled();
    });
});
