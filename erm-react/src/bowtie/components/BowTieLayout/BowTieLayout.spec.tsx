import React from 'react';
import { waitFor } from '@testing-library/react';
import { render, screen } from 'test/utils';

import BowTieLayout from './BowTieLayout';
import { MemoryRouter } from 'react-router';

import { mockBowTieEntryApi, mockedDefinition } from 'bowtie/api.mock';

const mockUseLocation = jest.fn();
ProtechtDictionary.ermMods = 'OLD_CONTROLS';

jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    useLocation: () => mockUseLocation(),
    useNavigate: jest.fn(),
}));

jest.mock('yfiles', () => ({}));
const mockTriggerGetDiagramData = jest.fn().mockReturnValue({ unwrap: () => Promise.resolve(mockBowTieEntryApi) });
const diagramsData = {
    records: [mockBowTieEntryApi],
    totalCount: 1,
};

jest.mock('bowtie/rtkApi', () => ({
    ...jest.requireActual('bowtie/rtkApi'),
    useBtrsGetDefinitionUsingGetQuery: jest.fn(() => ({
        data: mockedDefinition,
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
    useLazyBtrsGetDiagramUsingGetQuery: () => [mockTriggerGetDiagramData, { isFetching: false }],
}));

jest.mock('bowtie/api', () => ({
    ...jest.requireActual('bowtie/api'),
    getDiagram: jest.fn().mockImplementation(() => Promise.resolve(diagramsData)),
    getRecentDiagrams: jest.fn().mockImplementation(() => Promise.resolve(diagramsData)),
    getDefinition: jest.fn().mockImplementation(() => Promise.resolve(mockedDefinition)),
}));

describe('<BowTieLayout />', () => {
    const setup = () => {
        return render(
            <MemoryRouter initialEntries={['/worms/client/app/react/default/bowties/recent']}>
                <BowTieLayout />
            </MemoryRouter>,
        );
    };

    beforeEach(() => {
        mockUseLocation.mockImplementation(() => {
            return {
                pathname: '/testroute',
                search: '',
                hash: '',
                state: null,
            };
        });
    });

    afterEach(() => {
        mockUseLocation.mockReset();
    });

    it('was rendered', async () => {
        const view = setup();
        await waitFor(() => {
            expect(view.container).toBeInTheDocument();
        });
        expect(view.container).toBeVisible();
        expect(view.container).toMatchSnapshot();
    });

    it('open a new dialog for creating a New Bow Tie', async () => {
        const { user } = setup();
        const newBowTieBtn = screen.getByTestId('button-New Bow Tie');
        expect(newBowTieBtn).toBeInTheDocument();
        expect(newBowTieBtn).toBeEnabled();
        expect(newBowTieBtn).toBeVisible();
        await user.click(newBowTieBtn);
        expect(screen.getByRole('dialog')).toBeVisible();
    });

    it.each(['Recent', 'Drafts', 'Published'])('click on %s button', async (buttonName) => {
        mockUseLocation.mockImplementation(() => {
            return {
                pathname: `/bowties/${buttonName.toLowerCase()}`,
                search: '',
                hash: '',
                state: null,
            };
        });
        const { user } = setup();
        const button = screen.getByRole('button', { name: buttonName });
        await user.click(button);
        expect(button).toHaveClass('Mui-selected');
    });
});
