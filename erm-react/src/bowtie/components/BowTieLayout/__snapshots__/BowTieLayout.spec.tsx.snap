// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieLayout /> was rendered 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <h1
          class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-2bl176-MuiTypography-root"
          data-testid="bowties-heading"
        >
          Bow Ties
        </h1>
        <div
          class="MuiBox-root css-k008qs"
        >
          <button
            aria-label="import-new-bowtie-button"
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge Mui<PERSON>utton-outlinedSizeLarge <PERSON>-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge <PERSON>utton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-2vm4xl-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Import"
            tabindex="0"
            type="button"
          >
            <span
              class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
            >
              <svg
                data-icon="add"
                fill="currentColor"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.5 10.75h15v2.5h-15z"
                  fill="currentColor"
                />
                <path
                  d="M10.75 19.5v-15h2.5v15z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <span
              class="css-qv0y8m"
            >
              Import
            </span>
          </button>
          <button
            aria-label="create-new-bowtie-button"
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jezn90-MuiButtonBase-root-MuiButton-root"
            data-testid="button-New Bow Tie"
            tabindex="0"
            type="button"
          >
            <span
              class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
            >
              <svg
                data-icon="add"
                fill="currentColor"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.5 10.75h15v2.5h-15z"
                  fill="currentColor"
                />
                <path
                  d="M10.75 19.5v-15h2.5v15z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <span
              class="css-qv0y8m"
            >
              New Bow Tie
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-1do5ouh-MuiGrid-root"
      >
        <ul
          class="MuiList-root MuiList-padding css-1yyxzsq-MuiList-root"
          data-testid="side-menu"
        >
          <div
            focuscolor="#1B4AD5"
            style="margin: 2px; outline-color: rgb(27, 74, 213);"
            tabindex="0"
          >
            <div
              class="MuiBox-root css-0"
            >
              <div
                class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-root MuiListItemButton-dense css-14qn12j-MuiButtonBase-root-MuiListItemButton-root"
                role="button"
                tabindex="-1"
              >
                <div
                  class="MuiListItemIcon-root css-cveggr-MuiListItemIcon-root"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-clock "
                    data-icon="clock"
                    data-prefix="far"
                    focusable="false"
                    role="img"
                    viewBox="0 0 512 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M464 256A208 208 0 1 1 48 256a208 208 0 1 1 416 0zM0 256a256 256 0 1 0 512 0A256 256 0 1 0 0 256zM232 120l0 136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2 280 120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <span
                  aria-label="Recent"
                  class="css-2wjxxz"
                  data-mui-internal-clone-element="true"
                  style="overflow: hidden; width: 100%;"
                >
                  <div
                    class="MuiListItemText-root MuiListItemText-dense css-tlelie-MuiListItemText-root"
                    data-testid="sideMenu-item-Recent"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body2 MuiListItemText-primary css-dlbsvh-MuiTypography-root"
                    >
                      Recent
                    </span>
                  </div>
                </span>
              </div>
            </div>
            <div
              class="MuiBox-root css-0"
            >
              <div
                class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-root MuiListItemButton-dense css-14qn12j-MuiButtonBase-root-MuiListItemButton-root"
                role="button"
                tabindex="-1"
              >
                <div
                  class="MuiListItemIcon-root css-cveggr-MuiListItemIcon-root"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-file "
                    data-icon="file"
                    data-prefix="far"
                    focusable="false"
                    role="img"
                    viewBox="0 0 384 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M320 464c8.8 0 16-7.2 16-16l0-288-80 0c-17.7 0-32-14.3-32-32l0-80L64 48c-8.8 0-16 7.2-16 16l0 384c0 8.8 7.2 16 16 16l256 0zM0 64C0 28.7 28.7 0 64 0L229.5 0c17 0 33.3 6.7 45.3 18.7l90.5 90.5c12 12 18.7 28.3 18.7 45.3L384 448c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 64z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <span
                  aria-label="Drafts"
                  class="css-2wjxxz"
                  data-mui-internal-clone-element="true"
                  style="overflow: hidden; width: 100%;"
                >
                  <div
                    class="MuiListItemText-root MuiListItemText-dense css-tlelie-MuiListItemText-root"
                    data-testid="sideMenu-item-Drafts"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body2 MuiListItemText-primary css-dlbsvh-MuiTypography-root"
                    >
                      Drafts
                    </span>
                  </div>
                </span>
              </div>
            </div>
            <div
              class="MuiBox-root css-0"
            >
              <div
                class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-root MuiListItemButton-dense css-14qn12j-MuiButtonBase-root-MuiListItemButton-root"
                role="button"
                tabindex="-1"
              >
                <div
                  class="MuiListItemIcon-root css-cveggr-MuiListItemIcon-root"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-screen-users "
                    data-icon="screen-users"
                    data-prefix="far"
                    focusable="false"
                    role="img"
                    viewBox="0 0 640 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M96 384a64 64 0 1 0 0-128 64 64 0 1 0 0 128zM64 416c-35.3 0-64 28.7-64 64c0 17.7 14.3 32 32 32l128 0c17.7 0 32-14.3 32-32c0-35.3-28.7-64-64-64l-64 0zm256-32a64 64 0 1 0 0-128 64 64 0 1 0 0 128zm-32 32c-35.3 0-64 28.7-64 64c0 17.7 14.3 32 32 32l128 0c17.7 0 32-14.3 32-32c0-35.3-28.7-64-64-64l-64 0zm320-96a64 64 0 1 0 -128 0 64 64 0 1 0 128 0zM448 480c0 17.7 14.3 32 32 32l128 0c17.7 0 32-14.3 32-32c0-35.3-28.7-64-64-64l-64 0c-35.3 0-64 28.7-64 64zM544 48L96 48c-8.8 0-16 7.2-16 16l0 161.3c-18.3 3.1-34.8 11.3-48 23.1L32 64C32 28.7 60.7 0 96 0L544 0c35.3 0 64 28.7 64 64l0 184.4c-13.2-11.8-29.7-20.1-48-23.1L560 64c0-8.8-7.2-16-16-16z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <span
                  aria-label="Published"
                  class="css-2wjxxz"
                  data-mui-internal-clone-element="true"
                  style="overflow: hidden; width: 100%;"
                >
                  <div
                    class="MuiListItemText-root MuiListItemText-dense css-tlelie-MuiListItemText-root"
                    data-testid="sideMenu-item-Published"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body2 MuiListItemText-primary css-dlbsvh-MuiTypography-root"
                    >
                      Published
                    </span>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </ul>
        <hr
          class="MuiDivider-root MuiDivider-fullWidth css-iu0mcr-MuiDivider-root"
        />
      </div>
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-18q8d33-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-259gsz-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-k7hc3i"
          >
            <div
              class="css-g4z9mp"
            >
              <div
                class="css-1yihh2g"
              >
                <div
                  class="icon"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-spinner icon loadingSpinner"
                    data-icon="spinner"
                    data-prefix="fad"
                    focusable="false"
                    role="img"
                    viewBox="0 0 512 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      class="fa-duotone-group"
                    >
                      <path
                        class="fa-secondary"
                        d="M60.9 403.1a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM208 464a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM369.1 75A48 48 0 1 0 437 142.9 48 48 0 1 0 369.1 75zm0 294.2A48 48 0 1 0 437 437a48 48 0 1 0 -67.9-67.9zM416 256a48 48 0 1 0 96 0 48 48 0 1 0 -96 0z"
                        fill="currentColor"
                      />
                      <path
                        class="fa-primary"
                        d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM96 256A48 48 0 1 0 0 256a48 48 0 1 0 96 0zM75 142.9A48 48 0 1 0 142.9 75 48 48 0 1 0 75 142.9z"
                        fill="currentColor"
                      />
                    </g>
                  </svg>
                </div>
                <p
                  class="MuiTypography-root MuiTypography-body1 css-1tnh15z-MuiTypography-root"
                >
                  Loading...
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
