// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieTableContent /> was rendered 1`] = `
<div>
  <div
    class="css-m1l3og"
  >
    <div
      aria-colcount="11"
      aria-multiselectable="false"
      aria-rowcount="2"
      class="MuiDataGrid-root MuiDataGrid-root--densityStandard MuiDataGrid-withBorderColor css-1nbs6ka-MuiDataGrid-root"
      role="grid"
    >
      <div
        class="MuiDataGrid-main css-204u17-MuiDataGrid-main"
      >
        <div
          class="MuiDataGrid-columnHeaders MuiDataGrid-withBorderColor css-1iyq7zh-MuiDataGrid-columnHeaders"
          role="presentation"
          style="min-height: 34px; max-height: 34px; line-height: 34px;"
        >
          <div
            class="MuiDataGrid-columnHeadersInner MuiDataGrid-columnHeadersInner--scrollable css-gl260s-MuiDataGrid-columnHeadersInner"
            role="rowgroup"
            style="transform: translate3d(0px, 0px, 0px);"
          >
            <div
              aria-rowindex="1"
              class="css-yrdy0g-MuiDataGrid-columnHeaderRow"
              role="row"
            >
              <div
                aria-colindex="1"
                aria-label="ID"
                aria-sort="ascending"
                class="MuiDataGrid-columnHeader MuiDataGrid-columnHeader--sortable MuiDataGrid-columnHeader--sorted MuiDataGrid-withBorderColor"
                data-field="id"
                role="columnheader"
                style="height: 34px; width: 0px; min-width: 0; max-width: 0;"
                tabindex="0"
              >
                <div
                  class="MuiDataGrid-columnHeaderDraggableContainer"
                  draggable="false"
                  role="presentation"
                >
                  <div
                    class="MuiDataGrid-columnHeaderTitleContainer"
                    role="presentation"
                  >
                    <div
                      class="MuiDataGrid-columnHeaderTitleContainerContent"
                    >
                      <div
                        aria-label=""
                        class="MuiDataGrid-columnHeaderTitle css-t89xny-MuiDataGrid-columnHeaderTitle"
                        data-mui-internal-clone-element="true"
                      >
                        ID
                      </div>
                    </div>
                    <div
                      class="MuiDataGrid-iconButtonContainer css-ltf0zy-MuiDataGrid-iconButtonContainer"
                    >
                      <button
                        aria-label="Sort"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-qp0zsy-MuiButtonBase-root-MuiIconButton-root"
                        tabindex="-1"
                        title="Sort"
                        type="button"
                      >
                        <span>
                          <svg
                            aria-hidden="true"
                            class="svg-inline--fa fa-sort fa-1x css-16gf28f"
                            data-icon="sort"
                            data-prefix="fad"
                            focusable="false"
                            role="img"
                            viewBox="0 0 320 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              class="fa-duotone-group"
                            >
                              <path
                                class="fa-secondary"
                                d="M0 320c0 8.3 3.3 16.5 9.4 22.6l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 288c-12.9 0-24.6 7.8-29.6 19.8C.8 311.7 0 315.9 0 320z"
                                fill="currentColor"
                              />
                              <path
                                class="fa-primary"
                                d="M182.6 41.4c-12.5-12.5-32.8-12.5-45.3 0l-128 128c-9.2 9.2-11.9 22.9-6.9 34.9s16.6 19.8 29.6 19.8l256 0c12.9 0 24.6-7.8 29.6-19.8s2.2-25.7-6.9-34.9l-128-128z"
                                fill="currentColor"
                              />
                            </g>
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
                <div
                  class="MuiDataGrid-columnSeparator MuiDataGrid-columnSeparator--resizable MuiDataGrid-columnSeparator--sideRight"
                  style="min-height: 34px; opacity: 1;"
                >
                  <span>
                    <svg
                      aria-hidden="true"
                      class="svg-inline--fa fa-horizontal-rule fa-2x css-1o76ref"
                      data-icon="horizontal-rule"
                      data-prefix="fal"
                      focusable="false"
                      role="img"
                      style="transform-origin: 0.625em 0.5em;"
                      viewBox="0 0 640 512"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(320 256)"
                      >
                        <g
                          transform="translate(0, 0)  scale(1, 1)  rotate(90 0 0)"
                        >
                          <path
                            d="M0 256c0-8.8 7.2-16 16-16l608 0c8.8 0 16 7.2 16 16s-7.2 16-16 16L16 272c-8.8 0-16-7.2-16-16z"
                            fill="currentColor"
                            transform="translate(-320 -256)"
                          />
                        </g>
                      </g>
                    </svg>
                  </span>
                </div>
              </div>
              <div
                aria-colindex="2"
                aria-label="Name"
                aria-sort="none"
                class="MuiDataGrid-columnHeader MuiDataGrid-columnHeader--sortable MuiDataGrid-withBorderColor"
                data-field="name"
                role="columnheader"
                style="height: 34px; width: 0px; min-width: 0; max-width: 0;"
                tabindex="-1"
              >
                <div
                  class="MuiDataGrid-columnHeaderDraggableContainer"
                  draggable="false"
                  role="presentation"
                >
                  <div
                    class="MuiDataGrid-columnHeaderTitleContainer"
                    role="presentation"
                  >
                    <div
                      class="MuiDataGrid-columnHeaderTitleContainerContent"
                    >
                      <div
                        aria-label=""
                        class="MuiDataGrid-columnHeaderTitle css-t89xny-MuiDataGrid-columnHeaderTitle"
                        data-mui-internal-clone-element="true"
                      >
                        Name
                      </div>
                    </div>
                    <div
                      class="MuiDataGrid-iconButtonContainer css-ltf0zy-MuiDataGrid-iconButtonContainer"
                    >
                      <button
                        aria-label="Sort"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-qp0zsy-MuiButtonBase-root-MuiIconButton-root"
                        tabindex="-1"
                        title="Sort"
                        type="button"
                      >
                        <span>
                          <svg
                            aria-hidden="true"
                            class="svg-inline--fa fa-sort css-vcnw4h"
                            data-icon="sort"
                            data-prefix="fas"
                            focusable="false"
                            role="img"
                            viewBox="0 0 320 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8L32 224c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8l256 0c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z"
                              fill="currentColor"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
                <div
                  class="MuiDataGrid-columnSeparator MuiDataGrid-columnSeparator--resizable MuiDataGrid-columnSeparator--sideRight"
                  style="min-height: 34px; opacity: 1;"
                >
                  <span>
                    <svg
                      aria-hidden="true"
                      class="svg-inline--fa fa-horizontal-rule fa-2x css-1o76ref"
                      data-icon="horizontal-rule"
                      data-prefix="fal"
                      focusable="false"
                      role="img"
                      style="transform-origin: 0.625em 0.5em;"
                      viewBox="0 0 640 512"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(320 256)"
                      >
                        <g
                          transform="translate(0, 0)  scale(1, 1)  rotate(90 0 0)"
                        >
                          <path
                            d="M0 256c0-8.8 7.2-16 16-16l608 0c8.8 0 16 7.2 16 16s-7.2 16-16 16L16 272c-8.8 0-16-7.2-16-16z"
                            fill="currentColor"
                            transform="translate(-320 -256)"
                          />
                        </g>
                      </g>
                    </svg>
                  </span>
                </div>
              </div>
              <div
                aria-colindex="3"
                aria-label="Description"
                aria-sort="none"
                class="MuiDataGrid-columnHeader MuiDataGrid-columnHeader--sortable MuiDataGrid-withBorderColor"
                data-field="description"
                role="columnheader"
                style="height: 34px; width: 0px; min-width: 0; max-width: 0;"
                tabindex="-1"
              >
                <div
                  class="MuiDataGrid-columnHeaderDraggableContainer"
                  draggable="false"
                  role="presentation"
                >
                  <div
                    class="MuiDataGrid-columnHeaderTitleContainer"
                    role="presentation"
                  >
                    <div
                      class="MuiDataGrid-columnHeaderTitleContainerContent"
                    >
                      <div
                        aria-label=""
                        class="MuiDataGrid-columnHeaderTitle css-t89xny-MuiDataGrid-columnHeaderTitle"
                        data-mui-internal-clone-element="true"
                      >
                        Description
                      </div>
                    </div>
                    <div
                      class="MuiDataGrid-iconButtonContainer css-ltf0zy-MuiDataGrid-iconButtonContainer"
                    >
                      <button
                        aria-label="Sort"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-qp0zsy-MuiButtonBase-root-MuiIconButton-root"
                        tabindex="-1"
                        title="Sort"
                        type="button"
                      >
                        <span>
                          <svg
                            aria-hidden="true"
                            class="svg-inline--fa fa-sort css-vcnw4h"
                            data-icon="sort"
                            data-prefix="fas"
                            focusable="false"
                            role="img"
                            viewBox="0 0 320 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8L32 224c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8l256 0c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z"
                              fill="currentColor"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
                <div
                  class="MuiDataGrid-columnSeparator MuiDataGrid-columnSeparator--resizable MuiDataGrid-columnSeparator--sideRight"
                  style="min-height: 34px; opacity: 1;"
                >
                  <span>
                    <svg
                      aria-hidden="true"
                      class="svg-inline--fa fa-horizontal-rule fa-2x css-1o76ref"
                      data-icon="horizontal-rule"
                      data-prefix="fal"
                      focusable="false"
                      role="img"
                      style="transform-origin: 0.625em 0.5em;"
                      viewBox="0 0 640 512"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(320 256)"
                      >
                        <g
                          transform="translate(0, 0)  scale(1, 1)  rotate(90 0 0)"
                        >
                          <path
                            d="M0 256c0-8.8 7.2-16 16-16l608 0c8.8 0 16 7.2 16 16s-7.2 16-16 16L16 272c-8.8 0-16-7.2-16-16z"
                            fill="currentColor"
                            transform="translate(-320 -256)"
                          />
                        </g>
                      </g>
                    </svg>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="MuiDataGrid-virtualScroller css-qvtrhg-MuiDataGrid-virtualScroller"
          role="presentation"
          style="overflow-x: hidden;"
          tabindex="-1"
        >
          <div
            class="MuiDataGrid-virtualScrollerContent MuiDataGrid-virtualScrollerContent--overflowed css-1kwdphh-MuiDataGrid-virtualScrollerContent"
            role="presentation"
            style="width: auto; height: 34px; min-height: auto;"
          >
            <div
              class="MuiDataGrid-virtualScrollerRenderZone css-s1v7zr-MuiDataGrid-virtualScrollerRenderZone"
              role="rowgroup"
              style="transform: translate3d(0px, 0px, 0px);"
            >
              <div
                aria-rowindex="2"
                aria-selected="false"
                class="MuiDataGrid-row MuiDataGrid-row--lastVisible css-1zkxhd"
                data-id="100000000000000000000"
                data-rowindex="0"
                role="row"
                style="max-height: 34px; min-height: 34px;"
              >
                <div
                  aria-colindex="1"
                  aria-colspan="1"
                  class="MuiDataGrid-cell--withRenderer MuiDataGrid-cell MuiDataGrid-cell--textLeft MuiDataGrid-withBorderColor"
                  data-colindex="0"
                  data-field="id"
                  role="cell"
                  style="min-width: 0; max-width: 0; min-height: 34px; max-height: 34px;"
                  tabindex="-1"
                >
                  <span
                    class="css-2wjxxz"
                    data-mui-internal-clone-element="true"
                    style="width: 100%;"
                  >
                    <div
                      class="css-1o4wo1x"
                    >
                      100000000000000000000
                    </div>
                  </span>
                </div>
                <div
                  aria-colindex="2"
                  aria-colspan="1"
                  class="MuiDataGrid-cell--withRenderer MuiDataGrid-cell MuiDataGrid-cell--textLeft MuiDataGrid-withBorderColor"
                  data-colindex="1"
                  data-field="name"
                  role="cell"
                  style="min-width: 0; max-width: 0; min-height: 34px; max-height: 34px;"
                  tabindex="-1"
                >
                  <span
                    aria-label="Some text name"
                    class="css-2wjxxz"
                    data-mui-internal-clone-element="true"
                    style="width: 100%;"
                  >
                    <div
                      class="css-1o4wo1x"
                    >
                      Some text name
                    </div>
                  </span>
                </div>
                <div
                  aria-colindex="3"
                  aria-colspan="1"
                  class="MuiDataGrid-cell--withRenderer MuiDataGrid-cell MuiDataGrid-cell--textLeft MuiDataGrid-withBorderColor"
                  data-colindex="2"
                  data-field="description"
                  role="cell"
                  style="min-width: 0; max-width: 0; min-height: 34px; max-height: 34px;"
                  tabindex="-1"
                >
                  <span
                    aria-label="some text description"
                    class="css-2wjxxz"
                    data-mui-internal-clone-element="true"
                    style="width: 100%;"
                  >
                    <div
                      class="css-1o4wo1x"
                    >
                      some text description
                    </div>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiDataGrid-footerContainer MuiDataGrid-withBorderColor css-wop1k0-MuiDataGrid-footerContainer"
      >
        <div />
        <div
          class="MuiTablePagination-root MuiToolbar-root MuiToolbar-gutters MuiTablePagination-toolbar css-26z059"
        >
          <p
            class="MuiTypography-root MuiTypography-body1 TablePagination-listing css-w5l0qx-MuiTypography-root"
          >
            Listing 1-1 items of 1 items in total
          </p>
          <div
            class="TablePagination-controls"
          >
            <div
              class="TablePagination-buttons-wrapper"
            >
              <button
                aria-label="first page"
                class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-sizeMedium css-l4xoc0-MuiButtonBase-root-MuiIconButton-root"
                data-testid="tablePagination-first"
                disabled=""
                tabindex="-1"
                type="button"
              >
                <svg
                  data-icon="chevron-dbl-left"
                  fill="currentColor"
                  height="18px"
                  viewBox="0 0 24 24"
                  width="18px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="m7.137 11.775 5.424-5.424-1.963-1.963-7.386 7.386 7.39 7.39 1.962-1.963z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                  <path
                    clip-rule="evenodd"
                    d="m13.93 11.775 5.424-5.424-1.963-1.963-7.386 7.386 7.39 7.39 1.961-1.963z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </button>
              <button
                aria-label="previous page"
                class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-sizeMedium css-l4xoc0-MuiButtonBase-root-MuiIconButton-root"
                data-testid="tablePagination-previous"
                disabled=""
                tabindex="-1"
                type="button"
              >
                <svg
                  data-icon="chevron-left"
                  fill="currentColor"
                  height="18px"
                  viewBox="0 0 24 24"
                  width="18px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="m10.412 12.162 5.424-5.424-1.963-1.963-7.386 7.386 7.39 7.39 1.961-1.963z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </button>
            </div>
            <p
              class="MuiTypography-root MuiTypography-body1 TablePagination-page-progress css-w5l0qx-MuiTypography-root"
            >
              Page 1 of 1
            </p>
            <div
              class="TablePagination-buttons-wrapper"
            >
              <button
                aria-label="next page"
                class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-sizeMedium css-l4xoc0-MuiButtonBase-root-MuiIconButton-root"
                data-testid="tablePagination-next"
                disabled=""
                tabindex="-1"
                type="button"
              >
                <svg
                  data-icon="chevron-right"
                  fill="currentColor"
                  height="18px"
                  viewBox="0 0 24 24"
                  width="18px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M13.914 12.164 8.49 17.587l1.962 1.963 7.386-7.386-7.389-7.389-1.962 1.962z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </button>
              <button
                aria-label="last page"
                class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-sizeMedium css-l4xoc0-MuiButtonBase-root-MuiIconButton-root"
                data-testid="tablePagination-last"
                disabled=""
                tabindex="-1"
                type="button"
              >
                <svg
                  data-icon="chevron-dbl-right"
                  fill="currentColor"
                  height="18px"
                  viewBox="0 0 24 24"
                  width="18px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M16.871 11.777 11.448 17.2l1.962 1.962 7.386-7.386-7.39-7.389-1.961 1.962z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                  <path
                    clip-rule="evenodd"
                    d="M10.079 11.777 4.655 17.2l1.963 1.962 7.386-7.386-7.39-7.389L4.652 6.35z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
