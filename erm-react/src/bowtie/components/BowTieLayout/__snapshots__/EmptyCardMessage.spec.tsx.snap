// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<EmptyCardMessage /> was rendered 1`] = `
<div>
  <div
    style="text-align: center; white-space: pre-wrap; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; flex-direction: column;"
  >
    <h6
      class="MuiTypography-root MuiTypography-h6 css-1bz6q4p-MuiTypography-root"
    >
      Create new files and keep them private in the Drafts folder.
Only you have access to this folder.
    </h6>
    <button
      aria-label="create-new-bowtie-button"
      class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge Mu<PERSON>utton-colorPrimary MuiButton-disableElevation css-18wx9yk-MuiButtonBase-root-MuiButton-root"
      data-testid="button-New Bow Tie"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
      >
        <svg
          data-icon="add"
          fill="currentColor"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.5 10.75h15v2.5h-15z"
            fill="currentColor"
          />
          <path
            d="M10.75 19.5v-15h2.5v15z"
            fill="currentColor"
          />
        </svg>
      </span>
      <span
        class="css-qv0y8m"
      >
        New Bow Tie
      </span>
    </button>
    <h6
      class="MuiTypography-root MuiTypography-h6 css-1bz6q4p-MuiTypography-root"
    >
      Publish files to share them.
    </h6>
  </div>
</div>
`;
