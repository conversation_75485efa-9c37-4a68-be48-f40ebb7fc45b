// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieCardContent /> was rendered 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-i9tl9u-MuiGrid-root"
  >
    <div
      class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 MuiGrid-grid-md-6 MuiGrid-grid-lg-4 css-1yxg8da-MuiGrid-root"
    >
      <div
        class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiCard-root css-1770fvb-MuiPaper-root-MuiCard-root"
        data-testid="bowtie-card-100000000000000000000"
        style="height: 130px; border-bottom: 3px solid #1B4AD5;"
      >
        <div
          class="MuiCardHeader-root css-ohs746-MuiCardHeader-root"
        >
          <div
            class="MuiCardHeader-avatar css-f023cb-MuiCardHeader-avatar"
          >
            <div
              class="MuiAvatar-root MuiAvatar-circular MuiAvatar-colorDefault css-7oxtp7-MuiAvatar-root"
              style="width: 40px; height: 40px;"
            >
              <svg
                aria-hidden="true"
                class="svg-inline--fa fa-facBowTie fa-lg "
                data-icon="facBowTie"
                data-prefix="fac"
                focusable="false"
                role="img"
                style="color: rgb(27, 74, 213);"
                viewBox="0 0 22 22"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1.49662 15.4216V6.57831L7.95228 8.91004L8.46065 7.50224L1.00247 4.80832C0.773232 4.72526 0.517822 4.75942 0.318273 4.89963C0.118724 5.03984 0 5.26827 0 5.5122V16.4874C0 16.7313 0.118724 16.9599 0.318273 17.0998C0.445978 17.1893 0.596413 17.2357 0.74831 17.2357C0.833861 17.2357 0.919929 17.2212 1.00247 17.1913L8.4606 14.4974L7.95223 13.0898L1.49662 15.4216Z M21.6814 4.89994C21.4818 4.75999 21.2271 4.72583 20.9972 4.80863L13.5391 7.50255L14.0476 8.91035L20.5033 6.57862V15.4219L14.0476 13.0899L13.5391 14.4975L20.9972 17.1914C21.08 17.2213 21.166 17.236 21.2516 17.236C21.4035 17.236 21.5539 17.1899 21.6814 17.1001C21.8807 16.9601 21.9996 16.7317 21.9996 16.4877V5.51255C21.9996 5.26862 21.8809 5.04015 21.6814 4.89994Z M13.8191 7.45801H8.20681C7.79348 7.45801 7.4585 7.793 7.4585 8.20632V13.8186C7.4585 14.2319 7.79348 14.5669 8.20681 14.5669H13.8191C14.2327 14.5669 14.5674 14.2319 14.5674 13.8186V8.20632C14.5674 7.793 14.2324 7.45801 13.8191 7.45801ZM13.0708 13.0703H8.95512V8.95463H13.0708V13.0703Z"
                  fill="currentColor"
                />
              </svg>
            </div>
          </div>
          <div
            class="MuiCardHeader-content css-1qbkelo-MuiCardHeader-content"
          >
            <p
              class="MuiTypography-root MuiTypography-body2 css-13oqk3r-MuiTypography-root"
            >
              Some text name
            </p>
          </div>
          <div
            class="MuiCardHeader-action css-sgoict-MuiCardHeader-action"
          >
            <div>
              <div>
                <button
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-1ie4cnm-MuiButtonBase-root-MuiIconButton-root"
                  data-testid="ui-CardHeaderWithAction-IconButton"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-chevron-down "
                    data-icon="chevron-down"
                    data-prefix="fas"
                    focusable="false"
                    role="img"
                    viewBox="0 0 512 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="MuiCardContent-root css-q0dawu-MuiCardContent-root"
        >
          <p
            class="MuiTypography-root MuiTypography-body2 css-r4zjqu-MuiTypography-root"
          >
            Draft
          </p>
          <div
            style="display: flex; column-gap: 5px;"
          >
            <span
              class="MuiTypography-root MuiTypography-subtitle3 css-wof7js-MuiTypography-root"
            >
              Last modified by Some text, 
            </span>
            <span
              class="MuiTypography-root MuiTypography-subtitle3 css-1hs2c0c-MuiTypography-root"
            >
              05/07/2023
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
