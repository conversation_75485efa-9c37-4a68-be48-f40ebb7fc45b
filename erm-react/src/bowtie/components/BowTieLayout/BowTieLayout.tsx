import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import <PERSON><PERSON>yField from 'common/components/SearchByField';
import SideMenu from 'common/components/SideMenu';
import TagFilter from 'common/components/TagFilter';
import { AlertType, ApplicationName, DATA_SYSTEM_COLUMN, LayoutOption, PermissionCodes, TagContext } from 'common/types';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import { BowTieDiagram, BowTieStatus } from 'bowtie/types';
import { generatePath, useLocation, useNavigate, useParams } from 'react-router';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import BowTieDetails from 'bowtie/components/BowTieDetails';
import BowTieCreateDialog from 'bowtie/components/BowTieCreateDialog';

import { BowTieColDef, sideMenuItems } from './BowTieDefinitions';
import BusinessUnitSelector from 'common/components/BusinessUnitSelector';
import BowTieCopyDialog from '../BowTieCopyDialog';
import { IdWithName } from 'app/types';
import { strings } from 'common/utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimesCircle, faTrashAlt, faTrashRestoreAlt } from '@fortawesome/pro-solid-svg-icons';
import { faLockAlt, faLockOpen, faTrashCan, faUpload } from '@fortawesome/pro-regular-svg-icons';
import { getSearchFields } from 'common/utils/definitions';
import Typography from '@mui/material/Typography';
import useTheme from '@mui/system/useTheme';
import TableContent, { BowTieTableContentRef } from './BowTieTableContent';
import BowTieCardContent, { BowTieCardContentRef } from './BowTieCardContent';
import { LayoutButton } from 'app/components/buttons/LayoutButton';
import useSnackbar from 'common/hooks/useSnackbar';
import { BOWTIE_TEST_ID_PREFIX } from 'bowtie/constants';
import BowTieLinksDialog from '../BowTieLinks';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { useDispatch } from 'store';
import { useSelector } from 'react-redux';

import { getLayoutOption, getRequestParams, getSearchProperty, getSearchValue } from 'app/selectors';
import { setLayoutOption, setRequestParams, setSearchProperty, setSearchValue } from 'app/reducer';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import { TagFilterRef } from 'common/components/TagFilter/TagFilter';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import {
    useBtrsChangeDiagramLockStatusUsingPutMutation,
    useBtrsCreateDiagramUsingPostMutation,
    useBtrsDeleteDiagramUsingDeleteMutation,
    useBtrsGetDefinitionUsingGetQuery,
    useBtrsPublishDiagramUsingPutMutation,
    useBtrsPurgeDiagramUsingDeleteMutation,
    useBtrsRestoreDiagramUsingPutMutation,
    useBtrsUnpublishDiagramUsingPutMutation,
    useBtrsVerifyUserBUsUsingPutMutation,
    useLazyBtrsGetDiagramUsingGetQuery,
} from 'bowtie/rtkApi';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import DialogActions from '@mui/material/DialogActions';
import { BowTieDiagramRest, IdWithNameAndStatusRest } from 'api/generated/types';
import { getBowTieStatus } from 'bowtie/utils';
import { hasPermission } from 'common/components/ProtectedContent/ProtectedContent';
import { usersApi } from 'user/rtkApi';
import { BowtiePath } from 'bowtie/routes';
import BowTieImportDialog from '../BowTieImport';
import Box from '@mui/material/Box';
import { faSlidersH } from '@fortawesome/pro-light-svg-icons';
import { Add, Delete, Duplicate, Export, Link, Publish, RegisterEntry } from '@protecht/ui-library/library/components/SVGIcons';
import BowTieExportDialog from '../BowTieExport';
import SearchByFieldToolbarGroup from 'common/components/SearchByField/SearchByFieldToolbarGroup';
import { TagFilterParams } from '@protecht/ui-library/library/types';

export enum CurrentDialog {
    DETAILS = 'details',
    CREATE = 'create',
    COPY = 'copy',
    BU_SELECTOR = 'buSelector',
    LINKS = 'links',
    PUBLISH_OUTSIDE_BU = 'publishOutsideBu',
    IMPORT = 'import',
    EXPORT = 'export',
    SAVEALERT = 'saveAlert',
    BULK_UPDATE = 'bulkUpdate',
}

const BowTieLayout: React.FC = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const location = useLocation();

    const dispatch = useDispatch();
    const requestParams = useSelector(getRequestParams);

    const searchValue = useSelector(getSearchValue);
    const searchProperty = useSelector(getSearchProperty);

    const layoutOption = useSelector(getLayoutOption);

    const { enqueueSuccess, enqueueError } = useSnackbar();
    const cardContentRef = useRef<BowTieCardContentRef | undefined>();
    const tableContentRef = useRef<BowTieTableContentRef | undefined>();
    const tagFilterRef = useRef<TagFilterRef | undefined>();
    const [getBowtieDetailData] = useLazyBtrsGetDiagramUsingGetQuery();

    const { status } = useParams<{
        status: string;
    }>();

    const { showConfirmationAlert } = useConfirmationAlert();

    const SEARCH_FIELDS = getSearchFields(BowTieColDef);

    const [currentBowTieData, setCurrentBowTieData] = useState<BowTieDiagramRest>();
    const [visibleDialog, setVisibleDialog] = useState<CurrentDialog>();
    const [selectedBus, setSelectedBus] = useState<IdWithName[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [diagramData, setDiagramData] = useState<BowTieDiagramRest>();
    const [diagramDataForExport, setDiagramDataForExport] = useState<BowTieDiagramRest>();

    const [verifyUserBUs] = useBtrsVerifyUserBUsUsingPutMutation();
    const [publishDiagram] = useBtrsPublishDiagramUsingPutMutation();
    const [unPublishDiagram] = useBtrsUnpublishDiagramUsingPutMutation();
    const [deleteDiagram] = useBtrsDeleteDiagramUsingDeleteMutation();
    const [lockDiagram] = useBtrsChangeDiagramLockStatusUsingPutMutation();
    const [restoreDiagram] = useBtrsRestoreDiagramUsingPutMutation();
    const [purgeDiagram] = useBtrsPurgeDiagramUsingDeleteMutation();
    const { data: definition, isLoading: isLoadingDef } = useBtrsGetDefinitionUsingGetQuery();
    const [createDiagram] = useBtrsCreateDiagramUsingPostMutation();

    const [statusFilter, setStatusFilter] = useState<BowTieStatus | null>(() => getBowTieStatus(status));
    const { data: userPermissions } = useSelector(usersApi.endpoints.pursGetUserPermissionsUsingGet.select({}));

    const onDetailsClicked = (event, data: BowTieDiagramRest) => {
        setCurrentBowTieData(data);
        setVisibleDialog(CurrentDialog.DETAILS);
    };

    const onCreateNewClicked = () => {
        setVisibleDialog(CurrentDialog.CREATE);
    };

    const onLinksClicked = (event, data: BowTieDiagramRest) => {
        setCurrentBowTieData(data);
        setVisibleDialog(CurrentDialog.LINKS);
    };

    const onExportClicked = async (event, data: BowTieDiagramRest) => {
        try {
            if (data.id) {
                const dataForExport = await getBowtieDetailData({ id: data.id, withDiagram: true }).unwrap();
                setDiagramDataForExport(dataForExport);
            }
        } catch (error) {
            enqueueError(strings('bowtie:card.message.loadDataFailed'), error);
        }

        setVisibleDialog(CurrentDialog.EXPORT);
    };

    useEffect(() => {
        const bowTieStatus = getBowTieStatus(status);
        setStatusFilter(bowTieStatus);

        if (!bowTieStatus) {
            dispatch(setLayoutOption(LayoutOption.CARD));
        }
    }, [dispatch, status]);

    const getErrorAlert = useCallback(
        (message: string) => {
            return {
                icon: (
                    <FontAwesomeIcon
                        icon={faTimesCircle}
                        color={theme.palette.error.main}
                    />
                ),
                title: strings('ermConstants:dlg_title_error'),
                contentText: message,
            };
        },
        [theme.palette.error.main],
    );

    const onCopyClicked = (event, data: BowTieDiagramRest) => {
        setCurrentBowTieData(data);
        setVisibleDialog(CurrentDialog.COPY);
    };

    const onPublishClicked = (event, data: BowTieDiagramRest) => {
        setSelectedBus([]);

        showConfirmationAlert({
            onConfirm: () => {
                setCurrentBowTieData(data);
                setVisibleDialog(CurrentDialog.BU_SELECTOR);
                refreshContent();
            },
            onClose: () => {
                setSelectedBus([]);
            },
            icon: <FontAwesomeIcon icon={faUpload} />,
            title: strings('bowtie:title.publishToBusinessUnit'),
            contentText: strings('bowtie:message.publishBusinessUnit'),
            cancelButtonLabel: strings('ermMessages:btn_cancel'),
            confirmButtonLabel: strings('bowtie:button.publish'),
        });
    };

    const onUnpublishClicked = useCallback(
        (_event, data: BowTieDiagram) => {
            showConfirmationAlert({
                onConfirm: async () => {
                    try {
                        await unPublishDiagram({ id: data.id });
                        enqueueSuccess(strings('bowtie:file.message.successfullyUnpublished'));
                        refreshContent();
                    } catch (err) {
                        showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.deleteFileError')));
                    }
                },
                icon: <FontAwesomeIcon icon={faUpload} />,
                title: strings('bowtie:title.unpublish'),
                contentText: strings('bowtie:message.unpublishBusinessUnit'),
                cancelButtonLabel: strings('ermMessages:btn_cancel'),
                confirmButtonLabel: strings('bowtie:button.unpublish'),
            });
        },
        [enqueueSuccess, getErrorAlert, showConfirmationAlert, unPublishDiagram],
    );

    const onDeleteClicked = useCallback(
        (_event, data: BowTieDiagram) => {
            showConfirmationAlert({
                onConfirm: async () => {
                    try {
                        await deleteDiagram({ id: data.id });
                        enqueueSuccess(strings('bowtie:file.message.successfullyDeleted'));
                        refreshContent();
                    } catch (err) {
                        showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.deleteFileError')));
                    }
                },
                cancelButtonLabel: strings('ermMessages:btn_cancel'),
                confirmButtonLabel: strings('ermMessages:btn_delete'),
                type: AlertType.Warning,
                title: strings('bowtie:file.title.deleteBowTieFile', { name: data.name }),
                contentText: strings('bowtie:file.message.confirmDeleteQuestion', { name: data.name }),
            });
        },
        [showConfirmationAlert, deleteDiagram, enqueueSuccess, getErrorAlert],
    );

    const onLockClicked = useCallback(
        (_event, data: BowTieDiagram) => {
            showConfirmationAlert({
                onConfirm: async () => {
                    try {
                        await lockDiagram({
                            id: data.id,
                            lock: true,
                        });
                        enqueueSuccess(strings('bowtie:file.message.successfullyLocked'));
                        refreshContent();
                    } catch (err) {
                        showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.lockFileError')));
                    }
                },
                cancelButtonLabel: strings('ermMessages:btn_cancel'),
                confirmButtonLabel: strings('ermMessages:btn_lock'),
                type: AlertType.Warning,
                title: strings('bowtie:file.title.lockBowTieFile', { name: data.name }),
                contentText: strings('bowtie:file.message.confirmLockQuestion', { name: data.name }),
            });
        },
        [showConfirmationAlert, lockDiagram, enqueueSuccess, getErrorAlert],
    );

    const onUnlockClicked = useCallback(
        (_event, data: BowTieDiagram) => {
            showConfirmationAlert({
                onConfirm: async () => {
                    try {
                        await lockDiagram({
                            id: data.id,
                            lock: false,
                        });
                        enqueueSuccess(strings('bowtie:file.message.successfullyUnlocked'));
                        refreshContent();
                    } catch (err) {
                        showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.unlockFileError')));
                    }
                },
                cancelButtonLabel: strings('ermMessages:btn_cancel'),
                confirmButtonLabel: strings('ermMessages:btn_unlock'),
                type: AlertType.Warning,
                title: strings('bowtie:file.title.unlockBowTieFile', { name: data.name }),
                contentText: strings('bowtie:file.message.confirmUnlockQuestion', { name: data.name }),
            });
        },
        [showConfirmationAlert, deleteDiagram, enqueueSuccess, getErrorAlert],
    );

    const onRestoreClicked = useCallback(
        (_event, data: BowTieDiagram) => {
            showConfirmationAlert({
                onConfirm: async () => {
                    try {
                        await restoreDiagram({ id: data.id });
                        enqueueSuccess(strings('bowtie:file.message.successfullyRestored'));
                        refreshContent();
                    } catch (err) {
                        showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.restoreFileError')));
                    }
                },
                cancelButtonLabel: strings('ermMessages:btn_cancel'),
                confirmButtonLabel: strings('ermMessages:btn_restore'),
                type: AlertType.Warning,
                title: strings('bowtie:file.title.restoreBowTieFile', { name: data.name }),
                contentText:
                    data.status === BowTieStatus.Published
                        ? strings('bowtie:file.message.confirmRestoreQuestionPublished', {
                              name: data.name,
                              bus: data.businessUnits?.map((bu) => bu.name).join(', '),
                          })
                        : strings('bowtie:file.message.confirmRestoreQuestionDraft', { name: data.name, username: data.createdBy }),
            });
        },
        [showConfirmationAlert, restoreDiagram, enqueueSuccess, getErrorAlert],
    );

    const onPurgeClicked = useCallback(
        (_event, data: BowTieDiagram) => {
            showConfirmationAlert({
                onConfirm: async () => {
                    try {
                        await purgeDiagram({ id: data.id });
                        enqueueSuccess(strings('bowtie:file.message.successfullyPurged'));
                        refreshContent();
                    } catch (err) {
                        showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.purgeFileError')));
                    }
                },
                cancelButtonLabel: strings('ermMessages:btn_cancel'),
                confirmButtonLabel: strings('common:button.purge'),
                type: AlertType.Warning,
                title: strings('bowtie:file.title.purgeBowTieFile', { name: data.name }),
                contentText: strings('bowtie:file.message.confirmPurgeQuestion', { name: data.name }),
            });
        },
        [showConfirmationAlert, purgeDiagram, enqueueSuccess, getErrorAlert],
    );

    const refreshContent = () => {
        if (cardContentRef.current) {
            cardContentRef.current.refresh();
        } else if (tableContentRef.current) {
            tableContentRef.current.refresh();
        }
    };

    const refreshContentWithTags = () => {
        refreshContent();
        if (tagFilterRef.current) {
            tagFilterRef.current.refresh();
        }
    };

    const hasRestorePermission = useMemo(() => {
        if (userPermissions) {
            return hasPermission(
                [
                    {
                        code: PermissionCodes.BOWTIE_RESTORE,
                        applicationName: ApplicationName.RISK_ANALYSIS,
                    },
                ],
                userPermissions,
            );
        }
    }, [userPermissions]);

    const hasLockPermission = useMemo(() => {
        if (userPermissions) {
            return hasPermission(
                [
                    {
                        code: PermissionCodes.BOWTIE_LOCK,
                        applicationName: ApplicationName.RISK_ANALYSIS,
                    },
                ],
                userPermissions,
            );
        }
    }, [userPermissions]);

    const hasPurgePermission = useMemo(() => {
        if (userPermissions) {
            return hasPermission(
                [
                    {
                        code: PermissionCodes.BOWTIE_PURGE,
                        applicationName: ApplicationName.RISK_ANALYSIS,
                    },
                ],
                userPermissions,
            );
        }
    }, [userPermissions]);

    const contextMenuItemsDeleted: ContextMenuItem[] = useMemo(() => {
        const cmi: ContextMenuItem[] = [];

        if (hasRestorePermission) {
            cmi.push({
                icon: <FontAwesomeIcon icon={faTrashRestoreAlt} />,
                label: strings('bowtie:button.restore'),
                action: onRestoreClicked,
            });
        }

        if (hasPurgePermission) {
            cmi.push({
                icon: (
                    <FontAwesomeIcon
                        icon={faTrashAlt}
                        color={theme.palette.error.main}
                    />
                ),
                label: strings('bowtie:button.purge'),
                action: onPurgeClicked,
            });
        }

        return cmi;
    }, [hasRestorePermission, hasPurgePermission, onRestoreClicked, theme.palette.error.main, onPurgeClicked]);

    const basicContextMenuItems: ContextMenuItem[] = useMemo(() => {
        const cmi: ContextMenuItem[] = [];
        cmi.push(
            {
                icon: <RegisterEntry />,
                label: strings('bowtie:button.editDetails'),
                action: onDetailsClicked,
            },
            {
                icon: <Link color={theme.palette.primary.main} />,
                label: strings('common:label.links'),
                action: onLinksClicked,
            },
            { icon: <Publish />, label: strings('bowtie:button.publish'), action: onPublishClicked },
            {
                icon: <FontAwesomeIcon icon={faUpload} />,
                label: strings('bowtie:button.unpublish'),
                action: onUnpublishClicked,
            },
            { icon: <Duplicate />, label: strings('common:button.duplicate'), action: onCopyClicked },
            { divider: true },
            {
                icon: <Export color={theme.palette.primary.main} />,
                label: strings('common:button.export'),
                action: onExportClicked,
            },
            {
                icon: <Delete color={theme.palette.error.main} />,
                label: strings('ermConstants:label_delete'),
                action: onDeleteClicked,
            },
        );
        return cmi;
    }, [onDeleteClicked, onExportClicked, onPublishClicked, onUnpublishClicked, theme.palette.error.main, theme.palette.primary.main]);

    const contextMenuItemsLocked: ContextMenuItem[] = useMemo(() => {
        const cmi: ContextMenuItem[] = [];
        cmi.push(
            {
                icon: <RegisterEntry />,
                label: strings('bowtie:button.editDetails'),
                action: onDetailsClicked,
            },
            {
                icon: <Link color={theme.palette.primary.main} />,
                label: strings('common:label.links'),
                action: onLinksClicked,
            },
            { icon: <Publish />, label: strings('bowtie:button.publish'), action: onPublishClicked },
            {
                icon: <Publish />,
                label: strings('bowtie:button.unpublish'),
                action: onUnpublishClicked,
            },
            { icon: <Duplicate />, label: strings('common:button.duplicate'), action: onCopyClicked },
            { divider: true },
            {
                icon: <Export color={theme.palette.primary.main} />,
                label: strings('common:button.export'),
                action: onExportClicked,
            },
            {
                icon: <Delete color={theme.palette.error.main} />,
                label: strings('ermConstants:label_delete'),
                action: onDeleteClicked,
            },
        );

        if (hasLockPermission) {
            cmi.push(
                {
                    icon: <FontAwesomeIcon icon={faLockAlt} />,
                    label: strings('bowtie:button.lock'),
                    action: onLockClicked,
                },
                {
                    icon: <FontAwesomeIcon icon={faLockOpen} />,
                    label: strings('bowtie:button.unlock'),
                    action: onUnlockClicked,
                },
            );
        }

        return cmi;
    }, [hasLockPermission, onDeleteClicked, onUnpublishClicked, theme.palette.error.main, theme.palette.primary.main]);

    const contextMenuItems: ContextMenuItem[] = useMemo(() => {
        switch (statusFilter) {
            case BowTieStatus.Deleted:
                return contextMenuItemsDeleted;
            case BowTieStatus.Published:
                return contextMenuItemsLocked;
            default:
                return basicContextMenuItems;
        }
    }, [statusFilter, contextMenuItemsLocked, basicContextMenuItems, contextMenuItemsDeleted]);

    /* TODO: Rewrite this function after implementing useInfiniteScrollLoading and once we have only BowTieDiagramRest without BowTieDiagram. */
    const contextMenuFilterCondition = useCallback((item, data) => {
        if ('locked' in data) {
            if (data.locked && (item.label === strings('bowtie:button.lock') || item.label === strings('bowtie:button.editDetails'))) {
                return false;
            } else if (!data.locked && item.label === strings('bowtie:button.unlock')) {
                return false;
            }
        }
        if ('status' in data) {
            if (data.status === BowTieStatus.Published && item.label === strings('bowtie:button.publish')) {
                return false;
            } else if (data.status !== BowTieStatus.Published && item.label === strings('bowtie:button.unpublish')) {
                return false;
            }
        }
        return true;
    }, []);

    const bowtieTableContent = useMemo(() => {
        return (
            <TableContent
                ref={tableContentRef}
                contextMenuItems={contextMenuItems}
                contextMenuFilterCondition={contextMenuFilterCondition}
                status={statusFilter}
            />
        );
    }, [contextMenuItems, contextMenuFilterCondition, statusFilter]);

    const bowtieCardContent = useMemo(() => {
        return (
            <BowTieCardContent
                ref={cardContentRef}
                contextMenuItems={contextMenuItems}
                contextMenuFilterCondition={contextMenuFilterCondition}
                onCreateNewClicked={onCreateNewClicked}
                status={statusFilter}
            />
        );
    }, [contextMenuFilterCondition, contextMenuItems, statusFilter]);

    const getSidePanel = () => {
        return (
            <>
                <SideMenu
                    items={[
                        ...sideMenuItems,
                        ...(hasRestorePermission || hasPurgePermission
                            ? [
                                  {
                                      key: 'key-deleted',
                                      icon: <FontAwesomeIcon icon={faTrashCan} />,
                                      label: strings('bowtie:sideMenu.deleted'),
                                      pathname: generatePath(BowtiePath.OVERVIEW, { status: 'deleted' }),
                                  },
                              ]
                            : []),
                        ...(userPermissions &&
                        hasPermission([{ code: PermissionCodes.RISK_ANALYSIS_MANAGE, applicationName: ApplicationName.RISK_ANALYSIS }], userPermissions)
                            ? [
                                  {
                                      key: 'key-settings',
                                      icon: <FontAwesomeIcon icon={faSlidersH} />,
                                      label: strings('common:label.settings'),
                                      pathname: BowtiePath.SETTINGS,
                                  },
                              ]
                            : []),
                    ]}
                />
                <StyledDivider />
                {statusFilter && (
                    <TagFilter
                        ref={tagFilterRef}
                        tagContext={TagContext.BOWTIE}
                        selectedOperator={requestParams?.tagOperator}
                        selectedTagIds={requestParams?.tagIds}
                        onChange={(tagFilter: TagFilterParams) => {
                            dispatch(setRequestParams({ ...requestParams, ...tagFilter }));
                        }}
                    />
                )}
            </>
        );
    };

    const getToolbar = () => {
        if (!statusFilter) {
            return undefined;
        }

        return (
            <ToolbarContainer>
                <ToolbarGroup
                    flex={1}
                    justifyContent="space-between"
                >
                    <SearchByFieldToolbarGroup>
                        <SearchByField
                            dataTestId={`${BOWTIE_TEST_ID_PREFIX}-field-search`}
                            key="bow-tie-search-by"
                            aria-label="bow-tie-search-by"
                            disabled={!statusFilter}
                            fields={SEARCH_FIELDS.filter((field) => field.key !== DATA_SYSTEM_COLUMN.FIELD_BUSINESS_UNITS)}
                            searchField={searchProperty}
                            searchValue={searchValue}
                            onPropertyChanged={(property: string) => dispatch(setSearchProperty(property))}
                            onValueChanged={(value: string) => dispatch(setSearchValue(value))}
                        />
                    </SearchByFieldToolbarGroup>
                    <LayoutButton
                        key="bowtie-layout-options"
                        value={layoutOption}
                        updateValue={(value) => dispatch(setLayoutOption(value))}
                        disabled={!statusFilter}
                    />
                </ToolbarGroup>
            </ToolbarContainer>
        );
    };

    const checkPublishDiagram = async () => {
        const accessToBUs: boolean = await verifyUserBUs({ body: selectedBus.map((item) => item.id) }).unwrap();
        if (accessToBUs) {
            void confirmPublishDiagram();
        } else {
            setVisibleDialog(CurrentDialog.PUBLISH_OUTSIDE_BU);
        }
    };

    const confirmPublishDiagram = async () => {
        if (!currentBowTieData || currentBowTieData.id === undefined) {
            console.error('Unable to publish diagram: Diagram ID is missing.');
            showConfirmationAlert(getErrorAlert(strings('bowtie:message.publishFileError')));
            setIsLoading(false);
            return;
        }
        try {
            setIsLoading(true);
            await publishDiagram({
                id: currentBowTieData.id,
                body: selectedBus.map(({ id }) => id),
            });
            onDialogClosed();
            setSelectedBus([]);
            enqueueSuccess(strings('bowtie:file.message.successfullyPublished'));
            refreshContent();
        } catch (err) {
            showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.publishFileError')));
        } finally {
            setIsLoading(false);
        }
    };

    const onDialogClosed = () => {
        setSelectedBus([]);
        setVisibleDialog(undefined);
    };

    const onCopyLink = () => {
        enqueueSuccess(strings('bowtie:file.message.linkSuccessfullyCopied'));
    };

    const onImportConfirm = async (data) => {
        try {
            const diagram = await createDiagram({
                bowTieCreateDiagramRest: { name: data.name, diagramModel: data.diagramModel },
            }).unwrap();
            void navigate(generatePath(BowtiePath.DIAGRAM, { id: diagram?.id?.toString() ?? null }), { state: { from: location } });
        } catch (err) {
            enqueueError(`${strings('bowtie:file.message.diagramCreateError')} ${err.response || ''}`);
        }
        setDiagramData({ ...data, id: diagramData?.id, businessUnits: diagramData?.businessUnits });
        setVisibleDialog(undefined);
    };

    const handleSelectBUs = useCallback((currentSelection: IdWithNameAndStatusRest[]) => setSelectedBus(currentSelection as IdWithName[]), []);

    return (
        <>
            <ApplicationLayout>
                <ToolbarContainer
                    disableGutters={false}
                    variant="regular"
                >
                    <ToolbarGroup
                        flex={1}
                        justifyContent="space-between"
                    >
                        <Typography
                            variant="h1"
                            data-testid="bowties-heading"
                            noWrap
                        >
                            {strings('bowtie:file.title.bowTies')}
                        </Typography>
                        <Box sx={{ display: 'flex' }}>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                key="import-new-bowtie-button"
                                aria-label="import-new-bowtie-button"
                                startIcon={<Add />}
                                onClick={() => setVisibleDialog(CurrentDialog.IMPORT)}
                                variant={'secondary'}
                                sx={{ marginRight: '16px', ...ButtonStyles.pageToolbarButton.sx }}
                            >
                                {strings('common:button.import')}
                            </Button>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                key="create-new-bowtie-button"
                                aria-label="create-new-bowtie-button"
                                startIcon={<Add />}
                                onClick={onCreateNewClicked}
                            >
                                {strings('bowtie:button.newBowTie')}
                            </Button>
                        </Box>
                    </ToolbarGroup>
                </ToolbarContainer>
                <MainLayout sidebar={getSidePanel()}>
                    <ContentLayout toolbar={getToolbar()}>{layoutOption === LayoutOption.LIST ? bowtieTableContent : bowtieCardContent}</ContentLayout>
                </MainLayout>
            </ApplicationLayout>
            {currentBowTieData && currentBowTieData.id !== undefined && (
                <BowTieDetails
                    visible={visibleDialog === CurrentDialog.DETAILS}
                    onClose={onDialogClosed}
                    refreshContent={refreshContentWithTags}
                    detailId={currentBowTieData.id}
                    onCopyLink={onCopyLink}
                />
            )}
            <BowTieCreateDialog
                visible={visibleDialog === CurrentDialog.CREATE}
                onClose={onDialogClosed}
            />
            {currentBowTieData && (
                <BowTieCopyDialog
                    visible={visibleDialog === CurrentDialog.COPY}
                    onClose={onDialogClosed}
                    onConfirm={refreshContent}
                    data={currentBowTieData}
                />
            )}
            <Dialog
                title={strings('bowtie:title.selectBusinessUnits')}
                visible={visibleDialog === CurrentDialog.BU_SELECTOR}
                width={700}
                height={674}
                onOutsideClickClose={onDialogClosed}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={onDialogClosed}
                            dataTestId="button-cancel"
                        >
                            {strings('common:button.cancel')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            type="submit"
                            dataTestId="button-confirm"
                            disabled={selectedBus.length === 0 || isLoading}
                            onClick={checkPublishDiagram}
                        >
                            {strings('ermMessages:btn_okay')}
                        </Button>
                    </DialogActions>
                }
            >
                <BusinessUnitSelector
                    multiple
                    selected={selectedBus}
                    onSelect={handleSelectBUs}
                />
            </Dialog>
            {currentBowTieData && currentBowTieData.id !== undefined && visibleDialog === CurrentDialog.LINKS && (
                <BowTieLinksDialog
                    visible={visibleDialog === CurrentDialog.LINKS}
                    onClose={onDialogClosed}
                    diagramId={currentBowTieData.id}
                />
            )}
            <Dialog
                title={strings('bowtie:title.publishToBusinessUnit')}
                visible={visibleDialog === CurrentDialog.PUBLISH_OUTSIDE_BU}
                onOutsideClickClose={() => setVisibleDialog(CurrentDialog.BU_SELECTOR)}
                width={700}
                height={200}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={() => setVisibleDialog(CurrentDialog.BU_SELECTOR)}
                            dataTestId="button-cancel"
                        >
                            {strings('common:button.cancel')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            type="submit"
                            dataTestId="button-confirm"
                            disabled={selectedBus.length === 0 || isLoading}
                            onClick={confirmPublishDiagram}
                        >
                            {strings('ermMessages:btn_okay')}
                        </Button>
                    </DialogActions>
                }
            >
                <Typography variant="body2">{strings('bowtie:message.publishToOtherBu')}</Typography>
            </Dialog>

            {!isLoadingDef && (
                <>
                    <BowTieImportDialog
                        bowtieDefinition={definition}
                        visible={visibleDialog === CurrentDialog.IMPORT}
                        onClose={onDialogClosed}
                        onConfirm={onImportConfirm}
                    />
                    <BowTieExportDialog
                        visible={visibleDialog === CurrentDialog.EXPORT}
                        onClose={onDialogClosed}
                        bowtieDefinition={definition}
                        data={diagramDataForExport as BowTieDiagramRest}
                    />
                </>
            )}
        </>
    );
};

export default BowTieLayout;
