import React from 'react';
import { strings } from 'common/utils/i18n';
import { BowTieStatus } from '../../types';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Button from '@protecht/ui-library/library/components/Button';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

type Props = {
    onCreateNewClicked?: (event: React.MouseEvent<any, MouseEvent>) => void;
    statusFilter: BowTieStatus | null;
};

const EmptyFolder = styled(Typography)({
    whiteSpace: 'pre-wrap',
    textAlign: 'center',
    padding: '20px 0px',
});

const EmptyCardMessage: React.FC<Props> = (props: Props) => {
    const getStatusText = () => {
        switch (props.statusFilter) {
            case BowTieStatus.Published: {
                return strings('bowtie:card.message.createNewFiles');
            }
            case BowTieStatus.Template: {
                return strings('bowtie:card.message.noTemplatesAvailable');
            }
            case BowTieStatus.Deleted: {
                return strings('bowtie:card.message.noDeleted');
            }
            default: {
                return strings('bowtie:card.message.noRecentlyUsedFiles');
            }
        }
    };

    if (props.statusFilter === BowTieStatus.Draft) {
        return (
            <div
                style={{
                    textAlign: 'center',
                    whiteSpace: 'pre-wrap',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'column',
                }}
            >
                <EmptyFolder
                    variant="h6"
                    color="textSecondary"
                >
                    {strings('bowtie:card.message.createNewFiles')}
                </EmptyFolder>
                <Button
                    size="large"
                    key="create-new-bowtie-button"
                    aria-label="create-new-bowtie-button"
                    startIcon={<Add />}
                    onClick={props.onCreateNewClicked}
                >
                    {strings('bowtie:button.newBowTie')}
                </Button>
                <EmptyFolder
                    variant="h6"
                    color="textSecondary"
                >
                    {strings('bowtie:card.message.publishFilesToShare')}
                </EmptyFolder>
            </div>
        );
    }
    return (
        <EmptyFolder
            variant="h6"
            color="textSecondary"
        >
            {getStatusText()}
        </EmptyFolder>
    );
};

export default EmptyCardMessage;
