import React from 'react';
import { render, screen } from 'test/utils';
import BowTieCardContent from './BowTieCardContent';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import { BowTieStatus, BowTieStatusDisplay } from 'bowtie/types';
import { BOWTIE_CARD_TEST_ID_PREFIX } from 'bowtie/constants';
import { mockBowTieEntry, mockBowTieEntryApi } from 'bowtie/api.mock';
import user from '@testing-library/user-event';
import { mockLocation, mockNavigate } from 'test/config/setupAfterEnv';
import { waitForElementToBeRemoved } from '@testing-library/react';
import * as api from '../../api';
import { mockedUserWithDateFormat } from 'user/mocks';
jest.mock('../../api');

const diagramsData = {
    records: [mockBowTieEntryApi],
    totalCount: 1,
};

beforeEach(() => {
    jest.spyOn(api, 'getDiagrams').mockImplementation(() => Promise.resolve(diagramsData));
});

jest.mock('bowtie/rtkApi', () => ({
    ...jest.requireActual('bowtie/rtkApi'),
    useGetDiagramsUsingGetQuery: () => ({
        data: diagramsData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    }),
}));

jest.mock('user/rtkApi', () => {
    const actualRtkApi = jest.requireActual('user/rtkApi');

    return {
        ...actualRtkApi,
        usePursGetCurrentUserUsingGetQuery: jest.fn(() => ({
            data: mockedUserWithDateFormat,
            isLoading: false,
            isSuccess: true,
            isError: false,
        })),
    };
});

describe('<BowTieCardContent />', () => {
    const onCreateNewClickedMock = jest.fn(() => void 0);
    const contextMenuFilterCondition = jest.fn((_item: ContextMenuItem) => true).mockReturnValue(true);
    const contexMenuItems = [{ label: 'test label text', action: () => void 0 }];

    const setup = () => {
        return render(
            <BowTieCardContent
                contextMenuItems={contexMenuItems}
                contextMenuFilterCondition={contextMenuFilterCondition}
                onCreateNewClicked={onCreateNewClickedMock}
                status={BowTieStatus.Draft}
            />,
        );
    };

    it('was rendered', async () => {
        const view = setup();
        await waitForElementToBeRemoved(screen.queryByText('Loading...'));

        expect(screen.getByTestId(`${BOWTIE_CARD_TEST_ID_PREFIX}-${mockBowTieEntry.id}`)).toBeInTheDocument();
        expect(view.container).toMatchSnapshot();
    });

    it('redirects to the correct path when a card is clicked', async () => {
        setup();
        await waitForElementToBeRemoved(screen.queryByText('Loading...'));

        const card = await screen.findByTestId(`${BOWTIE_CARD_TEST_ID_PREFIX}-${mockBowTieEntry.id}`);
        await user.click(card);

        expect(mockNavigate).toHaveBeenCalledWith(expect.stringContaining(mockBowTieEntry.id.toString()), { state: { from: mockLocation } });
    });

    it('renders card with correct data', async () => {
        setup();
        await waitForElementToBeRemoved(screen.queryByText('Loading...'));

        const card = screen.getByTestId(`${BOWTIE_CARD_TEST_ID_PREFIX}-${mockBowTieEntry.id}`);
        expect(card).toHaveTextContent(mockBowTieEntry.name);
        expect(card).toHaveTextContent(BowTieStatusDisplay[mockBowTieEntry.status]);
        expect(card).toHaveTextContent(mockBowTieEntry.createdBy);
        expect(card).toHaveTextContent(mockBowTieEntry.lastModifiedBy);
    });
});
