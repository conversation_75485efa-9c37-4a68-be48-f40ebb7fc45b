import React from 'react';
import { strings } from 'common/utils/i18n';
import { RegisterRest } from 'register/types';
import DialogSelectorField, { DialogSelectorFieldProps } from 'common/components/Form/FormFields/DialogSelectorField';
import { RegisterType } from './types';
import RegisterDialogSelector from './RegisterDialogSelector';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { ViewExpressionRest } from 'api/generated/types';

interface Props extends Omit<DialogSelectorFieldProps, 'renderDialog'> {
    registerType: RegisterType;
    selected: RegisterRest | undefined;
    isLoading: boolean;
    onSelect: (registerType: RegisterType, selected: RegisterRest[], selectorIndex?: number) => void;
    error?: string;
    selectorIndex?: number;
    excludedIds?: number[];
    staticExpressions?: ViewExpressionRest[];
}

const RegisterDialogField: React.FC<Props> = ({
    registerType,
    selected,
    isLoading,
    error,
    selectorIndex,
    onSelect,
    excludedIds,
    staticExpressions,
    ...props
}) => {
    return (
        <>
            <Grid item>
                <DialogSelectorField
                    {...props}
                    selectButtonDataTestId={`button-change-${props.name}`}
                    displayValue={isLoading ? strings('common:message:loading') : selected?.label}
                    disabled={isLoading}
                    renderDialog={(dialogProps) => (
                        <RegisterDialogSelector
                            {...dialogProps}
                            registerType={registerType}
                            selected={(selected && [selected]) || []}
                            onSelect={(selectedRegister: RegisterRest[]) => {
                                onSelect(registerType, selectedRegister, selectorIndex);
                                dialogProps.onClose();
                            }}
                            excludedIds={excludedIds}
                            staticExpressions={staticExpressions}
                        />
                    )}
                />
            </Grid>
            {!isLoading && error && (
                <Grid item>
                    <Typography
                        variant="body2"
                        color="error"
                    >
                        {error}
                    </Typography>
                </Grid>
            )}
        </>
    );
};

export default RegisterDialogField;
