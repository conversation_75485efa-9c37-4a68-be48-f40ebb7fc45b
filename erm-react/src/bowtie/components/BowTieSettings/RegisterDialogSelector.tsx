import React from 'react';
import { RegisterRest } from 'register/types';
import { RegisterSettingsColDef } from 'common/components/RegisterSelector/constants';
import RegisterSelector from 'common/components/RegisterSelector/RegisterSelector';
import { RegisterType } from './types';
import { strings } from 'common/utils/i18n';
import { ViewExpressionRest } from 'api/generated/types';

interface Props {
    registerType: RegisterType;
    selected?: RegisterRest[];
    onClose: () => void;
    onSelect: (selected: RegisterRest[]) => void;
    excludedIds?: number[];
    staticExpressions?: ViewExpressionRest[];
}

const RegisterDialogSelector: React.FC<Props> = ({ registerType, selected, excludedIds, onClose, onSelect, staticExpressions }) => {
    const getRegisterSelectorTitle = (registerType: RegisterType) => {
        if (registerType === RegisterType.BOWTIE_RISK_EVENT) {
            return strings('bowtie:title.selectRiskEventRegister');
        } else if (registerType === RegisterType.BOWTIE_RISK_CAUSE) {
            return strings('bowtie:title.selectRiskCauseRegister');
        } else if (registerType === RegisterType.BOWTIE_RISK_CONTROL) {
            return strings('bowtie:title.selectRiskControlRegister');
        } else if (registerType === RegisterType.BOWTIE_RISK_IMPACT) {
            return strings('bowtie:title.selectRiskImpactRegister');
        }

        return undefined;
    };

    const getContractTypes = (registerType: RegisterType) => {
        if (registerType === RegisterType.BOWTIE_RISK_EVENT) {
            return [18];
        } else if (registerType === RegisterType.BOWTIE_RISK_CAUSE) {
            return [19];
        } else if (registerType === RegisterType.BOWTIE_RISK_IMPACT) {
            return [20];
        } else if (registerType === RegisterType.BOWTIE_RISK_CONTROL) {
            return [21];
        }

        return [];
    };

    return (
        <RegisterSelector
            onClose={onClose}
            title={getRegisterSelectorTitle(registerType)}
            selected={selected}
            columns={RegisterSettingsColDef}
            onSelect={onSelect}
            multiSelect={false}
            contractTypes={getContractTypes(registerType)}
            excludedIds={excludedIds}
            staticExpressions={staticExpressions}
        />
    );
};

export default RegisterDialogSelector;
