import React, { useEffect, useState } from 'react';
import { generatePath, useNavigate } from 'react-router';
import { FormProvider, useForm } from 'react-hook-form';
import useSnackbar from 'common/hooks/useSnackbar';
import { strings } from 'common/utils/i18n';
import Typography from '@mui/material/Typography';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import SettingsLayout from 'common/layouts/SettingsLayout';
import { RegisterRest } from 'register/types';
import { getRegister } from 'register/api';
import useAsync from 'common/hooks/useAsync';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { SettingsSection } from 'common/layouts/SettingsLayout';
import RegisterDialogField from './RegisterDialogField';
import { RegisterType } from './types';
import { BowtiePath } from '../../routes';
import { useBtrsGetDefinitionUsingGetQuery, useBtrsUpdateDiagramDefinitionUsingPutMutation } from '../../rtkApi';
import { BowTieDefinition, ViewExpressionRest } from 'api/generated/types';
import { ExpressionType } from 'view/types';

const BowTieSettings: React.FunctionComponent = () => {
    const navigate = useNavigate();

    const methods = useForm();
    const oldControlsEnabled = ProtechtDictionary.ermMods.includes('OLD_CONTROLS');
    const centralLibrariesEnabled = ProtechtDictionary.ermMods.includes('CENTRAL_LIBRARIES');

    const [configuration, setConfiguration] = useState<BowTieDefinition>();
    const [definitionLoading, setDefinitionLoading] = useState<boolean>(true);

    const { enqueueError } = useSnackbar();

    const [riskEventRegisterError, setRiskEventRegisterError] = useState<string>();
    const [riskCauseRegisterError, setRiskCauseRegisterError] = useState<string>();
    const [riskControlRegisterError, setRiskControlRegisterError] = useState<string>();
    const [riskImpactRegisterError, setRiskImpactRegisterError] = useState<string>();

    // data
    const {
        response: riskEventRegister,
        isLoading: riskEventRegisterLoading,
        error: riskEventRegisterLoadError,
        asyncLoad: loadRiskEventRegister,
    } = useAsync<RegisterRest>();
    const {
        response: riskCauseRegister,
        isLoading: riskCauseRegisterLoading,
        error: riskCauseRegisterLoadError,
        asyncLoad: loadRiskCauseRegister,
    } = useAsync<RegisterRest>();

    const {
        response: riskImpactRegister,
        isLoading: riskImpactRegisterLoading,
        error: riskImpactRegisterLoadError,
        asyncLoad: loadRiskImpactRegister,
    } = useAsync<RegisterRest>();
    const {
        response: riskControlRegister,
        isLoading: riskControlRegisterLoading,
        error: riskControlRegisterLoadError,
        asyncLoad: loadRiskControlRegister,
    } = useAsync<RegisterRest>();

    const staticExpressionToFilerOutCentralLibraries: ViewExpressionRest[] = [
        {
            id: 0,
            expression: ExpressionType.NOT_EQUAL,
            property: 'registerType',
            type: 'STRING',
            value: 'CentralLibrary',
        },
    ];

    useEffect(() => {
        configuration?.riskEventRegister?.registerId && void loadRiskEventRegister(getRegister(configuration?.riskEventRegister?.registerId));
    }, [configuration?.riskEventRegister?.registerId, loadRiskEventRegister]);

    useEffect(() => {
        configuration?.riskCauseRegister?.registerId && void loadRiskCauseRegister(getRegister(configuration?.riskCauseRegister?.registerId));
    }, [configuration?.riskCauseRegister?.registerId, loadRiskCauseRegister]);

    useEffect(() => {
        if (configuration?.riskImpactRegister?.registerId) {
            void loadRiskImpactRegister(getRegister(configuration?.riskImpactRegister?.registerId));
        }
    }, [configuration?.riskImpactRegister?.registerId, loadRiskImpactRegister]);

    useEffect(() => {
        if (configuration?.riskControlRegister?.registerId) {
            void loadRiskControlRegister(getRegister(configuration?.riskControlRegister?.registerId));
        }
    }, [configuration?.riskControlRegister?.registerId, loadRiskControlRegister]);

    const { data: definition, isLoading } = useBtrsGetDefinitionUsingGetQuery();
    const [putDefinition] = useBtrsUpdateDiagramDefinitionUsingPutMutation();

    useEffect(() => {
        if (definition) {
            setConfiguration(definition);
        } else {
            setConfiguration(undefined);
        }
        setDefinitionLoading(isLoading);
    }, [definition, isLoading]);

    useEffect(() => {
        if (definitionLoading) {
            return;
        }

        try {
            setRiskEventRegisterError(undefined);
        } catch (error) {
            setRiskEventRegisterError(error);
        }
    }, [definitionLoading, riskEventRegister?.id]);

    useEffect(() => {
        if (definitionLoading) {
            return;
        }

        try {
            setRiskImpactRegisterError(undefined);
        } catch (error) {
            setRiskImpactRegisterError(error);
        }
    }, [definitionLoading, riskImpactRegister?.id]);

    useEffect(() => {
        if (definitionLoading) {
            return;
        }

        try {
            setRiskCauseRegisterError(undefined);
        } catch (error) {
            setRiskCauseRegisterError(error);
        }
    }, [definitionLoading, riskCauseRegister?.id]);

    useEffect(() => {
        if (definitionLoading) {
            return;
        }

        try {
            setRiskControlRegisterError(undefined);
        } catch (error) {
            setRiskControlRegisterError(error);
        }
    }, [definitionLoading, riskControlRegister?.id]);

    const handleCancel = () => {
        void navigate(generatePath(BowtiePath.OVERVIEW, { status: 'recent' }));
    };

    const handleSave = async () => {
        if (configuration) {
            const newConfiguration = {
                riskEventRegister: configuration.riskEventRegister,
                riskCauseRegister: configuration.riskCauseRegister,
                riskImpactRegister: configuration.riskImpactRegister,
                riskControlRegister: configuration.riskControlRegister,
            };
            setConfiguration(newConfiguration as BowTieDefinition);
            try {
                await putDefinition({ bowTieDefinition: newConfiguration as BowTieDefinition });

                void navigate(generatePath(BowtiePath.OVERVIEW, { status: 'recent' }));
            } catch (e) {
                enqueueError(strings('bowtie:errorMessage.' + e.response.message));
            }
        }
    };

    const handleRegisterSelect = (registerType: RegisterType, selected: RegisterRest[], _selectorIndex?: number) => {
        if (selected) {
            if (registerType === RegisterType.BOWTIE_RISK_EVENT) {
                setConfigurationParameter('riskEventRegister', {
                    primary: true,
                    registerId: selected[0].id,
                    name: selected[0].label,
                });
            } else if (registerType === RegisterType.BOWTIE_RISK_CAUSE) {
                setConfigurationParameter('riskCauseRegister', {
                    primary: false,
                    registerId: selected[0].id,
                    name: selected[0].label,
                });
            } else if (registerType === RegisterType.BOWTIE_RISK_IMPACT) {
                setConfigurationParameter('riskImpactRegister', {
                    primary: true,
                    registerId: selected[0].id,
                    name: selected[0].label,
                });
            } else if (registerType === RegisterType.BOWTIE_RISK_CONTROL) {
                setConfigurationParameter('riskControlRegister', {
                    primary: true,
                    registerId: selected[0].id,
                    name: selected[0].label,
                });
            }
        }
    };

    const setConfigurationParameter = (parameter: string, value: any) => {
        const newConfiguration = (configuration && { ...configuration }) || {
            riskEventRegister: undefined,
            riskCauseRegister: undefined,
            riskImpactRegister: undefined,
            riskControlRegister: undefined,
        };
        newConfiguration[parameter] = value;
        setConfiguration(newConfiguration as BowTieDefinition);
    };

    return (
        <>
            <ApplicationLayout>
                <ToolbarContainer
                    disableGutters={false}
                    variant="regular"
                >
                    <ToolbarGroup
                        justifyContent="space-between"
                        flex={1}
                    >
                        <ToolbarGroup>
                            <Typography
                                variant="h1"
                                data-testid="bowtie-settings-heading"
                                noWrap
                            >
                                {strings('bowtie:title.settingsForBowtie')}
                            </Typography>
                        </ToolbarGroup>
                        <ToolbarGroup>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                key="bowtie-settings-cancel-action"
                                aria-label="bowtie-settings-cancel-action"
                                onClick={handleCancel}
                                variant={'secondary'}
                            >
                                {strings('common:button.cancel')}
                            </Button>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                key="bowtie-settings-save-action"
                                aria-label="bowtie-settings-save-action"
                                onClick={handleSave}
                            >
                                {strings('ermConstants:save')}
                            </Button>
                        </ToolbarGroup>
                    </ToolbarGroup>
                </ToolbarContainer>

                <MainLayout>
                    <SettingsLayout>
                        <SettingsSection>
                            <FormProvider {...methods}>
                                <Typography
                                    variant="h2"
                                    pb={1}
                                >
                                    {strings('bowtie:title.changeRiskEventRegister')}
                                </Typography>
                                <RegisterDialogField
                                    name="risk-event"
                                    registerType={RegisterType.BOWTIE_RISK_EVENT}
                                    label={strings('bowtie:title.riskEventRegister')}
                                    selected={riskEventRegister}
                                    isLoading={riskEventRegisterLoading}
                                    onSelect={handleRegisterSelect}
                                    placeholder={strings('bowtie:title.placeholderRiskEventRegister')}
                                    onInputClear={() => {
                                        setConfigurationParameter('riskEventRegister', undefined);
                                    }}
                                    error={riskEventRegisterLoadError?.response?.message || riskEventRegisterError}
                                    clearable
                                    excludedIds={riskEventRegister?.id ? [riskEventRegister?.id] : undefined}
                                />
                                <Typography
                                    variant="h2"
                                    pt={3}
                                    pb={1}
                                >
                                    {strings('bowtie:title.changeRiskCauseRegister')}
                                </Typography>
                                <RegisterDialogField
                                    name="risk-cause"
                                    registerType={RegisterType.BOWTIE_RISK_CAUSE}
                                    label={strings('bowtie:title.riskCauseRegister')}
                                    selected={riskCauseRegister}
                                    isLoading={riskCauseRegisterLoading}
                                    onSelect={handleRegisterSelect}
                                    placeholder={strings('bowtie:title.placeholderRiskCauseRegister')}
                                    onInputClear={() => {
                                        setConfigurationParameter('riskCauseRegister', undefined);
                                    }}
                                    error={riskCauseRegisterLoadError?.response?.message || riskCauseRegisterError}
                                    clearable
                                    excludedIds={riskCauseRegister?.id ? [riskCauseRegister?.id] : undefined}
                                />
                                <Typography
                                    variant="h2"
                                    pt={3}
                                    pb={1}
                                >
                                    {strings('bowtie:title.changeRiskImpactRegister')}
                                </Typography>
                                <RegisterDialogField
                                    name="risk impact"
                                    registerType={RegisterType.BOWTIE_RISK_IMPACT}
                                    label={strings('bowtie:title.riskImpactRegister')}
                                    selected={riskImpactRegister}
                                    isLoading={riskImpactRegisterLoading}
                                    onSelect={handleRegisterSelect}
                                    placeholder={strings('bowtie:title.placeholderRiskImpactRegister')}
                                    onInputClear={() => {
                                        setConfigurationParameter('riskImpactRegister', undefined);
                                    }}
                                    error={riskImpactRegisterLoadError?.response?.message || riskImpactRegisterError}
                                    clearable
                                    excludedIds={riskImpactRegister?.id ? [riskImpactRegister?.id] : undefined}
                                />
                                <Typography
                                    variant="h2"
                                    pt={3}
                                    pb={1}
                                >
                                    {strings('bowtie:title.changeRiskControlRegister')}
                                </Typography>
                                <RegisterDialogField
                                    name="risk control"
                                    registerType={RegisterType.BOWTIE_RISK_CONTROL}
                                    label={strings('bowtie:title.riskControlRegister')}
                                    selected={riskControlRegister}
                                    isLoading={riskControlRegisterLoading}
                                    onSelect={handleRegisterSelect}
                                    placeholder={
                                        oldControlsEnabled
                                            ? strings('bowtie:title.placeholderRiskControlRegister')
                                            : strings('bowtie:title.placeholderRiskControlNotSet')
                                    }
                                    onInputClear={() => {
                                        setConfigurationParameter('riskControlRegister', undefined);
                                    }}
                                    error={riskControlRegisterLoadError?.response?.message || riskControlRegisterError}
                                    excludedIds={riskControlRegister?.id ? [riskControlRegister?.id] : undefined}
                                    staticExpressions={centralLibrariesEnabled ? undefined : staticExpressionToFilerOutCentralLibraries}
                                    clearable
                                />
                            </FormProvider>
                        </SettingsSection>
                    </SettingsLayout>
                </MainLayout>
            </ApplicationLayout>
        </>
    );
};

export default BowTieSettings;
