import React, { useCallback, useEffect, useState } from 'react';
import { strings } from 'common/utils/i18n';
import Grid from '@mui/material/Grid';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import { BowTieDefinition, BowTieDiagramRest, ExportProgressResponse } from 'api/generated/types';
import { getReactRoot } from 'config';
import { ExportConfiguration, ExportProgressStatus, ExportType, RegisterExportParams } from 'vendorRiskManagement/types';
import { initiateFileDownload } from 'app/utils';
import { useExportRegisterEntriesMutation, useGetExportContentMutation, useGetExportProgressQuery } from 'vendorRiskManagement/rtkApi';
import { NodeType } from 'bowtie/types';
import ProgressDialog, { ProgressType } from 'common/components/ProgressDialog';
import {
    useExportRegisterEntriesPost123Mutation,
    useExportRegisterEntriesPost12Mutation,
    useExportRegisterEntriesPost1Mutation,
    useGetExportProgressGet1Query,
    useGetExportProgressGet12Query,
    useGetExportProgressGet123Query,
    useGetExportContentRiskCausesMutation,
    useGetExportContentControlsMutation,
    useGetExportContentRiskEventsMutation,
} from 'risk/rtkApi';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useFileDownloader } from '@protecht/ui-library/library/hooks/useFileDownloader';
import useTheme from '@mui/system/useTheme';
import Box from '@mui/material/Box';

type Props = {
    visible: boolean;
    onClose: () => void;
    bowtieDefinition?: BowTieDefinition;
    data: BowTieDiagramRest;
};

const ExportNodeItem = styled('div')(() => ({
    marginBottom: '20px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
}));

const BowTieExportDialog: React.FC<Props> = ({ visible, onClose, data, bowtieDefinition }: Props) => {
    const [exportId, setExportId] = useState<string>();
    const [exportRiskEventId, setExportRiskEventId] = useState<string>();
    const [exportRiskCauseId, setExportRiskCauseId] = useState<string>();
    const [exportControlId, setExportControlId] = useState<string>();
    const { downloadFile } = useFileDownloader();

    const theme = useTheme();

    const [exportRegisterEntries] = useExportRegisterEntriesMutation();
    const [exportRegisterEntriesCause] = useExportRegisterEntriesPost1Mutation();
    const [exportRegisterEntriesControl] = useExportRegisterEntriesPost12Mutation();
    const [exportRegisterEntriesRiskEvent] = useExportRegisterEntriesPost123Mutation();

    const [getExportContent] = useGetExportContentMutation();

    const [getExportContentRiskCause] = useGetExportContentRiskCausesMutation();
    const [getExportContentRiskControl] = useGetExportContentControlsMutation();
    const [getExportContentRiskEvent] = useGetExportContentRiskEventsMutation();

    const [exportProgress, setExportProgress] = useState<ExportProgressResponse>();
    const [progressType, setProgressType] = useState<ProgressType>();
    const [currentExportNodeType, setCurrentExportNodeType] = useState<NodeType>();

    const { currentData: exportProgressData } = useGetExportProgressQuery(exportId!, { skip: !exportId, pollingInterval: 1000 });
    const { currentData: exportProgressDataRiskCause } = useGetExportProgressGet1Query(
        { exportid: exportRiskCauseId! },
        { skip: !exportRiskCauseId, pollingInterval: 1000 },
    );

    const { currentData: exportProgressDataControl } = useGetExportProgressGet12Query(
        { exportid: exportControlId! },
        { skip: !exportControlId, pollingInterval: 1000 },
    );
    const { currentData: exportProgressDataRiskEvent } = useGetExportProgressGet123Query(
        { exportid: exportRiskEventId! },
        { skip: !exportRiskEventId, pollingInterval: 1000 },
    );

    const handleDownloadFileRiskEvent = useCallback(
        async (exportId: string, fileName = 'file') => {
            const file = (await getExportContentRiskEvent(exportId).unwrap()) as Blob;
            initiateFileDownload(fileName, file);
        },
        [getExportContentRiskEvent],
    );

    const handleDownloadFileControl = useCallback(
        async (exportId: string, fileName = 'file') => {
            const file = (await getExportContentRiskControl(exportId).unwrap()) as Blob;
            initiateFileDownload(fileName, file);
        },
        [getExportContentRiskControl],
    );

    const handleDownloadFileRiskCause = useCallback(
        async (exportId: string, fileName = 'file') => {
            const file = (await getExportContentRiskCause(exportId).unwrap()) as Blob;
            initiateFileDownload(fileName, file);
        },
        [getExportContentRiskCause],
    );

    const handleDownloadFile = useCallback(
        async (exportId: string, fileName = 'file') => {
            const file = (await getExportContent(exportId).unwrap()) as Blob;
            initiateFileDownload(fileName, file);
        },
        [getExportContent],
    );

    useEffect(() => {
        if ((exportId || exportRiskCauseId || exportControlId || exportRiskEventId) && exportProgress) {
            if (exportProgress?.status === ExportProgressStatus.DONE) {
                const regId = getRegisterIdFromBowtieDefinitionByNodeType(currentExportNodeType);

                if (regId && exportId) {
                    void handleDownloadFile(exportId, exportProgress.suggestedFilename);
                    setExportId(undefined);
                } else {
                    switch (currentExportNodeType) {
                        case NodeType.Cause:
                            if (exportRiskCauseId) {
                                void handleDownloadFileRiskCause(exportRiskCauseId, exportProgress.suggestedFilename);
                                setExportRiskCauseId(undefined);
                                return;
                            }
                            return;
                        case NodeType.Control:
                            if (exportControlId) {
                                void handleDownloadFileControl(exportControlId, exportProgress.suggestedFilename);
                                setExportControlId(undefined);
                                return;
                            }
                            return;
                        case NodeType.RiskEvent:
                        case NodeType.MainRiskEvent:
                            if (exportRiskEventId) {
                                void handleDownloadFileRiskEvent(exportRiskEventId, exportProgress.suggestedFilename);
                                setExportRiskEventId(undefined);
                                return;
                            }
                            return;
                        default:
                            return;
                    }
                }
            }

            if (exportProgress.status === ExportProgressStatus.FAILED) {
                if (exportProgress?.messages?.[0]) {
                    setExportId(undefined);
                    setExportControlId(undefined);
                    setExportRiskCauseId(undefined);
                    setExportRiskEventId(undefined);
                }
            }
        }
    }, [
        currentExportNodeType,
        exportControlId,
        exportId,
        exportProgress,
        exportRiskCauseId,
        exportRiskEventId,
        handleDownloadFile,
        handleDownloadFileControl,
        handleDownloadFileRiskCause,
        handleDownloadFileRiskEvent,
    ]);

    useEffect(() => {
        const regId = getRegisterIdFromBowtieDefinitionByNodeType(currentExportNodeType);
        if (regId) {
            if (exportProgressData) {
                setExportProgress(exportProgressData);
            }
        } else {
            switch (currentExportNodeType) {
                case NodeType.Cause:
                    if (exportProgressDataRiskCause) {
                        setExportProgress(exportProgressDataRiskCause);
                    }
                    return;
                case NodeType.Control:
                    if (exportProgressDataControl) {
                        setExportProgress(exportProgressDataControl);
                    }
                    return;
                case NodeType.RiskEvent:
                case NodeType.MainRiskEvent:
                    if (exportProgressDataRiskEvent) {
                        setExportProgress(exportProgressDataRiskEvent);
                    }
                    return;
                default:
                    return;
            }
        }
    }, [currentExportNodeType, exportProgressData, exportProgressDataControl, exportProgressDataRiskCause, exportProgressDataRiskEvent]);

    const collectLibraryLinkIds = (data: BowTieDiagramRest) => {
        const result: Record<string, number[]> = {};
        if (data) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            data.diagramModel.nodes.forEach((node) => {
                if (node.libraryLink) {
                    const { nodeType, libraryLink } = node;
                    if (!result[nodeType]) {
                        if (nodeType === 'MainRiskEvent') {
                            result['RiskEvent'] = [];
                        } else {
                            result[nodeType] = [];
                        }
                    }
                    if (libraryLink.id != -1) {
                        if (nodeType === 'MainRiskEvent') {
                            result['RiskEvent'].push(libraryLink.id);
                        } else {
                            result[nodeType].push(libraryLink.id);
                        }
                    }
                }
            });
        }

        return result;
    };

    const getRegisterIdFromBowtieDefinitionByNodeType = useCallback(
        (nodeType: NodeType | undefined): number | undefined => {
            if (bowtieDefinition) {
                switch (nodeType) {
                    case NodeType.RiskEvent:
                    case NodeType.MainRiskEvent:
                        return bowtieDefinition?.riskEventRegister?.registerId;
                    case NodeType.Cause:
                        return bowtieDefinition?.riskCauseRegister?.registerId;
                    case NodeType.Impact:
                        return bowtieDefinition?.riskImpactRegister?.registerId;
                    case NodeType.Control:
                        return bowtieDefinition?.riskControlRegister?.registerId;
                    default:
                        return undefined;
                }
            }
            return undefined;
        },
        [bowtieDefinition],
    );

    const libraryLinkIdsByNodeType = collectLibraryLinkIds(data);

    const exportDiagram = (data?: BowTieDiagramRest, fileName?) => {
        if (data) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            const updatedNodes = data?.diagramModel?.nodes?.map((node) => ({
                ...node,
                originalName: node.name,
            }));

            const updateData = {
                ...data,
                diagramModel: {
                    ...data.diagramModel,
                    nodes: updatedNodes,
                },
            };

            const jsonString = JSON.stringify(updateData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            downloadFile(url, fileName);
        }
    };

    const handleDataExport = useCallback(
        async (values) => {
            setCurrentExportNodeType(values.nodeType);
            setProgressType(ProgressType.Export);

            const exportConfiguration: ExportConfiguration = {
                exportType: ExportType.EXPORT_ALL,
                stripTags: true,
                includeDeleted: false,
            };

            const regId = getRegisterIdFromBowtieDefinitionByNodeType(values.nodeType);
            const queryParams: RegisterExportParams = {
                selectedItems: [values.selectedItems],
                excludeSystemFields: true,
            };

            if (regId) {
                const exportId = await exportRegisterEntries({
                    regId: regId,
                    payload: { exportConfiguration },
                    params: queryParams,
                }).unwrap();
                setExportId(exportId);
            } else {
                let exportId;
                switch (values.nodeType) {
                    case NodeType.Cause:
                    case 'Cause':
                        exportId = await exportRegisterEntriesCause({
                            ...queryParams,
                            dataSetExportRequest: { exportConfiguration },
                        }).unwrap();
                        setExportRiskCauseId(exportId);
                        return;
                    case NodeType.Control:
                    case 'Control':
                        exportId = await exportRegisterEntriesControl({
                            ...queryParams,
                            dataSetExportRequest: { exportConfiguration },
                        }).unwrap();
                        setExportControlId(exportId);
                        return;

                    case NodeType.RiskEvent:
                    case 'RiskEvent':
                    case NodeType.MainRiskEvent:
                    case 'MainRiskEvent':
                        exportId = await exportRegisterEntriesRiskEvent({
                            ...queryParams,
                            dataSetExportRequest: { exportConfiguration },
                        }).unwrap();
                        setExportRiskEventId(exportId);

                        return;

                    default:
                        return;
                }
            }
        },
        [
            exportRegisterEntries,
            exportRegisterEntriesCause,
            exportRegisterEntriesControl,
            exportRegisterEntriesRiskEvent,
            getRegisterIdFromBowtieDefinitionByNodeType,
        ],
    );
    const handleCloseProgressDialog = () => {
        if (progressType === ProgressType.Export) {
            setExportId(undefined);
            setExportRiskEventId(undefined);
            setExportRiskCauseId(undefined);
            setExportControlId(undefined);
            setExportProgress(undefined);
            setProgressType(undefined);
            return;
        }
        setProgressType(undefined);
    };

    return (
        <>
            <Dialog
                visible={visible}
                title={strings('bowtie:labels.exportBowtie')}
                height={'auto'}
                width={480}
                dialogContainer={getReactRoot()}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'primary'}
                            type="submit"
                            size="large"
                            dataTestId="button-close"
                            onClick={onClose}
                        >
                            {strings('ermMessages:btn_done')}
                        </Button>
                    </DialogActions>
                }
            >
                <Grid
                    container
                    direction={'column'}
                    spacing={1}
                    height={'100%'}
                >
                    <Grid
                        item
                        sx={{ height: '100%' }}
                    >
                        <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between', height: '100%' }}>
                            <Box sx={{ height: '100%' }}>
                                <Box
                                    key={'export-diagram-data'}
                                    sx={{
                                        marginBottom: '20px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <Typography
                                        variant="body1"
                                        sx={{ marginRight: '8px' }}
                                    >
                                        {strings('bowtie:message.exportNodes')}
                                    </Typography>
                                    <Button
                                        {...ButtonStyles.dialogButton}
                                        variant={'secondary'}
                                        size="large"
                                        onClick={() => exportDiagram(data, data.name)}
                                    >
                                        {strings('bowtie:button.export')}
                                    </Button>
                                </Box>

                                {Object.entries(libraryLinkIdsByNodeType).map(
                                    ([nodeType, ids]) =>
                                        ids?.length > 0 && (
                                            <ExportNodeItem key={nodeType}>
                                                <span style={{ marginRight: '8px' }}>
                                                    <Typography
                                                        variant="body1"
                                                        sx={{ whiteSpace: 'nowrap' }}
                                                    >
                                                        {strings('bowtie:diagram.message.exportNodesCount', {
                                                            count: ids.length,
                                                            type: ids.length <= 1 ? nodeType : `${nodeType}s`,
                                                        })}
                                                    </Typography>
                                                </span>
                                                {
                                                    <Button
                                                        {...ButtonStyles.dialogButton}
                                                        variant={'secondary'}
                                                        size="large"
                                                        onClick={() =>
                                                            handleDataExport({
                                                                stripTags: false,
                                                                includeDeleted: false,
                                                                useCurrentView: false,
                                                                selectedItems: ids,
                                                                nodeType: NodeType[nodeType as keyof typeof NodeType],
                                                            })
                                                        }
                                                    >
                                                        {strings('bowtie:button.export')}
                                                    </Button>
                                                }
                                            </ExportNodeItem>
                                        ),
                                )}
                            </Box>
                            <Box style={{ padding: '10px', border: `1px solid ${theme.palette.protechtGrey?.grey_231}` }}>
                                <Typography variant="body1">{strings('bowtie:message.exportDialog')}</Typography>
                            </Box>
                        </Box>

                        {progressType && (
                            <ProgressDialog
                                visible={true}
                                title={strings('register:registerIO.dataExport')}
                                progressType={progressType}
                                infoMessage={
                                    exportProgress?.status === ExportProgressStatus.DONE
                                        ? strings('register:progressDialog.finished')
                                        : strings('register:progressDialog.exportingRecords')
                                }
                                totalCount={exportProgress?.totalCount}
                                processedCount={exportProgress?.processedCount}
                                progressVariant="determinate"
                                onClose={handleCloseProgressDialog}
                            />
                        )}
                    </Grid>
                </Grid>
            </Dialog>
        </>
    );
};

export default BowTieExportDialog;
