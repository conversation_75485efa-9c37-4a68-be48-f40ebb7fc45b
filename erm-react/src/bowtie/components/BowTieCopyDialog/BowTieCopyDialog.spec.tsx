import { strings } from 'common/utils/i18n';
import React from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router';
import { render, screen } from 'test/utils';
import { mockBowTieEntry } from 'bowtie/api.mock';
import user from '@testing-library/user-event';

import BowTieCopyDialog from './BowTieCopyDialog';
import { mockNavigate } from 'test/config/setupAfterEnv';

const mockCopyDiagram = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve({ id: 1, name: 'test' }),
}));
jest.mock('bowtie/rtkApi', () => ({
    ...jest.requireActual('bowtie/rtkApi'),
    useBtrsGetDiagramCopyUsingPutMutation: () => [mockCopyDiagram, { isLoading: false, isSuccess: true, isError: false }],
}));

const getButtonByName = (name: string) => screen.getByRole('button', { name: strings(`common:button.${name}`) });
const getCancelButton = () => getButtonByName('cancel');
const getDuplicateButton = () => getButtonByName('duplicate');
const getInputFieldRole = () => screen.getByRole('textbox') as HTMLInputElement;

describe('<BowTieCopyDialog />', () => {
    const onClose = jest.fn();
    const onConfirm = jest.fn();

    const setup = () => {
        return render(
            <BrowserRouter>
                <BowTieCopyDialog
                    visible={true}
                    onClose={onClose}
                    data={mockBowTieEntry}
                    onConfirm={onConfirm}
                    redirect={true}
                />
            </BrowserRouter>,
        );
    };

    it('was rendered', () => {
        const view = setup();
        expect(view.container).toBeInTheDocument();
        expect(view.container).toBeVisible();
        expect(document.body).toMatchSnapshot();
    });

    it('cancel button invokes action on cancel', async () => {
        setup();
        const cancelBtn = getCancelButton();
        expect(cancelBtn).toBeInTheDocument();
        expect(cancelBtn).toBeEnabled();
        expect(cancelBtn).toBeVisible();
        await user.click(cancelBtn);
        expect(onClose).toHaveBeenCalled();
    });

    it('duplicat button is disable when input field is empty', () => {
        setup();
        const onConfirmBtn = getDuplicateButton();
        const textBox = getInputFieldRole();
        expect(onConfirmBtn).toBeInTheDocument();
        expect(onConfirmBtn).toBeVisible();

        expect(textBox).toHaveValue('');
        expect(onConfirmBtn).toBeDisabled();
    });

    it('duplicate button is able when input field is not empty', async () => {
        setup();
        const onConfirmBtn = getDuplicateButton();
        const textBox = getInputFieldRole();

        await user.type(textBox, 'test text');
        expect(textBox).not.toHaveValue('');
        expect(onConfirmBtn).toBeEnabled();
    });

    it('is able to submit valid data', async () => {
        setup();
        const onConfirmBtn = getDuplicateButton();
        const textBox = getInputFieldRole();

        await user.type(textBox, 'test text');
        expect(textBox).toHaveValue('test text');
        expect(onConfirmBtn).toBeEnabled();

        await user.click(onConfirmBtn);

        expect(onConfirm).toHaveBeenCalled();
        expect(mockCopyDiagram).toHaveBeenCalled();
        expect(mockNavigate).toHaveBeenCalled();
    });

    it('is not able to submit non unique data', async () => {
        setup();

        mockCopyDiagram.mockImplementation(() => ({
            unwrap: () => Promise.reject({ status: 409, data: { message: 'Diagram name already used.' } }),
        }));

        const onConfirmBtn = getDuplicateButton();
        const textBox = getInputFieldRole();
        await user.type(textBox, 'test text');
        await user.click(onConfirmBtn);
        expect(textBox).not.toHaveValue('');
        expect(mockCopyDiagram).toHaveBeenCalled();
        await expect(onConfirmBtn).toBeDisabled();
    });
});
