import React, { useState } from 'react';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import FieldWrapper from 'common/components/FieldWrapper';
import { strings } from 'common/utils/i18n';
import { useNavigate, useLocation, generatePath } from 'react-router';
import { FieldIndicatorType } from 'app/types';
import { isBlankString } from 'common/utils/functions';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import useSnackbar from 'common/hooks/useSnackbar';
import { BowtiePath } from 'bowtie/routes';
import { useBtrsGetDiagramCopyUsingPutMutation } from 'bowtie/rtkApi';
import { BowTieDiagramRest } from 'api/generated/types';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';

type Props = {
    visible: boolean;
    onClose: () => void;
    onConfirm?: () => void;
    data: BowTieDiagramRest;
    redirect?: boolean;
};

const BowTieCopyDialog: React.FC<Props> = (props: Props) => {
    const { visible, onClose } = props;
    const navigate = useNavigate();
    const location = useLocation();
    const { enqueueSuccess } = useSnackbar();

    const [name, setName] = useState<string>('');
    const [errorMessage, setErrorMessage] = useState<string>();

    const [copyDiagram] = useBtrsGetDiagramCopyUsingPutMutation();

    const closeDialog = () => {
        setName('');
        setErrorMessage(undefined);
        onClose();
    };

    const onConfirm = async (): Promise<void> => {
        try {
            const response = await copyDiagram({ id: props.data.id!, body: name }).unwrap();
            enqueueSuccess(strings('bowtie:file.message.successfullyCopied'));
            if (props.redirect) {
                const responseId = response?.id?.toString() ?? null;

                void navigate(generatePath(BowtiePath.DIAGRAM, { id: responseId }), {
                    replace: true,
                    state: location?.state?.from ? { from: location.state?.from } : {},
                });
            }
            props.onConfirm?.();
            closeDialog();
        } catch (err) {
            setErrorMessage(`${strings('bowtie:file.message.diagramCopyError')} ${err.response || ''}`);
        }
    };

    const onNameChanged = (event) => {
        setErrorMessage(undefined);
        setName(event.target.value);
    };

    return (
        <Dialog
            visible={visible}
            title={strings('bowtie:file.title.copyBowTieFile', { name: props.data.name })}
            width={500}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={closeDialog}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={isBlankString(name) || !!errorMessage}
                        onClick={onConfirm}
                    >
                        {strings('common:button.duplicate')}
                    </Button>
                </DialogActions>
            }
        >
            <Box pb={3}>
                <Typography
                    variant="body1"
                    color="textSecondary"
                >
                    {strings('bowtie:file.message.bowTieCopyInfo')}
                </Typography>
            </Box>
            <FieldWrapper
                label={strings('bowtie:file.label.enterNewFilename')}
                indicatorType={errorMessage ? FieldIndicatorType.ERROR : FieldIndicatorType.EMPTY}
                message={errorMessage}
            >
                <Input
                    value={name}
                    onChange={onNameChanged}
                />
            </FieldWrapper>
        </Dialog>
    );
};

export default BowTieCopyDialog;
