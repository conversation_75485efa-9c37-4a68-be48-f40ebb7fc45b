// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieCopyDialog /> was rendered 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-ob4s51-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Duplicate Bow Tie Some text name"
    id="draggable-dialog-Duplicate Bow Tie Some text name"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Duplicate Bow Tie Some text name
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiBox-root css-hshm0p"
            >
              <p
                class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
              >
                By duplicating this file, a new Bow Tie with the same content will be created in your Drafts folder.
              </p>
            </div>
            <div
              class="MuiContainer-root MuiContainer-maxWidthLg MuiContainer-disableGutters css-1g6kegs-MuiContainer-root"
              data-testid="field-wrapper-Enter a new file name:"
            >
              <p
                class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
              >
                Enter a new file name:
              </p>
              <div
                class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                >
                  <input
                    aria-invalid="false"
                    class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                    id=":r2:"
                    tabindex="0"
                    type="text"
                    value=""
                  />
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                  >
                    <legend
                      class="css-13wgbfv"
                    >
                      <span
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-knqc4i-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-cancel"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Cancel
              </span>
            </button>
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1m5dup3-MuiButtonBase-root-MuiButton-root"
              data-testid="button-confirm"
              disabled=""
              tabindex="-1"
              type="submit"
            >
              <span
                class="css-1d0doyg"
              >
                Duplicate
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;
