// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieLinksDialog /> was rendered 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-1hv426h-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Links"
    id="draggable-dialog-Links"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Links
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-1 MuiGrid-direction-xs-column css-ffjoah-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-1flsrww-MuiTypography-root"
                >
                  Links to this Bow Tie
                </p>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <button
                  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-d0besf-MuiButtonBase-root-MuiButton-root"
                  data-testid="bowtie-add-link"
                  style="width: 100%; justify-content: space-between;"
                  tabindex="0"
                  type="button"
                >
                  <span
                    class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
                  >
                    <svg
                      data-icon="add"
                      fill="currentColor"
                      height="24"
                      viewBox="0 0 24 24"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M4.5 10.75h15v2.5h-15z"
                        fill="currentColor"
                      />
                      <path
                        d="M10.75 19.5v-15h2.5v15z"
                        fill="currentColor"
                      />
                    </svg>
                  </span>
                  <span
                    class="css-qv0y8m"
                  >
                    Add Link
                  </span>
                  <span
                    class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
                  >
                    <svg
                      aria-hidden="true"
                      class="svg-inline--fa fa-ellipsis "
                      data-icon="ellipsis"
                      data-prefix="far"
                      focusable="false"
                      role="img"
                      viewBox="0 0 448 512"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                        fill="currentColor"
                      />
                    </svg>
                  </span>
                </button>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="css-1tucq0r"
                >
                  <div
                    class="MuiBox-root css-6c47we"
                  >
                    <a
                      href="<%= process.env.BASE_URL %>/camilla/worms/client/app/widget.html?tablename=table_112810&widget=RegisterPage&incidentId=1000000&appId=1062"
                      rel="noreferrer noopener"
                      style="text-decoration: none;"
                      target="_blank"
                    >
                      <div
                        class="MuiBox-root css-u4p24i"
                      >
                        <svg
                          aria-hidden="true"
                          class="svg-inline--fa fa-link fa-xs "
                          color="#1B4AD5"
                          data-icon="link"
                          data-prefix="far"
                          focusable="false"
                          role="img"
                          viewBox="0 0 640 512"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M580.3 267.2c56.2-56.2 56.2-147.3 0-203.5C526.8 10.2 440.9 7.3 383.9 57.2l-6.1 5.4c-10 8.7-11 23.9-2.3 33.9s23.9 11 33.9 2.3l6.1-5.4c38-33.2 95.2-31.3 130.9 4.4c37.4 37.4 37.4 98.1 0 135.6L433.1 346.6c-37.4 37.4-98.2 37.4-135.6 0c-35.7-35.7-37.6-92.9-4.4-130.9l4.7-5.4c8.7-10 7.7-25.1-2.3-33.9s-25.1-7.7-33.9 2.3l-4.7 5.4c-49.8 57-46.9 142.9 6.6 196.4c56.2 56.2 147.3 56.2 203.5 0L580.3 267.2zM59.7 244.8C3.5 301 3.5 392.1 59.7 448.2c53.6 53.6 139.5 56.4 196.5 6.5l6.1-5.4c10-8.7 11-23.9 2.3-33.9s-23.9-11-33.9-2.3l-6.1 5.4c-38 33.2-95.2 31.3-130.9-4.4c-37.4-37.4-37.4-98.1 0-135.6L207 165.4c37.4-37.4 98.1-37.4 135.6 0c35.7 35.7 37.6 92.9 4.4 130.9l-5.4 6.1c-8.7 10-7.7 25.1 2.3 33.9s25.1 7.7 33.9-2.3l5.4-6.1c49.9-57 47-142.9-6.5-196.5c-56.2-56.2-147.3-56.2-203.5 0L59.7 244.8z"
                            fill="currentColor"
                          />
                        </svg>
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1q0qo1i-MuiTypography-root"
                        >
                          Processes - nice title (BowTieTest)
                        </p>
                      </div>
                    </a>
                    <div
                      class="actions css-fw8ck8"
                    >
                      <div
                        class="MuiBox-root css-70qvj9"
                      >
                        <svg
                          aria-hidden="true"
                          class="svg-inline--fa fa-trash-can "
                          color="#DB2121"
                          data-icon="trash-can"
                          data-prefix="far"
                          focusable="false"
                          role="img"
                          viewBox="0 0 448 512"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M170.5 51.6L151.5 80l145 0-19-28.4c-1.5-2.2-4-3.6-6.7-3.6l-93.7 0c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6L354.2 80 368 80l48 0 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-8 0 0 304c0 44.2-35.8 80-80 80l-224 0c-44.2 0-80-35.8-80-80l0-304-8 0c-13.3 0-24-10.7-24-24S10.7 80 24 80l8 0 48 0 13.8 0 36.7-55.1C140.9 9.4 158.4 0 177.1 0l93.7 0c18.7 0 36.2 9.4 46.6 24.9zM80 128l0 304c0 17.7 14.3 32 32 32l224 0c17.7 0 32-14.3 32-32l0-304L80 128zm80 64l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16z"
                            fill="currentColor"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-knqc4i-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1m5dup3-MuiButtonBase-root-MuiButton-root"
              data-testid="button-confirm"
              tabindex="0"
              type="submit"
            >
              <span
                class="css-1d0doyg"
              >
                OK
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;
