// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieAddLinkDialog /> was rendered 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-1hv426h-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Relevant Registers"
    id="draggable-dialog-Relevant Registers"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Relevant Registers
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-1 MuiGrid-direction-xs-column css-ffjoah-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body1 css-e8ctdu-MuiTypography-root"
                >
                  Select the Register that contains the item you would like to link to.
                </p>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body1 css-e8ctdu-MuiTypography-root"
                >
                  Registers:
                </p>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <ul
                  class="MuiList-root MuiList-dense css-xs8o24-MuiList-root"
                >
                  <div
                    class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-gutters css-79l2i5-MuiButtonBase-root-MuiListItemButton-root"
                    data-testid="register-test-label - field-test-label"
                    role="button"
                    tabindex="0"
                  >
                    <div
                      class="MuiListItemText-root MuiListItemText-dense css-tlelie-MuiListItemText-root"
                    >
                      <span
                        class="MuiTypography-root MuiTypography-dropdownItem MuiListItemText-primary css-1xr844i-MuiTypography-root"
                      >
                        register-test-label - field-test-label
                      </span>
                    </div>
                  </div>
                  <div
                    class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-gutters css-79l2i5-MuiButtonBase-root-MuiListItemButton-root"
                    data-testid="register-test-label - test label text 2222222"
                    role="button"
                    tabindex="0"
                  >
                    <div
                      class="MuiListItemText-root MuiListItemText-dense css-tlelie-MuiListItemText-root"
                    >
                      <span
                        class="MuiTypography-root MuiTypography-dropdownItem MuiListItemText-primary css-1xr844i-MuiTypography-root"
                      >
                        register-test-label - test label text 2222222
                      </span>
                    </div>
                  </div>
                </ul>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-knqc4i-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-cancel"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Cancel
              </span>
            </button>
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1m5dup3-MuiButtonBase-root-MuiButton-root"
              data-testid="button-confirm"
              disabled=""
              tabindex="-1"
              type="submit"
            >
              <span
                class="css-1d0doyg"
              >
                OK
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;
