import React, { useCallback, useEffect, useState } from 'react';
import { Trans } from 'react-i18next';
import { strings } from 'common/utils/i18n';
import SelectedDataView from 'common/components/MultiselectPicker/SelectedDataView';
import { IdWithName } from 'app/types';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import useTheme from '@mui/system/useTheme';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEllipsisH, faLink, faQuestionCircle } from '@fortawesome/pro-regular-svg-icons';
import BowTieAddLinkDialog from './BowTieAddLinkDialog';
import { RegisterDataBowTieRest } from 'register/types';
import { BOWTIE_TEST_ID_PREFIX } from 'bowtie/constants';
import useSnackbar from 'common/hooks/useSnackbar';
import Loading from 'common/components/Loading/Loading';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { useBtrsGetEntriesUsingGetQuery, useBtrsUnlinkUsingPutMutation } from 'bowtie/rtkApi';
import { isEqual } from 'lodash';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import DialogActions from '@mui/material/DialogActions';
import { Field } from 'api/generated/types';
import { getReactRoot } from 'config';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

type Props = {
    visible: boolean;
    onClose: () => void;
    diagramId: number;
};

type LinkData = IdWithName &
    RegisterDataBowTieRest & {
        currentField: Field;
    };

const BowTieLinksDialog: React.FC<Props> = ({ visible, onClose, diagramId }: Props) => {
    const { enqueueError, enqueueSuccess } = useSnackbar();
    const theme = useTheme();

    const [addLinkDialogVisible, setAddLinkDialogVisible] = useState<boolean>(false);
    const [currentLinks, setCurrentLinks] = useState<LinkData[]>([]);

    const [unlinkingInProgress, setUnlinkingInProgress] = useState<boolean>(false);

    const { showConfirmationAlert } = useConfirmationAlert();

    const {
        data: links,
        isLoading,
        error: errorMessage,
    } = useBtrsGetEntriesUsingGetQuery({ id: diagramId }, { skip: !diagramId || !visible || addLinkDialogVisible });

    const [unLinkEntryToBowTie] = useBtrsUnlinkUsingPutMutation();
    useEffect(() => {
        const transformedLinks: LinkData[] = links
            ?.flatMap((link) =>
                link.entryId !== undefined
                    ? link.fields?.map((field) => ({
                          ...link,
                          id: parseInt(`${link.entryId}${field.fieldId}`, 10),
                          name: `${link.label} - ${link.displayValue} (${field.label})`,
                          currentField: field,
                      }))
                    : [],
            )
            .filter(Boolean) as LinkData[];

        if (!isEqual(transformedLinks, currentLinks)) {
            setCurrentLinks(transformedLinks);
        }
    }, [links, currentLinks]);

    const removeLink = useCallback(
        async (item: LinkData) => {
            setUnlinkingInProgress(true);
            try {
                await unLinkEntryToBowTie({
                    bowTieId: diagramId,
                    regId: item.regId,
                    entryId: item.entryId,
                    fieldBowTieRest: item.currentField,
                }).unwrap();
                setCurrentLinks(currentLinks.filter((link) => link.id !== item.id));
                enqueueSuccess(strings('bowtie:message.registerEntryLinkRemoved'));
            } catch (_err) {
                enqueueError(strings('bowtie:message.registerEntryLinkRemoveError'));
            } finally {
                setUnlinkingInProgress(false);
            }
        },
        [currentLinks, diagramId, enqueueError, enqueueSuccess, unLinkEntryToBowTie],
    );

    const showDeleteWarning = useCallback(
        (item: LinkData) => {
            showConfirmationAlert({
                onConfirm: () => {
                    void removeLink(item);
                },
                icon: <FontAwesomeIcon icon={faQuestionCircle} />,
                contentText: '',
                customContent: (
                    <Trans
                        i18nKey="bowtie:file.message.confirmUnlinkQuestion"
                        values={{ register: item.label, entry: item.displayValue, field: item.currentField.label }}
                        parent={Typography}
                    />
                ),
            });
        },
        [removeLink, showConfirmationAlert],
    );

    const onDialogClose = () => {
        setAddLinkDialogVisible(false);
    };

    const renderContent = () => {
        if (errorMessage) {
            return <Typography color={'error'}>{strings('ermErrors:loading_failed')}</Typography>;
        }

        return (
            <SelectedDataView
                isLoading={isLoading}
                selected={currentLinks}
                renderContent={(item: LinkData) => (
                    <a
                        href={`${ProtechtDictionary.siteUrl}/worms/client/app/widget.html?tablename=${item.tableName}&widget=RegisterPage&incidentId=${item.entryId}&appId=${item.appId}`}
                        target="_blank"
                        rel="noreferrer noopener"
                        style={{ textDecoration: 'none' }}
                    >
                        <Box
                            display={'flex'}
                            flexDirection={'row'}
                            alignItems="center"
                        >
                            <FontAwesomeIcon
                                icon={faLink}
                                color={theme.palette.primary.main}
                                size="xs"
                            />
                            <Typography
                                variant="body2"
                                color={'primary'}
                                ml={1}
                            >
                                {item.name}
                            </Typography>
                        </Box>
                    </a>
                )}
                onRemove={showDeleteWarning}
            />
        );
    };

    return (
        <>
            <Dialog
                visible={visible}
                title={strings('common:label.links')}
                height={400}
                width={500}
                dialogContainer={getReactRoot()}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            type="submit"
                            dataTestId="button-confirm"
                            disabled={unlinkingInProgress || isLoading}
                            onClick={onClose}
                        >
                            {strings('ermMessages:btn_okay')}
                        </Button>
                    </DialogActions>
                }
            >
                <Grid
                    container
                    direction={'column'}
                    spacing={1}
                >
                    <Grid item>
                        <Typography variant="body3">{strings('bowtie:labels.bowtieLinks')}</Typography>
                    </Grid>
                    <Grid item>
                        <Button
                            size="large"
                            style={{ width: '100%', justifyContent: 'space-between' }}
                            variant="secondary"
                            startIcon={<Add />}
                            endIcon={<FontAwesomeIcon icon={faEllipsisH} />}
                            onClick={() => setAddLinkDialogVisible(true)}
                            dataTestId={`${BOWTIE_TEST_ID_PREFIX}-add-link`}
                        >
                            {strings('common:label.addLink')}
                        </Button>
                    </Grid>
                    <Grid item>{renderContent()}</Grid>
                    {unlinkingInProgress && <Loading message={strings('bowtie:messages.info.unlinking')} />}
                </Grid>
            </Dialog>
            <BowTieAddLinkDialog
                visible={addLinkDialogVisible}
                onConfirm={onDialogClose}
                onClose={onDialogClose}
                diagramId={diagramId}
            />
        </>
    );
};

export default BowTieLinksDialog;
