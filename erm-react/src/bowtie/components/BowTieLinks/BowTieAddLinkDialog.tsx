import React, { useEffect, useState } from 'react';

import { strings } from 'common/utils/i18n';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import LinkedEntrySelector from 'resilience/diagram/components/dialogs/LinkedEntrySelector/LinkedEntrySelector';
import { IdOnly } from 'app/types';
import useSnackbar from 'common/hooks/useSnackbar';
import Loading from 'common/components/Loading/Loading';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { useBtrsLinkBowTieUsingPutMutation } from 'bowtie/rtkApi';
import { Field, RegisterBowTieRest } from 'api/generated/types';
import { useTmrsGetAllRegistersWithBowTieMslUsingGetQuery } from 'register/rtkApi';

type Props = {
    visible: boolean;
    onClose: () => void;
    onConfirm: () => void;
    diagramId: number;
};

const StyledList = styled(List)(() => ({
    border: '1px solid gray',
    maxHeight: '185px',
    overflow: 'auto',
}));

const BowTieAddLinkDialog: React.FC<Props> = ({ visible, onClose, diagramId, onConfirm }: Props) => {
    const { enqueueError, enqueueSuccess } = useSnackbar();
    const [selectedRegister, setSelectedRegister] = useState<RegisterBowTieRest>();
    const [selectedField, setSelectedField] = useState<Field>();
    const [selectorVisible, setSelectorVisible] = useState<boolean>(false);
    const [registerSelectorVisible, setRegisterSelectorVisible] = useState<boolean>(visible);
    const [linkingInProgress, setLinkingInProgress] = useState<boolean>(false);

    const { data: registerBowTieFields, isLoading, isError, error } = useTmrsGetAllRegistersWithBowTieMslUsingGetQuery();
    const [linkEntryToBowTie] = useBtrsLinkBowTieUsingPutMutation();

    useEffect(() => {
        setRegisterSelectorVisible(visible);
    }, [visible]);

    const confirmSelection = async (entry: IdOnly) => {
        setSelectorVisible(false);
        if (selectedRegister && selectedField && selectedRegister.id) {
            setLinkingInProgress(true);
            try {
                await linkEntryToBowTie({
                    bowTieId: diagramId,
                    regId: selectedRegister.id,
                    entryId: entry.id,
                    fieldBowTieRest: selectedField,
                }).unwrap();
                onConfirm();
                enqueueSuccess(strings('bowtie:message.registerEntryLinked'));
            } catch (_err) {
                enqueueError(strings('bowtie:message.registerEntryLinkError'));
            } finally {
                setLinkingInProgress(false);
            }
        }
    };

    const renderContent = () => {
        if (linkingInProgress) {
            return <Loading message={strings('bowtie:messages.info.linking')} />;
        }

        if (isLoading) {
            return (
                <Box
                    display={'flex'}
                    flex={1}
                    justifyContent="center"
                >
                    <CircularProgress size={20} />
                </Box>
            );
        }

        if (isError) {
            return <Typography color={'error'}>{strings('ermErrors:loading_failed') + error}</Typography>;
        }

        return (
            <StyledList
                dense
                disablePadding
            >
                {registerBowTieFields
                    ?.filter((item) => !!item)
                    ?.map((register) =>
                        register.fields?.map((field) => {
                            if (!field || field.fieldId === undefined) {
                                return null;
                            }
                            return (
                                <ListItemButton
                                    key={register.id + field?.fieldId}
                                    selected={register.id === selectedRegister?.id && field.fieldId === selectedField?.fieldId}
                                    onClick={() => {
                                        setSelectedRegister(register);
                                        setSelectedField(field);
                                    }}
                                    data-testid={`${register.label} - ${field.label}`}
                                >
                                    <ListItemText>{`${register.label} - ${field.label}`}</ListItemText>
                                </ListItemButton>
                            );
                        }),
                    )}
            </StyledList>
        );
    };

    return (
        <>
            <Dialog
                visible={registerSelectorVisible}
                title={strings('register:label.relevantRegisters')}
                height={400}
                width={500}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={onClose}
                            dataTestId="button-cancel"
                        >
                            {strings('common:button.cancel')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            type="submit"
                            dataTestId="button-confirm"
                            disabled={selectedRegister === undefined || linkingInProgress || isLoading}
                            onClick={() => {
                                setSelectorVisible(true);
                                onClose();
                            }}
                        >
                            {strings('ermMessages:btn_okay')}
                        </Button>
                    </DialogActions>
                }
            >
                <Grid
                    container
                    direction={'column'}
                    spacing={1}
                >
                    <Grid item>
                        <Typography>{strings('bowtie:messages.info.registerLink')}</Typography>
                    </Grid>
                    <Grid item>
                        <Typography>{`${strings('register:label.registers')}:`}</Typography>
                    </Grid>
                    <Grid item>{renderContent()}</Grid>
                </Grid>
            </Dialog>
            {selectedRegister && selectorVisible && (
                <LinkedEntrySelector
                    title={selectedRegister.label!}
                    registerId={selectedRegister.id}
                    onClose={() => setSelectorVisible(false)}
                    onConfirm={confirmSelection}
                    selectButtonLabel={strings('common:button.ok')}
                />
            )}
        </>
    );
};

export default BowTieAddLinkDialog;
