import { strings } from 'common/utils/i18n';
import React from 'react';
import { render, screen } from 'test/utils';
import { waitFor } from '@testing-library/react';
import BowTieLinksDialog from './BowTieLinksDialog';
import { RegisterBowTieRest } from 'api/generated/types';

jest.mock('register/rtkApi', () => {
    const actualRtkApi = jest.requireActual('register/rtkApi');
    return {
        ...actualRtkApi,
        useTmrsGetAllRegistersWithBowTieMslUsingGetQuery: () => ({
            data: [
                {
                    id: 1111,
                    tableName: 'test name TEST',
                    label: 'register-test-label',
                    fields: [
                        {
                            fieldId: '1112',
                            fieldName: 'field text TEST',
                            label: 'field-test-label',
                        },
                        {
                            fieldId: '1113',
                            fieldName: 'field text 2',
                            label: 'test label text 2222222',
                        },
                    ],
                },
            ] as RegisterBowTieRest[],
            isLoading: false,
            isSuccess: true,
            isError: false,
        }),
    };
});

jest.mock('bowtie/rtkApi', () => {
    const actualRtkApi = jest.requireActual('bowtie/rtkApi');
    return {
        ...actualRtkApi,
        useBtrsGetEntriesUsingGetQuery: () => ({
            data: [
                {
                    entryId: 1000000,
                    label: 'Processes',
                    displayValue: 'nice title',
                    tableName: 'table_112810',
                    regId: 938,
                    appId: 1062,
                    fields: [{ label: 'BowTieTest', fieldId: '104518', fieldName: 'col_112880' }],
                },
            ],
            isLoading: false,
            isSuccess: true,
            isError: false,
        }),
    };
});

describe('<BowTieLinksDialog />', () => {
    const onClose = jest.fn();

    const setup = () => {
        return render(
            <BowTieLinksDialog
                visible={true}
                onClose={onClose}
                diagramId={111111}
            />,
        );
    };

    it('was rendered', async () => {
        setup();
        expect(document.body).toMatchSnapshot();
        const confirmButton = screen.getByRole('button', { name: strings('ermMessages:btn_okay') });
        expect(confirmButton).toBeInTheDocument();
        await waitFor(() => {
            expect(confirmButton).toBeEnabled();
        });
    });

    it('ok button invokes action to close the dialog', async () => {
        const { user } = setup();
        const confirmButton = screen.getByRole('button', { name: strings('ermMessages:btn_okay') });

        expect(confirmButton).toBeInTheDocument();
        expect(confirmButton).toBeVisible();

        await user.click(confirmButton);
        expect(onClose).toHaveBeenCalled();
    });

    it('add new link button invokes action of opening a BowTieAddLinkDialog', async () => {
        const { user } = setup();

        const addLinkButton = screen.getByRole('button', { name: strings('common:label.addLink') });
        expect(addLinkButton).toBeInTheDocument();
        expect(addLinkButton).toBeVisible();
        expect(addLinkButton).toBeEnabled();
        await user.click(addLinkButton);
        expect(screen.getByRole('dialog', { hidden: false })).toBeInTheDocument();
    });
});
