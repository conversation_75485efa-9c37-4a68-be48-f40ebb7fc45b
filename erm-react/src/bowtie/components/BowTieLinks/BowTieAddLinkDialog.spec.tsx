import { strings } from 'common/utils/i18n';
import React from 'react';
import { render, screen } from 'test/utils';
import BowTieAddLinkDialog from './BowTieAddLinkDialog';
import Loading from '@protecht/ui-library/library/components/Loading';
import { RegisterBowTieRest } from 'api/generated/types';
import { ViewRest } from 'api/generated/types';
import { mockedPublishedItems } from 'resilience/mocks/api.mock';
import { mockResourceRegister } from 'resilience/mocks/ResourceRegister';

const viewData = {
    views: [],
} as ViewRest;

jest.mock('register/rtkApi', () => {
    const actualRtkApi = jest.requireActual('register/rtkApi');
    return {
        ...actualRtkApi,
        useTmrsGetRegisterConfigUsingGet1Query: jest.fn(() => ({
            data: mockResourceRegister,
            isLoading: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        })),
        useGetRegisterEntriesSearchPostQuery: jest.fn(() => ({
            data: mockedPublishedItems,
            isLoading: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        })),
        useTmrsGetAllRegistersWithBowTieMslUsingGetQuery: () => ({
            data: [
                {
                    id: 1111,
                    tableName: 'test name TEST',
                    label: 'register-test-label',
                    fields: [
                        {
                            fieldId: '1112',
                            fieldName: 'field text TEST',
                            label: 'field-test-label',
                        },
                        {
                            fieldId: '1113',
                            fieldName: 'field text 2',
                            label: 'test label text 2222222',
                        },
                    ],
                },
            ] as RegisterBowTieRest[],
            isLoading: false,
            isSuccess: true,
            isError: false,
        }),
    };
});

jest.mock('bowtie/rtkApi', () => {
    const actualRtkApi = jest.requireActual('bowtie/rtkApi');
    return {
        ...actualRtkApi,
        useGetEntriesUsingGet1Query: () => ({
            data: [
                {
                    entryId: 1000000,
                    label: 'Processes',
                    displayValue: 'nice title',
                    tableName: 'table_112810',
                    regId: 938,
                    appId: 1062,
                    fields: [{ label: 'BowTieTest', fieldId: '104518', fieldName: 'col_112880' }],
                },
            ],
            isLoading: false,
            isSuccess: true,
            isError: false,
        }),
    };
});

jest.mock('view/rtkApi', () => ({
    ...jest.requireActual('view/rtkApi'),
    useVrsGetViewsUsingGetQuery: jest.fn(() => ({
        data: viewData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
}));

describe('<BowTieAddLinkDialog />', () => {
    const onClose = jest.fn();
    const onConfirm = jest.fn();
    const diagramId = 111111;

    const setup = () => {
        return render(
            <React.Suspense
                fallback={
                    <div className="expand">
                        <Loading />
                    </div>
                }
            >
                <BowTieAddLinkDialog
                    visible={true}
                    onClose={onClose}
                    onConfirm={onConfirm}
                    diagramId={diagramId}
                />
            </React.Suspense>,
        );
    };

    const checkOnConfirmButtonProperties = (expectedEnabledState) => {
        const onConfirm = screen.getByRole('button', { name: strings('ermMessages:btn_okay') });
        expect(onConfirm).toBeInTheDocument();
        expect(onConfirm).toBeVisible();
        if (expectedEnabledState) {
            expect(onConfirm).toBeEnabled();
        } else {
            expect(onConfirm).toBeDisabled();
        }
        return onConfirm;
    };

    it('was rendered', async () => {
        setup();
        expect(document.body).toMatchSnapshot();
        expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    it('cancel button invokes action on cancel', async () => {
        const { user } = setup();
        const onCancel = screen.getByRole('button', { name: strings('common:button.cancel') });
        expect(onCancel).toBeInTheDocument();
        expect(onCancel).toBeVisible();
        expect(onCancel).toBeEnabled();
        await user.click(onCancel);
        expect(onClose).toHaveBeenCalled();
    });

    it('at least one item from the list must be selected, before making confirm button able', async () => {
        const { user } = setup();

        const firsElement = await screen.findByTestId('register-test-label - field-test-label');
        expect(firsElement).toBeInTheDocument();
        await user.click(firsElement);

        checkOnConfirmButtonProperties(true);
    });

    it('if any of list items is not selected, disable confirm button', async () => {
        setup();

        const firsElement = screen.getByTestId('register-test-label - field-test-label');
        expect(firsElement).toBeInTheDocument();

        checkOnConfirmButtonProperties(false);
    });

    it('displays dialog upon confirm button click', async () => {
        const { user } = setup();

        const firsElement = screen.getByTestId('register-test-label - field-test-label');
        expect(firsElement).toBeInTheDocument();
        await user.click(firsElement);

        const confirmButton = checkOnConfirmButtonProperties(true);
        await user.click(confirmButton);

        expect(screen.getByRole('dialog', { hidden: false })).toBeInTheDocument();
    });
});
