import React from 'react';
import { waitFor } from '@testing-library/react';
import { render, screen } from 'test/utils';
import BowTieDetailsGrid from './BowTieDetailsGrid';
import { BowTieStatus } from 'bowtie/types';
import { mockBowTieEntry } from 'bowtie/api.mock';
import { strings } from 'common/utils/i18n';
import { TagTypeRest } from 'api/generated/types';

const TagTypeMockData = {
    id: 3333,
    name: 'test-value-2',
    context: 'test-context-2',
};
const TagRestMockDataArray = [TagTypeMockData];

const mockUpdateDiagram = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve({ id: 17380, diagram: mockBowTieEntry }),
}));

const mockVerifyUserBUs = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve({ businessUnits: [17380] }),
}));

jest.mock('library/rtkApi', () => ({
    ...jest.requireActual('library/rtkApi'),
    useTrsGetTagTypesUsingGetQuery: jest.fn(() => ({
        ...(TagRestMockDataArray as TagTypeRest[]),
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
}));

jest.mock('bowtie/rtkApi', () => ({
    ...jest.requireActual('bowtie/rtkApi'),
    useBtrsGetDiagramUsingGetQuery: jest.fn(() => ({ ...mockBowTieEntry, isLoading: false, isSuccess: true, isError: false, refetch: jest.fn() })),
    useUpdateDiagramUsingPut1Mutation: () => [
        mockUpdateDiagram,
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],
    useVerifyUserBUsUsingPutMutation: () => [
        mockVerifyUserBUs,
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],
}));

describe('<BowTieDetailsGrid />', () => {
    const onSelectedTagsChange = jest.fn();
    const onDescriptionChange = jest.fn();
    const onNameChange = jest.fn();
    const onSelectedBusChange = jest.fn();

    const setup = (status = BowTieStatus.Published) => {
        const testEntry = { ...mockBowTieEntry, status };

        return render(
            <BowTieDetailsGrid
                data={testEntry}
                onSelectedTagsChange={onSelectedTagsChange}
                onDescriptionChange={onDescriptionChange}
                onNameChange={onNameChange}
                onSelectedBusChange={onSelectedBusChange}
            />,
        );
    };

    it('was rendered', async () => {
        const view = setup();
        await waitFor(() => expect(view.container).toBeInTheDocument());
        expect(view.container).toMatchSnapshot();
    });

    it('does not render the publishedForBusinessUnits label when the bowtie status is draft', async () => {
        setup(BowTieStatus.Draft);

        const publishedForBusinessUnitsLabel = screen.queryByText(strings('bowtie:file.label.publishedForBusinessUnits'));
        await waitFor(() => expect(publishedForBusinessUnitsLabel).not.toBeInTheDocument());
    });

    it('renders the publishedForBusinessUnits label when the bowtie status is published', async () => {
        setup(BowTieStatus.Published);

        const publishedForBusinessUnitsLabel = await screen.findByText(strings('bowtie:file.label.publishedForBusinessUnits'));
        expect(publishedForBusinessUnitsLabel).toBeInTheDocument();
    });
});
