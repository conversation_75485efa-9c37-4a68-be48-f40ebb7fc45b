import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Loading from 'common/components/Loading/Loading';
import { strings } from 'common/utils/i18n';
import { isEmptyString, isIdsArrayChanged } from 'common/utils/functions';
import { TagTable } from 'library/types';
import { faLink } from '@fortawesome/pro-regular-svg-icons';
import Box from '@mui/material/Box';
import DialogActions from '@mui/material/DialogActions';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import BowTieGrid from './BowTieDetailsGrid';
import IconText from '@protecht/ui-library/library/components/IconText';
import { useBtrsGetDiagramUsingGetQuery, useBtrsUpdateDiagramUsingPutMutation, useBtrsVerifyUserBUsUsingPutMutation } from 'bowtie/rtkApi';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import { BowTieDiagramRest, IdWithNameRest } from 'api/generated/types';
import { getReactRoot } from 'config';
import { AlertType } from '@protecht/ui-library/library/types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationCircle } from '@fortawesome/pro-solid-svg-icons';
import { styled } from '@mui/material/styles';
import { theme } from '@protecht/ui-library/library/theme/theme';

interface Props {
    visible: boolean;
    detailId: number;
    onClose: () => void;
    refreshContent?: () => void;
    onCopyLink: () => void;
}

const StyledBox = styled(Box)(() => ({
    marginTop: '2px',
    float: 'none',
    wordWrap: 'break-word',
}));

const BowTieDetails: React.FC<Props> = (props: Props) => {
    const { visible, onClose, detailId } = props;

    const [data, setData] = useState<BowTieDiagramRest>();
    const [name, setName] = useState<string>();
    const [description, setDescription] = useState<string>();
    const [selectedBus, setSelectedBus] = useState<IdWithNameRest[]>();
    const [selectedTags, setSelectedTags] = useState<IdWithNameRest[]>();
    const [errorMessage, setError] = useState<string | undefined>(undefined);
    const [updateInProgress, setUpdateInProgress] = useState<boolean>(false);
    const [isEditBuWarningVisible, setIsEditBuWarningVisible] = useState<boolean>(false);

    const { showConfirmationAlert } = useConfirmationAlert();

    const [updateDiagram] = useBtrsUpdateDiagramUsingPutMutation();
    const [verifyUserBUs] = useBtrsVerifyUserBUsUsingPutMutation();

    const {
        data: diagram,
        isFetching: diagramLoading,
        refetch: refetchDiagram,
        error: diagramError,
    } = useBtrsGetDiagramUsingGetQuery({ id: detailId, withDiagram: false }, { refetchOnMountOrArgChange: true });

    const derivedDiagramDetails = useMemo(() => {
        if (diagram) {
            return {
                selectedTags: diagram.tags || [],
            };
        }
    }, [diagram]);

    const setParameters = useCallback(
        (data?: BowTieDiagramRest) => {
            if (data) {
                setName(data.name || '');
                setDescription(data.description || '');
                setSelectedBus(data.businessUnits || []);
                setSelectedTags(data.tags || []);
            }
        },
        [setName, setDescription, setSelectedBus, setSelectedTags],
    );

    useEffect(() => {
        const refetch = async () => {
            await refetchDiagram()
                .unwrap()
                .then((data) => {
                    setData(data);
                    setParameters(data);
                });
        };
        if (visible) {
            void refetch();
        }
    }, [visible, detailId, refetchDiagram, setParameters]);

    const renderCopyLink = () => {
        return (
            <Link
                component="button"
                variant="body2"
                align="left"
                onClick={() => {
                    void navigator.clipboard.writeText(window.location.href.replace('recent', `${detailId}/diagram`));
                    props.onCopyLink();
                }}
                style={{ marginRight: 'auto', padding: '8px' }}
            >
                <IconText icon={faLink}>
                    <Typography variant="body1">{strings('common:button.copyLink')}</Typography>
                </IconText>
            </Link>
        );
    };

    const handleSave = async () => {
        if (selectedBus && selectedBus.length > 0) {
            const busIds = selectedBus.map((item) => item.id).filter((id): id is number => id !== undefined);

            const accessToBUs: boolean = await verifyUserBUs({ body: busIds }).unwrap();
            if (accessToBUs) {
                void update();
            } else {
                setIsEditBuWarningVisible(true);
            }
        } else {
            void update();
        }
    };

    function isFetchBaseQueryError(error: any): error is FetchBaseQueryError {
        return error && typeof error === 'object' && 'status' in error;
    }

    const update = async () => {
        try {
            setError(undefined);
            setUpdateInProgress(true);
            const updatedData = {
                ...data,
                name,
                description,
                businessUnits: selectedBus,
                tags: selectedTags?.filter((value, index: number, array) => array.findIndex((tag: TagTable) => tag.id === value.id) === index) || [],
            } as BowTieDiagramRest;
            const result = await updateDiagram({ id: detailId!, withDiagram: true, bowTieDiagramRest: updatedData }).unwrap();

            setData(result);
            if (props.refreshContent) {
                props.refreshContent();
            }
            onClose();
        } catch (err) {
            if (isFetchBaseQueryError(err)) {
                setError(err.data as string);
            } else {
                setError(strings('bowtie:message.dataUpdateError'));
            }
        } finally {
            setUpdateInProgress(false);
        }
    };

    const close = () => {
        if (isDataChanged()) {
            showConfirmationAlert({
                onConfirm: () => {
                    onClose();
                    setError(undefined);
                },
                type: AlertType.Error,
                title: strings('common:title.discardChanges'),
                cancelButtonLabel: strings('common:button.cancel'),
                confirmButtonLabel: strings('common:button.discard'),
                isCancelPrimary: true,
            });
        } else {
            setError(undefined);
            onClose();
        }
    };

    const isDataChanged = () => {
        return (
            isNameChanged() ||
            isDescriptionChanged() ||
            isIdsArrayChanged(typeof data === 'undefined' ? undefined : data.businessUnits, selectedBus) ||
            isIdsArrayChanged(typeof data === 'undefined' ? undefined : data.tags, selectedTags)
        );
    };

    const isDescriptionChanged = () => {
        if (typeof data === 'undefined' || typeof data.description === 'undefined') {
            return !isEmptyString(description);
        }
        return description !== data.description;
    };

    const isNameChanged = () => {
        if (typeof data === 'undefined' || typeof data.name === 'undefined') {
            return !isEmptyString(name);
        }
        return name !== data.name;
    };

    const handleSelectedTagsChange = useCallback((value) => {
        setSelectedTags(value);
    }, []);

    const handleSelectedBusChange = useCallback((value) => {
        setSelectedBus(value);
    }, []);

    const handleSelectedNameChange = useCallback((value) => {
        setName(value);
    }, []);

    const handleSelectedDescriptionChange = useCallback((value) => {
        setDescription(value);
    }, []);

    useEffect(() => {
        setData(diagram);
        setParameters(diagram);
    }, [diagram, setParameters]);

    return (
        <>
            <Dialog
                visible={visible}
                title={strings('bowtie:title.bowTieDetails')}
                onOutsideClickClose={close}
                width={700}
                height={750}
                dialogContainer={getReactRoot()}
                dialogActions={
                    <DialogActions>
                        {renderCopyLink()}
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={close}
                            dataTestId="button-cancel"
                        >
                            {strings('ermMessages:btn_cancel')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            disabled={updateInProgress || diagramLoading}
                            onClick={handleSave}
                            dataTestId="button-confirm"
                        >
                            {strings('ermConstants:save')}
                        </Button>
                    </DialogActions>
                }
            >
                {diagramLoading ? (
                    <Loading />
                ) : (
                    <Box
                        display="flex"
                        flex={1}
                        flexDirection="column"
                        justifyContent="space-between"
                    >
                        <BowTieGrid
                            data={data}
                            name={name}
                            description={description}
                            selectedTags={derivedDiagramDetails?.selectedTags || []}
                            selectedBus={selectedBus}
                            onSelectedBusChange={handleSelectedBusChange}
                            onNameChange={handleSelectedNameChange}
                            onDescriptionChange={handleSelectedDescriptionChange}
                            onSelectedTagsChange={handleSelectedTagsChange}
                        />

                        {(errorMessage || diagramError) && (
                            <StyledBox
                                component={'span'}
                                display={'flex'}
                                alignItems={'center'}
                            >
                                <FontAwesomeIcon
                                    color={theme.palette.error.main}
                                    icon={faExclamationCircle}
                                    style={{ marginRight: '8px' }}
                                />
                                <Typography variant={'body1'}>
                                    {isFetchBaseQueryError(diagramError) ? `${JSON.stringify(diagramError.data)}` : errorMessage}
                                </Typography>
                            </StyledBox>
                        )}
                    </Box>
                )}
            </Dialog>
            <Dialog
                title={strings('bowtie:title.editToBusinessUnit')}
                visible={isEditBuWarningVisible}
                onOutsideClickClose={() => setIsEditBuWarningVisible(false)}
                width={700}
                height={200}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={() => setIsEditBuWarningVisible(false)}
                            dataTestId="button-cancel"
                        >
                            {strings('common:button.cancel')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            type="submit"
                            dataTestId="button-confirm"
                            onClick={async () => {
                                await update()
                                    .then(() => {
                                        setIsEditBuWarningVisible(false);
                                        if (props.refreshContent) {
                                            props.refreshContent();
                                        }
                                    })
                                    .finally(undefined);
                            }}
                        >
                            {strings('ermMessages:btn_okay')}
                        </Button>
                    </DialogActions>
                }
            >
                <Typography variant="body2">{strings('bowtie:message.editToOtherBu')}</Typography>
            </Dialog>
        </>
    );
};

export default BowTieDetails;
