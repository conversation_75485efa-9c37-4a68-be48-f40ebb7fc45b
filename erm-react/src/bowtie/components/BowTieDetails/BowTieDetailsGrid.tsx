import React, { ChangeEvent, useCallback } from 'react';
import { faExclamationCircle } from '@fortawesome/pro-light-svg-icons';
import { faLockAlt, faLockOpen } from '@fortawesome/pro-solid-svg-icons';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import { BowTieStatus } from 'bowtie/types';
import FieldWrapper from 'common/components/FieldWrapper';
import MultiselectPicker from 'common/components/MultiselectPicker';
import { SelectorType } from 'common/types';
import { strings } from 'common/utils/i18n';
import { TagTable } from 'library/types';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import TagPicker from 'library/components/Tag/TagPicker';
import EditableText from '@protecht/ui-library/library/components/EditableText';
import IconText from '@protecht/ui-library/library/components/IconText';
import { BowTieDiagramRest, BusinessUnitSimpleRest, IdWithNameRest } from 'api/generated/types';
import { useLazyBursFilterBusinessUnitsUsingGetQuery } from 'common/api/businessUnits';
import { SEARCH_RESULT_LIMIT } from 'common/components/SuggestionsInput/InputLookup';
import { IdWithNameAndStatusRest } from 'app/types';

interface BowTieGridProps {
    data?: BowTieDiagramRest;
    name?: string;
    description?: string;
    selectedTags?: IdWithNameRest[];
    selectedBus?: IdWithNameRest[];
    onSelectedBusChange: (value: IdWithNameRest[]) => void;
    onNameChange: (value: string) => void;
    onDescriptionChange: (value: string) => void;
    onSelectedTagsChange: (value: TagTable[]) => void;
}

export default function BowTieGrid({
    data,
    name,
    description,
    selectedBus,
    selectedTags,
    onSelectedTagsChange,
    onSelectedBusChange,
    onNameChange,
    onDescriptionChange,
}: BowTieGridProps) {
    const [triggerSearch] = useLazyBursFilterBusinessUnitsUsingGetQuery();

    const getMatchingBUs = useCallback((node: BusinessUnitSimpleRest[] | undefined, arr: BusinessUnitSimpleRest[], matchingIds: number[]) => {
        node?.forEach((tree) => {
            if (matchingIds?.includes(tree.id!)) {
                arr.push(tree);
            }
            if (Array.isArray(tree?.children) && tree?.children.length > 0) {
                getMatchingBUs(tree.children, arr, matchingIds);
            }
        });
    }, []);

    const onDataLoad = useCallback(
        async (query: string) => {
            return await triggerSearch({ value: query })
                .unwrap()
                .then((res) => {
                    const matchingIds = res?.matchingBusinessunits;

                    if (Array.isArray(matchingIds) && matchingIds?.length > 0) {
                        const matchingBUs: BusinessUnitSimpleRest[] = [];

                        getMatchingBUs(res?.businessUnitSimpleRests, matchingBUs, matchingIds);
                        return matchingBUs.slice(0, SEARCH_RESULT_LIMIT) as IdWithNameAndStatusRest[];
                    } else {
                        return [];
                    }
                })
                .catch(() => {
                    return [];
                });
        },
        [triggerSearch, getMatchingBUs],
    );

    const renderBusinessUnits = useCallback(
        (bowTie?: BowTieDiagramRest) => {
            if (!bowTie || bowTie.status !== BowTieStatus.Published) {
                return <div />;
            }

            return (
                <FieldWrapper label={strings('bowtie:file.label.publishedForBusinessUnits')}>
                    <MultiselectPicker
                        data-testid="multiselect-picker"
                        type={SelectorType.BUSINESS_UNIT}
                        selected={selectedBus}
                        onSelect={onSelectedBusChange}
                        onDataLoad={onDataLoad}
                    />
                </FieldWrapper>
            );
        },
        [onDataLoad, onSelectedBusChange, selectedBus],
    );

    const getStatusIcon = (status?: string) => {
        switch (status) {
            case BowTieStatus.Draft: {
                return faLockAlt;
            }
            case BowTieStatus.Published: {
                return faLockOpen;
            }
            case BowTieStatus.Template: {
                return faLockOpen;
            }
            default: {
                return faExclamationCircle;
            }
        }
    };

    const getStatusText = (status?: string): string => {
        switch (status) {
            case BowTieStatus.Draft: {
                return strings('bowtie:labels.bowTieStatus.draft');
            }
            case BowTieStatus.Published: {
                return strings('bowtie:labels.bowTieStatus.published');
            }
            case BowTieStatus.Template: {
                return strings('bowtie:labels.bowTieStatus.template');
            }
            default: {
                return '';
            }
        }
    };

    const handleNameChange = (event: ChangeEvent<HTMLInputElement>) => {
        onNameChange(event.target.value);
    };

    return (
        <GridContainer
            container
            spacing={2}
            direction="column"
        >
            <Grid item>
                <EditableText
                    value={name}
                    fontSize="24px"
                    onChange={(event) => handleNameChange(event)}
                />
            </Grid>
            <Grid item>
                <IconText icon={getStatusIcon(data?.status)}>
                    <Typography variant="body3">{getStatusText(data?.status)}</Typography>
                </IconText>
            </Grid>
            <Grid
                item
                container
                spacing={1}
                direction="column"
            >
                <Grid item>
                    <Typography
                        variant="body1"
                        color="textSecondary"
                    >
                        {strings('ermConstants:description')}
                    </Typography>
                </Grid>
                <Grid item>
                    <Input
                        multiline={true}
                        maxRows={4}
                        value={description}
                        onChange={(event) => onDescriptionChange(event.target.value)}
                    />
                </Grid>
            </Grid>
            <Grid
                item
                container
                spacing={1}
            >
                <Grid
                    item
                    xs={12}
                    sm={4}
                >
                    <Typography
                        variant="body1"
                        color="textSecondary"
                    >
                        {strings('common:label.createdBy')}
                    </Typography>
                </Grid>
                <Grid
                    item
                    xs={12}
                    sm={8}
                >
                    <RightAlignedText variant="body1">{`${data?.createdBy}, on ${data?.createDateFormatted}`}</RightAlignedText>
                </Grid>
                <Grid
                    item
                    xs={12}
                    sm={4}
                >
                    <Typography
                        variant="body1"
                        color="textSecondary"
                    >
                        {strings('ermConstants:last_modified_by')}
                    </Typography>
                </Grid>
                <Grid
                    item
                    xs={12}
                    sm={8}
                >
                    <RightAlignedText variant="body1">{`${data?.lastModifiedBy}, on ${data?.lastModifiedDateFormatted}`}</RightAlignedText>
                </Grid>
            </Grid>
            <Grid item>{renderBusinessUnits(data)}</Grid>
            <Grid item>
                <Typography variant="body1">{strings('ermConstants:tags')}</Typography>
            </Grid>
            <Grid item>
                <TagPicker
                    selectedTags={selectedTags || []}
                    onTagsSelected={onSelectedTagsChange}
                />
            </Grid>
        </GridContainer>
    );
}

const GridContainer = styled(Grid)({
    flexWrap: 'nowrap',
    overflowY: 'auto',
    overflowX: 'hidden',
});

const RightAlignedText = styled(Typography)(({ theme }) => ({
    textAlign: 'right',
    [theme.breakpoints.down('sm')]: {
        textAlign: 'left',
    },
}));
