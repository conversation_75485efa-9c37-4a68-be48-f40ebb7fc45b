import React from 'react';
import { render, screen } from 'test/utils';
import { waitFor } from '@testing-library/react';
import BowTieDetails from './BowTieDetails';
import { mockBowTieEntry } from '../../api.mock';
import Loading from '@protecht/ui-library/library/components/Loading';
import { useBtrsGetDiagramUsingGetQuery } from 'bowtie/rtkApi';
import { TagTypeRest } from 'api/generated/types';

const TagTypeMockData = {
    id: 3333,
    name: 'test-value-2',
    context: 'test-context-2',
};
const tagRestMockDataArray = [TagTypeMockData];

const mockUpdateDiagram = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve({ id: 17380, diagram: mockBowTieEntry }),
}));

const mockVerifyUserBUs = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve({ businessUnits: [17380] }),
}));

const mockRefetch = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve(mockBowTieEntry),
}));

jest.mock('library/rtkApi', () => ({
    ...jest.requireActual('library/rtkApi'),
    useTrsGetTagTypesUsingGetQuery: jest.fn(() => ({
        ...(tagRestMockDataArray as TagTypeRest[]),
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
    useTrsSuggestTagsUsingPostMutation: () => [
        jest.fn().mockImplementation(() => []),
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],
}));

jest.mock('bowtie/rtkApi', () => ({
    ...jest.requireActual('bowtie/rtkApi'),
    useBtrsGetDiagramUsingGetQuery: jest.fn(() => ({ ...mockBowTieEntry, isLoading: false, isSuccess: true, isError: false, refetch: mockRefetch })),
    useBtrsUpdateDiagramUsingPutMutation: () => [
        mockUpdateDiagram,
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],
    useBtrsVerifyUserBUsUsingPutMutation: () => [
        mockVerifyUserBUs,
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],
}));

describe('<BowTieDetails />', () => {
    const onClose = jest.fn();
    const onCopyLink = jest.fn();

    const setup = () => {
        return render(
            <React.Suspense
                fallback={
                    <div className="expand">
                        <Loading />
                    </div>
                }
            >
                <BowTieDetails
                    visible={true}
                    detailId={17380}
                    onClose={onClose}
                    onCopyLink={onCopyLink}
                    refreshContent={jest.fn()}
                />
                ,
            </React.Suspense>,
        );
    };

    it('was rendered', async () => {
        setup();
        expect(document.body).toMatchSnapshot();
        await waitFor(() => {
            expect(useBtrsGetDiagramUsingGetQuery).toHaveBeenCalled();
        });
    });

    it('cancel button gets clicked', async () => {
        const { user } = setup();
        const cancelButton = screen.getByTestId('button-cancel');

        expect(cancelButton).toBeInTheDocument();
        expect(cancelButton).toBeEnabled();
        expect(cancelButton).toBeVisible();

        await user.click(cancelButton);
        expect(onClose).toHaveBeenCalled();
    });

    it('save button gets clicked', async () => {
        const { user } = setup();
        const saveButton = screen.getByTestId('button-confirm');

        expect(saveButton).toBeInTheDocument();
        expect(saveButton).toBeEnabled();
        expect(saveButton).toBeVisible();

        await user.click(saveButton);
        expect(onClose).toHaveBeenCalled();
    });
});
