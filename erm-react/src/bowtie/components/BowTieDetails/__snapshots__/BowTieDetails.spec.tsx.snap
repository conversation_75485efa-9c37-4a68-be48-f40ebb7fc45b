// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieDetails /> was rendered 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  >
    ,
  </div>
  <div
    class="MuiDialog-root MuiModal-root css-gb7w38-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Bow Tie Details"
    id="draggable-dialog-Bow Tie Details"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Bow Tie Details
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiBox-root css-1eto9a9"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 MuiGrid-direction-xs-column css-a6wr3i-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <div
                    style="position: relative;"
                  >
                    <div
                      class="MuiFormControl-root MuiTextField-root css-1f12z1n-MuiFormControl-root-MuiTextField-root"
                      inputmode="text"
                    >
                      <div
                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                      >
                        <input
                          aria-invalid="false"
                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                          id=":r3:"
                          type="text"
                          value=""
                        />
                        <div
                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-goi1o-MuiInputAdornment-root"
                        >
                          <svg
                            aria-hidden="true"
                            class="svg-inline--fa fa-pencil "
                            data-icon="pencil"
                            data-prefix="fas"
                            focusable="false"
                            role="img"
                            viewBox="0 0 512 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M410.3 231l11.3-11.3-33.9-33.9-62.1-62.1L291.7 89.8l-11.3 11.3-22.6 22.6L58.6 322.9c-10.4 10.4-18 23.3-22.2 37.4L1 480.7c-2.5 8.4-.2 17.5 6.1 23.7s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L387.7 253.7 410.3 231zM160 399.4l-9.1 22.7c-4 3.1-8.5 5.4-13.3 6.9L59.4 452l23-78.1c1.4-4.9 3.8-9.4 6.9-13.3l22.7-9.1 0 32c0 8.8 7.2 16 16 16l32 0zM362.7 18.7L348.3 33.2 325.7 55.8 314.3 67.1l33.9 33.9 62.1 62.1 33.9 33.9 11.3-11.3 22.6-22.6 14.5-14.5c25-25 25-65.5 0-90.5L453.3 18.7c-25-25-65.5-25-90.5 0zm-47.4 168l-144 144c-6.2 6.2-16.4 6.2-22.6 0s-6.2-16.4 0-22.6l144-144c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6z"
                              fill="currentColor"
                            />
                          </svg>
                        </div>
                        <fieldset
                          aria-hidden="true"
                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                        >
                          <legend
                            class="css-13wgbfv"
                          >
                            <span
                              class="notranslate"
                            >
                              ​
                            </span>
                          </legend>
                        </fieldset>
                      </div>
                    </div>
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-14llhli-MuiTypography-root"
                    />
                  </div>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <div
                    class="css-19n27rl"
                  >
                    <div
                      class="css-1lui99h"
                    >
                      <svg
                        aria-hidden="true"
                        class="svg-inline--fa fa-circle-exclamation "
                        data-icon="circle-exclamation"
                        data-prefix="fal"
                        focusable="false"
                        role="img"
                        viewBox="0 0 512 512"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M256 32a224 224 0 1 1 0 448 224 224 0 1 1 0-448zm0 480A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c-8.8 0-16 7.2-16 16l0 128c0 8.8 7.2 16 16 16s16-7.2 16-16l0-128c0-8.8-7.2-16-16-16zm24 224a24 24 0 1 0 -48 0 24 24 0 1 0 48 0z"
                          fill="currentColor"
                        />
                      </svg>
                    </div>
                    <div>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1flsrww-MuiTypography-root"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 MuiGrid-direction-xs-column css-1juzi88-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
                    >
                      Description
                    </p>
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                    >
                      <div
                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-multiline css-15pv5j7-MuiInputBase-root-MuiOutlinedInput-root"
                      >
                        <textarea
                          aria-invalid="false"
                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-1hoyx8a-MuiInputBase-input-MuiOutlinedInput-input"
                          id=":r4:"
                          style="height: 0px; overflow: hidden;"
                          tabindex="0"
                        />
                        <textarea
                          aria-hidden="true"
                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-1hoyx8a-MuiInputBase-input-MuiOutlinedInput-input"
                          readonly=""
                          style="visibility: hidden; position: absolute; overflow: hidden; height: 0px; top: 0px; left: 0px; transform: translateZ(0); padding-top: 0px; padding-bottom: 0px; width: 100%;"
                          tabindex="-1"
                        />
                        <fieldset
                          aria-hidden="true"
                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                        >
                          <legend
                            class="css-13wgbfv"
                          >
                            <span
                              class="notranslate"
                            >
                              ​
                            </span>
                          </legend>
                        </fieldset>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-v8g4am-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1f3xzpt-MuiGrid-root"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
                    >
                      Created by
                    </p>
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-mcsc27-MuiGrid-root"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-1cfer3c-MuiTypography-root"
                    >
                      undefined, on undefined
                    </p>
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1f3xzpt-MuiGrid-root"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
                    >
                      Last Modified By
                    </p>
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-mcsc27-MuiGrid-root"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-1cfer3c-MuiTypography-root"
                    >
                      undefined, on undefined
                    </p>
                  </div>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <div />
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-e8ctdu-MuiTypography-root"
                  >
                    Tags
                  </p>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <div
                    class="MuiBox-root css-1epd73n"
                  >
                    <button
                      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-azmj39-MuiButtonBase-root-MuiButton-root"
                      data-testid="tag-picker-select-button"
                      tabindex="0"
                      type="button"
                    >
                      <span
                        class="css-qv0y8m"
                      >
                        Add
                      </span>
                      <span
                        class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
                      >
                        <svg
                          data-icon="chevron-down"
                          fill="currentColor"
                          height="24"
                          viewBox="0 0 24 24"
                          width="24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M12.026 12.914 6.601 7.49 4.639 9.452l7.386 7.386 7.389-7.389-1.962-1.962z"
                            fill="currentColor"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </button>
                    <div
                      class="MuiBox-root css-1ixvsu7"
                    >
                      <div
                        style="margin: 0px;"
                        tabindex="0"
                      >
                        <div
                          class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 css-xeqq4s-MuiPaper-root"
                          tabindex="-1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-knqc4i-MuiDialogActions-root"
          >
            <button
              class="MuiTypography-root MuiTypography-body2 MuiTypography-alignLeft MuiLink-root MuiLink-underlineAlways MuiLink-button css-1vmtdfp-MuiTypography-root-MuiLink-root"
              style="margin-right: auto; padding: 8px;"
            >
              <div
                class="css-19n27rl"
              >
                <div
                  class="css-1lui99h"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-link "
                    data-icon="link"
                    data-prefix="far"
                    focusable="false"
                    role="img"
                    viewBox="0 0 640 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M580.3 267.2c56.2-56.2 56.2-147.3 0-203.5C526.8 10.2 440.9 7.3 383.9 57.2l-6.1 5.4c-10 8.7-11 23.9-2.3 33.9s23.9 11 33.9 2.3l6.1-5.4c38-33.2 95.2-31.3 130.9 4.4c37.4 37.4 37.4 98.1 0 135.6L433.1 346.6c-37.4 37.4-98.2 37.4-135.6 0c-35.7-35.7-37.6-92.9-4.4-130.9l4.7-5.4c8.7-10 7.7-25.1-2.3-33.9s-25.1-7.7-33.9 2.3l-4.7 5.4c-49.8 57-46.9 142.9 6.6 196.4c56.2 56.2 147.3 56.2 203.5 0L580.3 267.2zM59.7 244.8C3.5 301 3.5 392.1 59.7 448.2c53.6 53.6 139.5 56.4 196.5 6.5l6.1-5.4c10-8.7 11-23.9 2.3-33.9s-23.9-11-33.9-2.3l-6.1 5.4c-38 33.2-95.2 31.3-130.9-4.4c-37.4-37.4-37.4-98.1 0-135.6L207 165.4c37.4-37.4 98.1-37.4 135.6 0c35.7 35.7 37.6 92.9 4.4 130.9l-5.4 6.1c-8.7 10-7.7 25.1 2.3 33.9s25.1 7.7 33.9-2.3l5.4-6.1c49.9-57 47-142.9-6.5-196.5c-56.2-56.2-147.3-56.2-203.5 0L59.7 244.8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div>
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-e8ctdu-MuiTypography-root"
                  >
                    Copy Link
                  </p>
                </div>
              </div>
            </button>
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-cancel"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Cancel
              </span>
            </button>
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1m5dup3-MuiButtonBase-root-MuiButton-root"
              data-testid="button-confirm"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Save
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;
