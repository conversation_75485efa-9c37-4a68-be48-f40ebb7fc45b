// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BowTieDetailsGrid /> was rendered 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 MuiGrid-direction-xs-column css-a6wr3i-MuiGrid-root"
  >
    <div
      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
    >
      <div
        style="position: relative;"
      >
        <div
          class="MuiFormControl-root MuiTextField-root css-1f12z1n-MuiFormControl-root-MuiTextField-root"
          inputmode="text"
        >
          <div
            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
          >
            <input
              aria-invalid="false"
              class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
              id=":r1:"
              type="text"
              value=""
            />
            <div
              class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-goi1o-MuiInputAdornment-root"
            >
              <svg
                aria-hidden="true"
                class="svg-inline--fa fa-pencil "
                data-icon="pencil"
                data-prefix="fas"
                focusable="false"
                role="img"
                viewBox="0 0 512 512"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M410.3 231l11.3-11.3-33.9-33.9-62.1-62.1L291.7 89.8l-11.3 11.3-22.6 22.6L58.6 322.9c-10.4 10.4-18 23.3-22.2 37.4L1 480.7c-2.5 8.4-.2 17.5 6.1 23.7s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L387.7 253.7 410.3 231zM160 399.4l-9.1 22.7c-4 3.1-8.5 5.4-13.3 6.9L59.4 452l23-78.1c1.4-4.9 3.8-9.4 6.9-13.3l22.7-9.1 0 32c0 8.8 7.2 16 16 16l32 0zM362.7 18.7L348.3 33.2 325.7 55.8 314.3 67.1l33.9 33.9 62.1 62.1 33.9 33.9 11.3-11.3 22.6-22.6 14.5-14.5c25-25 25-65.5 0-90.5L453.3 18.7c-25-25-65.5-25-90.5 0zm-47.4 168l-144 144c-6.2 6.2-16.4 6.2-22.6 0s-6.2-16.4 0-22.6l144-144c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6z"
                  fill="currentColor"
                />
              </svg>
            </div>
            <fieldset
              aria-hidden="true"
              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
            >
              <legend
                class="css-13wgbfv"
              >
                <span
                  class="notranslate"
                >
                  ​
                </span>
              </legend>
            </fieldset>
          </div>
        </div>
        <p
          class="MuiTypography-root MuiTypography-body1 css-14llhli-MuiTypography-root"
        />
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
    >
      <div
        class="css-19n27rl"
      >
        <div
          class="css-1lui99h"
        >
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-lock-open "
            data-icon="lock-open"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 576 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M352 144c0-44.2 35.8-80 80-80s80 35.8 80 80l0 48c0 17.7 14.3 32 32 32s32-14.3 32-32l0-48C576 64.5 511.5 0 432 0S288 64.5 288 144l0 48L64 192c-35.3 0-64 28.7-64 64L0 448c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-192c0-35.3-28.7-64-64-64l-32 0 0-48z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div>
          <p
            class="MuiTypography-root MuiTypography-body3 css-1flsrww-MuiTypography-root"
          >
            Published
          </p>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 MuiGrid-direction-xs-column css-1juzi88-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
      >
        <p
          class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
        >
          Description
        </p>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
      >
        <div
          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
        >
          <div
            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-multiline css-15pv5j7-MuiInputBase-root-MuiOutlinedInput-root"
          >
            <textarea
              aria-invalid="false"
              class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-1hoyx8a-MuiInputBase-input-MuiOutlinedInput-input"
              id=":r2:"
              style="height: 0px; overflow: hidden;"
              tabindex="0"
            />
            <textarea
              aria-hidden="true"
              class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-1hoyx8a-MuiInputBase-input-MuiOutlinedInput-input"
              readonly=""
              style="visibility: hidden; position: absolute; overflow: hidden; height: 0px; top: 0px; left: 0px; transform: translateZ(0); padding-top: 0px; padding-bottom: 0px; width: 100%;"
              tabindex="-1"
            />
            <fieldset
              aria-hidden="true"
              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
            >
              <legend
                class="css-13wgbfv"
              >
                <span
                  class="notranslate"
                >
                  ​
                </span>
              </legend>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-v8g4am-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1f3xzpt-MuiGrid-root"
      >
        <p
          class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
        >
          Created by
        </p>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-mcsc27-MuiGrid-root"
      >
        <p
          class="MuiTypography-root MuiTypography-body1 css-1cfer3c-MuiTypography-root"
        >
          Some text, on 05/07/2023 15:12:59
        </p>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1f3xzpt-MuiGrid-root"
      >
        <p
          class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
        >
          Last Modified By
        </p>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-mcsc27-MuiGrid-root"
      >
        <p
          class="MuiTypography-root MuiTypography-body1 css-1cfer3c-MuiTypography-root"
        >
          Some text, on 05/07/2023 15:12:59
        </p>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
    >
      <div
        class="MuiContainer-root MuiContainer-maxWidthLg MuiContainer-disableGutters css-1g6kegs-MuiContainer-root"
        data-testid="field-wrapper-Published for Business Units"
      >
        <p
          class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
        >
          Published for Business Units
        </p>
        <div
          class="MuiBox-root css-4cxybv"
        >
          <div
            class="MuiGrid-root MuiGrid-container css-13r9tes-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13ded8f-MuiGrid-root"
            >
              <div
                class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                >
                  <input
                    aria-invalid="false"
                    class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                    id=":r3:"
                    placeholder="select a Business Unit"
                    tabindex="0"
                    type="text"
                    value=""
                  />
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                  >
                    <legend
                      class="css-13wgbfv"
                    >
                      <span
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-jrw6kt-MuiGrid-root"
            >
              <button
                class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                data-testid="button-select"
                tabindex="0"
                type="button"
              >
                <span
                  class="css-1d0doyg"
                >
                  Select
                </span>
              </button>
            </div>
          </div>
        </div>
        <div
          class="css-1tucq0r"
        >
          <div
            class="MuiBox-root css-mdugq6"
          >
            <p
              class="MuiTypography-root MuiTypography-body1 css-1yjbez4-MuiTypography-root"
            >
              No items
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
    >
      <p
        class="MuiTypography-root MuiTypography-body1 css-e8ctdu-MuiTypography-root"
      >
        Tags
      </p>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
    >
      <div
        class="MuiBox-root css-1epd73n"
      >
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-azmj39-MuiButtonBase-root-MuiButton-root"
          data-testid="tag-picker-select-button"
          tabindex="0"
          type="button"
        >
          <span
            class="css-qv0y8m"
          >
            Add
          </span>
          <span
            class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
          >
            <svg
              data-icon="chevron-down"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M12.026 12.914 6.601 7.49 4.639 9.452l7.386 7.386 7.389-7.389-1.962-1.962z"
                fill="currentColor"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </button>
        <div
          class="MuiBox-root css-1ixvsu7"
        >
          <div
            style="margin: 0px;"
            tabindex="0"
          >
            <div
              class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 css-xeqq4s-MuiPaper-root"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
