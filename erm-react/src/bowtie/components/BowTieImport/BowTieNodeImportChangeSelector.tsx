import React, { useMemo } from 'react';
import { BowTieDefinition } from 'api/generated/types';
import { NodeType } from 'bowtie/types';
import { styled, useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import NodeAutoCompleteImport from './AutoCompleteImportPopup';
import { IdWithName } from 'app/types';
import { NodeModel } from 'resilience/diagram/types';
import Box from '@mui/material/Box';
import { strings } from 'common/utils/i18n';
import { ClearOutlined, Clear, LinkBroken, WarningIcon } from '@protecht/ui-library/library/components/SVGIcons';
import LinkListItemButton from '@protecht/ui-library/library/components/LinksList/LinkListItemButton';

type Props = {
    node: NodeModel;
    onLinkedEntryChanged: (node, selectedItem) => void;
    onLinkedEntryReset: (node) => void;
    bowtieDefinition?: BowTieDefinition;
    sectionIndex: number;
    disabled?: boolean;
};

const TextBox = styled(Box)(({ theme }) => ({
    border: `1px solid ${theme.palette.protechtGrey?.grey_231}`,
    padding: '1px 0px 1px 1px',
    width: '100%',
    height: 'fit-content',
}));

const TextBoxInner = styled(Box)(() => ({
    minHeight: '28px',
    padding: '3px 10px 3px 10px',
}));

const BowTieNodeImportChangeSelector: React.FC<Props> = ({
    node,
    onLinkedEntryChanged,
    onLinkedEntryReset,
    bowtieDefinition,
    sectionIndex,
    disabled,
}: Props) => {
    const theme = useTheme();

    const clearButton = useMemo(() => {
        return (
            <Box
                sx={{
                    borderLeft: `solid 1px ${theme.palette.protechtGrey?.grey_231}`,
                    display: 'flex',
                    alignItems: 'center',
                    height: 'calc(100% + 8px)',
                }}
            >
                <LinkListItemButton
                    data-testid="clear-button"
                    onClick={() => onLinkedEntryReset(node)}
                    iconActiveColor={theme.palette.accentColors?.red}
                    icon={<ClearOutlined aria-label="clear" />}
                    iconActive={<Clear aria-label="clear" />}
                    tooltip={''}
                />
            </Box>
        );
    }, [node, onLinkedEntryReset, theme.palette.accentColors?.red, theme.palette.protechtGrey?.grey_231]);

    return (
        <Box sx={{ marginBottom: '16px' }}>
            <Box sx={{ display: 'flex', alignContent: 'space-between' }}>
                <TextBox sx={{ marginRight: '20px' }}>
                    <TextBoxInner
                        typography="body1"
                        display="flex"
                    >
                        {node.originalName}
                    </TextBoxInner>
                </TextBox>
                <Box sx={{ display: 'flex', width: '100%', flexDirection: 'row' }}>
                    <Box sx={{ display: 'flex', width: '100%', flexDirection: 'column', paddingRight: '8px' }}>
                        <TextBox>
                            <TextBoxInner
                                typography="body1"
                                display="flex"
                                sx={{ paddingRight: node.connected ? '0px' : '10px' }}
                            >
                                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                                    {disabled ? (
                                        <>
                                            <Box sx={{ width: '20px', height: '20px', marginRight: '4px', marginTop: '-2px' }}>
                                                <WarningIcon
                                                    color={theme.palette.protechtGrey?.grey_128}
                                                    height={20}
                                                    width={20}
                                                />
                                            </Box>
                                            <Box sx={{ width: '100%' }}>
                                                {node.nodeType === NodeType.Impact
                                                    ? strings('bowtie:labels.noImpactConf')
                                                    : strings('bowtie:labels.noControlConf')}
                                            </Box>
                                        </>
                                    ) : (
                                        <Box
                                            sx={{ width: '100%', color: !node?.connected ? theme.palette.protechtGrey?.grey_178 : theme.palette.text.primary }}
                                        >
                                            {node?.connected
                                                ? node.name
                                                : node?.reset
                                                ? strings('bowtie:labels.selectItem')
                                                : strings('bowtie:labels.selectManualItem')}
                                        </Box>
                                    )}
                                    {node.connected && clearButton}
                                </Box>
                            </TextBoxInner>
                        </TextBox>
                        {!node?.connected && !disabled && (
                            <Box sx={{ display: 'flex', flexDirection: 'row', marginTop: '6px', gap: '4px', alignItems: 'center' }}>
                                <LinkBroken
                                    color={theme.palette.error.main}
                                    width={22}
                                    height={22}
                                />
                                {node.reset ? (
                                    <Typography>{strings('bowtie:labels.noLinkedEntry')}</Typography>
                                ) : (
                                    <Typography>{strings('bowtie:labels.noLinkedEntryIdentified')}</Typography>
                                )}
                            </Box>
                        )}
                    </Box>
                    {!disabled && (
                        <NodeAutoCompleteImport
                            key={node?.libraryLink + node.nodeType + node.name}
                            bowtieDefinition={bowtieDefinition}
                            isEditing={true}
                            nodeType={node.nodeType as NodeType}
                            searchValue={node.name}
                            selected={node?.libraryLink as IdWithName}
                            onItemSelected={(selectedItem?: IdWithName) => onLinkedEntryChanged(node, selectedItem)}
                            isImportMode
                            sectionIndex={sectionIndex}
                            reset={node.reset}
                            connected={node.connected}
                        />
                    )}
                </Box>
            </Box>
        </Box>
    );
};

export default BowTieNodeImportChangeSelector;
