import React, { useCallback, useEffect, useMemo, useState } from 'react';

import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { IdWithName } from 'app/types';
import { BowTieRegisterEnum, NodeType } from 'bowtie/types';
import { getSelectorMetadata } from 'common/utils/definitions';
import Selector from 'common/components/Selector';
import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';
import { strings } from 'common/utils/i18n';
import useIsMounted from 'common/hooks/useIsMounted';
import { setSelectedView } from 'app/reducer';
import { useDispatch, useSelector } from 'store';
import { BowTieContractFieldsType, ColumnType, RegisterContractType, RegisterEntryRest } from 'register/types';
import { BowTieDefinition, BowTieRegister } from 'api/generated/types';
import { getContractColumn, getEntryTableRow } from 'register/utils';
import { getRegisterByType } from 'bowtie/selectors';
import { useLazyGetRegisterEntriesSearchPostQuery } from 'register/rtkApi';
import useSnackbar from 'common/hooks/useSnackbar';

type NodeAutoCompleteProps = {
    isEditing: boolean;
    nodeType: NodeType;
    searchValue?: string;
    selected?: IdWithName;
    onItemSelected: (item?: IdWithName) => void;
    bowtieDefinition?: BowTieDefinition;
    isImportMode?: boolean;
    sectionIndex: number;
};

const StyledList = styled(List)(({ theme }) => ({
    backgroundColor: theme.palette.background.default,
    boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.15)',
    borderRadius: '2px',
    maxWidth: '100%',
}));

const StyledListItemText = styled(ListItemText)(({ theme }) => ({
    display: 'flex',
    flex: 1,
    justifyContent: 'center',
    color: theme.palette.primary.main,
}));

const ListItemTypography = styled(Typography)(() => ({
    margin: '4px 0',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));

const NodeAutoComplete: React.FC<NodeAutoCompleteProps> = (props: NodeAutoCompleteProps) => {
    const { isEditing, nodeType, searchValue, selected, onItemSelected, isImportMode, sectionIndex } = props;
    const { enqueueError } = useSnackbar();
    const selectorMetadata = useMemo(() => {
        return getSelectorMetadata(nodeType);
    }, [nodeType]);

    const dispatch = useDispatch();

    const [selectorVisible, setSelectorVisible] = useState(false);
    const [data, setData] = useState<IdWithName[]>([]);
    const [loading, setLoading] = useState(false);
    const [firstChange, setFirstChange] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string>();
    const [triggerSearch] = useLazyGetRegisterEntriesSearchPostQuery();
    const riskEventRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskEventRegister));
    const riskCauseRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskCauseRegister));
    const riskControlRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskControlRegister));
    const riskImpactRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskImpactRegister));

    const riskEventNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_RISK_EVENT, BowTieContractFieldsType.BOWTIE_RISK_EVENT_NAME, riskEventRegisterContract)
            ?.columnName;
    }, [riskEventRegisterContract]);

    const riskCauseNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_RISK_CAUSE, BowTieContractFieldsType.BOWTIE_RISK_CAUSE_NAME, riskCauseRegisterContract)
            ?.columnName;
    }, [riskCauseRegisterContract]);

    const riskControlNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_CONTROL, BowTieContractFieldsType.BOWTIE_RISK_CONTROL_NAME, riskControlRegisterContract)
            ?.columnName;
    }, [riskControlRegisterContract]);

    const riskImpactNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_IMPACT, BowTieContractFieldsType.BOWTIE_RISK_IMPACT_NAME, riskImpactRegisterContract)?.columnName;
    }, [riskImpactRegisterContract]);

    const getRegisterDisplayColByTypeFromBowtieDefinition = useCallback(
        (nodeType: NodeType, bowtieDefinition: BowTieDefinition | undefined): string => {
            if (bowtieDefinition) {
                switch (nodeType) {
                    case NodeType.RiskEvent:
                    case NodeType.MainRiskEvent:
                        return riskEventNameCol || '';
                    case NodeType.Cause:
                        return riskCauseNameCol || '';
                    case NodeType.Impact:
                        return riskImpactNameCol || '';
                    case NodeType.Control:
                        return riskControlNameCol || '';
                    default:
                        return '';
                }
            }
            return '';
        },
        [riskCauseNameCol, riskControlNameCol, riskEventNameCol, riskImpactNameCol],
    );

    const isMounted = useIsMounted();

    const getRegisterByTypeFromBowtieDefinition = useCallback(
        (nodeType: NodeType, bowtieDefinition: BowTieDefinition | undefined): BowTieRegister | undefined => {
            if (bowtieDefinition) {
                switch (nodeType) {
                    case NodeType.RiskEvent:
                    case NodeType.MainRiskEvent:
                        return bowtieDefinition.riskEventRegister;
                    case NodeType.Cause:
                        return bowtieDefinition.riskCauseRegister;
                    case NodeType.Impact:
                        return bowtieDefinition.riskImpactRegister;
                    case NodeType.Control:
                        return bowtieDefinition.riskControlRegister;
                    default:
                        return undefined;
                }
            }
            return undefined;
        },
        [],
    );

    useEffect(() => {
        const loadData = async () => {
            setLoading(true);
            setErrorMessage(undefined);
            const register = getRegisterByTypeFromBowtieDefinition(nodeType, props.bowtieDefinition);
            const displayCol = getRegisterDisplayColByTypeFromBowtieDefinition(nodeType, props.bowtieDefinition);
            try {
                const searchExpression = {
                    id: 0,
                    expression: 'contains' as any, // Using 'as any' to bypass the type check temporarily
                    property: register ? displayCol : 'name',
                    type: 'STRING',
                    value: searchValue || '',
                };
                let result;
                if (register && register.registerId) {
                    result = await triggerSearch({
                        regId: register.registerId,
                        limit: 5,
                        offset: 0,
                        isSuggestion: true,
                        body: [searchExpression],
                    })
                        .unwrap()
                        .then((res) => {
                            if (res.totalCount === 0 || !res.records) {
                                return [];
                            }
                            return res;
                        })
                        .catch((error) => {
                            enqueueError(error.error);
                            return [];
                        });
                } else {
                    result = await selectorMetadata.onDataLoad?.({ limit: 5, offset: 0 }, [searchExpression]);
                }
                if (isMounted()) {
                    setData(
                        result?.records?.map((record) => {
                            const wholeEntry = register ? getEntryTableRow(record?.record as RegisterEntryRest) : undefined;
                            return {
                                id: register ? record?.record?.id : record?.id,
                                name: register ? wholeEntry![displayCol]?.simpleValue?.[0] || '' : record?.name,
                            };
                        }) || [],
                    );
                    setLoading(false);
                }
            } catch (err) {
                if (isMounted()) {
                    setErrorMessage(err);
                    setLoading(false);
                }
            }
        };

        void loadData();
    }, [
        searchValue,
        nodeType,
        selectorMetadata,
        isMounted,
        triggerSearch,
        getRegisterByTypeFromBowtieDefinition,
        getRegisterDisplayColByTypeFromBowtieDefinition,
        props.bowtieDefinition,
        enqueueError,
    ]);

    const submitSelection = (selected: IdWithName[]) => {
        onItemSelected((selected.length > 0 && selected[0]) || undefined);
        dispatch(setSelectedView(undefined));
        setSelectorVisible(false);
    };

    useEffect(() => {
        if (isImportMode && !firstChange && data.length > 0 && data[0].name === searchValue) {
            setFirstChange(true);
            onItemSelected(data[0]);
        }
    }, [isImportMode, firstChange, data, searchValue, onItemSelected]);

    const renderContent = useCallback(
        (data: IdWithName[]) => {
            if (loading) {
                return (
                    <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-autocomplete-loading`}
                    >
                        <CircularProgress size={20} />
                    </Box>
                );
            }
            if (data.length === 0) {
                return (
                    <ListItem
                        key={'node_library_autocomplete_empty'}
                        data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-autocomplete-empty`}
                    >
                        <ListItemText sx={{ color: errorMessage ? 'error.main' : 'text.secondary' }}>
                            {errorMessage ? errorMessage : strings('common:message.notFound')}
                        </ListItemText>
                    </ListItem>
                );
            }

            return data.map((item, index) => {
                return (
                    <ListItemButton
                        key={`${sectionIndex}-${index}-${item.name}-${item.id}`}
                        onMouseDown={() => onItemSelected(item)}
                        data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-autocomplete-option-${item.name}`}
                    >
                        <ListItemTypography variant="body1">{item.name}</ListItemTypography>
                    </ListItemButton>
                );
            });
        },
        [errorMessage, loading, onItemSelected, sectionIndex],
    );

    const renderSuggestions = useCallback(() => {
        if (!isEditing) {
            return null;
        }

        return (
            <StyledList
                dense
                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-autocomplete`}
            >
                {renderContent(data)}
                <ListItemButton
                    onMouseDown={() => setSelectorVisible(true)}
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-autocomplete-option-view-library`}
                >
                    <StyledListItemText>{strings('bowtie:diagram.button.viewLibrary')}</StyledListItemText>
                </ListItemButton>
            </StyledList>
        );
    }, [data, isEditing, renderContent]);

    return (
        <>
            {renderSuggestions()}
            {selectorVisible && (
                <Selector
                    visible={true}
                    onClose={() => {
                        setSelectorVisible(false);
                        dispatch(setSelectedView(undefined));
                    }}
                    onSubmit={submitSelection}
                    onDataLoad={selectorMetadata.onDataLoad}
                    columnDefinition={selectorMetadata.columnDefinition}
                    title={nodeType === NodeType.Impact ? strings('bowtie:title.impactSelect') : selectorMetadata.title}
                    type={getRegisterByTypeFromBowtieDefinition(nodeType, props.bowtieDefinition) ? ColumnType.TABLE : nodeType}
                    selected={selected ? [selected] : []}
                    filterData={
                        getRegisterByTypeFromBowtieDefinition(nodeType, props.bowtieDefinition)
                            ? { registerId: getRegisterByTypeFromBowtieDefinition(nodeType, props.bowtieDefinition)?.registerId }
                            : undefined
                    }
                />
            )}
        </>
    );
};

export default NodeAutoComplete;
