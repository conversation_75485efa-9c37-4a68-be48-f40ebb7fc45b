import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { strings } from 'common/utils/i18n';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { isEqual } from 'lodash';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import { BowTieDiagramRest } from 'api/generated/types';
import { getReactRoot } from 'config';
import { BowTieRegisterEnum, NodeType } from 'bowtie/types';
import { DataModel } from 'resilience/types';
import { useLazyBtrsValidateBowtieNameUsingGetQuery } from 'bowtie/rtkApi';
import { FormProvider } from 'react-hook-form';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import { BowTieDefinition } from 'api/generated/types';
import { getContractColumn, getEntryTableRow } from 'register/utils';
import { BowTieContractFieldsType, RegisterContractType, RegisterEntryRest } from 'register/types';
import { useLazyRdrsvGetEntryByIdUsingGet1Query } from 'register/rtkApi';
import { useSelector } from 'store';
import { getRegisterByType } from 'bowtie/selectors';
import useSnackbar from 'common/hooks/useSnackbar';
import { styled, useTheme } from '@mui/material/styles';
import useForm from 'common/hooks/forms/useForm';
import * as Yup from 'yup';
import Box from '@mui/material/Box';
import { isLibraryLinkSupported } from 'bowtie/diagram/components/DiagramComponent/components/utils';
import FileDropzone, { Attachment } from '@protecht/ui-library/library/components/FileDropzone';
import { Download } from '@protecht/ui-library/library/components/SVGIcons';
import NodeSection from './NodeSection';

type Props = {
    visible: boolean;
    onClose: () => void;
    bowtieDefinition?: BowTieDefinition;
    bowtieDiagramData?: BowTieDiagramRest;
    onConfirm: (data: BowTieDiagramRest | undefined) => void;
};

type FormValues = {
    diagramName: string;
};

const TextBox = styled(Box)(({ theme }) => ({
    border: `1px solid ${theme.palette.protechtGrey?.grey_231}`,
    padding: '1px',
}));

const TextBoxInner = styled(Box)(() => ({
    minHeight: '28px',
    padding: '3px 8px',
}));

const schema = Yup.object().shape({
    diagramName: Yup.string().required(strings('common:validators.requiredSimple')),
});

const BowTieImportDialog: React.FC<Props> = ({ visible, onClose, onConfirm, bowtieDefinition, bowtieDiagramData }: Props) => {
    const theme = useTheme();
    const [diagramData, setDiagramData] = useState<BowTieDiagramRest>();
    const methods = useForm<FormValues>({
        schema,
        mode: 'onChange',
    });
    const { control, watch, setError, clearErrors, formState, reset, setValue } = methods;
    const { errors } = formState;
    const { enqueueError } = useSnackbar();

    const [file, setFile] = useState<Attachment | null>(null);
    const [showImportDetail, setShowImportDetail] = useState<boolean>(false);

    const [getValidateBowtieName] = useLazyBtrsValidateBowtieNameUsingGetQuery();
    const [triggerEntryGet] = useLazyRdrsvGetEntryByIdUsingGet1Query();
    const riskEventRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskEventRegister));
    const riskCauseRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskCauseRegister));
    const riskControlRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskControlRegister));
    const riskImpactRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskImpactRegister));

    const oldControlsEnabled = ProtechtDictionary.ermMods.includes('OLD_CONTROLS');

    const inputValueDiagramName = watch('diagramName');

    useEffect(() => {
        if (inputValueDiagramName !== '' && visible) {
            clearErrors('diagramName');
            getValidateBowtieName({ diagramName: inputValueDiagramName })
                .unwrap()
                .catch((err) => {
                    setError('diagramName', { type: 'uniqueName', message: err.data });
                });
        }

        setDiagramData((prev) => ({ ...prev, name: inputValueDiagramName }));
    }, [bowtieDiagramData, clearErrors, getValidateBowtieName, inputValueDiagramName, setError, visible]);

    useEffect(() => {
        if (bowtieDiagramData) {
            setDiagramData(bowtieDiagramData);
            setShowImportDetail(true);
        }
    }, [bowtieDiagramData]);

    const riskEventNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_RISK_EVENT, BowTieContractFieldsType.BOWTIE_RISK_EVENT_NAME, riskEventRegisterContract)
            ?.columnName;
    }, [riskEventRegisterContract]);

    const riskCauseNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_RISK_CAUSE, BowTieContractFieldsType.BOWTIE_RISK_CAUSE_NAME, riskCauseRegisterContract)
            ?.columnName;
    }, [riskCauseRegisterContract]);

    const riskControlNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_CONTROL, BowTieContractFieldsType.BOWTIE_RISK_CONTROL_NAME, riskControlRegisterContract)
            ?.columnName;
    }, [riskControlRegisterContract]);

    const riskImpactNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_IMPACT, BowTieContractFieldsType.BOWTIE_RISK_IMPACT_NAME, riskImpactRegisterContract)?.columnName;
    }, [riskImpactRegisterContract]);

    const getRegisterDisplayColByTypeFromBowtieDefinition = useCallback(
        (nodeType: NodeType) => {
            if (bowtieDefinition) {
                switch (nodeType) {
                    case NodeType.RiskEvent:
                    case NodeType.MainRiskEvent:
                        return riskEventNameCol || '';
                    case NodeType.Cause:
                        return riskCauseNameCol || '';
                    case NodeType.Impact:
                        return riskImpactNameCol || '';
                    case NodeType.Control:
                        return riskControlNameCol || '';
                    default:
                        return '';
                }
            }
            return '';
        },
        [bowtieDefinition, riskCauseNameCol, riskControlNameCol, riskEventNameCol, riskImpactNameCol],
    );

    const getRegisterByTypeFromBowtieDefinition = useCallback(
        (nodeType: NodeType) => {
            if (bowtieDefinition) {
                switch (nodeType) {
                    case NodeType.RiskEvent:
                    case NodeType.MainRiskEvent:
                        return bowtieDefinition.riskEventRegister;
                    case NodeType.Cause:
                        return bowtieDefinition.riskCauseRegister;
                    case NodeType.Impact:
                        return bowtieDefinition.riskImpactRegister;
                    case NodeType.Control:
                        return bowtieDefinition.riskControlRegister;
                    default:
                        return undefined;
                }
            }
            return undefined;
        },
        [bowtieDefinition],
    );

    const handleDrop = useCallback(
        (data: Attachment) => {
            if (!data) {
                setFile(null);
                setDiagramData(undefined);
                setValue('diagramName', '');
                return;
            }

            if (data && data.file.type === 'application/json') {
                setFile(data);

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const loadedJson: string = e.target?.result as string;
                        const importedData = JSON.parse(loadedJson);
                        setDiagramData(importedData);

                        setValue('diagramName', importedData.name);

                        clearErrors('diagramName');
                        getValidateBowtieName({ diagramName: importedData.name })
                            .unwrap()
                            .catch((err) => {
                                setError('diagramName', { type: 'uniqueName', message: err.data });
                            });
                    } catch (error) {
                        enqueueError(strings('bowtie:labels.invalidJSONFile'), error);
                    }
                };
                reader.readAsText(data.file);
            } else {
                enqueueError(strings('bowtie:labels.dropValidJSONFile'));
            }
        },
        [clearErrors, enqueueError, getValidateBowtieName, inputValueDiagramName, setError, setValue],
    );

    const onLinkedEntryReset = useCallback(
        async (node) => {
            if (!node) {
                return;
            }

            // Find the node in the current state
            const foundNodeIndex = (diagramData?.diagramModel as DataModel)?.nodes.findIndex((item) => item.key === node.key);
            if (foundNodeIndex === -1) {
                return;
            }
            setDiagramData((prevData) => {
                const updatedNode = {
                    ...(prevData?.diagramModel as DataModel)?.nodes[foundNodeIndex],
                    libraryLink: undefined,
                    connected: false,
                    reset: true,
                };

                return {
                    ...prevData,
                    diagramModel: {
                        ...prevData?.diagramModel,
                        nodes: [
                            ...((prevData?.diagramModel as DataModel)?.nodes.slice(0, foundNodeIndex) || []),
                            updatedNode,
                            ...((prevData?.diagramModel as DataModel)?.nodes.slice(foundNodeIndex + 1) || []),
                        ],
                    },
                };
            });
        },
        [diagramData?.diagramModel, getRegisterByTypeFromBowtieDefinition, getRegisterDisplayColByTypeFromBowtieDefinition, triggerEntryGet],
    );

    const onLinkedEntryChanged = useCallback(
        async (node, selectedItem) => {
            if (!node) {
                return;
            }

            // Find the node in the current state
            const foundNodeIndex = (diagramData?.diagramModel as DataModel)?.nodes.findIndex((item) => item.key === node.key);
            if (foundNodeIndex === -1) {
                return;
            }

            if (node?.libraryLink?.name === selectedItem.name && node?.libraryLink?.id === selectedItem.id) {
                setDiagramData((prevData) => {
                    const updatedNode = {
                        ...(prevData?.diagramModel as DataModel)?.nodes[foundNodeIndex],
                        connected: true,
                        reset: false,
                        bulk: true,
                    };

                    return {
                        ...prevData,
                        diagramModel: {
                            ...prevData?.diagramModel,
                            nodes: [
                                ...((prevData?.diagramModel as DataModel)?.nodes.slice(0, foundNodeIndex) || []),
                                updatedNode,
                                ...((prevData?.diagramModel as DataModel)?.nodes.slice(foundNodeIndex + 1) || []),
                            ],
                        },
                    };
                });
                return;
            }

            const bowtieReg = getRegisterByTypeFromBowtieDefinition(node.nodeType);
            if (bowtieReg && bowtieReg.registerId && selectedItem) {
                const resourceEntry = await triggerEntryGet({ regId: bowtieReg.registerId, entryId: selectedItem.id }).unwrap();
                const wholeEntry = getEntryTableRow(resourceEntry?.record as RegisterEntryRest);

                setDiagramData((prevData) => {
                    const updatedNode = {
                        ...(prevData?.diagramModel as DataModel).nodes[foundNodeIndex],
                        libraryLink: {
                            id: selectedItem.id,
                            name: wholeEntry[getRegisterDisplayColByTypeFromBowtieDefinition(node.nodeType)]?.simpleValue?.[0] || '',
                        },
                        name: wholeEntry[getRegisterDisplayColByTypeFromBowtieDefinition(node.nodeType)]?.simpleValue?.[0] || '',
                        connected: true,
                        reset: false,
                        bulk: true,
                    };

                    if (isEqual(updatedNode, node)) {
                        return prevData;
                    }

                    // Create a new array with the updated node
                    const updatedNodes = [
                        ...((prevData?.diagramModel as DataModel)?.nodes.slice(0, foundNodeIndex) ?? []),
                        updatedNode,
                        ...((prevData?.diagramModel as DataModel)?.nodes.slice(foundNodeIndex + 1) ?? []),
                    ];

                    return {
                        ...prevData,
                        diagramModel: {
                            ...prevData?.diagramModel,
                            nodes: updatedNodes,
                        },
                    };
                });
            } else {
                setDiagramData((prevData) => {
                    const updatedNode2 = {
                        ...(prevData?.diagramModel as DataModel)?.nodes[foundNodeIndex],
                        libraryLink: selectedItem,
                        name: selectedItem?.name || '',
                        connected: true,
                        reset: false,
                        bulk: true,
                    };

                    return {
                        ...prevData,
                        diagramModel: {
                            ...prevData?.diagramModel,
                            nodes: [
                                ...((prevData?.diagramModel as DataModel)?.nodes.slice(0, foundNodeIndex) || []),
                                updatedNode2,
                                ...((prevData?.diagramModel as DataModel)?.nodes.slice(foundNodeIndex + 1) || []),
                            ],
                        },
                    };
                });
            }
        },
        [diagramData?.diagramModel, getRegisterByTypeFromBowtieDefinition, getRegisterDisplayColByTypeFromBowtieDefinition, triggerEntryGet],
    );

    return (
        <Dialog
            visible={visible}
            title={bowtieDiagramData ? strings('bowtie:button.bulkUpdate') : strings('bowtie:labels.importBowtie')}
            height={showImportDetail ? 1000 : 210}
            width={showImportDetail ? 1100 : 662}
            dialogContainer={getReactRoot()}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant="secondary"
                        type="submit"
                        size="large"
                        dataTestId="button-cancel"
                        onClick={() => {
                            setDiagramData(undefined);
                            reset({ diagramName: undefined });
                            setFile(null);
                            setShowImportDetail(false);
                            onClose();
                        }}
                    >
                        {strings('ermMessages:btn_cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-import"
                        size="large"
                        disabled={!bowtieDiagramData && (showImportDetail ? !diagramData || !!errors.diagramName : !file)}
                        onClick={() => {
                            if (showImportDetail) {
                                const diagramDataWithoutOriginalName = JSON.parse(JSON.stringify(diagramData));
                                // Iterate through nodes and delete the 'originalName' property
                                diagramDataWithoutOriginalName.diagramModel.nodes.forEach((node: any) => {
                                    if (node.originalName) {
                                        delete node.originalName;
                                    }
                                    if (node.connected) {
                                        delete node.connected;
                                    }
                                    if (node.bulk) {
                                        delete node.bulk;
                                    }
                                    if (node.reset) {
                                        delete node.reset;
                                    }
                                });
                                onConfirm(diagramDataWithoutOriginalName);
                                reset({ diagramName: undefined });
                                setDiagramData(undefined);
                            } else {
                                setShowImportDetail(true);
                            }
                        }}
                    >
                        {bowtieDiagramData ? strings('bowtie:button.update') : showImportDetail ? strings('common:button.import') : strings('common:button.ok')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                direction={'column'}
                spacing={1}
            >
                {showImportDetail && !bowtieDiagramData && (
                    <Grid
                        item
                        style={{ marginBottom: '16px' }}
                    >
                        <TextBox>
                            <TextBoxInner
                                typography="body1"
                                display="flex"
                            >
                                {strings('bowtie:message.importDialog')}
                            </TextBoxInner>
                        </TextBox>
                    </Grid>
                )}
                {!bowtieDiagramData && (
                    <Grid
                        item={showImportDetail}
                        sx={{ marginLeft: !showImportDetail ? '7px' : '0px' }}
                    >
                        <Typography variant="body2">{strings('bowtie:labels.toImport')}</Typography>
                    </Grid>
                )}

                {!bowtieDiagramData && (
                    <Grid
                        item={showImportDetail}
                        style={{
                            maxWidth: '100%',
                            display: 'flex',
                            justifyContent: 'center',
                            marginTop: showImportDetail ? 0 : 8,
                            marginLeft: !showImportDetail ? '8px' : '0px',
                        }}
                    >
                        {!showImportDetail ? (
                            <FileDropzone
                                multiple={false}
                                accept={{ 'application/json': ['.json'] }}
                                value={file}
                                placeholder={strings('bowtie:labels.importDrop')}
                                onChange={handleDrop}
                                readonly={showImportDetail}
                                removeType={'clear'}
                                renderContent={(item) => <Typography>{item.name}</Typography>}
                            ></FileDropzone>
                        ) : (
                            <TextBox sx={{ width: '100%' }}>
                                <TextBoxInner
                                    typography="body1"
                                    display="flex"
                                    width={'100%'}
                                    sx={{ alignItems: 'center' }}
                                >
                                    <Download
                                        color={theme.palette.protechtGrey?.grey_146}
                                        style={{ transform: 'rotate(180deg)', display: 'flex', alignItems: 'center', marginRight: '7px' }}
                                    ></Download>
                                    {file?.name}
                                </TextBoxInner>
                            </TextBox>
                        )}
                    </Grid>
                )}
                {showImportDetail && diagramData?.diagramModel && (
                    <Grid
                        item
                        sx={{ maxWidth: '100%', marginTop: '12px' }}
                    >
                        {!bowtieDiagramData && (
                            <Grid sx={{ maxWidth: '100%', marginBottom: '20px' }}>
                                <FormProvider {...methods}>
                                    <InputField
                                        name="diagramName"
                                        label={strings('bowtie:labels.diagramName')}
                                        formState={formState}
                                        control={control}
                                        clearable
                                    />
                                </FormProvider>
                            </Grid>
                        )}

                        <NodeSection
                            nodeType={NodeType.MainRiskEvent}
                            titleKey={strings('bowtie:nodeType.mainRiskEvent')}
                            bowtieDefinition={bowtieDefinition}
                            onLinkedEntryChanged={onLinkedEntryChanged}
                            onLinkedEntryReset={onLinkedEntryReset}
                            diagramData={diagramData?.diagramModel as DataModel}
                            isBulkUpdate={bowtieDiagramData !== undefined}
                        />

                        <NodeSection
                            nodeType={NodeType.RiskEvent}
                            titleKey={strings('bowtie:nodeType.riskEvent')}
                            bowtieDefinition={bowtieDefinition}
                            onLinkedEntryChanged={onLinkedEntryChanged}
                            onLinkedEntryReset={onLinkedEntryReset}
                            diagramData={diagramData?.diagramModel as DataModel}
                            isBulkUpdate={bowtieDiagramData !== undefined}
                        />

                        <NodeSection
                            nodeType={NodeType.Cause}
                            titleKey={strings('bowtie:nodeType.riskCause')}
                            bowtieDefinition={bowtieDefinition}
                            onLinkedEntryChanged={onLinkedEntryChanged}
                            onLinkedEntryReset={onLinkedEntryReset}
                            diagramData={diagramData?.diagramModel as DataModel}
                            isBulkUpdate={bowtieDiagramData !== undefined}
                        />

                        <NodeSection
                            nodeType={NodeType.Impact}
                            titleKey={strings('bowtie:nodeType.impact')}
                            bowtieDefinition={bowtieDefinition}
                            onLinkedEntryChanged={onLinkedEntryChanged}
                            onLinkedEntryReset={onLinkedEntryReset}
                            diagramData={diagramData?.diagramModel as DataModel}
                            disabled={!riskImpactRegisterContract}
                            isBulkUpdate={bowtieDiagramData !== undefined}
                        />

                        <NodeSection
                            nodeType={NodeType.Control}
                            titleKey={strings('bowtie:nodeType.control')}
                            bowtieDefinition={bowtieDefinition}
                            onLinkedEntryChanged={onLinkedEntryChanged}
                            onLinkedEntryReset={onLinkedEntryReset}
                            diagramData={diagramData?.diagramModel as DataModel}
                            disabled={!isLibraryLinkSupported(NodeType.Control, bowtieDefinition, oldControlsEnabled)}
                            isBulkUpdate={bowtieDiagramData !== undefined}
                        />
                    </Grid>
                )}
            </Grid>
        </Dialog>
    );
};

export default BowTieImportDialog;
