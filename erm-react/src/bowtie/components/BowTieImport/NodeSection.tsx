import React from 'react';

import { strings } from 'common/utils/i18n';
import Typography from '@mui/material/Typography';
import { NodeType } from 'bowtie/types';
import { DataModel } from 'resilience/types';
import BowTieNodeImportChangeSelector from './BowTieNodeImportChangeSelector';
import { Accordion, AccordionDetails, AccordionSummary } from '@protecht/ui-library/library/components/Accordion';
import Box from '@mui/material/Box';
import { BowTieDefinition } from 'api/generated/types';

interface NodeSectionProps {
    nodeType: NodeType;
    titleKey: string;
    bowtieDefinition?: BowTieDefinition;
    onLinkedEntryChanged: (node: any, selectedItem: any) => void;
    onLinkedEntryReset: (node: any) => void;
    diagramData: DataModel | null;
    disabled?: boolean;
    isBulkUpdate?: boolean;
}

const NodeSection: React.FC<NodeSectionProps> = ({
    nodeType,
    titleKey,
    bowtieDefinition,
    onLinkedEntryChanged,
    onLinkedEntryReset,
    diagramData,
    disabled = false,
    isBulkUpdate = false,
}) => {
    const nodes = isBulkUpdate
        ? disabled
            ? []
            : diagramData?.nodes?.filter((node) => node.nodeType === nodeType && (node.libraryLink == undefined || node.bulk)) || []
        : diagramData?.nodes?.filter((node) => node.nodeType === nodeType) || [];
    if (!nodes.length) {
        return null;
    }

    return (
        <Accordion
            defaultExpanded
            sx={{ marginTop: '28px' }}
        >
            <AccordionSummary
                aria-controls={`accordion-header-${nodeType}`}
                id={`accordion-header-${nodeType}`}
            >
                <Typography variant="h5">{titleKey}</Typography>
            </AccordionSummary>
            <AccordionDetails>
                <Box sx={{ display: 'flex', alignContent: 'space-between', width: '100%', marginBottom: '15px', marginTop: '5px' }}>
                    <Typography
                        variant="body2"
                        sx={{ width: '50%' }}
                    >
                        {isBulkUpdate
                            ? strings('bowtie:labels.foundInTheDiagram', { nodeType: titleKey })
                            : strings('bowtie:labels.foundInTheFile', { nodeType: titleKey })}
                    </Typography>
                    <Typography
                        variant="body2"
                        sx={{ width: '50%', paddingLeft: '11px' }}
                    >
                        {strings('bowtie:labels.linkToThisEntry')}
                    </Typography>
                </Box>
                {nodes.map((node, index) => (
                    <BowTieNodeImportChangeSelector
                        key={`${index}-${node.name}-${node.libraryLink?.id}`}
                        onLinkedEntryChanged={onLinkedEntryChanged}
                        onLinkedEntryReset={onLinkedEntryReset}
                        bowtieDefinition={bowtieDefinition}
                        node={node}
                        sectionIndex={index}
                        disabled={disabled}
                    />
                ))}
            </AccordionDetails>
        </Accordion>
    );
};

export default NodeSection;
