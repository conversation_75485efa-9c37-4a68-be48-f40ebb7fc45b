import { strings } from 'common/utils/i18n';
import React from 'react';
import { BrowserRouter } from 'react-router';
import { render, screen } from 'test/utils';
import { waitFor } from '@testing-library/react';
import { mockBowTieEntry } from 'bowtie/api.mock';
import user from '@testing-library/user-event';

import BowTieCreateDialog from './BowTieCreateDialog';
import { mockNavigate } from 'test/config/setupAfterEnv';

const mockCreateDiagram = jest.fn().mockImplementation(() => ({
    unwrap: () => Promise.resolve(mockBowTieEntry),
}));

jest.mock('bowtie/rtkApi', () => ({
    useBtrsCreateDiagramUsingPostMutation: () => [mockCreateDiagram, { isLoading: false, isSuccess: true, isError: false }],
}));

const getButtonByName = (name: string) => screen.getByRole('button', { name: strings(name) });
const getCancelButton = () => getButtonByName('common:button.cancel');
const getConfirmButton = () => getButtonByName('ermMessages:btn_okay');
const getInputFieldRole = () => screen.getByRole('textbox') as HTMLInputElement;

describe('<BowTieCreateDialog />', () => {
    const onClose = jest.fn();

    const setup = () => {
        return render(
            <BrowserRouter>
                <BowTieCreateDialog
                    visible={true}
                    onClose={onClose}
                />
            </BrowserRouter>,
        );
    };

    it('was rendered', () => {
        const view = setup();
        expect(view.container).toBeInTheDocument();
        expect(view.container).toBeVisible();
    });

    it('cancel button invokes action on cancel', async () => {
        setup();
        const cancelBtn = getCancelButton();

        expect(cancelBtn).toBeInTheDocument();
        expect(cancelBtn).toBeEnabled();
        expect(cancelBtn).toBeVisible();

        await user.click(cancelBtn);
        expect(onClose).toHaveBeenCalled();
    });

    it('ok button is disable when input field is empty', () => {
        setup();
        const onConfirmBtn = getConfirmButton();
        const textBox = getInputFieldRole();

        expect(onConfirmBtn).toBeInTheDocument();
        expect(onConfirmBtn).toBeVisible();
        expect(onConfirmBtn).toBeDisabled();

        expect(textBox).toHaveValue('');
    });

    it('ok button is able when input field is not empty', async () => {
        setup();
        const onConfirmBtn = getConfirmButton();
        const textBox = getInputFieldRole();

        await user.type(textBox, 'test text');
        expect(textBox).not.toHaveValue('');
        expect(onConfirmBtn).toBeInTheDocument();
        expect(onConfirmBtn).toBeVisible();
        expect(onConfirmBtn).toBeEnabled();
    });

    it('is able to submit valid data', async () => {
        setup();
        const onConfirmBtn = getConfirmButton();
        const textBox = getInputFieldRole();
        await user.type(textBox, 'test text');
        expect(textBox).not.toHaveValue('');
        expect(onConfirmBtn).toBeEnabled();
        await user.click(onConfirmBtn);

        await waitFor(() => expect(onClose).toHaveBeenCalled());
        expect(mockCreateDiagram).toHaveBeenCalled();

        expect(mockNavigate).toHaveBeenCalled();
    });

    it('is not able to submit non unique data', async () => {
        mockCreateDiagram.mockImplementation(() => ({
            unwrap: () => Promise.reject({ status: 409, data: 'errorFromBe' }),
        }));

        setup();
        const onConfirmBtn = screen.getByRole('button', { name: strings('ermMessages:btn_okay') });
        const textBox = getInputFieldRole();
        await user.type(textBox, 'test text');
        await user.click(onConfirmBtn);
        expect(textBox).not.toHaveValue('');
        expect(mockCreateDiagram).toHaveBeenCalled();
        await waitFor(() => expect(onConfirmBtn).toBeDisabled());
        expect(onClose).not.toHaveBeenCalled();
    });
});
