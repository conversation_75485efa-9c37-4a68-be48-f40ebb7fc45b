import React from 'react';
import { useNavigate, useLocation, generatePath } from 'react-router';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import { strings } from 'common/utils/i18n';
import { BowtiePath } from 'bowtie/routes';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { useBtrsCreateDiagramUsingPostMutation } from 'bowtie/rtkApi';
import { FormProvider, useForm } from 'react-hook-form';
import Box from '@mui/material/Box';
import FormField from '@protecht/ui-library/library/components/FormFields';
import * as Yup from 'yup';
import useFormValidationResolver from 'common/hooks/forms/useFormValidationResolver';

type Props = {
    visible: boolean;
    onClose: () => void;
};

type FormValues = {
    bowtieName: string;
};

const CreateBowtieFormSchema = Yup.object().shape({
    bowtieName: Yup.string()
        .required(strings('common:validators.required', { name: strings('vrm:label.bowtieName') }))
        .trim()
        .max(200, strings('common:validators.maxLength', { value: 200 })),
});

const BowTieCreateDialog: React.FC<Props> = (props: Props) => {
    const { visible, onClose } = props;

    const navigate = useNavigate();
    const location = useLocation();
    const resolver = useFormValidationResolver(CreateBowtieFormSchema);
    const methods = useForm<FormValues>({ mode: 'onChange', resolver });
    const { handleSubmit, setError, trigger, formState, reset } = methods;
    const { isValid } = formState;

    const [createDiagram] = useBtrsCreateDiagramUsingPostMutation();

    const closeDialog = () => {
        onClose();
        reset();
    };

    const onConfirm = async (data) => {
        try {
            const diagram = await createDiagram({
                bowTieCreateDiagramRest: { name: data.bowtieName },
            }).unwrap();

            closeDialog();
            void navigate(generatePath(BowtiePath.DIAGRAM, { id: diagram?.id?.toString() ?? null }), { state: { from: location } });
        } catch (error) {
            if (error.status === 409) {
                setError('bowtieName', { type: error.data, message: strings('bowtie:diagram.message.bowtieNameExists') });
            } else {
                return Promise.reject(error);
            }
        }
    };

    return (
        <Dialog
            visible={visible}
            title={strings('bowtie:file.title.createNewBowTie')}
            width={500}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={closeDialog}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!isValid}
                        onClick={handleSubmit(onConfirm)}
                    >
                        {strings('ermMessages:btn_okay')}
                    </Button>
                </DialogActions>
            }
        >
            <FormProvider {...methods}>
                <Box>
                    <FormField
                        name="bowtieName"
                        label={strings('bowtie:file.label.bowTieName')}
                        renderField={(field) => (
                            <Input
                                type="text"
                                placeholder={strings('bowtie:title.placeholderName')}
                                {...field}
                                onChange={(value) => {
                                    field.onChange(value);
                                    void trigger('bowtieName');
                                }}
                            />
                        )}
                    />
                </Box>
            </FormProvider>
        </Dialog>
    );
};

export default BowTieCreateDialog;
