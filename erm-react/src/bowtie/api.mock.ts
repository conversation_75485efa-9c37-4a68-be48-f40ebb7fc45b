import { BowTieStatus, LinkUpdateType } from './types';
import { BowTieDefinition } from '../api/generated/types';

export const mockBowTieEntry = {
    id: 100000000000000000000,
    name: 'Some text name',
    description: 'some text description',
    status: BowTieStatus.Draft,
    createdBy: 'Some text',
    lastModifiedBy: 'Some text',
    createDate: '2023-07-05 15:12:59.324',
    lastModifiedDate: '2023-07-05 15:12:59.331',
    createDateFormatted: '05/07/2023 15:12:59',
    lastModifiedDateFormatted: '05/07/2023 15:12:59',
    centralRiskName: 'New Main Event',
    libraryLinkEnabled: false,
    showLegendEnabled: false,
    showControlsEnabled: false,
    libraryLinkNotifications: [
        {
            id: 11111,
            type: LinkUpdateType.Changed,
            name: 'test name',
        },
    ],
};

export const mockBowTieEntryApi = {
    id: 100000000000000000000,
    name: 'Some text name',
    description: 'some text description',
    status: BowTieStatus.Draft,
    createdBy: 'Some text',
    lastModifiedBy: 'Some text',
    createDate: '2023-07-05 15:12:59.324',
    lastModifiedDate: '2023-07-05 15:12:59.331',
    createDateFormatted: '05/07/2023 15:12:59',
    lastModifiedDateFormatted: '05/07/2023 15:12:59',
    centralRiskName: 'New Main Event',
    libraryLinkEnabled: false,
    showLegendEnabled: false,
    showControlsEnabled: false,
    locked: false,
    libraryLinkNotifications: [
        {
            id: 11111,
            type: LinkUpdateType.Changed,
            name: 'test name',
        },
    ],
};

export const RegisterBowTieRestMock = {
    id: 11111,
    tableName: 'table-name-test',
    label: 'label-name-test',
    fields: [{ fieldId: 2222, fieldName: 'field-name-test', label: 'label-name-test' }],
};

export const IdOnly = {
    id: 123,
};

export const mockedDefinition = {
    riskEventRegister: {
        primary: true,
        name: 'reg 167270',
        registerId: 15467,
        registerTable: 'table_167280',
        displayColumn: 'id',
        applicationId: 7000,
    },
    riskImpactRegister: {
        primary: true,
        name: 'reg 167270',
        registerId: 15467,
        registerTable: 'table_167280',
        displayColumn: 'id',
        applicationId: 7000,
    },
} as BowTieDefinition;
