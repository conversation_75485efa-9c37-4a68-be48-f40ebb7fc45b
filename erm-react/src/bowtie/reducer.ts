import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { getRegister } from 'register/api';
import { AsyncThunkStatus } from 'common/types';
import { BowTieRegisterEnum, BowTieRegisterRest } from './types';
import { getDefinition } from './api';
import { ConfigDefinitionRest } from './types';
import { RegisterRest } from '../register/types';
import { BowTieDefinition } from '../api/generated/types';

export type BowTieRegisterData = Partial<Record<keyof ConfigDefinitionRest, RegisterRest | null>>;

interface State {
    configuration: BowTieDefinition | null;
    registers: Partial<Record<BowTieRegisterEnum | string, RegisterRest | null>>;
    loading: Partial<Record<BowTieRegisterEnum | string, AsyncThunkStatus>>;
    error: Partial<Record<BowTieRegisterEnum | string, string | null>>;
}

export const initialState: State = {
    configuration: null,
    registers: {},
    loading: {
        configuration: AsyncThunkStatus.IDLE,
    },
    error: {},
};

export const loadBowTieData = createAsyncThunk('bowtie/loadBowtieData', async (_, { dispatch }) => {
    dispatch(loadingStarted('configuration'));
    const config = await getDefinition();

    if (!config) {
        console.error('Config data is undefined or null');
        return;
    }

    Object.entries(config)
        .filter(([configKey]) => !['resourcesInProcess', 'resourcesInScenario'].includes(configKey))
        .forEach(([configKey, registerConfig]) => {
            void dispatch(loadRegister({ configKey, registerConfig: registerConfig as BowTieRegisterRest }));
        });

    return config;
});

export const loadRegister = createAsyncThunk(
    'bowtie/loadRegister',
    async (
        {
            configKey,
            registerConfig,
        }: {
            configKey: BowTieRegisterEnum | string;
            registerConfig: BowTieRegisterRest;
        },
        { dispatch, rejectWithValue },
    ) => {
        dispatch(loadingStarted(configKey));

        try {
            const register = await getRegister(registerConfig.registerId!);

            return { configKey, register };
        } catch (error) {
            return rejectWithValue({ configKey, error });
        }
    },
);

const bowtieSlice = createSlice({
    name: 'bowtie',
    initialState,
    reducers: {
        loadingStarted(state, action: PayloadAction<string>) {
            state.loading = {
                ...state.loading,
                [action.payload]: AsyncThunkStatus.PENDING,
            };
        },
    },
    extraReducers: (builder) => {
        builder.addCase(loadBowTieData.fulfilled, (state, { payload }) => {
            state.loading.configuration = AsyncThunkStatus.SUCCEEDED;
            state.configuration = payload as BowTieDefinition;
        });
        builder.addCase(loadRegister.fulfilled, (state, { payload }: PayloadAction<{ configKey: string; register: RegisterRest }>) => {
            state.loading[payload.configKey] = AsyncThunkStatus.SUCCEEDED;
            state.registers[payload.configKey] = payload.register;
        });
        builder.addCase(loadBowTieData.rejected, (state) => {
            state.loading.configuration = AsyncThunkStatus.FAILED;
            state.configuration = null;
        });
        builder.addCase(loadRegister.rejected, (state, { payload }) => {
            const p = payload as { configKey: string; error: string };
            state.loading[p.configKey] = AsyncThunkStatus.FAILED;
            state.registers[p.configKey] = null;
            state.error[p.configKey] = p.error;
        });
    },
});

export const { loadingStarted } = bowtieSlice.actions;
export default bowtieSlice;
