import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { BowTieSearchRequestParams } from 'common/api/types';
import { BowTieStatus } from './types';

export const getBowTieSearchRequestParams = (params: SearchRequestParams, searchField?: string, searchValue?: string): BowTieSearchRequestParams => {
    const offset = params.offset ?? 0;
    const limit = params.limit ?? 0;

    const bowTieParams = {
        size: params.limit,
        page: params.page ?? (limit !== 0 ? offset / limit : 0),
        'sort-by': params.orderBy,
        'sort-dir': params.orderType?.toUpperCase(),
        'filter-by': (searchValue ?? '').length > 0 ? searchField : undefined,
        'filter-value': (searchValue ?? '').length > 0 ? searchValue : undefined,
        'group-by': params.groupBy,
        'tag-type': params.tagType,
        tags: params.tagIds,
        'tag-operator': params.tagOperator,
    };

    Object.keys(bowTieParams).forEach((key) => bowTieParams[key] === undefined && delete bowTieParams[key]);

    return bowTieParams;
};

export const getBowTieStatus = (status?: string): BowTieStatus | null => {
    switch (status?.toLowerCase()) {
        case 'drafts': {
            return BowTieStatus.Draft;
        }
        case 'published': {
            return BowTieStatus.Published;
        }
        case 'templates': {
            return BowTieStatus.Template;
        }
        case 'deleted': {
            return BowTieStatus.Deleted;
        }
        default: {
            // recent
            return null;
        }
    }
};
