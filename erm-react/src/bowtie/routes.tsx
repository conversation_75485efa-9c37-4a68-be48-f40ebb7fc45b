import React from 'react';
import { RouteObject } from 'react-router';

import { BowTieLayout } from 'bowtie/components';
import BowtieDiagramPage from 'bowtie/diagram/components/BowtieDiagramPage';
import { getCurrentRoute, getScaleSets } from 'app/selectors';
import store from 'store';
import { resetLayoutProps } from 'app/reducer';
import { usersApi } from 'user/rtkApi';
import { viewsApi } from 'view/rtkApi';
import { bowtiesApi } from './rtkApi';
import BowTieSettings from './components/BowTieSettings';
import { loadBowTieData } from './reducer';

export enum BowtiePath {
    OVERVIEW = '/bowties/:status',
    DIAGRAM = '/bowties/:id/diagram',
    SETTINGS = '/bowties/settings',
}

const BowtieRoutes: RouteObject[] = [
    {
        path: 'bowties',
        children: [
            {
                path: ':status',
                element: <BowTieLayout />,
                loader: () => {
                    void store.dispatch(loadBowTieData());

                    void store.dispatch(usersApi.endpoints.pursGetUserPermissionsUsingGet.initiate({}, { subscribe: false }));
                    void store.dispatch(viewsApi.endpoints.vrsGetExpressionContextUsingGet.initiate(undefined, { subscribe: false }));

                    const previousPathname = getCurrentRoute(store.getState()).to;
                    if (!previousPathname.includes('/diagram')) {
                        store.dispatch(resetLayoutProps());
                    }

                    return null;
                },
                shouldRevalidate: () => {
                    const scaleSets = getScaleSets(store.getState());
                    return scaleSets.length === 0;
                },
            },
            {
                path: ':id/diagram',
                element: <BowtieDiagramPage />,
                loader: ({ params }) => {
                    void store.dispatch(loadBowTieData());

                    // we need to clear cached data, because they may be old and some change could be done in the background
                    // e.g. when updating links - risk causes/events
                    void store.dispatch(bowtiesApi.util.invalidateTags([{ type: 'diagrams', id: params.id }]));
                    void store.dispatch(bowtiesApi.util.invalidateTags([{ type: 'diagrams', id: 'configuration' }]));

                    return null;
                },
            },
            {
                path: 'settings',
                element: <BowTieSettings />,
                loader: () => {
                    void store.dispatch(loadBowTieData());

                    void store.dispatch(bowtiesApi.util.invalidateTags([{ type: 'diagrams', id: 'configuration' }]));

                    return null;
                },
            },
        ],
    },
];

export default BowtieRoutes;
