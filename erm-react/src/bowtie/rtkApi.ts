import { baseInjected<PERSON>pi } from 'api/generated/bowties';

export const bowtiesApi = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['diagrams'],
    endpoints: {
        btrsCreateDiagramUsingPost: {
            invalidatesTags: [{ type: 'diagrams', id: 'list' }],
        },
        btrsGetRecentDiagramsUsingGet: {
            providesTags: [{ type: 'diagrams', id: 'recent' }],
        },
        btrsGetDiagramStyleUsingGet: {
            providesTags: [{ type: 'diagrams', id: 'style' }],
        },
        btrsVerifyUserBUsUsingPut: {
            invalidatesTags: [{ type: 'diagrams', id: 'verification' }],
        },
        btrsLinkBowTieUsingPut: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.bowTieId },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsUnlinkUsingPut: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.bowTieId },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsGetDiagramUsingGet(endpoint) {
            endpoint.providesTags = (result, error, arg) => (result ? [{ type: 'diagrams', id: arg.id }] : []);
        },
        btrsUpdateDiagramUsingPut: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.id },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsDeleteDiagramUsingDelete: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.id },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsRestoreDiagramUsingPut: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.id },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsPurgeDiagramUsingDelete: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.id },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsGetDiagramCopyUsingPut: {
            invalidatesTags: (result, error, arg) => [{ type: 'diagrams', id: arg.id }],
        },
        btrsGetEntriesUsingGet: {
            providesTags: (result, error, arg) => (result ? [{ type: 'diagrams', id: arg.id }] : []),
        },
        btrsPublishDiagramUsingPut: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.id },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsUnpublishDiagramUsingPut: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.id },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsChangeDiagramLockStatusUsingPut: {
            invalidatesTags: (result, error, arg) => [
                { type: 'diagrams', id: arg.id },
                { type: 'diagrams', id: 'list' },
            ],
        },
        btrsGetDiagramsUsingGet: {
            providesTags: [{ type: 'diagrams', id: 'list' }],
        },
        btrsGetDefinitionUsingGet: {
            providesTags: [{ type: 'diagrams', id: 'configuration' }],
        },
        btrsUpdateDiagramDefinitionUsingPut: {
            invalidatesTags: [{ type: 'diagrams', id: 'configuration' }],
        },
    },
});

export const {
    useBtrsCreateDiagramUsingPostMutation,
    useBtrsUpdateDiagramUsingPutMutation,
    useBtrsVerifyUserBUsUsingPutMutation,
    useBtrsPublishDiagramUsingPutMutation,
    useBtrsUnpublishDiagramUsingPutMutation,
    useBtrsGetDiagramsUsingGetQuery,
    useLazyBtrsGetDiagramsUsingGetQuery,
    useBtrsGetRecentDiagramsUsingGetQuery,
    useBtrsGetDiagramUsingGetQuery,
    useBtrsGetDiagramCopyUsingPutMutation,
    useBtrsChangeDiagramLockStatusUsingPutMutation,
    useBtrsRestoreDiagramUsingPutMutation,
    useBtrsPurgeDiagramUsingDeleteMutation,
    useBtrsDeleteDiagramUsingDeleteMutation,
    useBtrsGetDiagramStyleUsingGetQuery,
    useBtrsGetEntriesUsingGetQuery,
    useLazyBtrsGetDiagramUsingGetQuery,
    useLazyBtrsValidateBowtieNameUsingGetQuery,
    useBtrsLinkBowTieUsingPutMutation,
    useBtrsUnlinkUsingPutMutation,
    useBtrsGetDefinitionUsingGetQuery,
    useBtrsUpdateDiagramDefinitionUsingPutMutation,
    usePrefetch,
} = bowtiesApi;
