import React, { ReactNode } from 'react';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { DIAGRAM_ACTION_TOOLBAR, DIAGRAM_TEST_ID_PREFIX } from 'common/diagram/constants';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRedo, faUndo } from '@fortawesome/pro-solid-svg-icons';
import ObjectState from 'common/components/ObjectState';
import DiagramActionToolbar from 'common/diagram/components/DiagramPage/DiagramActionToolbar';
import Zoomer from 'common/diagram/components/DiagramPage/Zoomer';
import { strings } from 'common/utils/i18n';
import BackIconButton from 'common/components/buttons/BackIconButton';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';

type Props = {
    title: string;
    status?: string;
    diagramSettingsToolbar: ReactNode;
    legend?: ReactNode;
    // Zoomer
    onZoomIn: () => void;
    onZoomOut: () => void;
    onZoomOriginal: () => void;
    onFitContent: () => void;
    // undo/redo
    onUndo: () => void;
    onRedo: () => void;
    // DiagramActionToolbar
    onPDFExport: () => void;
    onImageExport: () => void;
    onExportDiagram: () => void;
    bulkUpdate: () => void;
    onPrint: () => void;
    onDelete: () => void;
    onCancel: () => void;
    onSave: () => void;
    onEdit: () => void;
    onCopy: () => void;
    onLink: () => void;
    canEdit: boolean;
    saveDisabled: boolean;
};

const BowtieDiagramHeader: React.FC<Props> = (props: Props) => {
    return (
        <Grid
            container
            spacing={2}
            sx={{
                height: DIAGRAM_ACTION_TOOLBAR,
                backgroundColor: 'white',
                zIndex: 100,
                padding: '0 16px',
            }}
        >
            <Grid
                item
                container
                direction="row"
                flexWrap="nowrap"
                justifyContent="space-between"
                alignItems="center"
                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-title-bar`}
                sx={{ width: '100%' }}
            >
                <Grid
                    item
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        height: '40px',
                        flex: '1 1 auto',
                        minWidth: 0,
                        gap: 1,
                        marginRight: '20px',
                    }}
                >
                    <BackIconButton
                        onClick={props.onCancel}
                        dataTestId={`${DIAGRAM_TEST_ID_PREFIX}-back`}
                    />
                    <Grid
                        item
                        whiteSpace="nowrap"
                        overflow="hidden"
                        data-testid="label"
                    >
                        <Tooltip
                            title={props.title}
                            aria-label={props.title}
                        >
                            <Typography
                                variant={'h1'}
                                noWrap
                                sx={{
                                    fontWeight: 'bold',
                                    margin: 'auto 10px',
                                    flex: '1 1 auto',
                                    minWidth: 0,
                                }}
                                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-name`}
                            >
                                {props.title}
                            </Typography>
                        </Tooltip>
                    </Grid>
                    {props.status && (
                        <ObjectState
                            objectState={props.status}
                            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-status`}
                        />
                    )}
                </Grid>
                <Grid
                    item
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        flex: '0 0 auto',
                    }}
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-title-actions`}
                >
                    <DiagramActionToolbar
                        onSave={props.onSave}
                        onCancel={props.onCancel}
                        onImageExport={props.onImageExport}
                        onPDFExport={props.onPDFExport}
                        onPrint={props.onPrint}
                        onEdit={props.onEdit}
                        onDelete={props.onDelete}
                        bulkUpdate={props.bulkUpdate}
                        onCopy={props.onCopy}
                        onLink={props.onLink}
                        onExport={props.onExportDiagram}
                        saveDisabled={props.saveDisabled}
                        canEdit={props.canEdit}
                    />
                </Grid>
            </Grid>
            <Grid
                item
                container
                direction="row"
                justifyContent="space-between"
            >
                <Grid
                    item
                    xs={12}
                    sm={6}
                    md={3}
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: { xs: 'center', sm: 'flex-start' },
                        flex: { xs: '1 1 100%', sm: '1 1 auto' },
                        minWidth: 0,
                        paddingLeft: '10px',
                    }}
                >
                    <Zoomer
                        onZoomIn={props.onZoomIn}
                        onZoomOut={props.onZoomOut}
                        onZoomOriginal={props.onZoomOriginal}
                        onFitContent={props.onFitContent}
                    />
                    <Tooltip
                        title={strings('bowtie:diagram.button.undo')}
                        aria-label={strings('bowtie:diagram.button.undo')}
                    >
                        <IconButton
                            disabled={!props.canEdit}
                            color={'primary'}
                            onClick={props.onUndo}
                            sx={{ ml: '8px' }}
                            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-undo`}
                        >
                            <FontAwesomeIcon icon={faUndo} />
                        </IconButton>
                    </Tooltip>
                    <Tooltip
                        title={strings('bowtie:diagram.button.redo')}
                        aria-label={strings('bowtie:diagram.button.redo')}
                    >
                        <IconButton
                            disabled={!props.canEdit}
                            color={'primary'}
                            onClick={props.onRedo}
                            sx={{ ml: '8px' }}
                            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-redo`}
                        >
                            <FontAwesomeIcon icon={faRedo} />
                        </IconButton>
                    </Tooltip>
                </Grid>
                {props.legend && (
                    <Grid
                        item
                        container
                        xs={12}
                        sm={12}
                        md={5}
                        order={{ md: 2, xs: 3 }}
                        justifyContent="center"
                        alignItems="center"
                    >
                        {props.legend}
                    </Grid>
                )}
                <Grid
                    item
                    container
                    xs={12}
                    sm={6}
                    md={4}
                    order={{ md: 3, xs: 2 }}
                    justifyContent={{ xs: 'center', sm: 'flex-end' }}
                    alignItems="center"
                >
                    {props.diagramSettingsToolbar}
                </Grid>
            </Grid>
        </Grid>
    );
};

export default BowtieDiagramHeader;
