import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate, useLocation, useParams, generatePath } from 'react-router';
import Loading from 'common/components/Loading/Loading';
import Page from 'common/diagram/components/DiagramPage';
import { BowTieStyle, DiagramModelRef, LinkNameChangeOption, LinkUpdateNotification, LinkUpdateType, NodeModel } from 'bowtie/types';
import BowTieDetails from 'bowtie/components/BowTieDetails';
import DiagramComponent from '../DiagramComponent';
import { DELETED_LINK, DIAGRAM_TEST_ID_PREFIX, LINK_CHANGE_DIALOG_OPTIONS } from '../../constants';
import { ICommand } from 'yfiles';
import { strings } from 'common/utils/i18n';
import BowTieCopyDialog from 'bowtie/components/BowTieCopyDialog';
import { CurrentDialog } from 'bowtie/components/BowTieLayout/BowTieLayout';
import { TagTable } from 'library/types';
import { isEqual, cloneDeep, unset } from 'lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimesCircle } from '@fortawesome/pro-solid-svg-icons';
import Box from '@mui/material/Box';
import useTheme from '@mui/system/useTheme';
import LoadingOverlay from 'common/components/LoadingOverlay';
import BowtieDiagramHeader from './BowtieDiagramHeader';
import Legend from './components/Legend';
import useSnackbar from 'common/hooks/useSnackbar';
import BowTieLinksDialog from 'bowtie/components/BowTieLinks';
import { AlertType, DialogType } from 'common/types';
import { BowtiePath } from 'bowtie/routes';
import Switch from '@protecht/ui-library/library/components/Switch';
import RadioGroup from '@protecht/ui-library/library/components/RadioGroup';
import {
    useBtrsDeleteDiagramUsingDeleteMutation,
    useBtrsGetDiagramUsingGetQuery,
    useBtrsGetDiagramStyleUsingGetQuery,
    useBtrsUpdateDiagramUsingPutMutation,
    useBtrsGetDefinitionUsingGetQuery,
} from 'bowtie/rtkApi';
import { useUnsavedChangesAlert } from 'common/hooks/useUnsavedChangesAlert';
import { BowTieDiagramRest, BowTieLinkNotificationRest } from 'api/generated/types';
import BowTieExportDialog from 'bowtie/components/BowTieExport/BowTieExportDialog';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Typography from '@mui/material/Typography';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import BowTieImportDialog from 'bowtie/components/BowTieImport';

const BowtieDiagramPage = (): React.ReactElement => {
    const navigate = useNavigate();
    const location = useLocation();
    const { id } = useParams<{ id: number }>();
    const theme = useTheme();
    const { enqueueSuccess } = useSnackbar();

    const [bowTie, setBowTie] = useState<BowTieDiagramRest>();
    const [styles, setStyles] = useState<BowTieStyle>();

    const diagramComponentRef = useRef<DiagramModelRef>(null);
    const [linkChangeOption, setLinkChangeOption] = useState<LinkNameChangeOption>(LinkNameChangeOption.KEEP_LINK);

    const [detailsVisible, setDetailsVisible] = useState<boolean>(false);

    const [showLegendEnabled, setShowLegendEnabled] = useState<boolean>(false);
    const [showControlsEnabled, setShowControlsEnabled] = useState<boolean>(true);
    const [libraryLinkEnabled, setlibraryLinkEnabled] = useState<boolean>(false);
    const [layoutingEnabled, setLayoutingEnabled] = useState<boolean>(true);
    const [visibleDialog, setVisibleDialog] = useState<CurrentDialog>();
    const [bowtieDiagramActual, setBowtieDiagramActual] = useState<object>();
    const [isDiagramSaving, setIsDiagramSaving] = useState<boolean>(false);
    const [isDirty, setIsDirty] = useState<boolean>(false);
    const [diagramData, setDiagramData] = useState<BowTieDiagramRest>();
    const [isChangedLinks, setIsChangedLinks] = useState<boolean>(true);
    const [refreshDiagram, setRefreshDiagram] = useState<number>(0);

    const { data: definition } = useBtrsGetDefinitionUsingGetQuery();

    const [updateDiagram] = useBtrsUpdateDiagramUsingPutMutation();
    const [deleteDiagram] = useBtrsDeleteDiagramUsingDeleteMutation();

    const { data: diagramStyleData, isLoading: isStyleLoading, isError: isStyleError } = useBtrsGetDiagramStyleUsingGetQuery();
    const {
        data: data,
        isLoading: isDiagramLoading,
        isError: isDiagramError,
    } = useBtrsGetDiagramUsingGetQuery({ id: id!, withDiagram: true }, { skip: !id, refetchOnMountOrArgChange: true });

    const initialShowControlsEnabledRef = useRef<boolean>(true);
    const initialLibraryLinkEnabledRef = useRef<boolean>(false);
    const initialLegendEnabledRef = useRef<boolean>(false);

    useEffect(() => {
        if (data) {
            setDiagramData(JSON.parse(JSON.stringify(data)));
        }
    }, [data]);

    useEffect(() => {
        if (diagramData && diagramStyleData) {
            setBowTie(diagramData);
            setStyles(diagramStyleData as BowTieStyle);
            setBowtieDiagramActual(diagramData.diagramModel);
            setlibraryLinkEnabled(diagramData.libraryLinkEnabled ?? false);
            setShowControlsEnabled(diagramData.showControlsEnabled ?? true);
            setShowLegendEnabled(diagramData.showLegendEnabled ?? false);
            initialShowControlsEnabledRef.current = diagramData.showControlsEnabled ?? true;
            initialLibraryLinkEnabledRef.current = diagramData.libraryLinkEnabled ?? false;
            initialLegendEnabledRef.current = diagramData.showLegendEnabled ?? false;
        }
    }, [diagramData, diagramStyleData, isDiagramError, isStyleError]);

    const actualDiagramModel = diagramComponentRef.current?.getDiagramModel();
    const diagramModelString = JSON.stringify(actualDiagramModel);

    const hasChangesStates = useCallback(() => {
        return (
            showControlsEnabled !== initialShowControlsEnabledRef.current ||
            libraryLinkEnabled !== initialLibraryLinkEnabledRef.current ||
            showLegendEnabled !== initialLegendEnabledRef.current
        );
    }, [libraryLinkEnabled, showControlsEnabled, showLegendEnabled]);

    const hasChanges = useCallback(() => {
        if (bowTie?.locked) {
            return false;
        }
        const hasChanges = !(
            isEqual((actualDiagramModel as any)?.nodes, (bowtieDiagramActual as any)?.nodes) &&
            isEqual((actualDiagramModel as any)?.links, (bowtieDiagramActual as any)?.links)
        );
        //fixed for WORMS-17618,does not trigger hasChanges dialog when the only change is in y-position
        if (hasChanges && (actualDiagramModel as any)?.nodes && (actualDiagramModel as any)?.nodes.length === (bowtieDiagramActual as any)?.nodes.length) {
            for (let i = 0; i < (actualDiagramModel as any)?.nodes.length; i++) {
                const newNode: NodeModel = cloneDeep((actualDiagramModel as any)?.nodes[i]);
                const actualNode: NodeModel = cloneDeep((bowtieDiagramActual as any)?.nodes[i]);

                unset(newNode, 'position.y');
                unset(actualNode, 'position.y');

                if (!actualNode.position) {
                    unset(newNode, 'position');
                }
                const changed = !isEqual(newNode, actualNode);
                if (changed) {
                    return true;
                }
            }
            return hasChangesStates();
        }
        return hasChanges || hasChangesStates();
    }, [actualDiagramModel, bowTie?.locked, bowtieDiagramActual, hasChangesStates]);

    const { showConfirmationAlert } = useConfirmationAlert();

    const saveDiagram = useCallback(async () => {
        setIsDiagramSaving(true);
        setIsDirty(false);
        try {
            const updatedDiagram: BowTieDiagramRest = {
                ...(bowTie as BowTieDiagramRest),
                libraryLinkEnabled: libraryLinkEnabled,
                showLegendEnabled: showLegendEnabled,
                showControlsEnabled: showControlsEnabled,
                diagramModel: {
                    ...(bowTie?.diagramModel as object),
                    ...diagramComponentRef.current?.getDiagramModel(),
                },
                tags: bowTie?.tags?.filter((value, index: number, array) => array.findIndex((tag: TagTable) => tag.id === value.id) === index) || [],
            };
            await updateDiagram({ id: id!, withDiagram: true, bowTieDiagramRest: updatedDiagram });
            enqueueSuccess(strings('bowtie:diagram:message.diagramUpdated'));
            setBowtieDiagramActual(diagramComponentRef.current?.getDiagramModel());
        } catch (_e) {
            setIsDirty(true);
            showConfirmationAlert({
                icon: (
                    <FontAwesomeIcon
                        icon={faTimesCircle}
                        color={theme.palette.error.main}
                    />
                ),
                title: strings('ermConstants:dlg_title_error'),
                contentText: strings('bowtie:message.savingDiagramError'),
            });
        } finally {
            setIsDiagramSaving(false);
        }
    }, [
        bowTie,
        enqueueSuccess,
        id,
        libraryLinkEnabled,
        showConfirmationAlert,
        showControlsEnabled,
        showLegendEnabled,
        theme.palette.error.main,
        updateDiagram,
    ]);

    useUnsavedChangesAlert({
        blockNavigation: hasChanges(),
        onConfirm: saveDiagram,
        dialogType: DialogType.UNSAVED,
    });

    useEffect(() => {
        setIsDirty(hasChanges());
    }, [diagramModelString, bowtieDiagramActual, hasChanges]);

    // ignore error until we return Auto Layout switch
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const layoutingChange = () => {
        setLayoutingEnabled((prevOpen) => !prevOpen);
    };

    const legendVisiblityChange = () => {
        setShowLegendEnabled((prevValue) => !prevValue);
    };

    const controlsVisiblityChange = () => {
        setShowControlsEnabled((prevOpen) => !prevOpen);
    };

    const libraryLinkVisible = () => {
        setlibraryLinkEnabled((prevOpen) => !prevOpen);
    };

    const back = useCallback(() => {
        if (location.state?.from) {
            void navigate(location.state?.from);
        } else {
            void navigate(generatePath(BowtiePath.OVERVIEW, { status: 'recent' }));
        }
    }, [location.state?.from, navigate]);

    useEffect(() => {
        const alertUser = (event) => {
            if (hasChanges()) {
                const e = event || window.event;
                // Cancel the event
                e.preventDefault();
                if (e) {
                    e.returnValue = ''; // Legacy method for cross browser support
                }
                return ''; // Legacy method for cross browser support
            }
        };
        window.addEventListener('beforeunload', alertUser);
        return () => {
            window.removeEventListener('beforeunload', alertUser);
        };
    });

    useEffect(() => {
        if (detailsVisible) {
            return;
        }
        try {
            if (diagramData && diagramData.libraryLinkNotifications && diagramData.libraryLinkNotifications.length > 0) {
                const deletedLinks = diagramData.libraryLinkNotifications.filter(
                    (update) => update.type === LinkUpdateType.Purged || update.type === LinkUpdateType.Deleted,
                );
                const changedLinks = diagramData.libraryLinkNotifications.filter((update) => update.type === LinkUpdateType.Changed);
                if (changedLinks.length > 0) {
                    showConfirmationAlert({
                        icon: (
                            <FontAwesomeIcon
                                icon={faTimesCircle}
                                color={theme.palette.error.main}
                            />
                        ),
                        onConfirm: () => {
                            updateChangedLinks(diagramData, changedLinks);
                            if (deletedLinks && deletedLinks.length > 0) {
                                displayLinkDeletedDialog(diagramData, deletedLinks);
                            } else {
                                setBowTie(diagramData);
                                setIsChangedLinks(false);
                            }
                        },
                        onClose: () => {
                            if (deletedLinks && deletedLinks.length > 0) {
                                displayLinkDeletedDialog(diagramData, deletedLinks);
                            } else {
                                setBowTie(diagramData);
                                setIsChangedLinks(false);
                            }
                        },
                        keepOpenAfterConfirm: deletedLinks && deletedLinks.length > 0,
                        keepOpenAfterClose: deletedLinks && deletedLinks.length > 0,
                        title: strings('bowtie:title.warning'),
                        contentText: strings('bowtie:message.linkNameChanged'),
                        customContent: (
                            <RadioGroup
                                onChange={(event, value) => {
                                    setLinkChangeOption(value as LinkNameChangeOption);
                                }}
                                options={LINK_CHANGE_DIALOG_OPTIONS}
                                value={linkChangeOption}
                            />
                        ),
                        cancelButtonLabel: strings('ermMessages:btn_cancel'),
                        confirmButtonLabel: strings('ermMessages:btn_okay'),
                    });
                } else if (deletedLinks.length > 0) {
                    displayLinkDeletedDialog(diagramData, deletedLinks);
                }
            } else {
                if (diagramData) {
                    setBowTie(diagramData);
                    setIsChangedLinks(false);
                }
            }
            setStyles(diagramStyleData as BowTieStyle);
            setBowtieDiagramActual(diagramData && diagramData.diagramModel);
        } catch (_e) {
            showConfirmationAlert({
                icon: (
                    <FontAwesomeIcon
                        icon={faTimesCircle}
                        color={theme.palette.error.main}
                    />
                ),
                title: strings('ermConstants:dlg_title_error'),
                contentText: strings('bowtie:message.loadingDiagramError'),
            });
        }
    }, [id, detailsVisible, diagramData, showConfirmationAlert, theme.palette.error.main, linkChangeOption]);

    const displayLinkDeletedDialog = (diagram: BowTieDiagramRest, deletedLinks) => {
        showConfirmationAlert({
            icon: (
                <FontAwesomeIcon
                    icon={faTimesCircle}
                    color={theme.palette.error.main}
                />
            ),
            title: strings('bowtie:title.warning'),
            contentText: strings('bowtie:message.linkedLibraryDeleted'),
            cancelButtonLabel: strings('ermMessages:btn_cancel'),
            confirmButtonLabel: strings('ermMessages:btn_okay'),
            onConfirm: () => {
                markDeleted(diagram, deletedLinks);
            },
        });
    };

    const updateChangedLinks = (diagram: BowTieDiagramRest, changedLinks: BowTieLinkNotificationRest[]) => {
        if (linkChangeOption) {
            (diagram.diagramModel as any)?.nodes.forEach((node) => {
                const nodeUpdate = changedLinks.find((link) => link.id === node.key);
                if (nodeUpdate) {
                    if (linkChangeOption === LinkNameChangeOption.KEEP_LINK && nodeUpdate.name) {
                        node.name = nodeUpdate.name;
                        node.libraryLink = { ...node.libraryLink, name: nodeUpdate.name };
                    } else {
                        node.libraryLink = undefined;
                    }
                }
            });
        }
    };

    const markDeleted = (diagram: BowTieDiagramRest, deletedLinks: LinkUpdateNotification[]) => {
        (diagram.diagramModel as any)?.nodes.forEach((node) => {
            if (node.libraryLink && deletedLinks.some((link) => link.id === node.key)) {
                node.libraryLink = DELETED_LINK;
            }
        });
        setBowTie(diagram);
        setIsChangedLinks(false);
    };

    const exportDiagram = (data?: BowTieDiagramRest, _filename?) => {
        if (isDirty) {
            setVisibleDialog(CurrentDialog.SAVEALERT);
            return;
        }

        if (data) {
            setVisibleDialog(CurrentDialog.EXPORT);
        }
    };

    const bulkUpdate = () => {
        setVisibleDialog(CurrentDialog.BULK_UPDATE);
    };

    const onEdit = () => {
        setDetailsVisible(true);
    };

    const onDialogClosed = () => {
        setVisibleDialog(undefined);
    };

    const getErrorAlert = (message: string) => {
        return {
            icon: (
                <FontAwesomeIcon
                    icon={faTimesCircle}
                    color={theme.palette.error.main}
                />
            ),
            title: strings('ermConstants:dlg_title_error'),
            contentText: message,
        };
    };

    const onCopyClicked = () => {
        if (hasChanges()) {
            void saveDiagram();
        }
        setVisibleDialog(CurrentDialog.COPY);
    };

    const onCopyLink = () => {
        enqueueSuccess(strings('bowtie:file.message.linkSuccessfullyCopied'));
    };

    const onDeleteClicked = () => {
        showConfirmationAlert({
            type: AlertType.Warning,
            title: strings('bowtie:file.title.deleteBowTieFile', { name: bowTie?.name }),
            contentText: strings('bowtie:file.message.confirmDeleteQuestion', { name: bowTie?.name }),
            onConfirm: async () => {
                try {
                    await deleteDiagram({ id: id! });
                    enqueueSuccess(strings('bowtie:file.message.successfullyDeleted'));
                    back();
                } catch (err) {
                    showConfirmationAlert(getErrorAlert(err.response || strings('bowtie:message.deleteFileError')));
                }
            },
            confirmButtonLabel: strings('ermMessages:btn_delete'),
            cancelButtonLabel: strings('ermMessages:btn_cancel'),
        });
    };

    const diagramSettings = (
        <Box sx={{ display: 'flex', gap: 1 }}>
            <Switch
                label={strings('bowtie:diagram.button.showLegend')}
                checked={showLegendEnabled}
                onChange={legendVisiblityChange}
                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-show-legend-switch`}
            />
            <Switch
                label={strings('bowtie:diagram.button.showControls')}
                checked={showControlsEnabled}
                onChange={controlsVisiblityChange}
                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-show-controls-switch`}
            />
            <Switch
                label={strings('bowtie:diagram.button.libraryLink')}
                checked={libraryLinkEnabled}
                onChange={libraryLinkVisible}
                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-library-link-switch`}
            />
        </Box>
    );

    const diagramComponent = useMemo(() => {
        if (bowTie && styles && !isDiagramLoading && !isStyleLoading && !isChangedLinks) {
            return (
                <DiagramComponent
                    key={`${bowTie?.name ?? ''}${bowTie?.description}${refreshDiagram}`}
                    ref={diagramComponentRef}
                    data={bowTie}
                    styles={styles}
                    showControls={showControlsEnabled}
                    layoutingEnabled={layoutingEnabled}
                    libraryLinkEnabled={libraryLinkEnabled}
                    onChange={() => setIsDirty(hasChanges())}
                />
            );
        }

        return isDiagramLoading || isStyleLoading ? <Loading /> : null;
    }, [
        bowTie,
        styles,
        isDiagramLoading,
        showControlsEnabled,
        layoutingEnabled,
        libraryLinkEnabled,
        hasChanges,
        isStyleLoading,
        linkChangeOption,
        isChangedLinks,
        refreshDiagram,
    ]);

    const legend = useMemo(() => {
        if (styles && showLegendEnabled) {
            return <Legend nodeStyles={styles.nodeStyle} />;
        }
        return null;
    }, [styles, showLegendEnabled]);

    const diagramHeader = (
        <BowtieDiagramHeader
            title={bowTie?.name || ''}
            status={bowTie?.locked ? bowTie?.status + strings('bowtie:message.locked') : bowTie?.status}
            diagramSettingsToolbar={diagramSettings}
            legend={legend}
            onZoomIn={() => diagramComponentRef.current?.executeCommand(ICommand.INCREASE_ZOOM)}
            onZoomOriginal={() => diagramComponentRef.current?.executeCommand(ICommand.ZOOM, 1.0)}
            onZoomOut={() => diagramComponentRef.current?.executeCommand(ICommand.DECREASE_ZOOM)}
            onFitContent={() => diagramComponentRef.current?.executeCommand(ICommand.FIT_GRAPH_BOUNDS)}
            onUndo={() => diagramComponentRef.current?.executeCommand(ICommand.UNDO)}
            onRedo={() => diagramComponentRef.current?.executeCommand(ICommand.REDO)}
            onPDFExport={() => diagramComponentRef.current?.exportPDF()}
            onImageExport={() => diagramComponentRef.current?.exportImage()}
            onExportDiagram={() => exportDiagram(diagramData, diagramData?.name)}
            bulkUpdate={() => bulkUpdate()}
            onSave={saveDiagram}
            onCancel={back}
            onCopy={onCopyClicked}
            onDelete={onDeleteClicked}
            onEdit={onEdit}
            onPrint={() => diagramComponentRef.current?.printDiagram()}
            onLink={() => setVisibleDialog(CurrentDialog.LINKS)}
            saveDisabled={!isDirty}
            canEdit={!bowTie?.locked}
        />
    );

    function processBowtieForBulk() {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const updatedNodes = diagramComponentRef.current?.getDiagramModel()?.nodes?.map((node) => ({
            ...node,
            originalName: node.name,
        }));

        const updateData = {
            ...data,
            diagramModel: {
                ...diagramComponentRef.current?.getDiagramModel(),
                nodes: updatedNodes,
            },
        };

        return updateData as BowTieDiagramRest;
    }

    return (
        <>
            <Page
                header={diagramHeader}
                diagramComponent={diagramComponent}
            />
            {bowTie && (
                <BowTieCopyDialog
                    visible={visibleDialog === CurrentDialog.COPY}
                    redirect={true}
                    onClose={onDialogClosed}
                    data={bowTie}
                />
            )}
            {bowTie && bowTie.id !== undefined && (
                <BowTieDetails
                    visible={detailsVisible}
                    detailId={bowTie.id}
                    onClose={() => setDetailsVisible(false)}
                    onCopyLink={onCopyLink}
                />
            )}
            {bowTie && bowTie.id !== undefined && (
                <BowTieLinksDialog
                    visible={visibleDialog === CurrentDialog.LINKS}
                    onClose={onDialogClosed}
                    diagramId={bowTie.id}
                />
            )}
            <BowTieExportDialog
                visible={visibleDialog === CurrentDialog.EXPORT}
                onClose={onDialogClosed}
                bowtieDefinition={definition}
                data={diagramData as BowTieDiagramRest}
            />
            {visibleDialog === CurrentDialog.BULK_UPDATE && (
                <BowTieImportDialog
                    bowtieDefinition={definition}
                    bowtieDiagramData={processBowtieForBulk()}
                    visible={visibleDialog === CurrentDialog.BULK_UPDATE}
                    onClose={onDialogClosed}
                    onConfirm={(data) => {
                        setRefreshDiagram((state) => state + 1);
                        setBowTie(data);
                        setIsDirty(true);
                        onDialogClosed();
                        return;
                    }}
                />
            )}

            <Dialog
                visible={visibleDialog === CurrentDialog.SAVEALERT}
                title={strings('common:title.confirmation')}
                width={500}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={onDialogClosed}
                            dataTestId="button-no"
                        >
                            {strings('common:button.no')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'primary'}
                            onClick={async () => {
                                onDialogClosed();
                                await saveDiagram();
                                setVisibleDialog(CurrentDialog.EXPORT);
                            }}
                            dataTestId="button-yes"
                        >
                            {strings('common:button.yes')}
                        </Button>
                    </DialogActions>
                }
            >
                <Typography>{strings('bowtie:message.exportUnsavedDiagram')}</Typography>
            </Dialog>
            <LoadingOverlay
                open={isDiagramSaving}
                message={strings('ermMessages:saving')}
            />
        </>
    );
};

export default BowtieDiagramPage;
