import React from 'react';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import { NodeStyle, NodeType } from 'bowtie/types';
import { strings } from 'common/utils/i18n';
import { getStyleForType } from 'bowtie/diagram/components/DiagramComponent/components/utils';
import { NODE_LEGEND_LABELS } from '../../../DiagramComponent/components/constants';

type LegendProps = {
    nodeStyles: Map<NodeType, NodeStyle>;
};

const LegendContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    minHeight: '32px',
    flexDirection: 'row',
    boxSizing: 'border-box',
    flexWrap: 'nowrap',
    alignItems: 'start',
    padding: '6px 10px 6px 10px',
    border: '2px solid',
    borderColor: theme.palette.protechtGrey?.grey_238,
    borderRadius: '2px',
}));

const LegendItemContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: '-8px',
});

const LegendItem = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    paddingLeft: '12px',
    paddingBottom: '8px',
});

const legendNodes = [NodeType.Cause, NodeType.RiskEvent, NodeType.Impact, NodeType.Control];

const Legend: React.FC<LegendProps> = (props: LegendProps) => {
    if (!props.nodeStyles) {
        return <></>;
    }
    return (
        <LegendContainer display="flex">
            <Typography variant="subtitle2">{strings('bowtie:diagram.title.legend')}</Typography>
            <LegendItemContainer display="flex">
                {legendNodes.map((nodeType) => {
                    return (
                        <LegendItem key={nodeType}>
                            <div
                                style={{
                                    width: '32px',
                                    height: '16px',
                                    marginRight: '6px',

                                    backgroundColor: getStyleForType(props.nodeStyles, nodeType).fillColor1,
                                }}
                            />
                            <Typography
                                variant="subtitle2"
                                fontWeight={400}
                            >
                                {NODE_LEGEND_LABELS[nodeType]}
                            </Typography>
                        </LegendItem>
                    );
                })}
            </LegendItemContainer>
        </LegendContainer>
    );
};

export default Legend;
