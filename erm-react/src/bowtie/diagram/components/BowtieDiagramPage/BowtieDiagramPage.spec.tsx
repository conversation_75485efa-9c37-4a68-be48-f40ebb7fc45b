import React from 'react';
import { render } from 'test/utils';
import BowtieDiagramPage from './BowtieDiagramPage';

jest.mock('bowtie/diagram/components/BowtieDiagramPage/BowtieDiagramPage', () => {
    document.createElement('div');
    return 'div';
});

describe('<BowtieDiagramPage/>', () => {
    const setup = () => {
        return render(<BowtieDiagramPage />);
    };

    it('was rendered', () => {
        setup();
        expect(document.body).toMatchSnapshot();
    });
});
