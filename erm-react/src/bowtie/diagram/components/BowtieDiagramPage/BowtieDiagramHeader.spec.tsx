import React from 'react';
import { screen, within } from '@testing-library/react';
import { render } from 'test/utils';
import BowtieDiagramHeader from './BowtieDiagramHeader';

jest.mock('yfiles', () => ({
    Size: class Size {
        width: number;
        height: number;
        constructor(width: number, height: number) {
            this.width = width;
            this.height = height;
        }
    },
}));

describe('<BowtieDiagramHeader/>', () => {
    const mockFn = jest.fn();
    const mockedCommonProps = {
        title: 'text title',
        diagramSettingsToolbar: undefined,
        onZoomIn: mockFn,
        onZoomOut: mockFn,
        onZoomOriginal: mockFn,
        onFitContent: mockFn,
        onUndo: mockFn,
        onRedo: mockFn,
        onPDFExport: mockFn,
        onImageExport: mockFn,
        onPrint: mockFn,
        onDelete: mockFn,
        onCancel: mockFn,
        onSave: mockFn,
        onEdit: mockFn,
        onCopy: mockFn,
        onLink: mockFn,
        bulkUpdate: mockFn,
        onImportDiagram: mockFn,
        onExportDiagram: mockFn,
        saveDisabled: false,
        canEdit: true,
    };

    it('was rendered', () => {
        const { container } = render(<BowtieDiagramHeader {...mockedCommonProps} />);
        expect(container).toBeInTheDocument();
        expect(container).toBeVisible();
        expect(document.body).toMatchSnapshot();
    });

    it('has correct title', () => {
        render(<BowtieDiagramHeader {...mockedCommonProps} />);
        const diagramTitle = screen.getByTestId('diagram-name');
        expect(diagramTitle).toHaveTextContent('text title');
    });

    it('has status when available', () => {
        render(
            <BowtieDiagramHeader
                {...mockedCommonProps}
                status="Some status"
            />,
        );
        const status = screen.getByRole('heading', { name: 'Some status' });
        expect(status).toBeInTheDocument();
    });

    it('has legend when available', () => {
        render(
            <BowtieDiagramHeader
                {...mockedCommonProps}
                legend="Some legend"
            />,
        );
        const legend = screen.getByText('Some legend');
        expect(legend).toBeInTheDocument();
    });

    it('has diagram settings toolbar when available', () => {
        render(
            <BowtieDiagramHeader
                {...mockedCommonProps}
                diagramSettingsToolbar={<div data-testid="some-toolbar">toolbar settings</div>}
            />,
        );
        const settingsToolbarContent = within(screen.getByTestId('some-toolbar')).getByText('toolbar settings');
        expect(settingsToolbarContent).toBeInTheDocument();
    });
});
