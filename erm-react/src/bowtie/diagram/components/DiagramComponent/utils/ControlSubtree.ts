import { YNode } from 'yfiles';

/**
 * Class to hold tree structure of controls
 */
export class ControlSubtree {
    // root/parent control of subtree
    private _control: YNode;
    // subtree level
    private _level: number;
    // child controls connected to root control
    private _children: ControlSubtree[] = [];

    constructor(control: YNode, level: number) {
        this._control = control;
        this._level = level;
    }

    get control(): YNode {
        return this._control;
    }

    set control(value: YNode) {
        this._control = value;
    }

    get children(): ControlSubtree[] {
        return this._children;
    }

    set children(value: ControlSubtree[]) {
        this._children = value;
    }

    get level(): number {
        return this._level;
    }

    set level(value: number) {
        this._level = value;
    }

    /**
     * @return all controls of subtree including root control flattened to array.
     */
    public getAllControlsFlat(): YNode[] {
        const controls = [this._control];
        if (this._children && this._children.length > 0) {
            this._children.forEach((child) => controls.push(...child.getAllControlsFlat()));
        }
        return controls;
    }

    /**
     * @return maximal depth of subtree
     */
    getMaxDepth(): number {
        return Math.max(this._level, ...(this._children?.map((child) => child.getMaxDepth()) ?? []));
    }
}
