import { BaseClass, IClipboardHelper, IEdge, IGraph, IGraphClipboardContext, IModelItem, INode, List } from 'yfiles';
import { ElementIdProvider } from 'common/diagram/utils/ElementIdProvider';
import { BowtieDiagramUtils } from './BowtieDiagramUtils';
import { ClipboardSubtreeState, EdgeType, Node, NodeData, NodeType } from 'bowtie/types';
import DiagramPredicates from './DiagramPredicates';
import DiagramLayout from '../layout/DiagramLayout';

/**
 * ClipboardHelper implementation that decides if node can be pasted and connected to currently selected node
 */
export class ClipboardHelper extends BaseClass<IClipboardHelper>(IClipboardHelper) {
    private _targetNode: INode;
    private _idProvider: ElementIdProvider;
    private _layout: DiagramLayout;

    constructor(idProvider: ElementIdProvider, layout: DiagramLayout) {
        super();
        this._layout = layout;
        this._idProvider = idProvider;
    }

    set targetNode(value: INode) {
        this._targetNode = value;
    }

    /**
     * All nodes except MainRiskEvent can be copied.
     * @param {IGraphClipboardContext} context The context in which this interface is used, can be null
     * @param {IModelItem} item The item to be copied
     * @see Specified by {@link IClipboardHelper#shouldCopy}.
     * @return {boolean}
     */
    shouldCopy(context: IGraphClipboardContext, item: IModelItem): boolean {
        if (item instanceof INode) {
            return item.tag?.type !== NodeType.MainRiskEvent;
        }
        return true;
    }

    /**
     * All nodes except MainRiskEvent can be copied.
     * @param {IGraphClipboardContext} context The context in which this interface is used, can be null
     * @param {IModelItem} item The item to be cut
     * @see Specified by {@link IClipboardHelper#shouldCut}.
     * @return {boolean}
     */
    shouldCut(context: IGraphClipboardContext, item: IModelItem): boolean {
        if (item instanceof INode) {
            return item.tag?.type !== NodeType.MainRiskEvent;
        }
        return true;
    }

    /**
     * Conditional node pasting.
     * @param {IGraphClipboardContext} context The context in which this interface is used, can be null
     * @param {IModelItem} item The item to be pasted
     * @param {object} userData The state memento that had been created during cut or copy
     * @see Specified by {@link IClipboardHelper#shouldPaste}.
     * @return {boolean}
     */
    shouldPaste(context: IGraphClipboardContext, item: IModelItem, userData: ClipboardSubtreeState): boolean {
        if (item instanceof INode && this._targetNode) {
            return BowtieDiagramUtils.canPasteTo(item, this._targetNode, userData.nodes, context.targetGraph);
        }
        return false;
    }

    /**
     * If the copied node is a parent node of some graph subtree,
     * populate clipboard state with its children nodes and edges connecting these nodes.
     * (see {@link CopyItem} implementation).
     * @param {IGraphClipboardContext} context The context in which this interface is used, can be null
     * @param {IModelItem} item The item to be copied
     * @see Specified by {@link IClipboardHelper#copy}.
     * @return {Object}
     */
    copy(context: IGraphClipboardContext, item: INode): ClipboardSubtreeState {
        const subtreeNodes = new List<INode>();
        const subtreeEdges = new List<IEdge>();
        BowtieDiagramUtils.getSubtree(
            BowtieDiagramUtils.getGraph(context?.sourceGraph, DiagramPredicates.WITHOUT_SECONDARY_EDGES),
            item,
            subtreeNodes,
            subtreeEdges,
        );
        subtreeNodes.remove(item);
        return { nodes: subtreeNodes, edges: subtreeEdges };
    }

    /**
     * (see {@link CopyItem} implementation).
     * @param {IGraphClipboardContext} context The context in which this interface is used, can be null
     * @param {IModelItem} item The item to be cut
     * @see Specified by {@link IClipboardHelper#cut}.
     * @return {Object}
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/explicit-module-boundary-types
    cut(context, item): null {
        return null;
    }

    /**
     * If the pasted node state memento holds subtree nodes, create duplicate of this subtree for pasted node.
     * @param {IGraphClipboardContext} context The context in which this interface is used, can be null
     * @param {Object} item The copied item The item to be pasted
     * @param {object} userData The state memento that had been created during cut or copy
     * @see Specified by {@link IClipboardHelper#paste}.
     */
    paste(context: IGraphClipboardContext, item: IModelItem, userData: ClipboardSubtreeState): void {
        if (this._targetNode && userData && context) {
            const graph = BowtieDiagramUtils.getGraph(context.targetGraph, DiagramPredicates.WITHOUT_SECONDARY_EDGES);
            if (item) {
                const originalId = item.tag.id;
                item.tag = { ...item?.tag, id: this._idProvider.getNextNodeId() };
                // Possibly better solution is to update isLeft for BLANK origin cases but this is safer now.
                if (item.tag.originType === NodeType.Blank) {
                    item.tag.originType = item.tag.type;
                }
                let isLeft = BowtieDiagramUtils.isLeft(this._targetNode as INode, graph);
                if (BowtieDiagramUtils.isRoot(this._targetNode)) {
                    isLeft = BowtieDiagramUtils.isLeft(item as INode, graph);
                }
                if (isLeft && (item.tag as NodeData).type !== NodeType.Control) {
                    if (item.tag.type === NodeType.RiskEvent) {
                        item.tag.originType = NodeType.Cause;
                    }
                    graph.createEdge(item as INode, this._targetNode, graph.edgeDefaults.style, {
                        id: this._idProvider.getNextEdgeId(),
                        type: EdgeType.DEFAULT,
                    });
                } else {
                    if (item.tag.type === NodeType.RiskEvent) {
                        item.tag.originType = NodeType.Impact;
                    }
                    graph.createEdge(this._targetNode, item as INode, context.targetGraph.edgeDefaults.style, {
                        id: this._idProvider.getNextEdgeId(),
                        type: EdgeType.DEFAULT,
                    });
                }
                const directChildren = BowtieDiagramUtils.findDirectChildren(originalId, userData.nodes, userData.edges);
                this.renderSubtree(item as INode, directChildren, userData.nodes, userData.edges, graph);
            }
            this._layout.applyLayout(context.targetGraph);
            context.clipboard.clear();
        }
    }

    private renderSubtree(root: INode, directChildren: List<INode>, allSubtreeNodes: List<INode>, allSubtreeEdges: List<IEdge>, graph: IGraph) {
        directChildren &&
            directChildren.forEach((node: Node) => {
                const nodeToRemove = allSubtreeNodes.find((subtreeNode) => node.tag.id === subtreeNode.tag.id);
                nodeToRemove && allSubtreeNodes.remove(nodeToRemove);
                const isLeft = BowtieDiagramUtils.isLeft(root, graph);
                const nodeData = { ...node.tag, id: this._idProvider.getNextNodeId() };
                if (nodeData.type === NodeType.RiskEvent) {
                    nodeData.originType = root.tag.originType;
                }
                const newChild = graph.createNode({ tag: nodeData });
                graph.addLabel(newChild, newChild.tag.name);
                if (isLeft && (newChild.tag as NodeData).type !== NodeType.Control) {
                    graph.createEdge(newChild, root, graph.edgeDefaults.style, { id: this._idProvider.getNextEdgeId(), type: EdgeType.DEFAULT });
                } else {
                    graph.createEdge(root, newChild, graph.edgeDefaults.style, { id: this._idProvider.getNextEdgeId(), type: EdgeType.DEFAULT });
                }
                const children = BowtieDiagramUtils.findDirectChildren(node.tag.id, allSubtreeNodes, allSubtreeEdges);
                this.renderSubtree(newChild, children, allSubtreeNodes, allSubtreeEdges, graph);
            });
    }
}
