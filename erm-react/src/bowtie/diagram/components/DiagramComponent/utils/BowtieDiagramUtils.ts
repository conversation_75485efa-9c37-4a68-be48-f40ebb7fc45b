import { FilteredGraph<PERSON><PERSON>per, IDataProvider, IEdge, IGraph, INode, List, YNode } from 'yfiles';

import { DiagramPredicate, Node, NodeType, LinkModel, NodeModel, DataModel } from 'bowtie/types';
import { NonEmptyArray } from 'common/types';
import DiagramPredicates from './DiagramPredicates';
import { ControlSubtree } from './ControlSubtree';
import { DiagramUtils } from 'common/diagram/utils/DiagramUtils';
import { ElementIdProvider } from 'common/diagram/utils/ElementIdProvider';

/**
 * This class contains utility methods that deal with the bowtie.
 */
export class BowtieDiagramUtils extends DiagramUtils {
    /**
     * Gets the root.
     * @param {IGraph} graph The input graph.
     */
    static getRoot(graph: IGraph): INode {
        return graph.nodes.first((node) => node.tag.type === NodeType.MainRiskEvent);
    }

    /**
     * Creates the arrays containing the nodes and edges of a subtree
     * of a given root.
     * @param {IGraph} graph The input graph.
     * @param {INode} subtreeRoot The root node of the subtree.
     * @param {List} nodes A list to be filled with the nodes of the subtree.
     * @param {List} edges A list to be filled with the edges of the subtree.
     */
    static getSubtree(graph: IGraph, subtreeRoot: INode, nodes: List<INode>, edges: List<IEdge>): void {
        if (BowtieDiagramUtils.isLeft(subtreeRoot, graph) && subtreeRoot?.tag?.type !== NodeType.Control) {
            const inEdgesAt = graph.inEdgesAt(subtreeRoot);
            inEdgesAt.forEach((inEdge) => {
                BowtieDiagramUtils.getSubtree(graph, inEdge.sourceNode!, nodes, edges);
            });
            graph.outEdgesAt(subtreeRoot).forEach((edge) => {
                if (edge.targetNode?.tag?.type === NodeType.Control) {
                    BowtieDiagramUtils.getSubtree(graph, edge.targetNode, nodes, edges);
                } else {
                    edges.add(edge);
                }
            });
        } else {
            const outEdges = graph.outEdgesAt(subtreeRoot);
            outEdges.forEach((outEdge) => {
                BowtieDiagramUtils.getSubtree(graph, outEdge.targetNode!, nodes, edges);
            });
            graph.inEdgesAt(subtreeRoot).forEach((edge) => {
                edges.add(edge);
            });
        }
        nodes.add(subtreeRoot);
    }

    /**
     * Gets the first outgoing edge that's not a control.
     * @param {INode} node The given node.
     * @param {IGraph} graph The input graph.
     * @return {IEdge}
     */
    static getOutEdge(node: INode, graph: IGraph): IEdge | null {
        return graph.outEdgesAt(node).find((edge) => edge.targetNode?.tag?.type !== NodeType.Control);
    }

    /**
     * Returns whether a node is on the left of the root.
     * @param {Node} node The given node.
     * @param {IGraph} graph The input graph.
     * @return {boolean} True if a node is on the left of the root, false otherwise
     */
    static isLeft(node: Node | null | undefined, graph: IGraph): boolean {
        let isLeft;
        const type = node?.tag?.originType ?? node?.tag?.type;
        if (type === NodeType.Cause) {
            isLeft = true;
        } else if (type === NodeType.Impact) {
            isLeft = false;
        } else {
            isLeft = this.isMainRiskAsSuccessor((node as INode)!, graph);
        }
        return isLeft;
    }

    /**
     * It check if node is connected to BowTie Diagram.
     * @param {INode} node The given node.
     * @param {IGraph} graph The input graph
     * @return {boolean} True if node is connected to by any means to root, false otherwise.
     */
    public static isConnectedToRoot(node: INode, graph: IGraph): boolean {
        return (
            BowtieDiagramUtils.isRoot(node) || BowtieDiagramUtils.isMainRiskAsSuccessor(node, graph) || BowtieDiagramUtils.isMainRiskAsPredecessor(node, graph)
        );
    }

    /**
     * It check if node can be dragged to other node base on BowTie restrictions.
     * @param {INode} node The given node.
     * @param {INode} connectTo Node to be connected to
     * @param {boolean} toLeft determine if its left or right side.
     * @param {IGraph} graph The input graph
     * @return {boolean} True if node can be connected to other node, false otherwise.
     */
    public static canDragTo(node: INode, connectTo: INode | null, toLeft: boolean, graph: IGraph): boolean {
        if (!node || !connectTo) {
            return false;
        }
        switch (node?.tag?.type) {
            case NodeType.Cause:
                return toLeft && (connectTo?.tag?.type === NodeType.RiskEvent || connectTo?.tag?.type === NodeType.MainRiskEvent);
            case NodeType.Impact:
                return !toLeft && (connectTo?.tag?.type === NodeType.RiskEvent || connectTo?.tag?.type === NodeType.MainRiskEvent);
            case NodeType.RiskEvent:
                return (
                    (toLeft
                        ? graph.outEdgesAt(node).filter((edge) => edge.targetNode?.tag.type !== NodeType.Control).size === 0 ||
                          this.isMainRiskAsSuccessor(node, graph)
                        : graph.inEdgesAt(node).size === 0 || this.isMainRiskAsPredecessor(node, graph)) &&
                    (connectTo?.tag?.type === NodeType.RiskEvent || connectTo?.tag?.type === NodeType.MainRiskEvent)
                );
            case NodeType.Control:
                return true;
            default:
                return false;
        }
    }

    public static canPasteTo(node: INode, pasteTo: INode | null, subtreeNodes: List<INode>, graph: IGraph): boolean {
        if (node && pasteTo && BowtieDiagramUtils.isConnectedToRoot(pasteTo, graph)) {
            if (BowtieDiagramUtils.isRoot(pasteTo)) {
                return node.tag.type !== NodeType.Blank;
            }
            const isLeftTreeElement = BowtieDiagramUtils.isLeft(pasteTo, graph);
            if (node?.tag?.type === NodeType.Cause) {
                return isLeftTreeElement && (pasteTo?.tag?.type === NodeType.RiskEvent || pasteTo?.tag?.type === NodeType.MainRiskEvent);
            } else if (node?.tag?.type === NodeType.Impact) {
                return !isLeftTreeElement && (pasteTo?.tag?.type === NodeType.RiskEvent || pasteTo?.tag?.type === NodeType.MainRiskEvent);
            } else if (node?.tag?.type === NodeType.RiskEvent) {
                if (pasteTo?.tag?.type === NodeType.RiskEvent) {
                    if (
                        subtreeNodes?.size === 0 ||
                        !subtreeNodes.some((subtreeNode) => subtreeNode.tag.type === NodeType.Impact || subtreeNode.tag.type === NodeType.Cause)
                    ) {
                        return true;
                    }
                    return isLeftTreeElement ? node.tag.originType === NodeType.Cause : node.tag.originType === NodeType.Impact;
                }
            } else if (node?.tag?.type === NodeType.Control && pasteTo?.tag?.originType !== NodeType.Blank) {
                return true;
            }
        }
        return false;
    }

    /**
     * It checks if connection is allowed between two nodes as per BowTie rules.
     * @param {INode} sourceNode source node.
     * @param {INode} targetNode target node.
     * @param {IGraph} graph to check against.
     */
    public static isConnectionAllowed(sourceNode: INode, targetNode: INode, graph: IGraph): boolean {
        if (!BowtieDiagramUtils.isConnectedToRoot(sourceNode, graph) || !BowtieDiagramUtils.isConnectedToRoot(targetNode, graph)) {
            return false;
        }
        if (BowtieDiagramUtils.isRoot(sourceNode) || BowtieDiagramUtils.isRoot(targetNode)) {
            return true;
        }
        return (
            (BowtieDiagramUtils.isLeft(sourceNode, graph) && BowtieDiagramUtils.isLeft(targetNode, graph)) ||
            (!BowtieDiagramUtils.isLeft(sourceNode, graph) && !BowtieDiagramUtils.isLeft(targetNode, graph))
        );
    }

    /**
     * Checks whether provided node is the last node of its subtree.
     * @param {INode} node node
     * @param {IGraph} graph The input graph to check.
     */
    public static isEndNode(node: INode, graph: IGraph): boolean {
        return BowtieDiagramUtils.isLeft(node, graph)
            ? graph.predecessors(node).size === 0
            : graph.successors(node).filter((successor) => successor?.tag?.type !== NodeType.Control).size === 0;
    }

    private static isMainRiskAsSuccessor(node: INode, graph: IGraph): boolean {
        if (node?.tag?.type === NodeType.Control) {
            return this.isMainRiskAsSuccessorOfControl(node, graph);
        } else {
            return this.isMainRiskAsSuccessorOfNode(node, graph);
        }
    }

    /**
     * Recursively check all successors if there is MainRiskEvent.
     * @param {INode} node node element to check successors for.
     * @param {IGraph} graph input graph to check.
     * @param {List<INode>} visitedNodes visited nodes so we don't iterate over nodes multiple times.
     * @return {boolean} True if a node is on the left of the root, false otherwise.
     */
    private static isMainRiskAsSuccessorOfNode(node: INode, graph: IGraph, visitedNodes?: List<INode>): boolean {
        if (!visitedNodes) {
            visitedNodes = new List<INode>();
        }
        if (visitedNodes.indexOf(node) != -1) {
            return false;
        }
        visitedNodes.add(node);
        return !!graph?.successors(node)?.find((successor) => {
            if (successor) {
                if (successor?.tag?.type === NodeType.MainRiskEvent) {
                    return true;
                } else {
                    return this.isMainRiskAsSuccessorOfNode(successor, graph, visitedNodes);
                }
            } else {
                return false;
            }
        });
    }

    /**
     * Recursively check if there is MainRiskEvent as successor of Control.
     * @param {INode} node node element to check successors for.
     * @param {IGraph} graph input graph to check.
     * @param {List<INode>} visitedNodes visited nodes so we don't iterate over nodes multiple times.
     * @return {boolean} True if a node is on the left of the root, false otherwise.
     */
    private static isMainRiskAsSuccessorOfControl(node: INode, graph: IGraph, visitedNodes?: List<INode>): boolean {
        if (!visitedNodes) {
            visitedNodes = new List<INode>();
        }
        if (visitedNodes.indexOf(node) != -1) {
            return false;
        }
        visitedNodes.add(node);
        return !!graph?.predecessors(node)?.find((predecessor) => {
            if (predecessor) {
                if (predecessor?.tag?.type === NodeType.MainRiskEvent) {
                    return true;
                } else if (predecessor.tag.type === NodeType.Control) {
                    return this.isMainRiskAsSuccessorOfControl(predecessor, graph, visitedNodes);
                } else {
                    return this.isMainRiskAsSuccessorOfNode(predecessor, graph, visitedNodes);
                }
            } else {
                return false;
            }
        });
    }

    /**
     * Recursively check all predecessors if there is MainRiskEvent.
     * @param {INode} node node element to check predecessors for.
     * @param {IGraph} graph input graph to check.
     * @return {boolean} True if a node is on the right of the root, false otherwise.
     */
    private static isMainRiskAsPredecessor(node: INode, graph: IGraph): boolean {
        return !!graph?.predecessors(node)?.find((predecessor) => {
            if (predecessor) {
                if (predecessor?.tag?.type === NodeType.MainRiskEvent) {
                    return true;
                } else {
                    return this.isMainRiskAsPredecessor(predecessor, graph);
                }
            } else {
                return false;
            }
        });
    }

    /**
     * Returns whether a node is the root node.
     * @param {Node} node The given node.
     * @return {boolean} True if a node is the root, false otherwise
     */
    static isRoot(node: Node | null): boolean {
        return node?.tag?.type === NodeType.MainRiskEvent;
    }

    /**
     * Returns whether a node is the root node.
     * @param {Node} node The given node.
     * @param originalDp IDataProvider associated with node.
     * @return {boolean} True if a node is the root, false otherwise
     */
    static isRootNode(node: YNode, originalDp: IDataProvider | null): boolean {
        if (originalDp && originalDp.get(node) instanceof INode) {
            const originalINode = originalDp.get(node) as Node;
            return originalINode.tag && originalINode.tag.type === NodeType.MainRiskEvent;
        }
        return false;
    }

    /**
     * @param {!YNode} node
     * @param {?IDataProvider} originalDp
     * @returns {boolean}
     */
    static isControl(node: YNode, originalDp?: IDataProvider | null): boolean {
        if (originalDp && originalDp.get(node) instanceof INode) {
            const originalINode = originalDp.get(node) as Node;
            return originalINode.tag && originalINode.tag.type === NodeType.Control;
        }
        return false;
    }

    /**
     * @param {!YNode} node
     * @param {?IDataProvider} originalDp
     * @returns {!Array.<YNode>}
     */
    static getControls(node: YNode, originalDp?: IDataProvider | null): Array<YNode> | undefined {
        return node.successors?.filter((successor) => this.isControl(successor, originalDp)).toArray();
    }

    /**
     * Retrieve all Control children of given node not only direct successors
     * @param {!YNode} node
     * @param {?IDataProvider} originalDp
     * @param parentDepth
     * @returns {!Array.<YNode>}
     */
    static getControlSubtree(node: YNode, originalDp?: IDataProvider | null, parentDepth = 0): ControlSubtree[] {
        const nodeLevel = parentDepth + 1;
        const subtreeControls: ControlSubtree[] =
            this.getControls(node, originalDp)?.map((control) => {
                return new ControlSubtree(control, nodeLevel);
            }) || [];
        if (subtreeControls) {
            subtreeControls.forEach((subtree: ControlSubtree) => {
                if (subtree.control.successors) {
                    subtree.children.push(...this.getControlSubtree(subtree.control, originalDp, nodeLevel));
                }
            });
        }
        return subtreeControls;
    }

    /**
     * @param {!YNode} control
     * @returns {!YNode}
     */
    static getParentOfControl(control: YNode): YNode | undefined {
        return control.firstInEdge?.source;
    }

    /**
     * It traverse entire tree base on left/right position and apply callback function to everynode.
     * Useful for deleting/deselecting entire subtree.
     * @param {INode} node
     * @param {IGraph} graph
     * @param callback
     */
    static applyForSubtree(node: INode, graph: IGraph, callback: (node: INode) => void): void {
        if (BowtieDiagramUtils.isLeft(node, graph) && node?.tag?.type !== NodeType.Control) {
            graph
                .predecessors(node)
                .toArray()
                .forEach((childNode) => {
                    this.applyForSubtree(childNode, graph, callback);
                });
            graph
                .successors(node)
                .toArray()
                .forEach((childNode) => {
                    if (childNode?.tag?.type === NodeType.Control) {
                        this.applyForSubtree(childNode, graph, callback);
                    }
                });
        } else {
            graph
                .successors(node)
                .toArray()
                .forEach((childNode) => {
                    this.applyForSubtree(childNode, graph, callback);
                });
        }
        callback(node);
    }

    static findDirectChildren(rootId: number, nodes: List<INode>, edges: List<IEdge>): List<INode> {
        const childNodes = new List<INode>();
        edges.forEach((edge: IEdge) => {
            if (
                (edge.targetPort && edge.targetNode && (edge.targetNode as Node).tag?.id === rootId) ||
                (edge.sourcePort && edge.sourceNode && (edge.sourceNode as Node).tag?.id === rootId)
            ) {
                const child = nodes.find((node) => node.tag?.id !== rootId && (node === edge.targetNode || node === edge.sourceNode));
                if (child) {
                    childNodes.add(child);
                }
            }
        });
        return childNodes;
    }

    /**
     * It filters graph base on specified predicate(s).
     * @param graph graph to be wrapped into filtered graph(s).
     * @param predicates predicate or predicates to filter graph base on.
     * @return graph filtered graph base on predicate(s).
     */
    static getGraph(graph: IGraph, ...predicates: NonEmptyArray<DiagramPredicate>): IGraph {
        graph = this.getFullGraph(graph);

        if (predicates.indexOf(DiagramPredicates.FULL_GRAPH) !== -1) {
            return graph;
        }
        predicates.forEach((predicate) => {
            graph = new FilteredGraphWrapper(graph, predicate.nodePredicate, predicate.edgePredicate);
        });

        return graph;
    }

    /**
     * Generate new ID for all the nodes and edges with duplicate IDs and return all the nodes and edges not pointing to the invalid node IDs
     * @param nodes to be checked and handled if needed
     * @param edges to be checked and handled if needed
     * @returns valid data
     */
    static handleDuplicates(nodes: NodeModel[], edges: LinkModel[], elementProvider: ElementIdProvider): DataModel {
        const duplicateNodes = nodes.filter((item, index) => this.hasDuplicateId(nodes, item, index));
        const duplicateNodeIds = new Set(duplicateNodes.map((node) => node.key));

        duplicateNodes.forEach((node) => (node.key = elementProvider.getNextNodeId()));

        // filter out edges that are pointing to the invalid node IDs
        const validEdges = edges.filter((edge) => !duplicateNodeIds.has(edge.from) && !duplicateNodeIds.has(edge.to));
        const duplicateEdges = validEdges.filter((item, index) => this.hasDuplicateId(edges, item, index));

        duplicateEdges.forEach((edge) => (edge.key = elementProvider.getNextEdgeId()));

        return {
            nodes: nodes,
            links: validEdges,
        };
    }

    /**
     * Tries to remove all invalid links in graph as per BowTie rules to ensure graph is valid.
     * @param graph valid graph as per BowTie rules.
     */
    static ensureValid(graph: IGraph): IGraph {
        graph = BowtieDiagramUtils.getGraph(graph, DiagramPredicates.FULL_GRAPH);
        const filteredGraph = BowtieDiagramUtils.getGraph(graph, DiagramPredicates.WITHOUT_SECONDARY_EDGES);
        // filter graph from invalid edges
        graph.edges.toArray().forEach((edge) => {
            if (!edge.sourceNode || !edge.targetNode || !BowtieDiagramUtils.isConnectionAllowed(edge.sourceNode, edge.targetNode, filteredGraph)) {
                graph.remove(edge);
            }
        });
        return graph;
    }
}
