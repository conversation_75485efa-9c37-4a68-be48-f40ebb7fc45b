import { EdgeType, Node, NodeType, DiagramPredicate } from 'bowtie/types';
import { IEdge } from 'yfiles';

type PredicateName = 'WITHOUT_HIDDEN_CONTROL' | 'WITHOUT_SECONDARY_EDGES' | 'FULL_GRAPH';

const DiagramPredicates: Record<PredicateName, DiagramPredicate> = {
    FULL_GRAPH: {
        nodePredicate: () => true,
        edgePredicate: () => true,
    },
    WITHOUT_HIDDEN_CONTROL: {
        nodePredicate: (node: Node) => node.tag.type !== NodeType.Control || !node.tag.hidden,
        edgePredicate: () => true,
    },
    WITHOUT_SECONDARY_EDGES: {
        nodePredicate: () => true,
        edgePredicate: (edge: IEdge): boolean => !edge.tag || edge.tag.type !== EdgeType.SECONDARY,
    },
};

export default DiagramPredicates;
