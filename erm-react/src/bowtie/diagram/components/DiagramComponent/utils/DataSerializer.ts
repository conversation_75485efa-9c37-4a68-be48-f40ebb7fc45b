import { <PERSON>Graph, HashMap } from 'yfiles';

import { Node, DiagramModel, NodeModel, LinkModel, LinkData } from 'bowtie/types';
import { DELETED_LINK } from 'bowtie/diagram/constants';

export default class DataSerializer {
    /**
     * Serializes the graph to JSON.
     * @param {!IGraph} graph The graph
     * @returns {*}
     */
    static serializeToJSON(graph: IGraph): DiagramModel {
        const jsonOutput: DiagramModel = {
            nodes: [] as NodeModel[],
            links: [] as LinkModel[],
        };

        // serialize the nodes.
        const node2id = new HashMap<Node, number>();
        graph?.nodes.forEach((node: Node) => {
            // save the id to easily create the edgesSource afterwards
            node2id.set(node, node?.tag?.id);

            // serialize the node
            const jsonNode: NodeModel = {
                key: node?.tag?.id,
                name: node?.tag?.name,
                nodeType: node?.tag?.type,
                template: 'BOWTIE_NODE',
                position: {
                    x: node.layout.x,
                    y: node.layout.y,
                },
            };
            if (node?.tag?.originType) {
                jsonNode.originType = node?.tag?.originType;
            }
            if (node?.tag?.libraryLink && node.tag.libraryLink.id !== DELETED_LINK.id) {
                jsonNode.libraryLink = node.tag.libraryLink;
            }
            if (node?.tag?.rating) {
                jsonNode.rating = node?.tag?.rating;
            }
            jsonOutput.nodes.push(jsonNode);
        });

        // serialize the edges/links
        graph.edges.forEach((edge) => {
            const sourceId = node2id.get(edge.sourceNode)!;
            const targetId = node2id.get(edge.targetNode)!;
            jsonOutput.links.push({
                key: (edge.tag as LinkData).id,
                from: sourceId,
                to: targetId,
                template: (edge.tag as LinkData)?.type || 'DEFAULT',
            });
        });

        return jsonOutput;
    }
}
