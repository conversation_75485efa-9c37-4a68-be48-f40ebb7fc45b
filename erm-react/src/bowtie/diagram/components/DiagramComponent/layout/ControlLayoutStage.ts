import {
    Delegating<PERSON>odePlacer,
    LayeredNodePlacer,
    LayoutGraph,
    LayoutGraphAdapter,
    LayoutGraphHider,
    LayoutStageBase,
    NodeHalo,
    YList,
    YNode,
    YPoint,
} from 'yfiles';
import { BowtieDiagramUtils } from '../utils';
import { ControlSubtree } from '../utils/ControlSubtree';
import { DEFAULT_HORIZONTAL_LAYOUT_SPACING } from '../../../constants';

export default class ControlLayoutStage extends LayoutStageBase {
    private readonly spacing: number;
    private readonly xOffset: number;
    private rootPlacer: DelegatingNodePlacer;

    constructor(rootPlacer: DelegatingNodePlacer) {
        super();
        this.rootPlacer = rootPlacer;
        this.spacing = 5;
        this.xOffset = 20;
    }

    /**
     * @param {!LayoutGraph} graph
     */
    applyLayout(graph: LayoutGraph): void {
        //pre-process: find and hide assistants
        const originalNodeDp = graph.getDataProvider(LayoutGraphAdapter.ORIGINAL_NODE_DP_KEY);
        const halos = graph.createNodeMap();
        const controlsNodes: YNode[] = [];
        for (const node of graph.nodes || []) {
            if (!BowtieDiagramUtils.isControl(node, originalNodeDp)) {
                //since we hide all controls during layout, halo of a parent node must be big enough to contain all children Controls
                const controls = BowtieDiagramUtils.getControlSubtree(node, originalNodeDp);
                if (controls && controls.length > 0) {
                    const halo = this.getRequiredHalo(graph, node, controls);
                    halos.set(node, halo);
                    controls.forEach((control) => controlsNodes.push(...control.getAllControlsFlat()));
                    if (halo && BowtieDiagramUtils.isRootNode(node, originalNodeDp)) {
                        (this.rootPlacer.secondaryPlacer as LayeredNodePlacer).layerSpacing = halo.right + DEFAULT_HORIZONTAL_LAYOUT_SPACING;
                    }
                }
            }
        }
        const hider = new LayoutGraphHider(graph);
        for (const control of controlsNodes) {
            hider.hide(control);
        }

        //apply core (tree layout)
        graph.addDataProvider(NodeHalo.NODE_HALO_DP_KEY, halos);
        this.applyLayoutCore(graph);
        graph.removeDataProvider(NodeHalo.NODE_HALO_DP_KEY);
        graph.disposeNodeMap(halos);

        //post-process: re-insert and place the assistants
        hider.unhideAll();
        for (const node of graph.nodes || []) {
            if (!BowtieDiagramUtils.isControl(node, originalNodeDp)) {
                const controls = BowtieDiagramUtils.getControlSubtree(node, originalNodeDp);
                if (controls && controls.length > 0) {
                    this.placeControls(graph, node, controls);
                }
            }
        }
    }

    /**
     * @param {!LayoutGraph} graph
     * @param {!YNode} parent
     * @param {!Array.<YNode>} controls
     * @returns {!NodeHalo}
     */
    getRequiredHalo(graph: LayoutGraph, parent: YNode, controls: ControlSubtree[]): NodeHalo | null {
        let heightSum = 0;
        let maxRequiredWidth = 0;
        for (const control of controls) {
            control.getAllControlsFlat().forEach((c) => (heightSum += graph.getHeight(c) + this.spacing));
            maxRequiredWidth = Math.max(maxRequiredWidth, graph.getWidth(control.control) + this.xOffset * control.getMaxDepth());
        }
        const rightHalo = graph.getWidth(parent) < maxRequiredWidth ? maxRequiredWidth - graph.getWidth(parent) : 0;
        return NodeHalo.create(0, 0, heightSum, rightHalo);
    }

    /**
     * @param {!LayoutGraph} graph
     * @param {!YNode} parent
     * @param controlSubtree
     * @return next free y coordinate from last control subtree
     */
    placeControls(graph: LayoutGraph, parent: YNode, controlSubtree: ControlSubtree[]): number {
        const parentBottomY = graph.getY(parent) + graph.getHeight(parent);
        let currentY = parentBottomY + this.spacing;
        const parentX = graph.getX(parent);
        for (const item of controlSubtree) {
            graph.setLocation(item.control, parentX + this.xOffset, currentY);
            currentY += graph.getHeight(item.control) + this.spacing;

            const inEdge = item.control.firstInEdge;
            if (inEdge) {
                const path = new YList();
                path.add(new YPoint(parentX + this.xOffset * 0.2, parentBottomY));
                path.add(new YPoint(parentX + this.xOffset * 0.2, graph.getCenterY(item.control)));
                path.add(new YPoint(parentX + this.xOffset, graph.getCenterY(item.control)));
                graph.setPath(inEdge, path);
            }
            if (item.children) {
                currentY = this.placeControls(graph, item.control, item.children);
            }
        }
        return currentY;
    }
}
