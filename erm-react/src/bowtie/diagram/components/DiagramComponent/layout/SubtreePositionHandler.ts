import { BaseClass, GraphComponent, IEdge, IGraph, IInputModeContext, INode, IPoint, IPositionHandler, List, Point, Rect } from 'yfiles';

import { BowtieDiagramUtils } from '../utils';
import { ElementIdProvider } from 'common/diagram/utils/ElementIdProvider';
import { DiagramPredicate, EdgeType, NodeType } from 'bowtie/types';
import { NonEmptyArray } from 'common/types';
import DiagramLayout from './DiagramLayout';
import DiagramPredicates from '../utils/DiagramPredicates';

/**
 * A position handler that moves a node and its subtree.
 *
 * While moving the node, the nearest subtree parent candidate is determined.
 * If the parent candidate of the subtree root changes, the edge to the old
 * parent is removed and an edge is inserted from the new parent candidate
 * to the moved node.
 */
export default class SubtreePositionHandler extends BaseClass<IPositionHandler>(IPositionHandler) {
    private movedNode: INode;
    private parentEdge: IEdge;
    private lastLocation: Point;
    private rootNodeCenter: Point;
    private handler: IPositionHandler;
    private layout: DiagramLayout;
    private elementIdProvider: ElementIdProvider;

    private originalParent: INode | null;
    private oldParent: INode | null;
    private oldEdge: IEdge | null;
    private globalRoot: INode;
    private subtreeNodes: List<INode>;
    private subtreeEdges: List<IEdge>;
    private readonly onDragFinishedCallback: () => void;

    /**
     * Creates the SubtreePositionHandler.
     * @param {IPositionHandler} handler The given position handler.
     * @param {DiagramLayout} layout Layout handler for bowtie.
     * @param {ElementIdProvider} elementIdProvider for edges id.
     * @param onDragFinishedCallback function called when drag is finished.
     */
    constructor(handler: IPositionHandler, layout: DiagramLayout, elementIdProvider: ElementIdProvider, onDragFinishedCallback: () => void) {
        super();
        this.handler = handler;
        this.layout = layout;
        this.elementIdProvider = elementIdProvider;
        this.lastLocation = new Point(0, 0);
        this.rootNodeCenter = new Point(0, 0);
        this.onDragFinishedCallback = onDragFinishedCallback;
    }

    /**
     * Returns the maximum allowed distance for a parent candidate
     * @return {number}
     */
    static get MAX_DISTANCE(): number {
        return 150;
    }

    /**
     * Returns a view of the location of the item.
     * @return {IPoint} actual location.
     */
    get location(): IPoint {
        return this.handler.location;
    }

    /**
     * Called when the drag starts.
     * @param {IInputModeContext} inputModeContext The context to retrieve information about the drag from.
     */
    initializeDrag(inputModeContext: IInputModeContext): void {
        this.handler.initializeDrag(inputModeContext);
        if (!SubtreePositionHandler.canHandle(inputModeContext)) {
            return;
        }
        const graph = SubtreePositionHandler.getGraph(inputModeContext, DiagramPredicates.WITHOUT_SECONDARY_EDGES);
        this.lastLocation = new Point(this.location.x, this.location.y);

        // get the selected node
        this.movedNode = (inputModeContext?.canvasComponent as GraphComponent)?.selection.selectedNodes.first();
        // get the mindmap root node
        this.globalRoot = BowtieDiagramUtils.getRoot(graph);
        this.rootNodeCenter = this.globalRoot.layout.center;

        this.subtreeNodes = new List();
        this.subtreeEdges = new List();

        // get subtree nodes
        BowtieDiagramUtils.getSubtree(graph, this.movedNode, this.subtreeNodes, this.subtreeEdges);

        // move all dragged nodes toFront.
        this.subtreeNodes.forEach((node) => {
            const itemCo = (inputModeContext?.canvasComponent as GraphComponent)?.graphModelManager.getMainCanvasObject(node);
            if (itemCo) {
                itemCo.toFront();
            }
        });
        // check if its left or right node
        const isLeft = BowtieDiagramUtils.isLeft(this.movedNode, graph);

        // get incoming edge of moved node
        let edge;
        if (isLeft) {
            edge = BowtieDiagramUtils.getOutEdge(this.movedNode, graph);
            if (edge) {
                this.oldEdge = edge;
                this.parentEdge = edge;
                this.originalParent = edge.targetNode;
                this.oldParent = this.originalParent;
            }
        } else {
            edge = BowtieDiagramUtils.getInEdge(this.movedNode, graph);
            if (edge) {
                this.oldEdge = edge;
                this.parentEdge = edge;
                this.originalParent = edge.sourceNode;
                this.oldParent = this.originalParent;
            }
        }
    }

    /**
     * Handles the move during the dragging.
     * @param {IInputModeContext} inputModeContext The context in which the interactive drag is started.
     * @param {Point} originalLocation The location at the time of initializeDrag.
     * @param {Point} newLocation The new location.
     * @return {boolean}
     */
    handleMove(inputModeContext: IInputModeContext, originalLocation: Point, newLocation: Point): boolean {
        if (!SubtreePositionHandler.canHandle(inputModeContext) || newLocation.equals(this.lastLocation)) {
            return false;
        }
        const delta = newLocation.subtract(this.lastLocation);
        this.lastLocation = newLocation;

        // use unfiltered graph for all subsequent operations
        const graph = SubtreePositionHandler.getGraph(inputModeContext, DiagramPredicates.WITHOUT_SECONDARY_EDGES);

        // check if location is left of root node center
        const isLeft = newLocation.x + this.movedNode.layout.width * 0.5 < this.rootNodeCenter.x;

        this.moveSubtree(delta, graph);
        const newParent = this.computeClosestNode(isLeft, graph);

        // parent node has changed
        if (newParent !== this.oldParent) {
            // if oldParent is defined and still has some edge with the moved node, it will be disconnected
            this.oldParent && this.disconnectNodes(this.oldParent, this.movedNode, graph);

            if (this.movedNode?.tag?.type === NodeType.Control) {
                this.oldEdge = this.updateInEdge(newParent, graph);
            } else if (isLeft) {
                this.oldEdge = this.updateOutEdge(newParent, graph);
            } else {
                this.oldEdge = this.updateInEdge(newParent, graph);
            }
            this.oldParent = newParent;
        }
        return true;
    }

    /**
     * Cancels the dragging.
     * @param {IInputModeContext} inputModeContext The context in which the interactive drag is started.
     * @param {Point} originalLocation The location at the time of initializeDrag.
     */
    cancelDrag(inputModeContext: IInputModeContext, originalLocation: Point): void {
        this.handler.cancelDrag(inputModeContext, originalLocation);
        if (!SubtreePositionHandler.canHandle(inputModeContext)) {
            return;
        }
        this.lastLocation = originalLocation;

        // use unfiltered graph for subsequent operations
        const graph = SubtreePositionHandler.getGraph(inputModeContext, DiagramPredicates.WITHOUT_SECONDARY_EDGES);

        const isLeft = this.location.x + this.movedNode.layout.width * 0.5 < this.rootNodeCenter.x;
        if (this.oldEdge) {
            graph.remove(this.oldEdge);
        }
        if (this.movedNode?.tag?.type === NodeType.Control) {
            this.updateInEdge(this.originalParent, graph);
        } else if (isLeft) {
            this.updateOutEdge(this.originalParent, graph);
        } else {
            this.updateInEdge(this.originalParent, graph);
        }

        // re-layout the tree
        (inputModeContext.canvasComponent as GraphComponent)?.selection.clear();
        void this.layout.layout(inputModeContext.canvasComponent as GraphComponent);

        this.originalParent = null;
    }

    /**
     * Called when the drag has finished.
     * @param {IInputModeContext} inputModeContext The context in which the interactive drag is started.
     * @param {Point} originalLocation The location at the time of initializeDrag.
     * @param {Point} newLocation The new location.
     */
    dragFinished(inputModeContext: IInputModeContext, originalLocation: Point, newLocation: Point): void {
        this.handler.dragFinished(inputModeContext, originalLocation, newLocation);
        if (!SubtreePositionHandler.canHandle(inputModeContext)) {
            return;
        }

        // some custom cases for changing originType when connecting blank node.
        const isLeft = this.location.x + this.movedNode.layout.width * 0.5 < this.rootNodeCenter.x;
        if (this.movedNode.tag?.type === NodeType.Control) {
            this.movedNode.tag = {
                ...this.movedNode.tag,
                originType: NodeType.Control,
            };
        } else if (this.movedNode.tag?.type === NodeType.RiskEvent || this.movedNode.tag?.originType === NodeType.Blank) {
            this.movedNode.tag = {
                ...this.movedNode.tag,
                originType: isLeft ? NodeType.Cause : NodeType.Impact,
            };
        }
        // oldEdge is last created edge, make sure it has correct tag with ID.
        if (this.oldEdge && this.oldEdge !== this.parentEdge) {
            this.oldEdge.tag = {
                id: this.elementIdProvider.getNextEdgeId(),
                type: EdgeType.DEFAULT,
            };
            this.onDragFinishedCallback();
        } else if (!this.oldEdge) {
            const graph = SubtreePositionHandler.getGraph(inputModeContext, DiagramPredicates.FULL_GRAPH);
            const filteredGraph = SubtreePositionHandler.getGraph(inputModeContext, DiagramPredicates.WITHOUT_SECONDARY_EDGES);

            const edit = graph.beginEdit('disconnecting node', 'disconnecting node');
            // we pretend any disconnected is blank node
            this.movedNode.tag = {
                ...this.movedNode.tag,
                originType: NodeType.Blank,
            };
            // when node is disconnected from diagram disconnect all edges as well and update it's originType
            this.subtreeNodes.forEach((node) => {
                graph
                    .edgesAt(node)
                    .toArray()
                    .forEach((edge) => graph.remove(edge));
                node.tag = {
                    ...node.tag,
                    originType: NodeType.Blank,
                };
            });
            graph.edges.toArray().forEach((edge) => {
                // We need to check against filtered graph without secondary edges as isLeft could end up in cyclic loop if we search against full graph.
                if (edge.sourceNode && edge.targetNode && !BowtieDiagramUtils.isConnectionAllowed(edge.sourceNode, edge.targetNode, filteredGraph)) {
                    graph.remove(edge);
                }
            });

            edit.commit();
            this.onDragFinishedCallback();
        }
    }

    /**
     * If there is any edge between the defined nodes it will be removed.
     * @param {INode} node1 Node to be checked.
     * @param {INode} node2 Node to be checked.
     * @param {IGraph} graph Selected graph.
     */
    disconnectNodes(node1: INode, node2: INode, graph: IGraph): void {
        const edge1 = graph.getEdge(node1, node2);
        const edge2 = graph.getEdge(node2, node1);

        edge1 && graph.remove(edge1);
        edge2 && graph.remove(edge2);
    }

    /**
     * Moves the subtree by a given delta.
     * @param {Point} delta The distance to be moved.
     * @param {IGraph} graph The input graph.
     */
    moveSubtree(delta: Point, graph: IGraph): void {
        // move all subtree nodes
        this.subtreeNodes.forEach((n) => {
            graph.setNodeLayout(n, new Rect(n.layout.x + delta.x, n.layout.y + delta.y, n.layout.width, n.layout.height));
        });
        // move all bends of subtree edges
        this.subtreeEdges.forEach((e) => {
            e.bends.forEach((bend) => {
                graph.setBendLocation(bend, new Point(bend.location.x + delta.x, bend.location.y + delta.y));
            });
        });
    }

    /**
     * Removes the old incoming edge and create a new edge from the new parent to the moved node.
     * @param {INode} newParent The new parent of the node.
     * @param {IGraph} graph The input graph.
     * @return {IEdge} The edge created.
     */
    updateInEdge(newParent: INode | null, graph: IGraph): IEdge | null {
        if (graph.inDegree(this.movedNode) > 0) {
            // remove old edge
            const removedEdge = BowtieDiagramUtils.getInEdge(this.movedNode, graph);
            if (removedEdge) {
                graph.remove(removedEdge);
            }
        }

        if (newParent) {
            // create edge between subtree and new parent
            const edge = graph.createEdge(newParent, this.movedNode, null, { type: EdgeType.DEFAULT });
            const newList = new List<IEdge>();
            newList.add(edge);
            this.layout.adjustPortLocations(graph, newList);
            return edge;
        }
        return null;
    }

    /**
     * Removes the old outgoing edge and create a new edge from the new parent to the moved node.
     * @param {INode} newParent The new parent of the node.
     * @param {IGraph} graph The input graph.
     * @return {IEdge} The edge created.
     */
    updateOutEdge(newParent: INode | null, graph: IGraph): IEdge | null {
        if (graph.outDegree(this.movedNode) > 0) {
            // remove old edge
            const removedEdge = BowtieDiagramUtils.getOutEdge(this.movedNode, graph);
            if (removedEdge) {
                graph.remove(removedEdge);
            }
        }

        if (newParent) {
            // create edge between subtree and new parent
            const edge = graph.createEdge(this.movedNode, newParent, null, { type: EdgeType.DEFAULT });
            const newList = new List<IEdge>();
            newList.add(edge);
            this.layout.adjustPortLocations(graph, newList);
            return edge;
        }
        return null;
    }

    /**
     * Computes the nearest parent candidate for the moved node.
     * Returns null if the distance to the found candidate exceeds
     * the {@link SubtreePositionHandler#MAX_DISTANCE limit}.
     * @param {boolean} isLeft True if the node is on the left of the subtree, false otherwise.
     * @param {IGraph} graph The input graph.
     * @return {INode} The parent candidate, or null.
     */
    computeClosestNode(isLeft: boolean, graph: IGraph): INode | null {
        let p: Point;
        let dMin = Number.POSITIVE_INFINITY;
        let newParent: INode | null = null;
        graph.nodes.forEach((n) => {
            if (!this.subtreeNodes.includes(n)) {
                let q: Point;
                let d = Number.POSITIVE_INFINITY;

                if (this.movedNode?.tag?.type === NodeType.Control) {
                    q = this.movedNode.layout.topLeft;
                    p = n.layout.bottomLeft;
                    d = p.distanceTo(q);
                } else if (isLeft && (BowtieDiagramUtils.isLeft(n, graph) || BowtieDiagramUtils.isRoot(n))) {
                    q = this.movedNode.layout.bottomRight;
                    p = n.layout.bottomLeft;
                    d = p.distanceTo(q);
                } else if (!BowtieDiagramUtils.isLeft(n, graph) || BowtieDiagramUtils.isRoot(n)) {
                    q = this.movedNode.layout.bottomLeft;
                    p = n.layout.bottomRight;
                    d = p.distanceTo(q);
                }

                if (d < dMin && BowtieDiagramUtils.isConnectedToRoot(n, graph) && BowtieDiagramUtils.canDragTo(this.movedNode, n, isLeft, graph)) {
                    dMin = d;
                    newParent = n;
                }
            }
        });
        return dMin < SubtreePositionHandler.MAX_DISTANCE ? newParent : null;
    }

    /**
     * Returns a list of the bend locations of the given edge.
     * @param {IEdge} edge The given edge.
     * @return {List.<Point>}
     */
    getBendLocations(edge: IEdge): List<Point> {
        const points = new List<Point>();
        edge.bends.forEach((bend) => {
            points.add(bend.location.toPoint());
        });
        return points;
    }

    /**
     * Gets the full graph from the context.
     * @param {IInputModeContext} inputModeContext The given context.
     * @param {DiagramPredicates} predicates to filter graph according
     * @return {IGraph}
     */
    static getGraph(inputModeContext: IInputModeContext, ...predicates: NonEmptyArray<DiagramPredicate>): IGraph {
        let graph: IGraph;
        if (!(inputModeContext.canvasComponent instanceof GraphComponent)) {
            throw new Error('canvasComponent is NOT instance of GraphComponent');
        }
        graph = inputModeContext.canvasComponent.graph;
        graph = BowtieDiagramUtils.getGraph(graph, ...predicates);

        return graph;
    }

    static canHandle(inputModeContext: IInputModeContext): boolean {
        return inputModeContext.canvasComponent instanceof GraphComponent;
    }
}
