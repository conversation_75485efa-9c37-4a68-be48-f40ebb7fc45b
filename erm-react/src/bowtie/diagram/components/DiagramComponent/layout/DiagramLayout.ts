import {
    BaseClass,
    CompositeLayoutData,
    DefaultTreeLayoutPortAssignment,
    DelegatingNodePlacer,
    FixNodeLayoutData,
    FixNodeLayoutStage,
    FreeNodePortLocationModel,
    GraphComponent,
    GraphEditorInputMode,
    IComparer,
    IEdge,
    IEnumerable,
    IGraph,
    INode,
    LayeredNodePlacer,
    LayeredRoutingStyle,
    LayoutExecutor,
    PlaceNodesAtBarycenterStage,
    PortConstraint,
    PortConstraintKeys,
    PortSide,
    RotatableNodePlacerMatrix,
    SubgraphLayout,
    SubgraphLayoutData,
    TreeLayout,
    TreeLayoutData,
    TreeLayoutPortAssignmentMode,
    TreeReductionStage,
} from 'yfiles';
import ControlLayoutStage from './ControlLayoutStage';
import { BowtieDiagramUtils } from '../utils';
import { EdgeType, Node, NodeType } from 'bowtie/types';
import { strings } from 'common/utils/i18n';
import DiagramPredicates from '../utils/DiagramPredicates';
import { DEFAULT_HORIZONTAL_LAYOUT_SPACING } from '../../../constants';
import { SnackbarContext } from 'common/hooks/useSnackbar';

/**
 * This class contains methods that deal with the bowtie graph layout.
 */
export default class DiagramLayout {
    private readonly gtl: TreeLayout;
    private readonly subgraphLayout: SubgraphLayout;
    private readonly placerLeft: LayeredNodePlacer;
    private readonly placerRight: LayeredNodePlacer;
    private readonly placerRoot: DelegatingNodePlacer;
    private readonly snackbarProviderContext: SnackbarContext;
    private $inLayout: boolean;

    /**
     * Constructs the Layout.
     */
    constructor(snackbarProviderContext: SnackbarContext) {
        this.snackbarProviderContext = snackbarProviderContext;
        // initialize the layout
        this.gtl = new TreeLayout({
            // use port constraints
            defaultPortAssignment: new DefaultTreeLayoutPortAssignment(TreeLayoutPortAssignmentMode.PORT_CONSTRAINT),
        });
        this.subgraphLayout = new SubgraphLayout(this.gtl);
        this.gtl.prependStage(new PlaceNodesAtBarycenterStage());

        // create the node placers for causes
        this.placerLeft = new LayeredNodePlacer(RotatableNodePlacerMatrix.ROT270, RotatableNodePlacerMatrix.ROT270);
        this.placerLeft.routingStyle = LayeredRoutingStyle.STRAIGHT;
        this.placerLeft.verticalAlignment = 0;
        this.placerLeft.spacing = 10;
        this.placerLeft.layerSpacing = DEFAULT_HORIZONTAL_LAYOUT_SPACING;

        this.placerRight = new LayeredNodePlacer(RotatableNodePlacerMatrix.ROT90, RotatableNodePlacerMatrix.ROT90);
        this.placerRight.routingStyle = LayeredRoutingStyle.STRAIGHT;
        this.placerRight.verticalAlignment = 0;
        this.placerRight.spacing = 10;
        this.placerRight.layerSpacing = DEFAULT_HORIZONTAL_LAYOUT_SPACING;

        const rootPlacerRight = new LayeredNodePlacer(RotatableNodePlacerMatrix.ROT90, RotatableNodePlacerMatrix.ROT90);
        rootPlacerRight.routingStyle = LayeredRoutingStyle.STRAIGHT;
        rootPlacerRight.verticalAlignment = 0;
        rootPlacerRight.spacing = 10;
        rootPlacerRight.layerSpacing = DEFAULT_HORIZONTAL_LAYOUT_SPACING;

        this.placerRoot = new DelegatingNodePlacer(RotatableNodePlacerMatrix.DEFAULT, this.placerLeft, rootPlacerRight);

        this.$inLayout = false;
    }

    /**
     * Gets a flag that tells whether a layout animation is currently in progress.
     * @return {boolean}
     */
    get inLayout(): boolean {
        return this.$inLayout;
    }

    /**
     * Sets a flag that tells whether a layout animation is currently in progress.
     * @param {boolean} value The flag to be set.
     */
    set inLayout(value: boolean) {
        this.$inLayout = value;
    }

    /**
     * Adds the mappers to the graph that are needed for the layout.
     * The mappers provide the layout algorithm with additional information that is needed
     * to achieve the desired layout.
     * @param {IGraph} graph The input graph.
     */
    addMappers(graph: IGraph): void {
        // tells the DelegatingNodePlacer which side a node is on
        graph.mapperRegistry.createDelegateMapper(DelegatingNodePlacer.PRIMARY_NODES_DP_KEY, (node) => BowtieDiagramUtils.isLeft(node, graph));
        // tells the layout which node placer to use for a node
        graph.mapperRegistry.createDelegateMapper(TreeLayout.NODE_PLACER_DP_KEY, (node) => {
            if (BowtieDiagramUtils.isRoot(node)) {
                return this.placerRoot;
            }
            if (BowtieDiagramUtils.isLeft(node, graph)) {
                return this.placerLeft;
            }
            return this.placerRight;
        });
        // tells the layout which comparer to use for a node to sort the children
        graph.mapperRegistry.createDelegateMapper(TreeLayout.OUT_EDGE_COMPARER_DP_KEY, (node) => {
            if (BowtieDiagramUtils.isRoot(node)) {
                return new YCoordComparator();
            }
            if (BowtieDiagramUtils.isLeft(node, graph)) {
                return this.placerLeft.createComparer();
            }
            return this.placerRight.createComparer();
        });

        // tells the layout which side to place a source port on
        graph.mapperRegistry.createDelegateMapper(PortConstraintKeys.SOURCE_PORT_CONSTRAINT_DP_KEY, (edge) =>
            PortConstraint.create(BowtieDiagramUtils.isLeft(edge?.targetNode, graph) ? PortSide.WEST : PortSide.EAST, true),
        );
        // tells the layout which side to place a target port on
        graph.mapperRegistry.createDelegateMapper(PortConstraintKeys.TARGET_PORT_CONSTRAINT_DP_KEY, (edge) =>
            PortConstraint.create(BowtieDiagramUtils.isLeft(edge?.targetNode, graph) ? PortSide.EAST : PortSide.WEST, true),
        );

        // a layout stage that keeps a certain node in place during layout
        const fixNodeStage = new FixNodeLayoutStage();
        this.gtl.prependStage(fixNodeStage);
        this.gtl.prependStage(new TreeReductionStage());
        this.gtl.prependStage(new ControlLayoutStage(this.placerRoot));
    }

    /**
     * Moves the source and target ports of the given edges to the top-left or
     * top-right corner of the node.
     * @param {IGraph} graph The input graph.
     * @param {IEnumerable} edges The list of edges for which the ports should be adjusted.
     */
    adjustPortLocations(graph: IGraph, edges: IEnumerable<IEdge | null>): void {
        edges.forEach((edge) => {
            const s = edge?.sourceNode as Node;
            const t = edge?.targetNode as Node;

            // we let control determine edge placement later on in ControlLayoutStage.ts
            if (t?.tag?.type === NodeType.Control) {
                return;
            }

            if (!edge?.tag?.type || edge?.tag?.type === EdgeType.DEFAULT) {
                if (edge?.sourcePort) {
                    graph.setPortLocationParameter(edge.sourcePort, FreeNodePortLocationModel.NODE_RIGHT_ANCHORED);
                }

                if (edge?.targetPort) {
                    graph.setPortLocationParameter(edge.targetPort, FreeNodePortLocationModel.NODE_LEFT_ANCHORED);
                }
            } else if (edge?.tag?.type && edge?.tag?.type === EdgeType.SECONDARY) {
                const sourcePorts = BowtieDiagramUtils.generatePorts(s as INode);
                const pointsT = BowtieDiagramUtils.generatePorts(t as INode);

                const portPlacement = BowtieDiagramUtils.computeClosestPort(sourcePorts, pointsT);

                if (edge?.sourcePort && portPlacement) {
                    graph.setPortLocationParameter(edge.sourcePort, portPlacement.sourcePortLocation);
                }

                if (edge?.targetPort && portPlacement) {
                    graph.setPortLocationParameter(edge.targetPort, portPlacement.targetPortLocation);
                }
            }
        });
    }

    /**
     * Calculates an animated layout on the graph.
     * @param {GraphComponent} graphComponent The given graphComponent.
     * @return {Promise}
     */
    async layout(graphComponent: GraphComponent): Promise<void> {
        // check a layout is currently in progress
        if (this.inLayout) {
            return Promise.resolve();
        }

        this.inLayout = true;
        // disable the input mode so that no user interactions are allowed during layout
        const inputMode = graphComponent.inputMode as GraphEditorInputMode;
        inputMode.enabled = false;

        const layoutData = new CompositeLayoutData(
            new FixNodeLayoutData({ fixedNodes: BowtieDiagramUtils.isRoot }),
            new TreeLayoutData({ treeRoot: BowtieDiagramUtils.isRoot }),
            new SubgraphLayoutData({
                subgraphEdges: DiagramPredicates.WITHOUT_SECONDARY_EDGES.edgePredicate,
            }),
        );

        // execute an animated layout
        const newLayoutExecutor = new LayoutExecutor({
            graphComponent,
            layout: this.subgraphLayout,
            layoutData,
            duration: '0.2s',
        });
        try {
            await newLayoutExecutor.start();

            const graph = BowtieDiagramUtils.getGraph(graphComponent.graph, DiagramPredicates.FULL_GRAPH);
            this.adjustPortLocations(graph, graph.edges);
        } catch (error) {
            if (error.name === 'AlgorithmAbortedError') {
                this.snackbarProviderContext.enqueueError(strings('bowtie:diagram.message.layoutComputationCancelled'));
            } else {
                throw error;
            }
        } finally {
            this.inLayout = false;
            inputMode.enabled = true;
        }
    }

    /**
     * Synchronously calculates an layout on the graph.
     * This function should be used only when it's not possible to use async layout function.
     * @param {IGraph} graph Graph instance that we apply layout to.
     */
    applyLayout(graph: IGraph): void {
        // check a layout is currently in progress
        if (this.inLayout) {
            return;
        }

        const layoutData = new CompositeLayoutData(
            new FixNodeLayoutData({ fixedNodes: BowtieDiagramUtils.isRoot }),
            new TreeLayoutData({ treeRoot: BowtieDiagramUtils.isRoot }),
            new SubgraphLayoutData({
                subgraphEdges: DiagramPredicates.WITHOUT_SECONDARY_EDGES.edgePredicate,
            }),
        );

        graph.applyLayout(this.subgraphLayout, layoutData);

        const filteredGraph = BowtieDiagramUtils.getGraph(graph, DiagramPredicates.FULL_GRAPH);
        this.adjustPortLocations(filteredGraph, filteredGraph.edges);
    }
}

/**
 * Sorts edges depending on the y-coordinates of their target nodes.
 * By default, edges are sorted from top to bottom. However, in case sides are taken into consideration, edges with
 * target nodes to the right are sorted bottom up and edges with target nodes to the left are sorted top down.
 * This is important when this comparator is used to determine the order of children for layout.
 */
class YCoordComparator extends BaseClass(IComparer) {
    /** @return {number} */
    compare(x, y) {
        const edge1 = x;
        const edge2 = y;
        const y1 = YCoordComparator.getY(edge1);
        const y2 = YCoordComparator.getY(edge2);

        if (YCoordComparator.isLeft(edge1.target)) {
            return y1 - y2;
        }
        return y2 - y1;
    }

    /**
     * @return {boolean}
     */
    static isLeft(n) {
        return n.graph.getDataProvider(DelegatingNodePlacer.PRIMARY_NODES_DP_KEY).getBoolean(n);
    }

    /**
     * @return {number}
     */
    static getY(e) {
        return e.graph.getY(e.target);
    }
}
