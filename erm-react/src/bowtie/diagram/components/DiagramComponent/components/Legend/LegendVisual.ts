import { BaseClass, IRenderContext, IVisualCreator, Point, SvgVisual, SvgVisualGroup, Visual } from 'yfiles';
import React from 'react';
import ReactDOM from 'react-dom';
import Legend from './Legend';
import { NodeStyle, NodeType } from 'bowtie/types';

function dispose(context: IRenderContext, removedVisual: Visual): Visual | null {
    const gElement = (removedVisual as SvgVisual).svgElement as SVGGElement;
    ReactDOM.unmountComponentAtNode(gElement);
    return null;
}

/*
 TODO: In future, we should find a way to reuse React component for export (image rendering).
 Currently, we need this visual and legend in form of SVG for Export/Print purposes.
*/
export class LegendVisual extends BaseClass(IVisualCreator) {
    private nodeStyles: Map<NodeType, NodeStyle>;
    private position?: Point;

    constructor(nodeStyles: Map<NodeType, NodeStyle>, position?: Point) {
        super();
        this.nodeStyles = nodeStyles;
        this.position = position;
    }

    createVisual(context: IRenderContext): Visual | null {
        const viewGroup = new SvgVisualGroup();
        const gElement = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        const element = React.createElement(Legend, { nodeStyles: this.nodeStyles });
        ReactDOM.render(element, gElement);
        SvgVisual.setTranslate(gElement, this.position?.x || 0, this.position?.y || 0);
        const svgVisual = new SvgVisual(gElement);
        viewGroup.add(svgVisual);
        context.setDisposeCallback(svgVisual, dispose);
        return viewGroup;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    updateVisual(context: IRenderContext, oldVisual: Visual | null): Visual | null {
        return this.createVisual(context);
    }
}
