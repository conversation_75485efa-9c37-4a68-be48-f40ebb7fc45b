import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';
import { NodeStyle, NodeType } from 'bowtie/types';
import React from 'react';
import { NODE_LEGEND_LABELS } from '../constants';
import { getStyleForType } from '../utils';
import useTheme from '@mui/system/useTheme';
import { strings } from 'common/utils/i18n';
import { Font, Size, TextRenderSupport } from 'yfiles';

type LegendProps = {
    nodeStyles: Map<NodeType, NodeStyle>;
};

export const LEGEND_WIDTH = 436;
const LEGEND_HEIGHT = 32;
const LEGEND_ITEM_RECT_HEIGHT = 32;
const LEGEND_ITEM_RECT_WIDTH = 16;
const LEGEND_ITEM_LEFT_PADDING = 12;

const FONT = new Font({ fontSize: 12, fontFamily: 'Open Sans, Arial' });

const calculateTextSize = (text?: string | null): Size => {
    if (!text) {
        return new Size(0, 0);
    }

    return TextRenderSupport.measureText(text, FONT);
};

const Legend: React.FC<LegendProps> = (props: LegendProps) => {
    const theme = useTheme();

    const legendNodes = [NodeType.Cause, NodeType.RiskEvent, NodeType.Impact, NodeType.Control];
    let leftOffset = 0;

    return (
        <g>
            <rect
                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-legend}`}
                fill={'white'}
                stroke={theme.palette.protechtGrey?.grey_245}
                strokeWidth={2}
                x={0}
                y={0}
                width={LEGEND_WIDTH}
                height={LEGEND_HEIGHT}
                ry={2}
            />
            <text
                data-testid={`${DIAGRAM_TEST_ID_PREFIX}-legend-title`}
                x={12}
                y={20}
                fontFamily={FONT.fontFamily}
                fontWeight={600}
                fontSize={FONT.fontSize}
            >
                {strings('bowtie:diagram.title.legend')}
            </text>
            {(leftOffset += calculateTextSize(strings('bowtie:diagram.title.legend')).width + 2 * LEGEND_ITEM_LEFT_PADDING)}
            {legendNodes.map((nodeType, index) => {
                const itemWidth = calculateTextSize(NODE_LEGEND_LABELS[nodeType]).width + 38;
                return (
                    <g
                        key={index}
                        transform={`translate(${leftOffset} ,${8})`}
                    >
                        <rect
                            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-legend-item}`}
                            fill={getStyleForType(props.nodeStyles, nodeType).fillColor1}
                            x={0}
                            y={0}
                            width={LEGEND_ITEM_RECT_HEIGHT}
                            height={LEGEND_ITEM_RECT_WIDTH}
                            rx={0}
                        />
                        <text
                            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-default-text`}
                            x={38}
                            y={12}
                            fontFamily={FONT.fontFamily}
                            fontSize={FONT.fontSize}
                        >
                            {NODE_LEGEND_LABELS[nodeType]}
                        </text>
                        {(leftOffset += itemWidth + LEGEND_ITEM_LEFT_PADDING)}
                    </g>
                );
            })}
        </g>
    );
};

export default Legend;
