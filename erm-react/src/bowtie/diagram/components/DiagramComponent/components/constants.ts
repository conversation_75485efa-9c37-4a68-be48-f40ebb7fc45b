import { NodeType } from '../../../../types';

export const DEFAULT_NODE_STYLE = {
    fillColor1: '#FFFFFF',
    fillColor2: '#FFFFFF',
    strokeColor: '#8C8C8C',
    iconColor: '#595959',
    iconWeight: 900,
    iconContent: '\uf656',
};

export const BLANK_NODE_DATA = {
    id: -1,
    name: '',
    type: NodeType.Blank,
    originType: NodeType.Blank,
};

export const NODE_WIDTH = 254;
export const MIN_NODE_HEIGHT = 40;
export const MAX_NODE_HEIGHT = 81;

export const NODE_LEGEND_LABELS = {
    [NodeType.Cause]: 'Causes',
    [NodeType.Control]: 'Controls',
    [NodeType.RiskEvent]: 'Events',
    [NodeType.Impact]: 'Impacts',
};
