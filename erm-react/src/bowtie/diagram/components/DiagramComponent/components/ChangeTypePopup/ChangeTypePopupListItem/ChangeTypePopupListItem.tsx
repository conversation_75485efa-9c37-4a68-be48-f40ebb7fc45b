import React from 'react';
import ListItemButton from '@mui/material/ListItemButton';
import { listItemButtonClasses } from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { styled } from '@mui/material/styles';
import CheckIcon from '@mui/icons-material/Check';
import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';

const StyledListItemButton = styled(ListItemButton)(() => ({
    minWidth: '35px',
    paddingTop: '1px',
    paddingBottom: '1px',
    [`&.${listItemButtonClasses.gutters}`]: {
        paddingLeft: '5px',
        paddingRight: '10px',
    },
}));

const StyledListItemIcon = styled(ListItemIcon)(() => ({
    minWidth: '35px',
}));

type BaseProps = {
    checked: boolean;
    text: string;
    click: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
};

const ChangeTypePopupListItem: React.FC<BaseProps> = (props: BaseProps) => {
    return (
        <StyledListItemButton
            onClick={props.click}
            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-change-type-option-${props.text}`}
        >
            <StyledListItemIcon>{props.checked && <CheckIcon />}</StyledListItemIcon>
            <ListItemText primary={props.text} />
        </StyledListItemButton>
    );
};

export default ChangeTypePopupListItem;
