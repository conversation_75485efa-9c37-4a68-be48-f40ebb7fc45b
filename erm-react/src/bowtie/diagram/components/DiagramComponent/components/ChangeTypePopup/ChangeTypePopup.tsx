import React from 'react';
import { ExteriorLabelModelPosition, GraphComponent, INode } from 'yfiles';
import List from '@mui/material/List';
import { styled } from '@mui/material/styles';

import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';
import { Node, NodeType } from 'bowtie/types';
import ChangeTypePopupListItem from './ChangeTypePopupListItem/ChangeTypePopupListItem';
import { getChangeTypeOptions } from '../utils';
import { BowtieDiagramUtils } from '../../utils';
import DiagramPredicates from '../../utils/DiagramPredicates';
import NodeElement from 'common/diagram/components/DiagramElement/NodeElement';

const StyledList = styled(List)(() => ({
    display: 'block',
    alignItems: 'center',
    minWidth: '10px',
    userSelect: 'none',
    backgroundColor: 'white',
    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
    borderRadius: '2px',
}));

type NodeContextMenuProps = {
    graphComponent: GraphComponent;
    selected: INode | null;
    open: boolean;
    click: (node: Node, type: NodeType) => void;
    showControl: boolean;
};

const ChangeTypePopup = (props: NodeContextMenuProps): React.ReactElement => {
    let items: React.ReactElement[] | null = null;

    const handleListItemClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>, type: NodeType) => {
        props.click(props.selected!, type);
    };

    if (props.selected) {
        items = getChangeTypeOptions(
            props.selected,
            BowtieDiagramUtils.isEndNode(props.selected, BowtieDiagramUtils.getGraph(props.graphComponent.graph, DiagramPredicates.WITHOUT_SECONDARY_EDGES)),
        )
            .filter((item) => props.showControl || item !== NodeType.Control)
            .map((item) => (
                <ChangeTypePopupListItem
                    key={item}
                    click={(event) => handleListItemClick(event, item)}
                    checked={props.selected?.tag?.type === item}
                    text={item}
                />
            ));
    }

    return (
        <NodeElement
            graphComponent={props.graphComponent}
            selected={props.selected}
            position={ExteriorLabelModelPosition.EAST}
            style={{ zIndex: 11 }}
        >
            <div style={{ display: props.open ? 'block' : 'none' }}>
                <StyledList data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-change-type-popup`}>{items}</StyledList>
            </div>
        </NodeElement>
    );
};

export default ChangeTypePopup;
