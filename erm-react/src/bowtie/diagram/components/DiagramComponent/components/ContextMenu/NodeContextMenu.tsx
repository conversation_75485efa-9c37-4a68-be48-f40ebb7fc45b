import React, { useEffect, useState } from 'react';
import { ExteriorLabelModelPosition, GraphComponent, GraphCopier, INode, ItemCopiedEventArgs, List } from 'yfiles';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { Node, NodeType } from 'bowtie/types';

import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';
import { strings } from 'common/utils/i18n';
import { BowtieDiagramUtils } from '../../utils';
import DiagramPredicates from '../../utils/DiagramPredicates';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy, faPaste, faTrash } from '@fortawesome/pro-regular-svg-icons';
import NodeElement from 'common/diagram/components/DiagramElement/NodeElement';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';

type NodeContextMenuProps = {
    graphComponent: GraphComponent;
    selected: Node | null;
    onRemove: (node: Node | null) => void;
    // onRatingChanged: (node: Node, rating?: RatingModel) => void;
    onCopy: (node: Node | null) => void;
    onPaste: (node: Node | null) => void;
};

const StyledIcon = styled(IconButton)({
    padding: 0,
    borderRadius: '50%',
    background: 'transparent',
    '&:hover': {
        backgroundColor: 'transparent',
    },
    '&:focus': {
        backgroundColor: 'transparent',
    },
    '&:active': {
        backgroundColor: 'transparent',
    },

    '& svg': {
        verticalAlign: 'top',
        fontSize: '12px',
    },
    iconRoot: {
        width: '34px',
        height: '34px',
        padding: 0,
    },
});

const NodeMenu = styled('div')({
    display: 'block',
    alignItems: 'center',
    padding: '0 8px 0 8px',
    minWidth: '10px',
    userSelect: 'none',
    backgroundColor: '#262626',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.25)',
    borderRadius: '4px',
    height: '34px',
    '*': {
        userSelect: 'none',
        verticalAlign: 'middle',
        color: 'white',
    },
    '.rating': {
        display: 'inline-block',
        marginRight: '8px',
        padding: '8px',
        cursor: 'pointer',

        span: {
            fontFamily: 'Open Sans, sans-serif',
            fontSize: '14px',
            verticalAlign: 'baseline',
        },
    },
    '.icon-tray': {
        display: 'flex',
    },
});

/**
 * Contextual menu displayed on top of selected node.
 */
const NodeContextMenu = (props: NodeContextMenuProps): React.ReactElement => {
    // const [ratingsVisible, setRatingsVisible] = useState(false);
    const [lastCopiedNode, setLastCopiedNode] = useState<Node>();

    useEffect(() => {
        const nodeCopiedListener = (sender: GraphCopier, args: ItemCopiedEventArgs<Node>) => {
            if (args.original.tag?.type !== NodeType.MainRiskEvent) {
                setLastCopiedNode(args.original);
            }
        };

        const nodePastedListener = (sender: GraphCopier, args: ItemCopiedEventArgs<Node>) => {
            if (args.original.tag?.type !== NodeType.MainRiskEvent) {
                setLastCopiedNode(undefined);
            }
        };
        props.graphComponent?.clipboard.toClipboardCopier.addNodeCopiedListener(nodeCopiedListener);
        props.graphComponent?.clipboard.fromClipboardCopier.addNodeCopiedListener(nodePastedListener);

        return () => {
            props.graphComponent?.clipboard.toClipboardCopier.removeNodeCopiedListener(nodeCopiedListener);
            props.graphComponent?.clipboard.fromClipboardCopier.removeNodeCopiedListener(nodePastedListener);
        };
    }, [props.graphComponent]);

    const isDeleteVisible = props.selected?.tag?.type !== NodeType.MainRiskEvent;
    const isCopyVisible = props.selected?.tag?.type && props.selected.tag.type !== NodeType.MainRiskEvent && props.selected.tag.type !== NodeType.Blank;
    const isPasteVisible = (): boolean => {
        if (lastCopiedNode && props.selected && props.graphComponent?.clipboard && !props.graphComponent.clipboard.empty) {
            const childNodes = new List<INode>();
            const graph = BowtieDiagramUtils.getGraph(
                props.graphComponent.graph,
                DiagramPredicates.WITHOUT_HIDDEN_CONTROL,
                DiagramPredicates.WITHOUT_SECONDARY_EDGES,
            );
            BowtieDiagramUtils.getSubtree(graph, lastCopiedNode as INode, childNodes, new List());
            return BowtieDiagramUtils.canPasteTo(lastCopiedNode as INode, props.selected as INode, childNodes, graph);
        }
        return false;
    };

    // TODO: uncomment when ratings enabled
    // useEffect(() => {
    //     setRatingsVisible(false);
    // }, [props.selected?.tag.id, props.selected?.tag.type]);

    // const onRatingChanged = (node: Node, rating?: RatingModel) => {
    //     setRatingsVisible(false);
    //     props.onRatingChanged(node, rating);
    // };

    // TODO: remove when ratings or comments enabled
    if (!isCopyVisible && !isDeleteVisible && !isPasteVisible()) {
        return <div />;
    }

    return (
        <>
            <NodeElement
                graphComponent={props.graphComponent}
                selected={props.selected as INode}
                position={ExteriorLabelModelPosition.NORTH}
            >
                <NodeMenu data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-menu`}>
                    {/*
                    TODO: uncomment when ratings enabled
                    <div className={'rating'} onClick={() => setRatingsVisible(prevState => !prevState)} data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-menu-rating-button`}>
                        <span>{strings('bowtie:diagram.button.rating')}</span>
                    </div> */}
                    <div className={'icon-tray'}>
                        {isCopyVisible && (
                            <Tooltip
                                title={strings('bowtie:diagram.button.copyNode')}
                                aria-label={strings('bowtie:diagram.button.copyNode')}
                            >
                                <StyledIcon
                                    onClick={() => props.onCopy(props.selected)}
                                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-menu-copy-button`}
                                >
                                    <FontAwesomeIcon icon={faCopy} />
                                </StyledIcon>
                            </Tooltip>
                        )}
                        {isPasteVisible() && (
                            <Tooltip
                                title={strings('bowtie:diagram.button.pasteNode')}
                                aria-label={strings('bowtie:diagram.button.pasteNode')}
                            >
                                <StyledIcon
                                    onClick={() => props.onPaste(props.selected)}
                                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-menu-paste-button`}
                                >
                                    <FontAwesomeIcon icon={faPaste} />
                                </StyledIcon>
                            </Tooltip>
                        )}
                        {isDeleteVisible && (
                            <Tooltip
                                title={strings('bowtie:diagram.button.deleteNode')}
                                aria-label={strings('bowtie:diagram.button.deleteNode')}
                            >
                                <StyledIcon
                                    onClick={() => props.onRemove(props.selected)}
                                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-menu-delete-button`}
                                >
                                    <FontAwesomeIcon icon={faTrash} />
                                </StyledIcon>
                            </Tooltip>
                        )}
                    </div>
                </NodeMenu>
            </NodeElement>
            {/*
            TODO: uncomment when ratings enabled
            {ratingsVisible &&
            <NodeRatingsPopup graphComponent={props.graphComponent} selectedNode={props.selected} onRatingChanged={onRatingChanged}/>} */}
        </>
    );
};

export default NodeContextMenu;
