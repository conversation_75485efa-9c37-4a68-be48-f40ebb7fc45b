import React, { useState, useEffect } from 'react';
import { RatingLabel, Rating } from 'bowtie/types';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Label from 'common/components/Label';
import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck } from '@fortawesome/pro-solid-svg-icons';

type NodeRatingsListProps = {
    ratingLabel: RatingLabel | null;
    data?: Rating[];
    selectedRatingId?: number;
    onRatingChanged: (ratingId?: number) => void;
};

const StyledList = styled(List)(({ theme }) => ({
    backgroundColor: theme.palette.background.default,
    boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.15)',
    borderRadius: '2px',
    verticalAlign: 'middle',
}));

const StyledRatingLabel = styled(Typography)(() => ({
    padding: '0 5px 0 5px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));

const NodeRatingsList: React.FC<NodeRatingsListProps> = ({ ratingLabel, data, selectedRatingId, onRatingChanged }: NodeRatingsListProps) => {
    const [selected, setSelected] = useState<number | undefined>(selectedRatingId);

    useEffect(() => {
        setSelected(selectedRatingId);
    }, [selectedRatingId]);

    if (!data) {
        return <div />;
    }

    const selectRating = (rating: Rating) => {
        const newRating = rating.id !== selected ? rating.id : undefined;

        setSelected(newRating);
        onRatingChanged(newRating);
    };

    const renderSelectedIndicator = (item: Rating) => {
        if (selected !== item.id) {
            return <div />;
        }

        return (
            <ListItemIcon>
                <FontAwesomeIcon icon={faCheck} />
            </ListItemIcon>
        );
    };

    return (
        <StyledList
            dense
            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-rating-popup`}
        >
            {data.map((item: Rating) => {
                return (
                    <ListItemButton
                        key={item.id}
                        onClick={() => selectRating(item)}
                        data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-rating-option-${item.value}`}
                    >
                        <div style={{ width: '24px' }}>{renderSelectedIndicator(item)}</div>
                        <StyledRatingLabel variant="body1">{ratingLabel || ''}</StyledRatingLabel>
                        <Label
                            text={item.value}
                            textColor="primary.contrastText"
                            backgroundColor={item.color}
                        />
                    </ListItemButton>
                );
            })}
        </StyledList>
    );
};

export default NodeRatingsList;
