import React from 'react';
import { ExteriorLabelModelPosition, GraphComponent, INode } from 'yfiles';

import { Node, RatingLabel, RatingType } from 'bowtie/types';
import { getRatingsLabelForType, getRatingsTypeForType } from '../utils';
import NodeRatingsList from './NodeRatingsList';
import NodeElement from 'common/diagram/components/DiagramElement/NodeElement';

type Rating = {
    ratingType: RatingType;
    ratingLabel: RatingLabel;
    valueId: number;
};

type NodeRatingsPopupProps = {
    graphComponent: GraphComponent;
    selectedNode: Node | null;
    onRatingChanged: (selectedNode: Node, rating: Rating | undefined) => void;
};

const NodeRatingsPopup: React.FC<NodeRatingsPopupProps> = (props: NodeRatingsPopupProps) => {
    const ratingLabel = getRatingsLabelForType(props.selectedNode?.tag.type);
    const ratingType = getRatingsTypeForType(props.selectedNode?.tag.type);

    const handleRatingChanged = (ratingId?: number) => {
        const rating =
            (ratingId || ratingId === 0) && ratingLabel && ratingType
                ? {
                      ratingType,
                      ratingLabel,
                      valueId: ratingId,
                  }
                : undefined;

        if (props.selectedNode?.tag) {
            props.onRatingChanged(props.selectedNode, rating);
        }
    };

    return (
        <NodeElement
            graphComponent={props.graphComponent}
            selected={props.selectedNode as INode}
            position={ExteriorLabelModelPosition.SOUTH}
        >
            <NodeRatingsList
                ratingLabel={ratingLabel}
                data={props.selectedNode?.tag?.ratingOptions}
                selectedRatingId={props.selectedNode?.tag?.rating?.valueId}
                onRatingChanged={handleRatingChanged}
            />
        </NodeElement>
    );
};

export default NodeRatingsPopup;
