import { INode, IRenderContext, NodeStyleBase, SvgVisual, Visual } from 'yfiles';
import ReactDOM from 'react-dom';
import React, { ComponentClass, FunctionComponent } from 'react';
import { NodeData, NodeStyle, NodeType } from '../../../../../types';
import { DefaultBowtieNodeStyle, ReactNodeStyleProps } from '../types';
import { getStyleForType } from '../utils';

function dispose(context: IRenderContext, removedVisual: Visual): Visual | null {
    const gElement = (removedVisual as SvgVisual).svgElement as SVGGElement;
    ReactDOM.unmountComponentAtNode(gElement);
    return null;
}

type RenderType = ComponentClass<ReactNodeStyleProps> | FunctionComponent<ReactNodeStyleProps>;

/**
 * A simple INodeStyle implementation that uses React Components for rendering the node visualizations
 *
 */
export class ReactNodeStyle extends NodeStyleBase implements DefaultBowtieNodeStyle {
    private type: RenderType;
    private _nodeStyles: Map<NodeType, NodeStyle>;
    private typeChangeClick: () => void;
    private unlinkLibrary: (node: INode) => void;

    constructor(type: RenderType, styles: Map<NodeType, NodeStyle>, typeChangeClick: () => void, unlinkLibrary: (node: INode) => void) {
        super();
        this.type = type;
        this._nodeStyles = styles;
        this.typeChangeClick = typeChangeClick;
        this.unlinkLibrary = unlinkLibrary;
    }

    createProps(node: INode): ReactNodeStyleProps {
        return {
            width: node.layout.width,
            height: node.layout.height,
            tag: node.tag,
            style: getStyleForType(this._nodeStyles, (node.tag && (node.tag as NodeData).type) || NodeType.Blank),
            typeChangeClick: this.typeChangeClick,
            unlinkLibrary: () => this.unlinkLibrary(node),
        };
    }

    createVisual(context: IRenderContext, node: INode): Visual | null {
        const gElement = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        const props = this.createProps(node);
        const element = React.createElement(this.type, props);
        ReactDOM.render(element, gElement);
        SvgVisual.setTranslate(gElement, props.tag.loc?.x || node.layout.x, props.tag.loc?.y || node.layout.y);
        const svgVisual = new SvgVisual(gElement);
        (svgVisual as any)['data-state'] = props;

        context.setDisposeCallback(svgVisual, dispose);
        return svgVisual;
    }

    updateVisual(context: IRenderContext, oldVisual: Visual, node: INode): Visual | null {
        const gElement = (oldVisual as SvgVisual).svgElement as SVGGElement;

        const props = this.createProps(node);

        const lastProps = (oldVisual as any)['data-state'] as ReactNodeStyleProps;
        if (lastProps.width !== props.width || lastProps.height !== props.height || lastProps.tag !== props.tag || lastProps.style !== props.style) {
            const element = React.createElement(this.type, props);
            ReactDOM.render(element, gElement);
            (oldVisual as any)['data-state'] = props;
        }
        SvgVisual.setTranslate(gElement, node.layout.x, node.layout.y);
        return oldVisual;
    }

    get nodeStyles(): Map<NodeType, NodeStyle> {
        return this._nodeStyles;
    }
}
