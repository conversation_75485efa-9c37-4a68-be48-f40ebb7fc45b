import { DELETED_LINK, DIAGRAM_TEST_ID_PREFIX, LABEL_INSET } from 'bowtie/diagram/constants';
import { NodeType /* , Rating */ } from 'bowtie/types'; // TODO: uncomment when ratings enabled
import React, { useState, useEffect } from 'react';
import {
    Font /* , // TODO: uncomment when ratings enabled
    Size,
    TextRenderSupport */,
} from 'yfiles';

import { ReactNodeStyleProps } from '../types';
import { strings } from 'common/utils/i18n';

import 'yfiles/yfiles.css';
import { NODE_LEGEND_LABELS } from '../constants';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import * as iconsSolid from '@fortawesome/pro-solid-svg-icons';
import * as iconsRegular from '@fortawesome/pro-regular-svg-icons';
import { theme } from '@protecht/ui-library/library/theme/theme';

const FONT = new Font({ fontSize: 12, fontFamily: 'Open Sans, Arial' });

const NodeTemplate: React.FC<ReactNodeStyleProps> = (props: ReactNodeStyleProps) => {
    const [icon, setIcon] = useState(undefined);

    // TODO: WORMS-19416 - This is workaround. We use direct import of theme object in this particular case (yFiles component),
    // because useTheme returns system theme object instead of protecht extended theme (theme.palette.protechtGrey === undefined).
    // Therefore switch of dark/light theme is not working here. The problem with useTheme hook must be solved in the future.
    // const theme = useTheme();

    // TODO: uncomment when ratings enabled
    // const calculateTextSize = (text?: string | null): Size => {
    //     if (!text) {
    //         return new Size(0,0);
    //     }

    //     return TextRenderSupport.measureText(text, FONT);
    // };

    // TODO: uncomment when ratings enabled
    // const [rating, setRating] = useState<Rating | undefined>(props.tag.ratingOptions?.find(option => option.id === props.tag.rating?.valueId));
    // const [ratingLabelSize, setRatingLabelSize] = useState<Size>(calculateTextSize(props.tag.rating?.ratingLabel));
    // const [ratingValueSize, setRatingValueSize] = useState<Size>(calculateTextSize(rating?.value));
    const [isTypeChangeSupported, setIsTypeChangeSupported] = useState<boolean>(false);

    // TODO: uncomment when ratings enabled
    // useEffect(() => {
    //     setRating(props.tag.ratingOptions?.find(option => option.id === props.tag.rating?.valueId));
    // }, [props.tag.ratingOptions, props.tag.rating?.valueId]);

    // useEffect(() => {
    //     setRatingValueSize(calculateTextSize(rating?.value));
    // }, [rating]);

    // useEffect(() => {
    //     setRatingLabelSize(calculateTextSize(props.tag.rating?.ratingLabel));
    // }, [props.tag.rating?.ratingLabel]);

    useEffect(() => {
        const type = props.tag?.originType ?? props.tag?.type;
        setIsTypeChangeSupported(type !== NodeType.MainRiskEvent && type !== NodeType.Control && !props.isLegendNode);
    }, [props.tag?.originType, props.tag?.type]);

    useEffect(() => {
        const [prefix, iconName] = props.style.iconContent.split(' ');
        const camelCaseIconName = iconName.replace(/-(\w)/g, (match, letter) => letter.toUpperCase());
        if (prefix === 'fas') {
            setIcon(iconsSolid[camelCaseIconName]);
        } else if (prefix === 'far') {
            setIcon(iconsRegular[camelCaseIconName]);
        } else {
            setIcon(iconsSolid[camelCaseIconName]);
        }
    }, [props.style.iconContent]);

    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="400"
            height="150"
            fontSize="24"
            textAnchor="middle"
        >
            <g>
                <g></g>
                <rect
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-${props.tag.name}`}
                    fill={props.style.fillColor1}
                    stroke={props.style.strokeColor}
                    x={0}
                    y={0}
                    width={props.width}
                    height={props.height}
                    rx={8}
                />
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="icon"
                    aria-hidden="true"
                    focusable="false"
                >
                    {icon && (
                        <FontAwesomeIcon
                            x={props.width - 20}
                            y={8}
                            width={12}
                            height={12}
                            color={props.style.iconColor}
                            icon={icon}
                        />
                    )}
                </svg>

                {/*
            // TODO: uncomment when ratings enabled
            <g data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-${props.tag.name}-library-rating`} display={props.tag.rating ? 'block' : 'none'}>
                <text
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-library-rating-label`}
                    x={LABEL_INSET}
                    y={props.height + ratingValueSize.height - LABEL_INSET - FONT.fontSize}
                    fontFamily={FONT.fontFamily}
                    fontSize={FONT.fontSize}
                >
                    {props.tag.rating?.ratingLabel}
                </text>
                <rect
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-library-rating-color`}
                    x={LABEL_INSET + 4 + ratingLabelSize.width}
                    y={props.height - LABEL_INSET - FONT.fontSize}
                    width={ratingValueSize.width + 8}
                    height={ratingValueSize.height + 6}
                    rx="4"
                    fill={rating?.color}
                />
                <text
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-library-rating-value`}
                    x={LABEL_INSET + 8 + ratingLabelSize.width}
                    y={props.height + ratingValueSize.height - LABEL_INSET - FONT.fontSize}
                    fill="white"
                    fontFamily={FONT.fontFamily}
                    fontSize={FONT.fontSize}
                >
                    {rating?.value}
                </text>
            </g> */}
                {props.isLegendNode && (
                    <text
                        data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-default-text`}
                        x={LABEL_INSET}
                        y={LABEL_INSET + 14}
                        fontFamily={FONT.fontFamily}
                        fontWeight={600}
                        fontSize={14}
                    >
                        {NODE_LEGEND_LABELS[props.tag.type]}
                    </text>
                )}

                <g
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-${props.tag.name}-change-type-button`}
                    style={{
                        ...(isTypeChangeSupported && {
                            cursor: 'pointer',
                            '&:hover': {
                                fillOpacity: 1,
                                circle: {
                                    fillOpacity: 1,
                                },
                            },
                        }),
                    }}
                    onClick={isTypeChangeSupported ? props.typeChangeClick : undefined}
                >
                    <circle
                        fillOpacity={0}
                        fill={props.tag.type === NodeType.Blank ? theme.palette.protechtGrey?.grey_245 : theme.palette.background.default}
                        cx={props.width - 14}
                        cy={14}
                        r={10}
                    />
                    <use
                        xlinkHref={`#${props.tag.type}`}
                        x={props.width - 20}
                        y={8}
                        width={12}
                        height={12}
                    />
                </g>

                <g
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-${props.tag.name}-library-link-indicator`}
                    className={'bowtie-yfiles-node-icon-wrapper'}
                    onClick={props.unlinkLibrary}
                    display={props.tag.libraryLink ? 'block' : 'none'}
                >
                    <title>{strings('bowtie:diagram.message.unlinkLibrary')}</title>
                    <circle
                        fillOpacity={0}
                        className="bowtie-yfiles-node-inner-circle"
                        fill={theme.palette.background.default}
                        cx={props.width - 14}
                        cy={props.height - 14}
                        r={10}
                    />
                    <FontAwesomeIcon
                        x={props.width - 20}
                        y={props.height - 20}
                        width={12}
                        height={12}
                        color={props.tag.libraryLink?.id === DELETED_LINK.id ? theme.palette.error.main : theme.palette.protechtGrey?.grey_96}
                        icon={props.tag.libraryLink?.id === DELETED_LINK.id ? iconsSolid.faUnlink : iconsSolid.faLink}
                    />
                </g>
            </g>
        </svg>
    );
};

export default NodeTemplate;
