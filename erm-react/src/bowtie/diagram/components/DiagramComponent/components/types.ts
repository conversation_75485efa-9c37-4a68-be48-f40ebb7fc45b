import { NodeData, NodeStyle, NodeType } from 'bowtie/types';
import { INodeStyle } from 'yfiles';

export type ReactNodeStyleProps = {
    width: number;
    height: number;
    tag: NodeData;
    style: NodeStyle;
    typeChangeClick?: () => void;
    unlinkLibrary?: () => void;
    isLegendNode?: boolean;
};

export interface DefaultBowtieNodeStyle extends INodeStyle {
    nodeStyles: Map<NodeType, NodeStyle>;
}
