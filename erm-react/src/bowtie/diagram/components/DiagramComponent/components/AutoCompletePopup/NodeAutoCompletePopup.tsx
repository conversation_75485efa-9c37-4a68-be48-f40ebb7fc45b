import React, { useCallback, useEffect, useState } from 'react';
import { ExteriorLabelModelPosition, GraphComponent, GraphEditorInputMode, INode, TextEventArgs } from 'yfiles';
import debounce from 'lodash/debounce';

import { IdWithName } from 'app/types';
import { isLibraryLinkSupported } from '../utils';
import NodeElement from 'common/diagram/components/DiagramElement/NodeElement';
import { BowTieDefinition } from 'api/generated/types';
import NodeAutoComplete from 'bowtie/components/BowTieImport/AutoCompletePopup';

type NodeAutoCompletePopupProps = {
    graphComponent: GraphComponent;
    selected: INode | null;
    onSelect: (selected: INode | null, item?: IdWithName) => void;
    bowtieDefinition?: BowTieDefinition;
    oldControlsEnabled?: boolean;
};

const NodeAutoCompletePopup: React.FC<NodeAutoCompletePopupProps> = (props: NodeAutoCompletePopupProps) => {
    const { selected } = props;
    const [editing, setEditing] = useState<boolean>(false);
    const [searchValue, setSearchValue] = useState<string>(props.selected?.tag?.name);
    const initState = useCallback(() => {
        setEditing(false);

        setSearchValue(props.selected?.tag?.name);
    }, [props.selected?.tag?.name]);

    useEffect(() => {
        if (!(props.graphComponent?.inputMode as GraphEditorInputMode)?.textEditorInputMode) {
            return;
        }

        if (!props.graphComponent.inputMode) {
            return;
        }

        const textEditorInputMode = (props.graphComponent?.inputMode as GraphEditorInputMode).textEditorInputMode;

        textEditorInputMode.editorContainer?.addEventListener(
            'input',
            debounce((e) => setSearchValue(e.target.value), 500),
        );

        const editingStartedListener = (sender: any, evt: TextEventArgs) => {
            setEditing(true);
            setSearchValue(evt.text);
        };

        textEditorInputMode.addEditingStartedListener(editingStartedListener);
        textEditorInputMode.addTextEditedListener(initState);
        textEditorInputMode.addEditingCanceledListener(initState);

        return () => {
            textEditorInputMode.removeEditingStartedListener(editingStartedListener);
            textEditorInputMode.removeTextEditedListener(initState);
            textEditorInputMode.removeEditingCanceledListener(initState);
        };
    }, [initState, props.graphComponent, selected]);

    if (!isLibraryLinkSupported(props.selected?.tag?.type, props.bowtieDefinition, props.oldControlsEnabled)) {
        return <div />;
    }

    const zoom = props.graphComponent?.zoom;
    const nodeWidth = selected?.layout.width;
    const width = (zoom && nodeWidth && nodeWidth * zoom) || nodeWidth;

    const handleItemSelected = (item?: IdWithName) => {
        if (selected?.tag) {
            props.onSelect(selected, item);
        }
        initState();
    };

    return (
        <NodeElement
            graphComponent={props.graphComponent}
            selected={selected}
            position={ExteriorLabelModelPosition.SOUTH}
        >
            <div style={{ width }}>
                <NodeAutoComplete
                    bowtieDefinition={props.bowtieDefinition}
                    isEditing={editing}
                    nodeType={selected?.tag.type}
                    searchValue={searchValue}
                    selected={selected?.tag.libraryLink}
                    onItemSelected={handleItemSelected}
                    sectionIndex={1}
                />
            </div>
        </NodeElement>
    );
};

export default NodeAutoCompletePopup;
