import {
    EventRecognizers,
    GraphEditorInputMode,
    GraphItemTypes,
    IGraph,
    IInputModeContext,
    INode,
    IPoint,
    MouseEventRecognizers,
    MoveViewportInputMode,
    Point,
    Rect,
    TimeSpan,
    TouchEventRecognizers,
} from 'yfiles';
import { LabelEditorInputMode } from 'common/diagram/components/InputMode/LabelEditorInputMode';
import { EdgeType } from 'bowtie/types';
import { BowtieDiagramUtils } from '../../utils';
import { ElementIdProvider } from 'common/diagram/utils/ElementIdProvider';
import { BLANK_NODE_DATA } from '../constants';

/**
 * {@link GraphEditorInputMode} with basic configuration for our diagram.
 * This class is used to override behaviour of certain functions in order to modify default input mode behaviour.
 */
export class DiagramInputMode extends GraphEditorInputMode {
    private idProvider: ElementIdProvider;

    constructor(idProvider: ElementIdProvider) {
        super({
            // label config
            allowAddLabel: false,
            allowEditLabel: true,
            autoRemoveEmptyLabels: false,
            showHandleItems: GraphItemTypes.ALL & ~GraphItemTypes.NODE,
            textEditorInputMode: new LabelEditorInputMode(),
        });
        this.idProvider = idProvider;
        // disable drag and select nodes with marquee.
        this.marqueeSelectionInputMode.enabled = false;
        // disable multiselection of items.
        this.multiSelectionRecognizer = () => false;
        // config to disable bend creation
        this.createBendInputMode.enabled = false;
        this.createEdgeInputMode.allowCreateBend = false;
        this.createEdgeInputMode.allowSelfloops = false;
        this.createEdgeInputMode.edgeCreator = (context, graph, sourceCandidate, targetCandidate) => {
            if (!sourceCandidate || !targetCandidate) {
                // cancel if either candidate is missing
                return null;
            }
            // get the source and target ports from the candidates
            const sourcePort = sourceCandidate.port || sourceCandidate.createPort(context);
            const targetPort = targetCandidate.port || targetCandidate.createPort(context);

            // make sure there is only one edge between two nodes.
            if (BowtieDiagramUtils.isDirectlyConnectedTo(sourcePort.owner as INode, targetPort.owner as INode, context?.graph)) {
                return null;
            }

            return graph.createEdge(sourcePort, targetPort, null, { id: this.idProvider.getNextEdgeId(), type: EdgeType.SECONDARY });
        };

        //blank node creator -> called on canvas click
        this.nodeCreator = (context: IInputModeContext, graph: IGraph, location: IPoint, parent: INode | null) => {
            // create a node at the location with the given parent and the default size
            let node;
            if (parent !== null) {
                //in case of node groups, we use parent node
                node = graph.createNode(parent, Rect.fromCenter(location, graph.nodeDefaults.size), graph.nodeDefaults.style, {
                    ...BLANK_NODE_DATA,
                    id: this.idProvider.getNextNodeId(),
                });
            } else {
                node = graph.createNode(Rect.fromCenter(location, graph.nodeDefaults.size), graph.nodeDefaults.style, {
                    ...BLANK_NODE_DATA,
                    id: this.idProvider.getNextNodeId(),
                });
            }
            // add a label
            graph.addLabel(node, BLANK_NODE_DATA.name);
            // return the new node
            return node;
        };

        // NODE TOOLTIP SETUP
        this.toolTipItems = GraphItemTypes.NODE;
        this.mouseHoverInputMode.duration = TimeSpan.fromSeconds(20);
        this.mouseHoverInputMode.toolTipLocationOffset = new Point(0, 12);
        // register a listener
        this.addQueryItemToolTipListener((src, args) => {
            if (args.handled) {
                return;
            }
            // We can safely cast here because we set ToolTipItems to only Node.
            const hitNode = args.item as INode;
            if (hitNode.labels.size > 0 && hitNode.labels.get(0).text.length > 0) {
                args.toolTip = hitNode.labels.get(0).text;
                // Indicate that the tooltip content has been set.
                args.handled = true;
            }
        });
    }

    // By overriding this funciton we block creation of node on LEFT click
    clickCreateNode(): boolean {
        return false;
    }

    // creates MoveViewportInputMode that handles panning (moving graph)
    createMoveViewportInputMode(): MoveViewportInputMode {
        const moveViewportInputMode = new MoveViewportInputMode();
        moveViewportInputMode.priority = 50;
        moveViewportInputMode.pressedRecognizer = EventRecognizers.createOrRecognizer(
            MouseEventRecognizers.LEFT_DRAG,
            TouchEventRecognizers.TOUCH_MOVE_PRIMARY,
        );
        return moveViewportInputMode;
    }
}
