import {
    EditLabelHelper,
    IInputModeContext,
    ILabel,
    INode,
    LabelEditingEventArgs,
    Point,
    Rect,
    SimpleLabel,
    Size,
    TextEditorInputMode,
    TextEventArgs,
} from 'yfiles';
import { MAX_NODE_HEIGHT } from '../constants';
import { Node } from 'bowtie/types';

/**
 * Custom label edit helper.
 *
 * This class does the following:
 * <ul>
 * <li>Change the appearance of the {@link TextEditorInputMode}</li>
 * </ul>
 * For convenience, this implementation inherits from the predefined {@link EditLabelHelper} class.
 */
export default class CustomEditLabelHelper extends EditLabelHelper {
    public readonly afterEditCallback: (label: ILabel, node: Node) => void;

    constructor(callback: (label: ILabel, node: Node) => void) {
        super();
        this.afterEditCallback = callback;
    }

    /**
     * This method is called when label should be edited.
     *
     * If a label is edited directly, we either return it (if it is the second label) or prevent editing.
     * @param {LabelEditingEventArgs} args
     */
    onLabelEditing(args: LabelEditingEventArgs): void {
        args.textEditorInputModeConfigurator = this.configureTextEditorInputMode;
        super.onLabelEditing(args);
    }

    /**
     * Customize the text editor when we are using our helper.
     * @param {IInputModeContext} context
     * @param {TextEditorInputMode} mode
     * @param {ILabel} labelToEdit
     */
    configureTextEditorInputMode = (context: IInputModeContext, mode: TextEditorInputMode, labelToEdit: ILabel): void => {
        const node = labelToEdit.owner;

        if (INode.isInstance(node)) {
            const zoomedNodeHeight = MAX_NODE_HEIGHT * context.zoom;
            const zoomedNodeWidth = node.layout.width * context.zoom;

            const newLayout = Rect.fromCenter(node.layout.center, new Size(node.layout.width, MAX_NODE_HEIGHT));
            context.graph?.setNodeLayout(node, newLayout);

            const input = mode.editorContainer.getElementsByTagName('textarea').item(0);
            if (input) {
                // padding top 4px + padding bottom 4px = 8px
                // padding right 30px
                input.style.width = zoomedNodeWidth - 8 - 30 + 'px';
                input.style.height = zoomedNodeHeight - 8 + 'px';
            }

            // anchor the text editor to the top left corner
            mode.anchor = new Point(0, 0);

            // place the editor relative to the node's top left corner
            mode.location = new Point(node.layout.x + 4, node.layout.y + 4);

            const afterEditing = (sender: TextEditorInputMode, evt: TextEventArgs) => {
                const toBeLabel = new SimpleLabel({
                    owner: node,
                    text: evt.text,
                    style: context.graph?.nodeDefaults.labels.style,
                });
                toBeLabel.preferredSize =
                    context.graph?.nodeDefaults.labels.style.renderer.getPreferredSize(toBeLabel, toBeLabel.style) ?? labelToEdit.preferredSize;
                this.afterEditCallback(toBeLabel, node);

                // Potentially just set label text and layout without need to pass callback and get renderer from nodeDefaults
                // context.graph?.setLabelText(labelToEdit, evt.text);
                // context.graph?.setNodeLayout(node, Rect.fromCenter(node.layout.center, labelToEdit.preferredSize));

                mode.removeTextEditedListener(afterEditing);
            };
            mode.addTextEditedListener(afterEditing);
            mode.addEditingCanceledListener(() => mode.removeTextEditedListener(afterEditing));
        }
    };
}
