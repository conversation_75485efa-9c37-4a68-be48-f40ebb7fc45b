import React, { useState } from 'react';

import Box from '@mui/material/Box';
import DialogActions from '@mui/material/DialogActions';
import Typography from '@mui/material/Typography';
import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';
import { strings } from 'common/utils/i18n';
import { AlertCautionIcon } from 'common/icons/AlertCautionIcon';
import useIsMounted from 'common/hooks/useIsMounted';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Dialog from '@protecht/ui-library/library/components/Dialog';

type Props = {
    visible: boolean;
    onClose: () => void;
    onDelete: () => Promise<void>;
    onDisconnect: () => Promise<void>;
};

const DeleteNodeDialog: React.FC<Props> = (props: Props) => {
    const { visible, onClose, onDelete, onDisconnect } = props;

    const [actionInProgress, setActionInProgress] = useState<boolean>(false);
    const isMounted = useIsMounted();

    const onButtonClick = async (action: () => Promise<void>) => {
        if (action) {
            try {
                setActionInProgress(true);
                await Promise.resolve(action());
            } finally {
                if (isMounted()) {
                    setActionInProgress(false);
                }
            }
        }
    };

    return (
        <Dialog
            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-delete-node-dialog`}
            visible={visible}
            title={strings('bowtie:diagram.title.deleteBowTieNode')}
            onOutsideClickClose={onClose}
            width={500}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        disabled={actionInProgress}
                        onClick={() => onButtonClick(onDisconnect)}
                        dataTestId={`${DIAGRAM_TEST_ID_PREFIX}-delete-node-dialog-disconnect-button`}
                    >
                        {strings('common:label.disconnect')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant="secondary"
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('ermMessages:btn_cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        disabled={actionInProgress}
                        onClick={() => onButtonClick(onDelete)}
                        dataTestId="button-confirm"
                    >
                        {strings('common:button.delete')}
                    </Button>
                </DialogActions>
            }
        >
            <Box
                display="flex"
                flexDirection="row"
                height="100%"
            >
                <Box
                    pr={3}
                    fontSize={'45px'}
                >
                    <AlertCautionIcon />
                </Box>
                <Box>
                    <Typography
                        variant="body2"
                        whiteSpace="pre-line"
                        mb={1}
                    >
                        {strings('bowtie:diagram.message.nodeHasConnections')}
                    </Typography>
                    <Typography
                        variant="body2"
                        whiteSpace="pre-line"
                    >
                        {strings('bowtie:diagram.message.whatToDoWithConnectedNodes')}
                    </Typography>
                </Box>
            </Box>
        </Dialog>
    );
};

export default DeleteNodeDialog;
