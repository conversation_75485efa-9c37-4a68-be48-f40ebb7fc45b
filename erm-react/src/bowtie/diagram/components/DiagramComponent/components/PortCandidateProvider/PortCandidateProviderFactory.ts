import DefaultPortCandidateProvider from './DefaultPortCandidateProvider';
import ControlPortCandidateProvider from './ControlPortCandidateProvider';
import { Node, NodeType } from 'bowtie/types';
import { PortCandidateProviderBase } from 'yfiles';

export default (node: Node): PortCandidateProviderBase => {
    if (node?.tag?.type === NodeType.Control) {
        return new ControlPortCandidateProvider(node);
    } else {
        return new DefaultPortCandidateProvider(node);
    }
};
