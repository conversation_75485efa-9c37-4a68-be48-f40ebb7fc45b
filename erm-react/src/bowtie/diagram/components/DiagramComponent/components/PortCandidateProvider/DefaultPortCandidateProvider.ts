import {
    DefaultPortCandidate,
    FreeNodePortLocationModel,
    IGraph,
    IInputModeContext,
    INode,
    IPortCandidate,
    List,
    PortCandidateProviderBase,
    PortCandidateValidity,
} from 'yfiles';

import { BowtieDiagramUtils } from '../../utils';
import { Node } from 'bowtie/types';
import DiagramPredicates from '../../utils/DiagramPredicates';

class DefaultPortCandidateProvider extends PortCandidateProviderBase {
    private readonly owner: Node;

    // each instance is built for a specific port owner
    constructor(owner: Node) {
        super();
        this.owner = owner;
    }

    // the port candidate list which is returned by default
    getPortCandidates(context: IInputModeContext): List<IPortCandidate> {
        // create a new list
        const candidates: List<IPortCandidate> = new List();
        // add candidates for all existing ports
        const portCandidate = new DefaultPortCandidate(this.owner as INode, FreeNodePortLocationModel.NODE_CENTER_ANCHORED);
        if (
            IGraph.isInstance(context.graph) &&
            !BowtieDiagramUtils.isConnectedToRoot(this.owner as INode, BowtieDiagramUtils.getGraph(context.graph, DiagramPredicates.WITHOUT_SECONDARY_EDGES))
        ) {
            portCandidate.validity = PortCandidateValidity.INVALID;
        }
        candidates.add(portCandidate);
        return candidates;
    }

    // determine if node is valid for connection.
    getTargetPortCandidates(context: IInputModeContext, source: IPortCandidate): List<IPortCandidate> {
        const list = new List<IPortCandidate>();
        let valid = true;
        // determine validity of connection
        if (INode.isInstance(this.owner) && INode.isInstance(source.owner) && IGraph.isInstance(context.graph)) {
            if (
                // Target port need to be connected to main graph
                !BowtieDiagramUtils.isConnectedToRoot(this.owner, context.graph) ||
                // cross connection left to right and right to left is not allowed, root can be connected anywhere.
                !BowtieDiagramUtils.isConnectionAllowed(source.owner, this.owner, context.graph)
            ) {
                valid = false;
            }
        } else {
            valid = false;
        }

        if (valid) {
            // calculate closes port location and return it.
            if (INode.isInstance(this.owner) && INode.isInstance(source.owner)) {
                const sourcePorts = BowtieDiagramUtils.generatePorts(source.owner);
                const targetPorts = BowtieDiagramUtils.generatePorts(this.owner);

                const portLocations = BowtieDiagramUtils.computeClosestPort(sourcePorts, targetPorts);

                if (portLocations) {
                    list.add(new DefaultPortCandidate(this.owner, portLocations.targetPortLocation, PortCandidateValidity.VALID));
                } else {
                    list.add(new DefaultPortCandidate(this.owner, FreeNodePortLocationModel.NODE_CENTER_ANCHORED, PortCandidateValidity.INVALID));
                }
            }
        } else {
            list.add(new DefaultPortCandidate(this.owner as INode, FreeNodePortLocationModel.NODE_CENTER_ANCHORED, PortCandidateValidity.INVALID));
        }
        return list;
    }
}

export default DefaultPortCandidateProvider;
