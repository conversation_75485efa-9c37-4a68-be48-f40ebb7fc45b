import {
    DefaultPortCandidate,
    FreeNodePortLocationModel,
    IInputModeContext,
    INode,
    IPortCandidate,
    List,
    PortCandidateProviderBase,
    PortCandidateValidity,
} from 'yfiles';

import { Node } from 'bowtie/types';

class ControlPortCandidateProvider extends PortCandidateProviderBase {
    private readonly owner: Node;

    // each instance is built for a specific port owner
    constructor(owner: Node) {
        super();
        this.owner = owner;
    }

    // the port candidate list which is returned by default
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getPortCandidates(context: IInputModeContext): List<IPortCandidate> {
        // create a new list
        const candidates: List<IPortCandidate> = new List();
        // add candidates for all existing ports
        const portCandidate = new DefaultPortCandidate(this.owner as INode, FreeNodePortLocationModel.NODE_CENTER_ANCHORED);
        portCandidate.validity = PortCandidateValidity.INVALID;
        candidates.add(portCandidate);
        return candidates;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getTargetPortCandidates(context: IInputModeContext, source: IPortCandidate): List<IPortCandidate> {
        const candidates: List<IPortCandidate> = new List();
        const portCandidate = new DefaultPortCandidate(this.owner as INode, FreeNodePortLocationModel.NODE_CENTER_ANCHORED);
        portCandidate.validity = PortCandidateValidity.INVALID;
        candidates.add(portCandidate);
        return candidates;
    }
}

export default ControlPortCandidateProvider;
