import React, { useEffect, useState } from 'react';
import { GraphComponent, INode } from 'yfiles';

import { getNewNodeType, getPosition, getStyleForType } from '../utils';
import { NodeStyle, NodeType, Position } from 'bowtie/types';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { DIAGRAM_TEST_ID_PREFIX } from 'bowtie/diagram/constants';
import { strings } from 'common/utils/i18n';
import NodeElement from 'common/diagram/components/DiagramElement/NodeElement';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

type AddButtonProps = {
    graphComponent: GraphComponent;
    nodeStyles: Map<NodeType, NodeStyle>;
    selected: INode | null;
    position: Position;
    addNode: (node: INode | null, nodeType: NodeType, position: Position) => void;
    isHidden?: (newNodeType: NodeType) => boolean;
};

const StyledIcon = styled(IconButton)({
    width: 28,
    minWidth: 28,
    height: 28,
    padding: 0,
    borderRadius: '50%',
    background: 'transparent',
    '&:hover': {
        backgroundColor: 'transparent',
    },
    '&:focus': {
        backgroundColor: 'transparent',
    },
    '&:active': {
        backgroundColor: 'transparent',
    },

    '& svg': {
        verticalAlign: 'top',
        fontSize: '12px',
    },
});

const StyledAddButton = styled('div')<{ isVisible?: boolean | null; backgroundColor: string }>(({ isVisible, backgroundColor }) => ({
    display: 'none',
    alignItems: 'center',
    userSelect: 'none',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.25)',
    borderRadius: '50%',
    '*': {
        userSelect: 'none',
        verticalAlign: 'middle',
        color: 'white',
    },
    ...(isVisible && {
        display: 'block',
    }),
    backgroundColor,
}));

/**
 * Adds a button on left or right side of the selected node.
 * On button press new node is added to diagram.
 */
const AddButton = (props: AddButtonProps): React.ReactElement => {
    const [newNodeType, setNewNodeType] = useState<NodeType | null>(getNewNodeType(props.selected, props.position));

    useEffect(() => {
        setNewNodeType(getNewNodeType(props.selected, props.position));
    }, [props.selected, props.position, props.selected?.tag.type, props.selected?.tag?.originType]);

    const onAddNode = () => {
        if (newNodeType) {
            props.addNode(props.selected, newNodeType, props.position);
        }
    };

    const isVisible = newNodeType && !props.isHidden?.(newNodeType) && props.selected?.tag?.originType !== NodeType.Blank;

    return (
        <NodeElement
            graphComponent={props.graphComponent}
            selected={props.selected}
            position={getPosition(props.position)}
        >
            <Tooltip
                leaveTouchDelay={100000}
                disableHoverListener={!newNodeType}
                title={strings(`bowtie:diagram.button.add${newNodeType}`)}
                aria-label={strings(`bowtie:diagram.button.add${newNodeType}`)}
            >
                <StyledAddButton
                    isVisible={isVisible}
                    backgroundColor={getStyleForType(props.nodeStyles, newNodeType).iconColor}
                >
                    {newNodeType && (
                        <StyledIcon
                            color={'secondary'}
                            onClick={() => onAddNode()}
                            aria-label={strings('bowtie:diagram.button.addNode')}
                            data-testid={`${DIAGRAM_TEST_ID_PREFIX}-node-add-${newNodeType}-button`}
                        >
                            <Add
                                width={14}
                                height={14}
                            />
                        </StyledIcon>
                    )}
                </StyledAddButton>
            </Tooltip>
        </NodeElement>
    );
};

export default AddButton;
