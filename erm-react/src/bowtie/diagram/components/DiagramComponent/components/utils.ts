import { Node, NodeStyle, NodeType, Position, Rating, RatingLabel, RatingType } from 'bowtie/types';
import { ExteriorLabelModelPosition } from 'yfiles';
import { DEFAULT_NODE_STYLE } from './constants';
import { BowTieDefinition } from 'api/generated/types';

export const getStyleForType = (nodeStyle: Map<NodeType, NodeStyle>, nodeType?: NodeType | null): NodeStyle => {
    const nodeStyles = nodeType && nodeStyle && nodeStyle[nodeType];
    return nodeStyles || DEFAULT_NODE_STYLE;
};

export function getNewNodeType(node: Node | null, position: Position): NodeType | null {
    if (node && node.tag) {
        const type = node.tag.type;
        switch (type) {
            case NodeType.MainRiskEvent:
                if (position === Position.Bottom) {
                    return NodeType.Control;
                } else {
                    return NodeType.RiskEvent;
                }
            case NodeType.RiskEvent:
                if (node.tag.originType === NodeType.Impact ? position === Position.Right : position === Position.Left) {
                    return NodeType.RiskEvent;
                } else {
                    return NodeType.Control;
                }
            case NodeType.Impact:
                return position === Position.Left ? NodeType.Control : null;
            case NodeType.Cause:
                return position === Position.Left ? null : NodeType.Control;
            case NodeType.Control:
                return position === Position.Bottom ? NodeType.Control : null;
        }
    }
    return null;
}

export const getChangeTypeOptions = (node: Node, isEndNode: boolean): NodeType[] => {
    let type = node.tag?.originType ?? node.tag?.type;
    if (node.tag?.type === NodeType.RiskEvent && !isEndNode) {
        type = node.tag?.type;
    }
    switch (type) {
        case NodeType.Cause:
            return [NodeType.Cause, NodeType.RiskEvent];
        case NodeType.Impact:
            return [NodeType.Impact, NodeType.RiskEvent];
        case NodeType.Blank:
            return [NodeType.RiskEvent, NodeType.Cause, NodeType.Impact, NodeType.Control];
        default:
            return [type];
    }
};

export const getRatingsForType = (data?: object, nodeType?: NodeType): Rating[] => {
    if (!data) {
        return [];
    }

    switch (nodeType) {
        case NodeType.MainRiskEvent:
        case NodeType.RiskEvent: {
            return data['enumRiskAppetite'] || [];
        }
        case NodeType.Cause: {
            return data['enumLikelihood'] || [];
        }
        case NodeType.Impact: {
            return data['enumImpact'] || [];
        }
        case NodeType.Control: {
            return data['enumControlEffectiveness'] || [];
        }
        case NodeType.Blank:
        default: {
            return [];
        }
    }
};

export const getRatingsLabelForType = (nodeType?: NodeType): RatingLabel | null => {
    switch (nodeType) {
        case NodeType.MainRiskEvent:
        case NodeType.RiskEvent: {
            return RatingLabel.RiskAppetite;
        }
        case NodeType.Cause: {
            return RatingLabel.Likelihood;
        }
        case NodeType.Impact: {
            return RatingLabel.Impact;
        }
        case NodeType.Control: {
            return RatingLabel.ControlEffectiveness;
        }
        case NodeType.Blank:
        default: {
            return null;
        }
    }
};

export const getRatingsTypeForType = (nodeType?: NodeType): RatingType | null => {
    switch (nodeType) {
        case NodeType.MainRiskEvent:
        case NodeType.RiskEvent: {
            return RatingType.EnumRiskAppetite;
        }
        case NodeType.Cause: {
            return RatingType.EnumLikelihood;
        }
        case NodeType.Impact: {
            return RatingType.EnumImpact;
        }
        case NodeType.Control: {
            return RatingType.EnumControlEffectiveness;
        }
        case NodeType.Blank:
        default: {
            return null;
        }
    }
};

export const isLibraryLinkSupported = (type: NodeType, bowTieDefinition?: BowTieDefinition, oldControlsEnabled?: boolean): boolean => {
    const supportedNodes: NodeType[] = [NodeType.MainRiskEvent, NodeType.RiskEvent, NodeType.Cause];
    if (bowTieDefinition && bowTieDefinition.riskControlRegister != null) {
        supportedNodes.push(NodeType.Control);
    } else if (bowTieDefinition && bowTieDefinition.riskControlRegister === null && oldControlsEnabled) {
        supportedNodes.push(NodeType.Control);
    } else if (bowTieDefinition && oldControlsEnabled) {
        supportedNodes.push(NodeType.Control);
    }

    if (bowTieDefinition && bowTieDefinition.riskImpactRegister !== undefined) {
        supportedNodes.push(NodeType.Impact);
    }
    return supportedNodes.includes(type);
};

export const getPosition = (position: Position): ExteriorLabelModelPosition => {
    switch (position as Position) {
        case Position.Left: {
            return ExteriorLabelModelPosition.WEST;
        }
        case Position.Right: {
            return ExteriorLabelModelPosition.EAST;
        }
        case Position.Bottom: {
            return ExteriorLabelModelPosition.SOUTH;
        }
        default: {
            return ExteriorLabelModelPosition.WEST;
        }
    }
};
