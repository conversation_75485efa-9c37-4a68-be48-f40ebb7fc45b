import 'yfiles/yfiles.css';

import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import {
    Class,
    ClickEventArgs,
    DefaultLabelStyle,
    EdgesSource,
    FilteredGraphWrapper,
    Font,
    GraphBuilder,
    GraphComponent,
    GraphCopier,
    GraphEditorInputMode,
    GraphItemTypes,
    GraphViewerInputMode,
    GraphMLSupport,
    HorizontalTextAlignment,
    IArrow,
    ICommand,
    IEnumerable,
    IGraph,
    ILabel,
    IModelItem,
    INode,
    Insets,
    InteriorLabelModel,
    IPositionHandler,
    ItemCopiedEventArgs,
    Key,
    LabelEventArgs,
    LabelLayerPolicy,
    LabelTextValidatingEventArgs,
    LayoutExecutor,
    License,
    ModifierKeys,
    MouseButtons,
    NodesSource,
    Point,
    PolylineEdgeStyle,
    Rect,
    SelectionEventArgs,
    Size,
    StorageLocation,
    Stroke,
    TimeSpan,
    VerticalTextAlignment,
} from 'yfiles';
import { yFilesLicense } from 'config';

import {
    BowTieRegisterEnum,
    BowTieStyle,
    DataModel,
    DiagramModelRef,
    EdgeType,
    LinkData,
    LinkModel,
    Node,
    NodeData,
    NodeModel,
    NodeType,
    Position,
    RatingModel,
} from 'bowtie/types';
import { ReactNodeStyle } from './components/Node/ReactNodeStyle';
import NodeTemplate from './components/Node/NodeTemplate';
import { MAX_NODE_HEIGHT, MIN_NODE_HEIGHT, NODE_WIDTH } from './components/constants';
import DiagramLayout from './layout/DiagramLayout';
import SubtreePositionHandler from './layout/SubtreePositionHandler';
import { BowtieDiagramUtils, BowtieElementIdProvider, ClipboardHelper, DataSerializer as DiagramSerializer } from './utils';
import { ExportContext } from 'common/diagram/constants';
import AddButton from './components/AddButton';
import NodeContextMenu from './components/ContextMenu';
import ChangeType from './components/ChangeTypePopup';
import { getRatingsForType } from './components/utils';

import { BOTTOM_ADD_BUTTON_TYPES, LABEL_INSET, NODE_FOOTER_HEIGHT } from '../../constants';
import EditLabelHelper from './components/InputMode/EditLabelHelper';
import NodeAutoCompletePopup from './components/AutoCompletePopup';
import useSnackbar from 'common/hooks/useSnackbar';
import { IdWithName } from 'app/types';
import DeleteNodeDialog from './components/DeleteNodeDialog';

import PortCandidateProviderFactory from './components/PortCandidateProvider/PortCandidateProviderFactory';
import { DiagramInputMode } from './components/InputMode/DiagramInputMode';
import { strings } from 'common/utils/i18n';
import DiagramPredicates from './utils/DiagramPredicates';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationCircle } from '@fortawesome/pro-solid-svg-icons';
import { faUnlink } from '@fortawesome/pro-light-svg-icons';
import ExportUtils from 'common/diagram/utils/ExportUtils';
import { styled } from '@mui/material/styles';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import { BowTieDiagramRest, BowTieRegister } from 'api/generated/types';
import { BowTieContractFieldsType, RegisterContractType, RegisterEntryRest } from 'register/types';
import { useSelector } from 'store';
import { getRegisterByType } from 'bowtie/selectors';
import { useBtrsGetDefinitionUsingGetQuery } from 'bowtie/rtkApi';
import { getContractColumn, getEntryTableRow } from 'register/utils';
import { useLazyRdrsvGetEntryByIdUsingGet1Query } from 'register/rtkApi';
import AlertDialog from 'ui/components/AlertDialog';
import { AlertType } from 'common/types';
import { generatePath, useLocation, useNavigate } from 'react-router';
import { BowtiePath } from 'bowtie/routes';

type DiagramProps = {
    data?: BowTieDiagramRest;
    styles?: BowTieStyle;
    showControls: boolean;
    layoutingEnabled?: boolean;
    libraryLinkEnabled?: boolean;
    onChange: () => void;
};

License.value = yFilesLicense;
Class.ensure(LayoutExecutor);
let graphComponent: GraphComponent;
let diagramLayout: DiagramLayout;
let clipboardHelper: ClipboardHelper;

const GraphComponentContainer = styled('div')({
    display: 'flex',
    flex: 1,
    backgroundColor: 'white',
    marginTop: '8px',
    '.yfiles-canvascomponent': {
        display: 'flex',
        flex: 1,
    },
    '.yfiles-tooltip': {
        backgroundColor: 'white',
        color: '#595959',
        borderColor: '#bfbfbf',
        borderRadius: '4px',
        borderWidth: '1px',
        padding: '5px',
        font: '12px Arial, sans-serif',
        boxShadow: '0 1px 4px rgba(0, 0, 0, 0.25)',
    },
});

const DiagramComponent = forwardRef<DiagramModelRef, DiagramProps>((props: DiagramProps, ref) => {
    const snackbarProviderContext = useSnackbar();
    const navigate = useNavigate();
    const location = useLocation();

    const riskEventRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskEventRegister));
    const riskCauseRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskCauseRegister));
    const riskControlRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskControlRegister));
    const riskImpactRegisterContract = useSelector((state) => getRegisterByType(state, BowTieRegisterEnum.RiskImpactRegister));

    const [selectedNode, setSelectedNode] = useState<INode | null>(null);
    const [nodeData, setNodeData] = useState<NodeModel[]>((props.data?.diagramModel as any)?.nodes || []);
    const [edgeData, setEdgeData] = useState<LinkModel[]>((props.data?.diagramModel as any)?.links || []);
    const [openChangeType, setOpenChangeType] = useState<boolean>(false);
    const [lastSelectedNodeId, setLastSelectedNodeId] = useState<number | null>(null);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
    const [errorDefinitionDialogOpen, setErrorDefinitionDialogOpen] = useState<boolean>(false);
    const [isLabelEditing, setIsLabelEditing] = useState<boolean>(false);
    const [canEdit, setCanEdit] = useState<boolean>(!props.data?.locked);
    const [lastCopiedNode, setLastCopiedNode] = useState<Node>();

    const edgeSource = useRef<EdgesSource<any> | null>(null);
    const graphBuilder = useRef<GraphBuilder | null>(null);
    const nodeSource = useRef<NodesSource<any> | null>(null);
    const divRef = useRef<HTMLDivElement>(null);

    const forceUpdate = React.useReducer(() => ({}), {})[1] as () => void;
    const [triggerEntryGet] = useLazyRdrsvGetEntryByIdUsingGet1Query();

    const oldControlsEnabled = ProtechtDictionary.ermMods.includes('OLD_CONTROLS');

    const { data: definition, isLoading, error } = useBtrsGetDefinitionUsingGetQuery();

    const { showConfirmationAlert } = useConfirmationAlert();
    /**
     * Enables loading and saving the graph to GraphML.
     */
    function enableGraphML(): void {
        // Create a new GraphMLSupport instance that handles save and load operations.
        // This is a convenience layer around the core GraphMLIOHandler class
        // that does all the heavy lifting. It adds support for commands at the GraphComponent level
        // and file/loading and saving capabilities.
        new GraphMLSupport({
            graphComponent,
            // configure loading from and saving to the file system
            storageLocation: StorageLocation.FILE_SYSTEM,
        });
    }

    const autoLayoutListener = useCallback(async () => {
        await morphLayout();
    }, []);

    useEffect(() => {
        if (error) {
            setErrorDefinitionDialogOpen(true);
        }
    }, [error]);

    useEffect(() => {
        // instantiate a new GraphComponent
        graphComponent = new GraphComponent();
        // create layout
        diagramLayout = new DiagramLayout(snackbarProviderContext);
        diagramLayout.addMappers(graphComponent.graph);
        // Set maximum zoom factor of viewport to 2.0
        graphComponent.maximumZoom = 1.4;
        graphComponent.minimumZoom = 0.6;
        graphComponent.graph.undoEngineEnabled = true;
        graphComponent.graphModelManager.labelLayerPolicy = LabelLayerPolicy.AT_OWNER;
        // initialize element styles
        initializeStyles();

        // Create node decorator for connecting nodes.
        createNodeDecorator();
        // append the GraphComponent to the DOM
        divRef.current!.appendChild(graphComponent.div);

        // First initialize ID provider with what data we have to set correct ID generation
        BowtieElementIdProvider.getInstance().nodes = nodeData;
        BowtieElementIdProvider.getInstance().links = edgeData;

        // validate diagram data before the diagram is rendered
        const validData = validateData(nodeData, edgeData);
        setNodeData(validData.nodes);
        setEdgeData(validData.links);

        // create graph builder from provided data model
        graphBuilder.current = createGraphBuilder(validData.nodes, validData.links);

        if (canEdit) {
            // configure an input mode for out of the box editing
            // must be called after graphbuilder creation to pass creators new instance of BowtieElementIdProvider
            graphComponent.inputMode = createEditorModeWithListeners();
        } else {
            graphComponent.inputMode = new GraphViewerInputMode({ toolTipItems: GraphItemTypes.NODE });

            (graphComponent.inputMode as GraphViewerInputMode).mouseHoverInputMode.duration = TimeSpan.fromSeconds(20);
            (graphComponent.inputMode as GraphViewerInputMode).mouseHoverInputMode.toolTipLocationOffset = new Point(0, 12);
            // register a listener
            (graphComponent.inputMode as GraphViewerInputMode).addQueryItemToolTipListener((src, args) => {
                if (args.handled) {
                    return;
                }
                // We can safely cast here because we set ToolTipItems to only Node.
                const hitNode = args.item as INode;
                if (hitNode.labels.size > 0 && hitNode.labels.get(0).text.length > 0) {
                    args.toolTip = hitNode.labels.get(0).text;
                    // Indicate that the tooltip content has been set.
                    args.handled = true;
                }
            });
        }

        // generate graph and ensure its valid as per BowTie rules.
        const graph: IGraph = BowtieDiagramUtils.ensureValid(graphBuilder.current.buildGraph());
        // wrap graph with filtered graph for hidden controls
        graphComponent.graph = BowtieDiagramUtils.getGraph(graph, DiagramPredicates.WITHOUT_HIDDEN_CONTROL);
        void graphComponent.fitGraphBounds();
        void graphComponent.fitContent();
        graphComponent.graph.nodes.forEach((node) => {
            node.labels && node.labels.size > 0 && updateNodeLayout(node.labels.get(0), node);
        });
        // The decorator on labels is called when a label is edited
        graphComponent.graph.decorator.labelDecorator.editLabelHelperDecorator.setImplementation(new EditLabelHelper(labelUpdate));

        clipboardHelper = new ClipboardHelper(BowtieElementIdProvider.getInstance(), diagramLayout);
        graphComponent.graph.decorator.nodeDecorator.clipboardHelperDecorator.setImplementation(clipboardHelper);

        graphComponent.graph.undoEngine?.addUnitUndoneListener(() => forceUpdate());
        graphComponent.graph.undoEngine?.addUnitRedoneListener(() => forceUpdate());

        morphLayout()
            .then(() => {
                graphComponent.graph.undoEngine!.clear();
                graphComponent.graph.undoEngine?.addPropertyChangedListener(() => props.onChange());
            })
            .catch(() => {});
        const nodeCopiedListener = (sender: GraphCopier, args: ItemCopiedEventArgs<Node>) => {
            if (args.original.tag?.type !== NodeType.MainRiskEvent) {
                setLastCopiedNode(args.original);
            }
        };

        const nodePastedListener = (sender: GraphCopier, args: ItemCopiedEventArgs<INode>) => {
            selectNode(args.copy);
            if (args.original.tag?.type !== NodeType.MainRiskEvent) {
                setLastCopiedNode(undefined);
            }
        };

        enableGraphML();
        graphComponent.clipboard.toClipboardCopier.addNodeCopiedListener(nodeCopiedListener);
        graphComponent.clipboard.fromClipboardCopier.addNodeCopiedListener(nodePastedListener);
        return () => {
            graphComponent.clipboard.toClipboardCopier.removeNodeCopiedListener(nodeCopiedListener);
            graphComponent.clipboard.fromClipboardCopier.removeNodeCopiedListener(nodePastedListener);
        };
    }, []);

    useEffect(() => {
        if (canEdit) {
            // set auto-layouting
            enableAutoLayoutHandler(graphComponent.inputMode as GraphEditorInputMode, props.layoutingEnabled);
        }
    }, [props.layoutingEnabled]);

    useEffect(() => {
        setCanEdit(!props.data?.locked);
    }, [props.data?.locked]);

    useEffect(() => {
        setSelectedNode(null);
        BowtieDiagramUtils.getGraph(graphComponent.graph, DiagramPredicates.FULL_GRAPH).nodes.forEach((node: Node) => {
            if (node.tag.type === NodeType.Control) {
                node.tag.hidden = !props.showControls;
            }
        });
        (graphComponent.graph as FilteredGraphWrapper).nodePredicateChanged();
        void morphLayout();
    }, [props.showControls]);

    useEffect(() => {
        if (props.data?.diagramModel) {
            BowtieElementIdProvider.getInstance().nodes = (props.data.diagramModel as any).nodes;
            BowtieElementIdProvider.getInstance().links = (props.data.diagramModel as any).links;

            // validate diagram data before the diagram is rendered
            const validData = validateData((props.data.diagramModel as any).nodes, (props.data.diagramModel as any).links);

            setNodeData(validData.nodes);
            setEdgeData(validData.links);
        }
    }, [props.data?.diagramModel]);

    /**
     * To be able to return data from diagram.
     */
    useImperativeHandle(ref, () => ({
        getDiagramModel() {
            return DiagramSerializer.serializeToJSON(BowtieDiagramUtils.getGraph(graphComponent.graph, DiagramPredicates.FULL_GRAPH));
        },
        exportImage() {
            void ExportUtils.exportImage(
                graphComponent.graph,
                props.data?.name,
                {
                    diagramName: props.data?.name,
                    description: props.data?.description,
                    lastModifiedBy: props.data?.lastModifiedBy,
                    // TODO: remove 'as string' when correct types will be retrieved from swagger
                    lastModified: props.data?.lastModifiedDate as string,
                },
                ExportContext.BOWTIE,
                props.styles?.nodeStyle || new Map(),
            );
        },
        exportPDF() {
            void ExportUtils.exportPdf(
                graphComponent.graph,
                props.data?.name,
                {
                    diagramName: props.data?.name,
                    description: props.data?.description,
                    lastModifiedBy: props.data?.lastModifiedBy,
                    // TODO: remove 'as string' when correct types will be retrieved from swagger
                    lastModified: props.data?.lastModifiedDate as string,
                },
                ExportContext.BOWTIE,
            );
        },
        printDiagram() {
            void ExportUtils.printImage(
                graphComponent.graph,
                {
                    diagramName: props.data?.name,
                    description: props.data?.description,
                    lastModifiedBy: props.data?.lastModifiedBy,
                    // TODO: remove 'as string' when correct types will be retrieved from swagger
                    lastModified: props.data?.lastModifiedDate as string,
                },
                ExportContext.BOWTIE,
                props.styles?.nodeStyle || new Map(),
            );
        },
        setActualUndoEngineToken() {
            //todo
            return null;
        },
        executeCommand,
    }));

    const validateData = (nodes: NodeModel[], edges: LinkModel[]): DataModel => {
        if (!BowtieDiagramUtils.hasValidIds(nodes) || !BowtieDiagramUtils.hasValidIds(edges)) {
            // notify user about invalid data handling
            showConfirmationAlert({
                icon: <FontAwesomeIcon icon={faExclamationCircle} />,
                title: strings('bowtie:diagram.title.dataValidationFailed'),
                contentText: strings('bowtie:diagram.message.dataValidationFailed'),
            });

            return BowtieDiagramUtils.handleDuplicates(nodes, edges, BowtieElementIdProvider.getInstance());
        }
        return {
            nodes: nodes,
            links: edges,
        };
    };

    const riskEventNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_RISK_EVENT, BowTieContractFieldsType.BOWTIE_RISK_EVENT_NAME, riskEventRegisterContract)
            ?.columnName;
    }, [riskEventRegisterContract]);

    const riskCauseNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_RISK_CAUSE, BowTieContractFieldsType.BOWTIE_RISK_CAUSE_NAME, riskCauseRegisterContract)
            ?.columnName;
    }, [riskCauseRegisterContract]);

    const riskControlNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_CONTROL, BowTieContractFieldsType.BOWTIE_RISK_CONTROL_NAME, riskControlRegisterContract)
            ?.columnName;
    }, [riskControlRegisterContract]);

    const riskImpactNameCol = useMemo(() => {
        return getContractColumn(RegisterContractType.BOWTIE_IMPACT, BowTieContractFieldsType.BOWTIE_RISK_IMPACT_NAME, riskImpactRegisterContract)?.columnName;
    }, [riskImpactRegisterContract]);

    const executeCommand = (command: ICommand, parameter?: any) => {
        if (command.canExecute(parameter || null, graphComponent)) {
            command.execute(parameter || null, graphComponent);
        }
    };

    const selectNode = (newNode: INode | null) => {
        const graphEditorInputMode = graphComponent.inputMode as GraphEditorInputMode;
        selectedNode && graphEditorInputMode.clearSelection();
        graphComponent.selection.clear();
        if (newNode) {
            graphComponent.selection.setSelected(newNode, true);
            graphEditorInputMode.setSelected(newNode, true);
            graphEditorInputMode.setCurrentItem(newNode);
            setSelectedNode(newNode);
        } else {
            setSelectedNode(null);
        }
    };

    const onLabelUpdate = (sender: any, evt: LabelEventArgs) => {
        const label = evt.item;
        const node = label.owner;

        if (!INode.isInstance(node)) {
            return;
        }
        labelUpdate(label, node as Node);
    };
    const labelUpdate = (label: ILabel, node: Node) => {
        if (node.tag.name !== label.text) {
            if (node.tag.libraryLink && node.tag.libraryLink.name !== label.text) {
                unlinkLibraryOnLabelTextChanged(node as INode, label);
            } else {
                // update node tag for saving node label
                node.tag = { ...node.tag, name: label.text };
            }
        }

        updateNodeLayout(label, node);
        void morphLayout();

        setIsLabelEditing(false);
    };

    const updateNodeLayout = (label: ILabel, node: Node) => {
        let newLayout;
        if (node.tag.rating || node.tag.libraryLink) {
            const labelSize = new Size(label.layout.width, label.layout.height + NODE_FOOTER_HEIGHT);
            const minSize = new Size(NODE_WIDTH, MIN_NODE_HEIGHT + NODE_FOOTER_HEIGHT);
            const fitLabelSize = Size.max(minSize, labelSize);
            newLayout = Rect.fromCenter(node.layout.center, new Size(fitLabelSize.width, fitLabelSize.height));
        } else {
            const labelSize = new Size(label.layout.width, label.layout.height);
            const minSize = new Size(NODE_WIDTH, MIN_NODE_HEIGHT);
            newLayout = Rect.fromCenter(node.layout.center, Size.max(minSize, labelSize));
        }
        graphComponent.graph.setNodeLayout(node as INode, newLayout);
    };

    /**
     * Create decorator for dragging elements with subtrees.
     */
    const createNodeDecorator = () => {
        const nodeDecorator = graphComponent.graph.decorator.nodeDecorator;
        // customize the position handler
        nodeDecorator.positionHandlerDecorator.setImplementationWrapper((item: INode | null, implementation: IPositionHandler | null) => {
            if (BowtieDiagramUtils.getRoot(graphComponent.graph) !== item && implementation) {
                return new SubtreePositionHandler(implementation, diagramLayout, BowtieElementIdProvider.getInstance(), () => {
                    forceUpdate();
                    graphComponent.clipboard.clear();
                });
            }
            return null;
        });
        nodeDecorator.portCandidateProviderDecorator.setFactory(PortCandidateProviderFactory);
    };

    /**
     * Creates and configures the default input mode for the graph component,
     * a {@link GraphEditorInputMode}.
     * @return {GraphEditorInputMode} a new <code>GraphEditorInputMode</code> instance
     */
    const createEditorModeWithListeners = (): GraphEditorInputMode => {
        // configure interaction
        const inputMode = new DiagramInputMode(BowtieElementIdProvider.getInstance());

        // label listeners
        inputMode.addLabelEditingListener(() => setIsLabelEditing(true));
        inputMode.addLabelTextEditingCanceledListener(onLabelUpdate);
        inputMode.addValidateLabelTextListener((sender: any, args: LabelTextValidatingEventArgs) => {
            const label = args.label;
            const node = label.owner;
            if (INode.isInstance(node)) {
                args.cancel = args.newText.length > 250;
            }
            setIsLabelEditing(false);
        });

        // selection listeners
        inputMode.addMultiSelectionFinishedListener((src: any, args: SelectionEventArgs<IModelItem>) => {
            const nodes: IEnumerable<IModelItem> = args.selection.filter((item) => INode.isInstance(item));
            if (nodes.size === 0) {
                setSelectedNode(null);
            } else {
                setSelectedNode(nodes.last() as INode);
            }
        });

        // fire createNode when RIGHT clicking canvas
        inputMode.addCanvasClickedListener((sender: any, e: ClickEventArgs) => {
            setOpenChangeType(false);
            if (e.mouseButtons === MouseButtons.RIGHT) {
                inputMode.createNode(e.location);
            }
        });

        //item deletion
        inputMode.addDeletedItemListener(() => {
            setSelectedNode(null);
        });
        inputMode.deletablePredicate = (item) => {
            // don't allow MainRiskEvent deletion
            return !(INode.isInstance(item) && item.tag && (item.tag as NodeData).type === NodeType.MainRiskEvent);
        };
        const deleteCommand = ICommand.createCommand('delete');
        inputMode.keyboardInputMode.addCommandBinding(
            deleteCommand,
            (command, parameter, target) => {
                const nodes = BowtieDiagramUtils.getCommandTargetNodes(parameter, target);
                if (nodes && nodes.size > 0) {
                    nodes.forEach((node) => {
                        if (node?.tag?.type !== NodeType.MainRiskEvent) {
                            onNodeRemovedInitialized(node);
                        }
                    });
                }
                const edges = BowtieDiagramUtils.getCommandTargetEdges(parameter, target);
                if (edges && edges.size > 0) {
                    edges.forEach((edge) => {
                        const graph = BowtieDiagramUtils.getGraph(graphComponent.graph, DiagramPredicates.WITHOUT_SECONDARY_EDGES);
                        const fullGraph = BowtieDiagramUtils.getGraph(graphComponent.graph, DiagramPredicates.FULL_GRAPH);
                        const sourceNode = edge.sourceNode;
                        const targetNode = edge.targetNode;
                        if (ICommand.DELETE.canExecute(parameter, target)) {
                            ICommand.DELETE.execute(parameter, target);
                        }
                        if (sourceNode && !BowtieDiagramUtils.isConnectedToRoot(sourceNode, graph)) {
                            BowtieDiagramUtils.applyForSubtree(sourceNode, graph, (item) =>
                                fullGraph
                                    .edgesAt(item)
                                    .toArray()
                                    .forEach((e) => fullGraph.remove(e)),
                            );
                        }
                        if (targetNode && !BowtieDiagramUtils.isConnectedToRoot(targetNode, graph)) {
                            BowtieDiagramUtils.applyForSubtree(targetNode, graph, (item) =>
                                fullGraph
                                    .edgesAt(item)
                                    .toArray()
                                    .forEach((e) => fullGraph.remove(e)),
                            );
                        }
                    });
                    void morphLayout();
                }
                return true;
            },
            () => true,
        );
        inputMode.keyboardInputMode.addKeyBinding(Key.DELETE, ModifierKeys.NONE, deleteCommand);
        inputMode.keyboardInputMode.addKeyBinding(Key.BACK, ModifierKeys.NONE, deleteCommand);
        inputMode.keyboardInputMode.addCommandBinding(ICommand.UNDO, () => {
            if (graphComponent.graph.undoEngine?.canUndo()) {
                selectNode(null);
                graphComponent.graph.undoEngine?.undo();
            }
            return true;
        });

        return inputMode;
    };

    /**
     * Creates and configures the {@link GraphBuilder}.
     * @return {GraphBuilder}
     */
    const createGraphBuilder = (nodes: NodeModel[], edges: LinkModel[]): GraphBuilder => {
        const builder = new GraphBuilder(graphComponent.graph);
        nodeSource.current = builder.createNodesSource({
            data: nodes || [],
            id: 'key',
            labels: ['name'],
            layout: (item: NodeModel): Rect => {
                const x = item.position?.x || 0;
                const y = item.position?.y || 0;
                return new Rect(new Point(x, y), graphComponent.graph.nodeDefaults.size);
            },
            tag: (item: NodeModel): NodeData => {
                const type = NodeType[item.nodeType];
                return {
                    id: item.key,
                    name: item.name,
                    type,
                    originType: item.originType && NodeType[item.originType],
                    libraryLink: item.libraryLink,
                    rating: item.rating,
                    ratingOptions: getRatingsForType(props.data?.diagramModel, type),
                };
            },
        });
        edgeSource.current = builder.createEdgesSource({
            data: edges || [],
            sourceId: 'from',
            targetId: 'to',
            tag: (item: LinkModel): LinkData => ({ id: item.key, from: item.from, to: item.to, type: EdgeType[item.template] }),
        });
        return builder;
    };

    const morphLayout = async (): Promise<void> => {
        return diagramLayout.layout(graphComponent);
    };

    useEffect(() => {
        if (selectedNode && selectedNode?.tag?.id !== lastSelectedNodeId) {
            clipboardHelper!.targetNode = selectedNode;
            setOpenChangeType(false);
            setLastSelectedNodeId(selectedNode?.tag?.id);
        }
    }, [selectedNode?.tag?.id]);

    const changeTypePopupToggle = () => setOpenChangeType((prevState) => !prevState);

    const unlinkLibrary = (node: INode) => {
        if (!node) {
            return;
        }

        showConfirmationAlert({
            icon: <FontAwesomeIcon icon={faUnlink} />,
            title: strings('bowtie:diagram.title.unlinkBowTieNode'),
            contentText: strings('bowtie:diagram.message.unlinkBowTieNode'),
            onConfirm: () => {
                node.tag = { ...node.tag, libraryLink: undefined };
            },
        });
    };

    const unlinkLibraryOnLabelTextChanged = (node: INode, label: ILabel) => {
        showConfirmationAlert({
            icon: <FontAwesomeIcon icon={faUnlink} />,
            title: strings('bowtie:diagram.title.unlinkBowTieNode'),
            contentText: strings('bowtie:diagram.message.unlinkBowTieNodeAfterRename'),
            onClose: () => {
                const nodeLabel = node.labels.get(0);
                nodeLabel && graphComponent.graph.setLabelText(nodeLabel, node.tag.libraryLink.name);

                node.tag = { ...node.tag, name: node.tag.libraryLink.name };
                updateNodeLayout(label, node);
                void morphLayout();
            },
            onConfirm: () => {
                node.tag = { ...node.tag, name: label.text, libraryLink: undefined };
                updateNodeLayout(label, node);
                void morphLayout();
            },
        });
    };

    const unlinkLibraryOnNodeTypeChanged = (node: Node, type: NodeType) => {
        showConfirmationAlert({
            icon: <FontAwesomeIcon icon={faUnlink} />,
            title: strings('bowtie:diagram.title.unlinkBowTieNode'),
            contentText: strings('bowtie:diagram.message.unlinkBowTieNodeOnNodeTypeChanged'),
            onConfirm: () => {
                changeNodeType(node, type);
            },
        });
    };

    const initializeStyles = () => {
        graphComponent.graph.nodeDefaults.size = new Size(NODE_WIDTH, MIN_NODE_HEIGHT);
        graphComponent.graph.nodeDefaults.style = new ReactNodeStyle(NodeTemplate, props.styles?.nodeStyle || new Map(), changeTypePopupToggle, unlinkLibrary);
        graphComponent.graph.nodeDefaults.labels.style = new DefaultLabelStyle({
            minimumSize: new Size(NODE_WIDTH, MIN_NODE_HEIGHT),
            maximumSize: new Size(NODE_WIDTH, MAX_NODE_HEIGHT),
            textFill: '#262626',
            font: new Font('Open Sans, Arial', 14, 'normal', 'item600'),
            horizontalTextAlignment: HorizontalTextAlignment.LEFT,
            verticalTextAlignment: VerticalTextAlignment.TOP,
            insets: new Insets(LABEL_INSET, LABEL_INSET, 2 * LABEL_INSET, LABEL_INSET),
            wrapping: 'word-ellipsis',
        });
        graphComponent.graph.nodeDefaults.labels.layoutParameter = InteriorLabelModel.NORTH_WEST;

        graphComponent.graph.edgeDefaults.style = new PolylineEdgeStyle({
            sourceArrow: IArrow.NONE,
            targetArrow: IArrow.NONE,
            stroke: Stroke.DIM_GRAY,
            smoothingLength: 5,
        });
    };

    const enableAutoLayoutHandler = (inputMode: GraphEditorInputMode, enabled = true): void => {
        if (enabled) {
            inputMode?.moveInputMode.addDragFinishedListener(autoLayoutListener);
            inputMode?.createEdgeInputMode.addEdgeCreatedListener(autoLayoutListener);
            void morphLayout();
        } else {
            inputMode?.moveInputMode.removeDragFinishedListener(autoLayoutListener);
            inputMode?.createEdgeInputMode.removeEdgeCreatedListener(autoLayoutListener);
        }
    };

    /**
     * Create node called by 'Add' buttons.
     * Creates node of specified type with default label and links it to it's to node, we called 'Add node' action from.
     */
    const onNodeAdded = (parent: INode, nodeType: NodeType, position: Position) => {
        const edit = graphComponent.graph.beginEdit('Add node', 'Add node');
        let originType = (nodeType === NodeType.RiskEvent && position === Position.Left) || nodeType === NodeType.Cause ? NodeType.Cause : NodeType.Impact;

        if (nodeType === NodeType.Control) {
            originType = NodeType.Control;
        }
        const tag: NodeData = {
            id: BowtieElementIdProvider.getInstance().getNextNodeId(),
            name: 'New node',
            type: nodeType,
            originType,
            // TODO: uncomment when ratings enabled
            // ratingOptions: getRatingsForType(props.data?.diagramModel, nodeType)
        };
        const newNode = graphComponent.graph.createNode({ tag: tag });
        const nodeLabel = graphComponent.graph.addLabel(newNode, 'New node');
        let source;
        let target;
        if (position === Position.Left && nodeType !== NodeType.Control) {
            source = newNode;
            target = selectedNode || parent;
        } else {
            source = selectedNode || parent;
            target = newNode;
        }
        graphComponent.graph.createEdge(source, target, graphComponent.graph.edgeDefaults.style, {
            id: BowtieElementIdProvider.getInstance().getNextEdgeId(),
            type: EdgeType.DEFAULT,
        });

        morphLayout()
            .then(() => {
                if (props.showControls || nodeType !== NodeType.Control) {
                    selectNode(newNode);
                    void (graphComponent.inputMode as GraphEditorInputMode).editLabel(nodeLabel);
                    edit.commit();
                }
            })
            .catch(() => {});
    };

    /**
     * Initializes removes selected node operation. This is an action bound to the context menu icon and DELETE button.
     * This is first step two step operation as some additional user input is required.
     */
    const onNodeRemovedInitialized = (node: INode) => {
        if (BowtieDiagramUtils.hasConnections(node, BowtieDiagramUtils.getGraph(graphComponent.graph, DiagramPredicates.WITHOUT_SECONDARY_EDGES))) {
            setDeleteDialogOpen(true);
        } else {
            onNodeRemoveFinished(node, true, null).catch(() => {
                // log error.message for future investigations; do not show to user
                snackbarProviderContext.enqueueError(strings('bowtie:diagram.message.removeNodeError'));
            });
        }
    };

    /**
     * Finishes removes selected node operation with additional data.
     * This is second step of two step operation as some additional user input is required.
     */
    const onNodeRemoveFinished = async (node: INode, singleNode: boolean, removeSiblings: boolean | null) => {
        const fullGraph = graphComponent.graph;
        const graph = BowtieDiagramUtils.getGraph(fullGraph, DiagramPredicates.WITHOUT_SECONDARY_EDGES);
        const removeNode = (removedNode: INode) => {
            if (lastCopiedNode?.tag.id === removedNode.tag.id) {
                graphComponent.clipboard.clear();
            }
            graph.remove(removedNode);
        };
        if (singleNode) {
            removeNode(node);
        } else {
            if (removeSiblings) {
                BowtieDiagramUtils.applyForSubtree(node, graph, (item) => removeNode(item));
            } else {
                // remove ALL edges also secondary thus we need full graph in callback.
                BowtieDiagramUtils.applyForSubtree(node, graph, (item) => {
                    if (lastCopiedNode?.tag.id === item.tag.id) {
                        graphComponent.clipboard.clear();
                    }
                    fullGraph
                        .edgesAt(item)
                        .toArray()
                        .forEach((edge) => fullGraph.remove(edge));
                    item.tag = {
                        ...item.tag,
                        originType: NodeType.Blank,
                    };
                });
                removeNode(node);
            }
        }
        setSelectedNode(null);
        setDeleteDialogOpen(false);
        void morphLayout();
    };

    const changeNodeType = (node: Node, type: NodeType) => {
        const edit = graphComponent.graph.beginEdit('Change type', 'Change type');

        node.tag = {
            ...node.tag,
            libraryLink: undefined,
            type: type,
            originType: node.tag.originType ?? node.tag.type,
            rating: undefined,
            ratingOptions: getRatingsForType(props.data?.diagramModel, type),
        };
        graphComponent.invalidate();
        updateNodeLayout(node.labels.get(0), node);
        morphLayout()
            .then(() => edit.commit())
            .catch(() => {});
    };

    const changeTypeHandler = (node: Node, type: NodeType) => {
        if (node.tag) {
            if (node.tag.libraryLink) {
                unlinkLibraryOnNodeTypeChanged(node, type);
            } else {
                changeNodeType(node, type);
            }
        }
        setOpenChangeType(false);
    };

    // Temporarily disabled ratings change so suppress error until enabled
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const onRatingChanged = (node: Node, rating?: RatingModel) => {
        const edit = graphComponent.graph.beginEdit('Change rating', 'Change rating');
        node.tag = { ...node.tag, rating: rating };
        updateNodeLayout(node.labels.get(0), node);
        graphComponent.invalidate();
        morphLayout()
            .then(() => edit.commit())
            .catch(() => {});
    };

    function getRegisterByTypeFromBowtieDefinition(nodeType: NodeType): BowTieRegister | undefined {
        if (definition) {
            switch (nodeType) {
                case NodeType.RiskEvent:
                case NodeType.MainRiskEvent:
                    return definition.riskEventRegister;
                case NodeType.Cause:
                    return definition.riskCauseRegister;
                case NodeType.Impact:
                    return definition.riskImpactRegister;
                case NodeType.Control:
                    return definition.riskControlRegister;
                default:
                    return undefined;
            }
        }
        return undefined;
    }
    function getRegisterDisplayColByTypeFromBowtieDefinition(nodeType: NodeType): string {
        if (definition) {
            switch (nodeType) {
                case NodeType.RiskEvent:
                case NodeType.MainRiskEvent:
                    return riskEventNameCol || '';
                case NodeType.Cause:
                    return riskCauseNameCol || '';
                case NodeType.Impact:
                    return riskImpactNameCol || '';
                case NodeType.Control:
                    return riskControlNameCol || '';
                default:
                    return '';
            }
        }
        return '';
    }

    const onLinkedEntryChanged = async (node: Node | null, link?: IdWithName) => {
        if (!node) {
            return;
        }
        const edit = graphComponent.graph.beginEdit('Change library link', 'Change library link');
        const label = node.labels.get(0);
        const bowtieReg = getRegisterByTypeFromBowtieDefinition(node.tag.type);
        if (bowtieReg && bowtieReg.registerId && link) {
            const resourceEntry = await triggerEntryGet({ regId: bowtieReg.registerId, entryId: link.id }).unwrap();
            const wholeEntry = getEntryTableRow(resourceEntry?.record as RegisterEntryRest);
            node.tag = {
                ...node.tag,
                libraryLink: { id: link.id, name: wholeEntry[getRegisterDisplayColByTypeFromBowtieDefinition(node.tag.type)]?.simpleValue?.[0] || '' },
                name: wholeEntry[getRegisterDisplayColByTypeFromBowtieDefinition(node.tag.type)]?.simpleValue?.[0] || '',
            };
            graphComponent.graph.setLabelText(label, wholeEntry[getRegisterDisplayColByTypeFromBowtieDefinition(node.tag.type)]?.simpleValue?.[0] || ''); //can be replaced by some default name e.g. Empty
        } else {
            node.tag = { ...node.tag, libraryLink: link, name: link?.name || '' };
            graphComponent.graph.setLabelText(label, link?.name || 'Empty');
        }

        updateNodeLayout(label, node);
        morphLayout().then(
            () => edit.commit(),
            () => {},
        );
    };

    const back = useCallback(() => {
        if (location.state?.from) {
            void navigate(location.state?.from);
        } else {
            void navigate(generatePath(BowtiePath.OVERVIEW, { status: 'recent' }));
        }
    }, [location.state?.from, navigate]);

    return (
        <>
            {errorDefinitionDialogOpen ? (
                <AlertDialog
                    visible={errorDefinitionDialogOpen}
                    data={{
                        onClose: back,
                        type: AlertType.Error,
                        title: strings('bowtie:diagram.title.alert'),
                        contentText: strings('bowtie:diagram.message.dontHavePerm'),
                    }}
                />
            ) : (
                <GraphComponentContainer ref={divRef}>
                    {!isLabelEditing && (
                        <NodeContextMenu
                            graphComponent={graphComponent}
                            selected={selectedNode}
                            onCopy={() => executeCommand(ICommand.COPY)}
                            onPaste={() => {
                                executeCommand(ICommand.PASTE);
                            }}
                            // onRatingChanged={onRatingChanged}
                            onRemove={onNodeRemovedInitialized}
                        />
                    )}
                    <ChangeType
                        graphComponent={graphComponent}
                        selected={selectedNode}
                        open={openChangeType}
                        click={changeTypeHandler}
                        showControl={props.showControls}
                    />
                    {!isLabelEditing && (
                        <AddButton
                            graphComponent={graphComponent}
                            nodeStyles={props.styles?.nodeStyle || new Map()}
                            selected={selectedNode}
                            addNode={onNodeAdded}
                            isHidden={(newNodeType: NodeType) => !props.showControls && newNodeType === NodeType.Control}
                            position={Position.Left}
                        />
                    )}
                    {!isLabelEditing && (
                        <AddButton
                            graphComponent={graphComponent}
                            nodeStyles={props.styles?.nodeStyle || new Map()}
                            selected={selectedNode}
                            addNode={onNodeAdded}
                            isHidden={(newNodeType: NodeType) => !props.showControls && newNodeType === NodeType.Control}
                            position={Position.Right}
                        />
                    )}
                    {!isLabelEditing && BOTTOM_ADD_BUTTON_TYPES.includes(selectedNode?.tag?.type) && (
                        <AddButton
                            graphComponent={graphComponent}
                            nodeStyles={props.styles?.nodeStyle || new Map()}
                            selected={selectedNode}
                            addNode={onNodeAdded}
                            isHidden={(newNodeType: NodeType) => !props.showControls && newNodeType === NodeType.Control}
                            position={Position.Bottom}
                        />
                    )}
                    <DeleteNodeDialog
                        visible={deleteDialogOpen}
                        onDelete={() => onNodeRemoveFinished(selectedNode!, false, true)}
                        onDisconnect={() => onNodeRemoveFinished(selectedNode!, false, false)}
                        onClose={() => setDeleteDialogOpen(false)}
                    />
                    {props.libraryLinkEnabled && canEdit && !isLoading && (
                        <NodeAutoCompletePopup
                            oldControlsEnabled={oldControlsEnabled}
                            bowtieDefinition={definition}
                            graphComponent={graphComponent}
                            selected={selectedNode}
                            onSelect={onLinkedEntryChanged}
                        />
                    )}
                </GraphComponentContainer>
            )}
        </>
    );
});

DiagramComponent.displayName = 'DiagramComponent';

export default DiagramComponent;
