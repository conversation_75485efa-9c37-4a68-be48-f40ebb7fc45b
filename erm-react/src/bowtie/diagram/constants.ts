import { IdWithName } from 'app/types';
import { strings } from 'common/utils/i18n';
import { LinkNameChangeOption, NodeType } from 'bowtie/types';

export const NODE_FOOTER_HEIGHT = 28;
export const LABEL_INSET = 12;

export const DIAGRAM_TEST_ID_PREFIX = 'bowtie-diagram';

export const DELETED_LINK: IdWithName = { id: -1, name: '' };

export const LINK_CHANGE_DIALOG_OPTIONS = [
    { value: LinkNameChangeOption.KEEP_LINK, label: strings('bowtie:message.keepLinkUpdateNameOption') },
    { value: LinkNameChangeOption.KEEP_NAME, label: strings('bowtie:message.keepNameRemoveLinkOption') },
];

export const BOTTOM_ADD_BUTTON_TYPES = [NodeType.MainRiskEvent, NodeType.Control];

export const DEFAULT_HORIZONTAL_LAYOUT_SPACING = 45;
