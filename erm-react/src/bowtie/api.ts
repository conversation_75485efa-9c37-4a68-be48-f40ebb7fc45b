import { PagingResult } from 'common/api/types';
import { BowTieDiagram, BowTieStatus, BowTieStyle } from 'bowtie/types';
import { getBowTieSearchRequestParams } from './utils';
import { RegisterBowTieFieldRest, RegisterBowTieRest, RegisterDataBowTieRest } from 'register/types';
import authenticatedClient from '../common/api/utils/api';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { BowTieDefinition } from '../api/generated/types';

export const createDiagram = async (name: string): Promise<BowTieDiagram> => {
    return authenticatedClient.post('/v1/api/bowties', name, {
        headers: {
            'Content-Type': 'text/plain',
        },
    });
};

export const updateDiagram = async (id: number, diagram: BowTieDiagram): Promise<BowTieDiagram> => {
    return authenticatedClient.put(`/v1/api/bowties/${id}`, diagram);
};

export const renameDiagram = async (id: number, name: string): Promise<BowTieDiagram> => {
    return authenticatedClient.put(`/v1/api/bowties/${id}/rename`, name);
};

export const verifyUserBUs = async (businessUnits: number[]): Promise<boolean> => {
    return authenticatedClient.put('/v1/api/bowties/verify', businessUnits);
};
export const publishDiagram = async (id: number, businessUnits: number[]): Promise<void> => {
    return authenticatedClient.put(`/v1/api/bowties/${id}/publish`, businessUnits);
};

export const unPublishDiagram = async (id: number): Promise<void> => {
    return authenticatedClient.put(`/v1/api/bowties/${id}/unpublish`);
};

export const getRecentDiagrams = async (abortController: AbortController): Promise<BowTieDiagram[]> => {
    return authenticatedClient.get('/v1/api/bowties/recent', {
        signal: abortController.signal,
    });
};

export const getDiagrams = async (
    status: BowTieStatus | string,
    params: SearchRequestParams,
    searchField: string,
    searchValue: string,
    abortController?: AbortController,
): Promise<PagingResult<BowTieDiagram>> => {
    const bowtieParams = getBowTieSearchRequestParams(params, searchField, searchValue);
    return authenticatedClient.get(`/v1/api/bowties/${String(status)}`, {
        params: bowtieParams,
        signal: abortController?.signal,
    });
};

export const getDiagram = async (id: number, withDiagram = true): Promise<BowTieDiagram> => {
    return authenticatedClient.get(`/v1/api/bowties/${id}`, {
        params: withDiagram ? { withDiagram: true } : undefined,
    });
};

export const getDiagramCopy = async (id: number, name: string): Promise<BowTieDiagram> => {
    return authenticatedClient.put(`/v1/api/bowties/${id}/copy`, name, {
        headers: { 'Content-Type': 'text/plain' },
    });
};

export const deleteDiagram = async (id: number): Promise<void> => {
    return authenticatedClient.delete(`/v1/api/bowties/${id}`);
};

export const getDiagramStyle = async (): Promise<BowTieStyle> => {
    return authenticatedClient.get('/v1/api/bowties/style');
};

export const getBowTieRegisterEntries = async (diagramId: number): Promise<RegisterDataBowTieRest[]> => {
    return authenticatedClient.get(`/v1/api/bowties/${diagramId}/entries`);
};

export const getRegisterBowTieFields = async (): Promise<RegisterBowTieRest[]> => {
    return authenticatedClient.get('/v1/api/registers/bowties');
};

export const linkEntryToBowTie = async (bowTieId: number, regId: number, entryId: number, field: RegisterBowTieFieldRest): Promise<void> => {
    return authenticatedClient.put(`/v1/api/bowties/${bowTieId}/entries/${entryId}/link`, field, {
        params: { regId },
    });
};

export const unlinkEntryFromBowTie = async (bowTieId: number, regId: number, entryId: number, field: RegisterBowTieFieldRest): Promise<void> => {
    return authenticatedClient.put(`/v1/api/bowties/${bowTieId}/entries/${entryId}/unlink`, field, {
        params: { regId },
    });
};

export const getDefinition = async (): Promise<BowTieDefinition> => {
    return authenticatedClient.get('/v1/api/bowties/definition', { params: { extended: true } });
};
