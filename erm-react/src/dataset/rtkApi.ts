import { baseInjectedApi as datasetApi } from 'api/generated/dataset';

export const enhancedDataSetApi = datasetApi.enhanceEndpoints({
    addTagTypes: [],
    endpoints: {},
});

export const dataSetApiWithTags = enhancedDataSetApi.injectEndpoints({
    endpoints: (build) => ({
        getExportContentCsv: build.query({
            queryFn: async (exportId, _api, _extraOptions, baseQuery) => {
                const response = await baseQuery({
                    url: '/v1/api/dataset/export/content',
                    params: {
                        exportid: exportId,
                    },
                    responseHandler: async (response) => {
                        const text = await response.text();
                        return new Blob([text], { type: 'text/csv' });
                    },
                    cache: 'no-cache',
                });

                if (response.error) {
                    return { error: response.error };
                }

                return { data: response.data };
            },
        }),
        getTemplateFile: build.query({
            queryFn: async ({ regId, suggestedFileName }, _api, _extraOptions, baseQuery) => {
                const response = await baseQuery({
                    url: `/v1/api/dataset/${regId}/template`,
                    params: {
                        suggestedFileName,
                    },
                    responseHandler: async (response) => {
                        const text = await response.text();
                        return new Blob([text], { type: 'text/csv' });
                    },
                    cache: 'no-cache',
                });

                if (response.error) {
                    return { error: response.error };
                }

                return { data: response.data };
            },
        }),
    }),
});

export const { useLazyGetExportContentCsvQuery, useLazyGetExportProgressGetQuery, useLazyGetImportProgressGetQuery, useLazyGetTemplateFileQuery } =
    dataSetApiWithTags;
