export const ERM_CONTEXT = ProtechtDictionary.context;
export const REACT_ERM_PATH = '/worms/client/react';
export const REACT_RESOURCES_PATH = '/worms/react';
export const API_BASE_URL = `${ERM_CONTEXT}/rest`;
export const WORMS_CLIENT_BASE_URL = `${ERM_CONTEXT}/worms/client`;
export const WORMS_CLIENT_APP_BASE_URL = `${ERM_CONTEXT}/worms/client/app`;
export const WORMS_CLIENT_PATH = '/worms/client';

export const GWT_WIDGET_URL = `${ERM_CONTEXT}/worms/client/app/widget.html`;
export const ANONYMOUS_GWT_WIDGET_URL = `${ERM_CONTEXT}/worms/client/app/anonymousWidget.html`;
export const GWT_REACT_URL = `${ERM_CONTEXT}/worms/client/app/react`;
export const LOGOUT_URL = `${ProtechtDictionary.siteUrl}/logout`;
export const API_DOCUMENATION_URL = ProtechtDictionary.apiDocUrl;
export const HELP_URL = ProtechtDictionary.helpUrl;

// TODO: get these urls from Protecht Dictionary
export const PROTECHT_ACADEMY_URL = 'https://www.protechtgroup.com/en-gb/academy';
export const PROTECHT_COMMUNITY_PORTAL_URL = 'https://community.protechtgroup.com/s/';

export const APP_LOGO_MAIN_URL = `${WORMS_CLIENT_BASE_URL}/styles/style/logo_left`;
export const APP_LOGO_LOGIN_URL = `${WORMS_CLIENT_BASE_URL}/styles/style/logo_login`;

export const isProduction = process.env.NODE_ENV === 'production';
export const isReactUiClient = ProtechtDictionary.reactUiClient === 'true';
export const isLightMenu = ProtechtDictionary.useLightMenu === 'true';
export const isNewMyComplianceEntry = ProtechtDictionary.newMyComplianceEntry === 'true';

export const REACT_RESOURCES_URL = isProduction ? `${ProtechtDictionary.siteUrl}${REACT_RESOURCES_PATH}` : '';
export const SHOW_ERROR_DETAILS = isProduction;
export const NAVBAR_DISABLE_UNSUPPORTED_ITEMS = isProduction ? false : process.env.NAVBAR_DISABLE_UNSUPPORTED_ITEMS !== 'false';

let reactRoot: HTMLElement | null = null;
export const setReactRoot = (newReactRoot: HTMLElement) => {
    reactRoot = newReactRoot;
};
export const getReactRoot = (): HTMLElement | null => {
    return reactRoot;
};

export const yFilesLicense = isProduction
    ? {
          company: 'Protecht Group Services Pty Ltd',
          contact: 'Rhys Johnston',
          date: '06/07/2021',
          distribution: true,
          domains: ['*'],
          email: '<EMAIL>',
          fileSystemAllowed: true,
          licensefileversion: '1.1',
          localhost: true,
          oobAllowed: true,
          package: 'complete',
          product: 'yFiles for HTML',
          projectname: 'Protecht.ERM',
          subscription: '06/06/2022',
          type: 'project',
          version: '2.3',
          key: '616eb2fe8dcbc3de99d4b6b4bc61f9ac756d4d94',
      }
    : {
          company: 'Protecht Group Services Pty Ltd',
          contact: 'Rhys Johnston',
          date: '06/07/2021',
          distribution: false,
          domains: ['*'],
          email: '<EMAIL>',
          fileSystemAllowed: true,
          licensefileversion: '1.1',
          localhost: true,
          oobAllowed: true,
          package: 'complete',
          product: 'yFiles for HTML',
          projectname: 'Protecht.ERM',
          subscription: '06/06/2022',
          type: 'project',
          version: '2.3',
          watermark: 'yFiles HTML Development License',
          key: '284194662c555b70c5075d1254c448bb7472f28e',
      };
