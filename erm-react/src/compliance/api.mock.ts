import { ComplianceEntryRest, ComplianceResponseRest } from 'api/generated/types';
import { ControlType } from './types';

export const ControlResponseItems = [
    {
        id: 1,
        name: 'Yes',
        bgColour: '#CCFF99',
        fgColour: '#000000',
        forceComment: false,
        forceAction: false,
        iconId: 100180,
    },
    {
        id: 2,
        name: 'No',
        bgColour: '#ff0033',
        fgColour: '#ffffff',
        forceComment: true,
        forceAction: false,
        iconId: 100181,
    },
];

export const QuestionResponseItems = [
    {
        id: 1,
        name: 'Yes',
        bgColour: '#CCFF99',
        fgColour: '#000000',
        forceComment: false,
        forceAction: false,
        iconId: 100180,
    },
    {
        id: 2,
        name: 'No',
        bgColour: '#ff0033',
        fgColour: '#ffffff',
        forceComment: true,
        forceAction: false,
        iconId: 100181,
    },
    {
        id: 3,
        name: 'Maybe',
        bgColour: '#00FFFF',
        fgColour: '#000000',
        forceComment: true,
        forceAction: false,
    },
];

export const responses: ComplianceResponseRest[] = [
    {
        id: 1000,
        controlType: ControlType.CONTROL,
        complianceResponseItems: ControlResponseItems,
    },
    {
        id: 1001,
        controlType: ControlType.QUESTION,
        complianceResponseItems: QuestionResponseItems,
    },
];

export const entries: ComplianceEntryRest[] = [
    { id: 123, control: { name: 'First entry' }, controlType: ControlType.CONTROL },
    { id: 456, control: { name: 'Second entry' }, controlType: ControlType.CONTROL },
    { id: 789, control: { name: 'Third entry' }, controlType: ControlType.QUESTION },
];
