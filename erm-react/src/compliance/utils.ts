import { ComplianceResponseItem, ComplianceResponseRest } from 'api/generated/types';
import { ControlType } from './types';

export const getResponseItemsByControlType = (responses: ComplianceResponseRest[], controlType: ControlType): ComplianceResponseItem[] => {
    return responses.find((response) => response.controlType === controlType)?.complianceResponseItems || [];
};

export const responseHasForcedComments = (responseItems: ComplianceResponseItem[], responseId: number): boolean => {
    return responseItems.find((item) => item.id === responseId)?.forceComment || false;
};
