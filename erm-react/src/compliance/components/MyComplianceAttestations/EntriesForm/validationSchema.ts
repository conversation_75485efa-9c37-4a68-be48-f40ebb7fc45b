import * as Yup from 'yup';
import { ComplianceResponseRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import { getResponseItemsByControlType, responseHasForcedComments } from 'compliance/utils';
import { ControlType } from 'compliance/types';

export const validationSchema = Yup.object({
    entries: Yup.array().of(
        Yup.object().shape({
            response: Yup.string().required(strings('common:validators.requiredSimple')),
            controlType: Yup.string().oneOf(Object.values(ControlType)),
            comments: Yup.string().when(['response', 'controlType', '$responses'], {
                is: (response: string, controlType: string, responses: ComplianceResponseRest[]) => {
                    const responseItems = getResponseItemsByControlType(responses, controlType as ControlType);
                    const hasForcedComments = response !== '' ? responseHasForcedComments(responseItems, Number(response)) : false;
                    return hasForcedComments;
                },
                then: (schema) => schema.required(strings('common:validators.requiredSimple')),
            }),
            attachments: Yup.array().of(Yup.mixed()),
        }),
    ),
    confirmation: Yup.boolean().test('required', strings('common:validators.requiredSimple'), (curVal: boolean) => {
        return curVal;
    }),
});
