// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Layout shows entries component when responses and entries are loaded 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiBox-root css-1w0cxss"
    >
      <div
        class="MuiBox-root css-138ieew"
      >
        <div
          class="MuiBox-root css-1a99p2u"
        >
          <div
            class="MuiBox-root css-l5kdvk"
          >
            <div
              class="MuiBox-root css-1a99p2u"
            >
              <h1
                class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-2bl176-MuiTypography-root"
                data-testid="myCompliance-pageTitle"
              >
                My Compliance Attestations
              </h1>
              <div
                class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1t0k98z-MuiTypography-root"
                data-testid="myCompliance-totalEntries"
              >
                6 in total
              </div>
            </div>
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
              data-testid="myCompliance-buttonSubmit"
              disabled=""
              tabindex="-1"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-1w5ebv4-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-259gsz-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-1rhcntm-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-nw10hm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-13fjuhh-MuiGrid-root"
                data-testid="myCompliance-sectionEntries"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-jrw6kt-MuiGrid-root"
                >
                  <h5
                    class="MuiTypography-root MuiTypography-h5 css-1795yd1-MuiTypography-root"
                    data-testid="section-title"
                  >
                    Compliance items to be completed
                  </h5>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-1p0arqy-MuiGrid-root"
                  data-testid="section-content"
                >
                  <div
                    data-testid="virtuoso-scroller"
                    data-virtuoso-scroller="true"
                    style="height: 100%; outline: none; overflow-y: auto; position: relative;"
                    tabindex="0"
                  >
                    <div
                      data-viewport-type="element"
                      style="width: 100%; height: 100%; position: absolute; top: 0px;"
                    >
                      <div
                        data-testid="virtuoso-item-list"
                        style="box-sizing: border-box; margin-top: 0px; padding-top: 0px; padding-bottom: 0px;"
                      >
                        <div
                          data-index="0"
                          data-item-index="0"
                          data-known-size="150"
                        >
                          <div
                            class="MuiBox-root css-0"
                            data-testid="myCompliance-entryItem"
                            style="padding-bottom: 32px; padding-top: 0px; border-bottom: 1px solid #DCDCDC;"
                          >
                            <div
                              class="MuiBox-root css-0"
                              data-testid="myCompliance-entryResponse"
                            >
                              <div
                                class="MuiBox-root css-43yvyf"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                                      >
                                        <label
                                          for="entries.0.response"
                                        >
                                          1 - First entry
                                        </label>
                                      </p>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                                    role="radiogroup"
                                  >
                                    <label
                                      class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                                    >
                                      <span
                                        class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                                        shape="square"
                                      >
                                        <input
                                          class="PrivateSwitchBase-input css-1m9pwf3"
                                          name="entries.0.response"
                                          type="radio"
                                          value="1"
                                        />
                                        <svg
                                          aria-hidden="true"
                                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                                          data-icon="square-button-unchecked"
                                          data-testid="SquareRadioButtonUncheckedIcon"
                                          fill="currentColor"
                                          focusable="false"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          width="24"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <rect
                                            fill-opacity="0"
                                            height="16"
                                            rx="3"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            width="16"
                                            x="4"
                                            y="4"
                                          />
                                        </svg>
                                      </span>
                                      <span
                                        class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                                      >
                                        <span
                                          style="min-width: auto;"
                                        >
                                          Yes
                                        </span>
                                      </span>
                                    </label>
                                    <label
                                      class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                                    >
                                      <span
                                        class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                                        shape="square"
                                      >
                                        <input
                                          class="PrivateSwitchBase-input css-1m9pwf3"
                                          name="entries.0.response"
                                          type="radio"
                                          value="2"
                                        />
                                        <svg
                                          aria-hidden="true"
                                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                                          data-icon="square-button-unchecked"
                                          data-testid="SquareRadioButtonUncheckedIcon"
                                          fill="currentColor"
                                          focusable="false"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          width="24"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <rect
                                            fill-opacity="0"
                                            height="16"
                                            rx="3"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            width="16"
                                            x="4"
                                            y="4"
                                          />
                                        </svg>
                                      </span>
                                      <span
                                        class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                                      >
                                        <span
                                          style="min-width: auto;"
                                        >
                                          No
                                        </span>
                                      </span>
                                    </label>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          data-index="1"
                          data-item-index="1"
                          data-known-size="150"
                        >
                          <div
                            class="MuiBox-root css-0"
                            data-testid="myCompliance-entryItem"
                            style="padding-bottom: 32px; padding-top: 29px; border-bottom: 1px solid #DCDCDC;"
                          >
                            <div
                              class="MuiBox-root css-0"
                              data-testid="myCompliance-entryResponse"
                            >
                              <div
                                class="MuiBox-root css-43yvyf"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                                      >
                                        <label
                                          for="entries.1.response"
                                        >
                                          2 - Second entry
                                        </label>
                                      </p>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                                    role="radiogroup"
                                  >
                                    <label
                                      class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                                    >
                                      <span
                                        class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                                        shape="square"
                                      >
                                        <input
                                          class="PrivateSwitchBase-input css-1m9pwf3"
                                          name="entries.1.response"
                                          type="radio"
                                          value="1"
                                        />
                                        <svg
                                          aria-hidden="true"
                                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                                          data-icon="square-button-unchecked"
                                          data-testid="SquareRadioButtonUncheckedIcon"
                                          fill="currentColor"
                                          focusable="false"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          width="24"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <rect
                                            fill-opacity="0"
                                            height="16"
                                            rx="3"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            width="16"
                                            x="4"
                                            y="4"
                                          />
                                        </svg>
                                      </span>
                                      <span
                                        class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                                      >
                                        <span
                                          style="min-width: auto;"
                                        >
                                          Yes
                                        </span>
                                      </span>
                                    </label>
                                    <label
                                      class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                                    >
                                      <span
                                        class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                                        shape="square"
                                      >
                                        <input
                                          class="PrivateSwitchBase-input css-1m9pwf3"
                                          name="entries.1.response"
                                          type="radio"
                                          value="2"
                                        />
                                        <svg
                                          aria-hidden="true"
                                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                                          data-icon="square-button-unchecked"
                                          data-testid="SquareRadioButtonUncheckedIcon"
                                          fill="currentColor"
                                          focusable="false"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          width="24"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <rect
                                            fill-opacity="0"
                                            height="16"
                                            rx="3"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            width="16"
                                            x="4"
                                            y="4"
                                          />
                                        </svg>
                                      </span>
                                      <span
                                        class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                                      >
                                        <span
                                          style="min-width: auto;"
                                        >
                                          No
                                        </span>
                                      </span>
                                    </label>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          data-index="2"
                          data-item-index="2"
                          data-known-size="150"
                        >
                          <div
                            class="MuiBox-root css-0"
                            data-testid="myCompliance-entryItem"
                            style="padding-bottom: 0px; padding-top: 29px; border-bottom: 0;"
                          >
                            <div
                              class="MuiBox-root css-0"
                              data-testid="myCompliance-entryResponse"
                            >
                              <div
                                class="MuiBox-root css-43yvyf"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                                      >
                                        <label
                                          for="entries.2.response"
                                        >
                                          3 - Third entry
                                        </label>
                                      </p>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                                    role="radiogroup"
                                  >
                                    <label
                                      class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                                    >
                                      <span
                                        class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                                        shape="square"
                                      >
                                        <input
                                          class="PrivateSwitchBase-input css-1m9pwf3"
                                          name="entries.2.response"
                                          type="radio"
                                          value="1"
                                        />
                                        <svg
                                          aria-hidden="true"
                                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                                          data-icon="square-button-unchecked"
                                          data-testid="SquareRadioButtonUncheckedIcon"
                                          fill="currentColor"
                                          focusable="false"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          width="24"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <rect
                                            fill-opacity="0"
                                            height="16"
                                            rx="3"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            width="16"
                                            x="4"
                                            y="4"
                                          />
                                        </svg>
                                      </span>
                                      <span
                                        class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                                      >
                                        <span
                                          style="min-width: auto;"
                                        >
                                          Yes
                                        </span>
                                      </span>
                                    </label>
                                    <label
                                      class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                                    >
                                      <span
                                        class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                                        shape="square"
                                      >
                                        <input
                                          class="PrivateSwitchBase-input css-1m9pwf3"
                                          name="entries.2.response"
                                          type="radio"
                                          value="2"
                                        />
                                        <svg
                                          aria-hidden="true"
                                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                                          data-icon="square-button-unchecked"
                                          data-testid="SquareRadioButtonUncheckedIcon"
                                          fill="currentColor"
                                          focusable="false"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          width="24"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <rect
                                            fill-opacity="0"
                                            height="16"
                                            rx="3"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            width="16"
                                            x="4"
                                            y="4"
                                          />
                                        </svg>
                                      </span>
                                      <span
                                        class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                                      >
                                        <span
                                          style="min-width: auto;"
                                        >
                                          No
                                        </span>
                                      </span>
                                    </label>
                                    <label
                                      class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                                    >
                                      <span
                                        class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                                        shape="square"
                                      >
                                        <input
                                          class="PrivateSwitchBase-input css-1m9pwf3"
                                          name="entries.2.response"
                                          type="radio"
                                          value="3"
                                        />
                                        <svg
                                          aria-hidden="true"
                                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                                          data-icon="square-button-unchecked"
                                          data-testid="SquareRadioButtonUncheckedIcon"
                                          fill="currentColor"
                                          focusable="false"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          width="24"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <rect
                                            fill-opacity="0"
                                            height="16"
                                            rx="3"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            width="16"
                                            x="4"
                                            y="4"
                                          />
                                        </svg>
                                      </span>
                                      <span
                                        class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                                      >
                                        <span
                                          style="min-width: auto;"
                                        >
                                          Maybe
                                        </span>
                                      </span>
                                    </label>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-1jo2507-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-1v5stvd-MuiGrid-root"
                data-testid="myCompliance-sectionConfirmation"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-jrw6kt-MuiGrid-root"
                >
                  <h5
                    class="MuiTypography-root MuiTypography-h5 css-1795yd1-MuiTypography-root"
                    data-testid="section-title"
                  >
                    Confirm and then Submit
                  </h5>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-1p0arqy-MuiGrid-root"
                  data-testid="section-content"
                >
                  <div
                    class="MuiBox-root css-iwu3eg"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                          >
                            <label
                              for="confirmation"
                            >
                              Personal Confirmation
                            </label>
                          </p>
                        </div>
                      </div>
                      <label
                        class="MuiFormControlLabel-root Mui-disabled MuiFormControlLabel-labelPlacementEnd css-rpxqo-MuiFormControlLabel-root"
                      >
                        <span
                          aria-disabled="true"
                          class="MuiButtonBase-root MuiCheckbox-root MuiCheckbox-colorPrimary MuiCheckbox-sizeMedium Mui-disabled PrivateSwitchBase-root MuiCheckbox-root MuiCheckbox-colorPrimary MuiCheckbox-sizeMedium Mui-disabled MuiCheckbox-root MuiCheckbox-colorPrimary MuiCheckbox-sizeMedium css-d14e3w-MuiButtonBase-root-MuiCheckbox-root"
                          tabindex="-1"
                        >
                          <input
                            aria-label="Personal Confirmation"
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            data-indeterminate="false"
                            disabled=""
                            name="confirmation"
                            type="checkbox"
                            value="false"
                          />
                          <svg
                            data-icon="checkbox-unchecked"
                            data-testid="CheckBoxIcon"
                            fill="none"
                            font-size="medium"
                            height="18"
                            viewBox="0 0 18 18"
                            width="18"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <mask
                              fill="white"
                              id="checkbox-unchecked-mask"
                            >
                              <rect
                                height="16"
                                rx="1.5"
                                width="16"
                                x="1"
                                y="1"
                              />
                            </mask>
                            <rect
                              height="16"
                              mask="url(#checkbox-unchecked-mask)"
                              rx="1.5"
                              stroke="currentColor"
                              stroke-width="4"
                              width="16"
                              x="1"
                              y="1"
                            />
                            <rect
                              class="border"
                              height="16"
                              rx="1.5"
                              stroke="currentColor"
                              stroke-width="0"
                              width="16"
                              x="1"
                              y="1"
                            />
                          </svg>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label Mui-disabled css-e8ctdu-MuiTypography-root"
                        >
                          By submitting this form, I confirm that all answers are true and correct
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
             
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
