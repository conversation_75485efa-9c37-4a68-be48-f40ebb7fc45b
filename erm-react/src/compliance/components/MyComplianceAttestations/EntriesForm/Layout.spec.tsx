import React from 'react';
import { RouterProvider, createMemoryRouter } from 'react-router';
import { fireEvent, waitFor, within } from '@testing-library/react';
import { VirtuosoMockContext } from 'react-virtuoso';
import { createDataTransferWithFiles, createFile, render, screen } from 'test/utils';
import { strings } from 'common/utils/i18n';
import Layout from 'compliance/components/MyComplianceAttestations/EntriesForm/Layout';
import * as complianceApi from 'compliance/rtkApi';
import { responses, entries } from 'compliance/api.mock';
import { CompliancePath } from 'compliance/routes';
import { UserEvent } from '@testing-library/user-event';
import { PaginRestResultComplianceEntryRest, PaginRestResultComplianceResponseRest } from 'api/generated/types';

// increased default timeout for the whole suite due to timeouts on CI
jest.setTimeout(40000);

global.URL.createObjectURL = jest.fn();

const responsesMockData: PaginRestResultComplianceResponseRest = {
    totalCount: responses.length,
    records: responses,
};

const entriesMockData: PaginRestResultComplianceEntryRest = {
    totalCount: 6,
    records: entries,
};

const mockNavigate = jest.fn();

jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    useNavigate: () => mockNavigate,
}));

jest.mock('app/rtkApi', () => {
    const actualRtkApi = jest.requireActual('app/rtkApi');
    return {
        ...actualRtkApi,
        useGetSystemConfigurationQuery: () => ({
            data: {
                max_attach_size_bytes: 10,
            },
        }),
    };
});

jest.mock('common/api/files', () => {
    const actualRtkApi = jest.requireActual('common/api/files');

    return {
        ...actualRtkApi,
        useFersGetAllowedExtensionsUsingGetQuery: () => ({ data: [], isLoading: false, isSuccess: true, isError: false }),
    };
});

jest.mock('compliance/rtkApi', () => ({
    __esModule: true,
    ...jest.requireActual('compliance/rtkApi'),
}));

const mockTriggerUpload = jest.fn().mockImplementation((formData) => {
    let mockUuid = '';
    formData.forEach((value, key) => {
        if (key === 'file') {
            mockUuid = `uuid-${value.name}`;
        }
    });
    return { unwrap: () => Promise.resolve(mockUuid) };
});
jest.mock('common/api/attachments', () => {
    const actualRtkApi = jest.requireActual('common/api/attachments');
    return {
        ...actualRtkApi,
        useUploadTempAttachment1Mutation: () => [mockTriggerUpload],
    };
});

const setup = (viewportHeight = 450) => {
    const routes = [
        {
            path: CompliancePath.MY_COMPLIANCE,
            element: (
                <VirtuosoMockContext.Provider value={{ viewportHeight, itemHeight: 150 }}>
                    <Layout />
                </VirtuosoMockContext.Provider>
            ),
        },
    ];

    const router = createMemoryRouter(routes, {
        initialEntries: ['/compliance/my'],
    });

    return render(<RouterProvider router={router} />);
};

const addResponse = async (user: UserEvent, entry: HTMLElement, response: string, comments = '', files: File[] = []) => {
    const checkbox = within(entry).getByLabelText(response);
    await user.click(checkbox);
    expect(checkbox).toBeChecked();

    if (comments) {
        const commentsField = within(await within(entry).findByTestId('myCompliance-entryComments'));
        await user.type(commentsField.getByLabelText('Comments'), comments);
    }

    if (files.length > 0) {
        const event = createDataTransferWithFiles(files);
        const attachmentsField = within(await within(entry).findByTestId('myCompliance-entryAttachments'));
        const dropzone = attachmentsField.getByRole('presentation');
        fireEvent.drop(dropzone, event);

        await waitFor(() => {
            expect(within(dropzone).getAllByTestId('LinksList-Item')).toHaveLength(files.length);
        });
    }
};

const fillForm = async (user: UserEvent) => {
    const files1 = [createFile('file1.pdf', 1024, 'application/pdf')];
    const files2 = [createFile('file2.pdf', 1024, 'application/pdf'), createFile('file3.pdf', 1024, 'application/pdf')];
    const confirmCheckbox = screen.getByLabelText(strings('compliance:label.personalConfirmationDescription'));
    const entries = screen.getAllByTestId('myCompliance-entryItem');

    await waitFor(() => {
        expect(confirmCheckbox).toBeDisabled();
    });

    // select Yes for the first entry
    await addResponse(user, entries[0], 'Yes');

    // select No for the second entry, fill in comment and add attachment and change back to Yes
    await addResponse(user, entries[1], 'No', 'Some not used comment', files1);
    await user.click(within(entries[1]).getByLabelText('Yes'));

    // select No for the third entry, fill in comment and add attachment
    await addResponse(user, entries[2], 'No', 'Some test comment', files2);

    await waitFor(() => {
        expect(confirmCheckbox).toBeEnabled();
    });

    await user.click(confirmCheckbox);
    expect(screen.getByTestId('myCompliance-buttonSubmit')).toBeEnabled();
};

describe('Layout', () => {
    beforeEach(() => {
        jest.spyOn(complianceApi, 'useCrrsGetComplianceResponsesUsingGetQuery').mockReturnValue({
            data: responsesMockData,
            isLoading: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        jest.spyOn(complianceApi, 'useCersGetMyComplianceEntriesUsingGetQuery').mockReturnValue({
            data: entriesMockData,
            isLoading: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('shows info box with correct error when error for responses occurs', async () => {
        jest.spyOn(complianceApi, 'useCrrsGetComplianceResponsesUsingGetQuery').mockReturnValue({
            data: undefined,
            isLoading: false,
            isSuccess: false,
            isError: true,
            refetch: jest.fn(),
        });

        setup();
        await waitFor(() => {
            expect(screen.getByTestId('infoBox')).toBeInTheDocument();
        });
        expect(screen.getByTestId('infoPage-pageTitle')).toHaveTextContent(strings('compliance:title.myComplianceAttestations'));
        expect(screen.getByTestId('infoBox-title')).toHaveTextContent(strings('compliance:message.error'));
        expect(screen.getByTestId('infoBox-message')).toHaveTextContent(strings('compliance:message.tryAgain'));
        expect(screen.queryByTestId('myCompliance-sectionEntries')).not.toBeInTheDocument();
        expect(screen.queryByTestId('myCompliance-sectionConfirmation')).not.toBeInTheDocument();
        expect(screen.queryByTestId('myCompliance-buttonSubmit')).not.toBeInTheDocument();
    });

    it('shows info box with correct error when error for entries occurs', async () => {
        jest.spyOn(complianceApi, 'useCersGetMyComplianceEntriesUsingGetQuery').mockReturnValue({
            data: undefined,
            isLoading: false,
            isSuccess: false,
            isError: true,
            refetch: jest.fn(),
        });

        setup();
        await waitFor(() => {
            expect(screen.getByTestId('infoBox')).toBeInTheDocument();
        });
        expect(screen.getByTestId('infoPage-pageTitle')).toHaveTextContent(strings('compliance:title.myComplianceAttestations'));
        expect(screen.getByTestId('infoBox-title')).toHaveTextContent(strings('compliance:message.error'));
        expect(screen.getByTestId('infoBox-message')).toHaveTextContent(strings('compliance:message.tryAgain'));
        expect(screen.queryByTestId('myCompliance-sectionEntries')).not.toBeInTheDocument();
        expect(screen.queryByTestId('myCompliance-sectionConfirmation')).not.toBeInTheDocument();
        expect(screen.queryByTestId('myCompliance-buttonSubmit')).not.toBeInTheDocument();
    });

    it('redirects to confirmation page when no entries are available', async () => {
        jest.spyOn(complianceApi, 'useCersGetMyComplianceEntriesUsingGetQuery').mockReturnValue({
            data: { totalCount: 0, records: [] },
            isLoading: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        setup();
        await waitFor(() => {
            expect(mockNavigate).toHaveBeenCalledWith(CompliancePath.MY_COMPLIANCE_CONFIRMATION);
        });
    });

    it('shows loader while loading entries', async () => {
        jest.spyOn(complianceApi, 'useCersGetMyComplianceEntriesUsingGetQuery').mockReturnValue({
            data: undefined,
            isLoading: true,
            isSuccess: false,
            isError: false,
            refetch: jest.fn(),
        });

        setup();
        await waitFor(() => {
            expect(screen.getByText('Loading...')).toBeInTheDocument();
        });
    });

    it('shows loader while loading responses', async () => {
        jest.spyOn(complianceApi, 'useCrrsGetComplianceResponsesUsingGetQuery').mockReturnValue({
            data: undefined,
            isLoading: true,
            isSuccess: false,
            isError: false,
            refetch: jest.fn(),
        });

        setup();
        await waitFor(() => {
            expect(screen.getByText('Loading...')).toBeInTheDocument();
        });
    });

    it('shows entries component when responses and entries are loaded', async () => {
        const { container } = setup();
        const checkbox = screen.getByLabelText(strings('compliance:label.personalConfirmationDescription'));
        expect(complianceApi.useCersGetMyComplianceEntriesUsingGetQuery).toHaveBeenCalled();
        expect(complianceApi.useCrrsGetComplianceResponsesUsingGetQuery).toHaveBeenCalled();
        await waitFor(() => {
            expect(screen.getByTestId('myCompliance-sectionEntries')).toBeInTheDocument();
        });
        await waitFor(() => {
            expect(checkbox).toBeDisabled();
        });
        expect(container).toMatchSnapshot();
    });

    it('renders header with total count', async () => {
        setup();
        await waitFor(() => {
            expect(screen.getByTestId('myCompliance-totalEntries')).toHaveTextContent('6 in total');
        });
    });

    it('renders disabled confirmation checkbox and submit button when form not filled', async () => {
        setup();
        const checkbox = screen.getByLabelText(strings('compliance:label.personalConfirmationDescription'));
        await waitFor(() => {
            expect(checkbox).toBeDisabled();
        });
        expect(checkbox).not.toBeChecked();
        expect(screen.getByTestId('myCompliance-buttonSubmit')).toBeDisabled();
    });

    it('renders enabled confirmation checkbox when form filled', async () => {
        const { user } = setup();
        const checkbox = screen.getByLabelText(strings('compliance:label.personalConfirmationDescription'));
        const answersToSelect = screen.getAllByLabelText('Yes');
        await Promise.all(
            answersToSelect.map(async (item) => {
                await user.click(item);
            }),
        );
        answersToSelect.forEach((item) => {
            expect(item).toBeChecked();
        });

        await waitFor(() => {
            expect(checkbox).toBeEnabled();
        });
        expect(checkbox).not.toBeChecked();
        expect(screen.getByTestId('myCompliance-buttonSubmit')).toBeDisabled();
    });

    it('renders enabled submit button when form filled and confirmation checked', async () => {
        const { user } = setup();
        const checkbox = screen.getByLabelText(strings('compliance:label.personalConfirmationDescription'));
        const answersToSelect = screen.getAllByLabelText('Yes');
        await Promise.all(
            answersToSelect.map(async (item) => {
                await user.click(item);
            }),
        );
        await user.click(checkbox);
        expect(screen.getByTestId('myCompliance-buttonSubmit')).toBeEnabled();
    });

    it('shows loader when submitting the form', async () => {
        const updateMock = jest.fn().mockReturnValue({ unwrap: () => Promise.resolve() });
        jest.spyOn(complianceApi, 'useCersUpdateEntriesBulkUsingPutMutation').mockReturnValue([
            updateMock,
            {
                isLoading: true,
                reset: jest.fn(),
            },
        ]);

        const { user } = setup();
        await fillForm(user);
        await user.click(screen.getByTestId('myCompliance-buttonSubmit'));

        expect(screen.getByText('Saving entries...')).toBeInTheDocument();
    });

    it('calls update entries mutation with correct payload when the form is submitted', async () => {
        const updateMock = jest.fn().mockReturnValue({ unwrap: () => Promise.resolve() });
        jest.spyOn(complianceApi, 'useCersUpdateEntriesBulkUsingPutMutation').mockReturnValue([
            updateMock,
            {
                isLoading: false,
                isSuccess: true,
                reset: jest.fn(),
            },
        ]);

        const { user } = setup();
        await fillForm(user);

        await user.click(screen.getByTestId('myCompliance-buttonSubmit'));

        expect(updateMock).toHaveBeenCalledWith({
            bulkComplianceEntryCompleteEnvelope: {
                records: [
                    { attachments: undefined, complianceId: 123, entry: { comment: undefined, controlCompleted: 1 } },
                    { attachments: undefined, complianceId: 456, entry: { comment: undefined, controlCompleted: 1 } },
                    {
                        attachments: [
                            { fileId: 'uuid-file2.pdf', fileName: 'file2.pdf' },
                            { fileId: 'uuid-file3.pdf', fileName: 'file3.pdf' },
                        ],
                        complianceId: 789,
                        entry: { comment: 'Some test comment', controlCompleted: 2 },
                    },
                ],
            },
        });
    });

    it('shows alert dialog when submitting the form failed', async () => {
        const updateMock = jest.fn().mockReturnValue({ unwrap: () => Promise.reject() });
        jest.spyOn(complianceApi, 'useCersUpdateEntriesBulkUsingPutMutation').mockReturnValue([
            updateMock,
            {
                isLoading: false,
                isSuccess: false,
                reset: jest.fn(),
            },
        ]);

        const { user } = setup();
        await fillForm(user);
        await user.click(screen.getByTestId('myCompliance-buttonSubmit'));

        const dialog = screen.getByTestId(`dialog-${strings('compliance:submitEntriesAlertDialog.notSubmittedTitle')}`);

        expect(within(dialog).getByText(strings('compliance:submitEntriesAlertDialog.notSubmittedTitle'))).toBeInTheDocument();
        expect(within(dialog).getByText(strings('compliance:submitEntriesAlertDialog.notSubmittedSubtitle'))).toBeInTheDocument();
        expect(within(dialog).getByText(strings('compliance:submitEntriesAlertDialog.notSubmittedTryAgain'))).toBeInTheDocument();
    });

    it('tries to submit the form again when submitting fails and user clicks on try again button', async () => {
        const updateMock = jest.fn().mockReturnValue({ unwrap: () => Promise.reject() });
        jest.spyOn(complianceApi, 'useCersUpdateEntriesBulkUsingPutMutation').mockReturnValue([
            updateMock,
            {
                isLoading: false,
                isSuccess: false,
                reset: jest.fn(),
            },
        ]);

        const { user } = setup();
        await fillForm(user);
        await user.click(screen.getByTestId('myCompliance-buttonSubmit'));

        expect(updateMock).toHaveBeenCalled();

        const dialog = screen.getByTestId(`dialog-${strings('compliance:submitEntriesAlertDialog.notSubmittedTitle')}`);
        expect(dialog).toBeInTheDocument();

        await user.click(within(dialog).getByTestId('button-tryAgain'));
        expect(updateMock).toHaveBeenCalledTimes(2);
    });

    it('navigates to confirmation page after submitting the form successfully', async () => {
        const updateMock = jest.fn().mockReturnValue({ unwrap: () => Promise.resolve() });
        jest.spyOn(complianceApi, 'useCersUpdateEntriesBulkUsingPutMutation').mockReturnValue([
            updateMock,
            {
                isLoading: false,
                isSuccess: true,
                reset: jest.fn(),
            },
        ]);

        const { user } = setup();
        await fillForm(user);
        await user.click(screen.getByTestId('myCompliance-buttonSubmit'));
        expect(mockNavigate).toHaveBeenCalledWith(CompliancePath.MY_COMPLIANCE_CONFIRMATION);
    });

    it('calls api for entries and responses with offset 0 at the beginning', async () => {
        setup();
        expect(complianceApi.useCersGetMyComplianceEntriesUsingGetQuery).toHaveBeenCalledWith({
            isReportMonth: true,
            filter: 'status="0"',
            limit: 20,
            offset: 0,
        });
        expect(complianceApi.useCrrsGetComplianceResponsesUsingGetQuery).toHaveBeenCalledWith({});
        await waitFor(() => {
            expect(screen.getByTestId('myCompliance-sectionEntries')).toBeInTheDocument();
        });
    });

    it('calls api for entries with new offset when scrolled to the end of the list', async () => {
        setup(300);
        expect(complianceApi.useCersGetMyComplianceEntriesUsingGetQuery).toHaveBeenCalledWith({
            isReportMonth: true,
            filter: 'status="0"',
            limit: 20,
            offset: 0,
        });
        expect(complianceApi.useCrrsGetComplianceResponsesUsingGetQuery).toHaveBeenCalledWith({});

        const list = within(screen.getByTestId('myCompliance-sectionEntries')).getByTestId('section-content').childNodes[0];

        fireEvent.scroll(list, { target: { scrollTop: 150 } });

        expect(complianceApi.useCersGetMyComplianceEntriesUsingGetQuery).toHaveBeenCalledWith({
            isReportMonth: true,
            filter: 'status="0"',
            limit: 20,
            offset: 20,
        });

        await waitFor(() => {
            expect(screen.getByTestId('myCompliance-sectionEntries')).toBeInTheDocument();
        });
    });
});
