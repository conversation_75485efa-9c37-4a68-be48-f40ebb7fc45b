// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ComplianceEntry should render with options, required error and no comments when no option selected 1`] = `
<div>
  <div
    class="MuiBox-root css-0"
    data-testid="myCompliance-entryItem"
  >
    <div
      class="MuiBox-root css-0"
      data-testid="myCompliance-entryResponse"
    >
      <div
        class="MuiBox-root css-43yvyf"
      >
        <div
          class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <p
                class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
              >
                <label
                  for="entries.0.response"
                >
                  Some label
                </label>
              </p>
            </div>
          </div>
          <div
            class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
            role="radiogroup"
          >
            <label
              class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
            >
              <span
                class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                shape="square"
              >
                <input
                  class="PrivateSwitchBase-input css-1m9pwf3"
                  name="entries.0.response"
                  type="radio"
                  value="1"
                />
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                  data-icon="square-button-unchecked"
                  data-testid="SquareRadioButtonUncheckedIcon"
                  fill="currentColor"
                  focusable="false"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    fill-opacity="0"
                    height="16"
                    rx="3"
                    stroke="currentColor"
                    stroke-width="2"
                    width="16"
                    x="4"
                    y="4"
                  />
                </svg>
              </span>
              <span
                class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
              >
                <span
                  style="min-width: auto;"
                >
                  Yes
                </span>
              </span>
            </label>
            <label
              class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
            >
              <span
                class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-12y0knt-MuiButtonBase-root-MuiRadio-root"
                shape="square"
              >
                <input
                  class="PrivateSwitchBase-input css-1m9pwf3"
                  name="entries.0.response"
                  type="radio"
                  value="2"
                />
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1lcyhuf-MuiSvgIcon-root"
                  data-icon="square-button-unchecked"
                  data-testid="SquareRadioButtonUncheckedIcon"
                  fill="currentColor"
                  focusable="false"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    fill-opacity="0"
                    height="16"
                    rx="3"
                    stroke="currentColor"
                    stroke-width="2"
                    width="16"
                    x="4"
                    y="4"
                  />
                </svg>
              </span>
              <span
                class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
              >
                <span
                  style="min-width: auto;"
                >
                  No
                </span>
              </span>
            </label>
          </div>
          <div
            class="MuiBox-root css-1li2cig"
          >
            <svg
              data-icon="arrow-mandatory"
              fill="currentColor"
              height="19px"
              style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
              viewBox="0 0 24 24"
              width="19px"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                fill="#EE0700"
              />
            </svg>
            <p
              class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
              style="line-height: 100%;"
            >
              Required
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
