// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ComplianceEntries new items are displayed and onEndReached called when scrolled down the list 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-13fjuhh-MuiGrid-root"
    data-testid="myCompliance-sectionEntries"
  >
    <div
      class="MuiGrid-root MuiGrid-item css-jrw6kt-MuiGrid-root"
    >
      <h5
        class="MuiTypography-root MuiTypography-h5 css-1795yd1-MuiTypography-root"
        data-testid="section-title"
      >
        Compliance items to be completed
      </h5>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-1p0arqy-MuiGrid-root"
      data-testid="section-content"
    >
      <div
        data-testid="virtuoso-scroller"
        data-virtuoso-scroller="true"
        style="height: 100%; outline: none; overflow-y: auto; position: relative;"
        tabindex="0"
      >
        <div
          data-viewport-type="element"
          style="width: 100%; height: 100%; position: absolute; top: 0px;"
        >
          <div
            data-testid="virtuoso-item-list"
            style="box-sizing: border-box; margin-top: 0px; padding-top: 150px; padding-bottom: 0px;"
          >
            <div
              data-index="1"
              data-item-index="1"
              data-known-size="150"
            >
              <div
                data-testid="myCompliance-entryItem"
                style="padding-bottom: 32px; padding-top: 29px; border-bottom: 1px solid #DCDCDC;"
              >
                2 - Second item
              </div>
            </div>
            <div
              data-index="2"
              data-item-index="2"
              data-known-size="150"
            >
              <div
                data-testid="myCompliance-entryItem"
                style="padding-bottom: 0px; padding-top: 29px; border-bottom: 0;"
              >
                3 - Third item
              </div>
            </div>
          </div>
          <div />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`ComplianceEntries renders items in the list 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-13fjuhh-MuiGrid-root"
    data-testid="myCompliance-sectionEntries"
  >
    <div
      class="MuiGrid-root MuiGrid-item css-jrw6kt-MuiGrid-root"
    >
      <h5
        class="MuiTypography-root MuiTypography-h5 css-1795yd1-MuiTypography-root"
        data-testid="section-title"
      >
        Compliance items to be completed
      </h5>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-1p0arqy-MuiGrid-root"
      data-testid="section-content"
    >
      <div
        data-testid="virtuoso-scroller"
        data-virtuoso-scroller="true"
        style="height: 100%; outline: none; overflow-y: auto; position: relative;"
        tabindex="0"
      >
        <div
          data-viewport-type="element"
          style="width: 100%; height: 100%; position: absolute; top: 0px;"
        >
          <div
            data-testid="virtuoso-item-list"
            style="box-sizing: border-box; margin-top: 0px; padding-top: 0px; padding-bottom: 150px;"
          >
            <div
              data-index="0"
              data-item-index="0"
              data-known-size="150"
            >
              <div
                data-testid="myCompliance-entryItem"
                style="padding-bottom: 32px; padding-top: 0px; border-bottom: 1px solid #DCDCDC;"
              >
                1 - First item
              </div>
            </div>
            <div
              data-index="1"
              data-item-index="1"
              data-known-size="150"
            >
              <div
                data-testid="myCompliance-entryItem"
                style="padding-bottom: 32px; padding-top: 29px; border-bottom: 1px solid #DCDCDC;"
              >
                2 - Second item
              </div>
            </div>
          </div>
          <div />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`ComplianceEntries renders items in the list with loader when data is loading 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-13fjuhh-MuiGrid-root"
    data-testid="myCompliance-sectionEntries"
  >
    <div
      class="MuiGrid-root MuiGrid-item css-jrw6kt-MuiGrid-root"
    >
      <h5
        class="MuiTypography-root MuiTypography-h5 css-1795yd1-MuiTypography-root"
        data-testid="section-title"
      >
        Compliance items to be completed
      </h5>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-1p0arqy-MuiGrid-root"
      data-testid="section-content"
    >
      <div
        data-testid="virtuoso-scroller"
        data-virtuoso-scroller="true"
        style="height: 100%; outline: none; overflow-y: auto; position: relative;"
        tabindex="0"
      >
        <div
          data-viewport-type="element"
          style="width: 100%; height: 100%; position: absolute; top: 0px;"
        >
          <div
            data-testid="virtuoso-item-list"
            style="box-sizing: border-box; margin-top: 0px; padding-top: 0px; padding-bottom: 150px;"
          >
            <div
              data-index="0"
              data-item-index="0"
              data-known-size="150"
            >
              <div
                data-testid="myCompliance-entryItem"
                style="padding-bottom: 32px; padding-top: 0px; border-bottom: 1px solid #DCDCDC;"
              >
                1 - First item
              </div>
            </div>
            <div
              data-index="1"
              data-item-index="1"
              data-known-size="150"
            >
              <div
                data-testid="myCompliance-entryItem"
                style="padding-bottom: 32px; padding-top: 29px; border-bottom: 1px solid #DCDCDC;"
              >
                2 - Second item
              </div>
            </div>
          </div>
          <div>
            <div
              class="css-154v356"
            >
              <div
                class="css-17cedzo"
              >
                <div
                  class="icon"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-spinner icon loadingSpinner"
                    data-icon="spinner"
                    data-prefix="fad"
                    focusable="false"
                    role="img"
                    viewBox="0 0 512 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      class="fa-duotone-group"
                    >
                      <path
                        class="fa-secondary"
                        d="M60.9 403.1a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM208 464a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM369.1 75A48 48 0 1 0 437 142.9 48 48 0 1 0 369.1 75zm0 294.2A48 48 0 1 0 437 437a48 48 0 1 0 -67.9-67.9zM416 256a48 48 0 1 0 96 0 48 48 0 1 0 -96 0z"
                        fill="currentColor"
                      />
                      <path
                        class="fa-primary"
                        d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM96 256A48 48 0 1 0 0 256a48 48 0 1 0 96 0zM75 142.9A48 48 0 1 0 142.9 75 48 48 0 1 0 75 142.9z"
                        fill="currentColor"
                      />
                    </g>
                  </svg>
                </div>
                <p
                  class="MuiTypography-root MuiTypography-body1 css-1tnh15z-MuiTypography-root"
                >
                  Loading...
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
