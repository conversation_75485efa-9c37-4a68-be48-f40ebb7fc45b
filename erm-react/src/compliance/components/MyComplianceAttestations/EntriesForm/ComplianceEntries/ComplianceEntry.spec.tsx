import React from 'react';
import { FormProvider } from 'react-hook-form';
import { within } from '@testing-library/react';
import { render, screen } from 'test/utils';
import useForm from 'common/hooks/forms/useForm';
import { strings } from 'common/utils/i18n';
import { validationSchema } from '../validationSchema';
import ComplianceEntry from './ComplianceEntry';
import { ControlResponseItems, responses } from 'compliance/api.mock';
import { ControlType } from 'compliance/types';

jest.mock('app/rtkApi', () => {
    const actualRtkApi = jest.requireActual('app/rtkApi');
    return {
        ...actualRtkApi,
        useGetSystemConfigurationQuery: () => ({
            data: {
                max_attach_size_bytes: 10,
            },
        }),
    };
});

jest.mock('common/api/files', () => {
    const actualRtkApi = jest.requireActual('common/api/files');

    return {
        ...actualRtkApi,
        useFersGetAllowedExtensionsUsingGetQuery: () => ({ data: [], isLoading: false, isSuccess: true, isError: false }),
    };
});

const onResponseChangeMock = jest.fn();
const triggerMock = jest.fn();

const EntryWrapper = () => {
    const defaultValues = {
        confirmation: false,
        entries: [{ response: '', comments: '', controlType: ControlType.CONTROL }],
    };

    const methods = useForm({
        defaultValues,
        schema: validationSchema,
        mode: 'onChange',
        context: {
            responses,
        },
    });
    return (
        <FormProvider
            {...methods}
            trigger={triggerMock}
        >
            <ComplianceEntry
                index={0}
                label="Some label"
                onResponseChange={onResponseChangeMock}
                responseItems={ControlResponseItems}
            />
        </FormProvider>
    );
};

describe('ComplianceEntry', () => {
    it('should render with options, required error and no comments when no option selected', async () => {
        const { container } = render(<EntryWrapper />);
        const response = within(screen.getByTestId('myCompliance-entryResponse'));

        expect(response.getByText('Some label')).toBeInTheDocument();
        expect(response.getAllByRole('radio')).toHaveLength(2);
        expect(response.getByLabelText('Yes')).not.toBeChecked();
        expect(response.getByLabelText('No')).not.toBeChecked();
        expect(await response.findByText('Required')).toBeInTheDocument();
        expect(screen.queryByLabelText(strings('common:label.comments'))).not.toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('should render correct option and show/ hide comments depending on selected option', async () => {
        const { user } = render(<EntryWrapper />);
        const response = within(screen.getByTestId('myCompliance-entryResponse'));

        expect(response.getByLabelText('Yes')).not.toBeChecked();
        expect(response.getByLabelText('No')).not.toBeChecked();
        expect(await response.findByText('Required')).toBeInTheDocument();

        await user.click(response.getByLabelText('No'));

        expect(response.getByLabelText('Yes')).not.toBeChecked();
        expect(response.getByLabelText('No')).toBeChecked();
        expect(await response.findByText('Required')).not.toBeInTheDocument();

        const commentsField = within(await screen.findByTestId('myCompliance-entryComments'));
        expect(commentsField.getByLabelText(strings('common:label.comments'))).toBeInTheDocument();
        expect(await commentsField.findByText('Required')).toBeInTheDocument();

        await user.click(response.getByLabelText('Yes'));

        expect(response.getByLabelText('Yes')).toBeChecked();
        expect(response.getByLabelText('No')).not.toBeChecked();

        expect(screen.queryByLabelText(strings('common:label.comments'))).not.toBeInTheDocument();
    });

    it('should call onResponseChange', async () => {
        const { user } = render(<EntryWrapper />);
        const response = within(screen.getByTestId('myCompliance-entryResponse'));

        expect(response.getByLabelText('Yes')).not.toBeChecked();
        expect(response.getByLabelText('No')).not.toBeChecked();

        await user.click(response.getByLabelText('Yes'));
        expect(response.getByLabelText('Yes')).toBeChecked();
        expect(onResponseChangeMock).toHaveBeenCalledWith(0);
        expect(triggerMock).toHaveBeenCalledWith('entries.0.comments');
    });
});
