import React from 'react';
import { FormProvider } from 'react-hook-form';
import { within, waitFor, fireEvent } from '@testing-library/react';
import { render, screen, createDataTransferWithFiles, createFile } from 'test/utils';
import useForm from 'common/hooks/forms/useForm';
import { strings } from 'common/utils/i18n';
import CommentsSection from './CommentsSection';
import { validationSchema } from '../validationSchema';
import { responses } from 'compliance/api.mock';
import { ControlType } from 'compliance/types';

const mockTriggerUpload = jest.fn().mockReturnValue({ unwrap: () => Promise.resolve() });

global.URL.createObjectURL = jest.fn();

jest.mock('common/api/attachments', () => {
    const actualRtkApi = jest.requireActual('common/api/attachments');
    return {
        ...actualRtkApi,
        useUploadTempAttachment1Mutation: () => [mockTriggerUpload],
    };
});

jest.mock('app/rtkApi', () => {
    const actualRtkApi = jest.requireActual('app/rtkApi');
    return {
        ...actualRtkApi,
        useGetSystemConfigurationQuery: () => ({
            data: {
                max_attach_size_bytes: 10,
            },
        }),
    };
});

jest.mock('common/api/files', () => {
    const actualRtkApi = jest.requireActual('common/api/files');

    return {
        ...actualRtkApi,
        useFersGetAllowedExtensionsUsingGetQuery: () => ({ data: [], isLoading: false, isSuccess: true, isError: false }),
    };
});

const CommentsSectionWrapper = ({ response, comments }: { response: string; comments: string }) => {
    const defaultValues = {
        confirmation: false,
        entries: [{ response, comments, controlType: ControlType.CONTROL }],
    };
    const methods = useForm({
        defaultValues,
        mode: 'onChange',
        schema: validationSchema,
        context: {
            responses,
        },
    });
    return (
        <FormProvider {...methods}>
            <CommentsSection index={0} />
        </FormProvider>
    );
};

describe('CommentsSection', () => {
    it('should render comments field with required error and attachments field', async () => {
        render(
            <CommentsSectionWrapper
                response="2"
                comments=""
            />,
        );

        const commentsField = within(screen.getByTestId('myCompliance-entryComments'));
        const attachmentsField = within(screen.getByTestId('myCompliance-entryAttachments'));
        expect(commentsField.getByLabelText(strings('common:label.comments'))).toBeInTheDocument();
        expect(await commentsField.findByText('Required')).toBeInTheDocument();
        expect(attachmentsField.getByText('Attachments')).toBeInTheDocument();
    });

    it('should render with comments and its value without error when comment is written', async () => {
        render(
            <CommentsSectionWrapper
                response="2"
                comments="some comment"
            />,
        );

        const commentsField = within(screen.getByTestId('myCompliance-entryComments'));
        expect(commentsField.getByLabelText(strings('common:label.comments'))).toHaveValue('some comment');
        await waitFor(() => {
            expect(commentsField.queryByText('Required')).not.toBeInTheDocument();
        });
    });

    it('should render attachments field with correct placeholder and button', async () => {
        render(
            <CommentsSectionWrapper
                response="2"
                comments="some comment"
            />,
        );

        const attachmentsField = within(screen.getByTestId('myCompliance-entryAttachments'));
        const dropzone = attachmentsField.getByRole('presentation');
        await waitFor(() => {
            expect(attachmentsField.getByTestId('button-add')).toHaveTextContent('Add');
        });
        await waitFor(() => {
            expect(dropzone).toHaveTextContent('drop or add attachments here (maximum 10MB)');
        });
    });

    it('should display files in the attachments field when they are dropped', async () => {
        const files = [createFile('file1.pdf', 1024, 'application/pdf'), createFile('file2.pdf', 1024, 'application/pdf')];
        const event = createDataTransferWithFiles(files);

        render(
            <CommentsSectionWrapper
                response="2"
                comments="some comment"
            />,
        );

        const attachmentsField = within(screen.getByTestId('myCompliance-entryAttachments'));
        const dropzone = attachmentsField.getByRole('presentation');
        fireEvent.drop(dropzone, event);
        await waitFor(() => {
            expect(within(dropzone).getAllByTestId('LinksList-Item')).toHaveLength(2);
        });
        const fileItems = within(dropzone).getAllByTestId('LinksList-Item');
        expect(fileItems.at(0)).toHaveTextContent('file1.pdf');
        expect(fileItems.at(1)).toHaveTextContent('file2.pdf');
    });
});
