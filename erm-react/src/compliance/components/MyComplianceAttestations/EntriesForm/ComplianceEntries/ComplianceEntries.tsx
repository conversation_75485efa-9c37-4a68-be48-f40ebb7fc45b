import React, { useCallback, useRef } from 'react';
import _ from 'lodash';
import { Virtuoso, VirtuosoHandle } from 'react-virtuoso';
import { ComplianceResponseRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import Section from 'common/components/Section';
import Loading from 'common/components/Loading';
import { LoaderType } from 'common/components/Loading/Loading';
import ComplianceEntry from './ComplianceEntry';
import { ComplianceFormEntry } from 'compliance/types';
import { getResponseItemsByControlType } from 'compliance/utils';
import { useTheme } from '@mui/material/styles';

type EntriesProps = {
    entries: ComplianceFormEntry[];
    responses: ComplianceResponseRest[];
    isLoading: boolean;
    onEndReached: () => void;
    onResponseChange: () => void;
};

const ComplianceEntries: React.FC<EntriesProps> = ({ entries, responses, onEndReached, onResponseChange, isLoading }) => {
    const theme = useTheme();
    const listRef = useRef<VirtuosoHandle>(null);

    const handleResponseChange = useCallback(
        (index: number) => {
            setTimeout(() => {
                if (listRef.current) {
                    listRef.current.scrollIntoView({
                        index,
                        behavior: 'smooth',
                    });
                    onResponseChange();
                }
            }, 100);
        },
        [onResponseChange],
    );

    const item = useCallback(
        (index: number, item: ComplianceFormEntry & { id: string }) => {
            const responseItems = getResponseItemsByControlType(responses, item.controlType);

            return (
                <ComplianceEntry
                    key={item.id}
                    index={index}
                    label={`${index + 1} - ${item.label}`}
                    responseItems={responseItems}
                    onResponseChange={handleResponseChange}
                    style={{
                        paddingBottom: index === entries.length - 1 ? '0' : '32px',
                        paddingTop: index === 0 ? '0' : '29px',
                        borderBottom: index === entries.length - 1 ? '0' : `1px solid ${theme.palette.protechtGrey?.grey_220}`,
                    }}
                />
            );
        },
        [responses, entries.length, handleResponseChange, theme.palette.protechtGrey?.grey_220],
    );

    return (
        <Section
            data-testid="myCompliance-sectionEntries"
            title={strings('compliance:title.itemsToBeCompleted')}
            height="100%"
            contentBorder={false}
        >
            <Virtuoso
                style={{ height: '100%' }}
                data={entries}
                itemContent={item}
                ref={listRef}
                endReached={onEndReached}
                components={{
                    Footer: () => {
                        if (isLoading) {
                            return (
                                <Loading
                                    loaderType={LoaderType.Slim}
                                    inline={true}
                                />
                            );
                        }
                        return null;
                    },
                }}
            />
        </Section>
    );
};

export default ComplianceEntries;
