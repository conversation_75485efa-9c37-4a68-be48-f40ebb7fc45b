import React, { useCallback, useMemo } from 'react';
import Box, { BoxProps } from '@mui/material/Box';
import useFormContext from 'common/hooks/forms/useFormContext';
import RadioGroupField from 'common/components/Form/FormFields/RadioGroupField';
import { ComplianceResponseItem } from 'api/generated/types';
import CommentsSection from './CommentsSection';
import { responseHasForcedComments } from 'compliance/utils';

type ComplianceEntryProps = BoxProps & {
    index: number;
    label: string;
    responseItems: ComplianceResponseItem[];
    onResponseChange: (index: number) => void;
};

const ComplianceEntry: React.FC<ComplianceEntryProps> = ({ label, responseItems, onResponseChange, index, ...other }) => {
    const { watch, trigger } = useFormContext();
    const selectedResponse: string = watch(`entries.${index}.response`);
    const commentsSectionVisible = useMemo(
        () => (selectedResponse ? responseHasForcedComments(responseItems, Number(selectedResponse)) : false),
        [responseItems, selectedResponse],
    );

    const handleResponseChange = useCallback(() => {
        void trigger(`entries.${index}.comments`);
        onResponseChange(index);
    }, [onResponseChange, index, trigger]);

    return (
        <Box
            data-testid="myCompliance-entryItem"
            {...other}
        >
            <Box data-testid="myCompliance-entryResponse">
                <RadioGroupField
                    name={`entries.${index}.response`}
                    label={label}
                    shape="square"
                    options={responseItems.map((option) => {
                        return { label: option.name, value: option.id!.toString() };
                    })}
                    row
                    onChange={handleResponseChange}
                />
            </Box>
            {commentsSectionVisible && <CommentsSection index={index} />}
        </Box>
    );
};

export default React.memo(ComplianceEntry);
