import React, { CSSProperties } from 'react';
import { fireEvent, waitFor } from '@testing-library/react';
import { render, screen } from 'test/utils';
import ComplianceEntries from './ComplianceEntries';
import { responses } from 'compliance/api.mock';
import { VirtuosoMockContext } from 'react-virtuoso';
import { ControlType } from 'compliance/types';

jest.mock('./ComplianceEntry', () => ({ label, style }: { label: string; style: CSSProperties }) => (
    <div
        style={style}
        data-testid="myCompliance-entryItem"
    >
        {label}
    </div>
));

const onEndReachedMock = jest.fn();
const onResponseChangeMock = jest.fn();
const entries = [
    {
        response: '',
        comments: '',
        controlType: ControlType.CONTROL,
        id: 'abc',
        entryId: 123,
        label: 'First item',
        attachments: [],
    },
    {
        response: '',
        comments: '',
        controlType: ControlType.QUESTION,
        id: 'def',
        entryId: 456,
        label: 'Second item',
        attachments: [],
    },
    {
        response: '',
        comments: '',
        controlType: ControlType.CONTROL,
        id: 'ghi',
        entryId: 789,
        label: 'Third item',
        attachments: [],
    },
];

const EntriesWrapper = ({ isLoading = false }: { isLoading?: boolean }) => {
    return (
        <VirtuosoMockContext.Provider value={{ viewportHeight: 300, itemHeight: 150 }}>
            <ComplianceEntries
                entries={entries}
                responses={responses}
                isLoading={isLoading}
                onEndReached={onEndReachedMock}
                onResponseChange={onResponseChangeMock}
            />
        </VirtuosoMockContext.Provider>
    );
};

describe('ComplianceEntries', () => {
    it('renders items in the list', async () => {
        const { container } = render(<EntriesWrapper />);
        expect(screen.queryByText('Loading...')).not.toBeInTheDocument();

        // based on viewportHeight and itemHeight set for VirtuosoMockContext.Provider,
        // only 2 items are visible
        await waitFor(() => {
            expect(screen.getAllByTestId('myCompliance-entryItem')).toHaveLength(2);
        });
        expect(screen.getByText('1 - First item')).toBeInTheDocument();
        expect(screen.getByText('2 - Second item')).toBeInTheDocument();
        expect(screen.queryByText('3 - Third item')).not.toBeInTheDocument();

        expect(onEndReachedMock).not.toHaveBeenCalled();

        expect(container).toMatchSnapshot();
    });

    it('renders items in the list with loader when data is loading', async () => {
        const { container } = render(<EntriesWrapper isLoading={true} />);
        await waitFor(() => {
            expect(screen.getAllByTestId('myCompliance-entryItem')).toHaveLength(2);
        });
        expect(screen.getByText('Loading...')).toBeInTheDocument();

        expect(container).toMatchSnapshot();
    });

    it('new items are displayed and onEndReached called when scrolled down the list', async () => {
        const { container } = render(<EntriesWrapper />);
        const list = screen.getByTestId('section-content').childNodes[0];

        fireEvent.scroll(list, { target: { scrollTop: 150 } });

        await waitFor(() => {
            expect(screen.getAllByTestId('myCompliance-entryItem')).toHaveLength(2);
        });
        expect(screen.queryByText('1 - First item')).not.toBeInTheDocument();
        expect(screen.getByText('2 - Second item')).toBeInTheDocument();
        expect(screen.getByText('3 - Third item')).toBeInTheDocument();

        expect(onEndReachedMock).toHaveBeenCalled();

        expect(container).toMatchSnapshot();
    });
});
