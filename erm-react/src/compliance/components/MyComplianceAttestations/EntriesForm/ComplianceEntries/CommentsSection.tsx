import React from 'react';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { strings } from 'common/utils/i18n';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import AttachmentField from 'common/components/Form/FormFields/AttachmentField';

type CommentsSectionProps = {
    index: number;
};

const CommentsSection: React.FC<CommentsSectionProps> = ({ index }) => {
    return (
        <Box paddingTop="20px">
            <Grid
                container
                spacing="20px"
            >
                <Grid
                    item
                    xs={12}
                    sm={6}
                    data-testid="myCompliance-entryComments"
                >
                    <InputField
                        name={`entries.${index}.comments`}
                        label={strings('common:label.comments')}
                        multiline
                        dataTestId="input-comments"
                    />
                </Grid>
                <Grid
                    item
                    xs={12}
                    sm={6}
                    data-testid="myCompliance-entryAttachments"
                >
                    <AttachmentField
                        formFieldProps={{
                            label: strings('common:attachments.label'),
                            name: `entries.${index}.attachments`,
                        }}
                        multiple={true}
                        uploadFiles={true}
                    />
                </Grid>
            </Grid>
        </Box>
    );
};

export default CommentsSection;
