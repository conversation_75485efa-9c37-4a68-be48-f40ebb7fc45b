import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { FormProvider, useFieldArray } from 'react-hook-form';
import { useTheme } from '@mui/material/styles';
import DialogActions from '@mui/material/DialogActions';
import Grid from '@mui/material/Grid';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import BooleanField from '@protecht/ui-library/library/components/FormFields/BooleanField';
import { AlertType } from '@protecht/ui-library/library/types';
import { BulkComplianceEntryComplete, CersUpdateEntriesBulkUsingPutApiArg } from 'api/generated/types';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import useForm from 'common/hooks/forms/useForm';
import { useUnsavedChangesAlert } from 'common/hooks/useUnsavedChangesAlert';
import { strings } from 'common/utils/i18n';
import { DialogType } from 'common/types';
import Section from 'common/components/Section';
import LoadingOverlay from 'common/components/LoadingOverlay';
import Loading from 'common/components/Loading';
import AlertDialog from 'ui/components/AlertDialog';

import {
    useCrrsGetComplianceResponsesUsingGetQuery,
    useCersGetMyComplianceEntriesUsingGetQuery,
    useCersUpdateEntriesBulkUsingPutMutation,
} from 'compliance/rtkApi';
import { getResponseItemsByControlType, responseHasForcedComments } from 'compliance/utils';
import ComplianceEntries from './ComplianceEntries';
import { ComplianceFormEntry, ComplianceFormValues } from 'compliance/types';
import { CompliancePath } from 'compliance/routes';
import { validationSchema } from './validationSchema';
import InfoPage from 'common/components/InfoPage';
import PageToolbar from 'common/components/PageToolbar';
import PageToolbarGroup from 'common/components/PageToolbar/PageToolbarGroup';
import PageToolbarTitle from 'common/components/PageToolbar/PageToolbarTitle';
import PageToolbarSubtitle from 'common/components/PageToolbar/PageToolbarSubtitle';
import PageToolbarActionButton from 'common/components/PageToolbar/PageToolbarActionButton';

const PAGE_SIZE = 20;
const ENTRIES_FILTER = 'status="0"'; // we need only entries without responses

const Layout: React.FC = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const defaultValues: ComplianceFormValues = { confirmation: false, entries: [] };
    const [offset, setOffset] = useState<number>(0);
    const [isAlertDialogOpen, setIsAlertDialogOpen] = useState<boolean>(false);
    const [isDirty, setIsDirty] = useState<boolean>(false);

    const [updateEntries, { isSuccess: isUpdateEntriesSuccess, isLoading: isUpdateEntriesInProgress }] = useCersUpdateEntriesBulkUsingPutMutation();

    const {
        data: entries,
        isLoading: isLoadingEntries,
        isError: isErrorEntries,
        isFetching: isFetchingEntries,
    } = useCersGetMyComplianceEntriesUsingGetQuery({ isReportMonth: true, filter: ENTRIES_FILTER, limit: PAGE_SIZE, offset });

    const { data: responses, isLoading: isLoadingResponses, isError: isErrorResponses } = useCrrsGetComplianceResponsesUsingGetQuery({});

    const methods = useForm<ComplianceFormValues>({
        schema: validationSchema,
        mode: 'onChange',
        defaultValues,
        context: {
            responses: responses?.records ?? [],
        },
    });

    const { handleSubmit, getValues, control, formState, resetField } = methods;

    const { isValid, errors } = formState;

    const { append, fields } = useFieldArray({
        control,
        name: 'entries',
    });

    // navigate to the confirmation page when no entries are available for the user
    // or when updating entries has been successful
    useEffect(() => {
        if (entries?.totalCount === 0 || isUpdateEntriesSuccess) {
            void navigate(CompliancePath.MY_COMPLIANCE_CONFIRMATION);
        }
    }, [navigate, entries?.totalCount, isUpdateEntriesSuccess]);

    // reset the confirmation checkbox when entries fields have errors
    // e.g. when user fills the entire form with the confirmation checkbox
    // and then he changes some entry field and the error occurs
    useEffect(() => {
        if (formState.errors.entries && getValues('confirmation') === true) {
            resetField('confirmation');
        }
    }, [formState, resetField, getValues]);

    // append new entries to the field array
    useEffect(() => {
        if (responses?.records && entries?.records) {
            const newEntries = entries.records.map((entry) => {
                return {
                    response: '',
                    comments: '',
                    label: entry.control?.name,
                    entryId: entry.id,
                    controlType: entry.controlType,
                    attachments: [],
                } as ComplianceFormEntry;
            });
            append(newEntries);
        }
    }, [responses, entries, getValues, append]);

    const onSubmit = useCallback(
        async (formValues: ComplianceFormValues) => {
            const payload: CersUpdateEntriesBulkUsingPutApiArg = {
                bulkComplianceEntryCompleteEnvelope: {
                    records: formValues.entries.map((entry) => {
                        const responseId = Number(entry.response);
                        const responseItems = getResponseItemsByControlType(responses?.records || [], entry.controlType);
                        const hasForcedComments = responseHasForcedComments(responseItems, responseId);

                        const recordItem: BulkComplianceEntryComplete = {
                            complianceId: entry.entryId,
                            entry: {
                                controlCompleted: responseId,
                                comment: hasForcedComments ? entry.comments : undefined,
                            },
                            attachments: hasForcedComments
                                ? entry.attachments.map((attachment) => {
                                      return {
                                          fileName: attachment.name,
                                          fileId: attachment.uuid,
                                      };
                                  })
                                : undefined,
                        };

                        return recordItem;
                    }),
                },
            };

            try {
                await updateEntries(payload).unwrap();
            } catch {
                setIsAlertDialogOpen(true);
            }
        },
        [updateEntries, responses?.records],
    );

    // should show unsaved changes alert when any entry has been touched
    // but should not show alert when navigating to the confirmation page after the form is submitted successfully
    useUnsavedChangesAlert({
        blockNavigation: isDirty && !isUpdateEntriesSuccess && !isUpdateEntriesInProgress,
        onConfirm: handleSubmit(onSubmit),
        dialogType: !isValid ? DialogType.DISCARD : DialogType.UNSAVED,
    });

    const onAlertDialogConfirm = useCallback(
        (e: React.BaseSyntheticEvent) => {
            setIsAlertDialogOpen(false);
            void handleSubmit(onSubmit)(e);
        },
        [handleSubmit, onSubmit],
    );

    const onAlertDialogCancel = useCallback(() => {
        setIsAlertDialogOpen(false);
    }, [setIsAlertDialogOpen]);

    const loadMoreEntries = useCallback(() => {
        if (entries?.totalCount && entries?.totalCount > fields.length) {
            setOffset((prevOffset) => prevOffset + PAGE_SIZE);
        }
    }, [entries?.totalCount, fields.length]);

    const onResponseChange = useCallback(() => {
        if (!isDirty) {
            setIsDirty(true);
        }
    }, [isDirty]);

    if (isErrorEntries || isErrorResponses) {
        return (
            <InfoPage
                type={AlertType.Error}
                title={strings('compliance:title.myComplianceAttestations')}
                subtitle={strings('compliance:message.error')}
                message={strings('compliance:message.tryAgain')}
            />
        );
    }

    return (
        <ApplicationLayout>
            <PageToolbar withBorder>
                <PageToolbarGroup
                    justifyContent="space-between"
                    flex="1"
                >
                    <PageToolbarGroup>
                        <PageToolbarTitle data-testid="myCompliance-pageTitle">{strings('compliance:title.myComplianceAttestations')}</PageToolbarTitle>
                        {fields.length > 0 && entries?.totalCount && entries?.totalCount > 0 && (
                            <PageToolbarSubtitle
                                flex="0 0 auto"
                                data-testid="myCompliance-totalEntries"
                            >
                                {strings('compliance:title.totalEntries', { count: entries?.totalCount })}
                            </PageToolbarSubtitle>
                        )}
                    </PageToolbarGroup>
                    {fields.length > 0 && (
                        <PageToolbarActionButton
                            label={strings('common:button.submit')}
                            disabled={!isValid || isFetchingEntries}
                            onClick={handleSubmit(onSubmit)}
                            dataTestId="myCompliance-buttonSubmit"
                            variant="primary"
                            forceMinWidth
                        />
                    )}
                </PageToolbarGroup>
            </PageToolbar>
            <MainLayout>
                <ContentLayout sx={{ background: theme.palette.protechtGrey?.grey_250, margin: 0, paddingX: '24px' }}>
                    {(isLoadingEntries || isLoadingResponses) && <Loading />}
                    {fields.length > 0 && !isErrorEntries && (
                        <Grid
                            container
                            direction="column"
                            height="100%"
                            flexWrap="nowrap"
                            paddingTop={3}
                        >
                            <FormProvider {...methods}>
                                <Grid
                                    item
                                    flex="1 1 auto"
                                    overflow="hidden"
                                >
                                    <ComplianceEntries
                                        entries={fields}
                                        responses={responses?.records || []}
                                        onEndReached={loadMoreEntries}
                                        onResponseChange={onResponseChange}
                                        isLoading={isFetchingEntries}
                                    />
                                </Grid>
                                <Grid
                                    item
                                    flex="0 0 auto"
                                    marginTop="auto"
                                >
                                    <Section
                                        title={strings('compliance:title.confirmSubmit')}
                                        marginTop={3}
                                        data-testid="myCompliance-sectionConfirmation"
                                        contentBorder={false}
                                    >
                                        <BooleanField
                                            name="confirmation"
                                            label={strings('compliance:label.personalConfirmationDescription')}
                                            disabled={!!errors.entries || isFetchingEntries}
                                            formFieldLabel={strings('compliance:label.personalConfirmation')}
                                        />
                                    </Section>
                                </Grid>
                            </FormProvider>
                            <LoadingOverlay
                                open={isUpdateEntriesInProgress}
                                message={strings('compliance:message.saving')}
                            />
                            <AlertDialog
                                visible={isAlertDialogOpen}
                                data={{
                                    type: AlertType.Error,
                                    title: strings('compliance:submitEntriesAlertDialog.notSubmittedTitle'),
                                    contentText: strings('compliance:submitEntriesAlertDialog.notSubmittedSubtitle'),
                                    customContent: strings('compliance:submitEntriesAlertDialog.notSubmittedTryAgain'),
                                    actions: (
                                        <DialogActions>
                                            <Button
                                                {...ButtonStyles.dialogButton}
                                                variant={'secondary'}
                                                onClick={onAlertDialogCancel}
                                                dataTestId="button-cancel"
                                            >
                                                {isDirty ? strings('common:button.cancel') : strings('common:button.close')}
                                            </Button>
                                            <Button
                                                {...ButtonStyles.dialogButton}
                                                onClick={onAlertDialogConfirm}
                                                dataTestId="button-tryAgain"
                                            >
                                                {strings('compliance:submitEntriesAlertDialog.tryAgain')}
                                            </Button>
                                        </DialogActions>
                                    ),
                                }}
                            />
                        </Grid>
                    )}
                </ContentLayout>
            </MainLayout>
        </ApplicationLayout>
    );
};

export default Layout;
