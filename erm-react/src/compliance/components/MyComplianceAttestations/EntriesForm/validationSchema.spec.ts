import { ControlType } from 'compliance/types';
import { validationSchema } from './validationSchema';
import { responses } from 'compliance/api.mock';

describe('validationSchema', () => {
    it('should pass validation with all entries without forced comments and confirmation checked', async () => {
        const form = {
            confirmation: true,
            entries: [
                { response: '1', controlType: ControlType.CONTROL, comments: '' },
                { response: '1', controlType: ControlType.QUESTION, comments: '' },
            ],
        };
        await expect(validationSchema.validate(form, { context: { responses } })).resolves.toBe(form);
    });

    it('should pass validation with entries with/without forced comments and confirmation checked', async () => {
        const form = {
            confirmation: true,
            entries: [
                { response: '2', controlType: ControlType.CONTROL, comments: 'some comment' },
                { response: '1', controlType: ControlType.QUESTION, comments: '' },
            ],
        };
        await expect(validationSchema.validate(form, { context: { responses } })).resolves.toBe(form);
    });

    it('should not pass validation with valid entries but confirmation not checked', async () => {
        const form = {
            confirmation: false,
            entries: [
                { response: '1', controlType: ControlType.CONTROL, comments: '' },
                { response: '1', controlType: ControlType.QUESTION, comments: '' },
            ],
        };
        await expect(validationSchema.validate(form, { context: { responses } })).rejects.toBeDefined();
    });

    it('should not pass validation with any missing comment', async () => {
        const form = {
            confirmation: true,
            entries: [
                { response: '2', controlType: ControlType.CONTROL, comments: '' },
                { response: '1', controlType: ControlType.QUESTION, comments: '' },
            ],
        };
        await expect(validationSchema.validate(form, { context: { responses } })).rejects.toBeDefined();
    });

    it('should not pass validation with any missing response', async () => {
        const form = {
            confirmation: true,
            entries: [
                { response: '', controlType: ControlType.CONTROL, comments: '' },
                { response: '1', controlType: ControlType.QUESTION, comments: '' },
            ],
        };
        await expect(validationSchema.validate(form, { context: { responses } })).rejects.toBeDefined();
    });

    it('should not pass validation with not filled form', async () => {
        const form = {
            confirmation: false,
            entries: [
                { response: '', controlType: ControlType.CONTROL, comments: '' },
                { response: '', controlType: ControlType.QUESTION, comments: '' },
            ],
        };
        await expect(validationSchema.validate(form, { context: { responses } })).rejects.toBeDefined();
    });
});
