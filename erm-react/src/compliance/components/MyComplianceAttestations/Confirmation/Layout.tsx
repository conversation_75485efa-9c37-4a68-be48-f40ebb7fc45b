import React from 'react';

import { strings } from 'common/utils/i18n';
import InfoPage from 'common/components/InfoPage';
import { AlertType } from '@protecht/ui-library/library/types';

const Layout: React.FC = () => {
    return (
        <InfoPage
            type={AlertType.Confirm}
            title={strings('compliance:title.myComplianceAttestations')}
            subtitle={strings('compliance:message.allSubmitted')}
            message={strings('compliance:message.thankYou')}
        />
    );
};

export default Layout;
