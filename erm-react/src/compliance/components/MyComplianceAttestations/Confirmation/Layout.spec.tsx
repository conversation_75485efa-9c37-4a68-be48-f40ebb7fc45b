import React from 'react';
import { render, screen } from 'test/utils';
import { strings } from 'common/utils/i18n';
import Layout from './Layout';

describe('Layout', () => {
    it('should render with correct title and confirmation box', () => {
        const { container } = render(<Layout />);

        expect(screen.getByTestId('infoPage-pageTitle')).toHaveTextContent(strings('compliance:title.myComplianceAttestations'));
        expect(screen.getByTestId('infoBox-title')).toHaveTextContent(strings('compliance:message.allSubmitted'));
        expect(screen.getByTestId('infoBox-message')).toHaveTextContent(strings('compliance:message.thankYou'));
        expect(container).toMatchSnapshot();
    });
});
