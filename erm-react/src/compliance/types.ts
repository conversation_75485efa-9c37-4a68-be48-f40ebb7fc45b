import { Attachment } from '@protecht/ui-library/library/components/FileDropzone';

export enum ControlType {
    CONTROL = 'CONTROL',
    QUESTION = 'QUESTION',
}

export type ComplianceFormEntry = {
    response: string;
    comments: string;
    entryId: number;
    label: string;
    controlType: ControlType;
    attachments: Attachment[];
};

export type ComplianceFormValues = {
    entries: ComplianceFormEntry[];
    confirmation: boolean;
};
