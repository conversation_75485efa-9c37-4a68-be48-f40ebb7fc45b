import { baseInjectedApi } from 'api/generated/compliances';

export const compliancesApi = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['responses', 'myEntries'],
    endpoints: {
        crrsGetComplianceResponsesUsingGet: {
            providesTags: ['responses'],
        },
        cersGetMyComplianceEntriesUsingGet: {
            providesTags: ['myEntries'],
        },
    },
});

export const {
    useCrrsGetComplianceResponsesUsingGetQuery,
    useCersGetMyComplianceEntriesUsingGetQuery,
    useCersUpdateEntriesBulkUsingPutMutation,
    useCersGetAllComplianceEntriesUsingGetQuery,
} = compliancesApi;
