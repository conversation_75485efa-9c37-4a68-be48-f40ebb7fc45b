import React from 'react';
import { RouteObject } from 'react-router';
import store from 'store';
import EntriesForm from './components/MyComplianceAttestations/EntriesForm/Layout';
import Confirmation from './components/MyComplianceAttestations/Confirmation/Layout';
import { compliancesApi } from './rtkApi';

export const ComplianceRoutes: RouteObject[] = [
    {
        path: 'compliance',
        children: [
            {
                path: 'my',
                children: [
                    {
                        index: true,
                        element: <EntriesForm />,
                        loader: () => {
                            // invalidate tags for form data queries, e.g. when navigating back from other pages
                            void store.dispatch(compliancesApi.util.invalidateTags(['myEntries', 'responses']));
                            return null;
                        },
                    },
                    {
                        path: 'confirmation',
                        element: <Confirmation />,
                    },
                ],
            },
        ],
    },
];
