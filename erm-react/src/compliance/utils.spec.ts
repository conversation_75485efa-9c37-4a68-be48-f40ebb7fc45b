import { getResponseItemsByControlType, responseHasForcedComments } from './utils';
import { ControlResponseItems, QuestionResponseItems, responses } from './api.mock';
import { ControlType } from './types';

describe('getResponseItemsByControlType', () => {
    it('returns correct items for control type CONTROL', () => {
        expect(getResponseItemsByControlType(responses, ControlType.CONTROL)).toEqual(ControlResponseItems);
    });
    it('returns correct items for control type QUESTION', () => {
        expect(getResponseItemsByControlType(responses, ControlType.QUESTION)).toEqual(QuestionResponseItems);
    });
    it('returns empty array for unknown control type', () => {
        expect(getResponseItemsByControlType(responses, 'SOMETHING' as ControlType)).toEqual([]);
    });
    it('returns empty array when response items are not available', () => {
        const responsesMock = [
            {
                id: 1000,
                controlType: ControlType.CONTROL,
            },
        ];
        expect(getResponseItemsByControlType(responsesMock, ControlType.CONTROL)).toEqual([]);
    });
});

describe('responseHasForcedComments', () => {
    it('returns true when response has forced comments', () => {
        expect(responseHasForcedComments(ControlResponseItems, 2)).toBeTruthy();
    });
    it('returns false when response does not have forced comments', () => {
        expect(responseHasForcedComments(ControlResponseItems, 1)).toBeFalsy();
    });
    it('returns false when response not found', () => {
        expect(responseHasForcedComments(ControlResponseItems, 3)).toBeFalsy();
    });
    it('returns false when forceComment is not available', () => {
        const responseItemsMock = [
            {
                id: 1,
                name: 'Yes',
            },
            {
                id: 2,
                name: 'No',
            },
        ];
        expect(responseHasForcedComments(responseItemsMock, 1)).toBeFalsy();
    });
});
