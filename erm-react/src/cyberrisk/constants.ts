import { DataGridColDef } from 'common/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { FilterType } from 'view/types';
import { strings } from 'common/utils/i18n';

export const RegisterSelectorSettingsColDef: DataGridColDef[] = [
    {
        field: 'label',
        headerName: strings('common:label.registerName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 3,
        groupable: false,
    },
];

export const RegisterSettingsColDef: DataGridColDef[] = [
    ...RegisterSelectorSettingsColDef,
    {
        field: 'entriesCount',
        headerName: strings('common:label.entriesCount'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
        sortable: false,
        groupable: false,
    },
];

export const FrameworkColDef: DataGridColDef[] = [
    {
        field: 'name',
        headerName: strings('cyberrisk:label.fwTitle'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        groupable: false,
    },
    {
        field: 'version',
        headerName: strings('common:label.version'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
        sortable: true,
        groupable: false,
    },
    {
        field: 'status',
        headerName: strings('common:label.status'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
        sortable: true,
        groupable: false,
    },
];

export const CYBER_RISK_MODULE = 'CYBER_RISK';
