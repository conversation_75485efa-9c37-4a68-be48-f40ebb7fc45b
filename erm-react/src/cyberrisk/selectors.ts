import { RootState } from 'store';
import { createSelector } from '@reduxjs/toolkit';
import { hasPermission } from 'common/components/ProtectedContent/ProtectedContent';
import { ApplicationName, PermissionCodes } from 'common/types';
import { PermissionRest, PursGetUserPermissionsUsingGetApiResponse } from 'api/generated/types';

const getUserPermissions = (state: RootState) => state.api.queries?.['pursGetUserPermissionsUsingGet({})']?.data as PursGetUserPermissionsUsingGetApiResponse;

export const hasManagePermission = createSelector([getUserPermissions], (userPermissions) => {
    if (userPermissions) {
        return hasPermission(
            [
                {
                    code: PermissionCodes.WORKSPACE_MANAGE,
                    applicationName: ApplicationName.WORKSPACE,
                },
            ],
            userPermissions as PermissionRest[],
            true,
        );
    }
    return false;
});
