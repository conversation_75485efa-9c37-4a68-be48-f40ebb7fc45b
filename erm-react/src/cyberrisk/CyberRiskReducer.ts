import { CyberRiskAction, CyberRiskActionType, CyberRiskState, ItemTypes } from './types';
import { reAssignOrder, replaceItemById } from './Settings/MenuContentsSettings/utils';
import { WorkspaceGroup } from 'api/generated/types';
import _ from 'lodash';
import { swapItems } from 'app/utils';
import { v4 as uuid } from 'uuid';

export const initialState: CyberRiskState = {
    sections: [],
    filteredSections: [],
    activeSettingsDialog: null,
    activeSection: null,
    activeMenuItem: null,
    itemType: null,
    menuItemDialogType: null,
};

export const cyberRiskReducer = (state: CyberRiskState, { type, payload }: CyberRiskAction): CyberRiskState => {
    switch (type) {
        case CyberRiskActionType.SET_SECTIONS: {
            return {
                ...state,
                sections: payload,
            };
        }
        case CyberRiskActionType.SET_FILTERED_SECTIONS: {
            return {
                ...state,
                filteredSections: payload,
            };
        }
        case CyberRiskActionType.REMOVE_SECTION: {
            return {
                ...state,
                sections: state.sections?.filter((item) => item.id !== payload.id),
            };
        }
        case CyberRiskActionType.REMOVE_ITEM: {
            const newSections = replaceItemById(state.sections, payload.section.id, {
                ...payload.section,
                itemList: reAssignOrder(payload.section.itemList?.filter((_item) => !_.isEqual(_item, payload.item)) || []),
            } as Required<WorkspaceGroup>);
            return {
                ...state,
                sections: newSections,
            };
        }
        case CyberRiskActionType.REORDER_ITEMS: {
            return {
                ...state,
                sections: [
                    ...replaceItemById(state.sections, payload.section.id, {
                        ...payload.section,
                        itemList: reAssignOrder(payload.items),
                    }),
                ],
            };
        }
        case CyberRiskActionType.MOVE_SECTION: {
            const index = state.sections.findIndex((_section) => _section.id === payload.section.id);
            if (index !== undefined && index !== -1) {
                return {
                    ...state,
                    sections: reAssignOrder(swapItems(state.sections, index, payload.direction === 'UP' ? index - 1 : index + 1)),
                };
            }
            return state;
        }
        case CyberRiskActionType.MOVE_ITEM: {
            const index = payload.section.itemList?.findIndex((_item) => _item.id === payload.item.id);

            if (index !== undefined && index !== -1) {
                const newSectionList = replaceItemById(state.sections, payload.section.id, {
                    ...payload.section,
                    itemList: reAssignOrder(swapItems(payload.section.itemList, index, payload.direction === 'UP' ? index! - 1 : index! + 1)),
                } as Required<WorkspaceGroup>);

                return { ...state, sections: newSectionList };
            }
            return state;
        }
        case CyberRiskActionType.ADD_NEW_SECTION: {
            return {
                ...state,
                sections: [
                    ...state.sections,
                    {
                        name: payload.name,
                        id: uuid(),
                        itemList: [],
                        order: state.sections.length,
                        contentRole: payload.contentRole,
                    },
                ],
                activeSettingsDialog: null,
            };
        }
        case CyberRiskActionType.SET_ACTIVE_DIALOG: {
            return {
                ...state,
                activeSettingsDialog: payload.settingsDialogType,
            };
        }
        case CyberRiskActionType.SET_ACTIVE_SECTION: {
            return {
                ...state,
                activeSection: payload.section,
            };
        }
        case CyberRiskActionType.SET_ACTIVE_MENU_ITEM: {
            return {
                ...state,
                activeMenuItem: payload.item,
                itemType: payload.item?.itemType as ItemTypes,
                menuItemDialogType: payload.dialogType,
            };
        }
        case CyberRiskActionType.RENAME_SECTION: {
            return {
                ...state,
                sections: state.sections.map((section) => ({
                    ...section,
                    name: state.activeSection?.id === section.id ? payload.name : section.name,
                })),
                activeSection: null,
                activeSettingsDialog: null,
            };
        }
        case CyberRiskActionType.SET_ITEM_TYPE: {
            return {
                ...state,
                itemType: payload.itemType,
            };
        }
        case CyberRiskActionType.SAVE_MENU_ITEM: {
            const newItem = payload.item;
            const section = state.activeSection;

            const index = section?.itemList?.findIndex((item) => item.id === state.activeMenuItem?.id);
            if (index !== undefined && index >= 0 && section) {
                const newSection = { ...section, itemList: section.itemList?.map((item, i) => (index === i ? newItem : item)) };

                return {
                    ...state,
                    sections: replaceItemById(state.sections, section?.id, newSection),
                    activeSection: null,
                    activeMenuItem: null,
                    activeSettingsDialog: null,
                };
            }
            return state;
        }
        case CyberRiskActionType.ADD_ITEM_TO_SECTION: {
            const alteredSection = {
                ...state.activeSection,
                itemList: [
                    ...(state.activeSection?.itemList || []),
                    {
                        ...payload.item,
                        order: state.activeSection?.itemList ? state.activeSection.itemList.length + 1 : 0,
                        id: payload.item.id,
                    },
                ],
            };

            return {
                ...state,
                sections: replaceItemById(state.sections, state.activeSection?.id, {
                    ...alteredSection,
                }),
                activeSection: null,
                activeMenuItem: null,
                activeSettingsDialog: null,
            };
        }
        default:
            throw new Error('Unknown action type. It must be one of ReducerActionType.');
    }
};
