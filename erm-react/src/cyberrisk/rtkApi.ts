import { baseInjected<PERSON>pi as baseCyberRiskApi } from 'api/generated/workspace';
import { baseInjectedApi as baseRolesApi } from 'api/generated/iroles';
import { baseInjectedApi as baseEntriesApi } from 'api/generated/ientries';

export const cyberRiskApi = baseCyberRiskApi.enhanceEndpoints({
    addTagTypes: ['cyberriskConfig', 'metricsDisplayConfig', 'registerMetrics', 'registerStats'],
    endpoints: {
        wrsGetUserFilteredWorkspaceUsingGet: {
            providesTags: ['cyberriskConfig'],
        },
        wrsGetFullConfigForEditUsingGet: {
            providesTags: ['cyberriskConfig'],
        },
        wrsUpdateConfigUsingPut: {
            invalidatesTags: ['cyberriskConfig', 'registerStats'],
        },
        wrsGetDisplayConfigurationUsingGet: {
            providesTags: ['metricsDisplayConfig'],
        },
        wrsSetDisplayConfigurationUsingPost: {
            invalidatesTags: ['metricsDisplayConfig', 'registerMetrics'],
        },
        wrsFindByGroupItemUuidUsingGet: {
            providesTags: ['registerMetrics'],
        },
        wrsGetRegistersStatsUsingGet: {
            providesTags: ['registerStats'],
        },
    },
});

export const rolesApi = baseRolesApi.enhanceEndpoints({
    addTagTypes: ['dashboards'],
    endpoints: {
        rcGetRepositoryHierarchyUsingGet: {
            providesTags: ['dashboards'],
        },
    },
});

export const {
    useWrsUpdateConfigUsingPutMutation,
    useWrsGetRegistersStatsUsingGetQuery,
    useWrsGetDisplayConfigurationUsingGetQuery,
    useWrsFindByGroupItemUuidUsingGetQuery,
    useWrsSetDisplayConfigurationUsingPostMutation,
    useLazyWrsGetRepositoryHierarchyUsingGetQuery,
    useWrsGetRegisterColumnsByTypesUsingGetQuery,
    useWrsGetFullConfigForEditUsingGetQuery,
} = cyberRiskApi;
export const { useRcGetRepositoryHierarchyUsingGetQuery } = rolesApi;
export const { useRdrsiGetFrameworkLinkedRegisterEntriesUsingPostMutation } = baseEntriesApi;
