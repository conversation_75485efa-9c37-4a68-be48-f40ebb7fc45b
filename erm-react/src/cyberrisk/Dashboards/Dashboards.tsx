import React, { FC, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { cyberRiskApi, useLazyWrsGetRepositoryHierarchyUsingGetQuery } from 'cyberrisk/rtkApi';
import WorkspaceReporting from 'common/components/WorkspaceReporting/WorkspaceReporting';
import { RepositoryNodeBase } from 'rolesAndPermissions/types';
import { DashboardItem } from 'api/generated/types';
import { ItemTypes } from 'cyberrisk/types';
import { useParams } from 'react-router';
import { CYBER_RISK_MODULE } from '../constants';

const Dashboards: FC = () => {
    const { id: dashboardId } = useParams();
    const { data } = useSelector(cyberRiskApi.endpoints?.wrsGetUserFilteredWorkspaceUsingGet.select({ workspaceModule: CYBER_RISK_MODULE }));
    const [dashboard, setDashboard] = useState<DashboardItem>();
    const [trigger, { data: dashboardOptions, isLoading }] = useLazyWrsGetRepositoryHierarchyUsingGetQuery();

    useEffect(() => {
        const dashboard = data?.config?.groups
            ?.flatMap((section) => section.itemList)
            .find((item) => item?.itemType === ItemTypes.DASHBOARD && item?.id === dashboardId);
        setDashboard(dashboard as DashboardItem);
    }, [data, dashboardId]);

    useEffect(() => {
        if (dashboard && dashboard.path) {
            void trigger({ workspaceModule: CYBER_RISK_MODULE, parentPaths: [dashboard.path] });
        }
    }, [dashboard, trigger]);

    return (
        <WorkspaceReporting
            dashboardOptions={dashboardOptions as RepositoryNodeBase[]}
            isLoading={isLoading}
            translationPrefix={'cyberrisk'}
        />
    );
};

export default Dashboards;
