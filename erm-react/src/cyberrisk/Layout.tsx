import ApplicationLayout from '../common/layouts/ApplicationLayout';
import React, { FC, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { strings } from 'common/utils/i18n';
import MainLayout from 'common/layouts/MainLayout';
import SideMenu from 'common/components/SideMenu';
import { LayoutRef, NavigationMenuItem, NavigationMenuItemType } from 'common/types';
import { generatePath, Outlet, useLocation, useNavigate } from 'react-router';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import Typography from '@mui/material/Typography';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import { ControlsContext } from './ContextProvider';
import { useDispatch, useSelector } from 'store';
import { hasManagePermission } from './selectors';
import ActionIndicator from 'vendorRiskManagement/components/shared/ActionIndicator';
import { RegisterStats } from 'register/types';
import { ItemTypes } from './types';
import { CyberRiskRoutesEnum } from './routesConfig';
import { FrameworkItem, RegisterItem } from 'api/generated/types';
import { getViewId, isRegisterItem } from './Settings/MenuContentsSettings/utils';
import { useWrsGetRegistersStatsUsingGetQuery } from './rtkApi';
import { CYBER_RISK_MODULE } from './constants';
import { METRIC_SEARCH } from 'common/components/MetricsPanel';
import { BasicMenuItem } from '@protecht/ui-library/library';
import { setSelectedEntry } from '../app/reducer';

export const CyberRiskLayout: FC = () => {
    const [sideMenuItems, setSideMenuItems] = useState<NavigationMenuItem[]>([]);
    const layoutRef = useRef<LayoutRef>();
    const location = useLocation();
    const { filteredSections, isBusy, defaultWorkspaceName: workspaceName } = useContext(ControlsContext);
    const hasWorkspaceManagePermission = useSelector(hasManagePermission);
    const { data: registerStats } = useWrsGetRegistersStatsUsingGetQuery({
        workspaceModule: CYBER_RISK_MODULE,
    });
    const navigate = useNavigate();
    const dispatch = useDispatch();

    useEffect(() => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        window.ReactBridge!.FrameworkConfiguration!.refresh = () => {
            layoutRef.current?.refresh();
        };
    }, []);

    const handleNodeSelect = useCallback(() => {
        dispatch(setSelectedEntry(undefined));
    }, [dispatch]);

    useEffect(() => {
        const sideMenu: NavigationMenuItem[] = [];

        filteredSections
            ?.filter((section) => section.itemList && section.itemList.length)
            .map((section, index) => {
                sideMenu.push({
                    label: section.name,
                    type: NavigationMenuItemType.NESTED_TITLE,
                    key: section.id!.toString(),
                });
                section.itemList?.map((configItem) => {
                    let id = '';
                    if (configItem) {
                        switch (configItem.itemType) {
                            case ItemTypes.REGISTER:
                                id = String((configItem as RegisterItem).registerId || '');
                                break;
                            case ItemTypes.DASHBOARD:
                                id = configItem.id || '';
                                break;
                            case ItemTypes.FRAMEWORK:
                                id = String((configItem as FrameworkItem).frameworkId || '');
                                break;
                        }
                    }
                    const viewId = getViewId(configItem);
                    if (id === '') {
                        return;
                    }
                    const pathname = generatePath(`${CyberRiskRoutesEnum.CR_SLASHED}${configItem.itemType?.toLowerCase().replace('item', '')}/:id`, {
                        id,
                    });
                    const key = configItem.id!;
                    const registerStat = registerStats?.find(
                        (stats) => isRegisterItem(configItem) && (configItem as RegisterItem)?.registerId === stats.registerId,
                    );
                    sideMenu.push({
                        label: configItem.alias,
                        type: NavigationMenuItemType.ITEM,
                        key,
                        pathname: `${pathname}${viewId ? `?viewId=${viewId}` : ''}`,
                        secondaryLabel: isRegisterItem(configItem) && (configItem as RegisterItem).selectedDateColumn && registerStat && (
                            <ActionIndicator
                                registerStats={
                                    {
                                        entriesDueNow: registerStat.entriesDueNow,
                                        entriesOverdue: registerStat.entriesOverdue,
                                        registerId: registerStat.registerId,
                                        tableName: registerStat.tableName,
                                        entriesInFinalState: registerStat.entriesInFinalState,
                                        entriesInInitialState: registerStat.entriesInInitialState,
                                        openEntries: registerStat.openEntries,
                                    } as RegisterStats
                                }
                            ></ActionIndicator>
                        ),
                        onSelect: handleNodeSelect,
                    });
                });
                sideMenu.push({
                    type: NavigationMenuItemType.DIVIDER,
                    key: `divider-${index}`,
                });
            });
        if (!hasWorkspaceManagePermission) {
            sideMenu.pop();
        }
        if (hasWorkspaceManagePermission) {
            sideMenu.push({
                label: strings('common:label.settings'),
                type: NavigationMenuItemType.ITEM,
                key: 'settings',
                pathname: generatePath(CyberRiskRoutesEnum.CR_SETTINGS),
            });
        }
        setSideMenuItems(sideMenu);
    }, [filteredSections, hasWorkspaceManagePermission, registerStats]);

    useEffect(() => {
        if (
            sideMenuItems.length > 0 &&
            [CyberRiskRoutesEnum.CR_SLASHED.toString(), CyberRiskRoutesEnum.CR_SLASHED_START.toString()].includes(location.pathname)
        ) {
            const firstMenuItem = sideMenuItems.find((item) => item.type === NavigationMenuItemType.ITEM) as BasicMenuItem;
            firstMenuItem && firstMenuItem && void navigate(firstMenuItem.pathname!);
        }
    }, [sideMenuItems, navigate, location.pathname]);

    return (
        <ApplicationLayout>
            <ToolbarContainer
                disableGutters={false}
                variant="regular"
            >
                <ToolbarGroup
                    flex={1}
                    justifyContent="space-between"
                >
                    <ToolbarGroup>
                        <Typography
                            variant="h1"
                            noWrap
                            sx={{ flexGrow: 1 }}
                            data-testid="cyberrisk-heading"
                        >
                            {workspaceName}
                        </Typography>
                    </ToolbarGroup>
                </ToolbarGroup>
            </ToolbarContainer>
            <MainLayout
                sidebar={
                    <SideMenu
                        items={sideMenuItems}
                        loading={isBusy}
                        listOfIgnoredSearches={[METRIC_SEARCH]}
                    />
                }
            >
                <Outlet context={layoutRef} />
            </MainLayout>
        </ApplicationLayout>
    );
};
