import { ActiveWorkspaceGroupType, CyberRiskAction, CyberRiskActionType, ItemTypes, MenuItemDialogType, SettingsDialogsType } from './types';
import { WorkspaceGroupItem, WorkspaceGroup } from 'api/generated/types';

export const setSections = (sections: WorkspaceGroup[]): CyberRiskAction => {
    return {
        type: CyberRiskActionType.SET_SECTIONS,
        payload: sections,
    };
};

export const setFilteredSections = (sections: WorkspaceGroup[]): CyberRiskAction => {
    return {
        type: CyberRiskActionType.SET_FILTERED_SECTIONS,
        payload: sections,
    };
};

export const removeSection = (section: WorkspaceGroup): CyberRiskAction => {
    return {
        type: CyberRiskActionType.REMOVE_SECTION,
        payload: section,
    };
};

export const removeItem = (section: WorkspaceGroup, item: WorkspaceGroupItem): CyberRiskAction => {
    return {
        type: CyberRiskActionType.REMOVE_ITEM,
        payload: { section, item },
    };
};

export const handleItemReorder = (section: WorkspaceGroup, items: WorkspaceGroupItem[]): CyberRiskAction => {
    return {
        type: CyberRiskActionType.REORDER_ITEMS,
        payload: { section, items },
    };
};

export const moveSection = (section: WorkspaceGroup, direction: 'UP' | 'DOWN'): CyberRiskAction => {
    return {
        type: CyberRiskActionType.MOVE_SECTION,
        payload: { section, direction },
    };
};

export const moveItem = (section: WorkspaceGroup, item: WorkspaceGroupItem, direction: 'UP' | 'DOWN'): CyberRiskAction => {
    return {
        type: CyberRiskActionType.MOVE_ITEM,
        payload: { section, item, direction },
    };
};

export const addNewSection = (name: string): CyberRiskAction => {
    return {
        type: CyberRiskActionType.ADD_NEW_SECTION,
        payload: { name, contentRole: 'EVERYONE' },
    };
};
export const setActiveSettingsDialog = (settingsDialogType: SettingsDialogsType): CyberRiskAction => {
    return {
        type: CyberRiskActionType.SET_ACTIVE_DIALOG,
        payload: { settingsDialogType },
    };
};

export const setActiveSection = (section: ActiveWorkspaceGroupType): CyberRiskAction => {
    return {
        type: CyberRiskActionType.SET_ACTIVE_SECTION,
        payload: { section },
    };
};

export const setActiveMenuItem = (item: WorkspaceGroupItem | null, dialogType: MenuItemDialogType): CyberRiskAction => {
    return {
        type: CyberRiskActionType.SET_ACTIVE_MENU_ITEM,
        payload: { item, dialogType },
    };
};

export const renameSection = (name: string): CyberRiskAction => {
    return {
        type: CyberRiskActionType.RENAME_SECTION,
        payload: { name },
    };
};

export const setSelectedItemType = (itemType: ItemTypes | null): CyberRiskAction => {
    return {
        type: CyberRiskActionType.SET_ITEM_TYPE,
        payload: { itemType },
    };
};

export const saveMenuItem = (item: WorkspaceGroupItem): CyberRiskAction => {
    return {
        type: CyberRiskActionType.SAVE_MENU_ITEM,
        payload: { item },
    };
};

export const addItemToSection = (item: WorkspaceGroupItem): CyberRiskAction => {
    return {
        type: CyberRiskActionType.ADD_ITEM_TO_SECTION,
        payload: { item },
    };
};
