import { mockedAppSlice } from '../app/mock';
import { ItemTypes } from './types';
import { DashboardItem, FrameworkItem, RegisterItem, WorkspaceGroup } from 'api/generated/types';

export const mockedStore = {
    preloadedState: {
        app: mockedAppSlice,
    },
};

export const mockCyberriskMetricsConfig = {
    metricModule: {
        module: 'cyberrisk',
        metricContexts: [
            {
                context: 'cyberriskMetrics',
                metricDisplays: [
                    {
                        metricId: 1,
                        metricName: 'metric-API-7a428783',
                        metricOrder: 0,
                    },
                    {
                        metricId: 2,
                        metricName: 'metric-API-96035046',
                        metricOrder: 1,
                    },
                ],
            },
        ],
    },
};

const mockSections: WorkspaceGroup[] = [
    {
        id: '951794be-caec-4195-9f53-bb7b2691a3a2',
        name: 'Section 1',
        itemList: [
            {
                id: 'd3b3f97a-7ed3-41c7-aea1-0c090d367e47',
                alias: 'Item 1',
                name: 'Register 1',
                itemType: ItemTypes.REGISTER,
                tableName: 'table1',
                viewId: 201,
                hideMetrics: false,
                order: 0,
            } as RegisterItem,
            {
                id: 'a3b3f97a-7ed3-41c7-aea1-0c090d367e47',
                alias: 'Item 2',
                name: 'Register 2',
                itemType: ItemTypes.FRAMEWORK,
                frameworkId: 123,
                viewId: 202,
                order: 1,
            } as FrameworkItem,
            {
                id: 'b3b3f97a-7ed3-41c7-aea1-0c090d367e47',
                alias: 'Item 3',
                path: 'Root/Dashboard/Dashboard-1',
                itemType: ItemTypes.DASHBOARD,
                order: 2,
            } as DashboardItem,
        ],
    },
];

export const mockContext = {
    sections: mockSections,
    isBusy: false,
    defaultWorkspaceName: 'Workspace 1',
    settingsPristine: false,
    getLibrary: () => void 0,
    removeSectionItem: () => void 0,
    removeItem: () => void 0,
    doUpdateControls: () => void 0,
    resetDefaults: jest.fn(),
    dispatch: () => void 0,
    activeSettingsDialog: null,
    activeSection: null,
    activeMenuItem: null,
    menuItemDialogType: null,
    itemType: null,
};

export const mockFrameworkList = [
    {
        id: 1,
        name: 'Framework One',
        version: '1.0',
        status: 'Active',
    },
    {
        id: 2,
        name: 'Framework Two',
        version: '2.0',
        status: 'Draft',
    },
    {
        id: 3,
        name: 'Another Framework',
        version: '1.5',
        status: 'Active',
    },
];
