import { CSSProperties } from 'react';
import { styled } from '@mui/material/styles';
import IconButton from '@protecht/ui-library/library/components/IconButton';

export const styling: CSSProperties = {
    display: 'flex',
    flexGrow: 1,
    borderRadius: 'calc(4px / var(--scale-x, 1))',
    boxSizing: 'border-box',
    listStyle: 'none',
    width: '100%',
    maxWidth: '100%',
    fontWeight: 400,
    fontSize: '1rem',
    fontFamily: 'sans-serif',
};

export const ItemContent = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    width: '100%',
    height: '80px',
    borderRadius: 'calc(4px / var(--scale-x, 1))',
    border: `solid 1px ${theme.palette.protechtGrey?.grey_220}`,
    padding: '12px 24px',
}));

export const StyledIconButton = styled(IconButton)({
    width: '32px',
    height: '32px',
    padding: '0px',
    border: 'none',
    background: 'none',
    '&:hover': {
        background: 'none',
    },
});
