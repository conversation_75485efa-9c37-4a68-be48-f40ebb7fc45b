import SettingsLayout from 'common/layouts/SettingsLayout';
import { SettingsSection } from 'common/layouts/SettingsLayout';
import React, { FC, useCallback, useContext, useEffect, useMemo } from 'react';
import SettingsConfigItem from 'common/components/SettingsConfig/SettingsConfigItem';
import { FormProvider, useForm } from 'react-hook-form';
import useFormValidationResolver from 'common/hooks/forms/useFormValidationResolver';
import * as yup from 'yup';
import { strings } from 'common/utils/i18n';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import { useNavigate } from 'react-router';
import debounce from 'lodash/debounce';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';

const schema = yup.object().shape({
    workspaceName: yup.string().min(1),
});

const WorkspaceSettings: FC = () => {
    const resolver = useFormValidationResolver(schema);
    const navigate = useNavigate();

    const { defaultWorkspaceName, doUpdateControls } = useContext(ControlsContext);

    const methods = useForm({
        mode: 'onChange',
        resolver,
        values: { workspaceName: defaultWorkspaceName },
    });
    const { handleSubmit, formState, watch } = methods;
    const { isDirty, isValid } = formState;

    const debouncedOnSubmit = useCallback(
        debounce((args) => {
            doUpdateControls(args.workspaceName);
        }, 1000),
        [doUpdateControls],
    );

    const workspaceName = useMemo(() => {
        return watch('workspaceName');
    }, [watch('workspaceName')]);

    useEffect(() => {
        if (isDirty && isValid) {
            void handleSubmit(debouncedOnSubmit)();
        }
        return () => {
            debouncedOnSubmit.cancel(); // Cancel the debounced call
        };
    }, [isDirty, isValid, workspaceName]);

    return (
        <FormProvider {...methods}>
            <SettingsLayout>
                <SettingsSection>
                    <SettingsConfigItem
                        section={{
                            id: 1,
                            name: strings('cyberrisk:label.menuContents'),
                            label: strings('cyberrisk:label.menuContents'),
                            description: strings('cyberrisk:description.menuContents'),
                            actionButtonLabel: strings('cyberrisk:button.editMenuContents'),
                            actionButtonCallback: () => navigate(CyberRiskRoutesEnum.CR_MENU_CONTENTS),
                        }}
                    />

                    <StyledDivider />

                    <SettingsConfigItem
                        section={{
                            id: 2,
                            name: strings('cyberrisk:label.workspaceMetrics'),
                            label: strings('cyberrisk:label.workspaceMetrics'),
                            description: strings('cyberrisk:description.workspaceMetrics'),
                            actionButtonLabel: strings('cyberrisk:button.workspaceMetrics'),
                            actionButtonCallback: () => navigate(CyberRiskRoutesEnum.CR_METRICS),
                        }}
                    />

                    <StyledDivider />

                    <SettingsConfigItem
                        section={{
                            id: 3,
                            name: strings('cyberrisk:label.metricsToDisplay'),
                            label: strings('cyberrisk:label.metricsToDisplay'),
                            description: strings('cyberrisk:description.metricsToDisplay'),
                            actionButtonLabel: strings('cyberrisk:button.metricsToDisplay'),
                            actionButtonCallback: () => navigate(CyberRiskRoutesEnum.CR_METRICS_TO_DISPLAY),
                        }}
                    />
                </SettingsSection>
            </SettingsLayout>
        </FormProvider>
    );
};

export default WorkspaceSettings;
