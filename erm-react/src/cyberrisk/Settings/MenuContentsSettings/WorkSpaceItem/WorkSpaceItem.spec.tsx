import React from 'react';
import { render, screen } from 'test/utils/rtl';
import { fireEvent, waitFor, act } from '@testing-library/react';
import WorkSpaceItem from './WorkSpaceItem';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { MenuItemDialogType, ItemTypes } from 'cyberrisk/types';
import { FrameworkItem, DashboardItem, WorkspaceGroup } from 'api/generated/types';

// Mock the i18n strings function
jest.mock('common/utils/i18n', () => ({
    strings: jest.fn((key: string, params?: any) => {
        // Handle parameterized strings for validation messages
        if (key === 'cyberrisk:error.duplicateFramework' && params?.name) {
            return `Duplicate framework: ${params.name}`;
        }
        if (key === 'cyberrisk:error.duplicateDashboard' && params?.name) {
            return `Duplicate dashboard: ${params.name}`;
        }
        return key;
    }),
}));

// Mock the selectors - simplified
jest.mock('../FrameworkSelector/FrameworkSelector', () => {
    return function MockFrameworkSelector({ onChange }: any) {
        return (
            <div data-testid="framework-selector">
                <button
                    data-testid="select-framework"
                    onClick={() => onChange({ id: 1, name: 'Test Framework' })}
                >
                    Select Framework
                </button>
            </div>
        );
    };
});

jest.mock('../DashboardSelector/DashboardSelector', () => {
    return function MockDashboardSelector({ onChange }: any) {
        return (
            <div data-testid="dashboard-selector">
                <button
                    data-testid="select-dashboard"
                    onClick={() => onChange({ path: '/test/path', label: 'Test Dashboard' })}
                >
                    Select Dashboard
                </button>
            </div>
        );
    };
});

// Mock config
jest.mock('config', () => ({
    getReactRoot: jest.fn(() => document.body),
}));

// Mock actions
const mockDispatch = jest.fn();

jest.mock('cyberrisk/ControlsActions', () => ({
    setActiveSection: jest.fn(),
    setActiveSettingsDialog: jest.fn(),
    addItemToSection: jest.fn(),
    saveMenuItem: jest.fn(),
}));

// Import the mocked functions after mocking
import { setActiveSection, setActiveSettingsDialog, addItemToSection, saveMenuItem } from 'cyberrisk/ControlsActions';

const mockSetActiveSection = setActiveSection as jest.MockedFunction<typeof setActiveSection>;
const mockSetActiveSettingsDialog = setActiveSettingsDialog as jest.MockedFunction<typeof setActiveSettingsDialog>;
const mockAddItemToSection = addItemToSection as jest.MockedFunction<typeof addItemToSection>;
const mockSaveMenuItem = saveMenuItem as jest.MockedFunction<typeof saveMenuItem>;

// Mock ProtechtDictionary global
(global as any).ProtechtDictionary = {
    isInternal: 'false',
};

describe('WorkSpaceItem', () => {
    const mockFrameworkItem: FrameworkItem = {
        id: '1',
        name: 'Test Framework',
        alias: 'Test Alias',
        order: 1,
        itemType: ItemTypes.FRAMEWORK,
        frameworkId: 1,
    };

    const mockAnotherFrameworkItem: FrameworkItem = {
        id: '2',
        name: 'Test Framework 2',
        alias: 'Test Alias 2',
        order: 2,
        itemType: ItemTypes.FRAMEWORK,
        frameworkId: 2,
    };

    const mockDashboardItem: DashboardItem = {
        id: '2',
        name: 'Test Dashboard',
        alias: 'Dashboard Alias',
        order: 2,
        itemType: ItemTypes.DASHBOARD,
        path: 'test/dashboard',
    };

    const mockSections: WorkspaceGroup[] = [
        {
            id: 'section1',
            name: 'Test Section',
            order: 1,
            itemList: [mockFrameworkItem],
        },
    ];

    const defaultContextValue = {
        dispatch: mockDispatch,
        activeMenuItem: mockFrameworkItem,
        sections: mockSections,
        menuItemDialogType: MenuItemDialogType.NEW,
        activeSettingsDialog: null,
        activeSection: mockSections[0],
        itemType: ItemTypes.FRAMEWORK,
        isBusy: false,
        settingsPristine: true,
        getLibrary: jest.fn(),
        defaultWorkspaceName: 'Test Workspace',
        removeSectionItem: jest.fn(),
        removeItem: jest.fn(),
        doUpdateControls: jest.fn(),
        resetDefaults: jest.fn(),
    };

    const renderComponent = (contextOverrides = {}) => {
        const contextValue = { ...defaultContextValue, ...contextOverrides };
        return render(
            <ControlsContext.Provider value={contextValue}>
                <WorkSpaceItem />
            </ControlsContext.Provider>,
        );
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Rendering', () => {
        it('should render the dialog with correct title for framework item', async () => {
            await act(async () => {
                renderComponent();
            });

            expect(screen.getByText('cyberrisk:label.editFrameworkItem')).toBeInTheDocument();
            expect(screen.getByText('frameworks:label.framework')).toBeInTheDocument();
            expect(screen.getByText('cyberrisk:label.label')).toBeInTheDocument();
            expect(screen.getByTestId('button-cancel')).toBeInTheDocument();
            expect(screen.getByTestId('button-confirm')).toBeInTheDocument();
        });

        it('should render the dialog with correct title for dashboard item', async () => {
            await act(async () => {
                renderComponent({
                    activeMenuItem: mockDashboardItem,
                    itemType: ItemTypes.DASHBOARD,
                });
            });

            expect(screen.getByText('cyberrisk:label.editDashboardItem')).toBeInTheDocument();
            expect(screen.getByText('cyberrisk:label.dashboardFolder')).toBeInTheDocument();
            expect(screen.getByText('cyberrisk:label.label')).toBeInTheDocument();
            expect(screen.getByTestId('button-cancel')).toBeInTheDocument();
            expect(screen.getByTestId('button-confirm')).toBeInTheDocument();
        });
    });

    describe('Form Behavior', () => {
        it('should initialize form with framework item data', async () => {
            await act(async () => {
                renderComponent();
            });

            // Check that the form is initialized with framework name
            expect(screen.getByDisplayValue('Test Framework')).toBeInTheDocument();
            expect(screen.getByDisplayValue('Test Alias')).toBeInTheDocument();
        });

        it('should initialize form with dashboard item data', async () => {
            await act(async () => {
                renderComponent({
                    activeMenuItem: mockDashboardItem,
                    itemType: ItemTypes.DASHBOARD,
                });
            });

            // Check that the form is initialized with dashboard path (formatted)
            expect(screen.getByDisplayValue('test | dashboard')).toBeInTheDocument();
            expect(screen.getByDisplayValue('Dashboard Alias')).toBeInTheDocument();
        });

        it('should disable label field when item has NO_ONE role', async () => {
            const itemWithNoOneRole = {
                ...mockFrameworkItem,
                aliasRole: 'NO_ONE',
            };

            await act(async () => {
                renderComponent({
                    activeMenuItem: itemWithNoOneRole,
                });
            });

            const labelInput = screen.getByDisplayValue('Test Alias');
            expect(labelInput).toBeDisabled();
        });

        it('should disable label field when item has INTERNAL role and user is not internal', async () => {
            const itemWithInternalRole = {
                ...mockFrameworkItem,
                aliasRole: 'INTERNAL',
            };

            await act(async () => {
                renderComponent({
                    activeMenuItem: itemWithInternalRole,
                });
            });

            const labelInput = screen.getByDisplayValue('Test Alias');
            expect(labelInput).toBeDisabled();
        });

        it('should enable label field when item has INTERNAL role and user is internal', async () => {
            // Set user as internal
            (global as any).ProtechtDictionary.isInternal = 'true';

            const itemWithInternalRole = {
                ...mockFrameworkItem,
                aliasRole: 'INTERNAL',
            };

            await act(async () => {
                renderComponent({
                    activeMenuItem: itemWithInternalRole,
                });
            });

            const labelInput = screen.getByDisplayValue('Test Alias');
            expect(labelInput).toBeEnabled();

            // Reset to non-internal
            (global as any).ProtechtDictionary.isInternal = 'false';
        });

        it('should enable label field when editing is allowed', async () => {
            await act(async () => {
                renderComponent();
            });

            const labelInput = screen.getByDisplayValue('Test Alias');
            expect(labelInput).toBeEnabled();
        });
    });

    describe('Dialog Actions', () => {
        it('should close dialog when cancel button is clicked', async () => {
            await act(async () => {
                renderComponent();
            });

            await act(async () => {
                fireEvent.click(screen.getByTestId('button-cancel'));
            });

            expect(mockDispatch).toHaveBeenCalledWith(mockSetActiveSection(null));
            expect(mockDispatch).toHaveBeenCalledWith(mockSetActiveSettingsDialog(null));
        });

        it('should have confirm button enabled when form is valid', async () => {
            await act(async () => {
                renderComponent();
            });

            const confirmButton = screen.getByTestId('button-confirm');
            expect(confirmButton).toBeEnabled();
        });
    });

    describe('Form Submission', () => {
        it('should add new framework item when form is submitted in NEW mode', async () => {
            await act(async () => {
                renderComponent({
                    menuItemDialogType: MenuItemDialogType.NEW,
                    activeMenuItem: mockAnotherFrameworkItem,
                });
            });

            await act(async () => {
                const confirmButton = screen.getByTestId('button-confirm');
                fireEvent.click(confirmButton);
            });

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith(
                    mockAddItemToSection(
                        expect.objectContaining({
                            id: '2',
                            name: 'Test Framework 2',
                            alias: 'Test Alias 2',
                            itemType: ItemTypes.FRAMEWORK,
                        }),
                    ),
                );
            });
        });

        it('should save existing framework item when form is submitted in EDIT mode', async () => {
            await act(async () => {
                renderComponent({
                    menuItemDialogType: MenuItemDialogType.EDIT,
                });
            });

            await act(async () => {
                const confirmButton = screen.getByTestId('button-confirm');
                fireEvent.click(confirmButton);
            });

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith(
                    mockSaveMenuItem(
                        expect.objectContaining({
                            id: '1',
                            name: 'Test Framework',
                            alias: 'Test Alias',
                            itemType: ItemTypes.FRAMEWORK,
                            frameworkId: 1,
                        }),
                    ),
                );
            });
        });

        it('should handle dashboard item submission', async () => {
            await act(async () => {
                renderComponent({
                    activeMenuItem: mockDashboardItem,
                    itemType: ItemTypes.DASHBOARD,
                    menuItemDialogType: MenuItemDialogType.NEW,
                });
            });

            await act(async () => {
                const confirmButton = screen.getByTestId('button-confirm');
                fireEvent.click(confirmButton);
            });

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith(
                    mockAddItemToSection(
                        expect.objectContaining({
                            id: '2',
                            name: 'Test Dashboard',
                            alias: 'Dashboard Alias',
                            itemType: ItemTypes.DASHBOARD,
                            path: '/test/dashboard',
                        }),
                    ),
                );
            });
        });

        it('should not submit when active menu item type is not supported', async () => {
            const unsupportedItem = {
                ...mockFrameworkItem,
                itemType: 'UNSUPPORTED' as any,
            };

            await act(async () => {
                renderComponent({
                    activeMenuItem: unsupportedItem,
                    menuItemDialogType: MenuItemDialogType.NEW,
                });
            });

            await act(async () => {
                const confirmButton = screen.getByTestId('button-confirm');
                fireEvent.click(confirmButton);
            });

            // Wait a bit to ensure no action is dispatched
            await new Promise((resolve) => setTimeout(resolve, 100));

            expect(mockDispatch).not.toHaveBeenCalledWith(mockAddItemToSection(expect.any(Object)));
        });

        it('should show duplicate message when validation fails for framework', async () => {
            // Create a duplicate framework item in sections
            const duplicateFrameworkItem: FrameworkItem = {
                id: '3',
                name: 'Test Framework',
                alias: 'Different Alias',
                order: 2,
                itemType: ItemTypes.FRAMEWORK,
                frameworkId: 1, // Same frameworkId as mockFrameworkItem
            };

            const sectionsWithDuplicate: WorkspaceGroup[] = [
                {
                    id: 'section1',
                    name: 'Test Section',
                    order: 1,
                    itemList: [mockFrameworkItem, duplicateFrameworkItem],
                },
            ];

            await act(async () => {
                renderComponent({
                    sections: sectionsWithDuplicate,
                    menuItemDialogType: MenuItemDialogType.NEW,
                });
            });

            await act(async () => {
                const confirmButton = screen.getByTestId('button-confirm');
                fireEvent.click(confirmButton);
            });

            await waitFor(() => {
                expect(screen.getByText('Duplicate framework: Test Framework')).toBeInTheDocument();
            });
        });

        it('should show duplicate message when validation fails for dashboard', async () => {
            // Create a duplicate dashboard item in sections
            const duplicateDashboardItem: DashboardItem = {
                id: '4',
                name: 'Test Dashboard',
                alias: 'Different Alias',
                order: 2,
                itemType: ItemTypes.DASHBOARD,
                path: '/test/dashboard', // Same path as mockDashboardItem
            };

            const sectionsWithDuplicate: WorkspaceGroup[] = [
                {
                    id: 'section1',
                    name: 'Test Section',
                    order: 1,
                    itemList: [mockDashboardItem, duplicateDashboardItem],
                },
            ];

            await act(async () => {
                renderComponent({
                    activeMenuItem: mockDashboardItem,
                    sections: sectionsWithDuplicate,
                    menuItemDialogType: MenuItemDialogType.NEW,
                });
            });

            await act(async () => {
                const confirmButton = screen.getByTestId('button-confirm');
                fireEvent.click(confirmButton);
            });

            await waitFor(() => {
                expect(screen.getByText('Duplicate dashboard: Test Dashboard')).toBeInTheDocument();
            });
        });
    });

    describe('Validation', () => {
        it('should validate config structure for edited items excluding the current item', async () => {
            // Create another framework item with different frameworkId
            const otherFrameworkItem: FrameworkItem = {
                id: '3',
                name: 'Other Framework',
                alias: 'Other Alias',
                order: 2,
                itemType: ItemTypes.FRAMEWORK,
                frameworkId: 2,
            };

            const sectionsWithOtherItem: WorkspaceGroup[] = [
                {
                    id: 'section1',
                    name: 'Test Section',
                    order: 1,
                    itemList: [mockFrameworkItem, otherFrameworkItem],
                },
            ];

            await act(async () => {
                renderComponent({
                    sections: sectionsWithOtherItem,
                    menuItemDialogType: MenuItemDialogType.EDIT,
                });
            });

            await act(async () => {
                const confirmButton = screen.getByTestId('button-confirm');
                fireEvent.click(confirmButton);
            });

            // Should save successfully since the current item is excluded from validation
            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith(mockSaveMenuItem(expect.any(Object)));
            });
        });
    });

    describe('Selector Integration', () => {
        it('should render framework selector when dialog selector is opened for framework items', async () => {
            await act(async () => {
                renderComponent();
            });

            // Find and click the select button to open the selector
            const selectorButton = screen.getByRole('button', { name: 'common:button.select' });

            await act(async () => {
                fireEvent.click(selectorButton);
            });

            await waitFor(() => {
                expect(screen.getByTestId('framework-selector')).toBeInTheDocument();
            });
        });

        it('should render dashboard selector when dialog selector is opened for dashboard items', async () => {
            await act(async () => {
                renderComponent({
                    activeMenuItem: mockDashboardItem,
                    itemType: ItemTypes.DASHBOARD,
                });
            });

            // Find and click the select button to open the selector
            const selectorButton = screen.getByRole('button', { name: 'common:button.select' });

            await act(async () => {
                fireEvent.click(selectorButton);
            });

            await waitFor(() => {
                expect(screen.getByTestId('dashboard-selector')).toBeInTheDocument();
            });
        });
    });

    describe('Utils Integration', () => {
        it('should format dashboard path correctly using getDashboardItemDisplayPath utility', async () => {
            const dashboardWithPath: DashboardItem = {
                ...mockDashboardItem,
                path: 'folder1/folder2/dashboard',
            };

            await act(async () => {
                renderComponent({
                    activeMenuItem: dashboardWithPath,
                });
            });

            // Should display formatted path with pipe separators
            expect(screen.getByDisplayValue('folder1 | folder2 | dashboard')).toBeInTheDocument();
        });

        it('should handle empty dashboard path using getDashboardItemDisplayPath utility', async () => {
            const dashboardWithoutPath: DashboardItem = {
                ...mockDashboardItem,
                path: undefined,
            };

            await act(async () => {
                renderComponent({
                    activeMenuItem: dashboardWithoutPath,
                });
            });

            // Should handle undefined path gracefully
            expect(screen.getByDisplayValue('')).toBeInTheDocument();
        });
    });
});
