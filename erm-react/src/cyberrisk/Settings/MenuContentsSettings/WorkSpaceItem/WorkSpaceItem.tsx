import React, { useCallback, useContext, useState } from 'react';
import { strings } from 'common/utils/i18n';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { addItemToSection, saveMenuItem, setActiveSection, setActiveSettingsDialog } from 'cyberrisk/ControlsActions';
import useForm from 'common/hooks/forms/useForm';
import * as Yup from 'yup';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import Grid from '@mui/material/Grid';
import { DashboardItem, FrameworkItem, FrameworkStubRead, RepositoryNode, WorkspaceGroupItem } from 'api/generated/types';
import Typography from '@mui/material/Typography';
import DialogActions from '@mui/material/DialogActions';
import { getReactRoot } from 'config';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { ItemTypes, MenuItemDialogType } from 'cyberrisk/types';
import {
    cannotEdit,
    getDashboardItemDisplayPath,
    getDashboardPath,
    isFrameworkItem,
    validateConfigStructure,
} from 'cyberrisk/Settings/MenuContentsSettings/utils';
import { useTheme } from '@mui/material/styles';
import { FormProvider } from 'react-hook-form';
import DialogSelectorField from 'common/components/Form/FormFields/DialogSelectorField';
import FrameworkSelector from '../FrameworkSelector/FrameworkSelector';
import DashboardSelector from '../DashboardSelector/DashboardSelector';

type MenuItemDetails = {
    sourceName: string;
    sourceId: string | number;
    label: string;
};

const schema: Yup.AnyObjectSchema = Yup.object({
    sourceName: Yup.string().required(strings('common:validators.requiredSimple')),
    sourceId: Yup.mixed(),
    label: Yup.string().required(strings('common:validators.requiredSimple')),
});

const WorkSpaceItem = () => {
    const theme = useTheme();
    const { dispatch, activeMenuItem, sections, menuItemDialogType } = useContext(ControlsContext);
    const [duplicateMessage, setDuplicateMessage] = useState('');

    const formMethods = useForm<MenuItemDetails>({
        mode: 'onChange',
        schema,
        defaultValues: {
            sourceName: isFrameworkItem(activeMenuItem) ? (activeMenuItem as FrameworkItem).name : getDashboardItemDisplayPath(activeMenuItem) || '',
            label: activeMenuItem?.alias,
        },
    });

    const { handleSubmit, formState, setValue, trigger } = formMethods;
    const { isValid } = formState;

    const onClose = useCallback(() => {
        dispatch(setActiveSection(null));
        dispatch(setActiveSettingsDialog(null));
    }, [dispatch]);

    const onSubmit = useCallback(
        (values: MenuItemDetails) => {
            let menuItem: WorkspaceGroupItem;
            if (isFrameworkItem(activeMenuItem)) {
                menuItem = {
                    ...activeMenuItem,
                    alias: values.label,
                    frameworkId: menuItemDialogType === MenuItemDialogType.EDIT ? Number(values.sourceId) : (activeMenuItem as FrameworkItem).frameworkId,
                    name: menuItemDialogType === MenuItemDialogType.EDIT ? values.sourceName : (activeMenuItem as FrameworkItem).name,
                } as FrameworkItem;
            } else {
                if (activeMenuItem?.itemType === ItemTypes.DASHBOARD) {
                    menuItem = {
                        ...activeMenuItem,
                        alias: values.label,
                    } as DashboardItem;
                } else {
                    return;
                }
            }

            const sameItems = sections
                ?.flatMap((section) => section.itemList)
                .filter((item) => item?.itemType === activeMenuItem?.itemType)
                .filter((item: WorkspaceGroupItem) => !!item);

            if (sameItems && sameItems?.length) {
                if (menuItemDialogType === MenuItemDialogType.NEW) {
                    const res = validateConfigStructure(sameItems as WorkspaceGroupItem[], activeMenuItem!);
                    if (res.valid) {
                        dispatch(addItemToSection(menuItem as WorkspaceGroupItem));
                    } else {
                        setDuplicateMessage(res.message);
                    }
                } else if (menuItemDialogType === MenuItemDialogType.EDIT) {
                    const listWithoutActiveItem = sameItems.filter((item) => activeMenuItem?.id !== item?.id);

                    const res = validateConfigStructure(listWithoutActiveItem as WorkspaceGroupItem[], activeMenuItem!);
                    if (res.valid) {
                        dispatch(saveMenuItem(menuItem as WorkspaceGroupItem));
                        onClose();
                    } else {
                        setDuplicateMessage(res.message);
                    }
                }
            } else {
                dispatch(addItemToSection(menuItem));
            }
        },
        [dispatch, sections, activeMenuItem, menuItemDialogType, onClose],
    );

    const handleDashboardChange = useCallback(
        (dashboard: RepositoryNode) => {
            setValue('sourceName', getDashboardPath(dashboard.label) || '');
            setValue('sourceId', dashboard.path!);
            setDuplicateMessage('');
            void trigger();
        },
        [setValue],
    );

    const handleFrameworkChange = useCallback(
        (framework: FrameworkStubRead) => {
            setValue('sourceName', framework.name!);
            setValue('sourceId', framework.id!);
            setDuplicateMessage('');
            void trigger();
        },
        [setValue, trigger],
    );

    const renderDialog = useCallback(
        (props) => {
            return isFrameworkItem(activeMenuItem) ? (
                <FrameworkSelector
                    {...props}
                    onChange={handleFrameworkChange}
                />
            ) : (
                <DashboardSelector
                    {...props}
                    onChange={handleDashboardChange}
                />
            );
        },
        [activeMenuItem, handleDashboardChange, handleFrameworkChange],
    );

    return (
        <Dialog
            visible={true}
            width={500}
            title={isFrameworkItem(activeMenuItem) ? strings('cyberrisk:label.editFrameworkItem') : strings('cyberrisk:label.editDashboardItem')}
            dialogContainer={getReactRoot()}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('ermMessages:btn_cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        disabled={!isValid}
                        onClick={handleSubmit(onSubmit)}
                        dataTestId="button-confirm"
                    >
                        {strings('ermMessages:btn_okay')}
                    </Button>
                </DialogActions>
            }
        >
            <FormProvider {...formMethods}>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid
                        direction="column"
                        container
                        spacing={2}
                    >
                        <Grid item>
                            <DialogSelectorField
                                name="sourceName"
                                label={isFrameworkItem(activeMenuItem) ? strings('frameworks:label.framework') : strings('cyberrisk:label.dashboardFolder')}
                                clearable={true}
                                renderDialog={renderDialog}
                            />
                        </Grid>
                        <Grid item>
                            <InputField
                                clearable
                                name={'label'}
                                label={strings('cyberrisk:label.label')}
                                disabled={cannotEdit(activeMenuItem?.aliasRole)}
                            />
                        </Grid>
                    </Grid>
                </form>
            </FormProvider>
            <Grid pt="1rem">
                <Typography color={theme.palette.error.main}>{duplicateMessage}</Typography>
            </Grid>
        </Dialog>
    );
};

export default WorkSpaceItem;
