import { FrameworkItem, RegisterItem, WorkspaceGroup, WorkspaceGroupItem } from 'api/generated/types';
import { replaceItemById, reAssignOrder, validateConfigStructure, isRegisterItem, getViewId, mapDashboard, cannotEdit } from './utils';
import { ItemTypes } from 'cyberrisk/types';
import { NodeType, RepositoryNode } from 'rolesAndPermissions/types';

describe('Utility function tests', () => {
    describe('replaceItemById', () => {
        const sampleArray = [
            { id: 1, name: 'Item1', order: 1 },
            { id: 2, name: 'Item2', order: 2 },
        ];
        const newItem = { id: 2, name: 'NewItem', order: 2 };

        it('should replace an item by id', () => {
            const result = replaceItemById(sampleArray, 2, newItem);
            expect(result).toEqual([
                { id: 1, name: 'Item1', order: 1 },
                { id: 2, name: 'NewItem', order: 2 },
            ]);
        });

        it('should not modify the original array', () => {
            replaceItemById(sampleArray, 2, newItem);
            expect(sampleArray).toEqual([
                { id: 1, name: 'Item1', order: 1 },
                { id: 2, name: 'Item2', order: 2 },
            ]);
        });
    });

    describe('reAssignOrder', () => {
        const items = [
            { id: '1', name: 'First', order: 3 },
            { id: '2', name: 'Second', order: 1 },
            { id: '3', name: 'Third', order: 2 },
        ] as WorkspaceGroup[];

        it('should reassign order starting from 1', () => {
            const result = reAssignOrder(items);
            expect(result).toEqual([
                { id: '1', name: 'First', order: 1 },
                { id: '2', name: 'Second', order: 2 },
                { id: '3', name: 'Third', order: 3 },
            ]);
        });

        it('should not modify the original array', () => {
            reAssignOrder(items);
            expect(items).toEqual([
                { id: '1', name: 'First', order: 3 },
                { id: '2', name: 'Second', order: 1 },
                { id: '3', name: 'Third', order: 2 },
            ]);
        });
    });

    describe('validateConfigStructure', () => {
        const mockStrings = jest.fn();
        jest.mock('common/utils/i18n', () => ({
            strings: mockStrings,
        }));

        beforeEach(() => {
            mockStrings.mockClear();
            mockStrings.mockImplementation((key, params) => {
                if (key === 'cyberrisk:error.duplicateRegister') {
                    return `Duplicate found for register with name ${params.name} and view ${params.view}`;
                }
                if (key === 'cyberrisk:error.duplicateFramework') {
                    return `Duplicate found for framework with name ${params.name}`;
                }
                if (key === 'cyberrisk:error.empty') {
                    return 'empty';
                }
                return key;
            });
        });

        describe('Register item validation', () => {
            it('returns valid if there are no duplicates', () => {
                const itemList = [
                    { id: '1', viewId: 101, itemType: ItemTypes.REGISTER, registerId: 1 },
                    { id: '2', viewId: 102, itemType: ItemTypes.REGISTER, registerId: 1 },
                ] as RegisterItem[];
                const result = validateConfigStructure(
                    itemList,
                    {
                        id: '3',
                        itemType: ItemTypes.REGISTER,
                        registerId: 2,
                        order: 0,
                    },
                    { id: 303 },
                );
                expect(result).toEqual({ valid: true, message: '' });
            });

            it('returns invalid if there is a duplicate between parameters and itemList', () => {
                const itemList = [
                    { id: '1', viewId: 101, itemType: ItemTypes.REGISTER, registerId: 1, name: 'Register 1', order: 0 },
                    { id: '2', viewId: 102, itemType: ItemTypes.REGISTER, registerId: 2, name: 'Register 2', order: 0 },
                ] as RegisterItem[];
                const result = validateConfigStructure(
                    itemList,
                    {
                        id: '3',
                        itemType: ItemTypes.REGISTER,
                        registerId: 1,
                        name: 'Register 1',
                        order: 0,
                    },
                    { id: 101, name: 'View 101' },
                );
                expect(result).toEqual({ valid: false, message: 'Duplicate found for register with name Register 1 and view View 101' });
            });

            it('returns invalid if there are duplicates within the itemList', () => {
                const itemList = [
                    { id: '1', viewId: 101, itemType: ItemTypes.REGISTER, name: 'Register 1', registerId: 1 },
                    { id: '2', viewId: 101, itemType: ItemTypes.REGISTER, name: 'Register 1', registerId: 1 },
                ] as RegisterItem[];
                const result = validateConfigStructure(
                    itemList,
                    { id: '3', itemType: ItemTypes.REGISTER, registerId: 2, order: 0 },
                    {
                        id: 102,
                        name: 'View 102',
                    },
                );
                expect(result).toEqual({ valid: false, message: expect.any(String) });
            });

            it('handles falsy viewId values by substituting "noViewId"', () => {
                const itemList = [
                    { id: '1', itemType: ItemTypes.REGISTER, registerId: 1 },
                    { id: '2', itemType: ItemTypes.REGISTER, registerId: 2 },
                ] as RegisterItem[];
                const result = validateConfigStructure(itemList, { id: '3', itemType: ItemTypes.REGISTER, registerId: 1, order: 0 });
                expect(result).toEqual({ valid: false, message: expect.any(String) });
            });

            it('uses view parameter for key generation when provided', () => {
                const itemList = [{ id: '1', viewId: 101, itemType: ItemTypes.REGISTER, registerId: 1 }] as RegisterItem[];
                const result = validateConfigStructure(
                    itemList,
                    { id: '2', itemType: ItemTypes.REGISTER, registerId: 1, name: 'Test Register', order: 0 },
                    { id: 102, name: 'Different View' },
                );
                expect(result).toEqual({ valid: true, message: '' });
            });

            it('handles undefined registerId', () => {
                const itemList = [{ id: '1', viewId: 101, itemType: ItemTypes.REGISTER }] as RegisterItem[];
                const result = validateConfigStructure(itemList, { id: '2', itemType: ItemTypes.REGISTER, order: 0 }, { id: 101 });
                expect(result).toEqual({ valid: true, message: '' });
            });
        });

        describe('Framework item validation', () => {
            it('returns valid if there are no duplicate frameworks', () => {
                const itemList = [
                    { id: '1', itemType: ItemTypes.FRAMEWORK, frameworkId: 1 },
                    { id: '2', itemType: ItemTypes.FRAMEWORK, frameworkId: 2 },
                ] as FrameworkItem[];
                const result = validateConfigStructure(itemList, { id: '3', itemType: ItemTypes.FRAMEWORK, frameworkId: 3, order: 0 });
                expect(result).toEqual({ valid: true, message: '' });
            });

            it('returns invalid if there is a duplicate framework', () => {
                const itemList = [
                    { id: '1', itemType: ItemTypes.FRAMEWORK, frameworkId: 1, name: 'Framework 1' },
                    { id: '2', itemType: ItemTypes.FRAMEWORK, frameworkId: 2, name: 'Framework 2' },
                ] as FrameworkItem[];
                const result = validateConfigStructure(itemList, {
                    id: '3',
                    itemType: ItemTypes.FRAMEWORK,
                    frameworkId: 1,
                    name: 'Framework 1',
                    order: 0,
                });
                expect(result).toEqual({ valid: false, message: 'Duplicate found for framework with name Framework 1' });
            });

            it('handles duplicates within framework itemList', () => {
                const itemList = [
                    { id: '1', itemType: ItemTypes.FRAMEWORK, frameworkId: 1 },
                    { id: '2', itemType: ItemTypes.FRAMEWORK, frameworkId: 1 },
                ] as FrameworkItem[];
                const result = validateConfigStructure(itemList, { id: '3', itemType: ItemTypes.FRAMEWORK, frameworkId: 2, order: 0 });
                expect(result).toEqual({ valid: false, message: expect.any(String) });
            });
        });

        describe('Dashboard item validation', () => {
            it('returns valid if there are no duplicate dashboards', () => {
                const itemList = [
                    { id: '1', itemType: ItemTypes.DASHBOARD, path: '/dashboard1' },
                    { id: '2', itemType: ItemTypes.DASHBOARD, path: '/dashboard2' },
                ] as WorkspaceGroupItem[];
                const result = validateConfigStructure(itemList, { id: '3', itemType: ItemTypes.DASHBOARD, path: '/dashboard3', order: 0 });
                expect(result).toEqual({ valid: true, message: '' });
            });

            it('returns invalid if there is a duplicate dashboard path', () => {
                const itemList = [
                    { id: '1', itemType: ItemTypes.DASHBOARD, path: '/dashboard1' },
                    { id: '2', itemType: ItemTypes.DASHBOARD, path: '/dashboard2' },
                ] as WorkspaceGroupItem[];
                const result = validateConfigStructure(itemList, {
                    id: '3',
                    itemType: ItemTypes.DASHBOARD,
                    path: '/dashboard1',
                    name: 'Dashboard1',
                    order: 0,
                });
                expect(result).toEqual({ valid: false, message: 'Duplicate found for dashboard with name Dashboard1' });
            });

            it('handles undefined path', () => {
                const itemList = [{ id: '1', itemType: ItemTypes.DASHBOARD }] as WorkspaceGroupItem[];
                const result = validateConfigStructure(itemList, { id: '2', itemType: ItemTypes.DASHBOARD, order: 0 });
                expect(result).toEqual({ valid: true, message: '' });
            });
        });

        describe('Mixed item types validation', () => {
            it('validates different item types independently', () => {
                const itemList = [
                    { id: '1', itemType: ItemTypes.REGISTER, registerId: 1, viewId: 101 },
                    { id: '2', itemType: ItemTypes.FRAMEWORK, frameworkId: 1 },
                    { id: '3', itemType: ItemTypes.DASHBOARD, path: '/dashboard1' },
                ] as WorkspaceGroupItem[];
                const result = validateConfigStructure(itemList, {
                    id: '4',
                    itemType: ItemTypes.REGISTER,
                    registerId: 2,
                    viewId: 102,
                    order: 0,
                });
                expect(result).toEqual({ valid: true, message: '' });
            });
        });

        describe('Edge cases', () => {
            it('handles empty itemList', () => {
                const result = validateConfigStructure([], { id: '1', itemType: ItemTypes.REGISTER, registerId: 1, order: 0 });
                expect(result).toEqual({ valid: true, message: '' });
            });

            it('handles null/undefined items in list', () => {
                const itemList = [null, undefined, { id: '1', itemType: ItemTypes.REGISTER, registerId: 1 }] as WorkspaceGroupItem[];
                const result = validateConfigStructure(itemList, {
                    id: '2',
                    itemType: ItemTypes.REGISTER,
                    registerId: 2,
                    name: 'Register 1',
                    order: 0,
                });
                expect(result).toEqual({ valid: true, message: '' });
            });
        });
    });

    describe('isRegisterItem', () => {
        it('should return true for register items', () => {
            const registerItem: WorkspaceGroupItem = {
                id: '1',
                name: 'Test Register',
                itemType: ItemTypes.REGISTER,
                order: 1,
                viewId: 123,
            } as RegisterItem;

            expect(isRegisterItem(registerItem)).toBe(true);
        });

        it('should return false for framework items', () => {
            const frameworkItem: WorkspaceGroupItem = {
                id: '1',
                name: 'Test Framework',
                itemType: ItemTypes.FRAMEWORK,
                order: 1,
            } as FrameworkItem;

            expect(isRegisterItem(frameworkItem)).toBe(false);
        });

        it('should return false for dashboard items', () => {
            const dashboardItem: WorkspaceGroupItem = {
                id: '1',
                name: 'Test Dashboard',
                itemType: ItemTypes.DASHBOARD,
                order: 1,
            } as WorkspaceGroupItem;

            expect(isRegisterItem(dashboardItem)).toBe(false);
        });

        it('should return false for null item', () => {
            expect(isRegisterItem(null)).toBe(false);
        });

        it('should return false for undefined item', () => {
            expect(isRegisterItem(undefined)).toBe(false);
        });

        it('should return false for item without itemType', () => {
            const itemWithoutType = {
                id: '1',
                name: 'Test Item',
                order: 1,
            } as WorkspaceGroupItem;

            expect(isRegisterItem(itemWithoutType)).toBe(false);
        });
    });

    describe('getViewId', () => {
        it('should return viewId as string for register item with numeric viewId', () => {
            const registerItem: RegisterItem = {
                id: '1',
                name: 'Test Register',
                itemType: ItemTypes.REGISTER,
                order: 1,
                viewId: 123,
            };

            expect(getViewId(registerItem)).toBe('123');
        });

        it('should return undefined for register item without viewId', () => {
            const registerItem: RegisterItem = {
                id: '1',
                name: 'Test Register',
                itemType: ItemTypes.REGISTER,
                order: 1,
            };

            expect(getViewId(registerItem)).toBeUndefined();
        });

        it('should return undefined for register item with undefined viewId', () => {
            const registerItem: RegisterItem = {
                id: '1',
                name: 'Test Register',
                itemType: ItemTypes.REGISTER,
                order: 1,
                viewId: undefined,
            };

            expect(getViewId(registerItem)).toBeUndefined();
        });

        it('should return undefined for framework item', () => {
            const frameworkItem: FrameworkItem = {
                id: '1',
                name: 'Test Framework',
                itemType: ItemTypes.FRAMEWORK,
                order: 1,
            };

            expect(getViewId(frameworkItem)).toBeUndefined();
        });

        it('should return undefined for dashboard item', () => {
            const dashboardItem: WorkspaceGroupItem = {
                id: '1',
                name: 'Test Dashboard',
                itemType: ItemTypes.DASHBOARD,
                order: 1,
            };

            expect(getViewId(dashboardItem)).toBeUndefined();
        });

        it('should return undefined for null item', () => {
            expect(getViewId(null)).toBeUndefined();
        });

        it('should return undefined for undefined item', () => {
            expect(getViewId(undefined)).toBeUndefined();
        });

        it('should handle zero viewId correctly', () => {
            const registerItem: RegisterItem = {
                id: '1',
                name: 'Test Register',
                itemType: ItemTypes.REGISTER,
                order: 1,
                viewId: 0,
            };

            expect(getViewId(registerItem)).toBe('0');
        });
    });

    describe('mapDashboard', () => {
        it('maps a simple dashboard node correctly', () => {
            const input: RepositoryNode = {
                label: 'Test Dashboard',
                path: '/test/path',
                type: NodeType.FOLDER,
                subnodes: [],
            };

            const result = mapDashboard(input);

            expect(result).toEqual({
                label: 'Test Dashboard',
                path: '/test/path',
                type: NodeType.FOLDER,
                subnodes: [],
            });
        });

        it('handles undefined or null values', () => {
            const input: RepositoryNode = {
                label: '',
                path: '',
                type: NodeType.FOLDER,
                subnodes: undefined,
            };

            const result = mapDashboard(input);

            expect(result).toEqual({
                label: '',
                path: '',
                type: NodeType.FOLDER,
                subnodes: undefined,
            });
        });
    });

    describe('cannotEdit', () => {
        // Mock ProtechtDictionary global
        const originalProtechtDictionary = (global as any).ProtechtDictionary;

        afterEach(() => {
            // Restore original ProtechtDictionary after each test
            (global as any).ProtechtDictionary = originalProtechtDictionary;
        });

        it('should return true when role is NO_ONE', () => {
            (global as any).ProtechtDictionary = { isInternal: 'true' };

            expect(cannotEdit('NO_ONE')).toBe(true);
        });

        it('should return true when role is INTERNAL and ProtechtDictionary.isInternal is not "true"', () => {
            (global as any).ProtechtDictionary = { isInternal: 'false' };

            expect(cannotEdit('INTERNAL')).toBe(true);
        });

        it('should return true when role is INTERNAL and ProtechtDictionary.isInternal is undefined', () => {
            (global as any).ProtechtDictionary = { isInternal: undefined };

            expect(cannotEdit('INTERNAL')).toBe(true);
        });

        it('should return true when role is INTERNAL and ProtechtDictionary.isInternal is null', () => {
            (global as any).ProtechtDictionary = { isInternal: null };

            expect(cannotEdit('INTERNAL')).toBe(true);
        });

        it('should return true when role is INTERNAL and ProtechtDictionary.isInternal is empty string', () => {
            (global as any).ProtechtDictionary = { isInternal: '' };

            expect(cannotEdit('INTERNAL')).toBe(true);
        });

        it('should return false when role is INTERNAL and ProtechtDictionary.isInternal is "true"', () => {
            (global as any).ProtechtDictionary = { isInternal: 'true' };

            expect(cannotEdit('INTERNAL')).toBe(false);
        });

        it('should return false when role is ADMIN', () => {
            (global as any).ProtechtDictionary = { isInternal: 'false' };

            expect(cannotEdit('ADMIN')).toBe(false);
        });

        it('should return false when role is USER', () => {
            (global as any).ProtechtDictionary = { isInternal: 'true' };

            expect(cannotEdit('USER')).toBe(false);
        });

        it('should return false when role is undefined', () => {
            (global as any).ProtechtDictionary = { isInternal: 'false' };

            expect(cannotEdit(undefined)).toBe(false);
        });

        it('should return false when role is null', () => {
            (global as any).ProtechtDictionary = { isInternal: 'false' };

            expect(cannotEdit(null as any)).toBe(false);
        });

        it('should return false when role is empty string', () => {
            (global as any).ProtechtDictionary = { isInternal: 'false' };

            expect(cannotEdit('')).toBe(false);
        });

        it('should handle missing ProtechtDictionary gracefully', () => {
            (global as any).ProtechtDictionary = undefined;

            // This should throw an error since the function doesn't handle undefined ProtechtDictionary
            expect(() => cannotEdit('INTERNAL')).toThrow();
        });

        it('should handle ProtechtDictionary without isInternal property', () => {
            (global as any).ProtechtDictionary = {};

            expect(cannotEdit('INTERNAL')).toBe(true);
        });
    });
});
