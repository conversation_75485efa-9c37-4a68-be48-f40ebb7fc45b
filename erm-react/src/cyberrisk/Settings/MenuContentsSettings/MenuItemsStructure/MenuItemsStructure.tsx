import MainLayout from 'common/layouts/MainLayout';
import SettingsLayout from 'common/layouts/SettingsLayout';
import Box from '@mui/material/Box';
import { SettingsSection } from 'common/layouts/SettingsLayout';
import Typography from '@mui/material/Typography';
import { DragHandle, SortableItem, SortableList } from '@protecht/ui-library/library/components/SortableList';
import { WorkspaceGroup, WorkspaceGroupItem } from 'api/generated/types';
import SpaceBetween from '@protecht/ui-library/library/components/SpaceBetween';
import Button from '@protecht/ui-library/library/components/Button';
import { strings } from 'common/utils/i18n';
import React, { FC, useContext } from 'react';
import { ItemContent, styling, StyledIconButton } from 'cyberrisk/Settings/Styled';
import { useTheme } from '@mui/material/styles';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import {
    handleItemReorder,
    moveItem,
    moveSection,
    removeItem,
    removeSection,
    setActiveMenuItem,
    setActiveSection,
    setActiveSettingsDialog,
} from 'cyberrisk/ControlsActions';
import { ItemTypes, MenuItemDialogType, SettingsDialogs } from 'cyberrisk/types';
import { Add, Edit, ClearOutlined, ArrowUp, ArrowDown } from '@protecht/ui-library/library/components/SVGIcons';
import { TypeToDialogMap } from '../ItemTypeSelectorDialog/ItemTypeSelectorDialog';
import { cannotEdit, getDashboardItemDisplayPath } from '../utils';

type Props = {
    sectionList: WorkspaceGroup[];
};

const MenuItemsSections: FC<Props> = ({ sectionList }) => {
    const theme = useTheme();
    const { dispatch, fixedLevel } = useContext(ControlsContext);

    return (
        <MainLayout>
            <SettingsLayout>
                <Box sx={{ width: ['100%', '80%', '60%'] }}>
                    {sectionList.map((sectionItem, sectionIndex) => (
                        <Box
                            key={`${sectionIndex}-${sectionItem.id}`}
                            sx={{
                                display: 'flex',
                                marginBottom: '1rem',
                            }}
                        >
                            <SettingsSection width="100%">
                                <Typography
                                    paddingBottom="1rem"
                                    variant="h2"
                                >
                                    {sectionItem.name}
                                </Typography>

                                <Box>
                                    {sectionItem.itemList && (
                                        <SortableList
                                            items={sectionItem.itemList as Required<WorkspaceGroupItem>[]}
                                            onChange={(_from, _to, reorderedItems) => dispatch(handleItemReorder(sectionItem, reorderedItems))}
                                            renderItem={(item, index) => (
                                                <SortableItem
                                                    key={`${index!}-${item.id}`}
                                                    id={item.id}
                                                    styles={styling}
                                                >
                                                    <ItemContent>
                                                        <SpaceBetween>
                                                            <Typography
                                                                noWrap
                                                                ml="10px"
                                                                variant="body1"
                                                                fontWeight="600"
                                                                lineHeight={2}
                                                            >
                                                                {item.alias}
                                                            </Typography>
                                                            <Box
                                                                sx={{
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                }}
                                                            >
                                                                <StyledIconButton
                                                                    color="error"
                                                                    onClick={() => dispatch(removeItem(sectionItem, item))}
                                                                    disabled={cannotEdit(sectionItem.contentRole)}
                                                                    data-testid="item-delete-button"
                                                                >
                                                                    <ClearOutlined
                                                                        height="20px"
                                                                        width="20px"
                                                                    />
                                                                </StyledIconButton>
                                                                <StyledIconButton
                                                                    color="primary"
                                                                    onClick={() => {
                                                                        dispatch(setActiveMenuItem(item, MenuItemDialogType.EDIT));
                                                                        dispatch(setActiveSection(sectionItem));
                                                                        dispatch(setActiveSettingsDialog(SettingsDialogs.ITEM_DETAILS));
                                                                    }}
                                                                    data-testid="item-edit-button"
                                                                >
                                                                    <Edit
                                                                        height="20px"
                                                                        width="20px"
                                                                    />
                                                                </StyledIconButton>
                                                                <StyledIconButton
                                                                    color={index === 0 ? 'inherit' : 'primary'}
                                                                    onClick={() => dispatch(moveItem(sectionItem, item, 'UP'))}
                                                                    disabled={
                                                                        index === 0 || Boolean(sectionItem.fixedLevel && index! <= sectionItem.fixedLevel)
                                                                    }
                                                                    data-testid="item-move-up-button"
                                                                >
                                                                    <ArrowUp
                                                                        height="20px"
                                                                        width="20px"
                                                                    />
                                                                </StyledIconButton>
                                                                <StyledIconButton
                                                                    color={index! + 1 === sectionItem?.itemList?.length ? 'inherit' : 'primary'}
                                                                    onClick={() => dispatch(moveItem(sectionItem, item, 'DOWN'))}
                                                                    disabled={Boolean(
                                                                        index! + 1 === sectionItem?.itemList?.length ||
                                                                            (sectionItem.fixedLevel && index! < sectionItem.fixedLevel),
                                                                    )}
                                                                    data-testid="item-move-down-button"
                                                                >
                                                                    <ArrowDown
                                                                        height="20px"
                                                                        width="20px"
                                                                    />
                                                                </StyledIconButton>
                                                            </Box>
                                                        </SpaceBetween>
                                                        <Typography
                                                            noWrap
                                                            variant="body1"
                                                            ml="10px"
                                                            lineHeight="1.5"
                                                            color={theme.palette.protechtGrey?.grey_164}
                                                        >
                                                            {item.itemType === ItemTypes.DASHBOARD ? getDashboardItemDisplayPath(item) : item.name}
                                                        </Typography>
                                                    </ItemContent>
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        {(!sectionItem.fixedLevel || index! > sectionItem.fixedLevel) && <DragHandle />}
                                                    </Box>
                                                </SortableItem>
                                            )}
                                        />
                                    )}
                                    <Button
                                        size="large"
                                        sx={{
                                            marginTop: '23px',
                                        }}
                                        startIcon={<Add />}
                                        variant="secondary"
                                        dataTestId="button-add-items"
                                        onClick={() => {
                                            dispatch(setActiveSection(sectionItem));
                                            dispatch(setActiveMenuItem(null, MenuItemDialogType.NEW));

                                            // If section has exactly one item type restriction, navigate directly to the related selector dialog
                                            if (sectionItem.itemTypesRestriction && sectionItem.itemTypesRestriction.length === 1) {
                                                const itemType = sectionItem.itemTypesRestriction[0];
                                                dispatch(setActiveSettingsDialog(TypeToDialogMap[itemType as ItemTypes]));
                                            } else {
                                                dispatch(setActiveSettingsDialog(SettingsDialogs.ITEM_TYPE_SELECTOR));
                                            }
                                        }}
                                        disabled={cannotEdit(sectionItem.contentRole)}
                                    >
                                        {strings('cyberrisk:button.addItems')}
                                    </Button>
                                </Box>
                            </SettingsSection>
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    flexDirection: 'column',
                                    paddingLeft: '16px',
                                }}
                            >
                                <StyledIconButton
                                    sx={{ marginBottom: '10px' }}
                                    color="error"
                                    disabled={cannotEdit(sectionItem.contentRole)}
                                    onClick={() => dispatch(removeSection(sectionItem))}
                                    data-testid="section-delete-button"
                                >
                                    <ClearOutlined
                                        height="20px"
                                        width="20px"
                                    />
                                </StyledIconButton>
                                <StyledIconButton
                                    sx={{ marginBottom: '10px' }}
                                    color="primary"
                                    onClick={() => {
                                        dispatch(setActiveSettingsDialog(SettingsDialogs.GROUP_NAMING));
                                        dispatch(setActiveSection(sectionItem));
                                    }}
                                    disabled={cannotEdit(sectionItem.labelRole)}
                                    data-testid="section-edit-button"
                                >
                                    <Edit
                                        height="20px"
                                        width="20px"
                                    />
                                </StyledIconButton>
                                <StyledIconButton
                                    sx={{ marginBottom: '10px' }}
                                    color={sectionIndex === 0 ? 'inherit' : 'primary'}
                                    onClick={() => dispatch(moveSection(sectionItem, 'UP'))}
                                    disabled={sectionIndex === 0 || Boolean(fixedLevel !== undefined && sectionIndex! <= fixedLevel)}
                                    data-testid="section-move-up-button"
                                >
                                    <ArrowUp
                                        height="20px"
                                        width="20px"
                                    />
                                </StyledIconButton>
                                <StyledIconButton
                                    sx={{ marginBottom: '10px' }}
                                    color={sectionIndex! + 1 === sectionList?.length ? 'inherit' : 'primary'}
                                    onClick={() => dispatch(moveSection(sectionItem, 'DOWN'))}
                                    disabled={sectionIndex! + 1 === sectionList?.length || Boolean(fixedLevel !== undefined && sectionIndex! <= fixedLevel)}
                                    data-testid="section-move-down-button"
                                >
                                    <ArrowDown
                                        height="20px"
                                        width="20px"
                                    />
                                </StyledIconButton>
                            </Box>
                        </Box>
                    ))}
                </Box>
            </SettingsLayout>
        </MainLayout>
    );
};

export default MenuItemsSections;
