// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MenuContentsSettings renders the component with the correct heading 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorSecondary MuiIconButton-sizeMedium css-enqixr-MuiButtonBase-root-MuiIconButton-root"
            data-testid="back-button"
            tabindex="0"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-arrow-left "
              color="#1B4AD5"
              data-icon="arrow-left"
              data-prefix="fas"
              focusable="false"
              role="img"
              style="font-size: 22px;"
              viewBox="0 0 448 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"
                fill="currentColor"
              />
            </svg>
          </button>
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="menu-contents-heading"
          >
            Workspace Contents
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-cancel"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Cancel
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-save"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-25d70a"
          >
            <div
              class="MuiBox-root css-ifr8pt"
            >
              <div
                class="MuiBox-root css-1k6upjm"
              >
                <h2
                  class="MuiTypography-root MuiTypography-h2 css-z0z5yv-MuiTypography-root"
                >
                  Section 1
                </h2>
                <div
                  class="MuiBox-root css-0"
                >
                  <ul
                    class="css-cuxljv"
                  >
                    <li
                      class="css-1qhla7d"
                    >
                      <div
                        class="css-xuack"
                      >
                        <div
                          class="css-1ufrzn0"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-8str7f-MuiTypography-root"
                          >
                            Item 1
                          </p>
                          <div
                            class="MuiBox-root css-70qvj9"
                          >
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorError MuiIconButton-sizeMedium css-1xe21x0-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-delete-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="clear-outlined"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M12 2C6.47581 2 2 6.47581 2 12C2 17.5242 6.47581 22 12 22C17.5242 22 22 17.5242 22 12C22 6.47581 17.5242 2 12 2ZM12 20.0645C7.54435 20.0645 3.93548 16.4556 3.93548 12C3.93548 7.54435 7.54435 3.93548 12 3.93548C16.4556 3.93548 20.0645 7.54435 20.0645 12C20.0645 16.4556 16.4556 20.0645 12 20.0645ZM16.1048 9.49194L13.5968 12L16.1048 14.5081C16.2944 14.6976 16.2944 15.004 16.1048 15.1935L15.1935 16.1048C15.004 16.2944 14.6976 16.2944 14.5081 16.1048L12 13.5968L9.49194 16.1048C9.30242 16.2944 8.99597 16.2944 8.80645 16.1048L7.89516 15.1935C7.70565 15.004 7.70565 14.6976 7.89516 14.5081L10.4032 12L7.89516 9.49194C7.70565 9.30242 7.70565 8.99597 7.89516 8.80645L8.80645 7.89516C8.99597 7.70565 9.30242 7.70565 9.49194 7.89516L12 10.4032L14.5081 7.89516C14.6976 7.70565 15.004 7.70565 15.1935 7.89516L16.1048 8.80645C16.2944 8.99597 16.2944 9.30242 16.1048 9.49194Z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-emlmg2-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-edit-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="edit"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M4.9 19.245h1.33l8.194-8.194-1.33-1.33L4.9 17.915zm13.585-9.571-4.037-3.99L17.13 3l4.014 4.014zM3 21.145v-4.037l10.07-10.07 4.038 4.037-10.07 10.07z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-colorInherit MuiIconButton-sizeMedium css-dox7m0-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-move-up-button"
                              disabled=""
                              tabindex="-1"
                              type="button"
                            >
                              <svg
                                data-icon="arrow-up"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="m4 12 1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-emlmg2-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-move-down-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="arrow-down"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="m20 12-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <p
                          class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-2t3842-MuiTypography-root"
                        >
                          Register 1
                        </p>
                      </div>
                      <div
                        class="MuiBox-root css-70qvj9"
                      >
                        <button
                          aria-describedby="DndDescribedBy-0"
                          aria-disabled="false"
                          aria-roledescription="sortable"
                          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                          role="button"
                          tabindex="0"
                          type="button"
                        >
                          <svg
                            aria-hidden="true"
                            class="svg-inline--fa fa-bars "
                            data-icon="bars"
                            data-prefix="far"
                            focusable="false"
                            role="img"
                            viewBox="0 0 448 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                              fill="currentColor"
                            />
                          </svg>
                        </button>
                      </div>
                    </li>
                    <li
                      class="css-1qhla7d"
                    >
                      <div
                        class="css-xuack"
                      >
                        <div
                          class="css-1ufrzn0"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-8str7f-MuiTypography-root"
                          >
                            Item 2
                          </p>
                          <div
                            class="MuiBox-root css-70qvj9"
                          >
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorError MuiIconButton-sizeMedium css-1xe21x0-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-delete-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="clear-outlined"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M12 2C6.47581 2 2 6.47581 2 12C2 17.5242 6.47581 22 12 22C17.5242 22 22 17.5242 22 12C22 6.47581 17.5242 2 12 2ZM12 20.0645C7.54435 20.0645 3.93548 16.4556 3.93548 12C3.93548 7.54435 7.54435 3.93548 12 3.93548C16.4556 3.93548 20.0645 7.54435 20.0645 12C20.0645 16.4556 16.4556 20.0645 12 20.0645ZM16.1048 9.49194L13.5968 12L16.1048 14.5081C16.2944 14.6976 16.2944 15.004 16.1048 15.1935L15.1935 16.1048C15.004 16.2944 14.6976 16.2944 14.5081 16.1048L12 13.5968L9.49194 16.1048C9.30242 16.2944 8.99597 16.2944 8.80645 16.1048L7.89516 15.1935C7.70565 15.004 7.70565 14.6976 7.89516 14.5081L10.4032 12L7.89516 9.49194C7.70565 9.30242 7.70565 8.99597 7.89516 8.80645L8.80645 7.89516C8.99597 7.70565 9.30242 7.70565 9.49194 7.89516L12 10.4032L14.5081 7.89516C14.6976 7.70565 15.004 7.70565 15.1935 7.89516L16.1048 8.80645C16.2944 8.99597 16.2944 9.30242 16.1048 9.49194Z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-emlmg2-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-edit-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="edit"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M4.9 19.245h1.33l8.194-8.194-1.33-1.33L4.9 17.915zm13.585-9.571-4.037-3.99L17.13 3l4.014 4.014zM3 21.145v-4.037l10.07-10.07 4.038 4.037-10.07 10.07z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-emlmg2-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-move-up-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="arrow-up"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="m4 12 1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-emlmg2-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-move-down-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="arrow-down"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="m20 12-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <p
                          class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-2t3842-MuiTypography-root"
                        >
                          Register 2
                        </p>
                      </div>
                      <div
                        class="MuiBox-root css-70qvj9"
                      >
                        <button
                          aria-describedby="DndDescribedBy-0"
                          aria-disabled="false"
                          aria-roledescription="sortable"
                          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                          role="button"
                          tabindex="0"
                          type="button"
                        >
                          <svg
                            aria-hidden="true"
                            class="svg-inline--fa fa-bars "
                            data-icon="bars"
                            data-prefix="far"
                            focusable="false"
                            role="img"
                            viewBox="0 0 448 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                              fill="currentColor"
                            />
                          </svg>
                        </button>
                      </div>
                    </li>
                    <li
                      class="css-1qhla7d"
                    >
                      <div
                        class="css-xuack"
                      >
                        <div
                          class="css-1ufrzn0"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-8str7f-MuiTypography-root"
                          >
                            Item 3
                          </p>
                          <div
                            class="MuiBox-root css-70qvj9"
                          >
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorError MuiIconButton-sizeMedium css-1xe21x0-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-delete-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="clear-outlined"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M12 2C6.47581 2 2 6.47581 2 12C2 17.5242 6.47581 22 12 22C17.5242 22 22 17.5242 22 12C22 6.47581 17.5242 2 12 2ZM12 20.0645C7.54435 20.0645 3.93548 16.4556 3.93548 12C3.93548 7.54435 7.54435 3.93548 12 3.93548C16.4556 3.93548 20.0645 7.54435 20.0645 12C20.0645 16.4556 16.4556 20.0645 12 20.0645ZM16.1048 9.49194L13.5968 12L16.1048 14.5081C16.2944 14.6976 16.2944 15.004 16.1048 15.1935L15.1935 16.1048C15.004 16.2944 14.6976 16.2944 14.5081 16.1048L12 13.5968L9.49194 16.1048C9.30242 16.2944 8.99597 16.2944 8.80645 16.1048L7.89516 15.1935C7.70565 15.004 7.70565 14.6976 7.89516 14.5081L10.4032 12L7.89516 9.49194C7.70565 9.30242 7.70565 8.99597 7.89516 8.80645L8.80645 7.89516C8.99597 7.70565 9.30242 7.70565 9.49194 7.89516L12 10.4032L14.5081 7.89516C14.6976 7.70565 15.004 7.70565 15.1935 7.89516L16.1048 8.80645C16.2944 8.99597 16.2944 9.30242 16.1048 9.49194Z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-emlmg2-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-edit-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="edit"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M4.9 19.245h1.33l8.194-8.194-1.33-1.33L4.9 17.915zm13.585-9.571-4.037-3.99L17.13 3l4.014 4.014zM3 21.145v-4.037l10.07-10.07 4.038 4.037-10.07 10.07z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-emlmg2-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-move-up-button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                data-icon="arrow-up"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="m4 12 1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                            <button
                              class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-colorInherit MuiIconButton-sizeMedium css-dox7m0-MuiButtonBase-root-MuiIconButton-root"
                              data-testid="item-move-down-button"
                              disabled=""
                              tabindex="-1"
                              type="button"
                            >
                              <svg
                                data-icon="arrow-down"
                                fill="currentColor"
                                height="20px"
                                viewBox="0 0 24 24"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="m20 12-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <p
                          class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-2t3842-MuiTypography-root"
                        >
                          Root | Dashboard | Dashboard-1
                        </p>
                      </div>
                      <div
                        class="MuiBox-root css-70qvj9"
                      >
                        <button
                          aria-describedby="DndDescribedBy-0"
                          aria-disabled="false"
                          aria-roledescription="sortable"
                          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                          role="button"
                          tabindex="0"
                          type="button"
                        >
                          <svg
                            aria-hidden="true"
                            class="svg-inline--fa fa-bars "
                            data-icon="bars"
                            data-prefix="far"
                            focusable="false"
                            role="img"
                            viewBox="0 0 448 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                              fill="currentColor"
                            />
                          </svg>
                        </button>
                      </div>
                    </li>
                  </ul>
                  <div
                    id="DndDescribedBy-0"
                    style="display: none;"
                  >
                    
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                  </div>
                  <div
                    aria-atomic="true"
                    aria-live="assertive"
                    id="DndLiveRegion-0"
                    role="status"
                    style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                  />
                  <button
                    class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-qbay45-MuiButtonBase-root-MuiButton-root"
                    data-testid="button-add-items"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
                    >
                      <svg
                        data-icon="add"
                        fill="currentColor"
                        height="24"
                        viewBox="0 0 24 24"
                        width="24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M4.5 10.75h15v2.5h-15z"
                          fill="currentColor"
                        />
                        <path
                          d="M10.75 19.5v-15h2.5v15z"
                          fill="currentColor"
                        />
                      </svg>
                    </span>
                    <span
                      class="css-qv0y8m"
                    >
                      Add menu item
                    </span>
                  </button>
                </div>
              </div>
              <div
                class="MuiBox-root css-1qlmmlw"
              >
                <button
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorError MuiIconButton-sizeMedium css-15a0bwv-MuiButtonBase-root-MuiIconButton-root"
                  data-testid="section-delete-button"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    data-icon="clear-outlined"
                    fill="currentColor"
                    height="20px"
                    viewBox="0 0 24 24"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 2C6.47581 2 2 6.47581 2 12C2 17.5242 6.47581 22 12 22C17.5242 22 22 17.5242 22 12C22 6.47581 17.5242 2 12 2ZM12 20.0645C7.54435 20.0645 3.93548 16.4556 3.93548 12C3.93548 7.54435 7.54435 3.93548 12 3.93548C16.4556 3.93548 20.0645 7.54435 20.0645 12C20.0645 16.4556 16.4556 20.0645 12 20.0645ZM16.1048 9.49194L13.5968 12L16.1048 14.5081C16.2944 14.6976 16.2944 15.004 16.1048 15.1935L15.1935 16.1048C15.004 16.2944 14.6976 16.2944 14.5081 16.1048L12 13.5968L9.49194 16.1048C9.30242 16.2944 8.99597 16.2944 8.80645 16.1048L7.89516 15.1935C7.70565 15.004 7.70565 14.6976 7.89516 14.5081L10.4032 12L7.89516 9.49194C7.70565 9.30242 7.70565 8.99597 7.89516 8.80645L8.80645 7.89516C8.99597 7.70565 9.30242 7.70565 9.49194 7.89516L12 10.4032L14.5081 7.89516C14.6976 7.70565 15.004 7.70565 15.1935 7.89516L16.1048 8.80645C16.2944 8.99597 16.2944 9.30242 16.1048 9.49194Z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
                <button
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-eea3ag-MuiButtonBase-root-MuiIconButton-root"
                  data-testid="section-edit-button"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    data-icon="edit"
                    fill="currentColor"
                    height="20px"
                    viewBox="0 0 24 24"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4.9 19.245h1.33l8.194-8.194-1.33-1.33L4.9 17.915zm13.585-9.571-4.037-3.99L17.13 3l4.014 4.014zM3 21.145v-4.037l10.07-10.07 4.038 4.037-10.07 10.07z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
                <button
                  class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-colorInherit MuiIconButton-sizeMedium css-1ff7zfp-MuiButtonBase-root-MuiIconButton-root"
                  data-testid="section-move-up-button"
                  disabled=""
                  tabindex="-1"
                  type="button"
                >
                  <svg
                    data-icon="arrow-up"
                    fill="currentColor"
                    height="20px"
                    viewBox="0 0 24 24"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="m4 12 1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
                <button
                  class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-colorInherit MuiIconButton-sizeMedium css-1ff7zfp-MuiButtonBase-root-MuiIconButton-root"
                  data-testid="section-move-down-button"
                  disabled=""
                  tabindex="-1"
                  type="button"
                >
                  <svg
                    data-icon="arrow-down"
                    fill="currentColor"
                    height="20px"
                    viewBox="0 0 24 24"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="m20 12-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
   
</div>
`;
