import React from 'react';
import { render, screen } from 'test/utils';
import { waitFor } from '@testing-library/react';
import ItemTypeSelectorDialog, { itemTypeOptions, TypeToDialogMap } from './ItemTypeSelectorDialog';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { ItemTypes, SettingsDialogs } from 'cyberrisk/types';

jest.mock('config', () => ({
    getReactRoot: jest.fn(() => document.body),
}));

jest.mock('common/utils/i18n', () => ({
    strings: jest.fn((key: string) => {
        const mockStrings = {
            'cyberrisk:label.addMenuItem': 'Add Menu Item',
            'common:button.cancel': 'Cancel',
            'common:button.next': 'Next',
        };
        return mockStrings[key] || key;
    }),
}));

describe('ItemTypeSelectorDialog', () => {
    const mockDispatch = jest.fn();
    const defaultContextValue = {
        activeSettingsDialog: null,
        dispatch: mockDispatch,
        itemType: null,
        activeSection: null,
        sections: [],
        isBusy: false,
        settingsPristine: true,
        getLibrary: jest.fn(),
        defaultWorkspaceName: 'Test Workspace',
        removeSectionItem: jest.fn(),
        removeItem: jest.fn(),
        doUpdateControls: jest.fn(),
        resetDefaults: jest.fn(),
        activeMenuItem: null,
        menuItemDialogType: null,
        fixedLevel: undefined,
    };

    const renderWithContext = (contextValue = {}) => {
        const mergedContextValue = { ...defaultContextValue, ...contextValue };
        return render(
            <ControlsContext.Provider value={mergedContextValue}>
                <ItemTypeSelectorDialog />
            </ControlsContext.Provider>,
        );
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Dialog Visibility', () => {
        it('should not render when activeSettingsDialog is not ITEM_TYPE_SELECTOR', () => {
            renderWithContext({ activeSettingsDialog: null });

            expect(screen.queryByText('Add Menu Item')).not.toBeInTheDocument();
        });

        it('should render when activeSettingsDialog is ITEM_TYPE_SELECTOR', () => {
            renderWithContext({ activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR });

            expect(screen.getByText('Add Menu Item')).toBeInTheDocument();
        });
    });

    describe('Dialog Content', () => {
        it('should render dialog with correct title and buttons', () => {
            renderWithContext({ activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR });

            expect(screen.getByText('Add Menu Item')).toBeInTheDocument();
            expect(screen.getByTestId('button-cancel')).toBeInTheDocument();
            expect(screen.getByTestId('button-confirm')).toBeInTheDocument();
            expect(screen.getByText('Cancel')).toBeInTheDocument();
            expect(screen.getByText('Next')).toBeInTheDocument();
        });

        it('should render select component with options', () => {
            renderWithContext({ activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR });

            const selectElement = screen.getByRole('combobox');
            expect(selectElement).toBeInTheDocument();
        });
    });

    describe('Button States', () => {
        it('should disable confirm button when no itemType is selected', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: null,
            });

            const confirmButton = screen.getByTestId('button-confirm');
            expect(confirmButton).toBeDisabled();
        });

        it('should enable confirm button when itemType is selected', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.REGISTER,
            });

            const confirmButton = screen.getByTestId('button-confirm');
            expect(confirmButton).toBeEnabled();
        });
    });

    describe('Button Actions', () => {
        it('should dispatch setActiveSettingsDialog(null) when cancel button is clicked', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
            });

            const cancelButton = screen.getByTestId('button-cancel');
            await user.click(cancelButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: null },
            });
        });

        it('should dispatch correct dialog when confirm button is clicked with REGISTER type', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.REGISTER,
            });

            const confirmButton = screen.getByTestId('button-confirm');
            await user.click(confirmButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: SettingsDialogs.REGISTER_SELECTOR },
            });
        });

        it('should dispatch correct dialog when confirm button is clicked with FRAMEWORK type', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.FRAMEWORK,
            });

            const confirmButton = screen.getByTestId('button-confirm');
            await user.click(confirmButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: SettingsDialogs.FRAMEWORK_SELECTOR },
            });
        });

        it('should dispatch correct dialog when confirm button is clicked with DASHBOARD type', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.DASHBOARD,
            });

            const confirmButton = screen.getByTestId('button-confirm');
            await user.click(confirmButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: SettingsDialogs.DASHBOARD_SELECTOR },
            });
        });

        it('should not dispatch when confirm button is clicked without itemType', async () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: null,
            });

            const confirmButton = screen.getByTestId('button-confirm');
            expect(confirmButton).toBeDisabled();
        });
    });

    describe('Select Component Behavior', () => {
        it('should call dispatch when select value changes', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.REGISTER,
            });

            const selectElement = screen.getByRole('combobox');
            expect(selectElement).toBeInTheDocument();

            // Clear previous calls from auto-selection
            mockDispatch.mockClear();

            // Simulate changing the select value
            await user.click(selectElement);

            // Find and click on Framework option
            const frameworkOption = screen.getByText('Framework');
            await user.click(frameworkOption);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ITEM_TYPE',
                payload: { itemType: ItemTypes.FRAMEWORK },
            });
        });

        it('should display the currently selected value', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.FRAMEWORK,
            });

            const selectElement = screen.getByRole('combobox');
            expect(selectElement).toBeInTheDocument();
            expect(screen.getByText('Framework')).toBeInTheDocument();
        });

        it('should render all available options when no restrictions apply', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                activeSection: { itemTypesRestriction: null },
            });

            const selectElement = screen.getByRole('combobox');
            await user.click(selectElement);

            expect(screen.getByText('Register')).toBeInTheDocument();
            expect(screen.getByText('Framework')).toBeInTheDocument();
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });
    });

    describe('Options Filtering', () => {
        it('should show all options when no itemTypesRestriction is set', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                activeSection: { itemTypesRestriction: null },
            });

            const selectElement = screen.getByRole('combobox');
            await user.click(selectElement);

            // All options should be available
            expect(screen.getByText('Register')).toBeInTheDocument();
            expect(screen.getByText('Framework')).toBeInTheDocument();
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });

        it('should filter options when itemTypesRestriction is set', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                activeSection: {
                    itemTypesRestriction: [ItemTypes.REGISTER, ItemTypes.FRAMEWORK],
                },
            });

            const selectElement = screen.getByRole('combobox');
            await user.click(selectElement);

            // Only restricted options should be available
            expect(screen.getByText('Register')).toBeInTheDocument();
            expect(screen.getByText('Framework')).toBeInTheDocument();
            expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
        });

        it('should handle empty itemTypesRestriction array', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                activeSection: {
                    itemTypesRestriction: [],
                },
            });

            const selectElement = screen.getByRole('combobox');
            await user.click(selectElement);

            // No options should be available
            expect(screen.queryByText('Register')).not.toBeInTheDocument();
            expect(screen.queryByText('Framework')).not.toBeInTheDocument();
            expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
        });

        it('should handle single item restriction', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                activeSection: {
                    itemTypesRestriction: [ItemTypes.DASHBOARD],
                },
            });

            const selectElement = screen.getByRole('combobox');
            await user.click(selectElement);

            // Only Dashboard should be available
            expect(screen.queryByText('Register')).not.toBeInTheDocument();
            expect(screen.queryByText('Framework')).not.toBeInTheDocument();
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });
    });

    describe('Auto-selection Effect', () => {
        it('should auto-select first option when dialog opens and no itemType is set', async () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: null,
            });

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith({
                    type: 'SET_ITEM_TYPE',
                    payload: { itemType: ItemTypes.REGISTER },
                });
            });
        });

        it('should not auto-select when itemType is already set', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.FRAMEWORK,
            });

            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'SET_ITEM_TYPE',
                }),
            );
        });

        it('should not auto-select when dialog is not active', () => {
            renderWithContext({
                activeSettingsDialog: null,
                itemType: null,
            });

            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'SET_ITEM_TYPE',
                }),
            );
        });

        it('should auto-select first available option when restrictions apply', async () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: null,
                activeSection: {
                    itemTypesRestriction: [ItemTypes.FRAMEWORK, ItemTypes.DASHBOARD],
                },
            });

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith({
                    type: 'SET_ITEM_TYPE',
                    payload: { itemType: ItemTypes.FRAMEWORK },
                });
            });
        });
        it('should not auto-select when no options are available due to restrictions', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: null,
                activeSection: {
                    itemTypesRestriction: [],
                },
            });

            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'SET_ITEM_TYPE',
                }),
            );
        });
    });

    describe('Edge Cases', () => {
        it('should handle undefined activeSection gracefully', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                activeSection: undefined,
            });

            expect(screen.getByRole('combobox')).toBeInTheDocument();
        });

        it('should handle null activeSection gracefully', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                activeSection: null,
            });

            expect(screen.getByRole('combobox')).toBeInTheDocument();
        });

        it('should not crash when itemType is invalid', () => {
            renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: 'INVALID_TYPE' as ItemTypes,
            });

            expect(screen.getByRole('combobox')).toBeInTheDocument();

            const confirmButton = screen.getByTestId('button-confirm');
            expect(confirmButton).toBeEnabled(); // Should still be enabled with any truthy value
        });
    });

    describe('Constants and Mappings', () => {
        it('should have correct itemTypeOptions', () => {
            expect(itemTypeOptions).toEqual([
                { label: 'Register', value: ItemTypes.REGISTER },
                { label: 'Framework', value: ItemTypes.FRAMEWORK },
                { label: 'Dashboard', value: ItemTypes.DASHBOARD },
            ]);
        });

        it('should have correct TypeToDialogMap', () => {
            expect(TypeToDialogMap).toEqual({
                [ItemTypes.REGISTER]: SettingsDialogs.REGISTER_SELECTOR,
                [ItemTypes.FRAMEWORK]: SettingsDialogs.FRAMEWORK_SELECTOR,
                [ItemTypes.DASHBOARD]: SettingsDialogs.DASHBOARD_SELECTOR,
            });
        });

        it('should have mapping for all itemTypeOptions', () => {
            itemTypeOptions.forEach((option) => {
                expect(TypeToDialogMap[option.value]).toBeDefined();
            });
        });
    });

    describe('Dialog Properties', () => {
        it('should render dialog with correct properties', () => {
            renderWithContext({ activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR });

            const dialog = screen.getByRole('dialog');
            expect(dialog).toBeInTheDocument();
            expect(screen.getByText('Add Menu Item')).toBeInTheDocument();
        });

        it('should have proper dialog structure', () => {
            renderWithContext({ activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR });

            // Check for dialog title
            expect(screen.getByText('Add Menu Item')).toBeInTheDocument();

            // Check for select component
            expect(screen.getByRole('combobox')).toBeInTheDocument();

            // Check for action buttons
            expect(screen.getByTestId('button-cancel')).toBeInTheDocument();
            expect(screen.getByTestId('button-confirm')).toBeInTheDocument();
        });
    });

    describe('Accessibility', () => {
        it('should have accessible dialog structure', () => {
            renderWithContext({ activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR });

            // Dialog should be accessible
            expect(screen.getByRole('dialog')).toBeInTheDocument();

            // Select should be accessible
            expect(screen.getByRole('combobox')).toBeInTheDocument();

            // Buttons should be accessible
            expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Next' })).toBeInTheDocument();
        });

        it('should support keyboard navigation', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.REGISTER,
            });

            await user.tab();
            const selectElement = screen.getByRole('combobox');
            expect(selectElement).toHaveFocus();

            await user.tab();
            expect(screen.getByTestId('button-cancel')).toHaveFocus();

            await user.tab();
            expect(screen.getByTestId('button-confirm')).toHaveFocus();
        });
    });

    describe('Integration Tests', () => {
        it('should complete full workflow: open dialog, select option, confirm', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.REGISTER,
            });

            mockDispatch.mockClear();

            const selectElement = screen.getByRole('combobox');
            await user.click(selectElement);
            await user.click(screen.getByText('Framework'));

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ITEM_TYPE',
                payload: { itemType: ItemTypes.FRAMEWORK },
            });

            mockDispatch.mockClear();

            const confirmButton = screen.getByTestId('button-confirm');
            await user.click(confirmButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: SettingsDialogs.REGISTER_SELECTOR },
            });
        });

        it('should handle cancel workflow correctly', async () => {
            const { user } = renderWithContext({
                activeSettingsDialog: SettingsDialogs.ITEM_TYPE_SELECTOR,
                itemType: ItemTypes.REGISTER,
            });

            const cancelButton = screen.getByTestId('button-cancel');
            await user.click(cancelButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: null },
            });
        });
    });
});
