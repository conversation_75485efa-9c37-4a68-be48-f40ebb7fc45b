import DialogActions from '@mui/material/DialogActions';
import { strings } from 'common/utils/i18n';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import React, { useContext, useEffect } from 'react';
import { MenuItemType } from '@protecht/ui-library/library/types/types';
import Select from '@protecht/ui-library/library/components/Inputs/Select';
import { getReactRoot } from 'config';
import { ItemTypes, SettingsDialogs } from 'cyberrisk/types';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { setActiveSettingsDialog, setSelectedItemType } from 'cyberrisk/ControlsActions';
import FieldLabel from '@protecht/ui-library/library/components/FormFields/FieldLabel';

export const itemTypeOptions: MenuItemType<ItemTypes>[] = [
    {
        label: 'Register',
        value: ItemTypes.REGISTER,
    },
    {
        label: 'Framework',
        value: ItemTypes.FRAMEWORK,
    },
    {
        label: 'Dashboard',
        value: ItemTypes.DASHBOARD,
    },
];

export const TypeToDialogMap = {
    [ItemTypes.REGISTER]: SettingsDialogs.REGISTER_SELECTOR,
    [ItemTypes.FRAMEWORK]: SettingsDialogs.FRAMEWORK_SELECTOR,
    [ItemTypes.DASHBOARD]: SettingsDialogs.DASHBOARD_SELECTOR,
};

const ItemTypeSelectorDialog = () => {
    const { activeSettingsDialog, dispatch, itemType, activeSection } = useContext(ControlsContext);

    const handleNextStep = () => {
        if (itemType) {
            dispatch(setActiveSettingsDialog(TypeToDialogMap[itemType]));
        }
    };

    const options = itemTypeOptions.filter((option) => !activeSection?.itemTypesRestriction || activeSection?.itemTypesRestriction?.includes(option.value));

    useEffect(() => {
        if (activeSettingsDialog === SettingsDialogs.ITEM_TYPE_SELECTOR && !itemType && options.length > 0) {
            dispatch(setSelectedItemType(options[0].value));
        }
    }, [activeSettingsDialog, itemType, options, dispatch]);

    return (
        <Dialog
            visible={activeSettingsDialog === SettingsDialogs.ITEM_TYPE_SELECTOR}
            width={500}
            dialogContainer={getReactRoot()}
            title={strings('cyberrisk:label.addMenuItem')}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={() => dispatch(setActiveSettingsDialog(null))}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        disabled={!itemType}
                        onClick={handleNextStep}
                        dataTestId="button-confirm"
                    >
                        {strings('common:button.next')}
                    </Button>
                </DialogActions>
            }
        >
            <FieldLabel label={strings('cyberrisk:label.itemTypeSelect')} />
            <Select
                options={options}
                value={itemType}
                onChange={(value: ItemTypes) => dispatch(setSelectedItemType(value))}
            />
        </Dialog>
    );
};

export default ItemTypeSelectorDialog;
