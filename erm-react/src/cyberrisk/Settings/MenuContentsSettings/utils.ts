import { WorkspaceGroup, WorkspaceGroupItem, RegisterItem, FrameworkItem, DashboardItem } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import { ViewRest } from 'api/generated/types';
import { ItemTypes } from 'cyberrisk/types';
import { NodeType, RepositoryNode } from 'rolesAndPermissions/types';

export const replaceItemById = (array, itemId, newItem) => {
    return array.map((item) => {
        if (item.id === itemId) {
            return newItem;
        }
        return item;
    });
};

export const reAssignOrder = (items: WorkspaceGroupItem[] | WorkspaceGroup[]) => {
    return items.map((item, index) => {
        return {
            ...item,
            order: index + 1,
        };
    });
};

const getItemKey = (item: WorkspaceGroupItem | null | undefined, view?: ViewRest): string => {
    switch (item?.itemType) {
        case ItemTypes.REGISTER: {
            const registerItem = item as RegisterItem;
            return registerItem.registerId
                ? `${registerItem.registerId?.toString()}_${view ? view?.id : registerItem.viewId?.toString() ?? 'noViewId'}`
                : `${registerItem.id}`;
        }
        case ItemTypes.FRAMEWORK:
            return (item as FrameworkItem).frameworkId?.toString() || '';

        case ItemTypes.DASHBOARD:
            return (item as DashboardItem).path?.toString() || '';
    }
    return '';
};

export const validateConfigStructure = (itemList: WorkspaceGroupItem[], selectedItem: WorkspaceGroupItem, view?: ViewRest) => {
    const registerTracker = new Set<string>();
    registerTracker.add(getItemKey(selectedItem, view));
    for (const item of itemList) {
        const key = getItemKey(item);

        if (key && registerTracker.has(key)) {
            let message = '';
            switch (selectedItem.itemType) {
                case ItemTypes.REGISTER:
                    message = strings('cyberrisk:error.duplicateRegister', {
                        name: selectedItem.name,
                        view: view?.name || strings('cyberrisk:error.empty'),
                    });
                    break;
                case ItemTypes.FRAMEWORK:
                    message = strings('cyberrisk:error.duplicateFramework', { name: selectedItem.name });
                    break;
                case ItemTypes.DASHBOARD:
                    message = strings('cyberrisk:error.duplicateDashboard', { name: selectedItem.name });
                    break;
            }

            return {
                valid: false,
                message,
            };
        }

        registerTracker.add(key);
    }

    return { valid: true, message: '' };
};

export const mapDashboard = (dashboard: RepositoryNode): RepositoryNode => {
    return {
        ...dashboard,
        label: dashboard.label || '',
        path: dashboard.path || '',
        type: dashboard.type as NodeType,
    };
};

export const isRegisterItem = (item: WorkspaceGroupItem | null | undefined): boolean => {
    return item?.itemType === ItemTypes.REGISTER;
};

export const isFrameworkItem = (item: WorkspaceGroupItem | null | undefined): boolean => {
    return item?.itemType === ItemTypes.FRAMEWORK;
};

export const getDashboardPath = (path?: string) => {
    return path?.split('/').join(' | ');
};

export const getDashboardItemDisplayPath = (item: WorkspaceGroupItem | null | undefined) => {
    return getDashboardPath((item as DashboardItem)?.path);
};

export const getViewId = (item: WorkspaceGroupItem | null | undefined): string | undefined => {
    return item && isRegisterItem(item) && (item as RegisterItem).viewId !== undefined ? (item as RegisterItem).viewId?.toString() : undefined;
};

export const cannotEdit = (role: string | undefined) => {
    return role === 'NO_ONE' || (role === 'INTERNAL' && ProtechtDictionary.isInternal !== 'true');
};
