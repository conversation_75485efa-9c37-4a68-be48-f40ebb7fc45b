import React from 'react';
import { render, screen } from 'test/utils/rtl';
import userEvent from '@testing-library/user-event';
import DashboardSelector from './DashboardSelector';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { useRcGetRepositoryHierarchyUsingGetQuery } from 'cyberrisk/rtkApi';
import { ItemTypes, MenuItemDialogType, SettingsDialogs } from 'cyberrisk/types';
import { NodeType } from 'rolesAndPermissions/types';
import { mockContext } from 'cyberrisk/api.mock';
import { waitFor } from '@testing-library/react';

jest.mock('cyberrisk/rtkApi', () => ({
    useRcGetRepositoryHierarchyUsingGetQuery: jest.fn(),
}));

describe('DashboardSelector', () => {
    const mockDispatch = jest.fn();
    const mockDashboardData = [
        {
            label: 'Root',
            path: '/',
            type: NodeType.FOLDER,
            subnodes: [
                {
                    label: 'Dashboard 1',
                    path: '/dashboard1',
                    type: NodeType.FOLDER,
                    subnodes: [
                        {
                            label: 'Report 1',
                            path: '/dashboard1/report1',
                            type: NodeType.VIEW_SHEET,
                        },
                        {
                            label: 'Report 2',
                            path: '/dashboard1/report2',
                            type: NodeType.VIEW_SHEET,
                        },
                    ],
                },
                {
                    label: 'Dashboard 2',
                    path: '/dashboard2',
                    type: NodeType.FOLDER,
                    subnodes: [
                        {
                            label: 'Report 3',
                            path: '/dashboard2/report3',
                            type: NodeType.VIEW_SHEET,
                        },
                    ],
                },
                {
                    label: 'Empty Dashboard',
                    path: '/empty',
                    type: NodeType.FOLDER,
                    subnodes: [
                        {
                            label: 'Subfolder',
                            path: '/empty/subfolder',
                            type: NodeType.FOLDER,
                            subnodes: [],
                        },
                    ],
                },
            ],
        },
    ];

    const mockContextValue = {
        ...mockContext,
        dispatch: mockDispatch,
        sections: [
            {
                itemList: [
                    {
                        itemType: ItemTypes.DASHBOARD,
                        name: 'Dashboard 2',
                        path: '/dashboard2',
                        order: 0,
                    },
                ],
            },
        ],
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (useRcGetRepositoryHierarchyUsingGetQuery as jest.Mock).mockReturnValue({
            data: mockDashboardData,
        });
    });

    it('renders FileStructureBrowserDialog when dashboards are available', () => {
        const { container } = render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        expect(container).toBeVisible();
        expect(screen.getByText('Select the folder of Dashboards to be displayed')).toBeInTheDocument();
    });

    it('does not render FileStructureBrowserDialog when no dashboards', () => {
        (useRcGetRepositoryHierarchyUsingGetQuery as jest.Mock).mockReturnValue({
            data: [],
        });

        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        expect(screen.queryByText('Select the folder of Dashboards to be displayed')).not.toBeInTheDocument();
    });

    it('render Loading component when loading dashboards', () => {
        (useRcGetRepositoryHierarchyUsingGetQuery as jest.Mock).mockReturnValue({
            data: [],
            isLoading: true,
        });

        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        expect(screen.getByText(/Loading.../)).toBeInTheDocument();
    });

    it('handles dashboard selection for new dashboard', async () => {
        render(
            <ControlsContext.Provider value={{ ...mockContextValue, menuItemDialogType: MenuItemDialogType.NEW, activeMenuItem: null }}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Dashboard 1'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });

        await userEvent.click(okButton);

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_MENU_ITEM',
            payload: {
                item: {
                    itemType: ItemTypes.DASHBOARD,
                    name: 'Dashboard 1',
                    path: '/dashboard1',
                    order: 0,
                },
                dialogType: MenuItemDialogType.NEW,
            },
        });

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_DIALOG',
            payload: {
                settingsDialogType: SettingsDialogs.ITEM_DETAILS,
            },
        });
    });

    it('handles non-leaf folder selection', async () => {
        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Empty Dashboard'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeDisabled();
        });
    });

    it('handles report selection', async () => {
        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Dashboard 2'));

        await userEvent.click(screen.getByText('Report 3'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeDisabled();
        });
    });

    it('handles leaf empty folder selection', async () => {
        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        // First expand the Empty Dashboard folder to reveal Subfolder
        await userEvent.click(screen.getByText('Empty Dashboard'));

        // Now click on the Subfolder
        await userEvent.click(screen.getByText('Subfolder'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });
    });

    it('shows duplicate message for existing dashboard', async () => {
        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Dashboard 2'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });

        await userEvent.click(okButton);

        expect(screen.getByText(/Duplicate found for dashboard with name/)).toBeInTheDocument();
    });

    it('closes dialog when onClose is triggered', async () => {
        render(
            <ControlsContext.Provider value={{ ...mockContextValue, menuItemDialogType: MenuItemDialogType.NEW }}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Cancel'));

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_SECTION',
            payload: {
                section: null,
            },
        });

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_DIALOG',
            payload: {
                settingsDialogType: null,
            },
        });
    });

    it('handles edit mode dialog close correctly', async () => {
        render(
            <ControlsContext.Provider value={{ ...mockContextValue, menuItemDialogType: MenuItemDialogType.EDIT }}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Cancel'));

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_DIALOG',
            payload: {
                settingsDialogType: SettingsDialogs.ITEM_DETAILS,
            },
        });

        expect(mockDispatch).not.toHaveBeenCalledWith({
            type: 'SET_ACTIVE_SECTION',
            payload: {
                section: null,
            },
        });
    });

    it('calls setOpen prop when provided', async () => {
        const mockSetOpen = jest.fn();

        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector setOpen={mockSetOpen} />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Cancel'));

        expect(mockSetOpen).toHaveBeenCalledWith(false);
    });

    it('calls onChange prop when dashboard is selected', async () => {
        const mockOnChange = jest.fn();

        render(
            <ControlsContext.Provider value={{ ...mockContextValue, menuItemDialogType: MenuItemDialogType.NEW, activeMenuItem: null }}>
                <DashboardSelector onChange={mockOnChange} />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Dashboard 1'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });

        await userEvent.click(okButton);

        expect(mockOnChange).toHaveBeenCalledWith({
            label: 'Dashboard 1',
            path: '/dashboard1',
            type: NodeType.FOLDER,
            subnodes: [
                {
                    label: 'Report 1',
                    path: '/dashboard1/report1',
                    type: NodeType.VIEW_SHEET,
                },
                {
                    label: 'Report 2',
                    path: '/dashboard1/report2',
                    type: NodeType.VIEW_SHEET,
                },
            ],
        });
    });

    it('handles API error state gracefully', () => {
        (useRcGetRepositoryHierarchyUsingGetQuery as jest.Mock).mockReturnValue({
            data: undefined,
            isLoading: false,
            error: { message: 'API Error' },
        });

        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        expect(screen.queryByText('Select the folder of Dashboards to be displayed')).not.toBeInTheDocument();
        expect(screen.queryByText(/Loading.../)).not.toBeInTheDocument();
    });

    it('handles undefined data from API', () => {
        (useRcGetRepositoryHierarchyUsingGetQuery as jest.Mock).mockReturnValue({
            data: undefined,
            isLoading: false,
        });

        render(
            <ControlsContext.Provider value={mockContextValue}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        expect(screen.queryByText('Select the folder of Dashboards to be displayed')).not.toBeInTheDocument();
    });

    it('preserves activeMenuItem order when provided', async () => {
        const mockActiveMenuItem = {
            itemType: ItemTypes.DASHBOARD,
            name: 'Existing Dashboard',
            path: '/existing',
            order: 5,
        };

        render(
            <ControlsContext.Provider
                value={{
                    ...mockContextValue,
                    menuItemDialogType: MenuItemDialogType.NEW,
                    activeMenuItem: mockActiveMenuItem,
                }}
            >
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Dashboard 1'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });

        await userEvent.click(okButton);

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_MENU_ITEM',
            payload: {
                item: {
                    ...mockActiveMenuItem,
                    itemType: ItemTypes.DASHBOARD,
                    name: 'Dashboard 1',
                    path: '/dashboard1',
                    order: 5,
                },
                dialogType: MenuItemDialogType.NEW,
            },
        });
    });

    it('uses 0 as default order when no activeMenuItem and no activeSection', async () => {
        render(
            <ControlsContext.Provider
                value={{
                    ...mockContextValue,
                    menuItemDialogType: MenuItemDialogType.NEW,
                    activeMenuItem: null,
                    activeSection: null,
                }}
            >
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Dashboard 1'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });

        await userEvent.click(okButton);

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_MENU_ITEM',
            payload: {
                item: {
                    itemType: ItemTypes.DASHBOARD,
                    name: 'Dashboard 1',
                    path: '/dashboard1',
                    order: 0,
                },
                dialogType: MenuItemDialogType.NEW,
            },
        });
    });

    it('clears duplicate message when selecting non-duplicate dashboard', async () => {
        render(
            <ControlsContext.Provider value={{ ...mockContextValue, menuItemDialogType: MenuItemDialogType.NEW, activeMenuItem: null }}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        // First select a duplicate dashboard to show the message
        await userEvent.click(screen.getByText('Dashboard 2'));
        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });
        await userEvent.click(okButton);

        expect(screen.getByText(/Duplicate found for dashboard with name/)).toBeInTheDocument();

        // Now select a non-duplicate dashboard
        await userEvent.click(screen.getByText('Dashboard 1'));
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });
        await userEvent.click(okButton);

        // The duplicate message should be cleared and dialog should close
        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_MENU_ITEM',
            payload: {
                item: {
                    itemType: ItemTypes.DASHBOARD,
                    name: 'Dashboard 1',
                    path: '/dashboard1',
                    order: 0,
                },
                dialogType: MenuItemDialogType.NEW,
            },
        });

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_DIALOG',
            payload: {
                settingsDialogType: SettingsDialogs.ITEM_DETAILS,
            },
        });
    });

    it('handles dashboard selection with complex nested structure', async () => {
        const complexMockData = [
            {
                label: 'Root',
                path: '/',
                type: NodeType.FOLDER,
                subnodes: [
                    {
                        label: 'Level 1',
                        path: '/level1',
                        type: NodeType.FOLDER,
                        subnodes: [
                            {
                                label: 'Level 2',
                                path: '/level1/level2',
                                type: NodeType.FOLDER,
                                subnodes: [
                                    {
                                        label: 'Deep Dashboard',
                                        path: '/level1/level2/deep',
                                        type: NodeType.FOLDER,
                                        subnodes: [
                                            {
                                                label: 'Deep Report',
                                                path: '/level1/level2/deep/report',
                                                type: NodeType.VIEW_SHEET,
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ];

        (useRcGetRepositoryHierarchyUsingGetQuery as jest.Mock).mockReturnValue({
            data: complexMockData,
        });

        render(
            <ControlsContext.Provider value={{ ...mockContextValue, menuItemDialogType: MenuItemDialogType.NEW, activeMenuItem: null }}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Level 1'));
        await userEvent.click(screen.getByText('Level 2'));
        await userEvent.click(screen.getByText('Deep Dashboard'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });

        await userEvent.click(okButton);

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'SET_ACTIVE_MENU_ITEM',
            payload: {
                item: {
                    itemType: ItemTypes.DASHBOARD,
                    name: 'Deep Dashboard',
                    path: '/level1/level2/deep',
                    order: 0,
                },
                dialogType: MenuItemDialogType.NEW,
            },
        });
    });

    it('handles sections with mixed item types for duplicate detection', async () => {
        const mockContextWithMixedItems = {
            ...mockContextValue,
            menuItemDialogType: MenuItemDialogType.NEW,
            activeMenuItem: null,
            sections: [
                {
                    itemList: [
                        {
                            itemType: ItemTypes.REGISTER,
                            name: 'Register Item',
                            registerId: 1,
                            order: 1,
                        },
                        {
                            itemType: ItemTypes.FRAMEWORK,
                            name: 'Framework Item',
                            frameworkId: 1,
                            order: 2,
                        },
                        {
                            itemType: ItemTypes.DASHBOARD,
                            name: 'Dashboard 1',
                            path: '/dashboard1',
                            order: 3,
                        },
                    ],
                },
            ],
        };

        render(
            <ControlsContext.Provider value={mockContextWithMixedItems}>
                <DashboardSelector />
            </ControlsContext.Provider>,
        );

        await userEvent.click(screen.getByText('Dashboard 1'));

        const okButton = screen.getByTestId('button-confirm');
        await waitFor(() => {
            expect(okButton).toBeEnabled();
        });

        await userEvent.click(okButton);

        expect(screen.getByText(/Duplicate found for dashboard with name/)).toBeInTheDocument();
    });
});
