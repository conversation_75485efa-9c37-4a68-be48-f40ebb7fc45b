import React, { FC, useCallback, useContext, useEffect, useState } from 'react';
import { strings } from 'common/utils/i18n';
import FileStructureBrowserDialog from 'ui/components/FileStructureBrowserDialog';
import { NodeType, RepositoryNode } from 'rolesAndPermissions/types';
import { useRcGetRepositoryHierarchyUsingGetQuery } from 'cyberrisk/rtkApi';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { ItemTypes, MenuItemDialogType, SettingsDialogs } from 'cyberrisk/types';
import { setActiveMenuItem, setActiveSection, setActiveSettingsDialog } from 'cyberrisk/ControlsActions';
import { mapDashboard } from '../utils';
import { DashboardItem } from 'api/generated/types';
import Loading from 'common/components/Loading';

export interface DashboardSelectorProps {
    setOpen?: (isOpen: boolean) => void;
    onChange?: (dashboard: RepositoryNode) => void;
}

const DashboardSelector: FC<DashboardSelectorProps> = ({ setOpen, onChange }) => {
    const { dispatch, sections, activeSection, activeMenuItem, menuItemDialogType } = useContext(ControlsContext);
    const [dashboards, setDashboards] = useState<RepositoryNode[]>();
    const [duplicateMessage, setDuplicateMessage] = useState('');
    const { data, isLoading } = useRcGetRepositoryHierarchyUsingGetQuery();

    useEffect(() => {
        if (data) {
            setDashboards(data.map(mapDashboard));
        }
    }, [data]);

    const handleClose = useCallback(() => {
        if (menuItemDialogType === MenuItemDialogType.EDIT) {
            dispatch(setActiveSettingsDialog(SettingsDialogs.ITEM_DETAILS));
        } else {
            dispatch(setActiveSettingsDialog(null));
            dispatch(setActiveSection(null));
        }
        setOpen?.(false);
    }, [dispatch, setOpen, menuItemDialogType]);

    const handleSelection = useCallback(
        (dashboard: RepositoryNode) => {
            const isDuplicate = sections
                ?.flatMap((s) => s.itemList)
                .filter((_item) => _item && _item.itemType === ItemTypes.DASHBOARD)
                .some((item) => (item as DashboardItem)?.path === dashboard.path);

            if (!isDuplicate) {
                dispatch(
                    setActiveMenuItem(
                        {
                            ...activeMenuItem,
                            itemType: ItemTypes.DASHBOARD,
                            name: dashboard.label,
                            path: dashboard.path,
                            order: activeMenuItem?.order ?? (activeSection?.itemList?.length || 0),
                        },
                        menuItemDialogType!,
                    ),
                );
                onChange?.(dashboard);
                dispatch(setActiveSettingsDialog(SettingsDialogs.ITEM_DETAILS));
                setOpen?.(false);
            } else {
                setDuplicateMessage(strings('cyberrisk:error.duplicateDashboard', { name: dashboard.label }));
            }
        },
        [dispatch, sections, setDuplicateMessage, activeMenuItem, activeSection, menuItemDialogType, onChange, setOpen],
    );

    return (
        <>
            {dashboards?.length && (
                <FileStructureBrowserDialog
                    nodeStructure={dashboards[0]}
                    visible={true}
                    title={strings('cyberrisk:label.selectDashboard')}
                    message={duplicateMessage}
                    onClose={handleClose}
                    onConfirm={(dashboard) => handleSelection(dashboard)}
                    isNodeSelectable={(node: RepositoryNode) =>
                        ((node.type as NodeType.FOLDER) === NodeType.FOLDER && node.subnodes?.every((child) => child.type === NodeType.VIEW_SHEET)) || false
                    }
                />
            )}
            {isLoading && <Loading />}
        </>
    );
};

export default DashboardSelector;
