import React from 'react';
import { render, screen } from 'test/utils';
import { act, waitFor } from '@testing-library/react';
import RegisterItemEditor from './RegisterItemEditor';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { MenuItemDialogType, ItemTypes } from 'cyberrisk/types';
import { RegisterItem, TableMetadataRest, WorkspaceGroup } from 'api/generated/types';
import * as cyberRiskRtkApi from 'cyberrisk/rtkApi';
import * as viewRtkApi from 'view/rtkApi';
import * as registerRtkApi from 'register/rtkApi';
import userEvent from '@testing-library/user-event';

jest.mock('register/rtkApi', () => ({
    ...jest.requireActual('register/rtkApi'),
    useLazyTmrsGetRegisterConfigUsingGet1Query: jest.fn(),
}));

jest.mock('cyberrisk/rtkApi', () => ({
    useWrsGetRegisterColumnsByTypesUsingGetQuery: jest.fn(),
}));

jest.mock('view/rtkApi', () => ({
    useVrsGetViewsUsingGetQuery: jest.fn(),
}));

jest.mock('common/utils/i18n', () => ({
    strings: (key: string) => {
        const mockStrings: Record<string, string> = {
            'cyberrisk:label.registerEditor.title': 'Edit Register/Library menu item',
            'cyberrisk:label.registerEditor.register': 'Register/Library table shown by this menu item',
            'cyberrisk:label.label': 'Label for the menu item when shown in the sidebar menu',
            'cyberrisk:label.registerEditor.selectedView': 'Table View shown when menu item selected',
            'cyberrisk:label.registerEditor.description': 'Descriptive label for the Table View',
            'cyberrisk:label.registerEditor.showDateStatusField': 'Date status (Open, Due, Overdue)',
            'cyberrisk:label.registerEditor.showDateStatusFieldDescription': 'Date status (Open, Due, Overdue)',
            'cyberrisk:label.registerEditor.dateStatusField': 'Date status field',
            'cyberrisk:label.registerEditor.contract': 'Register/Library must satisfy this Field Mapping Criteria',
            'cyberrisk:label.registerEditor.contractTooltip': 'Contract tooltip',
            'cyberrisk:label.registerEditor.contractPlaceholder': 'Contract placeholder',
            'cyberrisk:label.registerEditor.registerPlaceholder': 'Register placeholder',
            'cyberrisk:label.registerEditor.labelPlaceholder': 'Label placeholder',
            'cyberrisk:label.registerEditor.descriptionPlaceholder': 'Description placeholder',
            'cyberrisk:label.showAll': 'Show All',
            'cyberrisk:label.removedView': 'Removed View',
            'ermMessages:btn_cancel': 'Cancel',
            'ermMessages:btn_okay': 'OK',
            'common:validators.requiredSimple': 'This field is required',
        };
        return mockStrings[key] || key;
    },
}));

// Mock the WSRegisterSelector component
jest.mock('../WSRegisterSelector/WSRegisterSelector', () => {
    return {
        __esModule: true,
        default: ({ onChange, setOpen }: any) => (
            <div data-testid="ws-register-selector">
                <button
                    onClick={() => {
                        onChange?.({
                            id: 123,
                            label: 'Test Register',
                            tableName: 'test_table',
                        });
                        setOpen(false);
                    }}
                >
                    Select Register
                </button>
            </div>
        ),
    };
});

const validateConfigStructureMock = jest.fn();

jest.mock('../utils', () => ({
    ...jest.requireActual('../utils'),
    validateConfigStructure: () => validateConfigStructureMock(),
}));

const addItemToSectionMock = jest.fn().mockReturnValue((item) => ({ type: 'ADD_ITEM_TO_SECTION', payload: { item } }));
const saveMenuItemMock = jest.fn().mockReturnValue((item) => ({ type: 'SAVE_MENU_ITEM', payload: { item } }));
const setActiveSectionMock = jest.fn().mockReturnValue((section) => ({ type: 'SET_ACTIVE_SECTION', payload: { section } }));
const setActiveSettingsDialogMock = jest.fn().mockReturnValue((dialog) => ({
    type: 'SET_ACTIVE_DIALOG',
    payload: { settingsDialogType: dialog },
}));

jest.mock('cyberrisk/ControlsActions', () => ({
    ...jest.requireActual('cyberrisk/ControlsActions'),
    addItemToSection: (data) => addItemToSectionMock(data),
    saveMenuItem: (data) => saveMenuItemMock(data),
    setActiveSection: (data) => setActiveSectionMock(data),
    setActiveSettingsDialog: (data) => setActiveSettingsDialogMock(data),
}));

describe('RegisterItemEditor', () => {
    const mockDispatch = jest.fn();
    const mockTriggerGetRegister = jest.fn();
    const mockUseWrsGetRegisterColumnsByTypeUsingGetQuery = cyberRiskRtkApi.useWrsGetRegisterColumnsByTypesUsingGetQuery as jest.Mock;
    const mockUseVrsGetViewsUsingGetQuery = viewRtkApi.useVrsGetViewsUsingGetQuery as jest.Mock;
    const mockUseLazyTmrsGetRegisterConfigUsingGet1Query = registerRtkApi.useLazyTmrsGetRegisterConfigUsingGet1Query as jest.Mock;

    const mockRegisterItem: RegisterItem = {
        id: '1',
        itemType: ItemTypes.REGISTER,
        alias: 'Test Register Item',
        name: 'Register 1',
        registerId: 123,
        tableName: 'test_table',
        viewId: 456,
        description: 'Test description',
        order: 1,
        selectedDateColumn: 789,
        contractType: {
            code: 321,
            label: 'Test Contract',
        },
    };

    const mockTableMetadata: TableMetadataRest = {
        id: 123,
        label: 'Test Register',
        tableName: 'test_table',
    };

    const mockStatusFields = [
        {
            id: 789,
            name: 'Due Date Field',
            sectionName: 'Test Section',
        },
        {
            id: 790,
            name: 'Another Date Field',
            sectionName: 'Another Section',
        },
    ];

    const mockViews = {
        views: [
            {
                id: 456,
                name: 'Test View',
                scope: null,
            },
            {
                id: 457,
                name: 'Another View',
                scope: null,
            },
        ],
    };

    const defaultContextValue = {
        dispatch: mockDispatch,
        activeMenuItem: mockRegisterItem,
        sections: [
            {
                itemList: [
                    {
                        id: '2',
                        itemType: ItemTypes.REGISTER,
                        alias: 'Test Register Item1',
                        name: 'Register 1',
                        registerId: 1234,
                        tableName: 'test_table123',
                        viewId: 456,
                        description: 'Test description',
                        order: 1,
                        selectedDueDateColumn: 789,
                        contractType: {
                            code: 321,
                            label: 'Test Contract',
                        },
                    } as RegisterItem,
                ],
            } as WorkspaceGroup,
        ] as WorkspaceGroup[],
        menuItemDialogType: MenuItemDialogType.NEW,
    };

    beforeEach(() => {
        jest.clearAllMocks();

        mockUseLazyTmrsGetRegisterConfigUsingGet1Query.mockReturnValue([mockTriggerGetRegister, { isLoading: false }]);

        mockTriggerGetRegister.mockReturnValue({
            unwrap: () => mockTableMetadata,
        });

        mockUseWrsGetRegisterColumnsByTypeUsingGetQuery.mockReturnValue({
            data: mockStatusFields,
            isLoading: false,
        });

        mockUseVrsGetViewsUsingGetQuery.mockReturnValue({
            data: mockViews,
            isLoading: false,
        });
    });

    const renderComponent = async (contextValue = defaultContextValue) => {
        let result;
        await act(async () => {
            result = render(
                <ControlsContext.Provider
                    value={{
                        ...contextValue,
                        isBusy: false,
                        settingsPristine: true,
                        getLibrary: jest.fn(),
                        defaultWorkspaceName: 'Test Workspace',
                        removeSectionItem: jest.fn(),
                        removeItem: jest.fn(),
                        doUpdateControls: jest.fn(),
                        resetDefaults: jest.fn(),
                        activeSettingsDialog: null,
                        activeSection: null,
                        itemType: null,
                    }}
                >
                    <RegisterItemEditor />
                </ControlsContext.Provider>,
            );
        });

        // Wait for form to initialize and all fields to be rendered
        await waitFor(
            () => {
                expect(screen.getByText('Edit Register/Library menu item')).toBeInTheDocument();
                // Wait for form fields to be available - check for any form field to ensure form is loaded
                const labelField = screen.queryByLabelText('Label for the menu item when shown in the sidebar menu');
                expect(labelField).toBeInTheDocument();
            },
            { timeout: 10000 },
        );
        return result;
    };

    describe('Rendering', () => {
        it('renders the dialog with correct title', async () => {
            await renderComponent();
            expect(screen.getByText('Edit Register/Library menu item')).toBeInTheDocument();
        });

        it('renders all form fields', async () => {
            await renderComponent();

            expect(screen.getByLabelText('Register/Library table shown by this menu item')).toBeInTheDocument();
            expect(screen.getByLabelText('Label for the menu item when shown in the sidebar menu')).toBeInTheDocument();
            expect(screen.getByLabelText('Table View shown when menu item selected')).toBeInTheDocument();
            expect(screen.getByLabelText('Descriptive label for the Table View')).toBeInTheDocument();
            expect(screen.getByLabelText('Date status (Open, Due, Overdue)')).toBeInTheDocument();
        });

        it('renders contract field when contract type exists', async () => {
            await renderComponent();
            expect(screen.getByDisplayValue('Test Contract')).toBeInTheDocument();
        });

        it('does not render contract field when contract type does not exist', async () => {
            const contextWithoutContract = {
                ...defaultContextValue,
                activeMenuItem: {
                    ...mockRegisterItem,
                    contractType: undefined,
                },
            };
            await renderComponent(contextWithoutContract);
            expect(screen.queryByDisplayValue('Test Contract')).not.toBeInTheDocument();
        });

        it('renders date status field when showDateStatusField is enabled and options exist', async () => {
            await renderComponent();

            // Wait for the checkbox to be available and interactable
            await waitFor(() => {
                const showDateStatusCheckbox = screen.getByLabelText('Date status (Open, Due, Overdue)');
                expect(showDateStatusCheckbox).toBeInTheDocument();
                expect(showDateStatusCheckbox).toBeEnabled();
            });

            const showDateStatusCheckbox = screen.getByLabelText('Date status (Open, Due, Overdue)');

            // Check if checkbox is already checked (since selectedDueDateColumn is set in mock data)
            if (!(showDateStatusCheckbox as HTMLInputElement).checked) {
                await act(async () => {
                    await userEvent.click(showDateStatusCheckbox);
                });
            }

            await waitFor(() => {
                expect(screen.getByLabelText('Date status field')).toBeInTheDocument();
            });
        });
    });

    describe('Form Validation', () => {
        it('disables submit button when form is invalid', async () => {
            const contextWithEmptyItem = {
                ...defaultContextValue,
                activeMenuItem: {
                    ...mockRegisterItem,
                    alias: '',
                },
            };
            await renderComponent(contextWithEmptyItem);

            // Clear the label field to make form invalid
            const labelField = screen.getByLabelText('Label for the menu item when shown in the sidebar menu');
            await act(async () => {
                await userEvent.clear(labelField);
            });

            await waitFor(() => {
                const submitButton = screen.getByTestId('button-confirm');
                expect(submitButton).toBeDisabled();
            });
        });

        it('enables submit button when form is valid', async () => {
            await renderComponent();
            await waitFor(
                () => {
                    const submitButton = screen.getByTestId('button-confirm');
                    expect(submitButton).toBeEnabled();
                },
                { timeout: 5000 },
            );
        });

        it('validates required fields', async () => {
            await renderComponent();

            const labelField = screen.getByLabelText('Label for the menu item when shown in the sidebar menu');
            await userEvent.clear(labelField);
            await userEvent.tab(); // Trigger validation

            await waitFor(() => {
                const submitButton = screen.getByTestId('button-confirm');
                expect(submitButton).toBeDisabled();
            });
        });
    });

    describe('Register Selection', () => {
        it('updates form when register is selected', async () => {
            validateConfigStructureMock.mockReturnValue({
                valid: true,
                message: '',
            });
            await renderComponent();

            const registerField = screen.getByLabelText('Register/Library table shown by this menu item');
            await userEvent.click(registerField);

            const selectButton = screen.getByText('Select Register');
            await userEvent.click(selectButton);

            await waitFor(() => {
                expect(screen.getByDisplayValue('Test Register')).toBeInTheDocument();
            });
        });

        it('clears view and date status fields when register changes', async () => {
            validateConfigStructureMock.mockReturnValue({
                valid: true,
                message: '',
            });
            await renderComponent();

            const registerField = screen.getByLabelText('Register/Library table shown by this menu item');
            await userEvent.click(registerField);

            const selectButton = screen.getByText('Select Register');
            await userEvent.click(selectButton);

            await waitFor(() => {
                expect(screen.getByDisplayValue('Test Register')).toBeInTheDocument();
            });
        });
    });

    describe('Form Submission', () => {
        it('calls addItemToSectionMock for new items', async () => {
            validateConfigStructureMock.mockReturnValue({
                valid: true,
                message: '',
            });
            await renderComponent();

            const submitButton = screen.getByTestId('button-confirm');
            await userEvent.click(submitButton);

            await waitFor(() => {
                expect(addItemToSectionMock).toHaveBeenCalled();
            });
        });

        it('calls saveMenuItem for edited items', async () => {
            validateConfigStructureMock.mockReturnValue({
                valid: true,
                message: '',
            });
            const editContext = {
                ...defaultContextValue,
                menuItemDialogType: MenuItemDialogType.EDIT,
            };
            await renderComponent(editContext);

            const submitButton = screen.getByTestId('button-confirm');
            await userEvent.click(submitButton);

            await waitFor(() => {
                expect(saveMenuItemMock).toHaveBeenCalled();
            });
        });

        it('shows error message when validation fails', async () => {
            validateConfigStructureMock.mockReturnValue({
                valid: false,
                message: 'Duplicate register error',
            });
            await renderComponent();

            const submitButton = screen.getByTestId('button-confirm');
            await userEvent.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText('Duplicate register error')).toBeInTheDocument();
            });
            await waitFor(() => {
                expect(saveMenuItemMock).not.toHaveBeenCalled();
            });
        });

        it('includes selected due date column when showDateStatusField is enabled', async () => {
            validateConfigStructureMock.mockReturnValue({
                valid: true,
                message: '',
            });
            const context = {
                ...defaultContextValue,
                activeMenuItem: {
                    ...mockRegisterItem,
                },
            };
            await renderComponent(context);

            const submitButton = screen.getByTestId('button-confirm');
            await userEvent.click(submitButton);

            await waitFor(() => {
                expect(addItemToSectionMock).toHaveBeenCalledWith(
                    expect.objectContaining({
                        selectedDateColumn: 789,
                    }),
                );
            });
        });

        it('excludes due date column when showDateStatusField is disabled', async () => {
            validateConfigStructureMock.mockReturnValue({
                valid: true,
                message: '',
            });
            const context = {
                ...defaultContextValue,
                activeMenuItem: {
                    ...mockRegisterItem,
                    selectedDueDateColumn: undefined,
                },
            };
            await renderComponent(context);

            const submitButton = screen.getByTestId('button-confirm');
            await waitFor(() => expect(submitButton).toBeEnabled());
            await userEvent.click(submitButton);

            await waitFor(() => {
                expect(addItemToSectionMock).toHaveBeenCalledWith(
                    expect.objectContaining({
                        selectedDueDateColumn: undefined,
                    }),
                );
            });
        });
    });

    describe('Dialog Actions', () => {
        it('calls clearData when cancel button is clicked', async () => {
            await renderComponent();

            const cancelButton = screen.getByTestId('button-cancel');
            await userEvent.click(cancelButton);

            expect(setActiveSectionMock).toHaveBeenCalledWith(null);
            expect(setActiveSettingsDialogMock).toHaveBeenCalledWith(null);
        });
    });

    describe('Error Handling', () => {
        it('handles register loading error', async () => {
            mockTriggerGetRegister.mockReturnValue({
                unwrap: jest.fn().mockRejectedValue(() => new Error('Failed to load register')),
            });

            await renderComponent();

            // Should handle the error gracefully
            await waitFor(() => {
                // The component should still render without crashing
                expect(screen.getByText('Edit Register/Library menu item')).toBeInTheDocument();
            });
        });
    });

    describe('Date Status Field Behavior', () => {
        it('disables date status field checkbox when no status fields available', async () => {
            mockUseWrsGetRegisterColumnsByTypeUsingGetQuery.mockReturnValue({
                data: [],
                isLoading: false,
            });

            await renderComponent();

            const showDateStatusCheckbox = screen.getByLabelText('Date status (Open, Due, Overdue)');
            expect(showDateStatusCheckbox).toBeDisabled();
        });

        it('automatically selects first date status field when available', async () => {
            const context = {
                ...defaultContextValue,
                activeMenuItem: {
                    ...mockRegisterItem,
                    selectedDateColumn: undefined,
                },
            };
            await renderComponent(context);

            const showDateStatusCheckbox = screen.getByLabelText('Date status (Open, Due, Overdue)');
            await userEvent.click(showDateStatusCheckbox);

            await waitFor(() => {
                const dateStatusSelect = screen.getByLabelText('Date status field');
                expect(dateStatusSelect).toBeInTheDocument();
            });
        });
    });

    describe('Default Values', () => {
        it('initializes form with existing register item data', async () => {
            await renderComponent();

            await waitFor(() => {
                expect(screen.getByDisplayValue('Test Register Item')).toBeInTheDocument();
                expect(screen.getByDisplayValue('Test description')).toBeInTheDocument();
            });
        });

        it('handles missing register data gracefully', async () => {
            const contextWithoutRegister = {
                ...defaultContextValue,
                activeMenuItem: {
                    ...mockRegisterItem,
                    registerId: undefined,
                },
            };

            await renderComponent(contextWithoutRegister);

            expect(screen.getByText('Edit Register/Library menu item')).toBeInTheDocument();
        });
    });
});
