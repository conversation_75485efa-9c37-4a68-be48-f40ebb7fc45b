import React, { FC, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import * as Yup from 'yup';
import useForm from 'common/hooks/forms/useForm';
import { RegisterItem, WorkspaceGroupItem } from 'api/generated/types';
import { cannotEdit, getViewId, isRegisterItem, validateConfigStructure } from '../utils';
import { useTheme } from '@mui/material/styles';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { strings } from 'common/utils/i18n';
import { getReactRoot } from 'config';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { addItemToSection, saveMenuItem, setActiveSection, setActiveSettingsDialog } from 'cyberrisk/ControlsActions';
import { FormProvider } from 'react-hook-form';
import Grid from '@mui/material/Grid';
import SelectField from '@protecht/ui-library/library/components/FormFields/SelectField';
import Typography from '@mui/material/Typography';
import { useVrsGetViewsUsingGetQuery } from 'view/rtkApi';
import { MenuItemType } from '@protecht/ui-library/library/types/types';
import BooleanField from '@protecht/ui-library/library/components/FormFields/BooleanField';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import WSRegisterSelector from '../WSRegisterSelector/WSRegisterSelector';
import { ItemTypes, MenuItemDialogType } from 'cyberrisk/types';
import { useWrsGetRegisterColumnsByTypesUsingGetQuery } from 'cyberrisk/rtkApi';
import { ColumnType, RegisterRest } from 'register/types';
import DialogSelectorField from 'common/components/Form/FormFields/DialogSelectorField';
import InfoBubble from '@protecht/ui-library/library/components/InfoBubble';
import Box from '@mui/material/Box';
import { SEPARATOR_SYMBOL } from 'register/components/RegisterField/LinkedToRegisterField/TruncatedLinkName/const';

type RegisterItemDetails = {
    contract?: string;
    registerName?: string;
    label: string;
    view?: string;
    description?: string;
    dateStatusField?: number;
    showDateStatusField: boolean;
};

const schema: Yup.AnyObjectSchema = Yup.object({
    contract: Yup.string(),
    registerName: Yup.string().required(strings('common:validators.requiredSimple')),
    label: Yup.string().required(strings('common:validators.requiredSimple')),
    view: Yup.string(),
    description: Yup.string().optional(),
    dateStatusField: Yup.number()
        .transform((_, value) => (value === '' ? null : value))
        .nullable()
        .optional(),
    showDateStatusField: Yup.boolean().optional(),
});

const RegisterItemEditor: FC = () => {
    const theme = useTheme();
    const { dispatch, activeMenuItem, sections, menuItemDialogType } = useContext(ControlsContext);
    const [errorMessage, setErrorMessage] = useState('');
    const [activeRegister, setActiveRegister] = useState<RegisterRest | null>(null);
    const registerData: RegisterItem | undefined = activeMenuItem as RegisterItem;
    const originalRegisterId = registerData?.registerId;

    const initDefaultValues = useMemo(() => {
        const registerData: RegisterItem | undefined = activeMenuItem as RegisterItem;

        return {
            contract: registerData?.contractType?.label || '',
            label: activeMenuItem?.alias || '',
            view: getViewId(activeMenuItem)?.toString() || '',
            description: registerData?.description || '',
            registerName: registerData?.name || '',
            dateStatusField: registerData?.selectedDateColumn,
            showDateStatusField: !!registerData?.selectedDateColumn,
        } as RegisterItemDetails;
    }, [activeMenuItem]);

    const formMethods = useForm<RegisterItemDetails>({
        mode: 'onChange',
        schema,
        defaultValues: initDefaultValues,
    });

    const { handleSubmit, formState, setValue, watch, trigger } = formMethods;
    const { isValid } = formState;

    const dateStatus = watch('dateStatusField');
    const showDateStatusField = watch('showDateStatusField');

    const id = activeMenuItem?.id;

    const { data: statusFields } = useWrsGetRegisterColumnsByTypesUsingGetQuery(
        {
            registerId: activeRegister?.id ?? originalRegisterId!,
            columnTypes: [ColumnType.DUE_DATE, ColumnType.DATE, ColumnType.TIMESTAMP_WITH_TIMEZONE, ColumnType.TIMESTAMP],
        },
        {
            skip: !activeRegister?.id && !originalRegisterId,
        },
    );

    const { data: viewResponse } = useVrsGetViewsUsingGetQuery(
        {
            context: activeRegister?.tableName ?? (isRegisterItem(activeMenuItem) ? registerData.tableName?.toString() : undefined),
            global: true,
        },
        { skip: !activeMenuItem && !activeRegister, refetchOnMountOrArgChange: true },
    );

    const viewOptions = useMemo(() => {
        const showAll: MenuItemType<string> = { label: strings('cyberrisk:label.showAll'), value: '' };
        const options: MenuItemType<string>[] = [];
        options.push(
            ...((viewResponse?.views
                ?.filter((w) => !w.scope)
                .map((view) => ({
                    label: view.name,
                    value: view.id?.toString(),
                })) as MenuItemType<string>[]) || []),
        );
        const viewId = getViewId(activeMenuItem);
        const viewExists = viewResponse?.views?.find((view) => String(view.id) === viewId);
        if (!viewExists && activeMenuItem && viewId) {
            options.unshift({ label: strings('cyberrisk:label.removedView'), value: viewId });
        }
        options.unshift(showAll);
        return options;
    }, [viewResponse, activeMenuItem]);

    const clearData = useCallback(() => {
        dispatch(setActiveSection(null));
        dispatch(setActiveSettingsDialog(null));
        setActiveRegister(null);
    }, [dispatch]);

    const onSubmit = useCallback(
        (values: RegisterItemDetails) => {
            const newViewId = values.view ? parseInt(values.view) : undefined;
            const newView = viewResponse?.views?.find((view) => view.id === newViewId);
            const label = values.label?.trim();
            const view = values.view?.trim();
            const description = values.description?.trim();

            const registerItem: RegisterItem = {
                ...activeMenuItem,
                id: activeMenuItem?.id,
                itemType: ItemTypes.REGISTER,
                registerId: activeRegister?.id ?? registerData?.registerId,
                tableName: activeRegister?.tableName ?? registerData?.tableName,
                name: activeRegister?.label ?? registerData?.name ?? '',
                alias: label,
                viewId: view ? parseInt(view, 10) : undefined,
                description: description,
                order: activeMenuItem?.order || 0,
                selectedDateColumn: values.showDateStatusField ? values.dateStatusField : undefined,
            };

            const sameItems = sections
                ?.flatMap((section) => section.itemList)
                .filter((item) => item?.itemType === ItemTypes.REGISTER)
                .filter((item: WorkspaceGroupItem) => !!item);

            if (sameItems && sameItems?.length) {
                if (menuItemDialogType === MenuItemDialogType.NEW) {
                    const res = validateConfigStructure(sameItems as WorkspaceGroupItem[], registerItem, newView);
                    if (res.valid) {
                        dispatch(addItemToSection(registerItem));
                        clearData();
                    } else {
                        setErrorMessage(res.message);
                    }
                } else if (menuItemDialogType === MenuItemDialogType.EDIT) {
                    const listWithoutActiveItem = sameItems.filter((item) => activeMenuItem?.id !== item.id);
                    const res = validateConfigStructure(listWithoutActiveItem as WorkspaceGroupItem[], registerItem, newView);
                    if (res.valid) {
                        dispatch(saveMenuItem(registerItem));
                        clearData();
                    } else {
                        setErrorMessage(res.message);
                    }
                }
            } else {
                dispatch(addItemToSection(registerItem));
                clearData();
            }
        },
        [
            sections,
            activeMenuItem,
            dispatch,
            menuItemDialogType,
            setErrorMessage,
            viewResponse,
            clearData,
            activeRegister,
            registerData?.tableName,
            registerData?.registerId,
            registerData?.name,
        ],
    );

    const dateStatusOptions = useMemo(
        () =>
            statusFields?.map((field) => ({
                value: field.id,
                label: `${field.sectionName} ${SEPARATOR_SYMBOL} ${field.name}`,
            })),
        [statusFields],
    );

    const handleRegisterChange = useCallback(
        (register: RegisterRest | null) => {
            setValue('registerName', register?.label || '');
            setValue('view', undefined);
            setValue('dateStatusField', undefined);
            setValue('showDateStatusField', false);
            setActiveRegister(register);
            void trigger();
        },
        [setValue, trigger],
    );

    useEffect(() => {
        if (dateStatusOptions && !dateStatus && dateStatusOptions.length > 0) {
            setValue('dateStatusField', dateStatusOptions[0]?.value);
        }
    }, [dateStatusOptions, setValue, dateStatus, id]);

    return (
        <Dialog
            visible={true}
            width={500}
            title={strings('cyberrisk:label.registerEditor.title')}
            dialogContainer={getReactRoot()}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={() => clearData()}
                        dataTestId="button-cancel"
                    >
                        {strings('ermMessages:btn_cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        disabled={!isValid}
                        onClick={handleSubmit(onSubmit)}
                        dataTestId="button-confirm"
                    >
                        {strings('ermMessages:btn_okay')}
                    </Button>
                </DialogActions>
            }
        >
            <FormProvider {...formMethods}>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid
                        direction="column"
                        container
                        spacing={2}
                        gap={'4px'}
                    >
                        {registerData?.contractType && (
                            <Grid item>
                                <Box sx={{ display: 'flex', margin: '3px 0 8px 0' }}>
                                    <Typography variant={'body2'}>{strings('cyberrisk:label.registerEditor.contract')}</Typography>
                                    <InfoBubble
                                        trigger={'click'}
                                        content={<>{strings('cyberrisk:label.registerEditor.contractTooltip')}</>}
                                        sx={{ marginLeft: '4px' }}
                                    />
                                </Box>
                                <InputField
                                    disabled
                                    name={'contract'}
                                    hideFieldLabel
                                    placeholder={strings('cyberrisk:label.registerEditor.contractPlaceholder')}
                                />
                            </Grid>
                        )}
                        <Grid item>
                            <DialogSelectorField
                                name="registerName"
                                label={strings('cyberrisk:label.registerEditor.register')}
                                clearable={true}
                                onInputClear={() => handleRegisterChange(null)}
                                placeholder={strings('cyberrisk:label.registerEditor.registerPlaceholder')}
                                renderDialog={({ ref: _ref, ...props }) => {
                                    return (
                                        <WSRegisterSelector
                                            {...props}
                                            onChange={handleRegisterChange}
                                            contractTypes={registerData?.contractType ? [registerData?.contractType] : undefined}
                                        />
                                    );
                                }}
                            />
                        </Grid>
                        <Grid item>
                            <InputField
                                clearable
                                name={'label'}
                                label={strings('cyberrisk:label.label')}
                                placeholder={strings('cyberrisk:label.registerEditor.labelPlaceholder')}
                                disabled={cannotEdit(registerData?.aliasRole)}
                            />
                        </Grid>
                        <Grid item>
                            <SelectField
                                name="view"
                                label={strings('cyberrisk:label.registerEditor.selectedView')}
                                options={viewOptions}
                            />
                        </Grid>
                        <Grid item>
                            <InputField
                                clearable
                                name={'description'}
                                label={strings('cyberrisk:label.registerEditor.description')}
                                placeholder={strings('cyberrisk:label.registerEditor.descriptionPlaceholder')}
                            />
                        </Grid>
                        <Grid item>
                            <BooleanField
                                name={'showDateStatusField'}
                                label={strings('cyberrisk:label.registerEditor.showDateStatusField')}
                                formFieldLabel={strings('cyberrisk:label.registerEditor.showDateStatusFieldDescription')}
                                disabled={!dateStatusOptions?.length}
                            />
                        </Grid>
                        {showDateStatusField && dateStatusOptions?.length && (
                            <Grid item>
                                <SelectField
                                    name={'dateStatusField'}
                                    label={strings('cyberrisk:label.registerEditor.dateStatusField')}
                                    options={dateStatusOptions}
                                />
                            </Grid>
                        )}
                    </Grid>
                </form>
            </FormProvider>
            {errorMessage && (
                <Grid pt="1rem">
                    <Typography color={theme.palette.error.main}>{errorMessage}</Typography>
                </Grid>
            )}
        </Dialog>
    );
};

export default RegisterItemEditor;
