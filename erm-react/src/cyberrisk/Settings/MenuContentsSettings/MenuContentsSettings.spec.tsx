import React from 'react';
import { createMemoryRouter, RouterProvider } from 'react-router';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import MenuContentsSettings from './MenuContentsSettings';
import { mockContext, mockedStore, mockFrameworkList } from 'cyberrisk/api.mock';
import { render, screen } from 'test/utils/rtl';
import { mockNavigate } from 'test/config/setupAfterEnv';
import { act } from '@testing-library/react';

jest.mock('cyberrisk/rtkApi', () => ({
    useRcGetRepositoryHierarchyUsingGetQuery: jest.fn(() => ({
        data: [],
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
}));

jest.mock('frameworks/rtkApi', () => ({
    useFcGetFrameworkListUsingGetQuery: jest.fn(() => ({
        data: mockFrameworkList,
        isError: false,
        isSuccess: true,
        isLoading: false,
    })),
}));

jest.mock('view/rtkApi', () => ({
    useVrsGetViewsUsingGetQuery: jest.fn(() => ({
        data: {
            views: [
                { id: 1, name: 'Test View 1', scope: null },
                { id: 2, name: 'Test View 2', scope: null },
            ],
        },
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
}));

describe('MenuContentsSettings', () => {
    const renderComponent = (contextOverrides = {}) => {
        const routes = [
            {
                path: '',
                element: (
                    <ControlsContext.Provider value={{ ...mockContext, ...contextOverrides }}>
                        <MenuContentsSettings />
                    </ControlsContext.Provider>
                ),
            },
        ];

        const router = createMemoryRouter(routes);

        return render(<RouterProvider router={router} />, mockedStore);
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders the component with the correct heading', () => {
        const { container } = renderComponent();

        expect(screen.getByTestId('menu-contents-heading')).toHaveTextContent('Workspace Contents');
        expect(container).toMatchSnapshot();
    });

    it('renders the cancel and save buttons', () => {
        renderComponent();

        expect(screen.getByTestId('button-cancel')).toBeInTheDocument();
        expect(screen.getByTestId('button-save')).toBeInTheDocument();
    });

    it('disables the save button when settings are pristine', () => {
        renderComponent({
            settingsPristine: true,
        });

        expect(screen.getByTestId('button-save')).toBeDisabled();
    });

    it('enables the save button when settings are not pristine', () => {
        renderComponent({ settingsPristine: false });

        expect(screen.getByTestId('button-save')).toBeEnabled();
    });

    it('calls navigate back when the back button is clicked', async () => {
        renderComponent();

        const backButton = await screen.findByTestId('back-button');
        await act(() => backButton.click());

        expect(mockNavigate).toHaveBeenCalled();
    });

    it('calls navigate back when the cancel button is clicked', async () => {
        renderComponent();

        const cancelButton = await screen.findByTestId('button-cancel');
        await act(() => cancelButton.click());
        expect(mockNavigate).toHaveBeenCalled();
    });

    it('shows the loading overlay when isBusy is true', () => {
        renderComponent({ isBusy: true });

        expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
});
