import React from 'react';
import { render, screen } from 'test/utils/rtl';
import WSRegisterSelector, { WSRegisterSelectorProps } from './WSRegisterSelector';
import { RegisterItemContractType } from 'api/generated/types';
import { RegisterRest } from 'register/types';

const mockContractTypes: RegisterItemContractType[] = [
    {
        name: 'ASSET',
        code: 1,
        label: 'Asset',
        category: 'CYBER_RISK',
    },
    {
        name: 'CONTROL',
        code: 2,
        label: 'Control',
        category: 'CYBER_RISK',
    },
    {
        name: 'THREAT',
        code: 3,
        label: 'Threat',
        category: 'CYBER_RISK',
    },
    {
        name: 'VULNERABILITY',
        code: 4,
        label: 'Vulnerability',
        category: 'CYBER_RISK',
    },
];

jest.mock('common/components/RegisterSelector/RegisterSelector', () => {
    return jest.fn(({ title, onClose, onSelect, multiSelect, columns, contractTypes, staticExpressions, prependElements }) => (
        <div data-testid="register-selector">
            <div data-testid="register-selector-title">{title}</div>
            <div data-testid="register-selector-multiselect">{multiSelect ? 'true' : 'false'}</div>
            <div data-testid="register-selector-columns">{JSON.stringify(columns)}</div>
            <div data-testid="register-selector-contract-types">{JSON.stringify(contractTypes)}</div>
            <div data-testid="register-selector-static-expressions">{JSON.stringify(staticExpressions)}</div>
            {prependElements}
            <button
                data-testid="register-selector-close"
                onClick={onClose}
            >
                Close
            </button>
            <button
                data-testid="register-selector-select"
                onClick={() => onSelect([{ id: 1, label: 'Test Register' } as RegisterRest])}
            >
                Select
            </button>
        </div>
    ));
});

jest.mock('common/utils/i18n', () => ({
    strings: jest.fn((key: string) => {
        const translations: Record<string, string> = {
            'cyberrisk:label.selectRegister': 'Select Register',
            'common:label.registerName': 'Register name',
        };
        return translations[key] || key;
    }),
}));

describe('WSRegisterSelector', () => {
    const defaultProps: WSRegisterSelectorProps = {
        setOpen: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Rendering', () => {
        it('should render RegisterSelector with correct default props', () => {
            render(<WSRegisterSelector {...defaultProps} />);

            expect(screen.getByTestId('register-selector')).toBeInTheDocument();
            expect(screen.getByTestId('register-selector-title')).toHaveTextContent('Select Register');
            expect(screen.getByTestId('register-selector-multiselect')).toHaveTextContent('false');
        });

        it('should pass RegisterSelectorSettingsColDef as columns', () => {
            render(<WSRegisterSelector {...defaultProps} />);

            const columnsElement = screen.getByTestId('register-selector-columns');
            const columns = JSON.parse(columnsElement.textContent || '[]');

            expect(columns).toHaveLength(1);
            expect(columns[0]).toEqual({
                field: 'label',
                headerName: 'Register name',
                filterType: 'STRING',
                minWidth: 150,
                flex: 3,
                groupable: false,
            });
        });
    });

    describe('Props handling', () => {
        it('should pass contractTypes prop to RegisterSelector', () => {
            const contractTypes = [mockContractTypes[0], mockContractTypes[1]];
            render(
                <WSRegisterSelector
                    {...defaultProps}
                    contractTypes={contractTypes}
                />,
            );

            const contractTypesElement = screen.getByTestId('register-selector-contract-types');
            // Component maps contractTypes to extract only the code property
            const expectedCodes = contractTypes.map((contract) => contract.code);
            expect(JSON.parse(contractTypesElement.textContent || '[]')).toEqual(expectedCodes);
        });

        it('should handle undefined contractTypes', () => {
            render(
                <WSRegisterSelector
                    {...defaultProps}
                    contractTypes={undefined}
                />,
            );

            const contractTypesElement = screen.getByTestId('register-selector-contract-types');
            expect(contractTypesElement).toHaveTextContent('');
        });

        it('should show contract type labels in prepend elements when contractTypes provided', () => {
            const contractTypes = [mockContractTypes[0], mockContractTypes[1]];
            render(
                <WSRegisterSelector
                    {...defaultProps}
                    contractTypes={contractTypes}
                />,
            );

            // Should show the contract type labels in the input field
            // The Input component renders as a regular input with name="contract"
            const input = screen.getByDisplayValue('Asset, Control');
            expect(input).toHaveAttribute('name', 'contract');
            expect(input).toBeDisabled();
        });
    });

    describe('Event handling', () => {
        it('should call setOpen(false) when onClose is triggered', () => {
            const mockSetOpen = jest.fn();
            render(
                <WSRegisterSelector
                    {...defaultProps}
                    setOpen={mockSetOpen}
                />,
            );

            const closeButton = screen.getByTestId('register-selector-close');
            closeButton.click();

            expect(mockSetOpen).toHaveBeenCalledWith(false);
        });

        it('should call onChange and setOpen(false) when onSelect is triggered', () => {
            const mockOnChange = jest.fn();
            const mockSetOpen = jest.fn();

            render(
                <WSRegisterSelector
                    {...defaultProps}
                    onChange={mockOnChange}
                    setOpen={mockSetOpen}
                />,
            );

            const selectButton = screen.getByTestId('register-selector-select');
            selectButton.click();

            expect(mockOnChange).toHaveBeenCalledWith({ id: 1, label: 'Test Register' });
            expect(mockSetOpen).toHaveBeenCalledWith(false);
        });
    });

    describe('Static expressions', () => {
        it('should pass static expressions for framework filtering', () => {
            const contractTypes = [mockContractTypes[0]];
            render(
                <WSRegisterSelector
                    {...defaultProps}
                    contractTypes={contractTypes}
                />,
            );

            const staticExpressionsElement = screen.getByTestId('register-selector-static-expressions');
            const staticExpressions = JSON.parse(staticExpressionsElement.textContent || '[]');

            expect(staticExpressions).toHaveLength(1);
            expect(staticExpressions[0]).toEqual({
                property: 'subtableType',
                value: 'Framework',
                expression: '<>',
            });
        });

        it('should pass contract type codes to RegisterSelector', () => {
            const contractTypes = [mockContractTypes[0], mockContractTypes[1], mockContractTypes[2]];
            render(
                <WSRegisterSelector
                    {...defaultProps}
                    contractTypes={contractTypes}
                />,
            );

            const contractTypesElement = screen.getByTestId('register-selector-contract-types');
            const passedContractTypes = JSON.parse(contractTypesElement.textContent || '[]');

            expect(passedContractTypes).toEqual([1, 2, 3]);
        });
    });
});
