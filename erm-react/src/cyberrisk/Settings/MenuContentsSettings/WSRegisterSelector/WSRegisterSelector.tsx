import RegisterSelector from 'common/components/RegisterSelector/RegisterSelector';
import { RegisterSelectorSettingsColDef } from 'cyberrisk/constants';
import React, { FC, useCallback } from 'react';
import { strings } from 'common/utils/i18n';
import { RegisterRest } from 'register/types';
import Grid from '@mui/material/Grid';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import { RegisterItemContractType } from 'api/generated/types';
import Typography from '@mui/material/Typography';

export interface WSRegisterSelectorProps {
    setOpen: (isOpen: boolean) => void;
    onChange?: (register: RegisterRest) => void;
    contractTypes?: RegisterItemContractType[];
}

const WSRegisterSelector: FC<WSRegisterSelectorProps> = ({ onChange, setOpen, contractTypes, ...otherProps }) => {
    const handleSelect = useCallback(
        (selected) => {
            const register = selected[0];
            onChange?.(register);
            setOpen(false);
        },
        [onChange, setOpen],
    );

    return (
        <>
            <RegisterSelector
                {...otherProps}
                title={strings('cyberrisk:label.selectRegister')}
                multiSelect={false}
                columns={RegisterSelectorSettingsColDef}
                onClose={() => setOpen(false)}
                onSelect={handleSelect}
                prependElements={
                    contractTypes && (
                        <Grid
                            item
                            xs={12}
                            mb={'16px'}
                        >
                            <Typography
                                variant={'body2'}
                                sx={{ mb: '8px' }}
                            >
                                {strings('cyberrisk:label.registerEditor.selectorCriteria')}
                            </Typography>
                            <Input
                                disabled
                                name={'contract'}
                                value={contractTypes?.map((contract) => contract.label).join(', ')}
                            />
                        </Grid>
                    )
                }
                contractTypes={contractTypes?.map((contract) => contract.code!)}
                staticExpressions={[{ property: 'subtableType', value: 'Framework', expression: '<>' }]}
            />
        </>
    );
};

export default WSRegisterSelector;
