import { strings } from 'common/utils/i18n';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import React, { useCallback, useContext } from 'react';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { addNewSection, renameSection, setActiveSection, setActiveSettingsDialog } from 'cyberrisk/ControlsActions';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import * as Yup from 'yup';
import useForm from 'common/hooks/forms/useForm';
import { FormProvider } from 'react-hook-form';
import Grid from '@mui/material/Grid';

type GroupNamingDialogInput = {
    sectionName: string;
};

const schema: Yup.AnyObjectSchema = Yup.object({
    sectionName: Yup.string().required(strings('common:validators.requiredSimple')),
});

const GroupNamingDialog = () => {
    const { activeSection, dispatch } = useContext(ControlsContext);

    const formMethods = useForm<GroupNamingDialogInput>({
        mode: 'onChange',
        schema,
        defaultValues: { sectionName: activeSection?.name || '' },
    });

    const { handleSubmit, formState, watch } = formMethods;
    const { isValid } = formState;
    const name = watch('sectionName');

    const clearContext = useCallback(() => {
        dispatch(setActiveSection(null));
        dispatch(setActiveSettingsDialog(null));
    }, [dispatch]);

    const onSubmit = useCallback(() => {
        if (activeSection) {
            dispatch(renameSection(name));
        } else {
            dispatch(addNewSection(name));
        }
        clearContext();
    }, [dispatch, name, activeSection, clearContext]);

    return (
        <Dialog
            visible={true}
            width={500}
            title={strings('cyberrisk:label.newGroup')}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={clearContext}
                        dataTestId="button-cancel"
                    >
                        {strings('ermMessages:btn_cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        disabled={!isValid}
                        onClick={handleSubmit(onSubmit)}
                        dataTestId="button-confirm"
                    >
                        {strings('common:button.ok')}
                    </Button>
                </DialogActions>
            }
        >
            <FormProvider {...formMethods}>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid
                        direction="column"
                        container
                    >
                        <Grid item>
                            <InputField
                                autoFocus
                                label={strings('cyberrisk:label.groupName')}
                                dataTestId="new-name"
                                name="sectionName"
                                clearable
                            />
                        </Grid>
                    </Grid>
                </form>
            </FormProvider>
        </Dialog>
    );
};

export default GroupNamingDialog;
