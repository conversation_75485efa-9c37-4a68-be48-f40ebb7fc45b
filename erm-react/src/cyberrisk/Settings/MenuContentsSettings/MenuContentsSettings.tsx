import ApplicationLayout from 'common/layouts/ApplicationLayout';
import React, { FC, useCallback, useContext } from 'react';
import Typography from '@mui/material/Typography';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import BackIconButton from '@protecht/ui-library/library/components/BackIconButton';
import { strings } from 'common/utils/i18n';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { backButtonTestId } from 'common/constants';
import { generatePath, useNavigate } from 'react-router';
import LoadingOverlay from 'common/components/LoadingOverlay';
import MenuItemsStructure from './MenuItemsStructure/MenuItemsStructure';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import GroupNamingDialog from './GroupNamingDialog/GroupNameDialog';
import ItemTypeSelectorDialog from './ItemTypeSelectorDialog/ItemTypeSelectorDialog';
import FrameworkSelector from './FrameworkSelector/FrameworkSelector';
import DashboardSelector from './DashboardSelector/DashboardSelector';
import { useUnsavedChangesAlert } from 'common/hooks/useUnsavedChangesAlert';
import { ItemTypes, SettingsDialogs } from 'cyberrisk/types';
import { DialogType } from 'common/types';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';
import RegisterItemEditor from './RegisterItemEditor/RegisterItemEditor';
import WorkSpaceItem from './WorkSpaceItem/WorkSpaceItem';
import { isRegisterItem } from './utils';

const MenuContentsSettings: FC = () => {
    const navigate = useNavigate();

    const { sections, activeSettingsDialog, isBusy, settingsPristine, doUpdateControls, resetDefaults, activeMenuItem } = useContext(ControlsContext);

    useUnsavedChangesAlert({
        blockNavigation: !settingsPristine,
        onConfirm: doUpdateControls,
        onBack: () => {
            resetDefaults();
        },
        dialogType: DialogType.UNSAVED,
    });

    const back = useCallback(() => {
        void navigate(generatePath(CyberRiskRoutesEnum.CR_SETTINGS));
    }, [navigate]);

    return (
        <>
            <ApplicationLayout>
                <ToolbarContainer
                    disableGutters={false}
                    variant="regular"
                >
                    <ToolbarGroup
                        flex={1}
                        justifyContent="space-between"
                    >
                        <ToolbarGroup>
                            <BackIconButton
                                onClick={() => {
                                    back();
                                }}
                                dataTestId={backButtonTestId}
                            />
                            <Typography
                                variant="h1"
                                data-testid="menu-contents-heading"
                            >
                                {strings('cyberrisk:title.menuContents')}
                            </Typography>
                        </ToolbarGroup>
                        <ToolbarGroup>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                variant="secondary"
                                onClick={() => {
                                    back();
                                }}
                                dataTestId="button-cancel"
                            >
                                {strings('common:button.cancel')}
                            </Button>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                variant="primary"
                                disabled={settingsPristine}
                                dataTestId="button-save"
                                onClick={() => doUpdateControls()}
                            >
                                {strings('ermConstants:save')}
                            </Button>
                        </ToolbarGroup>
                    </ToolbarGroup>
                </ToolbarContainer>

                {sections && <MenuItemsStructure sectionList={sections} />}
            </ApplicationLayout>

            {activeSettingsDialog === SettingsDialogs.GROUP_NAMING && <GroupNamingDialog />}

            <ItemTypeSelectorDialog />

            {activeSettingsDialog === SettingsDialogs.FRAMEWORK_SELECTOR && <FrameworkSelector />}
            {activeSettingsDialog === SettingsDialogs.DASHBOARD_SELECTOR && <DashboardSelector />}
            {(activeSettingsDialog === SettingsDialogs.REGISTER_SELECTOR ||
                (isRegisterItem(activeMenuItem) && activeSettingsDialog === SettingsDialogs.ITEM_DETAILS)) && <RegisterItemEditor />}

            {activeSettingsDialog === SettingsDialogs.ITEM_DETAILS &&
                (ItemTypes.DASHBOARD === activeMenuItem?.itemType || ItemTypes.FRAMEWORK === activeMenuItem?.itemType) && <WorkSpaceItem />}

            <LoadingOverlay open={isBusy} />
        </>
    );
};

export default MenuContentsSettings;
