import { FrameworkColDef } from 'cyberrisk/constants';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { strings } from 'common/utils/i18n';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { ItemTypes, MenuItemDialogType, SettingsDialogs } from 'cyberrisk/types';
import { setActiveMenuItem, setActiveSettingsDialog } from 'cyberrisk/ControlsActions';
import { Table } from '@protecht/ui-library/library/components/Table';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { GridPaginationModel, GridRowSelectionModel } from '@mui/x-data-grid-pro';
import Grid from '@mui/material/Grid';
import { useFcGetFrameworkListUsingGetQuery } from 'frameworks/rtkApi';
import { FrameworkItem, FrameworkStubRead } from 'api/generated/types';
import Search from '@protecht/ui-library/library/components/Inputs/Search';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';

export type FrameworkStubReadWithId = Omit<FrameworkStubRead, 'id'> & { id: number };

export interface FrameworkSelectorProps {
    setOpen?: (isOpen: boolean) => void;
    onChange?: (register: FrameworkStubRead) => void;
}

const FrameworkSelector = ({ setOpen, onChange }: FrameworkSelectorProps) => {
    const { dispatch, activeMenuItem, menuItemDialogType } = useContext(ControlsContext);
    const [currentSelection, setCurrentSelection] = useState<FrameworkStubRead>();
    const [searchValue, setSearchValue] = useState('');
    const [filteredItems, setFilteredItems] = useState<FrameworkStubRead[] | undefined>(undefined);
    const [params, setParams] = useState<SearchRequestParams | undefined>(undefined);
    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({ page: 0, pageSize: 10 });
    const { data, isLoading } = useFcGetFrameworkListUsingGetQuery();

    const handleSelection = (selectionModel: GridRowSelectionModel) => {
        const selectedId = selectionModel[selectionModel.length - 1];
        setCurrentSelection(data?.find((item) => selectedId === item.id));
    };

    const onValueChanged = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchValue(event.target.value);
    }, []);

    useEffect(() => {
        if (searchValue?.length) {
            setParams((params) => ({ ...params, page: 0 }));
            const filtered = data?.filter((row) => row['name']?.toLowerCase().includes(searchValue.toLowerCase()));
            setFilteredItems(filtered);
        } else {
            setFilteredItems(data);
        }
    }, [searchValue, data]);

    const handleConfirm = useCallback(() => {
        dispatch(
            setActiveMenuItem(
                {
                    ...activeMenuItem,
                    itemType: ItemTypes.FRAMEWORK,
                    name: currentSelection?.name,
                    frameworkId: currentSelection?.id,
                } as FrameworkItem,
                menuItemDialogType!,
            ),
        );
        dispatch(setActiveSettingsDialog(SettingsDialogs.ITEM_DETAILS));
        setOpen?.(false);
        onChange?.(currentSelection!);
    }, [dispatch, setOpen, onChange, activeMenuItem, menuItemDialogType, currentSelection]);

    const handleCancel = useCallback(() => {
        if (menuItemDialogType === MenuItemDialogType.NEW) {
            dispatch(setActiveSettingsDialog(null));
        } else {
            dispatch(setActiveSettingsDialog(SettingsDialogs.ITEM_DETAILS));
        }
        setOpen?.(false);
    }, [dispatch, setOpen, menuItemDialogType]);

    return (
        <Dialog
            title={strings('cyberrisk:label.selectFramework')}
            visible={true}
            width={1012}
            height={674}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={handleCancel}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>

                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!currentSelection}
                        onClick={handleConfirm}
                    >
                        {strings('common:button.ok')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                direction="column"
                sx={{ height: '100%', width: '100%', overflow: 'hidden', flexWrap: 'nowrap' }}
            >
                <Grid
                    item
                    sx={{ flex: '0 0 auto' }}
                >
                    <ToolbarContainer>
                        <Search
                            searchValue={searchValue}
                            onValueChanged={onValueChanged}
                            searchPlaceholder={strings('common:placeholder.search')}
                        />
                    </ToolbarContainer>
                </Grid>
                <Grid
                    item
                    sx={{
                        flex: '1 1 auto',
                        overflow: 'auto',
                    }}
                >
                    <Table<FrameworkStubReadWithId>
                        paginationMode={'client'}
                        autoPageSize
                        disableColumnMenu
                        disableMultipleRowSelection={true}
                        hideFooterSelectedRowCount
                        columnHeaderHeight={ThemeConfig.rowHeight}
                        rowHeight={ThemeConfig.rowHeight}
                        columns={FrameworkColDef}
                        params={params}
                        onParamsChanged={setParams}
                        paginationModel={paginationModel}
                        onPaginationModelChange={setPaginationModel}
                        rows={(filteredItems as FrameworkStubReadWithId[]) || []}
                        totalCount={filteredItems?.length || 0}
                        rowSelectionModel={currentSelection ? [currentSelection.id as Required<number>] : []}
                        onRowSelectionModelChange={handleSelection}
                        loading={isLoading}
                        loadingMessage={strings('common:message.loading')}
                        multiselect={false}
                    />
                </Grid>
            </Grid>
        </Dialog>
    );
};

export default FrameworkSelector;
