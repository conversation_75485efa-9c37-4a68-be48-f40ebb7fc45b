import React from 'react';
import { render, screen } from 'test/utils';
import { waitFor } from '@testing-library/react';
import FrameworkSelector from './FrameworkSelector';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { ItemTypes, MenuItemDialogType, SettingsDialogs } from 'cyberrisk/types';
import { mockFrameworkList } from 'cyberrisk/api.mock';
import { useFcGetFrameworkListUsingGetQuery } from 'frameworks/rtkApi';

ThemeConfig.rowHeight = 2; // set to low value to show more rows in the test environment

jest.mock('common/utils/i18n', () => ({
    strings: jest.fn((key: string) => {
        const mockStrings = {
            'cyberrisk:label.selectFramework': 'Select Framework',
            'common:button.cancel': 'Cancel',
            'common:button.ok': 'OK',
            'common:placeholder.search': 'Search...',
            'common:message.loading': 'Loading...',
            'cyberrisk:label.fwTitle': 'Framework Title',
            'common:label.version': 'Version',
            'common:label.status': 'Status',
        };
        return mockStrings[key] || key;
    }),
}));

jest.mock('frameworks/rtkApi', () => ({
    ...jest.requireActual('frameworks/rtkApi'),
    useFcGetFrameworkListUsingGetQuery: jest.fn(() => ({
        data: mockFrameworkList,
        isError: false,
        isSuccess: true,
        isLoading: false,
    })),
}));

describe('FrameworkSelector', () => {
    const mockDispatch = jest.fn();
    const defaultContextValue = {
        activeSettingsDialog: SettingsDialogs.FRAMEWORK_SELECTOR,
        dispatch: mockDispatch,
        itemType: null,
        activeSection: null,
        sections: [],
        isBusy: false,
        settingsPristine: true,
        getLibrary: jest.fn(),
        defaultWorkspaceName: 'Test Workspace',
        removeSectionItem: jest.fn(),
        removeItem: jest.fn(),
        doUpdateControls: jest.fn(),
        resetDefaults: jest.fn(),
        activeMenuItem: null,
        menuItemDialogType: null,
        fixedLevel: undefined,
    };

    const renderWithContext = (contextValue = {}) => {
        const mergedContextValue = { ...defaultContextValue, ...contextValue };

        return render(
            <ControlsContext.Provider value={mergedContextValue}>
                <FrameworkSelector />
            </ControlsContext.Provider>,
        );
    };

    beforeEach(() => {
        jest.clearAllMocks();
        // Reset the API mock to default state before each test
        (useFcGetFrameworkListUsingGetQuery as jest.Mock).mockReturnValue({
            data: mockFrameworkList,
            isError: false,
            isSuccess: true,
            isLoading: false,
        });
    });

    describe('Basic Rendering', () => {
        it('should render dialog with correct title', () => {
            renderWithContext();

            expect(screen.getByText('Select Framework')).toBeInTheDocument();
        });

        it('should render dialog with correct dimensions', () => {
            renderWithContext();

            expect(screen.getByText('Select Framework')).toBeInTheDocument();
        });

        it('should render search input', () => {
            renderWithContext();

            expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
        });

        it('should render cancel and OK buttons', () => {
            renderWithContext();

            expect(screen.getByTestId('button-cancel')).toBeInTheDocument();
            expect(screen.getByTestId('button-confirm')).toBeInTheDocument();
            expect(screen.getByText('Cancel')).toBeInTheDocument();
            expect(screen.getByText('OK')).toBeInTheDocument();
        });
    });

    describe('Framework List Display', () => {
        it('should display frameworks when data is loaded', async () => {
            renderWithContext();

            expect(screen.getByText('Select Framework')).toBeInTheDocument();

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
                expect(screen.getByText('Framework Two')).toBeInTheDocument();
                expect(screen.getByText('Another Framework')).toBeInTheDocument();
            });
        });
    });

    describe('Search Functionality', () => {
        it('should filter frameworks based on search input', async () => {
            const { user } = renderWithContext();

            const searchInput = screen.getByPlaceholderText('Search...');
            await user.type(searchInput, 'Framework One');

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
                expect(screen.queryByText('Framework Two')).not.toBeInTheDocument();
                expect(screen.queryByText('Another Framework')).not.toBeInTheDocument();
            });
        });

        it('should filter frameworks case-insensitively', async () => {
            const { user } = renderWithContext();

            const searchInput = screen.getByPlaceholderText('Search...');
            await user.type(searchInput, 'framework one');

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
                expect(screen.queryByText('Framework Two')).not.toBeInTheDocument();
            });
        });

        it('should show all frameworks when search is cleared', async () => {
            const { user } = renderWithContext();

            const searchInput = screen.getByPlaceholderText('Search...');

            // Type search term
            await user.type(searchInput, 'Framework One');
            await waitFor(() => {
                expect(screen.queryByText('Framework Two')).not.toBeInTheDocument();
            });

            // Clear search
            await user.clear(searchInput);
            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
                expect(screen.getByText('Framework Two')).toBeInTheDocument();
                expect(screen.getByText('Another Framework')).toBeInTheDocument();
            });
        });

        it('should show no results when search matches nothing', async () => {
            const { user } = renderWithContext();

            const searchInput = screen.getByPlaceholderText('Search...');
            await user.type(searchInput, 'NonExistentFramework');

            await waitFor(() => {
                expect(screen.queryByText('Framework One')).not.toBeInTheDocument();
                expect(screen.queryByText('Framework Two')).not.toBeInTheDocument();
                expect(screen.queryByText('Another Framework')).not.toBeInTheDocument();
            });
        });

        it('should reset pagination to first page when searching', async () => {
            const { user } = renderWithContext();

            const searchInput = screen.getByPlaceholderText('Search...');
            await user.type(searchInput, 'Framework');

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });
        });
    });

    describe('Framework Selection', () => {
        it('should disable OK button when no framework is selected', () => {
            renderWithContext();

            const okButton = screen.getByTestId('button-confirm');
            expect(okButton).toBeDisabled();
        });

        it('should enable OK button when a framework is selected', async () => {
            const { user } = renderWithContext();

            // Wait for frameworks to load
            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });

            // Click on a framework row to select it
            const frameworkRow = screen.getByText('Framework One');
            await user.click(frameworkRow);

            await waitFor(() => {
                const okButton = screen.getByTestId('button-confirm');
                expect(okButton).toBeEnabled();
            });
        });

        it('should update selection when different framework is clicked', async () => {
            const { user } = renderWithContext();

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });

            // Select first framework
            await user.click(screen.getByText('Framework One'));

            await waitFor(() => {
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });

            // Select second framework
            await user.click(screen.getByText('Framework Two'));

            await waitFor(() => {
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });
        });
    });

    describe('Button Actions', () => {
        it('should dispatch setActiveSettingsDialog(null) when cancel button is clicked', async () => {
            const { user } = renderWithContext({ ...defaultContextValue, menuItemDialogType: MenuItemDialogType.NEW });

            const cancelButton = screen.getByTestId('button-cancel');
            await user.click(cancelButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: null },
            });
        });

        it('should dispatch setActiveSettingsDialog(ITEM_DETAILS) when cancel button is clicked', async () => {
            const { user } = renderWithContext();

            const cancelButton = screen.getByTestId('button-cancel');
            await user.click(cancelButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: SettingsDialogs.ITEM_DETAILS },
            });
        });

        it('should dispatch correct actions when OK button is clicked with selection', async () => {
            const { user } = renderWithContext({
                menuItemDialogType: MenuItemDialogType.NEW,
            });

            // Wait for frameworks to load
            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });

            // Select a framework
            await user.click(screen.getByText('Framework One'));

            await waitFor(() => {
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });

            // Click OK button
            const okButton = screen.getByTestId('button-confirm');
            await user.click(okButton);

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_MENU_ITEM',
                payload: {
                    item: {
                        itemType: ItemTypes.FRAMEWORK,
                        name: 'Framework One',
                        frameworkId: 1,
                    },
                    dialogType: MenuItemDialogType.NEW,
                },
            });

            expect(mockDispatch).toHaveBeenCalledWith({
                type: 'SET_ACTIVE_DIALOG',
                payload: { settingsDialogType: SettingsDialogs.ITEM_DETAILS },
            });
        });

        it('should not dispatch actions when OK button is clicked without selection', async () => {
            renderWithContext();

            const okButton = screen.getByTestId('button-confirm');
            expect(okButton).toBeDisabled();

            // Should not dispatch any actions related to menu item creation
            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'SET_ACTIVE_MENU_ITEM',
                }),
            );
        });
    });

    describe('Table Configuration', () => {
        it('should configure table with correct properties', () => {
            renderWithContext();

            // Verify table is rendered (we can't easily test all props, but we can verify it's there)
            expect(screen.getByRole('grid')).toBeInTheDocument();
        });

        it('should handle pagination correctly', async () => {
            renderWithContext();

            // Wait for table to load
            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });

            // Table should be present with pagination
            expect(screen.getByRole('grid')).toBeInTheDocument();
        });

        it('should show correct total count', async () => {
            renderWithContext();

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });

            // The table should show all frameworks
            expect(screen.getByText('Framework One')).toBeInTheDocument();
            expect(screen.getByText('Framework Two')).toBeInTheDocument();
            expect(screen.getByText('Another Framework')).toBeInTheDocument();
        });
    });

    describe('Selection Model', () => {
        it('should maintain selection model correctly', async () => {
            const { user } = renderWithContext();

            await waitFor(() => {
                expect(screen.getByText('Framework Two')).toBeInTheDocument();
            });

            // Select Framework Two
            await user.click(screen.getByText('Framework Two'));

            await waitFor(() => {
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });
        });
    });

    describe('Search and Selection Interaction', () => {
        it('should maintain selection when search filters include selected item', async () => {
            const { user } = renderWithContext();

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });

            // Select Framework One
            await user.click(screen.getByText('Framework One'));

            await waitFor(() => {
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });

            // Search for "Framework One"
            const searchInput = screen.getByPlaceholderText('Search...');
            await user.type(searchInput, 'Framework One');

            // Selection should be maintained
            await waitFor(() => {
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });
        });

        it('should clear selection when search filters exclude selected item', async () => {
            const { user } = renderWithContext();

            await waitFor(() => {
                expect(screen.getByText('Framework One')).toBeInTheDocument();
            });

            // Select Framework One
            await user.click(screen.getByText('Framework One'));

            await waitFor(() => {
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });

            // Search for something that excludes Framework One
            const searchInput = screen.getByPlaceholderText('Search...');
            await user.type(searchInput, 'Framework Two');

            // Framework One should not be visible, but selection state might persist
            await waitFor(() => {
                expect(screen.queryByText('Framework One')).not.toBeInTheDocument();
                expect(screen.getByText('Framework Two')).toBeInTheDocument();
            });
        });

        describe('Props Integration', () => {
            it('should call setOpen prop when provided and cancel is clicked', async () => {
                const mockSetOpen = jest.fn();
                const { user } = render(
                    <ControlsContext.Provider value={{ ...defaultContextValue, menuItemDialogType: MenuItemDialogType.NEW }}>
                        <FrameworkSelector setOpen={mockSetOpen} />
                    </ControlsContext.Provider>,
                );

                const cancelButton = screen.getByTestId('button-cancel');
                await user.click(cancelButton);

                expect(mockDispatch).toHaveBeenCalledWith({
                    type: 'SET_ACTIVE_DIALOG',
                    payload: { settingsDialogType: null },
                });
                expect(mockSetOpen).toHaveBeenCalledWith(false);
            });

            it('should call onChange prop when framework is selected and confirmed', async () => {
                const mockOnChange = jest.fn();
                const mockSetOpen = jest.fn();
                const { user } = render(
                    <ControlsContext.Provider value={{ ...defaultContextValue, menuItemDialogType: MenuItemDialogType.NEW }}>
                        <FrameworkSelector
                            onChange={mockOnChange}
                            setOpen={mockSetOpen}
                        />
                    </ControlsContext.Provider>,
                );

                await waitFor(() => {
                    expect(screen.getByText('Framework One')).toBeInTheDocument();
                });

                await user.click(screen.getByText('Framework One'));
                await waitFor(() => {
                    expect(screen.getByTestId('button-confirm')).toBeEnabled();
                });

                const okButton = screen.getByTestId('button-confirm');
                await user.click(okButton);

                expect(mockOnChange).toHaveBeenCalledWith(mockFrameworkList[0]);
                expect(mockSetOpen).toHaveBeenCalledWith(false);
            });
        });

        describe('Loading States', () => {
            it('should show loading state when frameworks are being fetched', () => {
                (useFcGetFrameworkListUsingGetQuery as jest.Mock).mockReturnValue({
                    data: undefined,
                    isLoading: true,
                    isError: false,
                    isSuccess: false,
                });

                renderWithContext();

                expect(screen.getByText('Loading...')).toBeInTheDocument();
            });

            it('should handle empty framework list', () => {
                (useFcGetFrameworkListUsingGetQuery as jest.Mock).mockReturnValue({
                    data: [],
                    isLoading: false,
                    isError: false,
                    isSuccess: true,
                });

                renderWithContext();

                expect(screen.getByText('Select Framework')).toBeInTheDocument();
                expect(screen.queryByText('Framework One')).not.toBeInTheDocument();
            });

            it('should handle undefined framework data', () => {
                (useFcGetFrameworkListUsingGetQuery as jest.Mock).mockReturnValue({
                    data: undefined,
                    isLoading: false,
                    isError: false,
                    isSuccess: true,
                });

                renderWithContext();

                expect(screen.getByText('Select Framework')).toBeInTheDocument();
                expect(screen.queryByText('Framework One')).not.toBeInTheDocument();
            });

            it('should handle API error state gracefully', () => {
                (useFcGetFrameworkListUsingGetQuery as jest.Mock).mockReturnValue({
                    data: undefined,
                    isLoading: false,
                    isError: true,
                    error: { message: 'API Error' },
                    isSuccess: false,
                });

                renderWithContext();

                expect(screen.getByText('Select Framework')).toBeInTheDocument();
                expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
            });
        });

        describe('Menu Item Dialog Types', () => {
            it('should handle NEW dialog type correctly', async () => {
                const { user } = renderWithContext({
                    menuItemDialogType: MenuItemDialogType.NEW,
                    activeMenuItem: null,
                });

                await waitFor(() => {
                    expect(screen.getByText('Framework One')).toBeInTheDocument();
                });

                await user.click(screen.getByText('Framework One'));
                await waitFor(() => {
                    expect(screen.getByTestId('button-confirm')).toBeEnabled();
                });

                const okButton = screen.getByTestId('button-confirm');
                await user.click(okButton);

                expect(mockDispatch).toHaveBeenCalledWith({
                    type: 'SET_ACTIVE_MENU_ITEM',
                    payload: {
                        item: {
                            itemType: ItemTypes.FRAMEWORK,
                            name: 'Framework One',
                            frameworkId: 1,
                        },
                        dialogType: MenuItemDialogType.NEW,
                    },
                });
            });

            it('should handle EDIT dialog type correctly', async () => {
                const { user } = renderWithContext({
                    menuItemDialogType: MenuItemDialogType.EDIT,
                    activeMenuItem: {
                        itemType: ItemTypes.FRAMEWORK,
                        name: 'Existing Framework',
                        frameworkId: 999,
                        order: 5,
                    },
                });

                await waitFor(() => {
                    expect(screen.getByText('Framework Two')).toBeInTheDocument();
                });

                await user.click(screen.getByText('Framework Two'));
                await waitFor(() => {
                    expect(screen.getByTestId('button-confirm')).toBeEnabled();
                });

                const okButton = screen.getByTestId('button-confirm');
                await user.click(okButton);

                expect(mockDispatch).toHaveBeenCalledWith({
                    type: 'SET_ACTIVE_MENU_ITEM',
                    payload: {
                        item: {
                            itemType: ItemTypes.FRAMEWORK,
                            name: 'Framework Two',
                            frameworkId: 2,
                            order: 5,
                        },
                        dialogType: MenuItemDialogType.EDIT,
                    },
                });
            });
        });

        describe('Framework Data Handling', () => {
            it('should handle frameworks with missing properties gracefully', () => {
                const incompleteFrameworkList = [
                    {
                        id: 1,
                        name: 'Complete Framework',
                        version: '1.0',
                        status: 'Active',
                    },
                    {
                        id: 2,
                        name: 'Incomplete Framework',
                        // missing version and status
                    },
                ];

                (useFcGetFrameworkListUsingGetQuery as jest.Mock).mockReturnValue({
                    data: incompleteFrameworkList,
                    isLoading: false,
                    isError: false,
                    isSuccess: true,
                });

                renderWithContext();

                expect(screen.getByText('Complete Framework')).toBeInTheDocument();
                expect(screen.getByText('Incomplete Framework')).toBeInTheDocument();
            });

            it('should handle frameworks with null or undefined names', () => {
                const frameworksWithNullNames = [
                    {
                        id: 1,
                        name: null,
                        version: '1.0',
                        status: 'Active',
                    },
                    {
                        id: 2,
                        name: undefined,
                        version: '2.0',
                        status: 'Draft',
                    },
                    {
                        id: 3,
                        name: 'Valid Framework',
                        version: '1.5',
                        status: 'Active',
                    },
                ];

                (useFcGetFrameworkListUsingGetQuery as jest.Mock).mockReturnValue({
                    data: frameworksWithNullNames,
                    isLoading: false,
                    isError: false,
                    isSuccess: true,
                });

                renderWithContext();

                expect(screen.getByText('Valid Framework')).toBeInTheDocument();
            });
        });

        describe('Search Edge Cases', () => {
            it('should handle search with special characters', async () => {
                const { user } = renderWithContext();

                const searchInput = screen.getByPlaceholderText('Search...');
                await user.type(searchInput, '!@#$%^&*()');

                await waitFor(() => {
                    expect(screen.queryByText('Framework One')).not.toBeInTheDocument();
                    expect(screen.queryByText('Framework Two')).not.toBeInTheDocument();
                    expect(screen.queryByText('Another Framework')).not.toBeInTheDocument();
                });
            });

            it('should handle search with very long strings', async () => {
                const { user } = renderWithContext();

                const longSearchString = 'a'.repeat(100); // Reduced from 1000 to avoid timeout
                const searchInput = screen.getByPlaceholderText('Search...');
                await user.type(searchInput, longSearchString);

                await waitFor(() => {
                    expect(screen.queryByText('Framework One')).not.toBeInTheDocument();
                });
            });

            it('should handle search with whitespace only', async () => {
                const { user } = renderWithContext();

                const searchInput = screen.getByPlaceholderText('Search...');
                await user.type(searchInput, '   ');

                await waitFor(() => {
                    expect(screen.getByText('Framework One')).toBeInTheDocument();
                    expect(screen.getByText('Framework Two')).toBeInTheDocument();
                    expect(screen.getByText('Another Framework')).toBeInTheDocument();
                });
            });
        });

        describe('Selection Persistence', () => {
            it('should maintain selection when filtered data changes', async () => {
                const { user } = renderWithContext();

                await waitFor(() => {
                    expect(screen.getByText('Framework One')).toBeInTheDocument();
                });

                // Select Framework One
                await user.click(screen.getByText('Framework One'));
                await waitFor(() => {
                    expect(screen.getByTestId('button-confirm')).toBeEnabled();
                });

                // Search for something that includes Framework One
                const searchInput = screen.getByPlaceholderText('Search...');
                await user.type(searchInput, 'Framework');

                await waitFor(() => {
                    expect(screen.getByText('Framework One')).toBeInTheDocument();
                    expect(screen.getByText('Framework Two')).toBeInTheDocument();
                });

                // The selection should be maintained when the item is still visible
                expect(screen.getByTestId('button-confirm')).toBeEnabled();
            });
        });

        describe('Error Handling', () => {
            it('should handle missing menuItemDialogType gracefully', async () => {
                const { user } = renderWithContext({
                    menuItemDialogType: null,
                });

                await waitFor(() => {
                    expect(screen.getByText('Framework One')).toBeInTheDocument();
                });

                await user.click(screen.getByText('Framework One'));
                await waitFor(() => {
                    expect(screen.getByTestId('button-confirm')).toBeEnabled();
                });

                // Should not crash when clicking OK with null menuItemDialogType
                const okButton = screen.getByTestId('button-confirm');
                await user.click(okButton);

                // Should still dispatch actions even with null dialogType
                expect(mockDispatch).toHaveBeenCalled();
            });

            it('should handle missing activeMenuItem gracefully', async () => {
                const { user } = renderWithContext({
                    activeMenuItem: null,
                    menuItemDialogType: MenuItemDialogType.NEW,
                });

                await waitFor(() => {
                    expect(screen.getByText('Framework One')).toBeInTheDocument();
                });

                await user.click(screen.getByText('Framework One'));
                await waitFor(() => {
                    expect(screen.getByTestId('button-confirm')).toBeEnabled();
                });

                const okButton = screen.getByTestId('button-confirm');
                await user.click(okButton);

                expect(mockDispatch).toHaveBeenCalledWith({
                    type: 'SET_ACTIVE_MENU_ITEM',
                    payload: {
                        item: {
                            itemType: ItemTypes.FRAMEWORK,
                            name: 'Framework One',
                            frameworkId: 1,
                        },
                        dialogType: MenuItemDialogType.NEW,
                    },
                });
            });
        });
    });
});
