import React, { FC, useContext } from 'react';
import MetricsDisplay, { getScreenSettings, ScreenSettingsItem } from 'common/components/MetricsDisplay';
import { Module, WorkspaceMetric } from 'metrics/types';
import { SettingsSection } from 'common/layouts/SettingsLayout';
import ScreenSettings from 'common/components/MetricsDisplay/ScreenSettings';
import { strings } from 'common/utils/i18n';
import { METRICS_SELECTION_LIMIT } from 'vendorRiskManagement/constants';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { isRegisterItem } from 'cyberrisk/Settings/MenuContentsSettings/utils';
import { RegisterItem } from 'api/generated/types';

const CRMetricsDisplay: FC = () => {
    const { sections } = useContext(ControlsContext);

    const registerItems =
        sections?.flatMap((section) => section.itemList).filter((item) => isRegisterItem(item) && (item as RegisterItem).hideMetrics === false) || [];

    return (
        <MetricsDisplay
            homePath={CyberRiskRoutesEnum.CR_SETTINGS}
            getMetricContexts={(data) => {
                return Object.entries(data).map(([context, metrics]: [string, ScreenSettingsItem[]]) => ({
                    context: context,
                    metricDisplays: metrics.map((metric: ScreenSettingsItem, index: number) => ({
                        metricId: metric.originalId,
                        metricName: metric.name,
                        metricOrder: index,
                    })),
                }));
            }}
            defaultValues={registerItems?.reduce((acc, currentValue) => {
                acc[currentValue!.id!] = [];
                return acc;
            }, {})}
            getInitialValues={(metricDisplayConfig) =>
                metricDisplayConfig?.metricModule?.metricContexts?.reduce((acc, currentValue) => {
                    if (currentValue.context) {
                        acc[currentValue.context!] = getScreenSettings(currentValue.context!, metricDisplayConfig);
                    }
                    return acc;
                }, {}) as Record<string, ScreenSettingsItem[]>
            }
            module={Module.cyberRisk}
            workspaceModule={WorkspaceMetric.CYBER_RISK}
            renderSettings={() => (
                <>
                    {registerItems.map((item) => {
                        const itemId = item!.id!;
                        return (
                            <SettingsSection
                                width="60%"
                                key={itemId}
                            >
                                <ScreenSettings
                                    module={Module.cyberRisk}
                                    title={`${item!.alias} - ${strings('cyberrisk:label.metricsDisplay')}`}
                                    addButtonText={strings('cyberrisk:button.metricsToDisplayAdd', { name: item!.alias })}
                                    formPropName={itemId}
                                    selectionLimit={METRICS_SELECTION_LIMIT}
                                    workspaceModule={WorkspaceMetric.CYBER_RISK}
                                />
                            </SettingsSection>
                        );
                    })}
                </>
            )}
        />
    );
};

export default CRMetricsDisplay;
