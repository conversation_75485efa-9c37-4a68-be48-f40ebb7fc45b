// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CRMetricsDisplay Component Rendering renders the metrics display with default context 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorSecondary MuiIconButton-sizeMedium css-enqixr-MuiButtonBase-root-MuiIconButton-root"
            data-testid="back"
            tabindex="0"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-arrow-left "
              color="#1B4AD5"
              data-icon="arrow-left"
              data-prefix="fas"
              focusable="false"
              role="img"
              style="font-size: 22px;"
              viewBox="0 0 448 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"
                fill="currentColor"
              />
            </svg>
          </button>
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metrics-display-heading"
          >
            Metrics to Display
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-cancel"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Close
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-save"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-1thsre2"
        >
          <div
            class="MuiGrid-root MuiGrid-container css-emutsh-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1ww6kz5-MuiGrid-root"
            >
              <h2
                class="MuiTypography-root MuiTypography-h2 css-1d101aq-MuiTypography-root"
              >
                Item 1 - Metrics displayed (from left-right)
              </h2>
              <h2
                class="MuiTypography-root MuiTypography-h2 css-8ckxue-MuiTypography-root"
              >
                -
              </h2>
              <h2
                class="MuiTypography-root MuiTypography-h2 css-1wiidoj-MuiTypography-root"
              />
            </div>
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-grid-xs-12 css-p7db2m-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <ul
                  class="css-cuxljv"
                />
                <div
                  id="DndDescribedBy-0"
                  style="display: none;"
                >
                  
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                </div>
                <div
                  aria-atomic="true"
                  aria-live="assertive"
                  id="DndLiveRegion-0"
                  role="status"
                  style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                />
              </div>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
            >
              <button
                class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-qfe5n8-MuiButtonBase-root-MuiButton-root"
                data-testid="add-d3b3f97a-7ed3-41c7-aea1-0c090d367e47"
                tabindex="0"
                type="button"
              >
                <span
                  class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
                >
                  <svg
                    data-icon="add"
                    fill="currentColor"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4.5 10.75h15v2.5h-15z"
                      fill="currentColor"
                    />
                    <path
                      d="M10.75 19.5v-15h2.5v15z"
                      fill="currentColor"
                    />
                  </svg>
                </span>
                <span
                  class="css-qv0y8m"
                >
                  Metric to Item 1
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
