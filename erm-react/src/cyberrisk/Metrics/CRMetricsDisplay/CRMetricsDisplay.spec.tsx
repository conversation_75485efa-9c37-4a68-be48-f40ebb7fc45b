import { act, waitFor } from '@testing-library/react';
import { render, screen } from 'test/utils';
import { createMemoryRouter, generatePath, RouterProvider } from 'react-router';
import React from 'react';
import { mockNavigate } from 'test/config/setupAfterEnv';
import CRMetricsDisplay from './CRMetricsDisplay';
import { mockCyberriskMetricsConfig, mockedStore, mockContext } from 'cyberrisk/api.mock';
import { mockMetricEntries } from 'vendorRiskManagement/api.mock';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { ItemTypes } from 'cyberrisk/types';
import { RegisterItem, FrameworkItem, DashboardItem } from 'api/generated/types';

jest.mock('metrics/api', () => ({
    searchMetrics: jest.fn(() => Promise.resolve(mockMetricEntries)),
}));

const mockUseWrsGetDisplayConfigurationUsingGetQuery = jest.fn(() => ({
    data: mockCyberriskMetricsConfig,
    isLoading: false,
    isSuccess: true,
    isError: false,
}));

jest.mock('cyberrisk/rtkApi', () => ({
    ...jest.requireActual('cyberrisk/rtkApi'),
    useWrsGetDisplayConfigurationUsingGetQuery: () => mockUseWrsGetDisplayConfigurationUsingGetQuery(),
}));

const renderComponent = (contextValue = mockContext) => {
    const path = CyberRiskRoutesEnum.CR_METRICS_TO_DISPLAY;

    const routes = [
        {
            path: path,
            element: (
                <ControlsContext.Provider value={contextValue}>
                    <CRMetricsDisplay />
                </ControlsContext.Provider>
            ),
        },
    ];

    const router = createMemoryRouter(routes, {
        initialEntries: [generatePath(path)],
    });

    return render(<RouterProvider router={router} />, mockedStore);
};

describe('CRMetricsDisplay', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Component Rendering', () => {
        it('renders the metrics display with default context', async () => {
            const view = renderComponent();
            await waitFor(() => expect(view.container).toBeInTheDocument());
            expect(view.container).toMatchSnapshot();
        });

        it('renders settings sections for register items only', async () => {
            renderComponent();

            // Should render section for register item (Item 1)
            await waitFor(() => {
                expect(screen.getByText(/Item 1 - Metrics Display/i)).toBeInTheDocument();
            });

            // Should not render section for framework item (Item 2)
            expect(screen.queryByText(/Item 2 - Metrics Display/i)).not.toBeInTheDocument();
        });

        it('renders add metric buttons for each register item', async () => {
            renderComponent();

            // Should have add button for Item 1
            const addButton = screen.getByTestId('add-d3b3f97a-7ed3-41c7-aea1-0c090d367e47');
            expect(addButton).toBeInTheDocument();
        });

        it('renders with empty sections', async () => {
            const emptyContext = {
                ...mockContext,
                sections: [],
            };

            const view = renderComponent(emptyContext);
            await waitFor(() => expect(view.container).toBeInTheDocument());

            // Should not have any settings sections
            expect(screen.queryByText(/Metrics Display/i)).not.toBeInTheDocument();
        });
    });

    describe('Register Item Filtering', () => {
        it('filters out items with hideMetrics set to true', async () => {
            const contextWithHiddenMetrics = {
                ...mockContext,
                sections: [
                    {
                        id: 'section-1',
                        name: 'Section 1',
                        itemList: [
                            {
                                id: 'item-1',
                                alias: 'Visible Item',
                                name: 'Register 1',
                                itemType: ItemTypes.REGISTER,
                                tableName: 'table1',
                                viewId: 201,
                                hideMetrics: false,
                            } as RegisterItem,
                            {
                                id: 'item-2',
                                alias: 'Hidden Item',
                                name: 'Register 2',
                                itemType: ItemTypes.REGISTER,
                                tableName: 'table2',
                                viewId: 202,
                                hideMetrics: true,
                            } as RegisterItem,
                            {
                                id: 'item-3',
                                alias: 'Visible Item 3',
                                name: 'Register 3',
                                itemType: ItemTypes.REGISTER,
                                tableName: 'table2',
                                viewId: 202,
                            } as RegisterItem,
                        ],
                    },
                ],
            };

            renderComponent(contextWithHiddenMetrics);

            // Should render section for visible item
            await waitFor(() => {
                expect(screen.getByText(/Visible Item - Metrics Display/i)).toBeInTheDocument();
            });

            // Should not render section for hidden item
            expect(screen.queryByText(/Hidden Item - Metrics Display/i)).not.toBeInTheDocument();
            expect(screen.queryByText(/Visible Item 3 - Metrics Display/i)).not.toBeInTheDocument();
        });

        it('filters out framework items', async () => {
            const contextWithMixedItems = {
                ...mockContext,
                sections: [
                    {
                        id: 'section-1',
                        name: 'Section 1',
                        itemList: [
                            {
                                id: 'register-item',
                                alias: 'Register Item',
                                name: 'Register 1',
                                itemType: ItemTypes.REGISTER,
                                tableName: 'table1',
                                viewId: 201,
                                hideMetrics: false,
                                order: 0,
                            } as RegisterItem,
                            {
                                id: 'framework-item',
                                alias: 'Framework Item',
                                name: 'Framework 1',
                                itemType: ItemTypes.FRAMEWORK,
                                frameworkId: 123,
                                viewId: 202,
                                order: 1,
                            } as FrameworkItem,
                        ],
                    },
                ],
            };

            renderComponent(contextWithMixedItems);

            // Should render section for register item
            await waitFor(() => {
                expect(screen.getByText(/Register Item - Metrics Display/i)).toBeInTheDocument();
            });

            // Should not render section for framework item
            expect(screen.queryByText(/Framework Item - Metrics Display/i)).not.toBeInTheDocument();
        });

        it('filters out dashboard items', async () => {
            const contextWithMixedItems = {
                ...mockContext,
                sections: [
                    {
                        id: 'section-1',
                        name: 'Section 1',
                        itemList: [
                            {
                                id: 'register-item',
                                alias: 'Register Item',
                                name: 'Register 1',
                                itemType: ItemTypes.REGISTER,
                                tableName: 'table1',
                                viewId: 201,
                                hideMetrics: false,
                            } as RegisterItem,
                            {
                                id: 'dashboard-item',
                                alias: 'Dashboard Item',
                                name: 'Dashboard',
                                itemType: ItemTypes.DASHBOARD,
                                path: '/dashboard',
                            } as DashboardItem,
                        ],
                    },
                ],
            };

            renderComponent(contextWithMixedItems);

            // Should render section for register item
            await waitFor(() => {
                expect(screen.getByText(/Register Item - Metrics Display/i)).toBeInTheDocument();
            });

            // Should not render section for framework item
            expect(screen.queryByText(/Framework Item - Metrics Display/i)).not.toBeInTheDocument();
            // Should not render section for dashboard item
            expect(screen.queryByText(/Dashboard Item - Metrics Display/i)).not.toBeInTheDocument();
        });
    });

    describe('User Interactions', () => {
        it('opens metric selection dialog when add metric button is clicked', async () => {
            renderComponent();

            const addButton = await screen.findByTestId('add-d3b3f97a-7ed3-41c7-aea1-0c090d367e47');
            await act(() => addButton.click());

            expect(await screen.findByText(/select metrics/i)).toBeInTheDocument();
        });

        it('navigates back when close button is clicked', async () => {
            renderComponent();

            const closeButton = await screen.findByTestId(/button-cancel/i);
            await act(() => closeButton.click());

            expect(mockNavigate).toHaveBeenCalledWith(CyberRiskRoutesEnum.CR_SETTINGS);
        });

        it('handles save button click', async () => {
            renderComponent();

            const saveButton = await screen.findByTestId(/button-save/i);
            expect(saveButton).toBeInTheDocument();

            await act(() => saveButton.click());
        });
    });

    describe('Data Loading States', () => {
        it('handles loading state', async () => {
            mockUseWrsGetDisplayConfigurationUsingGetQuery.mockReturnValue({
                data: mockCyberriskMetricsConfig,
                isLoading: true,
                isSuccess: false,
                isError: false,
            });

            const view = renderComponent();
            await waitFor(() => expect(view.container).toBeInTheDocument());

            expect(view.container).toBeInTheDocument();
        });

        it('handles error state', async () => {
            mockUseWrsGetDisplayConfigurationUsingGetQuery.mockReturnValue({
                data: mockCyberriskMetricsConfig,
                isLoading: false,
                isSuccess: false,
                isError: true,
            });

            const view = renderComponent();
            await waitFor(() => expect(view.container).toBeInTheDocument());

            expect(view.container).toBeInTheDocument();
        });
    });

    describe('Multiple Register Items', () => {
        it('renders multiple settings sections for multiple register items', async () => {
            const contextWithMultipleItems = {
                ...mockContext,
                sections: [
                    {
                        id: 'section-1',
                        name: 'Section 1',
                        itemList: [
                            {
                                id: 'item-1',
                                alias: 'First Item',
                                name: 'Register 1',
                                itemType: ItemTypes.REGISTER,
                                tableName: 'table1',
                                viewId: 201,
                                hideMetrics: false,
                            } as RegisterItem,
                            {
                                id: 'item-2',
                                alias: 'Second Item',
                                name: 'Register 2',
                                itemType: ItemTypes.REGISTER,
                                tableName: 'table2',
                                viewId: 202,
                                hideMetrics: false,
                            } as RegisterItem,
                        ],
                    },
                ],
            };

            renderComponent(contextWithMultipleItems);

            // Should render sections for both items
            await waitFor(() => {
                expect(screen.getByText(/First Item - Metrics Display/i)).toBeInTheDocument();
            });
            await waitFor(() => {
                expect(screen.getByText(/Second Item - Metrics Display/i)).toBeInTheDocument();
            });

            // Should have add buttons for both items
            expect(screen.getByTestId('add-item-1')).toBeInTheDocument();
            expect(screen.getByTestId('add-item-2')).toBeInTheDocument();
        });
    });
});
