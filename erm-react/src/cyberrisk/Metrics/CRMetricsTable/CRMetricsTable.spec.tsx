import React from 'react';
import { screen, waitFor, waitForElementToBeRemoved } from '@testing-library/react';
import { render } from 'test/utils';
import { mockMetricEntries, mockMetricEntry, mockedStore } from 'vendorRiskManagement/api.mock';
import { MemoryRouter, Route, Routes, generatePath } from 'react-router';
import { mockNavigate } from 'test/config/setupAfterEnv';
import CRMetricsTable from './CRMetricsTable';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';

jest.mock('metrics/api', () => ({
    searchMetrics: jest.fn(() => Promise.resolve(mockMetricEntries)),
}));

describe('CRMetricsTable', () => {
    const renderComponent = () => {
        return render(
            <MemoryRouter initialEntries={[generatePath(CyberRiskRoutesEnum.CR_METRICS)]}>
                <Routes>
                    <Route
                        path={CyberRiskRoutesEnum.CR_METRICS}
                        element={<CRMetricsTable />}
                    />
                </Routes>
            </MemoryRouter>,
            mockedStore,
        );
    };
    it('Should display Loader while loading', async () => {
        renderComponent();

        expect(screen.getByText(/Loading.../i)).toBeInTheDocument();
        await waitForElementToBeRemoved(screen.queryByText('Loading...'));
    });

    it('Should display Toolbar', async () => {
        renderComponent();

        expect(await screen.findByTestId('button-Open')).toBeInTheDocument();
        expect(await screen.findByTestId('button-New Metric')).toBeInTheDocument();
    });

    it('Should open Entry detail after clicking on Open button', async () => {
        const { user } = renderComponent();

        const tableCell = await screen.findByRole('cell', { name: mockMetricEntry.name });
        const openButton = screen.getByTestId('button-Open');

        expect(openButton).toBeDisabled();

        await user.click(tableCell);

        await waitFor(() => expect(openButton).toBeEnabled());

        await user.click(openButton);

        expect(mockNavigate).toHaveBeenCalled();
    });

    it('Should open Entry detail when double clicking table row', async () => {
        const { user } = renderComponent();

        const tableCell = await screen.findByRole('cell', { name: mockMetricEntry.name });

        await user.dblClick(tableCell);

        expect(mockNavigate).toHaveBeenCalled();
    });

    it('Should open Add New Metric screen', async () => {
        const { user } = renderComponent();

        const openButton = screen.getByTestId('button-New Metric');

        await user.click(openButton);

        expect(mockNavigate).toHaveBeenCalled();
    });

    it('Should delete register Entry', async () => {
        const { user } = renderComponent();

        const tableCell = await screen.findByRole('cell', { name: mockMetricEntry.name });
        await user.click(tableCell);

        const deleteButton = screen.getByLabelText('delete');

        await user.click(deleteButton);

        // todo add expect to delete entry
    });
});
