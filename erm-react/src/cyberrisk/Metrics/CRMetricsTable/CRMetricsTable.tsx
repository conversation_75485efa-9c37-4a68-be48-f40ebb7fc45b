import MetricsTable from 'metrics/components/MetricsTable';
import { MetricsModule } from 'metrics/types';
import { strings } from 'common/utils/i18n';
import React, { FC, useCallback } from 'react';
import { generatePath, useNavigate } from 'react-router';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';

const ControlsMetrics: FC = () => {
    const navigate = useNavigate();

    const back = useCallback(() => {
        void navigate(generatePath(CyberRiskRoutesEnum.CR_SETTINGS));
    }, [navigate]);

    const createNew = useCallback(() => {
        void navigate(generatePath(CyberRiskRoutesEnum.CR_CREATE_METRIC));
    }, [navigate]);

    const openMetric = useCallback((id) => void navigate(generatePath(CyberRiskRoutesEnum.CR_EDIT_METRIC, { id })), [navigate]);

    return (
        <MetricsTable
            metricsModule={MetricsModule.CYBER_RISK}
            pageTitle={strings('cyberrisk:title.riskMetrics')}
            tableTitle={strings('cyberrisk:title.table.settings.riskMetrics')}
            onBackClicked={back}
            onCreateNewClicked={createNew}
            onOpenClicked={(id: number) => openMetric(id)}
        />
    );
};

export default ControlsMetrics;
