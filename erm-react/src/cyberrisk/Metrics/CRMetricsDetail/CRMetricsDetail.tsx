import { MetricsModule, Module } from 'metrics/types';
import MetricsDetail from 'common/components/MetricsDetail';
import React from 'react';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';

const CRMetricsDetail: React.FC = () => {
    return (
        <MetricsDetail
            module={Module.cyberRisk}
            feature={MetricsModule.CYBER_RISK}
            homePath={CyberRiskRoutesEnum.CR_METRICS}
        />
    );
};

export default CRMetricsDetail;
