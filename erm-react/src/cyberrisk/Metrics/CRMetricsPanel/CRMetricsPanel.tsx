import MetricsPanel, { METRIC_SEARCH } from 'common/components/MetricsPanel';
import React, { FC, useCallback, useContext } from 'react';
import { MetricRestResponse } from 'metrics/types';
import { RegisterItem } from 'api/generated/types';
import { useWrsFindByGroupItemUuidUsingGetQuery } from 'cyberrisk/rtkApi';
import { strings } from 'common/utils/i18n';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { generatePath, useNavigate } from 'react-router';
import { CyberRiskRoutesEnum } from 'cyberrisk/routesConfig';
import { ItemTypes } from 'cyberrisk/types';

export interface CRMetricsPanelProps {
    item: RegisterItem;
}

const CRMetricsPanel: FC<CRMetricsPanelProps> = ({ item }) => {
    const { defaultWorkspaceName, sections } = useContext(ControlsContext);
    const { data, isLoading, error } = useWrsFindByGroupItemUuidUsingGetQuery(
        { workspaceModule: 'CYBER_RISK', itemUuid: item.id!, eval: true, computeStyle: true },
        {
            skip: !item.id,
        },
    );
    const navigate = useNavigate();

    const handleMetricCardClick = useCallback(
        (metricData: MetricRestResponse) => {
            const registerItem = sections
                ?.flatMap((section) => section.itemList)
                .find((item) => item?.itemType === ItemTypes.REGISTER && item.tableName === metricData.metric.context) as RegisterItem | undefined;
            if (registerItem) {
                void navigate({
                    pathname: generatePath(CyberRiskRoutesEnum.CR_REGISTER, {
                        id: metricData.metric.context,
                    }),
                    search: `${METRIC_SEARCH}${metricData.metric.id}`,
                });
            }
        },
        [navigate, sections],
    );

    return (
        <MetricsPanel
            metrics={data as MetricRestResponse[]}
            isLoading={isLoading}
            error={error as string}
            title={strings('cyberrisk:label.metricsTitle', { name: defaultWorkspaceName })}
            getEvaluation={(metric) => metric}
            onMetricCardClick={handleMetricCardClick}
        />
    );
};

export default CRMetricsPanel;
