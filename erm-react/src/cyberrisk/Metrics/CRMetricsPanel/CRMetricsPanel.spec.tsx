import React from 'react';
import { render, screen } from 'test/utils';
import { waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CRMetricsPanel from './CRMetricsPanel';
import { MetricRestResponse, MetricsWidth, DisplayFunc } from 'metrics/types';
import { RegisterItem } from 'api/generated/types';
import * as cyberRiskApi from 'cyberrisk/rtkApi';
import { ControlsContext } from 'cyberrisk/ContextProvider';
import { mockContext } from 'cyberrisk/api.mock';
import { mockNavigate } from 'test/config/setupAfterEnv';

jest.mock('cyberrisk/rtkApi', () => ({
    useWrsFindByGroupItemUuidUsingGetQuery: jest.fn(),
}));

describe('CRMetricsPanel', () => {
    const mockMetrics: MetricRestResponse[] = [
        {
            metric: {
                id: 1,
                name: 'Test Cyber Risk Metric 1',
                context: 'cyberRiskContext',
                func: DisplayFunc.COUNT,
                styleModel: {
                    size: MetricsWidth.SIZE1,
                    color: '#FF0000',
                },
                configModel: { internal: [] },
                source: 'cyberRiskSource',
            },
            eval: {
                explain: [],
                idOrName: '1',
                simpleEval: '15',
                multiResult: {},
                symbol: '',
                symbolBefore: false,
            },
        },
        {
            metric: {
                id: 2,
                name: 'Test Cyber Risk Metric 2',
                context: 'cyberRiskContext',
                func: DisplayFunc.PERCENT,
                styleModel: {
                    size: MetricsWidth.SIZE2,
                    color: '#00FF00',
                },
                configModel: { internal: [] },
                source: 'cyberRiskSource',
            },
            eval: {
                explain: [],
                idOrName: '2',
                simpleEval: '85',
                multiResult: {},
                symbol: '%',
                symbolBefore: false,
            },
        },
    ];

    const mockRegisterItem: RegisterItem = {
        id: 'test-item-uuid-123',
        name: 'Test Cyber Risk Item',
        description: 'Test description',
        itemType: 'RegisterItem',
        order: 0,
    };

    const defaultProps = {
        item: mockRegisterItem,
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockNavigate.mockClear();

        // Mock clientWidth to prevent overflow calculations
        Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
            configurable: true,
            value: 1000, // Wide enough to show all cards without overflow
        });
    });

    const renderComponent = (props = {}) => {
        return render(
            <ControlsContext.Provider value={mockContext}>
                <CRMetricsPanel
                    {...defaultProps}
                    {...props}
                />
            </ControlsContext.Provider>,
        );
    };

    describe('Loading States', () => {
        it('should not render anything when data is loading and no metrics', () => {
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: undefined,
                isLoading: true,
                error: undefined,
            });

            const { container } = renderComponent();

            // MetricsPanel returns null when there are no metrics, even when loading
            expect(container).toBeEmptyDOMElement();
        });

        it('should show loading spinner when data is loading and metrics exist', async () => {
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: mockMetrics,
                isLoading: true,
                error: undefined,
            });

            renderComponent();

            await waitFor(() => {
                expect(screen.getByRole('progressbar')).toBeInTheDocument();
            });
            // Should show title even when loading if metrics exist
            expect(screen.getByText('Metrics for Workspace 1')).toBeInTheDocument();
        });
    });

    describe('Error Handling', () => {
        it('should not render anything when API call fails and no metrics', () => {
            const errorMessage = 'Failed to load cyber risk metrics';
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: undefined,
                isLoading: false,
                error: errorMessage,
            });

            const { container } = renderComponent();

            // MetricsPanel returns null when there are no metrics, even with error
            expect(container).toBeEmptyDOMElement();
        });
    });

    describe('Successful Rendering', () => {
        beforeEach(() => {
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: mockMetrics,
                isLoading: false,
                error: undefined,
            });
        });

        it('should render the panel title from item name', async () => {
            renderComponent();

            await waitFor(() => {
                expect(screen.getByText('Metrics for Workspace 1')).toBeInTheDocument();
            });
        });

        it('should render metric cards when data is loaded', async () => {
            renderComponent();

            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 1')).toBeInTheDocument();
            });
            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 2')).toBeInTheDocument();
            });
            await waitFor(() => {
                expect(screen.getByText('15')).toBeInTheDocument();
            });
            await waitFor(() => {
                expect(screen.getByText('85 %')).toBeInTheDocument();
            });
        });

        it('should not render anything when there are no metrics', async () => {
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: [],
                isLoading: false,
                error: undefined,
            });

            const { container } = renderComponent();

            await waitFor(() => {
                expect(container).toBeEmptyDOMElement();
            });
        });

        it('should handle empty item name gracefully', async () => {
            const itemWithoutName = { ...mockRegisterItem, name: undefined };
            renderComponent({ item: itemWithoutName });

            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 1')).toBeInTheDocument();
            });
            // Should render with empty title
            expect(screen.queryByText('Test Cyber Risk Item')).not.toBeInTheDocument();
        });
    });

    describe('API Calls', () => {
        it('should call useWrsFindByGroupItemUuidUsingGetQuery with correct parameters', () => {
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: mockMetrics,
                isLoading: false,
                error: undefined,
            });

            renderComponent();

            expect(cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery).toHaveBeenCalledWith(
                {
                    workspaceModule: 'CYBER_RISK',
                    itemUuid: 'test-item-uuid-123',
                    eval: true,
                    computeStyle: true,
                },
                {
                    skip: false,
                },
            );
        });

        it('should skip API call when item id is missing', () => {
            const itemWithoutId = { ...mockRegisterItem, id: undefined };
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: undefined,
                isLoading: false,
                error: undefined,
            });

            renderComponent({ item: itemWithoutId });

            expect(cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery).toHaveBeenCalledWith(
                {
                    workspaceModule: 'CYBER_RISK',
                    itemUuid: undefined,
                    eval: true,
                    computeStyle: true,
                },
                {
                    skip: true,
                },
            );
        });

        it('should skip API call when item id is empty string', () => {
            const itemWithEmptyId = { ...mockRegisterItem, id: '' };
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: undefined,
                isLoading: false,
                error: undefined,
            });

            renderComponent({ item: itemWithEmptyId });

            expect(cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery).toHaveBeenCalledWith(
                {
                    workspaceModule: 'CYBER_RISK',
                    itemUuid: '',
                    eval: true,
                    computeStyle: true,
                },
                {
                    skip: true,
                },
            );
        });
    });

    describe('Props Handling', () => {
        it('should handle different register item properties', async () => {
            const customItem: RegisterItem = {
                id: 'custom-uuid-456',
                name: 'Custom Cyber Risk Item',
                description: 'Custom description',
                itemType: 'RegisterItem',
                order: 0,
            };

            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: mockMetrics,
                isLoading: false,
                error: undefined,
            });

            renderComponent({ item: customItem });

            expect(cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery).toHaveBeenCalledWith(
                {
                    workspaceModule: 'CYBER_RISK',
                    itemUuid: 'custom-uuid-456',
                    eval: true,
                    computeStyle: true,
                },
                {
                    skip: false,
                },
            );

            await waitFor(() => {
                expect(screen.getByText('Metrics for Workspace 1')).toBeInTheDocument();
            });
        });

        it('should handle null item id', () => {
            const itemWithNullId = { ...mockRegisterItem, id: null };
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: undefined,
                isLoading: false,
                error: undefined,
            });

            renderComponent({ item: itemWithNullId });

            expect(cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery).toHaveBeenCalledWith(
                {
                    workspaceModule: 'CYBER_RISK',
                    itemUuid: null,
                    eval: true,
                    computeStyle: true,
                },
                {
                    skip: true,
                },
            );
        });
    });

    describe('Data Transformation', () => {
        it('should pass metrics data correctly to MetricsPanel', async () => {
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: mockMetrics,
                isLoading: false,
                error: undefined,
            });

            renderComponent();

            // Verify that the metrics are rendered correctly
            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 1')).toBeInTheDocument();
            });

            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 2')).toBeInTheDocument();
            });

            // Verify that evaluations are displayed
            expect(screen.getByText('15')).toBeInTheDocument();
            expect(screen.getByText('85 %')).toBeInTheDocument();
        });

        it('should handle metrics without evaluations', async () => {
            const metricsWithoutEval = mockMetrics.map((metric) => ({
                ...metric,
                eval: undefined,
            }));

            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: metricsWithoutEval,
                isLoading: false,
                error: undefined,
            });

            renderComponent();

            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 1')).toBeInTheDocument();
            });
            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 2')).toBeInTheDocument();
            });
        });
    });

    describe('Overflow and Navigation', () => {
        const mockClientWidth = 300; // Small width to trigger overflow

        beforeEach(() => {
            Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
                configurable: true,
                value: mockClientWidth,
            });

            // Create more metrics to ensure overflow
            const manyMetrics = Array.from({ length: 5 }, (_, i) => ({
                metric: {
                    id: i + 1,
                    name: `Cyber Risk Metric ${i + 1}`,
                    context: 'cyberRiskContext',
                    func: DisplayFunc.COUNT,
                    styleModel: {
                        size: MetricsWidth.SIZE2, // Use wide cards to trigger overflow
                        color: '#000000',
                    },
                    configModel: { internal: [] },
                    source: 'cyberRiskSource',
                },
                eval: {
                    explain: [],
                    idOrName: (i + 1).toString(),
                    simpleEval: (i + 1).toString(),
                    multiResult: {},
                    symbol: '',
                    symbolBefore: false,
                },
            }));

            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: manyMetrics,
                isLoading: false,
                error: undefined,
            });
        });

        it('should show navigation buttons when content overflows', async () => {
            renderComponent();

            await waitFor(() => {
                expect(screen.getByText('Cyber Risk Metric 1')).toBeInTheDocument();
            });

            // Should show navigation buttons when there are more cards
            const navigationButtons = screen.getAllByRole('button');
            expect(navigationButtons.length).toBeGreaterThan(0);
        });

        it('should navigate through metrics when navigation buttons are clicked', async () => {
            const user = userEvent.setup();
            renderComponent();

            await waitFor(() => {
                expect(screen.getByText('Cyber Risk Metric 1')).toBeInTheDocument();
            });

            // Find navigation buttons and click the right navigation button
            const navigationButtons = screen.getAllByRole('button');
            expect(navigationButtons.length).toBeGreaterThan(0);

            const rightButton = navigationButtons[navigationButtons.length - 1];
            await user.click(rightButton);

            // After clicking, verify navigation buttons are still present
            expect(rightButton).toBeInTheDocument();

            const navigationButtons1 = screen.getAllByRole('button');
            // Left button shows up
            expect(navigationButtons1.length).toEqual(2);
        });
    });

    describe('Metric Card Navigation', () => {
        beforeEach(() => {
            (cyberRiskApi.useWrsFindByGroupItemUuidUsingGetQuery as jest.Mock).mockReturnValue({
                data: mockMetrics,
                isLoading: false,
                error: undefined,
            });
        });

        it('should navigate to register page when metric card is clicked', async () => {
            const user = userEvent.setup();

            // Create a mock context with sections that include a register item matching the metric context
            const mockContextWithRegister = {
                ...mockContext,
                sections: [
                    {
                        id: 'section1',
                        name: 'Test Section',
                        itemList: [
                            {
                                id: 'register-item-1',
                                name: 'Test Register',
                                itemType: 'RegisterItem' as const,
                                tableName: 'cyberRiskContext', // This matches the metric context
                                registerId: 123,
                                viewId: 456,
                                order: 0,
                            },
                        ],
                    },
                ],
            };

            render(
                <ControlsContext.Provider value={mockContextWithRegister}>
                    <CRMetricsPanel {...defaultProps} />
                </ControlsContext.Provider>,
            );

            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 1')).toBeInTheDocument();
            });

            // Find and click the first metric card by its text content
            const metricCardText = screen.getByText('Test Cyber Risk Metric 1');
            expect(metricCardText).toBeInTheDocument();

            // Click directly on the metric card text, which will bubble up to the clickable container
            await user.click(metricCardText);

            // Verify navigation was called with correct parameters
            expect(mockNavigate).toHaveBeenCalledWith({
                pathname: '/cyberrisk/register/cyberRiskContext',
                search: '?metricId=1',
            });
        });

        it('should not navigate when no matching register item is found', async () => {
            const user = userEvent.setup();

            // Use default context without matching register items
            renderComponent();

            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 1')).toBeInTheDocument();
            });

            // Find and click the first metric card by its text content
            const metricCardText = screen.getByText('Test Cyber Risk Metric 1');
            expect(metricCardText).toBeInTheDocument();

            // Click directly on the metric card text, which will bubble up to the clickable container
            await user.click(metricCardText);

            // Verify navigation was not called
            expect(mockNavigate).not.toHaveBeenCalled();
        });

        it('should navigate with correct metric ID for different metrics', async () => {
            const user = userEvent.setup();

            // Create a mock context with a register item matching the second metric context
            const mockContextWithRegister = {
                ...mockContext,
                sections: [
                    {
                        id: 'section1',
                        name: 'Test Section',
                        itemList: [
                            {
                                id: 'register-item-1',
                                name: 'Test Register',
                                itemType: 'RegisterItem' as const,
                                tableName: 'cyberRiskContext', // This matches both metrics' context
                                registerId: 789,
                                viewId: 101,
                                order: 0,
                            },
                        ],
                    },
                ],
            };

            render(
                <ControlsContext.Provider value={mockContextWithRegister}>
                    <CRMetricsPanel {...defaultProps} />
                </ControlsContext.Provider>,
            );

            await waitFor(() => {
                expect(screen.getByText('Test Cyber Risk Metric 2')).toBeInTheDocument();
            });

            // Find and click the second metric card by its text content
            const metricCardText = screen.getByText('Test Cyber Risk Metric 2');
            expect(metricCardText).toBeInTheDocument();

            // Click directly on the metric card text, which will bubble up to the clickable container
            await user.click(metricCardText);

            // Verify navigation was called with correct metric ID (2)
            expect(mockNavigate).toHaveBeenCalledWith({
                pathname: '/cyberrisk/register/cyberRiskContext',
                search: '?metricId=2',
            });
        });
    });
});
