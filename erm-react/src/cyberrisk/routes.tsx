import { Outlet, RouteObject } from 'react-router';
import { CyberRiskLayout } from './Layout';
import React from 'react';
import { MenuContentsSettings, WorkspaceSettings } from './Settings';
import ControlsRegisters from './Registers';
import ControlsDashboards from './Dashboards';
import ControlsFrameworks from './Frameworks';
import store from '../store';
import { getScaleSets } from '../app/selectors';
import { usersApi } from 'user/rtkApi';
import { systemApi } from 'app/rtkApi';
import { viewsApi } from 'view/rtkApi';
import { cyberRiskApi } from './rtkApi';
import { registerApi } from '../register/rtkApi';
import FullscreenOutlet from '../common/layouts/FullscreenOutlet';
import { ControlsContextProvider } from './ContextProvider';
import CRMetricsDisplay from './Metrics/CRMetricsDisplay/CRMetricsDisplay';
import { CyberRiskRoutesEnum } from './routesConfig';
import ProtectedContent from 'common/components/ProtectedContent/ProtectedContent';
import { ApplicationName, PermissionCodes } from 'common/types';
import CRMetricsDetail from './Metrics/CRMetricsDetail';
import CRMetricsTable from './Metrics/CRMetricsTable';
import { RegisterItem } from 'api/generated/types';
import { isRegisterItem } from './Settings/MenuContentsSettings/utils';
import { CYBER_RISK_MODULE } from './constants';

export const CyberRiskRoutes: RouteObject[] = [
    {
        path: CyberRiskRoutesEnum.CR,
        element: (
            <ControlsContextProvider>
                <ProtectedContent
                    ignoreRelatedObjectId
                    redirectUrl={'/'}
                    permissions={[
                        {
                            code: PermissionCodes.WORKSPACE_VIEW,
                            applicationName: ApplicationName.WORKSPACE,
                        },
                        {
                            code: PermissionCodes.WORKSPACE_MANAGE,
                            applicationName: ApplicationName.WORKSPACE,
                        },
                    ]}
                >
                    <CyberRiskLayout />
                </ProtectedContent>
            </ControlsContextProvider>
        ),
        loader: () => {
            void store.dispatch(systemApi.endpoints.srsGetScaleSetsUsingGet.initiate({ pagingDisabled: true }, { subscribe: false }));
            void store.dispatch(usersApi.endpoints.pursGetUserPermissionsUsingGet.initiate({}, { subscribe: false }));
            void store.dispatch(viewsApi.endpoints.vrsGetExpressionContextUsingGet.initiate(undefined, { subscribe: false }));
            void store
                .dispatch(cyberRiskApi.endpoints?.wrsGetUserFilteredWorkspaceUsingGet.initiate({ workspaceModule: CYBER_RISK_MODULE }))
                .unwrap()
                .then((res) => {
                    const items = res.config?.groups?.flatMap((section) => section.itemList);
                    items
                        ?.filter((item) => isRegisterItem(item) && (item as RegisterItem)?.registerId !== undefined)
                        .map((item) => {
                            if (!item) {
                                return;
                            }
                            void store.dispatch(
                                registerApi.endpoints.tmrsGetRegisterConfigUsingGet1.initiate({ id: (item as RegisterItem).registerId! }, { subscribe: false }),
                            );
                        });
                });
            return null;
        },
        shouldRevalidate: () => {
            const scaleSets = getScaleSets(store.getState());
            return scaleSets.length === 0;
        },
        children: [
            {
                path: 'register/:id/:viewId?',
                element: <ControlsRegisters />,
            },
            {
                path: 'dashboard/:id',
                element: <ControlsDashboards />,
            },
            {
                path: 'framework/:id',
                element: <ControlsFrameworks />,
            },
            {
                path: 'settings',
                element: (
                    <ProtectedContent
                        ignoreRelatedObjectId
                        redirectUrl={CyberRiskRoutesEnum.CR}
                        permissions={[{ code: PermissionCodes.WORKSPACE_MANAGE, applicationName: ApplicationName.WORKSPACE }]}
                    >
                        <Outlet />
                    </ProtectedContent>
                ),
                children: [
                    {
                        index: true,
                        element: <WorkspaceSettings />,
                    },
                    {
                        path: 'menuContents',
                        element: (
                            <FullscreenOutlet zIndex={2}>
                                <MenuContentsSettings />
                            </FullscreenOutlet>
                        ),
                    },
                    {
                        path: 'metrics',
                        children: [
                            {
                                index: true,
                                element: (
                                    <FullscreenOutlet zIndex={2}>
                                        <CRMetricsTable />
                                    </FullscreenOutlet>
                                ),
                            },
                            {
                                path: 'create',
                                element: (
                                    <FullscreenOutlet zIndex={2}>
                                        <CRMetricsDetail />
                                    </FullscreenOutlet>
                                ),
                            },
                            {
                                path: ':id',
                                element: (
                                    <FullscreenOutlet zIndex={2}>
                                        <CRMetricsDetail />
                                    </FullscreenOutlet>
                                ),
                            },
                            {
                                path: 'display',
                                element: (
                                    <FullscreenOutlet zIndex={2}>
                                        <CRMetricsDisplay />
                                    </FullscreenOutlet>
                                ),
                            },
                        ],
                    },
                ],
            },
        ],
    },
];
