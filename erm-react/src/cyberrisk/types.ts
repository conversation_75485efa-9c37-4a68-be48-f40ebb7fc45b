import { WorkspaceGroup, WorkspaceGroupItem } from '../api/generated/types';

export enum SettingsDialogs {
    GROUP_NAMING,
    ITEM_TYPE_SELECTOR,
    ITEM_DETAILS,
    REGISTER_SELECTOR,
    FRAMEWORK_SELECTOR,
    DASH<PERSON>ARD_SELECTOR,
}

export type SettingsDialogsType = SettingsDialogs | null;
export type ActiveWorkspaceGroupType = WorkspaceGroup | null;

// Bound to ConfigMenuItem
export enum ItemTypes {
    REGISTER = 'RegisterItem',
    FRAMEWORK = 'FrameworkItem',
    DASHBOARD = 'DashboardItem',
}

export enum MenuItemDialogType {
    'NEW',
    'EDIT',
}

export interface CyberRiskState {
    sections: WorkspaceGroup[];
    filteredSections: WorkspaceGroup[];
    activeSettingsDialog: SettingsDialogsType;
    activeSection: ActiveWorkspaceGroupType;
    activeMenuItem: WorkspaceGroupItem | null;
    itemType: ItemTypes | null;
    menuItemDialogType: MenuItemDialogType | null;
}

export const enum CyberRiskActionType {
    SET_SECTIONS = 'SET_SECTIONS',
    REMOVE_SECTION = 'REMOVE_SECTION',
    REMOVE_ITEM = 'REMOVE_ITEM',
    REORDER_ITEMS = 'REORDER_ITEMS',
    MOVE_ITEM = 'MOVE_ITEM',
    MOVE_SECTION = 'MOVE_SECTION',
    ADD_NEW_SECTION = 'ADD_NEW_SECTION',
    SET_ACTIVE_DIALOG = 'SET_ACTIVE_DIALOG',
    SET_ACTIVE_SECTION = 'SET_ACTIVE_SECTION',
    SET_ACTIVE_MENU_ITEM = 'SET_ACTIVE_MENU_ITEM',
    RENAME_SECTION = 'RENAME_SECTION',
    SET_ITEM_TYPE = 'SET_ITEM_TYPE',
    ADD_ITEM_TO_SECTION = 'ADD_ITEM_TO_SECTION',
    SAVE_MENU_ITEM = 'SAVE_MENU_ITEM',
    SET_FILTERED_SECTIONS = 'SET_FILTERED_SECTIONS',
}

export type CyberRiskAction =
    | {
          type: CyberRiskActionType.SET_SECTIONS;
          payload: WorkspaceGroup[];
      }
    | {
          type: CyberRiskActionType.SET_FILTERED_SECTIONS;
          payload: WorkspaceGroup[];
      }
    | {
          type: CyberRiskActionType.REMOVE_SECTION;
          payload: WorkspaceGroup;
      }
    | {
          type: CyberRiskActionType.REMOVE_ITEM;
          payload: {
              section: WorkspaceGroup;
              item: WorkspaceGroupItem;
          };
      }
    | {
          type: CyberRiskActionType.REORDER_ITEMS;
          payload: {
              section: WorkspaceGroup;
              items: WorkspaceGroupItem[];
          };
      }
    | {
          type: CyberRiskActionType.MOVE_SECTION;
          payload: {
              section: WorkspaceGroup;
              direction: 'UP' | 'DOWN';
          };
      }
    | {
          type: CyberRiskActionType.MOVE_ITEM;
          payload: {
              section: WorkspaceGroup;
              item: WorkspaceGroupItem;
              direction: 'UP' | 'DOWN';
          };
      }
    | {
          type: CyberRiskActionType.ADD_NEW_SECTION;
          payload: WorkspaceGroup;
      }
    | {
          type: CyberRiskActionType.SET_ACTIVE_DIALOG;
          payload: {
              settingsDialogType: SettingsDialogsType;
          };
      }
    | {
          type: CyberRiskActionType.SET_ACTIVE_SECTION;
          payload: {
              section: ActiveWorkspaceGroupType;
          };
      }
    | {
          type: CyberRiskActionType.SET_ACTIVE_MENU_ITEM;
          payload: {
              item: WorkspaceGroupItem | null;
              dialogType: MenuItemDialogType;
          };
      }
    | {
          type: CyberRiskActionType.RENAME_SECTION;
          payload: {
              name: string;
          };
      }
    | {
          type: CyberRiskActionType.SET_ITEM_TYPE;
          payload: {
              itemType: ItemTypes | null;
          };
      }
    | {
          type: CyberRiskActionType.ADD_ITEM_TO_SECTION;
          payload: {
              item: WorkspaceGroupItem;
          };
      }
    | {
          type: CyberRiskActionType.SAVE_MENU_ITEM;
          payload: {
              item: WorkspaceGroupItem;
          };
      };
