import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import { getProtechtTheme } from 'app/theme';
import CRRegisters from './CRRegisters';
import { ControlsContext } from '../ContextProvider';
import { ItemTypes } from '../types';
import { RegisterItem, WorkspaceGroup } from 'api/generated/types';
import SnackbarProvider from 'context/SnackbarProvider/SnackbarProvider';

const mockUseWrsGetRegistersStatsUsingGetQuery = jest.fn();
const mockUseGetRegisterEntriesPostQuery = jest.fn();
const mockUseGetRegisterEntriesSearchPostQuery = jest.fn();
const mockDelete = jest.fn();
const mockUseTmrsGetRegisterConfigUsingGet1Query = jest.fn();
const mockUseVrsGetViewsUsingGetQuery = jest.fn();
const mockUseVrsDeleteViewUsingDeleteMutation = jest.fn();
const mockUseConfirmationAlert = jest.fn();
const mockUseDispatch = jest.fn();
const mockUseSelector = jest.fn();

mockUseVrsGetViewsUsingGetQuery.mockReturnValue({
    data: { views: [] },
    isSuccess: true,
    refetch: jest.fn(),
    error: null,
    isFetching: false,
});

mockUseGetRegisterEntriesSearchPostQuery.mockReturnValue({
    data: undefined,
    isLoading: false,
    refetch: jest.fn().mockResolvedValue({}),
});

mockUseGetRegisterEntriesPostQuery.mockReturnValue({
    data: undefined,
    isLoading: false,
    refetch: jest.fn().mockResolvedValue({}),
});

mockUseTmrsGetRegisterConfigUsingGet1Query.mockReturnValue({
    data: undefined,
});

mockUseWrsGetRegistersStatsUsingGetQuery.mockReturnValue({
    data: undefined,
});

jest.mock('../rtkApi', () => ({
    useWrsGetRegistersStatsUsingGetQuery: () => mockUseWrsGetRegistersStatsUsingGetQuery,
}));

jest.mock('register/rtkApi', () => ({
    useGetRegisterEntriesPostQuery: () => mockUseGetRegisterEntriesPostQuery(),
    useGetRegisterEntriesSearchPostQuery: () => mockUseGetRegisterEntriesSearchPostQuery(),
    useRdrsvDeleteRegisterEntryUsingDeleteMutation: () => [mockDelete, { isLoading: false }],
    useTmrsGetRegisterConfigUsingGet1Query: () => mockUseTmrsGetRegisterConfigUsingGet1Query(),
}));

jest.mock('view/rtkApi', () => ({
    useVrsGetViewsUsingGetQuery: () => mockUseVrsGetViewsUsingGetQuery(),
    useVrsDeleteViewUsingDeleteMutation: () => [mockUseVrsDeleteViewUsingDeleteMutation, { isLoading: false }],
}));

jest.mock('context/ConfirmationAlertProvider/useConfirmationAlert', () => ({
    useConfirmationAlert: () => mockUseConfirmationAlert,
}));

jest.mock('store', () => ({
    useDispatch: () => mockUseDispatch,
    useSelector: (selector) => mockUseSelector(selector),
}));

jest.mock('react-router', () => ({
    useOutletContext: jest.fn(),
    useSearchParams: jest.fn(),
    useParams: jest.fn(),
    MemoryRouter: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('view/hooks/useView', () => ({
    useView: jest.fn(),
}));

// Import the mocked functions
import { useOutletContext, useSearchParams, useParams } from 'react-router';
import { useView } from 'view/hooks/useView';

const mockUseOutletContext = useOutletContext as jest.MockedFunction<typeof useOutletContext>;
const mockUseSearchParams = useSearchParams as jest.MockedFunction<typeof useSearchParams>;
const mockUseParams = useParams as jest.MockedFunction<typeof useParams>;
const mockUseView = useView as jest.MockedFunction<typeof useView>;

jest.mock('cyberrisk/Metrics/CRMetricsPanel', () => {
    return function CRMetricsPanel() {
        return <div data-testid="cr-metrics-panel">CRMetricsPanel</div>;
    };
});

// Mock window.GwtBridge
const mockShowOverScreenWidget = jest.fn();
Object.defineProperty(window, 'GwtBridge', {
    value: {
        RegisterConfiguration: {
            showOverScreenWidget: mockShowOverScreenWidget,
        },
    },
    writable: true,
    configurable: true,
});

const mockRegisterData = {
    records: [
        {
            record: {
                id: 1,
                name: 'Test Entry 1',
                status: 'Active',
            },
        },
        {
            record: {
                id: 2,
                name: 'Test Entry 2',
                status: 'Inactive',
            },
        },
    ],
    totalCount: 2,
};

const mockRegisterConfig = {
    id: 123,
    name: 'Test Register',
    tableName: 'test_register',
    applicationId: 123,
};

const mockColumns = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'status', headerName: 'Status', width: 150 },
];

const mockRegisterItem: RegisterItem = {
    id: '1',
    itemType: ItemTypes.REGISTER,
    registerId: 123,
    alias: 'Test Register',
    name: 'test-register',
    appId: 123,
    tableName: 'test_register',
    order: 1,
};

const mockSections: WorkspaceGroup[] = [
    {
        id: '1',
        name: 'Test Section',
        itemList: [mockRegisterItem],
    },
];

const createMockContextValue = (overrides = {}) => ({
    sections: mockSections,
    filteredSections: mockSections,
    fixedLevel: 0,
    isBusy: false,
    settingsPristine: true,
    getLibrary: jest.fn(() => mockRegisterItem),
    defaultWorkspaceName: 'Test Workspace',
    removeSectionItem: jest.fn(),
    removeItem: jest.fn(),
    doUpdateControls: jest.fn(),
    resetDefaults: jest.fn(),
    dispatch: jest.fn(),
    activeSettingsDialog: null,
    activeSection: null,
    activeMenuItem: null,
    menuItemDialogType: null,
    itemType: null,
    ...overrides,
});

// Mock store
const createMockStore = () => {
    return configureStore({
        reducer: {
            // Add minimal reducer for testing
            test: (state = {}) => state,
        },
        preloadedState: {},
    });
};

const renderWithProviders = (contextValue = createMockContextValue(), routerProps = {}) => {
    const mockStore = createMockStore();
    const theme = getProtechtTheme();

    return render(
        <Provider store={mockStore}>
            <ThemeProvider theme={theme}>
                <SnackbarProvider>
                    <MemoryRouter
                        initialEntries={['/cyberrisk/registers/123']}
                        {...routerProps}
                    >
                        <ControlsContext.Provider value={contextValue}>
                            <CRRegisters />
                        </ControlsContext.Provider>
                    </MemoryRouter>
                </SnackbarProvider>
            </ThemeProvider>
        </Provider>,
    );
};

describe('CRRegisters', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockShowOverScreenWidget.mockClear();

        // Setup store mocks
        mockUseDispatch.mockReturnValue(jest.fn());
        mockUseSelector.mockImplementation((selector) => {
            if (selector.name === 'getSelectedView') return null;
            if (selector.name === 'getRequestParams') return { page: 0, pageSize: 25 };
            if (selector.name === 'getSelectedEntry') return null;
            return null;
        });

        // Setup router mocks
        mockUseOutletContext.mockReturnValue({ current: { refresh: jest.fn() } });
        mockUseSearchParams.mockReturnValue([new URLSearchParams(), jest.fn()]);
        mockUseParams.mockReturnValue({ id: '123' });

        // Setup register API mocks
        mockUseGetRegisterEntriesSearchPostQuery.mockReturnValue({
            data: mockRegisterData,
            isLoading: false,
            refetch: jest.fn().mockResolvedValue({}),
        });
        mockUseGetRegisterEntriesPostQuery.mockReturnValue({
            data: undefined,
            isLoading: false,
            refetch: jest.fn().mockResolvedValue({}),
        });
        mockUseTmrsGetRegisterConfigUsingGet1Query.mockReturnValue({
            data: mockRegisterConfig,
        });

        // Setup view API mocks
        mockUseVrsGetViewsUsingGetQuery.mockReturnValue({
            data: { views: [] },
            isSuccess: true,
            refetch: jest.fn().mockResolvedValue({}),
            error: null,
            isFetching: false,
        });
        mockUseVrsDeleteViewUsingDeleteMutation.mockReturnValue([jest.fn().mockResolvedValue({}), { isLoading: false }]);

        // Setup view hook mock
        mockUseView.mockReturnValue({
            columns: mockColumns,
            columnVisibilityModel: {},
            viewParams: { page: 0 },
            defaultViewSearchProperty: undefined,
            viewExpressions: undefined,
            isViewLoaded: true,
        });

        // Setup other mocks
        mockUseConfirmationAlert.mockReturnValue({
            showConfirmationAlert: jest.fn((params) => {
                if (params.onConfirm) params.onConfirm();
            }),
        });
    });

    afterEach(() => {
        delete (window as any).GwtBridge;
        mockShowOverScreenWidget.mockClear();

        // Recreate the GwtBridge mock for the next test
        Object.defineProperty(window, 'GwtBridge', {
            value: {
                RegisterConfiguration: {
                    showOverScreenWidget: mockShowOverScreenWidget,
                },
            },
            writable: true,
            configurable: true,
        });
    });

    describe('Basic Rendering', () => {
        it('should render the component without crashing', () => {
            renderWithProviders();

            expect(screen.getByRole('grid')).toBeInTheDocument();
        });

        it('should show loading state when data is loading', () => {
            mockUseGetRegisterEntriesSearchPostQuery.mockReturnValue({
                data: undefined,
                isLoading: true,
                refetch: jest.fn(),
            });

            renderWithProviders();

            expect(screen.getByText('Loading...')).toBeInTheDocument();
        });
    });

    describe('Toolbar Functionality', () => {
        it('should render toolbar with correct buttons', async () => {
            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByTestId('button-delete')).toBeInTheDocument();
                expect(screen.getByText('Open')).toBeInTheDocument();
                expect(screen.getByText('New')).toBeInTheDocument();
            });
        });

        it('should disable delete and open buttons when no entry is selected', async () => {
            renderWithProviders();

            await waitFor(() => {
                const deleteButton = screen.getByTestId('button-delete');
                const openButton = screen.getByRole('button', { name: 'Open' });

                expect(deleteButton).toBeDisabled();
                expect(openButton).toBeDisabled();
            });
        });

        it('should enable delete and open buttons when entry is selected', async () => {
            mockUseSelector.mockImplementation((selector) => {
                if (selector.name === 'getSelectedView') return null;
                if (selector.name === 'getRequestParams') return { page: 0, pageSize: 25 };
                if (selector.name === 'getSelectedEntry') return { id: '123', name: 'Test Entry' };
                return null;
            });

            // Re-render with selected entry
            renderWithProviders();

            await waitFor(() => {
                const deleteButton = screen.getByTestId('button-delete');
                expect(deleteButton).toBeEnabled();
                const openButton = screen.getByText('Open');
                expect(openButton).toBeEnabled();
            });
        });

        it('should call GwtBridge when new button is clicked', async () => {
            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByTestId('button-New')).toBeInTheDocument();
            });

            const newButton = screen.getByTestId('button-New');
            fireEvent.click(newButton);

            await waitFor(() => {
                expect(mockShowOverScreenWidget).toHaveBeenCalledWith(null, '123', 'test_register', 'true', null);
            });
        });
    });

    describe('View Selector', () => {
        it('should render view selector', async () => {
            renderWithProviders();

            await waitFor(() => {
                // ViewSelector should be rendered as part of the toolbar
                expect(screen.getByTestId('button-view-selector')).toBeInTheDocument();
            });
        });

        it('should show views after clicking the selector', async () => {
            mockUseVrsGetViewsUsingGetQuery.mockReturnValue({
                data: {
                    views: [
                        { id: 1, name: 'Test View 1', scope: null, context: 'test_register' },
                        { id: 2, name: 'Test View 2', scope: null, context: 'test_register' },
                    ],
                },
                isSuccess: true,
                refetch: jest.fn().mockResolvedValue({}),
                error: null,
                isFetching: false,
            });
            const mockDispatch = jest.fn();
            mockUseDispatch.mockReturnValue(mockDispatch);

            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByTestId('button-view-selector')).toBeInTheDocument();
            });

            const viewSelector = screen.getByTestId('button-view-selector');
            fireEvent.click(viewSelector);

            await waitFor(() => {
                expect(screen.getByText('Test View 1')).toBeInTheDocument();
                expect(screen.getByText('Test View 2')).toBeInTheDocument();
            });
        });
    });

    describe('Metrics Panel', () => {
        it('should render metrics panel when register item is available', async () => {
            const contextValue = createMockContextValue();
            renderWithProviders(contextValue);

            await waitFor(() => {
                expect(screen.getByTestId('cr-metrics-panel')).toBeInTheDocument();
            });
        });

        it('should not render metrics panel when register item is not available', async () => {
            const contextValue = createMockContextValue({
                sections: [],
            });
            renderWithProviders(contextValue);

            await waitFor(() => {
                expect(screen.queryByTestId('cr-metrics-panel')).not.toBeInTheDocument();
            });
        });
    });

    describe('Metric Mode', () => {
        it('should skip regular data query when in metric mode', async () => {
            mockUseSearchParams.mockReturnValue([new URLSearchParams('metricId=456'), jest.fn()]);

            // Reset the mock to track calls properly
            mockUseGetRegisterEntriesSearchPostQuery.mockClear();

            const mockSearchQueryResult = {
                data: undefined,
                isLoading: false,
                refetch: jest.fn(),
            };
            mockUseGetRegisterEntriesSearchPostQuery.mockReturnValue(mockSearchQueryResult);

            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByRole('grid')).toBeInTheDocument();
            });

            expect(mockUseGetRegisterEntriesSearchPostQuery).toHaveBeenCalled();
        });
    });

    describe('Refresh Functionality', () => {
        it('should expose refresh method through ref', async () => {
            const mockRefetch = jest.fn();
            const mockMetricRefetch = jest.fn();

            mockUseGetRegisterEntriesSearchPostQuery.mockReturnValue({
                data: mockRegisterData,
                isLoading: false,
                refetch: mockRefetch,
            });

            mockUseGetRegisterEntriesPostQuery.mockReturnValue({
                data: undefined,
                isLoading: false,
                refetch: mockMetricRefetch,
            });

            const mockRef = { current: null as any };
            mockUseOutletContext.mockReturnValue(mockRef);

            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByRole('grid')).toBeInTheDocument();
            });

            // The ref should be populated with refresh method by the component
            await waitFor(() => {
                expect(mockRef.current).toBeTruthy();
            });

            mockRef.current.refresh();
            expect(mockRefetch).toHaveBeenCalled();
        });
    });

    describe('Delete Entry Dialog', () => {
        beforeEach(() => {
            mockUseSelector.mockImplementation((selector) => {
                if (selector.name === 'getSelectedView') return null;
                if (selector.name === 'getRequestParams') return { page: 0, pageSize: 25 };
                if (selector.name === 'getSelectedEntry') return { id: '123', name: 'Test Entry' };
                return null;
            });
        });

        it('should show delete dialog when delete button is clicked', async () => {
            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByTestId('button-delete')).toBeInTheDocument();
            });

            const deleteButton = screen.getByTestId('button-delete');
            fireEvent.click(deleteButton);

            await waitFor(() => {
                expect(screen.getByText('Are you sure you want to remove entry with id 123?')).toBeInTheDocument();
            });
        });

        it('should close delete dialog when cancel is clicked', async () => {
            renderWithProviders();

            const deleteButton = screen.getByTestId('button-delete');
            fireEvent.click(deleteButton);

            await waitFor(() => {
                expect(screen.getByText('Are you sure you want to remove entry with id 123?')).toBeInTheDocument();
            });

            // Find and click cancel button
            const cancelButton = screen.getByText('No');
            fireEvent.click(cancelButton);

            await waitFor(() => {
                expect(screen.queryByText('Are you sure you want to remove entry with id 123?')).not.toBeInTheDocument();
            });
        });

        it('should execute delete when confirmed', async () => {
            renderWithProviders();

            const deleteButton = screen.getByTestId('button-delete');
            fireEvent.click(deleteButton);

            await waitFor(() => {
                expect(screen.getByText('Are you sure you want to remove entry with id 123?')).toBeInTheDocument();
            });

            const confirmButton = screen.getByText('Yes');
            fireEvent.click(confirmButton);

            await waitFor(() => {
                expect(mockDelete).toHaveBeenCalledWith({
                    regId: 123,
                    entryId: '123',
                });
            });
        });
    });

    describe('URL Parameter Handling', () => {
        it('should handle metric mode correctly', async () => {
            mockUseSearchParams.mockReturnValue([new URLSearchParams('metricId=456'), jest.fn()]);

            const mockMetricRefetch = jest.fn();
            mockUseGetRegisterEntriesPostQuery.mockReturnValue({
                data: mockRegisterData,
                isLoading: false,
                refetch: mockMetricRefetch,
            });

            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByRole('grid')).toBeInTheDocument();
            });

            // Should use metric query instead of regular query
            expect(mockUseGetRegisterEntriesPostQuery).toHaveBeenCalled();
        });

        it('should handle view ID parameter', async () => {
            mockUseSearchParams.mockReturnValue([new URLSearchParams('viewId=123'), jest.fn()]);

            const mockViews = [{ id: 123, name: 'Test View', scope: null, context: 'test_register' }];

            mockUseVrsGetViewsUsingGetQuery.mockReturnValue({
                data: { views: mockViews },
                isSuccess: true,
                refetch: jest.fn(),
                error: null,
                isFetching: false,
            });

            renderWithProviders();

            await waitFor(() => {
                expect(screen.getByRole('grid')).toBeInTheDocument();
            });

            expect(mockUseGetRegisterEntriesSearchPostQuery).toHaveBeenCalled();
        });
    });
});
