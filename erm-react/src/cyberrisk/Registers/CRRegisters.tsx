import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import Typography from '@mui/material/Typography';
import ViewSelector from 'view/components/ViewSelector';
import { setRequestParams, setSelectedEntry, setSelectedView } from 'app/reducer';
import { DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'library/components/Control/ControlDefinitions';
import { SortType } from 'ui/types';
import ToolbarDivider, { ToolbarDividerType } from 'common/components/ToolbarSpacing/ToolbarDivider';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import { strings } from 'common/utils/i18n';
import { Table } from '@protecht/ui-library/library/components/Table';
import ContentLayout from 'common/layouts/ContentLayout';
import React, { FC, MutableRefObject, useCallback, useContext, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import { useDispatch, useSelector } from 'store';
import { getRequestParams, getSelectedEntry, getSelectedView } from 'app/selectors';
import { useOutletContext, useSearchParams } from 'react-router';
import { IdOnly, IdWithParams } from 'app/types';
import { getEntryTableRow } from 'register/utils';
import { useView } from 'view/hooks/useView';
import useSnackbar from 'common/hooks/useSnackbar';
import DeleteEntryDialog from 'common/components/DeleteEntryDialog/DeleteEntryDialog';
import {
    useGetRegisterEntriesPostQuery,
    useGetRegisterEntriesSearchPostQuery,
    useRdrsvDeleteRegisterEntryUsingDeleteMutation,
    useTmrsGetRegisterConfigUsingGet1Query,
} from 'register/rtkApi';
import { getRegisterColDef } from 'register/definitions/RegisterDefinitions';
import { RegisterEntryRest, RegisterRest } from 'register/types';
import { LayoutRef } from 'common/types';
import { Register, RegisterItem, ViewExpressionRest } from 'api/generated/types';
import { useNumericParam } from 'common/hooks/useNumericParam';
import { ControlsContext } from '../ContextProvider';
import { useVrsGetViewsUsingGetQuery } from 'view/rtkApi';
import { isRegisterItem } from 'cyberrisk/Settings/MenuContentsSettings/utils';
import CRMetricsPanel from 'cyberrisk/Metrics/CRMetricsPanel';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';
import { ExpressionType } from 'view/types';
import { STATUS_PROPERTY } from 'rules/constants';

type RegisterWithIdMandatory = Omit<Register, 'id'> & {
    id: number;
};

const CRRegisters: FC = () => {
    const theme = useTheme();
    const dispatch = useDispatch();
    const registerId = useNumericParam('id');
    const ref = useOutletContext<MutableRefObject<LayoutRef>>();
    const { getLibrary, sections } = useContext(ControlsContext);
    const [searchParams] = useSearchParams();
    const viewId = searchParams.get('viewId');
    const metricId = searchParams.get('metricId');
    const registerItem = sections
        ?.flatMap((section) => section.itemList)
        .find((item) => isRegisterItem(item) && (item as RegisterItem).registerId === registerId);
    const { enqueueSuccess, enqueueError } = useSnackbar();
    const [deleteEntryDialogVisible, setDeleteEntryDialogVisible] = useState(false);
    const selectedEntry: IdWithParams | undefined = useSelector(getSelectedEntry);
    const [loadedItems, setLoadedItems] = useState<RegisterWithIdMandatory[]>([]);
    const [totalCount, setTotalCount] = useState(0);

    const [columnFilters, setColumnFilters] = useState<Record<string, string>>({});

    const selectedView = useSelector(getSelectedView);
    const requestParams = useSelector(getRequestParams);
    const library = getLibrary(registerId!, viewId ? Number(viewId) : undefined);
    const { data: register } = useTmrsGetRegisterConfigUsingGet1Query({ id: registerId! }, { skip: !registerId });

    const currentColDef = useMemo(() => {
        return getRegisterColDef(register as RegisterRest);
    }, [register]);
    const { columns, columnVisibilityModel, viewParams } = useView(currentColDef, requestParams, selectedView);

    const columnFilterExpressions = useMemo(() => {
        const expressions: ViewExpressionRest[] = [];

        Object.entries(columnFilters).forEach(([field, value]) => {
            if (value) {
                expressions.push({
                    property: field,
                    expression: ExpressionType.CONTAINS,
                    value: value,
                    type: 'string',
                });
            }
        });

        return expressions;
    }, [columnFilters]);

    const { data, isLoading, refetch } = useGetRegisterEntriesSearchPostQuery(
        { regId: registerId!, ...requestParams, body: columnFilterExpressions },
        { refetchOnMountOrArgChange: true, skip: !registerId || !!metricId },
    );

    const {
        data: metricData,
        isLoading: isMetricDataLoading,
        refetch: metricRefetch,
    } = useGetRegisterEntriesPostQuery(
        { ...requestParams, body: columnFilterExpressions, metricId: Number(metricId) },
        {
            refetchOnMountOrArgChange: true,
            skip: !metricId,
        },
    );

    const [executeDelete, result] = useRdrsvDeleteRegisterEntryUsingDeleteMutation();

    const mapData = (data) => {
        return (
            data?.records?.map((record) => ({
                ...getEntryTableRow(record.record as RegisterEntryRest, null),
                status: record.record.status,
            })) || []
        );
    };

    useEffect(() => {
        setLoadedItems(mapData(data));
        setTotalCount(data?.totalCount || 0);
    }, [data]);

    useEffect(() => {
        setLoadedItems(mapData(metricData));
        setTotalCount(metricData?.totalCount || 0);
    }, [metricData]);

    useEffect(() => {
        if (viewParams !== undefined) {
            dispatch(setRequestParams(viewParams));
        }
    }, [dispatch, viewParams]);

    const { data: viewResponse } = useVrsGetViewsUsingGetQuery(
        {
            context: library?.tableName,
            global: true,
        },
        { skip: !library },
    );

    useEffect(() => {
        const defaultView = viewId ? viewResponse?.views?.find((view) => view.id === Number(viewId)) : null;
        dispatch(setSelectedView(defaultView || null));
        if (viewId && viewResponse && !defaultView) {
            enqueueError(strings('cyberrisk:error.viewNotFound'));
        }
    }, [viewId, viewResponse, dispatch, enqueueError]);

    useImperativeHandle(
        ref,
        () => ({
            refresh() {
                if (metricId) {
                    void metricRefetch();
                } else {
                    void refetch();
                }
            },
        }),
        [metricId, metricRefetch, refetch],
    );

    useEffect(() => {
        if (result.isSuccess) {
            enqueueSuccess(strings('ermMessages:deleting_success'));
            dispatch(setSelectedEntry(undefined));
        }
        if (result.isError) {
            enqueueError(strings('ermErrors:delete_failed'));
        }
    }, [result, enqueueError, enqueueSuccess]);

    useEffect(() => {
        setColumnFilters({});
    }, [registerId]);

    const handleColumnFilterChange = useCallback((field: string, value: string) => {
        setColumnFilters((prev) => {
            const newFilters = { ...prev };

            if (!value) {
                delete newFilters[field];
            } else {
                newFilters[field] = value;
            }

            return newFilters;
        });
    }, []);

    const handleRowDoubleClick = useCallback(
        (params) => {
            const entry = params.row as IdWithParams;
            if (entry) {
                dispatch(setSelectedEntry(undefined));
                window.GwtBridge?.RegisterConfiguration?.showOverScreenWidget?.(
                    `${entry.id}`,
                    `${register?.applicationId}`,
                    `${register?.tableName}`,
                    'true',
                    null,
                );
            }
        },
        [register],
    );

    const handleOpen = useCallback(() => {
        if (selectedEntry) {
            window.GwtBridge?.RegisterConfiguration?.showOverScreenWidget?.(
                `${selectedEntry.id}`,
                `${register?.applicationId}`,
                `${register?.tableName}`,
                'true',
                null,
            );
        }
    }, [selectedEntry, register]);

    const handleNew = useCallback(() => {
        window.GwtBridge?.RegisterConfiguration?.showOverScreenWidget?.(null, `${register?.applicationId}`, `${register?.tableName}`, 'true', null);
    }, [register]);

    const handleSelect = useCallback(
        (selection: IdOnly[]) => {
            dispatch(setSelectedEntry(selection?.[0]));
        },
        [dispatch],
    );

    const handleConfirm = useCallback(() => {
        if (selectedEntry && registerId) {
            void executeDelete({ regId: registerId, entryId: selectedEntry?.id });
            setDeleteEntryDialogVisible(false);
        }
    }, [selectedEntry, registerId, executeDelete]);

    const handleParamChanged = useCallback(
        (params) => {
            dispatch(setRequestParams({ ...requestParams, ...params }));
        },
        [dispatch, requestParams],
    );

    const toolbar = useMemo(
        () => (
            <ToolbarContainer>
                <ToolbarGroup
                    flex={1}
                    justifyContent="space-between"
                >
                    <ToolbarGroup>
                        <Typography
                            noWrap
                            variant="body2"
                        >
                            {library?.description}
                        </Typography>
                    </ToolbarGroup>
                    <ToolbarGroup>
                        <ViewSelector
                            selectedView={selectedView}
                            onViewSelected={(view) => {
                                dispatch(setSelectedView(view));
                            }}
                            key="selectView"
                            context={library?.tableName}
                            columns={currentColDef}
                            defaultFilter={DEFAULT_SEARCH_FIELD}
                            defaultOrderBy={DEFAULT_ORDER_FIELD}
                            defaultOrderType={SortType.ASC}
                            isMetricView={!!metricId}
                            register={register as RegisterRest}
                        />
                        <ToolbarDivider type={ToolbarDividerType.DIVIDER}></ToolbarDivider>
                        <Button
                            {...ButtonStyles.tableToolbarButton}
                            variant="outlined"
                            disabled={!selectedEntry}
                            dataTestId={'button-delete'}
                            aria-label={'Delete entry'}
                            onClick={() => selectedEntry && setDeleteEntryDialogVisible(true)}
                            startIcon={
                                <FontAwesomeIcon
                                    color={theme.palette.error.main}
                                    icon={faTrashAlt}
                                />
                            }
                            style={{ color: theme.palette.error.main }}
                        />
                        <Button
                            {...ButtonStyles.tableToolbarButton}
                            disabled={!selectedEntry}
                            variant="outlined"
                            onClick={handleOpen}
                        >
                            {strings('common:button.open')}
                        </Button>
                        <Button
                            {...ButtonStyles.tableToolbarButton}
                            variant="outlined"
                            startIcon={<Add />}
                            onClick={handleNew}
                        >
                            {strings('common:button.new')}
                        </Button>
                    </ToolbarGroup>
                </ToolbarGroup>
            </ToolbarContainer>
        ),
        [library, selectedView, currentColDef, selectedEntry, dispatch, theme, register, handleOpen, handleNew, metricId],
    );

    return (
        <>
            <ContentLayout toolbar={registerItem ? <CRMetricsPanel item={registerItem as RegisterItem} /> : toolbar}>
                {registerItem && toolbar}
                <Table
                    onColumnFilterChanged={handleColumnFilterChange}
                    columns={columns.map((col) => ({
                        ...col,
                        renderCell: col.headerName === STATUS_PROPERTY ? (params) => params.row.status : col.renderCell,
                    }))}
                    columnFilterValues={columnFilters}
                    rows={loadedItems}
                    totalCount={totalCount}
                    multiselect={false}
                    onSelect={handleSelect}
                    columnVisibilityModel={columnVisibilityModel}
                    params={requestParams}
                    onParamsChanged={handleParamChanged}
                    loading={isLoading || isMetricDataLoading}
                    onRowDoubleClick={handleRowDoubleClick}
                    selected={selectedEntry ? [selectedEntry] : []}
                ></Table>
            </ContentLayout>
            <DeleteEntryDialog
                visible={deleteEntryDialogVisible}
                onConfirm={handleConfirm}
                onClose={() => setDeleteEntryDialogVisible(false)}
                question={strings('cyberrisk:dialog.deleteEntry.question', { entryId: selectedEntry?.id })}
            />
        </>
    );
};

export default CRRegisters;
