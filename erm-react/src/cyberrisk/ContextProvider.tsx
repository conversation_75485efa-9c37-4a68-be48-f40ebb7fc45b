import React, { create<PERSON>ontext, Props<PERSON>ith<PERSON><PERSON>dren, useCallback, useEffect, useMemo, useReducer } from 'react';
import { useSelector } from 'react-redux';
import { cyberRiskApi, useWrsGetFullConfigForEditUsingGetQuery, useWrsUpdateConfigUsingPutMutation } from './rtkApi';
import { RegisterItem, WorkspaceConfig, WorkspaceGroup, WorkspaceGroupItem } from 'api/generated/types';
import { cyberRiskReducer, initialState } from './CyberRiskReducer';
import { removeItem, removeSection, setFilteredSections, setSections } from './ControlsActions';
import { ActiveWorkspaceGroupType, CyberRiskAction, ItemTypes, MenuItemDialogType, SettingsDialogsType } from './types';
import _ from 'lodash';
import useSnackbar from 'common/hooks/useSnackbar';
import { strings } from 'common/utils/i18n';
import { getViewId, isRegisterItem } from './Settings/MenuContentsSettings/utils';
import { CYBER_RISK_MODULE } from './constants';

type ProviderProps = {
    sections?: WorkspaceGroup[];
    filteredSections?: WorkspaceGroup[];
    fixedLevel?: number;
    isBusy: boolean;
    settingsPristine: boolean;
    getLibrary: (id: number, viewId?: number | null) => RegisterItem | undefined;
    defaultWorkspaceName: string;
    removeSectionItem: (sectionItem: WorkspaceGroup) => void;
    removeItem: (sectionItem: WorkspaceGroup, item: WorkspaceGroupItem) => void;
    doUpdateControls: (workspaceName?: string) => void;
    resetDefaults: () => void;
    dispatch: React.Dispatch<CyberRiskAction>;
    activeSettingsDialog: SettingsDialogsType;
    activeSection: ActiveWorkspaceGroupType;
    activeMenuItem: WorkspaceGroupItem | null;
    menuItemDialogType: MenuItemDialogType | null;
    itemType: ItemTypes | null;
};

export const ControlsContext = createContext<ProviderProps>({
    isBusy: true,
    settingsPristine: false,
    getLibrary: () => undefined,
    removeSectionItem: () => ({}),
    removeItem: () => ({}),
    dispatch: () => ({}),
    doUpdateControls: () => ({}),
    resetDefaults: () => ({}),
    defaultWorkspaceName: '',
    activeSettingsDialog: null,
    activeSection: null,
    activeMenuItem: null,
    menuItemDialogType: null,
    itemType: null,
});

const useControlsContext = () => {
    const [state, dispatch] = useReducer(cyberRiskReducer, initialState);
    const { data: filteredConfig, isLoading: isFilteredLoading } = useSelector(
        cyberRiskApi.endpoints.wrsGetUserFilteredWorkspaceUsingGet.select({ workspaceModule: CYBER_RISK_MODULE }),
    );
    const { data: fullConfig, isLoading: isFullLoading } = useWrsGetFullConfigForEditUsingGetQuery({ workspaceModule: CYBER_RISK_MODULE });
    const [saveSettings, { isLoading: isSaving }] = useWrsUpdateConfigUsingPutMutation();
    const { enqueueError, enqueueSuccess } = useSnackbar();
    const [fixedLevel, setFixedLevel] = React.useState<number | undefined>(undefined);
    const [workspaceId, setWorkspaceId] = React.useState<string | undefined>(undefined);
    const [defaultWorkspaceName, setDefaultWorkspaceName] = React.useState<string>('');
    const [contentRole, setContentRole] = React.useState<'NO_ONE' | 'INTERNAL' | 'EVERYONE' | undefined>();

    useEffect(() => {
        if (filteredConfig?.config?.groups) {
            dispatch(setFilteredSections(filteredConfig?.config?.groups));
        }
    }, [filteredConfig]);

    useEffect(() => {
        if ((fullConfig as WorkspaceConfig)?.groups) {
            dispatch(setSections(fullConfig?.groups || []));
        }
    }, [fullConfig]);

    const settingsPristine = useMemo(() => {
        return _.isEqual(state.sections, fullConfig?.groups);
    }, [fullConfig?.groups, state.sections]);

    const getLibrary = useCallback(
        (registerId: number, viewId?: number) => {
            if (state.sections.length && registerId) {
                return state.sections
                    .flatMap((s) => s.itemList)
                    .find((item) => {
                        const itemViewId = getViewId(item);
                        return isRegisterItem(item) && (item as RegisterItem)?.registerId === registerId && (!itemViewId || itemViewId === String(viewId));
                    }) as RegisterItem | undefined;
            }
        },
        [state.sections],
    );

    useEffect(() => {
        if (filteredConfig?.config) {
            setFixedLevel(filteredConfig?.config?.fixedLevel);
            setWorkspaceId(filteredConfig?.config?.id);
            setDefaultWorkspaceName(filteredConfig?.name || '');
            setContentRole(filteredConfig?.config?.contentRole);
        }
    }, [filteredConfig?.config, filteredConfig?.id, filteredConfig?.name]);

    const doUpdateControls = useCallback(() => {
        void saveSettings({
            workspaceConfig: {
                groups: state.sections,
                contentRole: contentRole ?? 'EVERYONE',
                id: workspaceId,
            },
        })
            .unwrap()
            .then(() => {
                enqueueSuccess(strings('common:message.saved'));
            })
            .catch((err) => {
                enqueueError(err.data.message);
            });
    }, [state.sections, enqueueError, contentRole, enqueueSuccess, saveSettings, workspaceId]);

    const removeSectionItem = (sectionItem: WorkspaceGroup) => {
        dispatch(removeSection(sectionItem));
    };

    const resetDefaults = useCallback(() => {
        if (filteredConfig?.config?.groups) {
            dispatch(setSections(filteredConfig?.config?.groups));
        }
    }, [filteredConfig?.config?.groups]);

    return {
        sections: state.sections,
        filteredSections: state.filteredSections,
        activeSettingsDialog: state.activeSettingsDialog,
        activeSection: state.activeSection,
        activeMenuItem: state.activeMenuItem,
        menuItemDialogType: state.menuItemDialogType,
        itemType: state.itemType,
        isBusy: isSaving || isFilteredLoading || isFullLoading,
        settingsPristine,
        getLibrary,
        defaultWorkspaceName,
        doUpdateControls,
        resetDefaults,
        removeSectionItem,
        removeItem,
        dispatch,
        fixedLevel,
    };
};

export const ControlsContextProvider: React.FC<PropsWithChildren> = ({ children }) => {
    const contextData = useControlsContext();
    return <ControlsContext.Provider value={contextData}>{children}</ControlsContext.Provider>;
};
