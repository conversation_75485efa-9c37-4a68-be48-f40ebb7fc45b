import React from 'react';
import { render } from 'test/utils';
import { createMemoryRouter, RouterProvider } from 'react-router';
import Frameworks from './Frameworks';
import { mockFrameworkData, mockFrameworkNodesHierarchyData, mockFrameworkNodesLinksCounts } from 'frameworks/mock';

jest.mock('frameworks/selectors', () => ({
    hasEditPermission: jest.fn(),
    hasMetaDataRegisterPermission: jest.fn(),
    getFrameworkAppId: jest.fn(),
    getFlattenedTree: jest.fn(),
    getSearchValue: jest.fn().mockReturnValue(''),
}));

jest.mock('frameworks/rtkApi', () => ({
    ...jest.requireActual('frameworks/rtkApi'),
    useFcGetFrameworkUsingGetQuery: jest.fn(() => ({
        data: mockFrameworkData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
    useFcGetFrameworkNodesHierarchyUsingGetQuery: jest.fn(() => ({
        data: mockFrameworkNodesHierarchyData,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
    useFlcGetFrameworkNodesLinksCountsUsingGet1Query: jest.fn(() => ({
        data: mockFrameworkNodesLinksCounts,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
}));

describe('Frameworks', () => {
    beforeEach(() => {
        window.ReactBridge = {
            FrameworkConfiguration: {
                refresh: jest.fn(),
            },
        };
    });

    const renderComponent = () => {
        const routes = [
            {
                path: '',
                element: <Frameworks />,
            },
        ];

        const router = createMemoryRouter(routes, {
            initialEntries: [''],
        });

        return render(<RouterProvider router={router} />);
    };

    it('matches snapshot', () => {
        const { container } = renderComponent();
        expect(container).toMatchSnapshot();
    });
});
