import React, { FC } from 'react';
import ContentLayout from 'common/layouts/ContentLayout';
import { FrameworkContextProvider } from 'frameworks/ContextProvider';
import FrameworkNodes from 'frameworks/FrameworkNodes';

const Frameworks: FC = () => {
    return (
        <FrameworkContextProvider>
            <ContentLayout>
                <FrameworkNodes disableToolbar />
            </ContentLayout>
        </FrameworkContextProvider>
    );
};

export default Frameworks;
