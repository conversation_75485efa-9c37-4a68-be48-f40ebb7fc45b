// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Frameworks matches snapshot 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-item css-18q8d33-MuiGrid-root"
  >
    <div
      class="MuiGrid-root MuiGrid-item css-259gsz-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-container MuiGrid-item css-18q8d33-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-item css-1c3mlp4-MuiGrid-root"
          >
            <div
              style="margin: 2px;"
              tabindex="0"
            >
              <div
                class="MuiToolbar-root MuiToolbar-dense css-17sj562-MuiToolbar-root"
              >
                <div
                  class="MuiBox-root css-qjw807"
                >
                  <div
                    class="MuiBox-root css-m39v42"
                  />
                  <div
                    class="MuiBox-root css-m39v42"
                  >
                    <div>
                      <div>
                        <button
                          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-1wrdo5k-MuiButtonBase-root-MuiButton-root"
                          data-testid="button-register1"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-MuiButton-startIcon"
                          >
                            <svg
                              data-icon="view"
                              fill="currentColor"
                              height="20px"
                              viewBox="0 0 24 24"
                              width="20px"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M19.313 4H4.688C3.738 4 3 4.786 3 5.714v12.572C3 19.25 3.738 20 4.688 20h14.625c.914 0 1.687-.75 1.687-1.714V5.714C21 4.786 20.227 4 19.313 4m-8.157 14.286H4.898q-.21 0-.21-.215V13.43h6.468zm0-6.572H4.687V6.857h6.47zm7.946 6.572h-6.258v-4.857h6.469v4.642a.204.204 0 0 1-.211.215m.21-6.572h-6.468V6.857h6.469z"
                                fill="currentColor"
                              />
                            </svg>
                          </span>
                          <span
                            class="css-qv0y8m"
                          >
                            register1
                          </span>
                          <span
                            class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeMedium css-1gnd1fd-MuiButton-endIcon"
                          >
                            <svg
                              data-icon="chevron-down"
                              fill="currentColor"
                              height="24"
                              viewBox="0 0 24 24"
                              width="24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M12.026 12.914 6.601 7.49 4.639 9.452l7.386 7.386 7.389-7.389-1.962-1.962z"
                                fill="currentColor"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </span>
                        </button>
                      </div>
                    </div>
                    <hr
                      class="MuiDivider-root MuiDivider-fullWidth MuiDivider-vertical css-lqeocn-MuiDivider-root"
                    />
                    <button
                      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-863il4-MuiButtonBase-root-MuiButton-root"
                      data-testid="button-Collapse"
                      disabled=""
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="css-1d0doyg"
                      >
                        Collapse
                      </span>
                    </button>
                    <button
                      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-863il4-MuiButtonBase-root-MuiButton-root"
                      data-testid="button-Open"
                      disabled=""
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="css-1d0doyg"
                      >
                        Open
                      </span>
                    </button>
                  </div>
                </div>
              </div>
              <div
                class="MuiBox-root css-1ypw3u6"
              >
                <div
                  class="css-19s4rfn"
                  data-testid="virtuoso-scroller"
                  data-virtuoso-scroller="true"
                  style="height: 100%; outline: none; overflow-y: auto; position: relative;"
                  tabindex="0"
                >
                  <div
                    data-viewport-type="element"
                    style="width: 100%; height: 100%; position: absolute; top: 0px;"
                  >
                    <div
                      data-testid="virtuoso-item-list"
                      style="box-sizing: border-box; margin-top: 0px; padding-top: 0px; padding-bottom: 0px;"
                    />
                  </div>
                </div>
                <div
                  id="DndDescribedBy-0"
                  style="display: none;"
                >
                  
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                </div>
                <div
                  aria-atomic="true"
                  aria-live="assertive"
                  id="DndLiveRegion-0"
                  role="status"
                  style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
       
    </div>
  </div>
</div>
`;
