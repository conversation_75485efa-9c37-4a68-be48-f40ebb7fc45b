import { Key, ReactNode, Ref } from 'react';
import { GridColDef } from '@mui/x-data-grid-pro';
import { TableRestPath } from 'library/types';
import { FilterType, ViewExpression } from 'view/types';
import { RegisterRest } from '../register/types';
import { PagingResult } from './api/types';
import { IdWithName } from 'app/types';
import { LocalizedMessages } from '../resilience/types';
import { SxProps } from '@mui/material/styles';

export interface IconOption {
    value: string;
    icon: ReactNode;
}

export enum MenuOrientation {
    HORIZONTAL = 'HORIZONTAL',
    VERTICAL = 'VERTICAL',
}

export enum NavigationMenuItemType {
    ITEM = 'ITEM',
    DIVIDER = 'DIVIDER',
    EXPANDABLE_ITEM = 'EXPANDABLE_ITEM',
    SCROLLABLE_SECTION = 'SCROLLABLE_SECTION',
    NESTED_TITLE = 'NESTED_TITLE',
    CUSTOM_SECTION = 'CUSTOM_SECTION',
}

export type NavigationMenuItem = BasicMenuItem | ExpandableMenuItem | MenuTitle | ScrollableMenuSection | CustomMenuSection;

export type MenuItemBase = {
    key: Key;
    ignoreArrowNavigation?: boolean;
    id?: string | number;
    type?: NavigationMenuItemType;
};

export type BasicMenuItem = MenuItemBase & {
    icon?: ReactNode;
    label?: string | ReactNode;
    secondaryLabel?: ReactNode;
    pathname?: string;
    onSelect?: (item: NavigationMenuItem) => Promise<boolean> | void;
    disabled?: boolean;
    level?: number;
};

export type ExpandableMenuItem = MenuItemBase & {
    label?: string | ReactNode;
    secondaryLabel?: ReactNode;
    nestedItems: NavigationMenuItem[];
    disabled?: boolean;
};

export type ScrollableMenuSection = MenuItemBase & {
    intersectionObserver: Ref<HTMLDivElement | HTMLLIElement>;
    containerRef?: Ref<HTMLDivElement | HTMLLIElement>;
    nestedItems?: NavigationMenuItem[];
    customStyling?: SxProps;
    isSectionLoading?: boolean;
};

export type CustomMenuSection = MenuItemBase & {
    content: ReactNode;
};

export type MenuTitle = MenuItemBase & {
    label: string | ReactNode;
};

export enum SelectorType {
    AUDIT_QUESTION = 'AUDIT_QUESTION',
    BUSINESS_UNIT = 'BUSINESS_UNIT',
    CONTROL = 'CONTROL',
    COUNTRY = 'COUNTRY',
    QUESTION = 'QUESTION',
    KRI = 'KRI',
    RISK_CAUSE = 'RISK_CAUSE',
    RISK_EVENT = 'RISK_EVENT',
    ROLE = 'ROLE',
    STATE = 'STATE',
    TAGS = 'TAGS',
    TAG_CATEGORY = 'TAG_CATEGORY',
    TAGS_AND_CATEGORIES = 'TAGS_AND_CATEGORIES',
    USER = 'USER',
    BOWTIE = 'BOWTIE',
}

export enum TagContext {
    BOWTIE = 'bowtiediagram',
    RISK_CAUSE = 'riskcause',
    RISK_EVENT = 'riskevent',
    CONTROL = 'control',
    KRI = 'keyriskindicator',
}

export type NonEmptyArray<T> = [T, ...T[]];

export type DataGridColDef = GridColDef & {
    field: string;
    apiField?: string;
    filterType?: FilterType;
    groupable?: boolean;
    hidden?: boolean;
    columnType?: string;
};

export enum AlertType {
    Info = 'INFO',
    Warning = 'WARNING',
    Error = 'ERROR',
    Confirm = 'CONFIRM',
}

export interface DataGridVisibilityParams {
    field: string;
    isVisible: boolean;
}

export interface DataGridOrderChangeParams {
    element?: HTMLElement | null;
    field: string;
    colDef: GridColDef;
    targetIndex: number;
    oldIndex: number;
}

export type DataLoaderResponse<T> = {
    response: PagingResult<T>;
    error?: string;
    loading: boolean;
    allRecordsCount?: number;
    refresh: (pageLimit?: number) => void;
    reset?: () => void;
    lastElementRef?: Ref<HTMLDivElement | HTMLLIElement>;
    hasMoreToLoad?: boolean;
};

export type LayoutRef = {
    refresh: () => void;
};

export type AdditionalRequestParams = {
    path?: TableRestPath | string;
    register?: RegisterRest | null;
    removeViewId?: boolean;
    statusFilter?: ViewExpression | null;
    stateId?: string;
    // if entry filter is empty, all the results will be displayed
    // this parameter defines if it should display all the results in case entry filter is empty
    showAllResults?: boolean;
    vendorId?: number;
    vendorBusinessUnit?: IdWithName;
};

export enum LayoutOption {
    CARD,
    LIST,
}

export enum SYSTEM_COLUMN {
    ID = 'id',
    FIELD_CREATE_DATE = 'createdate',
    FIELD_CREATED_BY = 'createdby',
    FIELD_MODIFIED_DATE = 'lastmodifieddate',
    FIELD_MODIFIED_BY = 'lastmodifiedby',
    FIELD_COMPLETED = 'completed',
    FIELD_END_DATE = 'enddate',
    FIELD_ORIGINAL_ID = 'originalid',
    FIELD_REPLACEMENT_DATE = 'replacementdate',
    FIELD_UUID = 'uuid',
    FIELD_BUSINESS_UNIT = 'businessunit',
}

export enum DATA_SYSTEM_COLUMN {
    FIELD_CREATE_DATE = 'createDate',
    FIELD_CREATED_DATE = 'createdDate',
    FIELD_CREATED_BY = 'createdBy',
    FIELD_MODIFIED_DATE = 'lastModifiedDate',
    FIELD_MODIFIED_BY = 'lastModifiedBy',
    FIELD_COMPLETED = 'completed',
    FIELD_ORIGINAL_ID = 'originalid',
    FIELD_NAME = 'name',
    FIELD_DESCRIPTION = 'description',
    FIELD_BUSINESS_UNITS = 'businessUnits',
    FIELD_TAGS = 'tags',
    CENTRAL_RISK_EVENT_NAME = 'centralRiskName',
}

export type MaybeNullable<T> = T | undefined | null;

export enum ApplicationName {
    VRM = 'Vendor Risk Management',
    MARKETPLACE = 'Marketplace',
    RISK_ANALYSIS = 'Risk Analysis',
    FRAMEWORK = 'Framework',
    SECTION = 'Section',
    CONFIGURATION = 'Configuration',
    WALK_ME = 'WalkMe',
    ADMINISTRATION = 'Administration',
    PROFILE = 'Profile',
    NATIVE_MOBILE_APP = 'Native Mobile App',
    WEB_SERVICES = 'Web Services',
    TAG = 'Tag',
    CENTRAL_LIBRARIES = 'Central Libraries',
    WORKSPACE = 'Workspace',
}

export enum PermissionCodes {
    REGISTER_DATA_ADD = 'REGISTER.DATA.ADD',
    REGISTER_DATA_VIEW_ALL = 'REGISTER.DATA.VIEW.ALL',
    REGISTER_DATA_VIEW_BU = 'REGISTER.DATA.VIEW.BU',
    REGISTER_DATA_VIEW_CREATOR = 'REGISTER.DATA.VIEW.CREATOR',
    REGISTER_DATA_VIEW_SUBTABLE = 'REGISTER.DATA.VIEW.SUBTABLE',
    REGISTER_DATA_EDIT_ALL = 'REGISTER.DATA.EDIT.ALL',
    REGISTER_DATA_EDIT_BU = 'REGISTER.DATA.EDIT.BU',
    REGISTER_DATA_EDIT_CREATOR = 'REGISTER.DATA.EDIT.CREATOR',
    SECTION_DATA_VIEW = 'SECTION.DATA.VIEW',
    SECTION_DATA_EDIT = 'SECTION.DATA.EDIT',
    REGISTER_DATA_TRANSITION = 'REGISTER.DATA.TRANSITION',
    WORKFLOW_APPLICATION_MANAGE = 'WORKFLOW.APPLICATION.MANAGE',
    VRM_PORTAL_LOGIN = 'VRM.PORTAL.LOGIN',
    VRM_MANAGE_WORKSPACE = 'VRM.MANAGE.WORKSPACE',
    VRM_MANAGE_SETTINGS = 'VRM.MANAGE.SETTINGS',
    RISK_ANALYSIS_MANAGE = 'RISK.ANALYSIS.MANAGE',
    METRICS_MANAGE = 'METRICS.MANAGE',
    REGISTER_DATA_EXPORT = 'REGISTER.DATA.EXPORT',
    REGISTER_DATA_IMPORT = 'REGISTER.DATA.IMPORT',
    REGISTER_DATA_REPORT_ALL = 'REGISTER.DATA.REPORT.ALL',
    REGISTER_DATA_REPORT_BU = 'REGISTER.DATA.REPORT.BU',
    REGISTER_DATA_REPORT_CREATOR = 'REGISTER.DATA.REPORT.CREATOR',
    REGISTER_DATA_DELETE = 'REGISTER.DATA.DELETE',
    REGISTER_DATA_SHARE = 'REGISTER.DATA.SHARE',
    REGISTER_DATA_ADDITIONAL_ACCESS = 'REGISTER.DATA.ADDITIONAL.ACCESS',
    REGISTER_DATA_EXCLUSIVE_ACCESS = 'REGISTER.DATA.EXCLUSIVE.ACCESS',
    REGISTER_DATA_COPY = 'REGISTER.DATA.COPY',
    MARKETPLACE_MANAGE = 'MARKETPLACE.MANAGE',
    MARKETPLACE_VIEW = 'MARKETPLACE.VIEW',
    BOWTIE_RESTORE = 'RISK.ANALYSIS.RESTORE',
    BOWTIE_LOCK = 'RISK.ANALYSIS.LOCK',
    BOWTIE_PURGE = 'RISK.ANALYSIS.PURGE',
    FRAMEWORK_VIEW = 'FRAMEWORK.VIEW',
    FRAMEWORK_MANAGE = 'FRAMEWORK.MANAGE',
    FRAMEWORK_EDIT = 'FRAMEWORK.DATA.EDIT.ALL',
    REGISTER_DESIGN = 'REGISTER.DESIGN',
    WEB_API_DOCS = 'VIEW.WAPI.DOCS',
    WALKME_ADMIN = 'WALKME.ADMIN',
    WALKME_ANALYTICS = 'WALKME.ANALYTICS',
    WALKME_ENDUSER = 'WALKME.ENDUSER',
    WORKSPACE_MANAGE = 'WORKSPACE.MANAGE',
    WORKSPACE_VIEW = 'WORKSPACE.VIEW',
    APPLICATION_MANAGE = 'APPLICATION.MANAGE',
    TAGS_IMPORT = 'TAGS.IMPORT',
    TAGS_EXPORT = 'TAGS.EXPORT',
    TAGS_MANAGE = 'TAGS.MANAGE',
    TAGS_CREATE = 'TAGS.CREATE',
    PROFILE_MANAGER_EDIT = 'PROFILE.MANAGER.EDIT',
    PROFILE_PASSWORD_CHANGE = 'PROFILE.PASSWORD.CHANGE',
    PROFILE_DETAILS_EDIT = 'PROFILE.DETAILS.EDIT',
    USERS_MANAGE = 'USERS.MANAGE',
    MOBILE_NATIVE_ACCESS = 'MOBILE.NATIVE.ACCESS',
    WEB_SERVICES_ACCESS = 'WEB.SERVICES.ACCESS',
}

export enum PasswordStrength {
    NONE,
    WEAK,
    MEDIUM,
    GOOD,
    STRONG,
    VERYSTRONG,
}

export type PasswordStrengthProps = {
    strength: string;
    name: string;
    color: string;
    message: string;
};

export type TerminologyReplacement = {
    placeholderString: string;
    terminologyItem: keyof LocalizedMessages;
};

export type StaticTerminologyReplacement = {
    placeholderString: string;
    staticTerminologyItem?: string;
};

export type InterpolateTerminologyFunc = (
    translatedString: string,
    terminologyReplacements?: TerminologyReplacement[],
    staticTerminologyReplacements?: StaticTerminologyReplacement[],
) => string;

export enum AsyncThunkStatus {
    IDLE = 'idle',
    PENDING = 'pending',
    SUCCEEDED = 'succeeded',
    FAILED = 'failed',
}

export type DateWithFormat = {
    value: string | null | undefined;
    dateTimePattern?: string;
    datePattern?: string;
    timePattern?: string;
};

export type Accept = {
    [key: string]: string[];
};

export enum DialogType {
    PERMISSIONS = 'permissions',
    REGISTER_SELECTOR = 'registerSelector',
    DISCARD = 'discard',
    UNSAVED = 'unsaved',
    CONFIRMATION = 'confirmation',
}
