import { Ref, useCallback, useEffect, useRef, useState } from 'react';
import { PagingResult } from 'common/api/types';
import { RolePagingResult } from 'rolesAndPermissions/api';
import useIntersectionObserver from '../useIntersectionObserver';
import useAsync, { AsyncStatus } from '../useAsync/useAsync';

type InfinityScrollLoadingReturnType<T> = {
    intersectionObserver: Ref<HTMLDivElement | HTMLLIElement>;
    isLoading: boolean;
    error: any;
    loadedItems: T[] | undefined;
    hasMoreToLoad: boolean;
    totalCount: number;
    currentPage: number | undefined;
    refresh: (pageLimit?: number) => void;
    reset: () => void;
    overrideLoadedItems: (loadedItems: T[] | undefined) => void;
};

type InfiniteScrollQuery<T> = (page: number, limit?: number) => Promise<T[] | PagingResult<T> | undefined>;

const getRefreshPages = (itemsCount: number, pageLimit: number): number => {
    return Math.ceil(itemsCount / pageLimit);
};

/**
 * Hook for creating infinitely loaded lists.
 * Necessary data (current page for requests triggered by scrolling, or limit used when refreshing the whole list) are communicated upwards to parent components as query function parameters.
 * Request parameters and any query dependencies (filters, searchStrings etc.) are handled in parent component.
 * Use useCallback when creating query and use it's dependencies as a way to reset loader when needed for new results.
 * List is completely reset (page is set to 0 and new results are being displayed) when query changes (either different url needs to be called or when any query dependencies (filters) change).
 * Use useAbortableQuery hook in parent if cancelling requests is needed.
 *
 * Example usage
 *
 * const [anyParamsOrFilters, setAnyParamsOrFilters] = useState();
 *
 * const dataLoader = useCallback((page: number, limit?: number) => {
 *   return callYourApiRequest({ ...anyParamsOrFilters, page, limit: limit ?? queryParams.limit });
 * }, [anyParamsOrFilters, ...otherDeps]);
 *
 * use useCallback deps to trigger reset of scroller list and start displaying different results
 * otherwise new items are loaded with same query and params as user scrolls
 *
 * use intersectionObserver as ref={intersectionObserver} on last item of scrollable list or on the loading indicator to listen for element intersections
 * const {intersectionObserver, loadedItems } = useInfiniteScrollLoading<T>(dataLoader);
 *
 * const lastIndex = loadedItems.length - 1;
 *
 * <Box>
 *   {loadedItems.map((item, index) => <span ref={index === lastIndex ? intersectionObserver : null}>your content here</span> )}
 * </Box>
 * @param query
 * @returns
 */
const useInfiniteScrollLoading = <T>(query: InfiniteScrollQuery<T> | undefined): InfinityScrollLoadingReturnType<T> => {
    const currentPageRef = useRef<number>(0);
    const [loadedItems, setLoadedItems] = useState<T[] | undefined>(undefined);
    const loadedItemsCount = useRef<number>(0);
    const [totalCount, setTotalCount] = useState<number>(0);
    const [hasMoreToLoad, setHasMoreToLoad] = useState<boolean>(false);

    const { intersectionObserver, intersectedElement } = useIntersectionObserver();

    const queryRef = useRef(query);
    useEffect(() => {
        queryRef.current = query;
    }, [query]);

    const { isLoading, error, response, asyncLoad } = useAsync<T[] | PagingResult<T> | RolePagingResult<T> | undefined>({ status: AsyncStatus.PENDING });
    const [loading, setLoading] = useState<boolean>(true);

    // loader function triggered by intersecting observed element or when resetting / refreshing of scrollable list
    const triggerLoad = useCallback(() => {
        if (queryRef.current) {
            void asyncLoad(queryRef.current(currentPageRef.current));
        }
    }, [asyncLoad]);

    // observing intersection of observed DOM element (usually last item of scrollable list)
    useEffect(() => {
        if (intersectedElement?.isIntersecting && hasMoreToLoad) {
            currentPageRef.current = currentPageRef.current + 1;
            triggerLoad();
        }
    }, [intersectedElement, hasMoreToLoad, triggerLoad]);

    // trigger first load on init
    useEffect(() => {
        triggerLoad();
    }, [triggerLoad]);

    // reset function called when query url or it's dependencies change in parent component
    // (e.g. queryParams, expressions, filtering etc.)
    const reset = useCallback(() => {
        currentPageRef.current = 0;
        loadedItemsCount.current = 0;
        setHasMoreToLoad(false);
        setLoadedItems(undefined);
        setTotalCount(0);
        setLoading(true);
        triggerLoad();
    }, [triggerLoad]);

    // reset loader and trigger loading when query or it's deps change
    useEffect(() => {
        reset();
    }, [query, reset]);

    // get refresh limit based on given page limit and actual number of loaded items or undefined if page limit or loaded items are not
    // defined
    const getRefreshLimit = useCallback(
        (pageLimit?: number) => {
            if (loadedItems?.length) {
                if (pageLimit) {
                    return pageLimit * getRefreshPages(loadedItems?.length, pageLimit);
                }
                return loadedItems?.length;
            }
            return undefined;
        },
        [loadedItems?.length],
    );

    // refresh list of items after some user update interaction, request the same amount of items as already loaded in the list
    // page is 0, limit is the multiple of the given page limit, higher as the count of currently loaded items
    const refresh = useCallback(
        (pageLimit?: number) => {
            if (queryRef.current) {
                setLoading(true);
                void asyncLoad(queryRef.current(0, getRefreshLimit(pageLimit)));
                if (loadedItems?.length && pageLimit) {
                    const pages = getRefreshPages(loadedItems?.length, pageLimit);
                    currentPageRef.current = pages - 1;
                }
                setLoadedItems(undefined);
            }
        },
        [asyncLoad, loadedItems],
    );

    useEffect(() => {
        if (Array.isArray(response) && response.length) {
            setLoadedItems((previousItems) => [...(previousItems || []), ...response]);
            setHasMoreToLoad(true);
            setLoading(isLoading);
        } else if (response && (('records' in response && response.records?.length) || ('results' in response && response.results?.length))) {
            const pagedResults = (response as PagingResult<T>).records || (response as RolePagingResult<T>).results || [];
            setLoadedItems((previousItems) => {
                const newItems = [...(previousItems || []), ...pagedResults];
                loadedItemsCount.current = newItems.length;
                return newItems;
            });
            setTotalCount(response.totalCount);
            setLoading(isLoading);
        } else if ((response && 'records' in response && response.records?.length) === 0) {
            setLoadedItems([] as T[]);
            loadedItemsCount.current = 0;
            setTotalCount(0);
            setLoading(isLoading);
        } else {
            setHasMoreToLoad(false);
            setLoading(isLoading);
        }
    }, [response]);

    useEffect(() => {
        if (loadedItemsCount.current === totalCount) {
            setHasMoreToLoad(false);
        } else {
            setHasMoreToLoad(true);
        }
    }, [loadedItems, totalCount]);

    useEffect(() => {
        if (error) {
            setTotalCount(-1);
            setLoadedItems(undefined);
        }
    }, [error]);

    return {
        intersectionObserver,
        isLoading: loading,
        error,
        loadedItems,
        totalCount,
        hasMoreToLoad,
        currentPage: currentPageRef.current,
        refresh,
        reset,
        overrideLoadedItems: setLoadedItems,
    };
};

export default useInfiniteScrollLoading;
