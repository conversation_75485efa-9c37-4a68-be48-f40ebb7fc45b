import { renderHook } from '@testing-library/react';
import useInfiniteScrollLoading from './useInfiniteScrollLoading';
import { Provider } from 'react-redux';
import React from 'react';
import { setupTestingStore } from '../../../store';
import { act, waitFor } from '@testing-library/react';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';

const _mockParams: SearchRequestParams = {
    limit: 10,
    offset: 0,
};

const mockFetchData = jest.fn(async (page: number, _limit?: number) => {
    return page > 0 ? Promise.resolve({ totalCount: 2, records: [{ id: 1 }, { id: 2 }] }) : Promise.resolve({ totalCount: 2, records: [{ id: 3 }, { id: 4 }] });
});

describe('useInfiniteScrollLoading', () => {
    const store = setupTestingStore({});

    it('should have an initial state', async () => {
        const { result } = renderHook(() => useInfiniteScrollLoading(mockFetchData), {
            wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
        });
        expect(result.current.error).toBe(undefined);
        expect(result.current.isLoading).toBe(true);
        expect(result.current.loadedItems).toEqual(undefined);
        expect(result.current.totalCount).toBe(0);

        await waitFor(() => expect(result.current.isLoading).toBe(false));
    });

    it('should load more data', async () => {
        let result;
        await act(async () => {
            result = renderHook(() => useInfiniteScrollLoading(mockFetchData), {
                wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
            }).result;
        });

        expect(mockFetchData).toHaveBeenCalledWith(0);
        expect(result.current.loadedItems).toEqual([{ id: 3 }, { id: 4 }]);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.totalCount).toBe(2);
    });

    it('should refresh data', async () => {
        let result;
        await act(async () => {
            result = renderHook(() => useInfiniteScrollLoading(mockFetchData), {
                wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
            }).result;
        });

        await act(async () => {
            result.current.refresh(mockFetchData);
        });

        expect(mockFetchData).toHaveBeenCalledWith(0);
        expect(result.current.loadedItems).toEqual([{ id: 3 }, { id: 4 }]);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.totalCount).toBe(2);
    });

    it('should reset data', async () => {
        let result;
        await act(async () => {
            result = renderHook(() => useInfiniteScrollLoading(mockFetchData), {
                wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
            }).result;
        });

        await waitFor(() => {
            expect(result.current.loadedItems).toEqual([{ id: 3 }, { id: 4 }]);
        });

        await act(async () => {
            result.current.reset();
        });

        expect(result.current.error).toBe(undefined);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.loadedItems).toEqual([{ id: 3 }, { id: 4 }]);
    });
});
