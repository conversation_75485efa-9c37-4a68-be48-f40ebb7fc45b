import { useMemo } from 'react';
import { useGetSystemConfigurationQuery } from 'app/rtkApi';
import { useFersGetAllowedExtensionsUsingGetQuery } from 'common/api/files';
import { filterAllowedFileTypes, transformSystemExtensions } from 'common/utils/FileUtils';
import { Accept } from 'common/types';

type useFilesSettingsReturn = {
    allowedFileTypes?: Accept;
    maxFileSize: number;
};

const useFilesSettings = (fileSize = Infinity, accept?: Accept): useFilesSettingsReturn => {
    const { data: systemConfiguration } = useGetSystemConfigurationQuery();
    const { data: allowedExtensions } = useFersGetAllowedExtensionsUsingGetQuery();

    const systemFileSize = systemConfiguration?.max_attach_size_bytes ? Number(systemConfiguration.max_attach_size_bytes) * 1024 * 1024 : 0;
    const maxFileSize = Math.min(systemFileSize, fileSize);

    const allowedFileTypes = useMemo(() => {
        let systemAllowedFileTypes: Accept | undefined;

        if (systemConfiguration?.upload_whitelist === 'true') {
            systemAllowedFileTypes = transformSystemExtensions(allowedExtensions?.records);
        }

        return systemAllowedFileTypes ? filterAllowedFileTypes(systemAllowedFileTypes, accept) : accept || undefined;
    }, [accept, allowedExtensions?.records, systemConfiguration?.upload_whitelist]);

    return { maxFileSize, allowedFileTypes };
};

export default useFilesSettings;
