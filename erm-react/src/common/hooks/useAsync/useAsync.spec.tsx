import { renderHook, waitFor } from '@testing-library/react';
import useAsync, { AsyncStatus } from './useAsync';
import { setupTestingStore } from '../../../store';
import { Provider } from 'react-redux';
import React from 'react';

describe('useAsync', () => {
    const store = setupTestingStore({});

    it('should handle a successful async request', async () => {
        const mockPromise = Promise.resolve({ data: 'some data' });
        const { result } = renderHook(() => useAsync<{ data: string }>(), {
            wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
        });

        await waitFor(async () => {
            await result.current.asyncLoad(mockPromise);
        });
        await mockPromise;

        await waitFor(() => {
            expect(result.current.status).toBe(AsyncStatus.RESOLVED);
        });
        expect(result.current.response).toEqual({ data: 'some data' });
        expect(result.current.error).toBe(undefined);
    });

    it('should handle a rejected async request', async () => {
        const mockPromise = Promise.reject({ message: 'Some error message' });
        const { result } = renderHook(() => useAsync<{ data: string }>(), {
            wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
        });

        await waitFor(async () => {
            await result.current.asyncLoad(mockPromise);
        });

        await mockPromise.catch(() => ({}));

        await waitFor(() => {
            expect(result.current.status).toBe(AsyncStatus.REJECTED);
        });
        expect(result.current.response).toBe(undefined);
        expect(result.current.error).toEqual({ message: 'Some error message' });
    });

    it('should reset the state', async () => {
        const mockPromise = Promise.resolve({ data: 'some data' });
        const { result } = renderHook(() => useAsync<{ data: string }>(), {
            wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
        });

        await waitFor(async () => {
            await result.current.asyncLoad(mockPromise);
        });

        await waitFor(() => {
            result.current.reset();
            expect(result.current.status).toBe(AsyncStatus.IDLE);
        });
        expect(result.current.response).toBe(undefined);
        expect(result.current.error).toBe(undefined);
    });
});
