import { ApiError } from 'common/api/types';
import { isCancel } from 'common/api/utils/api';
import { useReducer, useRef, useCallback, Reducer } from 'react';
import useSafeDispatch from '../useSafeDispatch';
import { useDispatch } from 'store';
import { setGlobalError } from 'app/reducer';

export enum AsyncStatus {
    IDLE = 'IDLE',
    RESOLVED = 'RESOLVED',
    REJECTED = 'REJECTED',
    PENDING = 'PENDING',
}

export type UseAsyncReturnType<T> = {
    isIdle: boolean;
    isLoading: boolean;
    isError: boolean;
    isSuccess: boolean;
    setResponse: (response: T) => void;
    setError: (response: unknown) => void;
    error: ApiError | undefined;
    status: AsyncStatus;
    response: T | undefined;
    asyncLoad: (promise: Promise<T>) => Promise<T>;
    reset: () => void;
};

export type AsyncState<T> = {
    status: AsyncStatus;
    response: T | undefined;
    error: ApiError | undefined;
};

const asyncReducer = <T>(state: AsyncState<T>, action: Partial<AsyncState<T>>): AsyncState<T> => ({
    ...state,
    ...action,
});

const defaultInitialState = { status: AsyncStatus.IDLE, response: undefined, error: undefined };

/**
 * *****Please use this useAsync hook instead of async/await functions so that errors are managed correctly.*****
 *
 * UseAsync hooks makes it easier to manage making async calls, managing loading state, recieved response and catched errors.
 * Errors are stored on APP redux slice where they are managed globally.
 * where they are translated and displayed as toasts.
 *
 * Example usage
 *
 * EXAMPLE 1 basic usage
 * import {yourRequest} from '../../api';
 *
 * // response/error come from useState inside useAsync and update the UI
 * const {asyncLoad: loadData, response: loadedData, error: loadingError, isLoading} = useAsync<YourTypeHere>();
 *
 * useEffect(() => {
 *   loadData(yourRequest('parameter'));
 * }, []);
 *
 * if (loadingError) {
 *  return <span>an error occured</span>
 * }
 *
 * return isLoading ? <span>...loading</span> : <span>display loaded data {loadedData}<span>
 *
 * EXAMPLE 2 chained error handling / success callbacks
 * *****don't forget to return rejected promise so that global error handling and toast in ApiErrorContext is fired as well*****
 *
 * import {yourRequest} from '../../api';
 *
 * // no error or response variables this time, changes of response/error do not update UI
 * const {asyncLoad} = useAsync<YourTypeHere>();
 *
 * useEffect(() => {
 *   asyncLoad(yourRequest('parameter').then((response) => {
 *      // e.g. update store, redirect etc
 *    }, error => {
 *      // handle additional error sideeffects
 *      history.push('/home');
 *      // trigger error callback with global error handling inside asyncLoad
 *      return Promise.reject(error);
 *    }));
 * }, []);
 */

const useAsync = <T>(initialState: Partial<AsyncState<T>> = {}, disableErrorHandling?: boolean): UseAsyncReturnType<T> => {
    const initialStateRef = useRef<AsyncState<T>>({
        ...defaultInitialState,
        ...initialState,
    });

    const [{ status, response, error }, setState] = useReducer<Reducer<AsyncState<T>, Partial<AsyncState<T>>>>(asyncReducer, initialStateRef.current);
    const dispatch = useDispatch();

    const safeSetState = useSafeDispatch<Partial<AsyncState<T>>>(setState);

    const asyncLoad = useCallback(
        (promise: Promise<T>) => {
            if (!promise || !promise.then) {
                throw new Error('The argument passed to useAsync().asyncLoad must be a promise.');
            }
            safeSetState?.({ status: AsyncStatus.PENDING, error: undefined });
            return promise.then(
                (response: T) => {
                    safeSetState?.({ response, status: AsyncStatus.RESOLVED });
                    return response;
                },
                (error) => {
                    if (!disableErrorHandling && !isCancel(error)) {
                        safeSetState?.({ error });
                        dispatch(
                            setGlobalError({
                                response: error.response,
                                status: error.status,
                                statusText: error.statusText,
                            }),
                        );
                    }
                    if (!isCancel(error)) {
                        safeSetState({ status: AsyncStatus.REJECTED });
                        return error;
                    }
                },
            );
        },
        [disableErrorHandling, dispatch, safeSetState],
    );

    const setResponse = useCallback((response: T) => safeSetState?.({ response }), [safeSetState]);
    const setError = useCallback((error) => safeSetState?.({ error }), [safeSetState]);
    const reset = useCallback(() => safeSetState?.(initialStateRef.current), [safeSetState]);

    return {
        isIdle: status === AsyncStatus.IDLE,
        isLoading: status === AsyncStatus.PENDING,
        isError: status === AsyncStatus.REJECTED,
        isSuccess: status === AsyncStatus.RESOLVED,
        setResponse,
        setError,
        error,
        status,
        response,
        asyncLoad,
        reset,
    };
};

export default useAsync;
