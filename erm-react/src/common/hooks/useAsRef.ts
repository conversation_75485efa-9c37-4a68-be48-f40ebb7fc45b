import { MutableRefObject, useEffect, useRef } from 'react';

/**
 * Use any value as useRef mutable object value, this prevents triggering of useEffects, useCallbacks etc as useRef does not trigger rerendering
 * Use as a last resort when trying to avoid rerenders
 * https://legacy.reactjs.org/docs/hooks-faq.html#is-it-safe-to-omit-functions-from-the-list-of-dependencies
 * https://react.dev/learn/removing-effect-dependencies
 * @param value
 * @returns
 */
const useAsRef = <T>(value: T): MutableRefObject<T | undefined> => {
    const refValue = useRef<T>();

    useEffect(() => {
        refValue.current = value;
    }, [value]);

    return refValue;
};

export default useAsRef;
