//import { MenuOrientation } from 'common/types';
import { useMemo } from 'react';
import { usePursGetCurrentUserUsingGetQuery } from 'user/rtkApi';

const useUserMenuOrientation = (): { isLoading: boolean; hasVerticalMenu: boolean | undefined } => {
    const { data: _currentUser, isLoading } = usePursGetCurrentUserUsingGetQuery();

    const returnValue = useMemo(() => {
        return { isLoading, hasVerticalMenu: isLoading ? undefined : ProtechtDictionary.ariaEnabled === 'true' };
        // TODO: when BE is done uncomment and remove the previous line with ProtechtDictionary check
        //return { isLoading, hasVerticalMenu: isLoading ? undefined : currentUser?.menuOrientation === MenuOrientation.VERTICAL };
    }, [isLoading /* , currentUser?.menuOrientation */]);

    return returnValue;
};

export default useUserMenuOrientation;
