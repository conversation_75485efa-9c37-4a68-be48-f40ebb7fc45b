import { usePursGetUserPermissionsUsingGetQuery, usePursGetCurrentUserUsingGetQuery } from 'user/rtkApi';
import { useUmrsGetMenuUsingGetQuery } from 'common/api/menu';
import { useGetSystemConfigurationQuery } from 'app/rtkApi';
import { useEffect } from 'react';
import { useDispatch } from 'store';
import { setIsCriticalError } from 'app/reducer';

type UseCriticalAppApiOptions = {
    skipAll?: boolean;
    skipMenu?: boolean;
    skipPermissions?: boolean;
};

/**
 * This hook is used to fetch critical data needed for the application
 * If one of the queries fails, it sets the critical error state which is used to show an error page
 */

const useCriticalAppApi = (options: UseCriticalAppApiOptions = {}) => {
    const dispatch = useDispatch();
    const { isError: isErrorUser } = usePursGetCurrentUserUsingGetQuery(undefined, { skip: options.skipAll });
    const { isError: isErrorPermissions } = usePursGetUserPermissionsUsingGetQuery({}, { skip: options.skipAll || options.skipPermissions });
    const { isError: isErrorMenu } = useUmrsGetMenuUsingGetQuery({ fillDefaultIcons: true }, { skip: options.skipAll || options.skipMenu });
    const { isError: isErrorSystemConfig } = useGetSystemConfigurationQuery(undefined, { skip: options.skipAll });
    const isError = isErrorUser || isErrorPermissions || isErrorSystemConfig || isErrorMenu;

    useEffect(() => {
        if (isError) {
            dispatch(setIsCriticalError(true));
        }
    }, [dispatch, isError]);
};

export default useCriticalAppApi;
