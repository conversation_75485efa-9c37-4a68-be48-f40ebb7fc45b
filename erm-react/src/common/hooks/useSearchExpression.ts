import { ViewExpressionRest } from 'api/generated/types';
import { useEffect, useState } from 'react';
import { FilterType } from 'view/types';

/**
 * Search expression is used as a useCallback dep that triggers table rerenders and fetching of table data. It consist of search value and search field property.
 * When user changes (or clears) search value and when user changes field property and search value is defined, a request is triggered.
 * When user changes field property but value is an empty string, no refresh is triggered. This exception is handled inside of this custom hook.
 * @param searchValue value of search input
 * @param searchProperty value of search select - search field property (id, business unit, status etc)
 * @returns complete search expression
 */
export default function useSearchExpression(searchValue: string, searchProperty?: string): ViewExpressionRest | undefined {
    const [expression, setExpression] = useState<ViewExpressionRest | undefined>(undefined);

    useEffect(() => {
        setExpression((previousExpression) => {
            if (searchValue === '' && previousExpression?.value === '') {
                return previousExpression;
            } else {
                return {
                    id: 0,
                    expression: 'contains' as const,
                    property: searchProperty,
                    type: FilterType.STRING,
                    value: searchValue || '',
                };
            }
        });
    }, [searchValue, searchProperty]);

    return expression;
}
