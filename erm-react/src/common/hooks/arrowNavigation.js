import React, { useEffect, useRef, createContext, useReducer, useContext, useCallback } from 'react';

function useKey(key, handler) {
    const savedHandler = useRef(handler);

    useEffect(() => {
        savedHandler.current = handler;
    }, [handler]);

    useEffect(() => {
        const keyListener = (event) => {
            const focusableInputs = ['text', 'number'];
            const activeElement = document.activeElement;

            const isFocusableInput =
                (activeElement instanceof HTMLInputElement && focusableInputs.includes(activeElement.type)) || activeElement instanceof HTMLTextAreaElement;

            if (event.code === key && savedHandler.current && !isFocusableInput) {
                event.preventDefault(); // this prevents from scrolling down when using keyboard nav
                savedHandler.current(event);
            }
        };

        window.addEventListener('keydown', keyListener);

        return () => {
            window.removeEventListener('keydown', keyListener);
        };
    }, [key]);
}

function modulo(x, y) {
    return ((x % y) + y) % y;
}

function findBoundedXIndex(xIndex, yIndex, delta, matrix) {
    var xLength = matrix[yIndex]?.length;
    var newXIndex = xIndex + delta;

    var iterations = Math.sign(delta) === -1 ? newXIndex + 1 : xLength - newXIndex;
    for (var i = 0; i < iterations; i++) {
        if (matrix[yIndex] && matrix[yIndex][newXIndex]) {
            return { xIndex: newXIndex, yIndex: yIndex };
        }

        newXIndex = newXIndex + delta;
    }

    return { xIndex: xIndex, yIndex: yIndex };
}

function findContinuousXIndex(xIndex, yIndex, delta, matrix) {
    var deltaIsNegative = Math.sign(delta) === -1;
    var yLength = matrix.length;
    var newYIndex = yIndex;

    for (var i = 0; i < yLength + 1; i++) {
        if (!matrix[newYIndex]) {
            continue;
        }

        var xLength = matrix[newYIndex].length;
        var newXIndex = i === 0 ? xIndex + delta : deltaIsNegative ? xLength - 1 : 0;

        for (var j = 0; j < xLength; j++) {
            if (matrix[newYIndex] && matrix[newYIndex][newXIndex]) {
                return { xIndex: newXIndex, yIndex: newYIndex };
            }

            if ((newXIndex >= xLength - 1 && !deltaIsNegative) || (newXIndex === 0 && deltaIsNegative)) {
                break;
            }
            newXIndex = newXIndex + delta;
        }

        newYIndex = modulo(newYIndex + delta, yLength);
    }

    return { xIndex: xIndex, yIndex: yIndex };
}

function findRoundTheWorldXIndex(xIndex, yIndex, delta, matrix) {
    var xLength = matrix[yIndex]?.length;
    var newXIndex = modulo(xIndex + delta, xLength);

    for (var i = 0; i < xLength - 1; i++) {
        if (matrix[yIndex][newXIndex]) {
            return { xIndex: newXIndex, yIndex: yIndex };
        }

        newXIndex = modulo(newXIndex + delta, xLength);
    }

    return { xIndex: xIndex, yIndex: yIndex };
}
function findNextXIndex(xIndex, yIndex, delta, matrix, mode) {
    switch (mode) {
        case 'bounded':
            return findBoundedXIndex(xIndex, yIndex, delta, matrix);
        case 'continuous':
            return findContinuousXIndex(xIndex, yIndex, delta, matrix);
        default:
            return findRoundTheWorldXIndex(xIndex, yIndex, delta, matrix);
    }
}

function findBoundedYIndex(xIndex, yIndex, delta, matrix) {
    var yLength = matrix.length;
    var newYIndex = yIndex + delta;

    var iterations = Math.sign(delta) === -1 ? newYIndex + 1 : yLength - newYIndex;
    for (var i = 0; i < iterations; i++) {
        if (matrix[newYIndex] && matrix[newYIndex][xIndex]) {
            return { xIndex: xIndex, yIndex: newYIndex };
        }

        newYIndex = newYIndex + delta;
    }

    return { xIndex: xIndex, yIndex: yIndex };
}

function getMaxXLength(matrix) {
    var xLength = 0;
    matrix.forEach(function (xArray) {
        if (xArray && xArray.length > xLength) {
            xLength = xArray.length;
        }
    });

    return xLength;
}

function findContinuousYIndex(xIndex, yIndex, delta, matrix) {
    var deltaIsNegative = Math.sign(delta) === -1;
    var xLength = getMaxXLength(matrix);
    var yLength = matrix.length;
    var newXIndex = xIndex;

    for (var i = 0; i < xLength + 1; i++) {
        var newYIndex = i === 0 ? yIndex + delta : deltaIsNegative ? yLength - 1 : 0;

        for (var j = 0; j < yLength; j++) {
            if (matrix[newYIndex] && matrix[newYIndex][newXIndex]) {
                return { xIndex: newXIndex, yIndex: newYIndex };
            }

            if ((newYIndex >= yLength - 1 && !deltaIsNegative) || (newYIndex === 0 && deltaIsNegative)) {
                break;
            }
            newYIndex = newYIndex + delta;
        }

        newXIndex = modulo(newXIndex + delta, xLength);
    }

    return { xIndex: xIndex, yIndex: yIndex };
}
function findRoundTheWorldYIndex(xIndex, yIndex, delta, matrix) {
    var yLength = matrix.length;
    var newYIndex = modulo(yIndex + delta, yLength);

    for (var i = 0; i < yLength - 1; i++) {
        if (matrix[newYIndex] && matrix[newYIndex][xIndex]) {
            return { xIndex: xIndex, yIndex: newYIndex };
        }

        newYIndex = modulo(newYIndex + delta, yLength);
    }

    return { xIndex: xIndex, yIndex: yIndex };
}

function findNextYIndex(xIndex, yIndex, delta, matrix, mode) {
    switch (mode) {
        case 'bounded':
            return findBoundedYIndex(xIndex, yIndex, delta, matrix);
        case 'continuous':
            return findContinuousYIndex(xIndex, yIndex, delta, matrix);
        default:
            return findRoundTheWorldYIndex(xIndex, yIndex, delta, matrix);
    }
}

var _extends =
    Object.assign ||
    function (target) {
        for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];

            for (var key in source) {
                if (Object.prototype.hasOwnProperty.call(source, key)) {
                    target[key] = source[key];
                }
            }
        }

        return target;
    };

var objectWithoutProperties = function (obj, keys) {
    var target = {};

    for (var i in obj) {
        if (keys.indexOf(i) >= 0) {
            continue;
        }
        if (!Object.prototype.hasOwnProperty.call(obj, i)) {
            continue;
        }
        target[i] = obj[i];
    }

    return target;
};

var slicedToArray = (function () {
    function sliceIterator(arr, i) {
        var _arr = [];
        var _n = true;
        var _d = false;
        var _e = undefined;

        try {
            for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {
                _arr.push(_s.value);

                if (i && _arr.length === i) {
                    break;
                }
            }
        } catch (err) {
            _d = true;
            _e = err;
        } finally {
            try {
                if (!_n && _i['return']) {
                    _i['return']();
                }
            } finally {
                if (_d) {
                    // eslint-disable-next-line no-unsafe-finally
                    throw _e;
                }
            }
        }

        return _arr;
    }

    return function (arr, i) {
        if (Array.isArray(arr)) {
            return arr;
        } else if (Symbol.iterator in Object(arr)) {
            return sliceIterator(arr, i);
        } else {
            throw new TypeError('Invalid attempt to destructure non-iterable instance');
        }
    };
})();

var toConsumableArray = function (arr) {
    if (Array.isArray(arr)) {
        for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
            arr2[i] = arr[i];
        }

        return arr2;
    } else {
        return Array.from(arr);
    }
};

function baseArrowNavigationreducer(state, _ref) {
    var type = _ref.type,
        payload = _ref.payload;
    var xIndex = state.xIndex,
        yIndex = state.yIndex,
        initialXIndex = state.initialXIndex,
        initialYIndex = state.initialYIndex,
        matrix = state.matrix,
        mode = state.mode;

    function initializeSelectedIndex(xDelta, yDelta) {
        if (!matrix.length) {
            return state;
        }

        if (xDelta === 1 || yDelta === 1) {
            if (matrix[0] && matrix[0][0]) {
                return _extends({}, state, {
                    xIndex: 0,
                    yIndex: 0,
                });
            }

            return xDelta === 1
                ? _extends({}, state, findNextXIndex(0, 0, 1, matrix, 'continuous'))
                : _extends({}, state, findNextYIndex(0, 0, 1, matrix, 'continuous'));
        }

        if (xDelta === -1) {
            return matrix[0] ? _extends({}, state, { xIndex: matrix[0].length - 1, yIndex: 0 }) : _extends({}, state, findNextXIndex(0, 0, -1, 'continuous'));
        }

        if (yDelta === -1) {
            return matrix[matrix.length - 1][0]
                ? _extends({}, state, { xIndex: 0, yIndex: matrix.length - 1 })
                : _extends({}, state, findNextYIndex(0, matrix.length - 1, -1, 'continuous'));
        }
    }

    switch (type) {
        case 'registerChild': {
            var x = payload.x,
                y = payload.y;

            if (!matrix[y]) {
                matrix[y] = [];
            }
            matrix[y][x] = true;

            return _extends({}, state, { matrix: matrix });
        }
        case 'deRegisterChild': {
            var _x = payload.x,
                _y = payload.y;

            // Remove the child from the matrix

            var row = matrix[_y];
            try {
                delete row[_x];

                // If we removed a child that was at the end of the row, remove all empty elements
                // up to the next child
                var newRow = [].concat(toConsumableArray(row.slice(0, row.lastIndexOf(true) + 1)));

                newRow.length ? (matrix[_y] = newRow) : delete matrix[_y];
            } catch {
                //Do not need to handle
                console.warn('Failed to reinit arrow navigation.');
            }

            // Reset the current index if it pointed to this child
            var indicies = _x === xIndex && _y === yIndex && { xIndex: initialXIndex, yIndex: initialYIndex };

            return _extends({}, state, indicies, { matrix: matrix });
        }
        case 'updateXIndex':
            return xIndex === undefined && yIndex === undefined
                ? initializeSelectedIndex(payload.delta, undefined)
                : _extends({}, state, findNextXIndex(xIndex, yIndex, payload.delta, matrix, mode));
        case 'updateYIndex':
            return xIndex === undefined && yIndex === undefined
                ? initializeSelectedIndex(undefined, payload.delta)
                : _extends({}, state, findNextYIndex(xIndex, yIndex, payload.delta, matrix, mode));
        case 'resetIndexes':
            return _extends({}, state, { xIndex: initialXIndex, yIndex: initialYIndex });
        case 'setIndexes': {
            var _x2 = payload.x,
                _y2 = payload.y;

            return _extends({}, state, { xIndex: _x2, yIndex: _y2 });
        }
        default:
            throw new Error('Action type ' + type + ' does not exist');
    }
}

function arrowNavigationReducer(state, _ref2) {
    var type = _ref2.type,
        payload = _ref2.payload;

    switch (type) {
        case 'activate':
            return _extends({}, state, { active: true });
        case 'deactivate':
            return _extends({}, state, {
                active: false,
                childTabIndex: state.reInitOnDeactivate ? undefined : state.childTabIndex,
            });
        case 'setChildTabIndex': {
            var x = payload.x,
                y = payload.y;

            return _extends({}, state, { childTabIndex: { x: x, y: y } });
        }
        case 'unsetChildTabIndex': {
            var _x3 = payload.x,
                _y3 = payload.y;

            var _ref3 = state.childTabIndex || {},
                currentX = _ref3.x,
                currentY = _ref3.y;

            return _extends({}, state, {
                childTabIndex: _x3 === currentX && _y3 === currentY ? undefined : state.childTabIndex,
            });
        }
        default:
            throw new Error('Action type ' + type + ' does not exist');
    }
}

var BaseArrowNavigationContext = createContext({
    xIndex: undefined,
    yIndex: undefined,
    registerChild: undefined,
});

function BaseArrowNavigation(_ref) {
    var children = _ref.children,
        _ref$initialIndex = _ref.initialIndex,
        initialIndex = _ref$initialIndex === undefined ? [undefined, undefined] : _ref$initialIndex,
        _ref$mode = _ref.mode,
        mode = _ref$mode === undefined ? 'roundTheWorld' : _ref$mode,
        _ref$active = _ref.active,
        active = _ref$active === undefined ? true : _ref$active,
        _ref$reInitOnDeactiva = _ref.reInitOnDeactivate,
        reInitOnDeactivate = _ref$reInitOnDeactiva === undefined ? false : _ref$reInitOnDeactiva;

    var initialXIndex = initialIndex[0];
    var initialYIndex = initialIndex[1];

    var _useReducer = useReducer(baseArrowNavigationreducer, {
            xIndex: initialXIndex,
            yIndex: initialYIndex,
            initialXIndex: initialXIndex,
            initialYIndex: initialYIndex,
            matrix: [],
            mode: mode,
        }),
        _useReducer2 = slicedToArray(_useReducer, 2),
        _useReducer2$ = _useReducer2[0],
        xIndex = _useReducer2$.xIndex,
        yIndex = _useReducer2$.yIndex,
        dispatch = _useReducer2[1];

    useEffect(
        function () {
            if (reInitOnDeactivate && !active) {
                dispatch({ type: 'resetIndexes' });
            }
        },
        [active, dispatch, reInitOnDeactivate, initialXIndex, initialYIndex],
    );

    useKey('ArrowLeft', function () {
        return active && dispatch({ type: 'updateXIndex', payload: { delta: -1 } });
    });
    useKey('ArrowRight', function () {
        return active && dispatch({ type: 'updateXIndex', payload: { delta: 1 } });
    });
    useKey('ArrowUp', function () {
        return active && dispatch({ type: 'updateYIndex', payload: { delta: -1 } });
    });
    useKey('ArrowDown', function () {
        return active && dispatch({ type: 'updateYIndex', payload: { delta: 1 } });
    });

    var contextValue = { xIndex: xIndex, yIndex: yIndex, active: active, dispatch: dispatch };
    return React.createElement(BaseArrowNavigationContext.Provider, { value: contextValue }, children);
}

var ArrowNavigationContext = createContext({ dispatch: undefined });

var ArrowNavigation = function (props, providedRef) {
    var children = props.children,
        _ref$initialIndex = props.initialIndex,
        initialIndex = _ref$initialIndex === undefined ? [undefined, undefined] : _ref$initialIndex,
        _ref$mode = props.mode,
        mode = _ref$mode === undefined ? 'roundTheWorld' : _ref$mode,
        _ref$reInitOnDeactiva = props.reInitOnDeactivate,
        reInitOnDeactivate = _ref$reInitOnDeactiva === undefined ? false : _ref$reInitOnDeactiva,
        divProps = objectWithoutProperties(props, ['children', 'initialIndex', 'mode', 'reInitOnDeactivate']);

    var _useReducer = useReducer(arrowNavigationReducer, {
            active: false,
            childTabIndex: undefined,
            reInitOnDeactivate: reInitOnDeactivate,
        }),
        _useReducer2 = slicedToArray(_useReducer, 2),
        _useReducer2$ = _useReducer2[0],
        active = _useReducer2$.active,
        childTabIndex = _useReducer2$.childTabIndex,
        dispatch = _useReducer2[1];

    var containerRef = useRef();
    var ref = providedRef || containerRef;

    useKey('Escape', function () {
        childTabIndex ? dispatch({ type: 'deactivate' }) : ref.current && ref.current.blur();
    });

    return React.createElement(
        'div',
        _extends({}, divProps, {
            tabIndex: childTabIndex ? undefined : 0,
            ref: ref,
            onFocus: function onFocus() {
                return dispatch({ type: 'activate' });
            },
            onBlur: function onBlur(_ref2) {
                var relatedTarget = _ref2.relatedTarget;

                if (ref.current && ((relatedTarget && !ref.current.contains(relatedTarget)) || !relatedTarget)) {
                    dispatch({ type: 'deactivate' });
                }
            },
            style: {
                margin: '2px',
                outlineColor: props.focuscolor,
                ...divProps.style,
            },
        }),
        React.createElement(
            ArrowNavigationContext.Provider,
            { value: dispatch },
            React.createElement(
                BaseArrowNavigation,
                {
                    active: active,
                    initialIndex: initialIndex,
                    mode: mode,
                    reInitOnDeactivate: reInitOnDeactivate,
                    focuscolor: props.focuscolor,
                },
                children,
            ),
        ),
    );
};

function useArrowNavigation(x) {
    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;

    var _useContext = useContext(BaseArrowNavigationContext),
        xIndex = _useContext.xIndex,
        yIndex = _useContext.yIndex,
        active = _useContext.active,
        dispatch = _useContext.dispatch;

    useEffect(
        function () {
            dispatch({ type: 'registerChild', payload: { x: x, y: y } });

            return function () {
                dispatch({ type: 'deRegisterChild', payload: { x: x, y: y } });
            };
        },
        [x, y, dispatch],
    );

    var selected = x === xIndex && y === yIndex;
    var memoizedSelect = useCallback(
        function () {
            dispatch({ type: 'setIndexes', payload: { x: x, y: y } });
        },
        [dispatch, x, y],
    );

    return {
        selected: selected,
        active: active,
        select: memoizedSelect,
    };
}

function useArrowNavigationWithFocusState(x, y) {
    var _useArrowNavigation = useArrowNavigation(x, y),
        selected = _useArrowNavigation.selected,
        active = _useArrowNavigation.active,
        select = _useArrowNavigation.select;

    var dispatch = useContext(ArrowNavigationContext);
    var ref = useRef();

    useEffect(
        function () {
            selected ? dispatch({ type: 'setChildTabIndex', payload: { x: x, y: y } }) : dispatch({ type: 'unsetChildTabIndex', payload: { x: x, y: y } });

            if (selected && active && document.activeElement !== ref.current) {
                ref.current && ref.current.focus();
            }

            if (!(selected && active) && document.activeElement === ref.current) {
                ref.current && ref.current.blur();
            }

            return function () {
                dispatch({ type: 'unsetChildTabIndex', payload: { x: x, y: y } });
            };
        },
        [x, y, selected, active, dispatch],
    );

    var focusProps = {
        ref: ref,
        tabIndex: selected ? 0 : -1,
        onClick: select,
    };

    const baseArrowNavigationContext = useContext(BaseArrowNavigationContext);
    const setFocusFromOutside = (newX, newY) => {
        baseArrowNavigationContext.dispatch({ type: 'setIndexes', payload: { x: newX, y: newY } });
    };

    return { selected: selected, active: active, focusProps: focusProps, setFocusFromOutside };
}

export { ArrowNavigation, ArrowNavigationContext, BaseArrowNavigation, BaseArrowNavigationContext, useArrowNavigation, useArrowNavigationWithFocusState };
//# sourceMappingURL=index.es.js.map
