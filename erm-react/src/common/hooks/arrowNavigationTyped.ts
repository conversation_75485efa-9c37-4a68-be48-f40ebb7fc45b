import { ArrowNavigation as ArrowNavigationFn, useArrowNavigationWithFocusState as useArrowNavHook } from './arrowNavigation';
import { CSSProperties, forwardRef, PropsWithChildren } from 'react';

type PropsType = PropsWithChildren<{
    initialIndex: [number, number];
    reInitOnDeactivate: boolean;
    mode?: any;
    focuscolor?: any;
    style?: CSSProperties;
}>;

const useArrowNavigationWithFocusState: (
    x: number,
    y: number,
) => {
    selected: any;
    active: any;
    focusProps: any;
    setFocusFromOutside: (x: number, y: number) => void;
} = useArrowNavHook;

const ArrowNavigation = forwardRef<any, PropsType>(ArrowNavigationFn);

export { ArrowNavigation, useArrowNavigationWithFocusState };
