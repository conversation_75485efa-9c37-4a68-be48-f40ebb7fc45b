import { useParams } from 'react-router';

export const useNumericParam = (paramName: string, defaultValue?: number): number | null => {
    const params = useParams<{ [key: string]: string | undefined }>();

    const param = params[paramName];
    if (param === undefined) {
        return defaultValue !== undefined ? defaultValue : null;
    }

    const numericParam = Number(param);
    return isNaN(numericParam) ? (defaultValue !== undefined ? defaultValue : null) : numericParam;
};
