export enum SearchErrorType {
    INVALID = 'invalidSearchResult',
}

export type SearchError = {
    type: SearchErrorType;
    message: string;
};

export type SearchErrors = Record<string, SearchError>;

export type ValidationContext = {
    searchErrors: SearchErrors;
};

export type SetSearchError = (fieldName: string, message?: string) => void;
export type ClearSearchError = (fieldName: string) => void;
