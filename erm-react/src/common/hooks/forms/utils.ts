import _ from 'lodash';
import * as Yup from 'yup';
import { SearchErrors } from './types';

export const mapToNestedErrors = (errors?: Yup.ValidationError[]) => {
    if (!errors) {
        return {};
    }

    return errors.reduce((allErrors, currentError) => {
        const finalErrors = { ...allErrors };

        if (currentError.path === undefined) {
            return finalErrors;
        }

        // split keys for nested values: arrays and objects
        const itemPathNames = currentError.path
            .replace(/\[|\]/g, '.')
            .split('.')
            .filter((val: string) => !!val);

        if (itemPathNames.length > 1) {
            // create nested object structure for nested errors
            const newErrors = itemPathNames.reduceRight((obj: any, errorKey: string) => ({ [errorKey]: obj }), {
                type: currentError.type ?? 'validation',
                message: currentError.message,
            });

            // merge with other errors, keep nesting of the errors intact
            return _.merge(finalErrors, newErrors);
        } else {
            return {
                ...allErrors,
                [currentError.path]: {
                    ...finalErrors[currentError.path],
                    type: currentError.type ?? 'validation',
                    message: currentError.message,
                },
            };
        }
    }, {});
};

export const createSchemaSearchErrors = (errors?: SearchErrors): Yup.ValidationError[] => {
    if (!errors) {
        return [];
    }

    return Object.entries(errors).map(([key, value]) => {
        const error = {
            path: key,
            type: value.type,
            message: value.message,
        } as Yup.ValidationError;

        return error;
    });
};
