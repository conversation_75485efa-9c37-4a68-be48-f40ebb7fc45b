import {
    UseFormReturn as UseFormReturnRHF,
    UseFormProps as UseFormPropsRHF,
    useForm as useFormRHF,
    FieldValues as FieldValuesRHF,
    Path as PathRHF,
} from 'react-hook-form';
import * as Yup from 'yup';
import useFormValidationResolver from 'common/hooks/forms/useFormValidationResolver';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import _ from 'lodash';
import { SetSearchError, ClearSearchError, ValidationContext, SearchErrorType } from './types';
import { strings } from 'common/utils/i18n';

type UseFormProps<TFieldValues extends FieldValuesRHF = FieldValuesRHF, TContext = any> = Omit<UseFormPropsRHF<TFieldValues, TContext>, 'resolver'> & {
    schema?: Yup.AnySchema;
    initialRequiredCheck?: boolean;
};

export type UseFormReturn<TFieldValues extends FieldValuesRHF = FieldValuesRHF, TContext = any> = UseFormReturnRHF<TFieldValues, TContext> & {
    setSearchError: SetSearchError;
    clearSearchError: ClearSearchError;
};

const useForm = <TFieldValues extends FieldValuesRHF = FieldValuesRHF, TContext = any>(
    props: UseFormProps<TFieldValues, TContext>,
): UseFormReturn<TFieldValues, TContext> => {
    const { schema, context, initialRequiredCheck = true, ...restProps } = props;
    const validationContext = useRef<ValidationContext>({
        searchErrors: {},
    });

    const setSearchError: SetSearchError = useCallback((fieldName, message) => {
        validationContext.current.searchErrors[fieldName] = {
            type: SearchErrorType.INVALID,
            message: message || strings('common:validators.noSelections'),
        };
    }, []);

    const clearSearchError: ClearSearchError = useCallback((fieldName) => {
        delete validationContext.current.searchErrors[fieldName];
    }, []);

    const resolver = useFormValidationResolver(schema, validationContext);

    const methods = useFormRHF<TFieldValues, TContext>({
        resolver,
        context,
        ...restProps,
    });

    const { formState, ...restFormMethods } = methods;
    const { isLoading, isSubmitting, defaultValues, errors } = formState;
    const { getValues, setError, trigger } = restFormMethods;

    const errorsRef = useRef({});
    useEffect(() => {
        errorsRef.current = errors;
    }, [formState, errors]);

    useEffect(() => {
        const validate = async () => {
            const data = getValues();
            try {
                await schema?.validate(data, { abortEarly: false, context });
            } catch (validationError: any) {
                validationError?.inner
                    ?.filter((error) => error.type === 'required')
                    .forEach((error) => {
                        // update path e.g. from user[0].address.city to user.0.address.city
                        const itemPathName = _.toPath(error.path).join('.') as PathRHF<TFieldValues>;
                        // if the field is not ignored and error does not yet exist in the formState.errors
                        // we set a new error, otherwise skip setting the same error
                        const requiredErrorExists = _.get(errorsRef.current, error.path)?.type === 'required';
                        if (!requiredErrorExists) {
                            setError(itemPathName, { type: 'required', message: error.message });
                        }
                    });
            }
        };

        if (initialRequiredCheck && schema && typeof defaultValues !== 'undefined' && !isLoading && !isSubmitting) {
            void validate();
        }
    }, [getValues, setError, schema, isLoading, isSubmitting, defaultValues, context, initialRequiredCheck]);

    /**
     * When new schema is dynamically created e.g. after evaluation of conditional rules,
     * check if previous errors still apply by triggering validation on fields with errors
     */
    useEffect(() => {
        const errorFieldNames = Object.keys(errorsRef.current);
        void trigger(errorFieldNames as PathRHF<TFieldValues>[]);
    }, [schema, trigger]);

    const memoizedReturn = useMemo(() => {
        return {
            ...restFormMethods,
            formState,
            setSearchError,
            clearSearchError,
        };
    }, [restFormMethods, formState, setSearchError, clearSearchError]);

    return memoizedReturn;
};

export default useForm;
