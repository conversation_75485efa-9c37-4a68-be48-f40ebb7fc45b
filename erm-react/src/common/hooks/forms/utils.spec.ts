import * as Yup from 'yup';
import { mapToNestedErrors, createSchemaSearchErrors } from './utils';
import { SearchErrors, SearchErrorType } from './types';

const createValidationError = (path: string | undefined, message: string, type?: string): Yup.ValidationError => {
    const error = new Yup.ValidationError(message, null, path);
    if (type) {
        error.type = type;
    }
    return error;
};

describe('mapToNestedErrors', () => {
    it('should return an empty object for an empty array of errors', () => {
        const errors: Yup.ValidationError[] = [];
        expect(mapToNestedErrors(errors)).toEqual({});
    });

    it('should return an empty object if errors array is undefined', () => {
        expect(mapToNestedErrors(undefined)).toEqual({});
    });

    it('should map simple validation errors', () => {
        const errors = [createValidationError('name', 'Name is required', 'required'), createValidationError('email', 'Invalid email format', 'format')];
        const expected = {
            name: { type: 'required', message: 'Name is required' },
            email: { type: 'format', message: 'Invalid email format' },
        };
        expect(mapToNestedErrors(errors)).toEqual(expected);
    });

    it('should map nested validation errors using array syntax', () => {
        const errors = [
            createValidationError('users[0].name', 'User name is required'),
            createValidationError('users[1].email', 'User email is invalid', 'email'),
        ];
        const expected = {
            users: {
                '0': { name: { type: 'validation', message: 'User name is required' } },
                '1': { email: { type: 'email', message: 'User email is invalid' } },
            },
        };
        expect(mapToNestedErrors(errors)).toEqual(expected);
    });

    it('should map nested validation errors using dot syntax', () => {
        const errors = [
            createValidationError('address.street', 'Street is required'),
            createValidationError('address.city.name', 'City name is too short', 'min'),
        ];
        const expected = {
            address: {
                street: { type: 'validation', message: 'Street is required' },
                city: { name: { type: 'min', message: 'City name is too short' } },
            },
        };
        expect(mapToNestedErrors(errors)).toEqual(expected);
    });

    it('should merge deeply nested errors correctly', () => {
        const errors = [
            createValidationError('profile.contact.emails[0].value', 'Primary email is required'),
            createValidationError('profile.contact.phones[0].number', 'Phone number is invalid'),
            createValidationError('profile.address.street', 'Street cannot be empty'),
        ];
        const expected = {
            profile: {
                contact: {
                    emails: {
                        '0': { value: { type: 'validation', message: 'Primary email is required' } },
                    },
                    phones: {
                        '0': { number: { type: 'validation', message: 'Phone number is invalid' } },
                    },
                },
                address: {
                    street: { type: 'validation', message: 'Street cannot be empty' },
                },
            },
        };
        expect(mapToNestedErrors(errors)).toEqual(expected);
    });

    it('should handle mixed simple and nested errors', () => {
        const errors = [
            createValidationError('globalSetting', 'Setting is invalid'),
            createValidationError('items[0].value', 'Item value must be positive', 'positive'),
        ];
        const expected = {
            globalSetting: { type: 'validation', message: 'Setting is invalid' },
            items: {
                '0': { value: { type: 'positive', message: 'Item value must be positive' } },
            },
        };
        expect(mapToNestedErrors(errors)).toEqual(expected);
    });

    it('should ignore errors with undefined path', () => {
        const errors = [createValidationError(undefined, 'Generic error'), createValidationError('name', 'Name is required')];
        const expected = {
            name: { type: 'validation', message: 'Name is required' },
        };
        expect(mapToNestedErrors(errors)).toEqual(expected);
    });
});

describe('createSchemaSearchErrors', () => {
    it('should return an empty array if errors object is undefined', () => {
        expect(createSchemaSearchErrors(undefined)).toEqual([]);
    });

    it('should return an empty array for an empty errors object', () => {
        const errors: SearchErrors = {};
        expect(createSchemaSearchErrors(errors)).toEqual([]);
    });

    it('should convert SearchErrors object to an array of Yup.ValidationError', () => {
        const errors: SearchErrors = {
            query: { type: SearchErrorType.INVALID, message: 'Invalid query search result' },
            filter: { type: SearchErrorType.INVALID, message: 'Invalid filter search result' },
        };
        const expected = [
            expect.objectContaining({ path: 'query', type: SearchErrorType.INVALID, message: 'Invalid query search result' }),
            expect.objectContaining({ path: 'filter', type: SearchErrorType.INVALID, message: 'Invalid filter search result' }),
        ];

        const result = createSchemaSearchErrors(errors);
        expect(result).toHaveLength(2);
        expect(result).toEqual(expect.arrayContaining(expected));

        result.forEach((err) => {
            expect(err).toHaveProperty('path');
            expect(err).toHaveProperty('message');
            expect(err).toHaveProperty('type');
        });
    });
});
