import { useFormContext as useFormContextRHF, FieldValues as FieldValuesRHF } from 'react-hook-form';
import { UseFormReturn } from './useForm';

const useFormContext = <TFieldValues extends FieldValuesRHF = FieldValuesRHF, TContext = any>() => {
    const context = useFormContextRHF<TFieldValues, TContext>();
    return context as UseFormReturn<TFieldValues, TContext>;
};

export default useFormContext;
