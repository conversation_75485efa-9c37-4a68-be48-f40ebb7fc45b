import React from 'react';
import { useCallback } from 'react';
import _ from 'lodash';
import * as Yup from 'yup';
import { createSchemaSearchErrors, mapToNestedErrors } from './utils';
import { ValidationContext } from './types';

const useFormValidationResolver = (validationSchema?: Yup.AnySchema, validationContextRef?: React.MutableRefObject<ValidationContext>) =>
    useCallback(
        /**
         * Custom validation resolver
         *
         * when fieldNotInForm is defined in the context, it can be used in the validation schema as follows:
         * field: Yup.string()
         .when('$fieldNotInForm', {
                is: (fieldNotInFormValue) => fieldNotInFormValue === 'test',
                then: (schema) => schema.optional(),
                otherwise: (schema) => schema.required()
            })
         *
         * @param data to be validated
         * @param context specifies external parameters used in validation schema
         * @returns validated values with errors
         */
        async (data, context) => {
            const searchSchemaErrors = createSchemaSearchErrors(validationContextRef?.current.searchErrors);
            const searchErrors = mapToNestedErrors(searchSchemaErrors);

            try {
                const values = await validationSchema?.validate(data, {
                    context,
                    abortEarly: false,
                });

                return {
                    values: _.isEmpty(searchErrors) ? values : {},
                    errors: { ...searchErrors },
                };
            } catch (errors: any) {
                const formErrors = mapToNestedErrors(errors?.inner);
                const allErrors = _.merge(formErrors, searchErrors);

                return {
                    values: _.isEmpty(allErrors) ? data : {},
                    errors: allErrors,
                };
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [validationSchema],
    );

export default useFormValidationResolver;
