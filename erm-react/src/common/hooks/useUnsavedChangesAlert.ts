import { useEffect, useRef } from 'react';
import { useBlocker } from 'react-router';
import { useConfirmationAlert } from 'context/ConfirmationAlertProvider/useConfirmationAlert';
import { AlertType, DialogType } from 'common/types';
import { strings } from 'common/utils/i18n';

type Props = {
    blockNavigation?: boolean;
    contentText?: string;
    onConfirm?: () => void;
    onClose?: () => void;
    onBack?: () => void;
    type?: AlertType;
    title?: string;
    dialogType: DialogType;
    confirmButtonLabel?: string;
};

/**
 * Hook used to intercept router navigation and block it when blockNavigation condition is met with confirmation dialog.
 * One important note: using only isDirty from React Hook Form as condition is not enough when user has to be redirected after save, because it is not reset after submitting form automatically
 * isDirty has to be used in combination with !isSubmitted (isDirty && !isSubmitted) or dirtyValues have to be reset()/setIsDirty() manually
 */
export const useUnsavedChangesAlert = ({
    blockNavigation = false,
    onConfirm,
    onClose,
    contentText,
    type,
    onBack,
    title,
    dialogType,
    confirmButtonLabel,
}: Props) => {
    const alertShown = useRef(false);
    const blockerRef = useRef<any>(null);

    const blocker = useBlocker(blockNavigation);
    blockerRef.current = blocker;

    const { showConfirmationAlert } = useConfirmationAlert();

    useEffect(() => {
        // Reset the flag when blockNavigation changes
        if (blockNavigation) {
            alertShown.current = false;
        }
    }, [blockNavigation]);

    useEffect(() => {
        if (blocker.state === 'blocked' && !alertShown.current) {
            alertShown.current = true;

            showConfirmationAlert({
                onConfirm: () => {
                    onConfirm?.();
                    blockerRef.current?.proceed?.();
                },
                onClose: () => {
                    onClose?.();
                    blockerRef.current?.reset?.();
                    alertShown.current = false;
                },
                onBack: () => {
                    onBack?.();
                    blockerRef.current?.proceed?.();
                },
                isCancelPrimary: dialogType === DialogType.DISCARD,
                contentText:
                    contentText || (dialogType === DialogType.DISCARD ? strings('common:message.discardChanges') : strings('common:message.unsavedChanges')),
                type: type || (dialogType === DialogType.DISCARD ? AlertType.Error : AlertType.Warning),
                title: title || (dialogType === DialogType.DISCARD ? strings('common:title.discardChanges') : strings('common:title.saveChanges')),
                dialogType,
                cancelButtonLabel: strings('common:button.cancel'),
                confirmButtonLabel:
                    confirmButtonLabel || (dialogType === DialogType.DISCARD ? strings('common:button.discard') : strings('common:button.save')),
            });
        }
    }, [blocker, contentText, onClose, onConfirm, showConfirmationAlert, confirmButtonLabel, dialogType, onBack, title, type, blockNavigation]);

    useEffect(() => {
        const handleUnload = (event: BeforeUnloadEvent) => {
            event.returnValue = true;
        };
        if (blockNavigation) {
            window.addEventListener('beforeunload', handleUnload);
        }
        return () => {
            window.removeEventListener('beforeunload', handleUnload);
        };
    }, [blockNavigation]);

    useEffect(() => {
        if (window.GwtBridge) {
            window.GwtBridge.reactIsDirty = () => blockNavigation;
        }
        return () => {
            if (window.GwtBridge) {
                window.GwtBridge.reactIsDirty = () => false;
            }
        };
    }, [blockNavigation]);

    return { showConfirmationAlert };
};
