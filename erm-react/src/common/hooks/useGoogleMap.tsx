import { RefObject, useEffect, useState } from 'react';

export type MapControls = { controlElement: HTMLElement; controlPosition: google.maps.ControlPosition };

const useGoogleMap = (
    mapContainerRef: RefObject<HTMLDivElement>,
    options: google.maps.MapOptions,
    markerPosition?: google.maps.LatLngLiteral,
    mapControls?: any[] | undefined,
) => {
    const [map, setMap] = useState<google.maps.Map | null>(null);
    const [marker, setMarker] = useState<google.maps.Marker | null>(null);

    useEffect(() => {
        if (map === null && mapContainerRef.current && typeof window?.google?.maps === 'object') {
            const map = new google.maps.Map(mapContainerRef.current, options);
            const marker = new google.maps.Marker({
                map: map,
                position: markerPosition,
            });

            setMap(map);
            setMarker(marker);
        }
    }, [map, mapContainerRef, markerPosition, options]);

    useEffect(() => {
        if (map && mapControls && mapControls.length > 0) {
            mapControls.forEach((control) => {
                map.controls[control.controlPosition].push(control.controlElement);
            });
        }

        return () => {
            if (map && mapControls && mapControls.length > 0) {
                mapControls.forEach((control) => {
                    map.controls[control.controlPosition].clear();
                });
            }
        };
    }, [mapControls, map]);

    return { map, marker };
};

export default useGoogleMap;
