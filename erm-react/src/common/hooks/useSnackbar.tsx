import { OptionsObject, ProviderContext, Snackbar<PERSON>ey, SnackbarMessage } from 'notistack';
import { useSnackbar } from 'context/SnackbarProvider';
import { useCallback } from 'react';

export interface SnackbarContext extends ProviderContext {
    enqueueInfo: (message: SnackbarMessage, options?: OptionsObject) => SnackbarKey;
    enqueueSuccess: (message: SnackbarMessage, options?: OptionsObject) => SnackbarKey;
    enqueueWarning: (message: SnackbarMessage, options?: OptionsObject) => SnackbarKey;
    enqueueError: (message: SnackbarMessage, error?: string, options?: OptionsObject) => SnackbarKey;
}

export default (): SnackbarContext => {
    const context = useSnackbar();

    const enqueueInfo = useCallback(
        (message: SnackbarMessage, options?: OptionsObject): SnackbarKey => {
            return context.enqueueSnackbar(message, { ...options, variant: 'info' });
        },
        [context],
    );

    const enqueueSuccess = useCallback(
        (message: SnackbarMessage, options?: OptionsObject): SnackbarKey => {
            return context.enqueueSnackbar(message, { ...options, variant: 'success' });
        },
        [context],
    );

    const enqueueWarning = useCallback(
        (message: SnackbarMessage, options?: OptionsObject): SnackbarKey => {
            return context.enqueueSnackbar(message, { ...options, variant: 'warning' });
        },
        [context],
    );

    const enqueueError = useCallback(
        (message: SnackbarMessage, error?: string, options?: OptionsObject): SnackbarKey => {
            // log error
            return context.enqueueSnackbar(message, { ...options, variant: 'error' });
        },
        [context],
    );

    return { ...context, enqueueInfo, enqueueSuccess, enqueueWarning, enqueueError };
};
