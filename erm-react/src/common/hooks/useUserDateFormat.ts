import { getDateFormat } from 'common/utils/date';
import { useMemo } from 'react';
import { usePursGetCurrentUserUsingGetQuery } from 'user/rtkApi';

const useUserDateFormat = (): string => {
    const { data: currentUser } = usePursGetCurrentUserUsingGetQuery();
    const { dateFormatPattern } = currentUser ?? {};

    const dateFormat = useMemo(() => getDateFormat(dateFormatPattern), [dateFormatPattern]);

    return dateFormat;
};

export default useUserDateFormat;
