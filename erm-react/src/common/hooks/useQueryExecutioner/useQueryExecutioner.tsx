import { useCallback, useEffect, useRef, useState } from 'react';
import { ApiError, PagingResult } from 'common/api/types';
import { ViewExpression } from 'view/types';
import useAsync, { AsyncStatus } from '../useAsync/useAsync';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';

type LoadQuery<T> = () => Promise<T[] | PagingResult<T> | undefined>;

type QueryExecutionerResponse<T> = {
    loadedItems: T[] | undefined;
    totalCount: number;
    isLoading: boolean;
    error: ApiError | undefined;
    refresh: () => void;
};

export type AbortableLoadingQuery<T> = (
    abortController: AbortController | undefined,
    params?: SearchRequestParams,
    expressions?: ViewExpression[],
) => Promise<PagingResult<T>>;

/**
 * Similarly to useInfiniteScrollLoading, this hook takes in a query and outputs new results whenever query or any of it's useCallback deps change.
 * This hook is mainly used for loading data for tables, page/offset and limit query params are handled by Table component, and are part of
 * useCallback query deps. When deps change, loader helper values are reset and new results are presented to the user.
 *
 * Example usage:
 *
 * // offset and limit is updated inside queryParams in parent component
 * const loadFunction: AbortableLoadingQuery<Response> = useCallback(
        async (abortController: AbortController): Promise<PagingResult<BowTieDiagram>> => {
                const response = await yourRequest(queryParams, searchField, searchValue, otherParams, abortController);
                return { totalCount: results.totalCount, records: response.records };
        },
        // when these change, results are automatically loaded and refreshed inside useQueryExecutioner
        [queryParams, searchField,searchValue, otherParams],
    );

    // use abortable query if needed, wait until queryParams are defined if needed
    const abortableLoadingFunction = useAbortableQuery<PagingResult<Response>>(queryParams ? loadFunction : undefined);

    // results are stored in loadedItems
    // use refresh for refreshing the Table/component after some user interactions (updates of item, delete of item, etc)
    const { loadedItems, totalCount, error, isLoading, refresh } = useQueryExecutioner<IdOnly>(abortableLoadingFunction);
 *
 * @param query
 * @returns
 */
const useQueryExecutioner = <T,>(query: LoadQuery<T> | undefined): QueryExecutionerResponse<T> => {
    const [loadedItems, setLoadedItems] = useState<T[] | undefined>(undefined);
    const [totalCount, setTotalCount] = useState(0);

    // query changes when new endpoint is used (page is changed from SideMenu),
    // or when queryParams, expressions or other query useCallback deps change
    const queryRef = useRef(query);

    const { isLoading, error, response, asyncLoad } = useAsync<T[] | PagingResult<T> | undefined>({ status: AsyncStatus.PENDING });

    // loader function triggered by change of query or queryParams (mainly current offset inside queryParams, sorting, view expressions...)
    const triggerLoad = useCallback(() => {
        if (queryRef.current) {
            void asyncLoad(queryRef.current());
        }
    }, [asyncLoad]);

    const refresh = useCallback(() => {
        setLoadedItems(undefined);
        triggerLoad();
    }, [triggerLoad]);

    // when query changes, new results are expected to be loaded from page 0 and loader helper values are being reset
    // offset has to be updated to 0 in parent component
    useEffect(() => {
        queryRef.current = query;
        refresh();
    }, [query, refresh]);

    useEffect(() => {
        if (response) {
            const pagedResponse = response as PagingResult<T>;
            const pagedResults = pagedResponse.records || [];
            setLoadedItems(pagedResults);
            setTotalCount(pagedResponse.totalCount);
        }
    }, [response]);

    useEffect(() => {
        if (error) {
            setTotalCount(-1);
            setLoadedItems(undefined);
        }
    }, [error]);

    return {
        loadedItems,
        totalCount,
        isLoading,
        error,
        refresh,
    };
};

export default useQueryExecutioner;
