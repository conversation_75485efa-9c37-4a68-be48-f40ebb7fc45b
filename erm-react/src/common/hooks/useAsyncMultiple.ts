import { Reducer, useCallback, useMemo, useReducer, useRef } from 'react';
import useSafeDispatch from './useSafeDispatch';
import { AsyncState, AsyncStatus, UseAsyncReturnType } from './useAsync/useAsync';
import { isCancel } from 'common/api/utils/api';
import { setGlobalError } from '../../app/reducer';
import { useDispatch } from '../../store';

type AsyncStateMultiple<T> = Record<string, Partial<AsyncState<T>>>;

enum ActionType {
    SET_ALL_PENDING = 'SET_ALL_PENDING',
    SET_RESPONSE = 'SET_RESPONSE',
    SET_ERROR = 'SET_ERROR',
    RESET = 'RESET',
}

type UseAsyncReturnTypeMultiple<T> = Pick<UseAsyncReturnType<T>, 'reset'> & {
    state: AsyncStateMultiple<T>;
    asyncLoad: (promiseMap: Record<string, Promise<T>>) => Promise<T | unknown>;
    isSuccess: boolean;
    isLoading: boolean;
};

type ActionTypeMultiple<T> = { type: ActionType; payload?: { key: string; value: Partial<AsyncState<T>> } };

const asyncReducer = <T>(state: AsyncStateMultiple<T>, action: ActionTypeMultiple<T>): AsyncStateMultiple<T> => {
    const { type, payload } = action;

    if (!payload) {
        return {};
    }
    switch (type) {
        case ActionType.SET_RESPONSE:
            return {
                ...state,
                [payload.key]: {
                    response: payload.value.response,
                    status: AsyncStatus.RESOLVED,
                },
            };
        case ActionType.SET_ALL_PENDING:
            return {
                ...state,
                [payload.key]: {
                    status: payload.value.status,
                },
            };
        case ActionType.SET_ERROR:
            return {
                ...state,
                [payload.key]: {
                    error: payload.value.error,
                    status: AsyncStatus.REJECTED,
                },
            };
        default:
            return state;
    }
};

const defaultInitialState = {};

const useAsyncMultiple = <T>(disableErrorHandling?: boolean): UseAsyncReturnTypeMultiple<T> => {
    const initialStateRef = useRef<AsyncStateMultiple<T>>({
        ...defaultInitialState,
    });

    const [state, setState] = useReducer<Reducer<AsyncStateMultiple<T>, ActionTypeMultiple<T>>>(asyncReducer, initialStateRef.current);

    const dispatch = useDispatch();

    const safeSetState = useSafeDispatch<ActionTypeMultiple<T>>(setState);

    const asyncLoad = useCallback(
        async (promiseMap: Record<string, Promise<T>>) => {
            const keys = Object.keys(promiseMap);
            const promises = Object.values(promiseMap);
            if (!promises.length || !promises.every((promise) => promise.then)) {
                throw new Error('The argument passed to useAsync().asyncLoad must be a promise.');
            }

            return Promise.allSettled(
                keys.map(async (key) => {
                    safeSetState({ type: ActionType.SET_ALL_PENDING, payload: { key, value: { status: AsyncStatus.PENDING } } });
                    const promise = promiseMap[key];
                    return promise
                        .then((response?: T) => {
                            safeSetState({ type: ActionType.SET_RESPONSE, payload: { key, value: { response } } });
                        })
                        .catch((error) => {
                            safeSetState({ type: ActionType.SET_ERROR, payload: { key, value: { error } } });

                            if (!disableErrorHandling && !isCancel(error)) {
                                dispatch(
                                    setGlobalError({
                                        response: error.response,
                                        status: error.status,
                                        statusText: error.statusText,
                                    }),
                                );
                            }
                        });
                }),
            ).then(() => ({}));
        },
        [safeSetState],
    );

    const reset = useCallback(() => safeSetState?.({ type: ActionType.RESET }), [safeSetState]);

    const isSuccess = useMemo(() => {
        const responses = Object.values(state);
        return responses.length > 0 && responses.every((response) => response && response.status === AsyncStatus.RESOLVED);
    }, [state]);

    return {
        isLoading: Object.values(state).some((response) => response && response.status === AsyncStatus.PENDING),
        isSuccess,
        state,
        asyncLoad,
        reset,
    };
};

export default useAsyncMultiple;
