import { useCallback, useRef } from 'react';

type AbortableQuery<T> = (abortController: AbortController | undefined, ...params: unknown[]) => Promise<T>;

const useAbortableQuery = <T>(loadFunction: AbortableQuery<T> | undefined) => {
    const lastAbortController = useRef<AbortController | undefined>(undefined);

    const cancelRequestIfNeeded = useCallback((): AbortController | undefined => {
        if (lastAbortController.current) {
            lastAbortController.current?.abort();
        }
        lastAbortController.current = new AbortController();
        return lastAbortController.current;
    }, []);

    const query = useCallback(
        async (...queryParams: unknown[]) => {
            const currentAbortController = cancelRequestIfNeeded();
            return await loadFunction?.(currentAbortController, ...queryParams);
        },
        [cancelRequestIfNeeded, loadFunction],
    );

    if (!loadFunction) {
        return undefined;
    }

    return query;
};

export default useAbortableQuery;
