import { useCallback, useEffect, useRef, useState } from 'react';

const defaultObserverOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '20px',
    threshold: 0,
};

const useIntersectionObserver = (observerOptions: IntersectionObserverInit = defaultObserverOptions) => {
    const intersectionObserverRef = useRef<IntersectionObserver | null>(null);
    const [intersectedElement, setIntersectingElement] = useState<IntersectionObserverEntry>();

    const handleObserver = ([entry]: IntersectionObserverEntry[]): void => {
        setIntersectingElement(entry);
    };

    const intersectionObserver = useCallback(
        (node: HTMLDivElement | HTMLLIElement) => {
            if (!node) {
                return;
            }
            if (intersectionObserverRef.current) {
                intersectionObserverRef.current.disconnect();
            }
            intersectionObserverRef.current = new IntersectionObserver(handleObserver, observerOptions);
            if (node) {
                intersectionObserverRef.current?.observe(node as Element);
            }
        },
        [observerOptions],
    );

    useEffect(() => {
        return () => {
            intersectionObserverRef.current?.disconnect();
        };
    }, []);

    return { intersectionObserver, intersectedElement };
};

export default useIntersectionObserver;
