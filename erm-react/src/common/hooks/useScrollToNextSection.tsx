import { RefObject, useCallback } from 'react';

const useScrollToNextSection = (
    containerEl: RefObject<HTMLDivElement> | null,
    headerEl: RefObject<HTMLDivElement>,
    contentEl: RefObject<HTMLDivElement>,
    bufferHeight: number,
    marginTop: number,
) => {
    return useCallback(() => {
        if (containerEl?.current && headerEl.current && contentEl.current) {
            const headerHeight = headerEl.current.getBoundingClientRect().height - marginTop;
            const topOffset = containerEl.current.getBoundingClientRect().top + bufferHeight;
            const isSectionSticky = Number(headerEl.current?.getBoundingClientRect().top) === topOffset;

            if (isSectionSticky) {
                const currentScroll = containerEl.current.scrollTop;
                const currentElementHiddenHeight = Number(contentEl.current.getBoundingClientRect()?.top) - topOffset;

                containerEl.current.scrollTo({
                    top: currentScroll + currentElementHiddenHeight - headerHeight,
                    behavior: 'smooth',
                });
            }
        }
    }, [containerEl, headerEl, contentEl, marginTop, bufferHeight]);
};

export default useScrollToNextSection;
