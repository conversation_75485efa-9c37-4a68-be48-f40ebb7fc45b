import { getDateTimeFormat } from 'common/utils/date';
import { useMemo } from 'react';
import { usePursGetCurrentUserUsingGetQuery } from 'user/rtkApi';

const useUserDateTimeFormat = (): string => {
    const { data: currentUser } = usePursGetCurrentUserUsingGetQuery();
    const { dateFormatPattern, timeFormatPattern } = currentUser ?? {};

    const dateTimeFormat = useMemo(() => getDateTimeFormat(dateFormatPattern, timeFormatPattern), [dateFormatPattern, timeFormatPattern]);

    return dateTimeFormat;
};

export default useUserDateTimeFormat;
