import { useEffect, useState } from 'react';

type MutationObserverInit = {
    attributeFilter?: string[];
    attributeOldValue?: boolean;
    attributes?: boolean;
    characterData?: boolean;
    characterDataOldValue?: boolean;
    childList?: boolean;
    subtree?: boolean;
};

const DEFAULT_CONFIG: MutationObserverInit = {
    attributes: true,
    childList: true,
    subtree: true,
};

/**
 * This hook uses MutationObserver = interface which provides the ability to watch for changes being made to the DOM tree.
 * You can specify what is being observed by providing config prop value (e.g. attribute changes, changes in subtree, changes in children).
 * MutationObserver observes specified target element and fires a callback function when DOM changes occur. Observer is cleaned before
 * firing new observe() function in useEffect cleanup function.
 *
 * Example usage: check if component or its children are fully loaded (DOM changes from empty div to div with child nodes);
 * observe / count number of DOM mutations...
 * @param targetEl element to be observed
 * @param callback function to be fired when DOM changes occurs
 * @param config config provided for MutationObserver.observe function (second parameter)
 */
const useMutationObservable = (targetEl: HTMLElement | null, callback: (mutations: Partial<MutationRecord>[]) => void, config = DEFAULT_CONFIG): void => {
    const [observer, setObserver] = useState<MutationObserver>();
    const [targetElement, setTargetElement] = useState<HTMLElement | null>(targetEl);

    useEffect(() => {
        /**
         * In case that observed element is created prior to MutationObserver starts observing its mutations,
         * first mutation (creation) of element could be unnoticed by the observer. Initial mutation is therefore triggered manually.
         */
        setTargetElement((previousTargetElement) => {
            if (!previousTargetElement && targetEl) {
                callback([
                    {
                        target: targetEl,
                        addedNodes: targetEl.childNodes,
                        type: 'childList',
                    },
                ]);
                return targetEl;
            } else {
                return previousTargetElement;
            }
        });
    }, [callback, targetEl]);

    useEffect(() => {
        if (!callback || typeof callback !== 'function') {
            throw new Error(`You must provide a valid callback function for useMutationObservable hook, instead you've provided ${callback}`);
        }
        const obs = new MutationObserver(callback);
        setObserver(obs);
    }, [callback, config]);

    useEffect(() => {
        if (!observer) {
            return;
        }

        if (targetElement) {
            try {
                observer.observe(targetElement, config);
            } catch (e) {
                throw new Error(`An error occured ${e}`);
            }
        }

        return () => {
            if (observer) {
                observer.disconnect();
            }
        };
    }, [observer, targetElement, config]);
};

export default useMutationObservable;
