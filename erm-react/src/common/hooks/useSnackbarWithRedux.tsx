import { useDispatch, useSelector } from '../../store';
import { getErrors } from 'app/selectors';
import { useCallback, useEffect, useMemo } from 'react';
import { strings } from '../utils/i18n';
import { ErrorParam } from '../api/types';
import { OptionsObject } from 'notistack';
import { useSnackbar } from 'context/SnackbarProvider';
import { clearError } from 'app/reducer';

let displayed: string[] = [];

// TODO: add return type to the this
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
const useSnackbarWithRedux = () => {
    const dispatch = useDispatch();
    const errors = useSelector(getErrors);
    const { enqueueSnackbar, closeSnackbar } = useSnackbar();
    const ERROR_SNACK_OPTIONS: OptionsObject = useMemo(
        () => ({
            variant: 'error',
            onExited: (_, snackbarKey: string) => {
                dispatch(clearError(snackbarKey));
                removeDisplayed(snackbarKey);
            },
        }),
        [dispatch],
    );

    const storeDisplayed = (id) => {
        displayed = [...displayed, id];
    };

    const removeDisplayed = (id) => {
        displayed = [...displayed.filter((key) => id !== key)];
    };

    const getTranslatedErrorMessage = useCallback((errorCode: string, errorParams?: ErrorParam[]) => {
        let errorParamsString = '';
        if (errorParams?.length) {
            errorParamsString = strings('errorCodes:errorParams');
            errorParams.forEach((errorParam) => {
                errorParamsString = `${errorParamsString}${errorParam.paramName} ${errorParam.paramValue},`;
            });
            errorParamsString = errorParamsString.slice(0, -1);
        }
        return strings(`errorCodes:${errorCode}`, errorParams?.length ? { errorParams: errorParamsString } : undefined);
    }, []);

    useEffect(() => {
        Object.keys(errors).forEach((key) => {
            const error = errors[key];
            if (error) {
                if (displayed.includes(key)) {
                    return;
                }
                if (error.response?.errCode) {
                    if (error.response?.displayServerMessage === true) {
                        enqueueSnackbar(error.response.message, { key, ...ERROR_SNACK_OPTIONS });
                    } else {
                        enqueueSnackbar(getTranslatedErrorMessage(error.response.errCode, error.response.errorParams), { key, ...ERROR_SNACK_OPTIONS });
                    }
                } else {
                    if (error.response?.message) {
                        enqueueSnackbar(error.response.message, { key, ...ERROR_SNACK_OPTIONS });
                    } else if (error.response?.data?.message) {
                        enqueueSnackbar(error.response.data.message, { key, ...ERROR_SNACK_OPTIONS });
                    } else {
                        enqueueSnackbar(strings('errorCodes:defaultError'), { key, ...ERROR_SNACK_OPTIONS });
                    }
                }
                storeDisplayed(key);
            }
        });
    }, [errors, dispatch, enqueueSnackbar, closeSnackbar, getTranslatedErrorMessage, ERROR_SNACK_OPTIONS]);
};

export default useSnackbarWithRedux;
