import React, { ReactNode } from 'react';
import AlertDialog from 'ui/components/AlertDialog';
import { AlertDialogData } from 'ui/types';
import { DIAGRAM_TOP_PADDING } from 'common/diagram/constants';
import { styled } from '@mui/material/styles';

type DiagramPageProps = {
    header: ReactNode;
    diagramComponent: ReactNode;
    alertDialogData?: AlertDialogData;
};

const Wrapper = styled('div')({
    display: 'flex',
    flex: '1 1 auto',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    height: '100%',
    paddingTop: DIAGRAM_TOP_PADDING,
    overflow: 'hidden',
});

const DiagramPage = (props: DiagramPageProps) => {
    return (
        <Wrapper>
            {props.header}
            {props.alertDialogData && (
                <AlertDialog
                    visible={Boolean(props.alertDialogData)}
                    data={props.alertDialogData}
                />
            )}
            {props.diagramComponent}
        </Wrapper>
    );
};

export default DiagramPage;
