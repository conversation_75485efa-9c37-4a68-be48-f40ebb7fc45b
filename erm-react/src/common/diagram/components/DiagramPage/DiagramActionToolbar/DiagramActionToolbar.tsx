import React, { useEffect } from 'react';
import { strings } from 'common/utils/i18n';
import ContextMenu from '@protecht/ui-library/library/components/ContextMenu';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import { DIAGRAM_TEST_ID_PREFIX } from 'common/diagram/constants';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy, faFileImage, faFilePdf, faPencil, faPrint, faEdit } from '@fortawesome/pro-solid-svg-icons';
import { faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import IconButton from '@mui/material/IconButton';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { getReactRoot } from 'config';
import { DownloadReport, ChevronDown, Link } from '@protecht/ui-library/library/components/SVGIcons';

type ActionToolbarProps = {
    onPDFExport: () => void;
    onImageExport: () => void;
    onPrint: () => void;
    onCopyLinkToPNG?: () => void;
    onDelete: () => void;
    onCancel: () => void;
    onSave: () => void;
    saveDisabled?: boolean;
    onEdit?: () => void;
    onCopy: () => void;
    onExport?: () => void;
    onLink?: () => void;
    bulkUpdate?: () => void;
    canEdit: boolean;
};

const DiagramActionToolbar: React.FC<ActionToolbarProps> = (props: ActionToolbarProps) => {
    const exportMenuItems: ContextMenuItem[] = [
        { icon: <FontAwesomeIcon icon={faFilePdf} />, label: strings('bowtie:button.exportPDF'), action: props.onPDFExport },
        { icon: <FontAwesomeIcon icon={faFileImage} />, label: strings('bowtie:button.exportImage'), action: props.onImageExport },
        { icon: <FontAwesomeIcon icon={faPrint} />, label: strings('bowtie:button.print'), action: props.onPrint },
        ...(props.bulkUpdate ? [{ icon: <FontAwesomeIcon icon={faEdit} />, label: strings('bowtie:button.bulkUpdate'), action: props.bulkUpdate }] : []),
    ];

    useEffect(() => {
        if (props.onCopyLinkToPNG) {
            exportMenuItems.push({
                icon: <FontAwesomeIcon icon={faFileImage} />,
                label: strings('resilience:label.copyLinkToPNG'),
                action: props.onCopyLinkToPNG,
            });
        }
    }, [props.onCopyLinkToPNG]);

    return (
        <>
            {props.onLink && (
                <Button
                    {...ButtonStyles.pageToolbarButton}
                    disabled={!props.canEdit}
                    variant={'secondary'}
                    sx={{ marginRight: '10px' }}
                    startIcon={
                        <Link
                            width="24px"
                            height="24px"
                        />
                    }
                    onClick={props.onLink}
                    dataTestId={`${DIAGRAM_TEST_ID_PREFIX}-links`}
                >
                    {strings('common:label.links')}
                </Button>
            )}

            <ContextMenu
                baseElement={
                    <Button
                        {...ButtonStyles.pageToolbarButton}
                        variant={'secondary'}
                        sx={{ marginRight: '10px' }}
                        startIcon={
                            <DownloadReport
                                width="24px"
                                height="24px"
                            />
                        }
                        endIcon={<ChevronDown />}
                        dataTestId={`${DIAGRAM_TEST_ID_PREFIX}-export`}
                    >
                        {strings('bowtie:button.diagram')}
                    </Button>
                }
                menuAnchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                items={exportMenuItems}
                rootContainer={getReactRoot()}
            />
            {props.onEdit && (
                <Tooltip
                    title={strings('ermMessages:btn_edit')}
                    aria-label={strings('ermMessages:btn_edit')}
                >
                    <IconButton
                        disabled={!props.canEdit}
                        sx={{ marginRight: '10px' }}
                        color={'primary'}
                        onClick={props.onEdit}
                        data-testid={`${DIAGRAM_TEST_ID_PREFIX}-edit`}
                    >
                        <FontAwesomeIcon icon={faPencil} />
                    </IconButton>
                </Tooltip>
            )}
            <Tooltip
                title={strings('common:button.duplicate')}
                aria-label={strings('common:button.duplicate')}
            >
                <IconButton
                    sx={{ marginRight: '10px' }}
                    color={'primary'}
                    onClick={props.onCopy}
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-copy`}
                >
                    <FontAwesomeIcon icon={faCopy} />
                </IconButton>
            </Tooltip>
            <Tooltip
                title={strings('common:button.delete')}
                aria-label={strings('common:button.delete')}
            >
                <IconButton
                    disabled={!props.canEdit}
                    sx={{ marginRight: '20px' }}
                    color={'primary'}
                    onClick={props.onDelete}
                    data-testid={`${DIAGRAM_TEST_ID_PREFIX}-delete`}
                >
                    <FontAwesomeIcon
                        color={props.canEdit ? '#DB2121' : 'primary'}
                        icon={faTrashAlt}
                    />
                </IconButton>
            </Tooltip>
            <Button
                {...ButtonStyles.pageToolbarButton}
                variant={'secondary'}
                style={{ marginRight: '10px' }}
                onClick={props.onCancel}
                dataTestId={`${DIAGRAM_TEST_ID_PREFIX}-cancel`}
            >
                {strings(props.saveDisabled ? 'ermMessages:btn_close' : 'ermMessages:btn_cancel')}
            </Button>
            <Button
                {...ButtonStyles.pageToolbarButton}
                onClick={props.onSave}
                disabled={props.saveDisabled}
                dataTestId={`${DIAGRAM_TEST_ID_PREFIX}-save`}
            >
                {strings('ermMessages:btn_save')}
            </Button>
        </>
    );
};

export default DiagramActionToolbar;
