// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<DiagramActionToolbar/> was rendered 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge <PERSON>-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge <PERSON>on-outlinedSizeLarge <PERSON>i<PERSON>utton-colorPrimary MuiButton-disableElevation css-1k7wz4u-MuiButtonBase-root-MuiButton-root"
      data-testid="diagram-links"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-Mui<PERSON>utton-startIcon"
      >
        <svg
          data-icon="link"
          fill="currentColor"
          height="24px"
          viewBox="0 0 24 24"
          width="24px"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M14.0587 9.94135C13.6716 9.55425 13.2493 9.27273 12.7918 9.02639C12.6158 8.95601 12.4399 8.9912 12.2991 9.13196L12.1584 9.27273C11.8416 9.55425 11.6657 9.94135 11.6305 10.3284C11.5953 10.5044 11.7009 10.6804 11.8416 10.7859C12.088 10.8915 12.4751 11.1378 12.651 11.349C13.8123 12.5103 13.8123 14.3754 12.651 15.5367L10.0117 18.176C8.85044 19.3372 6.98534 19.3372 5.82405 18.176C4.66276 17.0147 4.66276 15.1496 5.82405 13.9883L7.44282 12.3695C7.54839 12.2639 7.58358 12.1232 7.54839 11.9824C7.47801 11.6305 7.40762 11.0323 7.37243 10.6452C7.37243 10.2933 6.91496 10.1173 6.66862 10.3636C6.24633 10.7859 5.57771 11.4545 4.45161 12.5806C2.51613 14.5161 2.51613 17.6481 4.45161 19.5484C6.35191 21.4839 9.48387 21.4839 11.4194 19.5484C14.305 16.6628 14.1642 16.8035 14.3754 16.522C15.9589 14.6217 15.8534 11.7361 14.0587 9.94135ZM19.5484 4.45161C17.6481 2.51613 14.5161 2.51613 12.5806 4.45161C9.69501 7.33724 9.83578 7.19648 9.62463 7.47801C8.04106 9.3783 8.14663 12.2639 9.94135 14.0587C10.3284 14.4457 10.7507 14.7273 11.2082 14.9736C11.3842 15.044 11.5601 15.0088 11.7009 14.868L11.8416 14.7273C12.1584 14.4457 12.3343 14.0587 12.3695 13.6716C12.4047 13.4956 12.2991 13.3196 12.1584 13.2141C11.912 13.1085 11.5249 12.8622 11.349 12.651C10.1877 11.4897 10.1877 9.62463 11.349 8.46334L13.9883 5.82405C15.1496 4.66276 17.0147 4.66276 18.176 5.82405C19.3372 6.98534 19.3372 8.85044 18.176 10.0117L16.5572 11.6305C16.4516 11.7361 16.4164 11.8768 16.4516 12.0176C16.522 12.3695 16.5924 12.9677 16.6276 13.3548C16.6276 13.7067 17.085 13.8827 17.3314 13.6364C17.7537 13.2141 18.4223 12.5455 19.5484 11.4194C21.4839 9.48387 21.4839 6.35191 19.5484 4.45161Z"
            fill="currentColor"
          />
        </svg>
      </span>
      <span
        class="css-qv0y8m"
      >
        Links
      </span>
    </button>
    <div>
      <div>
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-q50794-MuiButtonBase-root-MuiButton-root"
          data-testid="diagram-export"
          tabindex="0"
          type="button"
        >
          <span
            class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
          >
            <svg
              data-icon="download-report"
              fill="currentColor"
              height="24px"
              viewBox="0 0 24 24"
              width="24px"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5 21V3h9l5.4 5.4V21zm8.1-11.7V4.8H6.8v14.4h10.8V9.3z"
                fill="currentColor"
              />
              <path
                d="M11.3 10.2h1.8v3.758l1.418-1.418L15.8 13.8l-3.6 3.6-3.6-3.6 1.26-1.283 1.44 1.44z"
                fill="currentColor"
              />
            </svg>
          </span>
          <span
            class="css-qv0y8m"
          >
            Diagram
          </span>
          <span
            class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
          >
            <svg
              data-icon="chevron-down"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M12.026 12.914 6.601 7.49 4.639 9.452l7.386 7.386 7.389-7.389-1.962-1.962z"
                fill="currentColor"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </button>
      </div>
    </div>
    <span
      aria-label="Edit"
      class="css-2wjxxz"
      data-mui-internal-clone-element="true"
    >
      <button
        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-vanfmr-MuiButtonBase-root-MuiIconButton-root"
        data-testid="diagram-edit"
        tabindex="0"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-pencil "
          data-icon="pencil"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 512 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M410.3 231l11.3-11.3-33.9-33.9-62.1-62.1L291.7 89.8l-11.3 11.3-22.6 22.6L58.6 322.9c-10.4 10.4-18 23.3-22.2 37.4L1 480.7c-2.5 8.4-.2 17.5 6.1 23.7s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L387.7 253.7 410.3 231zM160 399.4l-9.1 22.7c-4 3.1-8.5 5.4-13.3 6.9L59.4 452l23-78.1c1.4-4.9 3.8-9.4 6.9-13.3l22.7-9.1 0 32c0 8.8 7.2 16 16 16l32 0zM362.7 18.7L348.3 33.2 325.7 55.8 314.3 67.1l33.9 33.9 62.1 62.1 33.9 33.9 11.3-11.3 22.6-22.6 14.5-14.5c25-25 25-65.5 0-90.5L453.3 18.7c-25-25-65.5-25-90.5 0zm-47.4 168l-144 144c-6.2 6.2-16.4 6.2-22.6 0s-6.2-16.4 0-22.6l144-144c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </span>
    <span
      aria-label="Duplicate"
      class="css-2wjxxz"
      data-mui-internal-clone-element="true"
    >
      <button
        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-vanfmr-MuiButtonBase-root-MuiIconButton-root"
        data-testid="diagram-copy"
        tabindex="0"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-copy "
          data-icon="copy"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 448 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M208 0L332.1 0c12.7 0 24.9 5.1 33.9 14.1l67.9 67.9c9 9 14.1 21.2 14.1 33.9L448 336c0 26.5-21.5 48-48 48l-192 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48zM48 128l80 0 0 64-64 0 0 256 192 0 0-32 64 0 0 48c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 176c0-26.5 21.5-48 48-48z"
            fill="currentColor"
          />
        </svg>
      </button>
    </span>
    <span
      aria-label="Delete"
      class="css-2wjxxz"
      data-mui-internal-clone-element="true"
    >
      <button
        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-rmuahj-MuiButtonBase-root-MuiIconButton-root"
        data-testid="diagram-delete"
        tabindex="0"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-trash-can "
          color="#DB2121"
          data-icon="trash-can"
          data-prefix="far"
          focusable="false"
          role="img"
          viewBox="0 0 448 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M170.5 51.6L151.5 80l145 0-19-28.4c-1.5-2.2-4-3.6-6.7-3.6l-93.7 0c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6L354.2 80 368 80l48 0 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-8 0 0 304c0 44.2-35.8 80-80 80l-224 0c-44.2 0-80-35.8-80-80l0-304-8 0c-13.3 0-24-10.7-24-24S10.7 80 24 80l8 0 48 0 13.8 0 36.7-55.1C140.9 9.4 158.4 0 177.1 0l93.7 0c18.7 0 36.2 9.4 46.6 24.9zM80 128l0 304c0 17.7 14.3 32 32 32l224 0c17.7 0 32-14.3 32-32l0-304L80 128zm80 64l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16z"
            fill="currentColor"
          />
        </svg>
      </button>
    </span>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
      data-testid="diagram-cancel"
      style="margin-right: 10px;"
      tabindex="0"
      type="button"
    >
      <span
        class="css-1d0doyg"
      >
        Cancel
      </span>
    </button>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
      data-testid="diagram-save"
      tabindex="0"
      type="button"
    >
      <span
        class="css-1d0doyg"
      >
        Save
      </span>
    </button>
  </div>
  <div
    aria-hidden="true"
    class="MuiPopover-root MuiMenu-root MuiModal-root MuiModal-hidden css-kxpf75-MuiModal-root-MuiPopover-root-MuiMenu-root"
    data-testid="contextMenu"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiBackdrop-invisible MuiModal-backdrop css-g3hgs1-MuiBackdrop-root-MuiModal-backdrop"
      style="opacity: 0; visibility: hidden;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="-1"
    />
    <div
      class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPopover-paper MuiMenu-paper MuiMenu-paper css-kz6wg5-MuiPaper-root-MuiPopover-paper-MuiMenu-paper"
      style="opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
      tabindex="-1"
    >
      <ul
        class="MuiList-root MuiList-padding MuiMenu-list css-6hp17o-MuiList-root-MuiMenu-list"
        role="menu"
        tabindex="-1"
      >
        <li
          class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-dense MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-dense MuiMenuItem-gutters css-deinnr-MuiButtonBase-root-MuiMenuItem-root"
          role="menuitem"
          tabindex="-1"
        >
          <div
            class="MuiListItemIcon-root css-1v5mzmn-MuiListItemIcon-root"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-file-pdf "
              data-icon="file-pdf"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 144-208 0c-35.3 0-64 28.7-64 64l0 144-48 0c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128zM176 352l32 0c30.9 0 56 25.1 56 56s-25.1 56-56 56l-16 0 0 32c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-48 0-80c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24l-16 0 0 48 16 0zm96-80l32 0c26.5 0 48 21.5 48 48l0 64c0 26.5-21.5 48-48 48l-32 0c-8.8 0-16-7.2-16-16l0-128c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-16 0 0 96 16 0zm80-112c0-8.8 7.2-16 16-16l48 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 32 32 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 48c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-64 0-64z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div
            class="MuiListItemText-root MuiListItemText-dense css-1qa7vcw-MuiListItemText-root"
            data-testid="contextMenu-PDF"
          >
            <span
              class="MuiTypography-root MuiTypography-dropdownItem MuiListItemText-primary css-1xr844i-MuiTypography-root"
            >
              PDF
            </span>
          </div>
        </li>
        <li
          class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-dense MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-dense MuiMenuItem-gutters css-deinnr-MuiButtonBase-root-MuiMenuItem-root"
          role="menuitem"
          tabindex="-1"
        >
          <div
            class="MuiListItemIcon-root css-1v5mzmn-MuiListItemIcon-root"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-file-image "
              data-icon="file-image"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 384 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM64 256a32 32 0 1 1 64 0 32 32 0 1 1 -64 0zm152 32c5.3 0 10.2 2.6 13.2 6.9l88 128c3.4 4.9 3.7 11.3 1 16.5s-8.2 8.6-14.2 8.6l-88 0-40 0-48 0-48 0c-5.8 0-11.1-3.1-13.9-8.1s-2.8-11.2 .2-16.1l48-80c2.9-4.8 8.1-7.8 13.7-7.8s10.8 2.9 13.7 7.8l12.8 21.4 48.3-70.2c3-4.3 7.9-6.9 13.2-6.9z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div
            class="MuiListItemText-root MuiListItemText-dense css-1qa7vcw-MuiListItemText-root"
            data-testid="contextMenu-PNG"
          >
            <span
              class="MuiTypography-root MuiTypography-dropdownItem MuiListItemText-primary css-1xr844i-MuiTypography-root"
            >
              PNG
            </span>
          </div>
        </li>
        <li
          class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-dense MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-dense MuiMenuItem-gutters css-deinnr-MuiButtonBase-root-MuiMenuItem-root"
          role="menuitem"
          tabindex="-1"
        >
          <div
            class="MuiListItemIcon-root css-1v5mzmn-MuiListItemIcon-root"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-print "
              data-icon="print"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M128 0C92.7 0 64 28.7 64 64l0 96 64 0 0-96 226.7 0L384 93.3l0 66.7 64 0 0-66.7c0-17-6.7-33.3-18.7-45.3L400 18.7C388 6.7 371.7 0 354.7 0L128 0zM384 352l0 32 0 64-256 0 0-64 0-16 0-16 256 0zm64 32l32 0c17.7 0 32-14.3 32-32l0-96c0-35.3-28.7-64-64-64L64 192c-35.3 0-64 28.7-64 64l0 96c0 17.7 14.3 32 32 32l32 0 0 64c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-64zM432 248a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div
            class="MuiListItemText-root MuiListItemText-dense css-1qa7vcw-MuiListItemText-root"
            data-testid="contextMenu-Print"
          >
            <span
              class="MuiTypography-root MuiTypography-dropdownItem MuiListItemText-primary css-1xr844i-MuiTypography-root"
            >
              Print
            </span>
          </div>
        </li>
      </ul>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="-1"
    />
  </div>
</body>
`;
