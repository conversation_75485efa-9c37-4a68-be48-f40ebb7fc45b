import React from 'react';
import { screen } from '@testing-library/react';
import { render } from 'test/utils';
import DiagramActionToolbar from './DiagramActionToolbar';

jest.mock('yfiles', () => ({
    Size: class Size {
        width: number;
        height: number;
        constructor(width: number, height: number) {
            this.width = width;
            this.height = height;
        }
    },
}));

describe('<DiagramActionToolbar/>', () => {
    const mockFn = jest.fn();
    const mockedCommonProps = {
        onPDFExport: mockFn,
        onImageExport: mockFn,
        onPrint: mockFn,
        onCopyLinkToPNG: mockFn,
        onDelete: mockFn,
        onCancel: mockFn,
        onSave: mockFn,
        onEdit: mockFn,
        onCopy: mockFn,
        onLink: mockFn,
        onImport: mockFn,
        onExport: mockFn,
        saveDisabled: false,
        canEdit: true,
    };

    it('was rendered', () => {
        const { container } = render(<DiagramActionToolbar {...mockedCommonProps} />);
        expect(container).toBeInTheDocument();
        expect(container).toBeVisible();
        expect(document.body).toMatchSnapshot();
    });

    it('renders save button as disabled and cancel button with correct title', () => {
        render(
            <DiagramActionToolbar
                {...mockedCommonProps}
                saveDisabled={true}
            />,
        );
        const saveButton = screen.getByTestId('diagram-save');
        const cancelButton = screen.getByTestId('diagram-cancel');
        expect(saveButton).toBeDisabled();
        expect(cancelButton).toHaveTextContent('Close');
    });

    it('does not render links button when onLink is undefined', () => {
        render(
            <DiagramActionToolbar
                {...mockedCommonProps}
                onLink={undefined}
            />,
        );
        const linksButton = screen.queryByTestId('diagram-links');
        expect(linksButton).not.toBeInTheDocument();
    });

    it('does not render edit button when onEdit is undefined', () => {
        render(
            <DiagramActionToolbar
                {...mockedCommonProps}
                onEdit={undefined}
            />,
        );
        const editButton = screen.queryByTestId('diagram-edit');
        expect(editButton).not.toBeInTheDocument();
    });
});
