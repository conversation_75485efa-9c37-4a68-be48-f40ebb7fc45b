// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Zoomer/> was rendered 1`] = `
<div>
  <div
    class="MuiBox-root css-1bgvo2p"
  >
    <span
      aria-label="Zoom In"
      class="css-2wjxxz"
      data-mui-internal-clone-element="true"
    >
      <button
        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-1vwz8ow-MuiButtonBase-root-MuiIconButton-root"
        data-testid="zoom-in"
        role="button"
        tabindex="0"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-magnifying-glass-plus "
          data-icon="magnifying-glass-plus"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 512 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM184 296c0 13.3 10.7 24 24 24s24-10.7 24-24l0-64 64 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-64 0 0-64c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 64-64 0c-13.3 0-24 10.7-24 24s10.7 24 24 24l64 0 0 64z"
            fill="currentColor"
          />
        </svg>
      </button>
    </span>
    <span
      aria-label="Zoom Original"
      class="css-2wjxxz"
      data-mui-internal-clone-element="true"
    >
      <button
        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-1vwz8ow-MuiButtonBase-root-MuiIconButton-root"
        data-testid="zoom-original"
        role="button"
        tabindex="0"
        type="button"
      >
        <p
          class="MuiTypography-root MuiTypography-body2 css-u4gdk6-MuiTypography-root"
        >
          1:1
        </p>
      </button>
    </span>
    <span
      aria-label="Zoom Out"
      class="css-2wjxxz"
      data-mui-internal-clone-element="true"
    >
      <button
        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-1vwz8ow-MuiButtonBase-root-MuiIconButton-root"
        data-testid="zoom-out"
        role="button"
        tabindex="0"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-magnifying-glass-minus "
          data-icon="magnifying-glass-minus"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 512 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM136 184c-13.3 0-24 10.7-24 24s10.7 24 24 24l144 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-144 0z"
            fill="currentColor"
          />
        </svg>
      </button>
    </span>
    <span
      aria-label="Fit Content"
      class="css-2wjxxz"
      data-mui-internal-clone-element="true"
    >
      <button
        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeMedium css-1vwz8ow-MuiButtonBase-root-MuiIconButton-root"
        data-testid="fit-content"
        role="button"
        tabindex="0"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-expand "
          data-icon="expand"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 448 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M32 32C14.3 32 0 46.3 0 64l0 96c0 17.7 14.3 32 32 32s32-14.3 32-32l0-64 64 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 32zM64 352c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7 14.3 32 32 32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-64 0 0-64zM320 32c-17.7 0-32 14.3-32 32s14.3 32 32 32l64 0 0 64c0 17.7 14.3 32 32 32s32-14.3 32-32l0-96c0-17.7-14.3-32-32-32l-96 0zM448 352c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 64-64 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l96 0c17.7 0 32-14.3 32-32l0-96z"
            fill="currentColor"
          />
        </svg>
      </button>
    </span>
  </div>
</div>
`;
