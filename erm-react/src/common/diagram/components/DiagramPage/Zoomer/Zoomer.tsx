import React from 'react';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import useTheme from '@mui/system/useTheme';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { strings } from 'common/utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearchPlus, faSearchMinus, faExpand } from '@fortawesome/pro-solid-svg-icons';
import Box from '@mui/material/Box';

type ZoomerProps = {
    onZoomIn: () => void;
    onZoomOut: () => void;
    onZoomOriginal: () => void;
    onFitContent: () => void;
};

const Zoomer: React.FC<ZoomerProps> = (props: ZoomerProps) => {
    const theme = useTheme();

    return (
        <Box
            display="flex"
            columnGap="8px"
        >
            <Tooltip
                title={strings('bowtie:diagram.button.zoomIn')}
                aria-label={strings('bowtie:diagram.button.zoomIn')}
            >
                <IconButton
                    color={'primary'}
                    onClick={() => props.onZoomIn()}
                    role={'button'}
                    data-testid="zoom-in"
                >
                    <FontAwesomeIcon icon={faSearchPlus} />
                </IconButton>
            </Tooltip>
            <Tooltip
                title={strings('bowtie:diagram.button.zoomOriginal')}
                aria-label={strings('bowtie:diagram.button.zoomOriginal')}
            >
                <IconButton
                    color={'primary'}
                    onClick={() => props.onZoomOriginal()}
                    role={'button'}
                    data-testid="zoom-original"
                >
                    <Typography
                        variant="body2"
                        color={theme.palette.primary.main}
                    >
                        1:1
                    </Typography>
                </IconButton>
            </Tooltip>
            <Tooltip
                title={strings('bowtie:diagram.button.zoomOut')}
                aria-label={strings('bowtie:diagram.button.zoomOut')}
            >
                <IconButton
                    color={'primary'}
                    onClick={() => props.onZoomOut()}
                    role={'button'}
                    data-testid="zoom-out"
                >
                    <FontAwesomeIcon icon={faSearchMinus} />
                </IconButton>
            </Tooltip>
            <Tooltip
                title={strings('bowtie:diagram.button.fitContent')}
                aria-label={strings('bowtie:diagram.button.fitContent')}
            >
                <IconButton
                    color={'primary'}
                    onClick={() => props.onFitContent()}
                    role={'button'}
                    data-testid="fit-content"
                >
                    <FontAwesomeIcon icon={faExpand} />
                </IconButton>
            </Tooltip>
        </Box>
    );
};

export default Zoomer;
