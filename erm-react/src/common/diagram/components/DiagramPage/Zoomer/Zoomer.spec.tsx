import React from 'react';
import { render, screen } from 'test/utils';
import Zoomer from './Zoomer';
import user from '@testing-library/user-event';

describe('<Zoomer/>', () => {
    const onZoomIn = jest.fn();
    const onZoomOut = jest.fn();
    const onZoomOriginal = jest.fn();
    const onFitContent = jest.fn();

    const setup = () => {
        return render(
            <Zoomer
                onZoomIn={onZoomIn}
                onZoomOut={onZoomOut}
                onZoomOriginal={onZoomOriginal}
                onFitContent={onFitContent}
            />,
        );
    };

    it('was rendered', () => {
        const view = setup();
        expect(view.container).toMatchSnapshot();
        expect(view.container).toBeInTheDocument();
    });

    const buttonTestIdAndHandlers = [
        ['zoom-in', onZoomIn],
        ['zoom-original', onZoomOriginal],
        ['zoom-out', onZoomOut],
        ['fit-content', onFitContent],
    ];

    it.each(buttonTestIdAndHandlers)('%s button', async (testId, handler) => {
        setup();
        const button = screen.getByTestId(testId);

        expect(button).toBeInTheDocument();
        expect(button).toBeEnabled();
        expect(button).toBeVisible();

        await user.click(button);
        expect(handler).toHaveBeenCalled();
    });
});
