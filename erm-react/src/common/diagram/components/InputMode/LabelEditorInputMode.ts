import { Point, TextEditorInputMode } from 'yfiles';
import { DIAGRAM_TEST_ID_PREFIX } from '../../constants';

/**
 * A custom {@link TextEditorInputMode}.
 */
export class LabelEditorInputMode extends TextEditorInputMode {
    /**
     * Creates a new instance of the {@link LabelEditorInputMode} which utilizes a text editor with custom styling.
     */
    constructor() {
        super();
        this.autoCommitOnFocusLost = true;
        this.initializeContainer(this.editorContainer);

        // always place the editor horizontal
        this.upVector = new Point(0, -1);
    }

    /**
     * Initializes an {@link HTMLDivElement}
     */
    private initializeContainer(container: HTMLDivElement) {
        container.style.border = 'none';
        container.style.borderRadius = '4px';
        container.style.padding = '0';

        const input = container.getElementsByTagName('textarea').item(0);
        if (input) {
            input.setAttribute('data-testid', `${DIAGRAM_TEST_ID_PREFIX}-label-editor`);
            input.style.padding = '8px';
            input.style.resize = 'none';
            input.style.outline = 'none';
            input.style.whiteSpace = 'pre-wrap';
            input.setAttribute('maxlength', '250');
        }
    }
}
