import { EdgePathLabelModel, EdgeSides, <PERSON>rap<PERSON><PERSON>omponent, IEdge } from 'yfiles';
import DiagramElement from './DiagramElement';

type Props = {
    graphComponent: GraphComponent;
    selected: IEdge | null;
    edgeSides: EdgeSides;
};

/**
 * Component used to position react elements around selected edge.
 */
class EdgeElement extends DiagramElement {
    /**
     * Constructs a new instance of the EdgeElement.
     */
    constructor(props: Props) {
        super(props);
    }

    /**
     * Changes the location of element to the location calculated by a label model parameter.
     * Depending on the selection, an edge specific label model is used to place the element.
     */
    updateLocation(): void {
        if (!this.props.selected) {
            return;
        }

        // when edge is removed, ports are missing and createDummyLabel funtion will fail
        const edge = this.props.selected as IEdge;
        if (!edge.sourcePort || !edge.targetPort) {
            return;
        }

        // initialize a label model parameter that is used to position the edge pop-up
        const edgeLabelModel = new EdgePathLabelModel(0, 0, 0, false, this.props.edgeSides);
        const labelModelParameter = edgeLabelModel.createRatioParameter(0.5, this.props.edgeSides);

        // create a dummy label to let the LabelModelParameter compute the correct location
        super.createDummyLabel(this.props.selected, labelModelParameter);
    }
}

export default EdgeElement;
