import React, { Component, PropsWithChildren } from 'react';
import {
    ExteriorLabelModelPosition,
    EdgeSides,
    GraphComponent,
    IEdge,
    ILabelModelParameter,
    INode,
    Insets,
    InsetsConvertible,
    Point,
    SimpleLabel,
    ILabelOwner,
    Size,
} from 'yfiles';

type BaseProps = PropsWithChildren<{
    graphComponent: GraphComponent;
    selected: INode | IEdge | null;
    // eslint-disable-next-line react/no-unused-prop-types
    position?: ExteriorLabelModelPosition;
    // eslint-disable-next-line react/no-unused-prop-types
    edgeSides?: EdgeSides;
    // eslint-disable-next-line react/no-unused-prop-types
    insets?: Insets | InsetsConvertible;
    style?: any;
}>;

/**
 * Component used to position react elements around selected node. This component
 */
abstract class DiagramElement extends Component<BaseProps> {
    private $dirty: boolean;

    protected $graphComponent: GraphComponent;
    protected $container: React.RefObject<HTMLDivElement>;

    state = {
        isVisible: false,
        isVisuallyHidden: false,
    };

    /**
     * Constructs a new instance of the DiagramElement.
     */
    constructor(props: BaseProps) {
        super(props);
        this.$graphComponent = props.graphComponent;
        this.$container = React.createRef<HTMLDivElement>();
        this.$dirty = false;
    }

    componentDidMount(): void {
        if (this.props.graphComponent) {
            this.$graphComponent = this.props.graphComponent;
            this.registerUpdateListeners();
            if (this.props.selected) {
                this.show();
            } else {
                this.hide();
            }
        }
    }

    componentDidUpdate(prevProps: Readonly<BaseProps>): void {
        if (this.props.graphComponent) {
            if (prevProps.graphComponent !== this.props.graphComponent) {
                this.$graphComponent = this.props.graphComponent;
                this.registerUpdateListeners();
            }
            if (this.props.selected) {
                this.show();
            } else {
                this.hide();
            }
        }
    }

    componentWillUnmount(): void {
        this.removeUpdateListeners();
    }

    /**
     * Makes this menu visible near the given items.
     */
    show(): void {
        // place the contextual menu
        this.updateLocation();

        if (!this.state.isVisible) {
            this.setState({
                isVisible: true,
            });
        }
    }

    /**
     * Hides this menu.
     */
    hide(): void {
        if (this.state.isVisible) {
            this.setState({
                isVisuallyHideen: true,
                isVisible: false,
            });
        }

        if (this.$container.current) {
            this.$container.current.addEventListener(
                'transitionend',
                () => {
                    if (!this.state.isVisible) {
                        this.setState({
                            isVisible: false,
                        });
                    }
                },
                {
                    capture: false,
                    once: true,
                    passive: false,
                },
            );
        }
    }

    /**
     * Changes the location of element to the location calculated by a label model parameter.
     * Depending on the selection, either an edge specific label model is used, or a node label model
     * that uses the union of all selected elements to place the element above that union.
     */
    abstract updateLocation(): void;

    createDummyLabel(owner: ILabelOwner, layoutParameter: ILabelModelParameter): void {
        const width = this.$container.current?.clientWidth || 0;
        const height = this.$container.current?.clientHeight || 0;
        const zoom = this.$graphComponent.zoom;

        // create a dummy label to let the LabelModelParameter compute the correct location
        const dummyLabel = new SimpleLabel(owner, '', layoutParameter);
        if (layoutParameter.supports(dummyLabel)) {
            dummyLabel.preferredSize = new Size(width / zoom, height / zoom);
            const newLayout = layoutParameter.model.getGeometry(dummyLabel, layoutParameter);
            this.setLocation(newLayout.anchorX, newLayout.anchorY - height / zoom, width, height);
        }
    }

    /**
     * Sets the location of this pop-up to the given world coordinates.
     * @param {number} x The target x-coordinate of the menu
     * @param {number} y The target y-coordinate of the menu
     * @param {number} width The width of the menu
     * @param {number} height The height of the menu
     */
    setLocation(x: number, y: number, width: number, height: number): void {
        // recalculate element world coordinates the view coordinates since we have to place the div in the regular HTML coordinate space
        const viewPoint = this.$graphComponent.toViewCoordinates(new Point(x, y));
        // recalculate view coordinates to actual page coordinates, since canvas does not cover whole screen
        const pageLocation = this.$graphComponent.toPageFromView(viewPoint);
        // get canvas offset from page top
        const canvasTopOffset = this.$graphComponent.toPageFromView(new Point(0, 0)).y;
        const left = pageLocation.x;
        const top = pageLocation.y;
        const isOverTop = top + height < canvasTopOffset;

        if (this.$container.current) {
            this.$container.current.style.left = `${left}px`;
            this.$container.current.style.top = `${top}px`;
            this.$container.current.style.visibility = isOverTop ? 'hidden' : '';
        }
    }

    /**
     * Adds listeners for graph changes, to update the location or state of the menu accordingly.
     */
    registerUpdateListeners(): void {
        this.removeUpdateListeners();
        if (this.$graphComponent) {
            this.$graphComponent.addViewportChangedListener(this.viewportChangedListener);
            this.$graphComponent.graph.addNodeLayoutChangedListener(this.nodeLayoutChangedListener);
            this.$graphComponent.addUpdatedVisualListener(this.updatedVisualListener);
            this.$graphComponent.clipboard.addElementsCutListener(this.elementsCutListener);
        }
    }

    private removeUpdateListeners() {
        if (this.$graphComponent) {
            this.$graphComponent.removeViewportChangedListener(this.viewportChangedListener);
            this.$graphComponent.graph.removeNodeLayoutChangedListener(this.nodeLayoutChangedListener);
            this.$graphComponent.removeUpdatedVisualListener(this.updatedVisualListener);
            this.$graphComponent.clipboard.removeElementsCutListener(this.elementsCutListener);
        }
    }

    private viewportChangedListener = () => {
        if (this.props.selected) {
            this.$dirty = true;
        }
    };

    private nodeLayoutChangedListener = () => {
        if (this.props.selected) {
            this.$dirty = true;
        }
    };

    private updatedVisualListener = () => {
        if (this.props.selected && this.$dirty) {
            this.$dirty = false;
            this.updateLocation();
        }
    };

    private elementsCutListener = () => {
        this.hide();
    };

    render(): JSX.Element {
        return (
            <div
                style={{
                    ...this.props.style,
                    zIndex: 10,
                    position: 'fixed',
                    display: 'block',
                    alignItems: 'center',
                    userSelect: 'none',
                    '@keyframes showup': {
                        '0%': {
                            opacity: 0,
                        },

                        '100%': {
                            opacity: 1,
                        },
                    },
                    ...(this.state.isVisible
                        ? {
                              opacity: 1,
                              animation: 'showup 0.2s ease-out',
                          }
                        : {
                              opacity: 0,
                              transition: 'opacity 0.2s ease-out',
                          }),
                    ...(this.state.isVisuallyHidden && {}),
                }}
                ref={this.$container}
            >
                {this.props.children}
            </div>
        );
    }
}

export default DiagramElement;
