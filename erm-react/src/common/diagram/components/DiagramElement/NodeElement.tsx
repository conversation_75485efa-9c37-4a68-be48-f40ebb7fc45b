import { ExteriorLabelModel, ExteriorLabelModelPosition, GraphComponent, INode, Insets, InsetsConvertible, SimpleNode } from 'yfiles';
import DiagramElement from './DiagramElement';

type Props = {
    graphComponent: GraphComponent;
    selected: INode | null;
    position: ExteriorLabelModelPosition;
    insets?: Insets | InsetsConvertible;
    style?: any;
};

const DEFAULT_ELEMENT_INSETS = 10;

/**
 * Component used to position react elements around selected node. This component
 */
class NodeElement extends DiagramElement {
    /**
     * Constructs a new instance of the NodeElement.
     */
    constructor(props: Props) {
        super(props);
    }

    /**
     * Changes the location of element to the location calculated by a label model parameter.
     * Depending on the selection, either an edge specific label model is used, or a node label model
     * that uses the union of all selected elements to place the element above that union.
     */
    updateLocation(): void {
        if (!this.props.selected) {
            return;
        }

        // if nodes and edges are selected, we use the union of the node's bounding boxes as position reference
        const dummyOwner = new SimpleNode({
            layout: (this.props.selected as INode).layout,
        });
        // initialize a label model parameter that is used to position the node pop-up
        const nodeLabelModel = new ExteriorLabelModel({ insets: this.props.insets ?? DEFAULT_ELEMENT_INSETS });
        const labelModelParameter = nodeLabelModel.createParameter(this.props.position!);

        // create a dummy label to let the LabelModelParameter compute the correct location
        super.createDummyLabel(dummyOwner, labelModelParameter);
    }
}

export default NodeElement;
