import { BaseModel } from 'common/diagram/types';

export class ElementIdProvider {
    protected currentNodeId = 0;
    protected currentEdgeId = 0;

    protected constructor() {
        // protected constructor
    }

    set nodes(nodes: BaseModel[] | undefined) {
        nodes?.forEach((node) => {
            if (node.key > this.currentNodeId) {
                this.currentNodeId = node.key;
            }
        });
    }

    set links(links: BaseModel[] | undefined) {
        links?.forEach((link) => {
            if (link.key > this.currentEdgeId) {
                this.currentEdgeId = link.key;
            }
        });
    }

    public getNextNodeId(): number {
        this.currentNodeId += 1;
        return this.currentNodeId;
    }

    public getNextEdgeId(): number {
        this.currentEdgeId += 1;
        return this.currentEdgeId;
    }
}
