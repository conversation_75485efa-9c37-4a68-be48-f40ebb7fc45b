import {
    FilteredGraph<PERSON>rap<PERSON>,
    FreeNodePortLocationModel,
    GraphComponent,
    IEdge,
    IEnumerable,
    IGraph,
    IModelItem,
    INode,
    IPort,
    List,
    ShortestPath,
    SimplePort,
} from 'yfiles';

import { BaseModel, PortLocations } from 'common/diagram/types';

/**
 * This class contains utility methods that deal with the diagram.
 */
export class DiagramUtils {
    /**
     * Gets the first incoming edge.
     * @param {INode} node The given node.
     * @param {IGraph} graph The input graph.
     * @return {IEdge}
     */
    static getInEdge(node: INode, graph: IGraph): IEdge | null {
        return graph.inEdgesAt(node).firstOrDefault();
    }

    /**
     * It checks if node has any outgoing connections.
     * @param {INode} node The given node.
     * @param {IGraph} graph The input graph.
     * @return true if there are any outgoing edges, false otherwise
     */
    static hasOutEdges(node: INode, graph: IGraph): boolean {
        return graph.outEdgesAt(node).size > 0;
    }

    /**
     * It checks if node has any connections.
     * @param {INode} node The given node.
     * @param {IGraph} graph The input graph
     * @return {boolean} True if node have some connections, false otherwise.
     */
    public static hasConnections(node: INode, graph: IGraph): boolean {
        return graph.edgesAt(node).size > 1;
    }

    /**
     * It checks if any node from list has any connections.
     * @param {List<INode>} nodes List of selected nodes.
     * @param {IGraph} graph The input graph
     * @return {boolean} True if any node have some connections, false otherwise.
     */
    public static anyHasConnections(nodes: List<INode>, graph: IGraph): boolean {
        return nodes.firstOrDefault((node) => this.hasConnections(node, graph)) !== null;
    }

    /**
     * It checks if target is directly connected to source.
     * @param {INode} source node
     * @param {INode} target node
     * @param {IGraph} graph The input graph to check.
     */
    public static isDirectlyConnectedTo(source: INode, target: INode, graph: IGraph | undefined | null): boolean {
        return !!(graph && (graph.successors(source).indexOf(target) !== -1 || graph.predecessors(source).indexOf(target) !== -1));
    }

    /**
     * Extract targeted nodes by command.
     * @param parameter of command
     * @param target of command usually {GraphComponent}
     */
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    static getCommandTargetNodes(parameter: any, target: any): List<INode> | null {
        if (!parameter) {
            if (GraphComponent.isInstance(target)) {
                if (target.selection.selectedNodes.size > 0) {
                    return target.selection.selectedNodes.toList();
                }
            }
        } else if (parameter instanceof IModelItem) {
            const nodes = new List<INode>();
            if (parameter instanceof INode) {
                nodes.add(parameter);
            }
            return nodes;
        } else if (parameter instanceof IEnumerable) {
            const nodes = parameter.filter((para) => para instanceof INode).toList();
            return nodes.size > 0 ? nodes : null;
        }
        return null;
    }

    /**
     * Extract targeted edges by command.
     * @param parameter of command
     * @param target of command usually {GraphComponent}
     */
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    static getCommandTargetEdges(parameter: any, target: any): List<IEdge> | null {
        if (!parameter) {
            if (GraphComponent.isInstance(target)) {
                if (target.selection.selectedEdges.size > 0) {
                    return target.selection.selectedEdges.toList();
                }
            }
        } else if (parameter instanceof IModelItem) {
            const edges = new List<IEdge>();
            if (parameter instanceof IEdge) {
                edges.add(parameter);
            }
            return edges;
        } else if (parameter instanceof IEnumerable) {
            const edges = parameter.filter((para) => para instanceof IEdge).toList();
            return edges.size > 0 ? edges : null;
        }
        return null;
    }

    /**
     * To determine if there is already existing path from src to target
     * @param {IGraph} graph to check path.
     * @param {INode} src node for path.
     * @param {INode} target node for path.
     * @return true if there is path from src to target, false otherwise.
     */
    static hasPath(graph: IGraph, src: INode, target: INode): boolean {
        if (src === target) {
            return true;
        }

        const paths = new ShortestPath({
            source: src,
            sink: target,
        });
        return !!paths.run(graph).path;
    }

    /**
     * Check if the provided data contain item with the same ID as the ID of the provided item
     * @param data to be checked
     * @param item for which the ID should be checked
     * @param index of the item for which the ID should be checked
     * @returns true if the provided item ID is duplicated, false otherwise
     */
    protected static hasDuplicateId = (data: BaseModel[], item: BaseModel, index: number): boolean => {
        return !!data.find((nextItem, nextIndex) => nextItem.key === item.key && nextIndex !== index);
    };

    /**
     * Validates IDs of the provided data
     * @param data to be validated
     * @returns true if the provided data have valid IDs
     */
    static hasValidIds(data: BaseModel[]): boolean {
        const duplicatesFound = data.some((item, index) => this.hasDuplicateId(data, item, index));

        return !duplicatesFound;
    }

    /**
     * It generates all possible port location for BowTie nodes.
     * @param node for ports to be generated.
     * @return array of generated ports.
     */
    static generatePorts(node: INode): Array<IPort> {
        const result = new Array<IPort>();
        result.push(new SimplePort(node, FreeNodePortLocationModel.NODE_LEFT_ANCHORED));
        result.push(new SimplePort(node, FreeNodePortLocationModel.NODE_RIGHT_ANCHORED));
        result.push(new SimplePort(node, FreeNodePortLocationModel.NODE_TOP_ANCHORED));
        result.push(new SimplePort(node, FreeNodePortLocationModel.NODE_BOTTOM_ANCHORED));

        return result;
    }

    /**
     * It calculates closes ports for 2 set of ports, ideally ports of 2 neighbouring nodes.
     * @param sourcePorts ports of source node.
     * @param targetPorts ports of target node.
     * @return portLocation for closest port for source and target node.
     */
    static computeClosestPort(sourcePorts: Array<IPort>, targetPorts: Array<IPort>): PortLocations | null {
        if (sourcePorts.length > 0 && targetPorts.length > 0) {
            let sourceClosest = sourcePorts[0];
            let targetClosest = targetPorts[0];
            let closesDistance = Number.MAX_VALUE;

            sourcePorts.forEach((sp) =>
                targetPorts.forEach((tp) => {
                    const distance = sp.location.distanceTo(tp.location);
                    if (closesDistance > distance) {
                        closesDistance = distance;
                        sourceClosest = sp;
                        targetClosest = tp;
                    }
                }),
            );

            return {
                sourcePortLocation: sourceClosest.locationParameter,
                targetPortLocation: targetClosest.locationParameter,
            };
        }
        return null;
    }

    /**
     * It un-warps filtered graph to gets full graph with everything.
     * @param graph filtered graph wrapper.
     * @return graph fill un-wrapped graph.
     */
    static getFullGraph(graph: IGraph): IGraph {
        while (graph instanceof FilteredGraphWrapper) {
            graph = graph.wrappedGraph!;
        }
        return graph;
    }
}
