/**
 * A class that provides png-image export. The image is exported to svg and converted to png.
 */
import { FilteredGraphWrapper, GraphComponent, GraphCopier, ICanvasObjectDescriptor, IGraph, Insets, Point, Rect, Size, SvgExport } from 'yfiles';
import { A3_SIZE, DEFAULT_EXPORT_NAME, ExportContext } from 'common/diagram/constants';
import { FileUtils } from 'common/utils/FileUtils';
import { LegendVisual } from 'bowtie/diagram/components/DiagramComponent/components/Legend/LegendVisual';
import { DefaultBowtieNodeStyle } from 'bowtie/diagram/components/DiagramComponent/components/types';
import { LEGEND_WIDTH } from 'bowtie/diagram/components/DiagramComponent/components/Legend/Legend';
import { DefaultResilienceNodeStyle, DiagramMetadata, Node, NodeType } from 'resilience/diagram/types';
import { NodeType as NodeTypeBowtie } from 'bowtie/types';
import { strings } from 'common/utils/i18n';
import addOpenSansBoldFont from 'common/diagram/utils/jsPDF/openSansBold';
import addOpenSansRegularFont from 'common/diagram/utils/jsPDF/openSansRegular';
import { jsPDF } from './jsPDF/jspdf.es';
import { DateTime } from 'luxon';
import store from 'store';
import { calculateLegendWidth, LegendResilienceVisual } from 'resilience/components/ResilienceLayout/ResilienceDiagramPage/Legend';
import { NodeStyle } from 'bowtie/types';
import { DEFAULT_DATE_FORMAT, PROTECHT_DATE_TIME_FORMAT } from 'common/constants';

const DEFAULT_MARGINS = new Insets(24);
const MIN_WIDTH_PNG = 1000;
const DESCRIPTION_WIDTH = 0.8;

export default class ExportUtils {
    /**
     * Exports the graph to a PNG image.
     * @param {IGraph} graph
     * @param {string} filename of generated file
     * @param context
     */
    static async exportImage(
        graph: IGraph,
        filename: string = DEFAULT_EXPORT_NAME,
        diagramMetadata: DiagramMetadata,
        context?: ExportContext,
        styles?: Map<NodeTypeBowtie, NodeStyle>,
    ): Promise<string> {
        const exportComponent = new GraphComponent();
        let graphCopy = exportComponent.graph;

        const copier = new GraphCopier();
        copier.copy(graph, graphCopy);
        exportComponent.graph = graphCopy;

        graphCopy = new FilteredGraphWrapper(
            graphCopy,
            (node: Node) => node.tag.type !== NodeType.AddNode,
            () => true,
        );
        graphCopy.nodes.forEach((node) => {
            node.tag = { ...node.tag, forExport: true };
        });

        return this.generateWholePng(graphCopy, diagramMetadata, context, styles).then((image: HTMLImageElement) =>
            FileUtils.saveFile(image.src, `${filename}.png`),
        );
    }

    /**
     * Exports the graph to a PNG image and opens print preview in new tab.
     * @param {IGraph} graph
     * @param context
     */
    static async printImage(graph: IGraph, diagramMetadata: DiagramMetadata, context?: ExportContext, styles?: Map<NodeTypeBowtie, NodeStyle>): Promise<void> {
        const exportComponent = new GraphComponent();
        let graphCopy = exportComponent.graph;

        const copier = new GraphCopier();
        copier.copy(graph, graphCopy);
        exportComponent.graph = graphCopy;

        if (context === ExportContext.RESILIENCE) {
            graphCopy = new FilteredGraphWrapper(
                graphCopy,
                (node: Node) => node.tag.type !== NodeType.AddNode,
                () => true,
            );
        }
        graphCopy.nodes.forEach((node) => {
            node.tag = { ...node.tag, forExport: true };
        });
        return this.generateWholePng(graphCopy, diagramMetadata, context, styles).then((image: HTMLImageElement) => printImage(image.src));
    }

    /**
     * Exports the graph to a PDF image.
     * @param {IGraph} graph
     * @param {string} filename of generated file
     * @param context
     * @param print
     */
    static async exportPdf(
        graph: IGraph,
        filename: string = DEFAULT_EXPORT_NAME,
        diagramMetadata: DiagramMetadata,
        context?: ExportContext,
        print?: boolean,
    ): Promise<void> {
        // Create a new graph component for exporting the original SVG content
        const exportComponent = new GraphComponent();
        let graphCopy = exportComponent.graph;

        const copier = new GraphCopier();
        copier.copy(graph, graphCopy);
        exportComponent.graph = graphCopy;

        if (context === ExportContext.RESILIENCE) {
            // let graphCopy = Utils.getFullGraph(graph);
            graphCopy = new FilteredGraphWrapper(
                graphCopy,
                (node: Node) => node.tag.type !== NodeType.AddNode,
                () => true,
            );
            graphCopy.nodes.forEach((node) => {
                node.tag = { ...node.tag, forExport: true };
            });
        }

        const exporter: SvgExport = this.prepareExport(graphCopy, exportComponent, 2, context);
        // export the component to svg
        return this.generatePng(context === ExportContext.RESILIENCE ? graphCopy : graph, context).then((image: HTMLImageElement) =>
            convertImageToPdfAndSave(image, new Size(exporter.viewWidth, exporter.viewHeight), filename, diagramMetadata, context, print),
        );
    }

    static async generateSvgBase64(graph: IGraph, diagramMetadata: DiagramMetadata, context?: ExportContext): Promise<string> {
        const exportComponent = new GraphComponent();
        let graphCopy = exportComponent.graph;

        const copier = new GraphCopier();
        copier.copy(graph, graphCopy);
        exportComponent.graph = graphCopy;

        if (context === ExportContext.RESILIENCE) {
            graphCopy = new FilteredGraphWrapper(
                graphCopy,
                (node: Node) => node.tag.type !== NodeType.AddNode,
                () => true,
            );
            graphCopy.nodes.forEach((node) => {
                node.tag = { ...node.tag, forExport: true };
            });
        }

        const svg: SVGProps = await this.generateSvg(graphCopy, diagramMetadata, context);

        if (window.btoa != null) {
            const s = new XMLSerializer().serializeToString(svg.svgElement);
            return window.btoa(unescape(encodeURIComponent(s)));
        }
        throw Error('Failed to export svg');
    }

    static async generateSvg(
        graph: IGraph,
        diagramMetadata: DiagramMetadata,
        context?: ExportContext,
        styles?: Map<NodeTypeBowtie, NodeStyle>,
    ): Promise<SVGProps> {
        const exportComponent = new GraphComponent();
        const exporter: SvgExport = this.prepareExport(graph, exportComponent, 1, context, styles);
        const svgElement = await exporter.exportSvgAsync(exportComponent);
        const expWidth = exporter.viewWidth < MIN_WIDTH_PNG && context === ExportContext.RESILIENCE ? MIN_WIDTH_PNG + 96 : exporter.viewWidth + 96;

        const DESCRIPTION_HEIGHT = this.measureDescriptionHeight(diagramMetadata.description ?? '', expWidth * DESCRIPTION_WIDTH);

        let svgContent = `
        <svg width="${expWidth}">
            <defs>
                <style
                  type="text/css">@font-face {font-family: "Open Sans"; src: url(data:application/font-woff;base64,d09GRgABAAAAAFDgABIAAAAAepQAAQABAAAAAAAAAAAAAAAAAAAAAAAAAABHREVGAAABlAAAAEwAAABiBnQFxUdQT1MAAAHgAAAAIAAAACBEcExrR1NVQgAAAgAAAAFSAAACUEqpTVdPUy8yAAADVAAAAFMAAABgc1fjLFNUQVQAAAOoAAAASQAAAF5e+0M1Y21hcAAAA/QAAACyAAABDBlZEyZjdnQgAAAEqAAAALoAAAD8PT8syGZwZ20AAAVkAAAH1wAAD5TiGZ5aZ2FzcAAADTwAAAAQAAAAEAAVACNnbHlmAAANTAAAO3UAAFgaAXCqTWhlYWQAAEjEAAAANgAAADYbnTS6aGhlYQAASPwAAAAgAAAAJA3IBZFobXR4AABJHAAAAmMAAAQ4SZlfdGxvY2EAAEuAAAACDAAAAh4UQ/+CbWF4cAAATYwAAAAgAAAAIASVEKVuYW1lAABNrAAAATAAAAJ4P9tkbnBvc3QAAE7cAAAAEwAAACD/nwAycHJlcAAATvAAAAHuAAACn4X9e+l42j3DsREBUQAFwH0nAihCGzKxDnSgBJEYkEAjkOsFUIMfmZ0VlQ5gpNLQNxBDYzEpYlrEzEJs7cXRWVzdxMNH5VukqKH3H34moww6AAEAAAAKABIAFAABREZMVAAMAAAAAAAEAAAAAP//AAB42mJgBFQ1BhwNhVEYfs6921ZVUEBCAIIBBCglxbQwSUJrVbe6rbkqxcwEBpLJECCBfkKAot8QAhDVUoAC63gR+Xie97wf5wB9jLNEemY2XyQqnyYx0XayuUcUba4nRHHpsEJMdqNysM/SVlIqsxrvbJfYig/KMXHlaD8hqbo4OXRSZ5Bl1ohoUOXE2aTl74prbmmDZ5ueKo5xAxgpICRNhqzmARExFPvEfjJyg5R89mf75wYydTJAQI1z1NBG5hKZFin5ArpdtJ3AuzQZNVnUEKIOY5eAhGPMfKLqTrljjCbPznUenTW+CN2r+sMJPWoWMVbUQK9zrttx5nnB/7ojzjuMoq7mKOAt2kOk64YxJD4ROD/5BjxZaAMoMcwoSvxYAKSZYJI5FiiqP8XIYcqgWZfAeNB8L3YImSfPh1zglcD95szz7ixgGCI5cf4X3qROUQAAeNpjYGGZyDiBgZWBgdWY5SwDA8MsCM10lsGIaRIDEmhgYFAHUtxArAHiu/v7uzMeYGD4/5ej9+8KBgaOf0zdDAyM80FyLB6s24CUAgMLAMQ1D8wAeNoFwbGBAUAABMDZu3+kkAAQKwoAZKANBSnOjIiWip6KpfLePU4J7+PpEXJ+rG8ZKRo6FoKSgR2qKPnLh/JFVZSMQfgBpagHzQAAAHjabMuBBgJBFIXhM1OQBLWRlZwGEaACLYCeoN4kAehZEoAiiqAg9AolySUABUSwexsDyML5D3wALIACgMivCS3i65uA/sUAKphDTc9M7MGe7c3eaVlimRFjtujY4YAJR5xx1Xau6uqZaqoKBEssvB3/2RobbAbb5zDHGv3oU0961GnWzZC+05c85CoX2ctOtrKRtSzlt7kPpjzIeKB7f8e9k/d+MD5heA70AdkAAJhRSnEAAHjaLY2DdQNQAEVvreNglWKCzFL3qLbtNrbtAXKUVcINghd8/8dZI0szZcyDPW3BCL26dhPdXXOvK9ykdwOIEudPM8yVZkKvPTy8ExNSZYsn7GzwhoMjgrxOrHEjxC+8zS0HExadR0Lcyikgt/4f2PBIHSOLQzlJlCTV1ThNN19ivISFBtSa4ZoXrErf44J73sWGOOWOZ36H77T6DjjjgW91Hkoljh/sRGYrU0czixh7K5PuqUgfv3FJKgAAeNpy8Jo5Y/q09rbWluamxob6utqa6qrKivKy0pLiosKC/LzcnOyszIz0tNSU5KTEhPi42JjoqMiI8LDQkODAAH8/Xx9vL08PdzcNeUDjVaHkOnJFu2WG2fJ74WihVR07oJkwPKpd1XjsoYUB74s0kJIs+1GYOVmGTmH4G4KtoDcF4Y/JV0zO7W57IFnwXEl9z+XbMFKn2VjmRavZl/1pc2WZFc0Whq2VZa6rfV0zoH4iFDrajYPtvXiw5gdB4stAR7rcHdCVTVQ+FyRwASvYwsX2vtzePYjFQKVGCGR0jrPyKwuZG2mvP4r1MAR3hl8Hf4bduCDenIul0GxHqUnBSl3gkV9wM6j0f5CgkkTqcSgDGU+hW9RZOxilfYza8xEX6/AoZh02xpV/Us64Gx3EWqS3kg1oM6+rDe3P2MfkN+w41SIXQle7crwTq0DzVPqO34sl+MxXgQxEksxO/n4/acsAvjy2Wkj+0m4R8Zf2D2KE0uKlUfw7j3v9dDUp3gVZPBNMRwb1CCWQGEEM2+bbe2DqRt+fRUw/ZaRlAxg+RxWEOaVXIsZZPvMs1rGBehQIEg+SspVEc+0ysLrFnrLa73HadUg6JHmFeZxpI7S/gtHMRM1KVI8aUdtb8jAXBP0OyF84Yw3Oft/mS9wv4HPPwDP+VNGIfGSrHQTNp6BJ2FMLDJmT2hlHiGcLf/K0gicP4t+3GfybOzRW6beyPCi8x0N5uqx3Y8zeoOCPhymWNrGl7kBgWetoPybd1A8SULK2skyrS8Ry6sukePOb1RcGRafT31Z9LGSsNbPAiqzaS0NllxwtNNm5trIMn5u5HKZQkdg2oE1A+U2R6nEaYig6QwWxyDPSZm8tvFK34OUuf5g9jL5V27opp6u6JVcXkkfYI1ZSJUlNrmr+Vtv1gRyIt99VuRwLFLkT3/ZvJZkWqEhmuixX/aLMVrFf3s5R0qBgj4eobRtr8Ilw5zAWphlCqTVRROVelmfErwXY98qJ5NpacsZiIJSOsjyFxiAhDbiTAAcyExN0GeWic/sSw4MDshkdxKo9kROJDkeRylC2L/LEV0luOg57pMZWliunp5M7nDza8938Fm7YFuNUji1Au/MidvsicAtaZzG5ReHMk5un2pKDCTToyia6hBUXiElilwzbwXS+hhI/oyQwp8a56lyfc9xxYEBK3z7P3lmwQ7pSdO39uAz1BDkO9D1ffyYJFyqZfmoslOjIa5JuxnidrlRXMHgqz+hwqtLaA7AFQMRjrGU4HKZqvuJgVu4tIunPhedcSqH5KA7giMrRT+2INBFpChS7J/CFruApbmW0uOjY3bH17ODsxyNT+7BlCe0hXRvFUJzKwA8IS0xfaWYo9BZWSKyZr5RUmidAhlCG+56u9jbpAfpCKLMpJpHiiWxKtlBTtjvkzR/IIMmmgKmX1DicFmO65UrC23EaohOX1GUlriqcWscdBqX8ZhrKQHTEUJipznxwaMImcQkcWcUGdqyxN9TTnw2L41p3jjj6fGiV68YrMtuL9c5cpUZEgy+G2nvbFQipeL53EFPzq3QqkHgT7Y2wqnyyFtobkRhk7TfJ1HeIMwOS0C+g3zzfls3XBq0aahOhEF3vYqJ1GTlARkTlzH3SGEk7mxKl6wrAGKGEldhCUsvAcEoyFASiAmjmo0zS5c9O/rYT6yiVdCUJwoMQiCysa0VD264qhP+3FSRZUItok0o4BzcN1SgzkrmSKucb77qHrIhzfbOtA4zIL7pd6fbd1Nd3knBiraruBBcih4rId83bxiF2gwxqPkAPYCb0fqhyW9uLtqtb9nTASPChZENdmQ/YW5lmcoPTjQnN5Yb2wC5G8nce43V5hR4NeaXweA2nPR1GnaW2yqXK04n9R40usyv+DYioGQ0imtuv0dE0iit+OTFLpqe/HrpVTHdSmMshgXFt3sk6ydRCSI00xlXyYu9QqP9fK1V/Y8HqbjZ1g2ToD6j+2qFKdoK2KAAenvW8hYsetKeVoqOtOL6Pdmi7dwn4ZaR2Vddw2SzRm+8glR0KXQfi2FEMHWA0baAWBB3o/t0u7RaEHWTzd99qgWYnJ0jMabsmIG/qA8iJnbVdnRAnGA3pSqEypMvtpBZtUaILp75zj6SJzgvlwhn9o5fWo+MK3sY7cNmvIGJPdNCua+gkCKmCV9cKXus5hQoAZHlNqdb8/NecXu9YxMzLJUvURUB/F/OBuV76/5L6RXTJwG6Wl9zTgm47NPu61af3lwx6DVoA78f8fvff9sxBF87VbiFdOo++Xdfn516TTuW57bxvt2hLO9uL6Cj+LlDq1L+1hyfHs9IL6PLRNxsNaxxu3Yvud6GnnyF3uAtxN9PlPpeZTlMai0xAG2QOOaUygBkN7WfM2/EutUdvx/gCkB3Bb7Ab9mNIuu+MWCBkfMO/mmiezk7+c39ijiqI1ukaKSE6lyBS4rLmmX7etNfJpMGkQN5OCyOoKGX1KPu2p7b30QT6Imte8ZsIvvjA+nn4WmJB9prLGbsjvxFQL2bsU/KbAWCphTiKoabZjD12f6KUwJ+kr6mbsb2TkM/YB++Hv4TeZRb6D9yfyHNAGw5Qx+zkl/fL4Gzcpxdxv464NFLzwDN2+/+GpSXHD3G35AdG94pm0iQCyMVWR+pABhLCBym8y4f4++4nNZPQjyih/wK/JnnXAAABAAMACAAKAA0AB///AA942ox6B0BT2dbu3vuUVAJJSEKHEENgUFqogyIgHRQRUVGK42DBAhocsVeaXVGxl2tDsItivfZep/febhmnOPUfJce3dhLKde577x9MYw6rfutba+0TRFDT869xDdeOGOSMtIlSjEUiJ5mFEAsKDo5QmueGhoVjA2NmIqPNEVqNK2/wD8CpU0LufJJijkhNjTCn4CbW7+mJtPj4tNT+/RFIrGf2kxabRBFyaefwQpah0iI8HkSALMbA6OGB4wPKjcTfWG7k2q0/ERf6gL+NRIj9Hf7WE/miuERP7OWlQL6+7hqNSuXMisUymUIh1akJLxWj/ub+ZlVcqFkJPw8j6DM80R+qRK8BJWrbI0pve5gZ20OD4SPzdhL2FO4X1BcIj4bU5gpPse8A4d+4T/7SfGzOr8nHko6vcXiy8IhZIhxaIAzGx+hjAR46D7cLWfQxTziEh4KHC54vZXW8CvVCvVEUCkx09QwJCewt8erjxGNXldIoFhsQRqEPzGAXPKt0cTYDuaiAAFOUVqtThpCoyOjomCizBj6J4LdKHwJRFmmU0dFRkfDZpFTAL7Rawle9kZz6fnnN4WGvHJsxe9Kx7MiUHYPrThW9e2XOrNCJo2ZnjzP3GVv9JunlvdsvjOAzxtJlY8atHcIfPsgE+S33DBAJ2aYRi8c3tqk/xBXy0kGxGVFur+N6LiTLHJ8bgzg07vljPoa7h2RIB/E3ITPyT1QGeoX4O1k85CoeeXrKEUcTGRHqcj+if3DEfZrPTlCAGzp/Hiw1R1DDjdiA1f+X/4dXN21t2rpmc+PGvJycvKFD8pjMjncameBJG7Y1bV27ae0mx6+5ez9++OEvv3z44Y/VLS0thw61HGgd8zQL8AK//+CXXz748MeZB1paDrYcPNCCIBeVzx9zd7gHyIjCUF8UnOgu8olhkCrMWRzAyWSoj1ZmcZFawhFDfaDgjgvVxUFGbE5QS0UYMmHm4dVgUhCwO8CWG3uK1ApidyAG21zR2fJDKidvL2hZjJti0twO+I3NwcYvTiWua6ta8XDaoDVloUMjRzW7D6qM31HTcGmAsGvUtCE586aVcw+yV7y6+GSWMCOx5dWaT4cKXxyPLwwvOrFo4qYZsZGT5mSVrcrp+K3PiIpBr60cmcgGlxbkLByUumh47hiEaXXheFt1SduwrbAegAOOinIUE4LrhggXiA9c5wLXObH0OqhkdaRKBR6CJyqVzhBAyJCkunE71zYmLSrdPq4uiYS34ETsP/uiEPbbLuGO8PnCmdgT9wVpCSBNCtKUVBqxcCg0GLS6EiIyRKtUUZGEmMxalYpIk2rHbx+9KCl50Ss7x9cmEXOzcE34unoB9scxu/bgvrjXgjnCZ8IVamEhqWOlvCtSIM9EF04iYqUyxgkTuYwXL0QoFOjClhvGqOPUIoYxqY0xHEOWB+DlHkLdr7tP7T/ws9DghWtNvKtQVXXUJJwvxGXCtkKcajpahVeChvHoWzaQvQyIVpyUSiQcj0LNdoIAflAalMALSjNZgbcJZUuFMrxtKaNtEEbgAw24Ff46X/gfPBv9gKRI3sYtxGBShAfE2qhTgNcJJAZPcVLFJOnnagL10h90mfubMwcfP5xhiz3+mCSTqZAj8XHb30Hko/SaIfgn/PH69XCFnXXRv5AM4slIZJAduCamB8c2pYSb09LM4SkTupgVIw1C5ANb7mUnIfUYSJCmAUqNfGD9fC/Rc+20PhBB458/ZoOhFlyQH3I57e3EWTSckw33Eba88VQH4FpFNao4VxVFNs1jgMGfJ2xw9PQzi/a8U1769pF9707o+Cph+bRpy/snLKuqWtaPfHJC+OfVsSdw3g//xnmn2oSjj+8LP9XXYZcHd7Dz0qXCE7D0LFj6CCxxQppEqUwq5eWsBcnBAMoekAC9QUlLC2pNZMJm8mg3Zhjf6MDm4XjFMlYeX5Bu9Oujr5uGw0HWWIRYA/CSN9IlyqQqzuJJvLwwFQaEb6YO6fVROAG8ocaLTAnEbK9nKGSNnjV0JOP6KRsGrZtZvrug8OWl2wvrr08q2l8pXCXfLccrKw41l81dkpE8NTynd8mRmdNvXZsiXBPRaBdBDD1AbwjyTXTmWC8vIMBevTmLWu6IJBAIdCCKUapLY9cJcQVmdzBdgMEQBe+i7aaBbdE03sz3OQ1FR45W/3Zp+o2Ro94tu33i1bOLG9dV7x++fsGghVNjhw7+tvHzz9lXxqwZpRJrTzYuuF5pDN7Wx7xzWelrMavSFgyvnOUXa7QkjLyPCPQdxE7mLgJKtdROXgs4x5ioRKyF2ANOzQTUU9gbbKDH8EmvjAQIG2iM2Mkf7bb2I2cOfyQsISyDxepwd6EfzhcO4/wVzGcdwfjTmMKBaf6BhQbrIohLDcTFG3KrR4ZEpYe7u1bri5DYx5O1OIt7RKazF0e8GJfozkSpaZeOjibmDR/PWvxrq+X6gMzXx1Q2F7ddSVgfNjSWvGc9bQyYycwfe3lp0wevBQbu6B2ZvraicaerPIYc2SSki5Xg/3SwJgyy5I/Cqf8qZVCQVssbfJwsTjxnt8Y+JZgp/SUwEH+dJsAGc5G5CypgXgjBEQ7TgN99yHTjiE1f7ewzwd9YGnbunRKRzHf00lELL5VPfbR60snaLPy4YW/jurL1Q0yDZ5NpNR0f7C2WSFc7yXcL3x2OyAubcHPlxo+rh+x+IjQfObbmQP78pOSG6WkQvWLIV2+IHo/UiRKOEKarMGi09Bg4SYOxPxnR8RXzyHqI89m08OldxNgqOgj89AU89kUeiYpgN3E0iwwuTpZQxDnC3qOZ8SIfQl9eaGMM7WGOyUJB7K1Mq7MVD8kAoA1cnFuycUT1u2u2fjBt2KqhmbPSp924umPXpv05K0p7FS1KwX1nNGXOH5QzI2UDd2/w1mmFta+agic3ls1pG118dM7YDZONgeOWjqrcmveseeratTMyK0f0cgoonsEsmTYxpzzbz2/gxKyJU5GNob5jjd15U6oceeMgb90ocuDWVcF040cVY+juzuYIH7sXDmSFENZoLNz0zc7gCoOxLOz8uyW83OeVZUUL/l5e9frKySdrMwVV7b5V68dtyIPEMem1HR/uLZZJ1zg5/w1rDoflhU+4uWLjJzOH7PkJjzhydO2BIfOTkxuq0lHnpMyOofMRcKrGWWRxMPeL3K38bzz+Ip8zFzLi4zNSEhJAchJ0moUgmdaw6rSrQmTB3EIpbYFxILu74/zHEF6kUMUm+c3XBBqk7inhUSkpUeEpP+gympszc48fySBPOsXb+uAvbBD7PuJBOuEwwzI87UoPbQ3WiKMwII4N6khgrnb8zCh+wa2l0KeE1UIb2FaFr7DezBd0moc+iBHLQRN96OhoGB5VTGvHMKaVGVlfL4yvr39RH4dZhjCItl67PjW2KRzPOHc8AYUJuA5n46r1Qq9SYRjo833+NdPXhvNgyve+vgZDkMwiVzMOTJjpQmIDdDfjUxjYMQ9DmuY/ouTnkz1g0KjAutmvbkl/ecjlGVfe7luRnZzmcTw5Mjw1NTwyGTdFDq4qi00aVZQ0oTLCUBccfam5eMmUfrEFhQl64c0ee82M5+n8ae4kSkK5KChRnZmTo1UbExiLJDXVm+PkC0OiwEeP+9DfwEo6HcOLDbuEYtfAOyZhapyO4bshS3oZ/FnbTMbS8VJDL+xl6829zBGsCtunTrWNs3j+dPCIJYXFFTJJwLpJ21omXastPBDhisnAmAGjwrYIT44dFZ6cH70Wu9+d/WXhiA+XPBFa26u/wEl33sPZf3/2w8SS6VPEcu/8PuMXvEw+LD21dtjkrNzxjw7VPar2UQlTjCbZ5Ozy45MvYcWqijvC4+b7wpcbov0XG0NP4aE/XMEpPyw5LpwVttTOPbIacxKnMdsgLvAf58RdAHyoYJJzcmJUhDAcsYgYsS1ntjKGxOvp4ofNGDMhxASIZtjik9b2tkOk72YSK5Qf5D29JFKdVnwEvy6EcReeppDx+IFqyAjPoLxUN8EMCLyNEHuBa6d6kJGyhkSsVnt5IW+V2OKJSBfb27uyWu/H2kChcTAhbH52hjcolXpHf76Nd2AIcdLcUaPXD5339TZB0ordLq07eqkDh13bnbmngWvfe6OgfoRS6jx4TWX9uVJmwPxZ8yqsq62fbF6cP6M/wmgeMPM9ilhgBeQOeHCRdE1a/gqaV+1f82pb9pi4WVfqM6q+OTTm8JDBx2Z9JryzobgNcy2VD/NT0pq5qPztny4/IzxZr3df7+N9Fg/5dj9W357SO2iNXygiNBpcHkRDilxBswtPLEpO3GPGYw16cJNViSIJddnsB5efFA7+IlThs5/h5KNtS6ynf3uEQ3Ao+dcfwqlmrn2PcOXJDXDo2RT8EkKYamDoNi5D7olyXiLBHCEWKRZ30zNkFZRoHA/m9443mRDrXFJq/RuBQ4UmwbTe+lVPSRJa052SbIK65Bg65WBLq01IsXUPiLD+c4M9xlwsxFhPLdGD465OjMWLk/S0BOuVXSF2RJiWTFcCwL44/Lzql6avhVu723D+D7M/KknKuzah7Xn9jKc3Xl31snCQCNZ4U0A7zvvnV3jI+T5B6w0R64Wfz54WnjTpXPBlhyecV2dMxAzDySDw2B74nr4oIeLwbOa8Wq2otZVBreS4NRf2s7Wk0iEHW2yzvPwUBuhSATDM06npdiud4xF+XiQswBNtW5f8pBMUFCVtuIbpMUrB5QBtWUSOp19WcGxJ7MFRzcIChXytVNZ7YjF35Gnp0pOFdm3styBJQScHhogVLOfU0266BoPZWGQzG8gVmzH77R3rk22trXEz4/HBW9Zz5NYK6w1wwJS0KBm/Y63rzOszkMshRTt4wSC7PLsfes3tVrIUfPlyk+NannqjQ16JzjK5BrNihZRYdC5i2x91WYENUK/UDgw/SvuiYMBMtqARszy7owM/EuF3nx5geUYsOHOKoBnLMzrKuPZniezlp1nMgaQlrwU//dWRqf6gT00xR+QStQwclnQ6HGd3uFsXzEeQrf7XO74SM2JuxzXGQ0R4bhd7P3zeysyOM6Ag1rBoQyKTixCx4fEC7FdK+36lYCxaTvLifhXgQCLbvWC5EIpI7sKwlqf7Wq3N+etxn68+wEGbn02f+FFL6wcTvn/W+vEk8kmL8N2tyZNvY23L53jIhXPC0W/OCT+tX49dzmFP3L9R+AXZ658dB/7JkZZ66CyRyBRyYpH1rCpMKRAaEB38OXDTPn2TNBDD9Wv4ZNeJVlxxfeeK8yXnuPZPHhY3L8i0XuHa11oP9a2f2rja5qmwl3oKOowU8f7QiFmRpycnpe7ep0zb6bIIG7D//8fvMVfW4BG/CN84eTr9P9w/KXx8N0TYiLPI/yYGDyEGzsgNeScqxCqVXK5QOgNLKXpUpKrnWAxW6ruiQYORf/K76UMbBuK1N4UtwuuteNF7/2xraePaC9tXTt862sNaScZYd3Dt66yPGldtmEuRXA6Mj4GNQgHJLi7ItXegO8/28mYsclbSo56wY/zu3nvgfQhDZ4BuerIP4D6ExYv+uT1//dzQ7PdnVGzKTd5cU7xqdHj1W0tfu5UflXu8pGRRStLqGYtOluDaxadHu/ho9/hGxVcOyh6bH6rPLaspeK25sLexUd87pix14Ljc4NDyjdRSFcQnH+IjotuGiGHYbtZ18FQUMC5ZelCIZJOEaM6vqYlu74BuD1o5gG21hFgUPbCtpE75IRvxOCiI85jXcVF4Inxz8Mb1uoOgrmPQ1p+X44DfmSMdA8/dfeVoFXMSpMJ/7A3KQdQWCQEashC7Lbb8YDob+BAdPVlhb1h/OWj95QQeptF7SKUe/ho81H620dHv5YljQkJenRTPXAaZPgiJUkFmEPKE7Ot6Mc5BPi4g19mRfcdKBLJDGJOCEVElah9Gl8DEUG0M1aYWpQqzDgqfOumgqRCZUuckfHtQmHv8O7lGqeA5F7XOCYuOYpXCTe3Mi1w9Xb6jpjDvv1QwMN7Z+eWcYb07gsCwSSGFQwcHmApGjOzDLO+oCC0anmsInzyjP7Me7OTB9020a1D+JZxMzGIp5V/2Bf7F8M9M/7GbrgivCZWn8D+E+L/jKbjypNCX1EBXfEouWM+SdGu6I6KzQKoY6aA/MwjSKyIWm0yHSCoKwI7NpB+uPS24twpu58gH5JOORdY7JJSxTeuDQAqNoYTWt4hlOZoXMYUJRYkDKfoo+2aqZ1M7JIzaKmZudTxmghayOzYtfEbPJfcLF8gkG9IUp2hz5zFMwzbEOIYDMkmIxzeET7BBuMA/bXjq1nmi9ZGjCzKd2KQ7ggGfJf77rJ/Zsw5XmoUL+E+HfCQCT6l5lOJ0IDsKHmZsAOE3hZfPcf9s+JOn0gcTI5vCXUE8tYlFWElPSyNsO4iZRnowPj0af7JBWCYcIkZmW0cZ+bdVC323Q3jA1D7PsJ/fEUS1UO+Z2o5qZqnwYA6VPYT9AP+D1yM57DgyFi105jHsOMWwsoF8imONASAXhd3c0nP103oVDE1R8/qQaZMC3LwHF+Z5u1L2Knn+mLnHjkTeKBAZElUio1Hmo1YjmczCSi06JLIfVMNA232WAs3K1LX+9jLGdK9AWNS5U0SRzOsXZ/mJQ8qWlJZtLcSAtb/Prhy2Lc2cdRlnND84YClpPMeOPHbFqtvdNy8obdWUvcfE4gGrLBH+8wOirVdF4hmTS0bu3g0WHgWmq+Jdgf17IWOiqxZZNFKLSu3r6yyzcKxOJxZ1HcPRp2A7QYjMlOFUSsexj0EECKRM130HoEBEJH1aax7+NvX+8sXNfZz8Z+Jvq1f0W9a+efNG3lWYM2C+IWuc8JXwj7PC4lfS1vGu1gvF51dLHn30+etvvwvRt0DkGtki5A0M5QxG6FDXJs7R0PyVYh0jOBm3/sv5tf/eMGRzeETEkjjLxuzMtVOGr4iLqXed/fTiLetsvftyV78xF5fXXnjV6L7eCwiGUG1sFERBZ4+CUmpxQRaZ1NeXF1nUrm5uSPZiFDh/onRR0cMlnRnMUZrtaTOB9/aMgUkk6vVf/ufh8MUlfnJWvL/OXeRbuqT/scblpzdv2uiKvbAb9Dl9yMBw38n++PSfj7eFDw5ddvuGzxuff3fv7scOq9QQAx3ypZXryfNI4iKzuCFR9yLk6HsafVcA7MhR0RsyIn0IIQUrbk+0yphzLRN2DcvfU7nju8XVH6yt3eFMUnD8TLZo7mcbxl+5G2BsNPovenrqOGZWNBzZNmZDvp17GQXvi9wo9/IcxjoezkaxhZGCfvuuTkuBNltDVMx/pgQsIhuFH/dfvTpk29T+5YHB/gW9S0dhJXOz42Xm5uCYzGWHh2gVS2TqOUsGgxrkK7iyoWwJdNz+KBv1SdT6yxMT/fi4uKAg5KHTpalFljDk3Mn5ZnvTvwnNn1pgW7tjArqfo22J0WtEWvsxgckBEduxAWt0tGqDAzpqCllHzvAX7/97yfTgfgNSh00ac67GOz7aFc+86xP46dUQmHlNWZFX2i4J7wvfN7QV9p0+9GJWRXxTe/XM2dWWOXMF17+tHd/o5jo4su+olwzNlTOaR3ASfpK7qWLA3itiz+RgvwBl05r2q4tjxqcnD9CFjBxQWMxMnlk1Z86C2VNnQryPIsQmAQo9aLw9kMXZXWphObVa0l2DjvrrOtiE0DsONOEdLmA5mX95XXbN8cK98xbNXtt/P5RaaGCe/8u5QUOPrbBGk0tLG4pPLrZeAy0E7UIIT+C+pHeGocpYKcNbiC219PSHdpWe9/dwwf6CoPj4oOC4ODbj2Rk2A2+Jio2ByT02CiHyfI3gapPlgrxo93f1kPEWkSNhVJy6a6VxbDTqHoc4weYcL9/EwJSh+xNrnOOC7WoEV2dpvVSWU8b++UxWtjpH9EpslE2jI1K9wAcV8ktUqyBSCvBczEudnKB0pbx9/oH+2BUu+8KD7fsPHsoTkSbPC0++JqTjD24L82r38a4d7wdW9fYY54trhN7WFUQ0WwBetOvCw+Ad7WBQk9gWIdtoZcbDmps7r+GvQqWaUGCizsu7l5FVq/USqFO9yOLBw3jlLrVvQGAXuK+zl21X9uxvuvJo1tnTqwOzuYJTZ/ZWTdu5Zp957MApNfFAIxJ92dy0eS0BkugLbNHf37BGkJu1tWfarFvJzcIZ8SOPL7JeYDO2xg0KLC4tLUaduALrNBRXzryFUwDFKyUS1BNXLxpkt4K0X7sNUGpfT/UaJtexRZfessPo4XXrVTZjByDLwVSjQYN9W/JRiGBb6uJr+wFZCOmsS861e1oGXezotJrzMypO12bO/mp70xcwaaY1VlnWpaU1Tj+4kwTUv904cGDj2/UnrbV11raaqxMnXq1ZcmnCl7R3CVq2CrS6oUDK2ireolRYZHJ/f5HMonF1d0d/7V1dPQuallbb5abOn54iddtFTjz4deq9ZcNrSnzljKR5qVbsV1SbYFmeSPqtrB7XMGAjWyR8K3xzXlgcMijEVBEkJPARuyPyw1eMOFrjt/xyWcWNhVe/AQungYWRbFFXd1VYNEChKuiuIqhs6K6yv3ZX4DADNZEoHYRqjjErMdgV00VSzCqAe0hr3Z1fpz5YumR/qNi9RnCfviKJrGnb0tQkaPG7pnHGnHHQYjzOCkvGZ23/8yEZPfzYEsPbH313585nDkwwARQT0FmcABM2SIhENGTdtnSyuYJ0A+Jv+gwvjWeyd/JrIyKal6lE/qMXsEUifotIHPXq8gLrSTbjbr+xfRGm9zOY6aDhJToRK1QmLcv6uYssMlb2lw2q5w01EwVKz0Zi356Y6eXX6qsO9B9w7pVVC80zJ5Zvzv/i9KuHs2NTtgyumhM3f/z4pjzcu3x1Ri/9Zn10UW6f1CRTYK5l1PINJn2td2BOsjn5ZVNAXhX13e/5Y3KQS6MbJbXMSaNxcQbwIJHDsgdmpW2jNPZoqLTDxZg1dNCFxq4MGubpPT58+KqCbduTy3CScKl0ukK6yEnVr3ERWZaX9IOw2Dpn/DjQtQcqI5bNQDpaezon3sKwKpVYZqeRrvubuHvo67l3sbENbhL/yfU5i48VHpi7YPbaREq9W+IGmYYeW0YeWhOW1he1LSZJ9k2BeQ56bPdjRTySY3DIrsN+EgJrEQSSEqHajPEuYfSJJ+6RUpE8VvfkhFDKZljLU/ZGROwbQDY+O4MwxIbwnmwWZA/s1vjzjCLIG2DykkJqj5E9fUYmhAHDGRFjW7sIIIQB+fZ3hGpig6856dTOnIhVqLWK++eFNw/tcPHWcGLeWa2V3W275uztxolZrbfLrhbhDULmB+Vm9tfp+mflBVlrwao9fV8bH+CelptrIHOsdWETXgnwL5uZQIrBSLDShBArAa9l9MRJwiOGxWKxTGrhMO84HOhakuz0D/9OCrUXsR77XRBq8ZqLwgPh3gUSTnTCSLzf+i/rI3xJoPEkgpolINmbopf39nJikZvS2SJHfA/02j11xFRBetwBjYkhJ37W9PISixSRPt+0CYaAsWvGhWZ4aPv7J4xKdusLntXEji/pHbGngMx5phvblCcT1XLi0JEJW6hfdCSqBO1dGxtv29ik/31jYyo7viCF1gfksfUoKalgRiye00H38TjY2C7CRmUEnDt5eUulQPmuamIxIhTa8xwdQ8nZ+k9UAryhrKOhFe9qv+WtpAVILs5tK42tyMuriC1rq84qj5lYMXVCdHk27GzpqybhiPThgcuwaVg6jpy0Mq2BvDQsRXhrwnxXuXp+ufAgpSAQg1f4seBCqnmNbetCBIWGgmqYm/Hj5ct5zR/PwOIBYPHtToulEi8vb2+kAovxCxarHZbGRNGCoawR5UNslpo1tIlSkh+QNSFm0pTK8ugJ2XNPlMZW5uVVxpadEC4sI0EFKTi0fL5a7jp/Ag5LHRaEG3DaysnC6xngReCwNOHuxFXplMFIHhvE5CN3sEbuqlYzUt1C2MFcdPRbJnAnyOWB/bsO2EZUoig7XfccxMlL6WNC4xaYI+ZkJrw2InN0n/j5EWGzs/rOII1+7imRG6KiPQN8PVIi10XGedjuQArLcA1bZJvI1KcREomcRBaIFM34vFAb4Axqx0RGlb1dGXLv09SIiFT6IC3PvuBy6f2sFLifZT/lYT6BUywDcj3t4eai8xfbv6oGtj+gZmu0/22BEmkMUZ1fWiOvD1xpDoupja3alJ21rmJEQ0JM9eCF89rHlLXP4e7tY/x0yzW+oy+tXnV2lL/HWk8/cuu+at6/T57+cZHqMLUgBSHYz++ABcZElU6t1vrIZN4KBdLKLSLHPW2aU3PX9/q6aZ+eKdiO8+CVZjvKEGWm2CRHKo+l9e23IX/WfMpigUkBjFsH6lcSdevW2DPcnVD/Ou/QhauELxZ8OrRygHbTSkXKzFGfL8Dhc48U0wjPwjyrY/8GFplRVqJB7+MTqHFzCwz01Op0fr6eznJ5GMPzYWEShZMTy0kknnLU3xz6kO4c3T8uL3yGxAAWTTE6OwJidPa9Q6QTmeztTWSCvSTmP+ce/nSQ/9KiZeEzkxOnRywvqtcHm4y1I5eHzUhMnh26bFSDf9DCzHk5g+ZnZc0fOHBeJnmrf3X4ypF1BpPJUDdyRVh1UuKs8GWFdf4mU6/6wuXh1YlwURb9g+zs+TT2vhD7qVBLWtQ7UYNcYakFNLmAc0qlHGtYixw86/zqRJc/dq6koe/6AhX9wWaSgWcdFYqwWLiB44Ubu4XruC88WGH0cfINeaPDOufIHOEJdoYXhkGkR63Tu8osz0AF24vFDGIdVU/rvuMPQdHxO2Rm9vM/mNW8D4pGqcC5zqF6s0/fAYxO3LuXiwTbz4JsnOvY8f46ItCIO/Y9Gx90DxQ08jqdPfKQkRXjBuQMe3R52o5sc+rK0fMmR84aO6Ep7/9Uch0ATWXp+pxzS0hCh4TeSUKPhFCFGJoF6U1QugoOFiBUO7Jir6iMO6NOsfeu29zRmd0ptnV2yjbHebN9nuO8tn3I5Z1zcnOJou/t2u718N3T6/9/52s0zSr7w4ddJ83xPYPmV/vjepe+tL98Z8a69hRj1dQdq01NTPTCNSG6jqoVO3TBg4G6PFO0KUOjmdNTuWhDhGZp6dZXI0N2qXS+daYYU1aUrrJ/SXJpfpRXUOP0lMoCrXsTAAimcO7MYu6nlGHgesVNpWLkEmPN4TykcXiHKUZcmmSd1ggHjRptSgr+KWcxJCQYEvEv8QkAAxZgdmceHu1eIAHkED9teEC6grFEeQ/qAbXuYs+1jdLJhUdqJfez2lPljXiWkVzXUqVKE4LBhAjvgu4DMW+GqTwsPLnT0XkHer15EHrc7+m8IzwZun23+bvrc4bS0wwt+rXb1m42dBsTF7UNf3lhQeOZx9u2PT7b2HD2sXfnbfzRYeh1u7PrjvD1m28KT253Qu3fHwzf6w0N6vEPuPjK62f8VSvUgTebzj/ZvuHfryykz8eXFwIE8hgvtIx7DwQCHQjBq3JkpGcQ4Dj5TTVgxALqsUHA4GBJc6DHOFjSvCYsaXDR0Pp5PjpLkbkxafqupZ1VptS2hFjj0Mctq+aakhfVc+8t6xdMc2LT4+bm5tUZWFafnxbk2+gfJrzMsuaM5MT5FQCBErCCOc38GPDABUSaPZXOzpzcyYl3YRRAxiNIKRV3MTnCIM0X0AdCGdTYHiWwsl/4Aob0Y9LxDljbK/waBvcKx1Ec/Hae8JrwWj58OWDilaxMUMZGMmrA4V4EIUvjtxEn6JhlIzeMfcCorf8FN24aH7ezFREP3AEArNRTXIAaxAATsUw6O7kyQA2xCdSiAIOxPg4dRrSwY08zndO8IhjsXU8S/exez+lKumf6C5oGucMH5pufHPnJlo6Kxpq6peuf04HefKarwEBm9bfWN3f5Cb2E9Al/WT69KO+be8/rQK2T+goc/wfvzfL8HrrXQFCy8LL8N39inXnvtWQt6GXmob/henADAZjH7g5uqp3s/QijxX5j54/pHI1E15p2lZXsamncXV6+u76lZWHjwpZmZl7x9rr67SUl2+vrthdbluJyLq2rWUqYUACwMeyPAUNSMitdWRcXuUyhgHRXg5stSWTvJdHd3ARxtY15R2iZ4K5u3MgEOLBXDUwl6uY/Av6E84MPuwMKj368Vv0Wx0jL+lz6jCwMdVvfelJeE1ack9WWnJu5rXpob+riolPwFaZy0R9LW8v1OTP00bWGzHWdeZaeWUcAgEDD5KPt/EfUB+PMy4KC/P29XQYiAE7O8FuDtIukSYgsSccznciRdDBkV+wcqTWVNZ6+NPtbWXlbTEva+pf09y9qrjKbO1LzTNv4j9a0VLZ78e4vVfTv9Q9eHho5Z3Zhfnt5brY5NqI2PuVZ6zx0cn7GOq/CRxKym02Ff/DOr6yK7AgtnOnLh3kHldWWBvlqF3cl0DOw4M3m0j1+mBkvikqW8fW1Hc28AW/nkCfhX3rHE5pO9Cg7HNNyN/rLwxtWl1Uvj3JiWHTs2OC6VTuyyFltf0pRdEqC74zaZOEib7RmDm+uv7wOZZPUVwhjbD21YXlew4QJxoOzLZHpepHIiX9PTF9JYWx9x887Q4o1q7+zcNm7wyhGGOPP/r2CyZfLm39x5cbDBoBH+iiaDtczs5EMeACAiNcmUPCGpwEAauB+1ctdwdz0BHrcv2ktPdNMutQkTCmEp0d3h6R5eGUGzy5dXFHbkYK5jsKvm5fK2JdYPs6YapBf6FTETCclKGQq4Rf8LeACPM1ypQIfVNCAC+4Uv7VNGA4MYA38YiNikMoYtDQHKvlbY3tQSuS0rCCvGK+iBkIlBrmMF7rBvY/b05eMRYZRy8lYFDfYE1teaj6SehO6cXbP1vOn9jZvL7aO17W21te1LmxgO4585/LJY5fLXlm2fPXq5QMrBgACRXiGPinO0Hh3geQKFxngWUap4JyhE52eaa6hDsJUyNkezEnheA8MFn7dA2uFDuFUL3nvhdXIU1iUD1tgU56wKGDilcytOQBwNga3BwgEGpBiDlQFB/v6eoUHBKg9GcbT2eLi7CpnWTcI3Vi6rXG/65NOH+n6dJFUS47rkHGoP2wBSrbxelURKjy4jfQHUMYs2zW2e/pE1W5GfxaWwX1bBweFNqhEjDLLhzjYUIG9vr0LG74zttd67V4LyrPetdyD034StyzRryEcMIRpzD2gefcE8UBr9vaOjQ2M8HK2uLq4KVjWHUJ3hxzfNdCcJtlyakITOX3R1KN6wHTsHNuthQwD1Uk0v1vgfTwZhZdmm1qTc6Zurxram9ZOJiN7pgMSE+LVE7l+3gzVD1gQBgAfhc8ZDM53GsgG08whiRkZRqPelJpqSGCYBGeLLgpvgT2UbJg/hGEcLYbB/a44ez23+v+vKSzJcbJ7btMsIGUdeb9gfVbeVjzDDdhnuGVpeabtDTtGakylTWeYTHtrLYIKhnHJtLVWRkDw8rCI6kJp0qtJSFnbUtHuJXNfVNG/z/ru5NZDIFnoZ8rZBsqrVJvlAGBepQzzKnHfth8fIW0I6Z6Lj8RYJA2W7HiwRGWEVlmn27ic0irf6se0yo6C3Hz/i9nJhFaZnI3OGEt67LTKpPCnaZUwQaRVjo/b2IOyXUgLIgGAMpALo2EHUFz244iH9/6UxOdg+uDNF2B2Sph3wR8oJmQSplDCfAC+TzGRkzDZEqYdGDHG+UpUoA3kiFLYUEAG3oeVYAigcQEAfh3lo3iBWLPKSy53ZxQuDP7N8ZhHASFnd6vjnaheb7u1JrLomAgoMemI14xfZ2UF2aML6DzzsfUcirV+gsq/KYLXuyk/UiTXwQ/RdZyf1YI34SDi/GhpflYgNQA4nPLuaM1FibWroiX2RFKJJ2H64NkXYHZKmHfBRy/AZEuYdhBGay5MhSZqbvxPAMDNNLU4W47G36QxKeBETFcAQLMcMH2g5VnM+H2M6SA5EjHvjq+aFA+PMVNl2RKmXXhAc+TiB+05AghkiDxKVxBIOKXOeBrw9pa7uyKLSu5AIhItEKynykatJAYHjpIWCbXSA5Ms0bpHMPf8FeHUn4ShV1+FI1/CHmHbO1APE9ZbKbnyMaFZos370P7d1r/vE+QwBuoI31JsKW4a7ZuJtr4JM2l5vBUT5aGsM1ovBrE136YYzGx5IaYP1VBM1CTMTgnzLlxLMfGTMIUihuSnnGIMkzDZEqYd3KX1mxJDQbR+IRiG7qyCHQVKEGn2kskh46REnJOCB0Ch4FmWMMPxQMD/iNZYeuYRV9gzDcIh2NwoHBAOz4fNwqH5sIkdxU/hUDNsEl5vJkvrfOEAYHBe3sY8tb8Cf3r/L90cGEPWVZUqPt4HL6yWkOhopdLJKTxcIefkJG8GyTzj+C6R96hRRiSyiTYbTKTV6qDtCoIGXwAkdj2JzNd3s6C1Z9m8uYTRtvh7s1u62+vnX05LtTamjczDxLYxndGIDqVsqZqg9yUl7So8sACGXCL0tqSErSV7W4Qvrm34ReFYYVQ8obkNfVTEnNNGEabb+LiNk0XbNU1s+520PfzlUv+YhOlDWooJmITZKWHehUUUEzYJky1h2sGrtF2jg+RPz4HscppapjhvDNCY3GViTCI/r42OLV8yttx4XyWPfzvheCjrU5oGac2LREX8v1AW2m8KRbBtmRs/P3zh1KkPYThEVuWhXd+vu37nHca1AdMVhbe4a+PgM+vfszZ27NkOIGWsHORDcJ5UZmVCOrQYFRZ/T5vrRnQ3MhMWocnGoGSjiUmecJqLRzzWP6OqKiOopGBme3rDya7mXSma+vaUnjZN1YyKlpi0yoq03d1Fg4XHem6vW3dzMfunGa3FOdlRIVOyEqe09Fd0jsyICH1ZpfWdX5lcPDshpaY2Lbs2z1ySVlMe2zbvm1r2+OajBbnD4+M2RhAfhus0z9bK4CEAuE71QCa2zmRMH+x5ASZUwrwLXqGY5Gcx3GcihgcfLCWIjEmIn0qI9j8C3AtMBhvEMaVNUkrvQwUAwOU6g6IBhIPVINZoBIAhOH41WwcSQRbIB0lmf7m7u282r4nDXnADb+BBuq/SojeZ7C5G6pjS082Jw00y2TNMFnpPd7IFhwxZR0YUNuQQukuKVst+NPxWK7ROZY8dWPBKWfnBhU2vLY+K79i/eMHBuR2fH//emiVlB6cbC95bsO6oLvmccPLgrdeXzpu7rRS+v3xEiTxgSifmxDwaeen2O5QTk7q4eNaSqZhBtfTlCs5pxtauxIg1uuS5C+e1Wm/aaFQzdy1BQSffmL+vnIxii+BNeEu4tqbb1mnYAEg45fLQNp0ptvu/0/bSSq0xGdMH170AEyph3gUnKCbuWQxt1Zm2Vv0LadXkaIdWHf8bAHA7Tetrisk9RmJRIF6KhZCDIx0QfVnPIMavYkQ1zcvX4go9n+bF3QHzB4wJJHkRMe3CRUBy4+dCQeIK3YVzPI8PApHACMLMXhqWi2TC+ODQELlaDeJd8eAGemqr9kkn/UQt2tipvWniohgJxj2G05F/vSa8GsQYBa/nDWU8nnm3cWfJwK9fG/liTd/Pds9e0RgqD91cK/x5xh9imxL/3L13xow9ltNv6kpX80EhBbGbW/trvxR+e/SHwtj6gc/3f/thf0BKNLP8ftkri4aD0/2s+yrf7F30eu2Dd00dcwzj4zaeBR1vZeLu8wytD90zbdNEa7VcnOWdbRjXF2P64JEXYEIlzLvgNsXEP4uh+SkX83OIYgyTMD+lGNpXfEnrxKRQCG0dBrQBK7ZfHbXZr4A3ZgoBtbvNeoU9Ie7SXTH74QFPu4zDOzFjBSeYTAlxJlOc+EQ7Nm0SOtKzso3G7Kx09MP0LLPRaM5KBwzO0Q3MJRkGviAcxINks3+QVqtWh4aqvN1lFl1goAL7MWJi5E6s8kWrPFkDHPwuye5A8spw0rLOSWt9Ctu08jcHSzam1yxtzsqFbo+Lt6RVdTZn5o390C8QfqxpysA8lFF/fyEuojkdE1OQ9sr4Ro1m+ez1s+8NQVcYFaXtn7V+1u215ZuNaDAg+Nfvl2xIsq4NCGx/m4x8ygCgrVkjtrgvbYVgF2mUTML0wfMUEzIJEyph3gWfPD8e2po1ttaMIK0ZH2aD2DDUs03TqhPTmkrj0QAxHpFZQ9hPgSAGj0dPXPv+Mos74VlZ5DodK7JWKJtporYdLWMiQ82Tsc3Y4iTNdnfe3lyzHvMhEc8eP84iWWj9+rn3/nR634Eze9COy20bc5GOkGqK9J75M32FVJkgJIW0BCWWJQq/Fh5//NGP34588PnCt+2loCWts5X0PClpkg44lFS8u4IxejoXv4cqrcts4xRZcOm0oMQWDmuso/TOx9f8Y+4O5RPFm3014eEecgs21DJKfNeAZ5BFi82sjFh20R0t1oCBsA2li1ITt5R8HO6HeWi1jMrYHM94W4+gZOsdVDf257gW42+ET/bsgbon626szFr86FRaa1R0C3dHobQd/0adFD+AFb/+NSz93mj5gUfbvif81x4PN7bP3ZWsuZ3jX8li2LkgEiSBqYQ1GabRpPK82ghAMGauxsXhNVgtthbJtMicxI/n8kYd3CVkxJgQR83dktdSQ9ZY/OQsW3/0kjWYPX9k8cGKikNLRj41ZX60bfej1QP/9sqm+xnarhGr+djLuds7O7bl5m1Hxwb3u6CEtF527spH+9pu36IL64r+vpUbxq9eHhte1VO4OH3LLz7cdmt+y40tO261oONvzN9bJlp50f/wocCT3H+Rs66M5yDv5gydsaU3yf+ej0TEpm490eL7Z7+5C3R9KZaUlI4Ui7a51p8Pje9aolNHtKamLoxURy3pjCcxc07QH/MVlbjXANF4jCvFkfF4TaQ7cr+WiI4I5+iXyJ3fJHrwPPmbzgDoJY+BNOc95S3QN69a2dI4NNicnJuPNXVM7C8trQsslpa27qbs1FSzOTU1m5b1IZPOfUbZBi5qFxfOY9DPT+HmAT1I1pL0hG5AhSbIFXe6oU1Nsl1rJyOsnA9PK9InVBbOCJuS7Nri0tkYV144KyLR6Mo+zGzIidDoNVPNvVvJY5qoYMGce46CBVNHFCxEhOx5CJmECGBuwRx6P9H1KgSD1HyCDfAYp8MNkeSKZDpnz7ScsBXe0eFK5pZ61vHjM4sunZ9BvvXH32bT+xL4W25QKX0LbXehmVRocKMfu4UE+jhx1/DXx2alD21YkzI+Tu9TLwJPgAzcA7pvUgEieUEXaV5UuK8oPNzdeXZQoUKiP8fW40nMJGsR9hd0URUdrrjo5pmeG/4X8nrB2TM1J5y7VnT53Aw1UfyQXgAieUYXaBpqkgZ0dvbgBhVqMQ2y+kjZx2nYy4HeoOVYrYoJV9x09UjPCRtwCwnwkeEiFRw/Oqvw0oUZ5MWxbOhntGwPaNkgOA2/QXOYh8ANtwTrTH1ZXhOeKTpkT6c1Z2U1p9n+RV/NeWnKlPbqysUGQ/v4OLnXzh0GXwIt8QoAHfBA5CkDHmwIoGwj5iRiRcUZBEXFGXqBYu4p4fsd+Ebg/wBEUEwrQdEbGjKAeFrw+waRM0AIVheh8pTwPWEKVOAPcawu1v8hfwEkHlLkT+3vmGnMMwBxkAP6u9SjP8kJ6uj4BJDY7pGv/Vv8Ic8BRvpWRQwJb8Nq0TrP/HjCJA8AFNYDgNaJiirI0wLQhKLKurHdO5hl0v0TI147e7nbIIXY9ZKmTHHT+frGqJzd3EBghAulq0iWIke6xARHkjBVyJ6TLgXByPEQg01JdFolJBYTYlxbz5dPT99fWjy0YLoX/Jv1G0h+3ReyfV460V1yyGz6dvPhL3cGHd8PIdp/1G/XY+52vH5TiMHQ+u3WVd7RalWCetXyU3O1gfsCNGu+OrbKLzLAR++66uTfN5H79eh33DAuEV69SUkCfHwYN2fGV4ZpN0DlyyHkK96scb9rK4UoWQU9kry8bNcxU7GPl6H8WGIFC1NFeKCRy+/ePbeChbKPWj9wQoz1qw9R0EeE+WG9i4xWPTzk0xJEXiOqvIUa9NKiRVbqp0Y72BhG/ewcgneEZAMIAAIqphJd5d8DCuBF5j1eqZTLWQiZATcZ9WY66IxEqCJEQ7vdyM6gq4M7bBZ04b8Z4uy4yIbdsxnHRcO4ME56uInJQVv5WyCY8AaCAgP9/Bie93IeQME2l6lDKqowGRlak52YVNAEtQjrvfyzIjdfOXMkd1tC2pbUxs6lC1MKQzLD+FvtX3JOOwZ3HAz0HwzTVs8tq3V30sITJP0MXMoRnD5P9dIgP8AQN5nIBKAkvsOwWUhBNcIhjPxHd8PYfwEGGJl8tJd/APyoVhe+Aabz9+YTuDDngSjR9yrKgUjMF+moTB6SohV17nk5iFrBhV1vVGdXNJ/Y2Lx5RkxBQk7+0cop2WHV1bVV8ypgXlGVObu4hH+wdFnhgtDwzsquzVOXzU6vNvtEzS1e0isEqJKyozOLCrMyywLgBlNa2kw/3xkZaSbAgErhluwU5cqHgXicY3mYBvm4xcYiAPR67F9J9yTnNznD4dxJTiLMk5IcKuQOEePg4GME5sxYJZafeByTO7VYd+Nw+vLOhPK8tanTTMl5hbkBZZlpFRVpmWWYvl89Noc5+Y3eYDLqK1tyN+8PiAu4FJ6Qoc+YkZzVVTpVeDJz2rSZhVlZZE8urj6IB/cA6YXRTB46xIu8nKvOYMCTpx3j/1rVWw4eGD188LXRY3XV1XVzqyqZvC8//9XjPz765dcDR0+fOnr0zMmjYsxv8B+CYBAH/LDahbPSzU2nHpBNOM+p/omkWSNerVJPKLuY0FNHWS18FDLd12eGtmNdBu8U1FW1fHT2lvr6PdW65lM+WGUsttpUOr+G/9CJtzgpq/fUxWXu/FbD5oKqc99q+/7uSqEoLl+X3GJqbiqtAwhE4Nwd4D8BQSCW+Pa9VZGRvr7u8oHgiex5JEkaP08zXCN4R+U3aY8Iq+r2ztE1nhiKmOmjzknsXJPOKQLbK4ZePr61cEFlTUatIaaK/6Ti9Ia2G3urnPgeufO80Zqo7JHh4RPLGurKmmPzdVlNabiNggHg3uJDgBbMoqvVLHQRAFs46+cQXugQLnMIL5kI529L4ToaD/2Oa5F+/p3n/LyE/Fy8/1HFzrTd/+Cfvv9RdewYOxPHET3+n+gQswbJwO8BGDuDQwoBgF8wS3HISjHEAADqZtbikB4xRDP+X2g7DekVQ1QYc5XpxiEPxRDT+NdoK43nMxIipvUGsxqH/EEMyQAAjVDMIzHEiGPey6zCIZ+LIRH4qwPMOhzyRxJCdF/wepdKb7gTXYtA1+cqiDlIiLl7kht/dgGxVCIgtvJqf0bjR+ePfYIVxHafPDnylH7YwlvQ9/JjWEoFxDDN+gHUQ6NdQIzc9yQ+ILuCGBFvU7IWF0cFMUcBMQ94DDFMSGrUsTlw69anBMSwkALdXwDW2a4hJvfgLP4wIAA4aoglvVhCTFViExDrbz9cWZux6dDcDT9qrzva+X8KiFHFgzm4DlU41XiydrEMYcYoI2M5T6WLqF45oR82WT5skn6YJB/2x+ObHdTDho8Xz//e0G6bfFjf2Qq7etieiwGO4mEzh+sk9bD8NCwedofMbFMoC5Soh6no+srgBdadJZwxgPmf09L1ntTpTevb7sX2sDEPqHYY+pM1E333zK9+9QZkGSe12U8oFUXDRoWz8Ev4aFZPnSZqQYR1D6mPNbg+fKhyWJjZzVE5zM1JrJF/STcsfvThwND/nOqWdMNuTturr0pDP7dei9Qsf6Fu2EyZh9i/9aL+FJ7b7PJTLi48NyEt+s9qhum0Ns2wbk3Ny795LaEtXNMw5bufNsqcRc2wrp/sWHx1eBb8auORkRGiGVbylGbYG8Ljc5M0w45DWLHWrhrmAQDzb9w1ukPgnalsGJiG8zcNd1zbDuHTSYphK3AJM6guzRRgIvuDqGgn30QuzZ0LBbi6DdMoYZZ7Wh8syVE9jNLaJLEwssZ42ccIlSRtePXnXVU7qgoGZnS9s2rwR8uKBkvqR2v6Pt3buqOgcGsT1gvLnb69q6A/f++q3tGC1d5PK4Q9rR42nNNXYlcLKy7ozUE/CylcPLu9k+qGAUTUSdmIye3FYbWwifb6Z7XCfOxaYRGOWmGfNNq1whZhrbDFRCvMa8PRnXtaR+1aYb84WqdQ7iRaYd7nbFph+x/atMLOXth1DJatpmJhZBZGGegQ975txhf+QmZ8VAC/4K6TGV8MMaAC1M19QGZ8MUSDpqLtNKRXDFFhzFXuBpnxxRATSkNbaTyfkRAxrTe498iML4Zk4K9GKOaRGGLEMe/l3iUzvhgSgb86wN0hM74YIqpLIp6oRvwvPEbGOwAAAAABAAAAAwBC88GLDF8PPPUACwgAAAAAANnMwvcAAAAA3XcmUfuc/dMJnAhiAAAABgACAAAAAAAAeNpjYGRg4Oj9u4KBgXPZ7zl/TTjnAEVQACMfAKAfBmh42o2TAwwcQRiF387+O1fbtm27UW3btm3btu02qm0jTmrb7fbNZHs1Lvky/LHvzclp7AcAFScgKWa6OTDKy4w8Mg2D9AK09C6hk7Mfo1R9VCXFpDXq8KyVioVqahr3EmGmeoK4Zo/sIS1IfZKFDCfdSQN7Tsx9UjJYdzOj2x3JQ1nQ04sOsPYxLxIGeGdwTLqSlFyf5/o2jqkiJJ1fXziXrDgWyoVjWpOCGCDngvEpz5qhDfuM5V1mP4eBUDMkk0XQ0geQ6aioFmKl6Zljbtav7A7xP6oCqMp6DeUWNrmX0VVukYHoqrYwtjaSy11sUh4WK8+fLHnsfFOoNTaZfdY29zeRLm5Zxp/jd95ACp4tc31A50YCyYH0oqDcfawHFJQWzkOOpa0mgfaBfmVIH5Lc3JEk6CvLnHx6NZqriyjjvkElG0PtzZ7Af+92QA+7dwy5SFr7Lc+xySuKvkZv5wiScL+8+oTSjK/gPUQpnRZZdCGkpPZ5je6/Qz/3PxsvjA/fQR/8l/RiO8czHDW9KvTVh59hXyPMaLz4HusFPZMP2GR0/x36NhpaLwb+CD14S/0ncdxB7sgpdAn78DPUxY704gfohfXMjHxLKj79bIkBoZnoTA+rOu1RVTVFVXcstQSqRiBOPSQmiZxXKKlOIDFJxLslXYV1uiB1vkpvC6KyaoVKpIKjP5v3n0cnQfLAn7ikGClE8ggApzrCP9aobvLzLGNAaq8pkslOWNifxby94Lx82PNwbu4H+U28rEU3tydyS1tUktGoJXORg+NAmYxuEgsxZTv6ct7yf/OZ/80Xtvv9vQB42kXBA7TbUAAA0DJ26jhN2pfmd7Zt27Zt27Zt27Zt27Z3tN3r+M9yFHbMdnqdDZ1XnC9cvCunq5VrpGuZa7vrkuu523aXdY903/GEPXU8WzwXvDm8E737vO8gBkpARaCJsAZXgufCNxEKSY+UR6YgO5BTyHtUQTOiLdHp6E2MwWpj07DXOIZnxGvj6/FbhJMwiNxEBaIVMYyYQewiabI6OY68SUlUeeoiTdLp6Wb0HHod42TSMS2ZXSzO5mCHsrvZ95zAteKmc9e4n3xBvhI/k7/0909fLt9g3w9/dn8F/yH/h0DDwLrAs6AziAa7B0cG9wZfhLRQ89DI0InQp9DvcDJcPtw/vDnij7SLfBDyCOWEBkInYYgwXVgs3Bdei06RF00xi1hMrCG2EvuKB8RL4hPxm0RIspRWaiBdkB5JX2RMluTUchP5sHxVfi7/VChFVTIom5X9ykXlsfJVxVVRTaq51XLqRvWwelV9rv7UaE3Tqmh7tHPaA+2TjugRvbneUx+qT9RfRWtFh0cXRy8YuCEaSaO40cxobyw0rhh3TafJmoaZ36xr9jYnmF9iuWKlY/1i+2I345n+HRYfF98NKFADNAEdQB8wBswB68B2cACcBJfAbfAEvAXfLLdFWCWs9tYq63ciU6JH4nzipV3BXm5vsHfah+zT9hX7rv3Mfm//SPGmkH8A4POhRgABAAABDgCRABYAXwAFAAIAEAAvAJoAAAK+D4MAAwABeNpEzAEGhFAARdH76s8wyccAMkPSEhKgpbSoFhCAkEAA0gJaVNEj4IADRBZSFD7ADLb4MtsJkc1OaTnsQKXEflGott+U6uxIo/62INdgi0yjHZ5fBbkm+3d5tf9k2s/CyEG5gSgMo+dROqw9bAe1zfHUi+BuFbx9TuxkFh9+XbBPIKPKDwkRMX/MsMEafrIHnU/xmkwsyu55FX9lN9YEUv13rGKXfzEm6BtnDrv5ZKotVn0iEsww740VqwIFdMVARJ5PvmRFc371Q2vmb2vi/IhV3Kkj++XVP2zadQ0ftjnm2udY1q5q1yz3V43cW3/Gk+pHPyFgXnfOtN7tE2jt/5d3vUTtDu1hR/Ji0I2MX3PEBc98tu9CnvAhxlxhFgW75jnVEY29c6evS7EG65xdkXjaY2BmAIP/cxiMGLAAACqDAdEAeNoUjcVBXAEABTvICVuddXd333iCO/wE/bj7iR4o43fBfWt6LKeZ00xHL43n5ef1h8os9xVxV7G4LY24KYnrksVVWVwWxUXe4DwvzgoWpwVxkhPHGZOjjDAzNxxmhhxkxX52xF52lt20wU5a/E/N8i9lYaTEdqrEVlJsJgw2EmI9YbIWN1iNv7ESF8sxsRQ1WIxaLETFfOSVuYiYHfNvWPwJid9B8Sv4ys9ghx8B8d0vvvksvvrE0Gcw8L7R94oeI7qIDhZtj2h5TJpu0XCJuvOGmlNUnbOUO+8lg0J+lmwmTjQyS2TSYdsKO5KExovgF7ttK2DP4reBzya8mHjGVZwzU1vuGSeuaeGcEo6cjc2ZylR50/5p0582aWtN6YPkMDdCEIgC6N/D9RbU2NBoI7cCBkMSEf8MHwJLMKAAImdIYOjExYguLIUSEIbkHcnxcsD9xcUDubQ1G5wZiRuS5MhcdlI4/Q7XZoVzo5A9AXe0IkU/1SkO9IIcPtCBdIEtzQKF4bggTg7PeceFC8LzZqxltX3ooJ3398Aub6llhT1HI70wsaqwgEkaN4xV9C5LOPmBraP4K2BIaji/hnEjRUV+Bhq01tngMccZd4qJ7D8kyADiAjFCDi4IpCAAwkNwESJofIQYgiMBAIj4rGYAAA==)}
                </style>;
            </defs>
            <text x="24" y="45" font-size="22px" font-family="Open Sans">${diagramMetadata.diagramName ?? ''}</text>
        `;

        if (diagramMetadata.lastModified && diagramMetadata.lastModifiedBy) {
            svgContent += `
        <text x="24" y="80" font-size="15px" font-family="Open Sans">
            ${getLastModifiedText(diagramMetadata.lastModified, diagramMetadata.lastModifiedBy)}
        </text>
        `;
        }

        svgContent += `
    <foreignObject x="24" y="90" width="${DESCRIPTION_WIDTH * expWidth}" height="${DESCRIPTION_HEIGHT + 100}" font-size="14px" font-family="Open Sans">
        <div xmlns="http://www.w3.org/1999/xhtml"
            style="word-wrap: break-word; white-space: normal; display: inline-block;">
            ${diagramMetadata.description ?? ''}
        </div>
    </foreignObject>
`;

        svgContent += `
    <svg x="24" y="${90 + DESCRIPTION_HEIGHT}">
        ${svgElement.innerHTML}
    </svg>
    `;

        svgContent += '</svg>';
        const parser = new DOMParser();
        const wholeSvgElement = parser.parseFromString(svgContent, 'image/svg+xml').documentElement;
        wholeSvgElement.setAttribute('height', String(DESCRIPTION_HEIGHT + exporter.viewHeight + 90));
        return {
            svgElement: wholeSvgElement as unknown as SVGSVGElement,
            width: expWidth,
            height: DESCRIPTION_HEIGHT + exporter.viewHeight + 90,
        };
    }

    private static measureDescriptionHeight(text: string, maxWidth: number): number {
        if (!text) {
            return 0;
        }

        const tempDiv = document.createElement('div');
        tempDiv.style.position = 'absolute';
        tempDiv.style.visibility = 'hidden';
        tempDiv.style.width = `${maxWidth}px`;
        tempDiv.style.fontSize = '14px';
        tempDiv.style.lineHeight = '1.2';
        tempDiv.style.fontFamily = 'Open Sans';
        tempDiv.innerHTML = String(text);

        document.body.appendChild(tempDiv);
        const height = tempDiv.clientHeight;
        document.body.removeChild(tempDiv);

        return height;
    }

    private static async generateWholePng(
        graph: IGraph,
        diagramMetadata: DiagramMetadata,
        context?: ExportContext,
        styles?: Map<NodeTypeBowtie, NodeStyle>,
    ): Promise<HTMLImageElement> {
        const svg: SVGProps = await this.generateSvg(graph, diagramMetadata, context, styles);
        return await renderSvgToPng(svg.svgElement, new Size(svg.width, svg.height), DEFAULT_MARGINS);
    }

    private static async generatePng(graph: IGraph, context?: ExportContext): Promise<HTMLImageElement> {
        // Create a new graph component for exporting the original SVG content
        const exportComponent = new GraphComponent();
        const exporter: SvgExport = this.prepareExport(graph, exportComponent, 2, context);
        // export the component to svg
        const svgElement = await exporter.exportSvgAsync(exportComponent);

        return await renderSvgToPng(svgElement, new Size(exporter.viewWidth, exporter.viewHeight), DEFAULT_MARGINS);
    }

    private static prepareLegendExport(graph: IGraph, exportComponent: GraphComponent): SvgExport {
        exportComponent.updateContentRect();
        // Determine the bounds of the exported area
        const targetRect = new Rect(0, 0, 500, 100);
        // Create the exporter class
        const exporter = new SvgExport(targetRect, 4);
        exporter.margins = DEFAULT_MARGINS;

        if (window.btoa != null) {
            // Don't use base 64 encoding if btoa is not available and don't inline images as-well.
            // Otherwise, canvg will throw an exception.
            exporter.encodeImagesBase64 = true;
            exporter.inlineSvgImages = true;
        }
        return exporter;
    }

    private static prepareExport(
        graph: IGraph,
        exportComponent: GraphComponent,
        scale: number,
        context?: ExportContext,
        styles?: Map<NodeTypeBowtie, NodeStyle>,
    ): SvgExport {
        exportComponent.graph = graph;
        exportComponent.updateContentRect(new Insets(5, 5, 5, 5));
        // Determine the bounds of the exported area

        let content = exportComponent.contentRect;
        if (context === ExportContext.BOWTIE) {
            let legendVisual;
            if (styles) {
                legendVisual = new LegendVisual(styles, new Point(content.centerX - LEGEND_WIDTH / 2, content.y - 40));
            } else {
                legendVisual = new LegendVisual(
                    (graph.nodeDefaults.style as DefaultBowtieNodeStyle).nodeStyles,
                    new Point(content.centerX - LEGEND_WIDTH / 2, content.y - 40),
                );
            }
            exportComponent.rootGroup.addChild(legendVisual, ICanvasObjectDescriptor.ALWAYS_DIRTY_INSTANCE);
            content = content.getEnlarged(new Insets(0, 60, 0, 0));
        } else if (context === ExportContext.RESILIENCE) {
            const legendWidth = calculateLegendWidth();
            const legendVisual = new LegendResilienceVisual(
                (graph.nodeDefaults.style as DefaultResilienceNodeStyle).nodeStyles,
                new Point(content.centerX - legendWidth / 2, content.y - 40),
            );
            exportComponent.rootGroup.addChild(legendVisual, ICanvasObjectDescriptor.ALWAYS_DIRTY_INSTANCE);
            if (content.width < legendWidth) {
                content = content.getEnlarged(new Insets((legendWidth - content.width + 32) / 2, 60, (legendWidth - content.width + 32) / 2, 0));
            } else {
                content = content.getEnlarged(new Insets(0, 60, 0, 0));
            }
        }
        // Create the exporter class
        const exporter = new SvgExport(content, scale);
        exporter.margins = DEFAULT_MARGINS;
        if (window.btoa != null) {
            // Don't use base 64 encoding if btoa is not available and don't inline images as-well.
            // Otherwise, canvg will throw an exception.
            exporter.encodeImagesBase64 = true;
            exporter.inlineSvgImages = true;
        }
        return exporter;
    }
}

/**
 * Converts the given SVG element to PDF.
 * @param {HTMLImageElement} image to be added to PDF
 * @param {Size} size
 * @param {boolean} print - toggle for autoprint and save actions
 * @return {{raw: string, uri: string}}
 */
async function convertImageToPdfAndSave(
    image: HTMLImageElement,
    size: Size,
    filename: string,
    diagramMetadata: DiagramMetadata,
    context?: ExportContext,
    print?: boolean,
) {
    addOpenSansRegularFont();
    addOpenSansBoldFont();

    const jsPdf = new jsPDF({
        orientation: 'l',
        unit: 'px',
        format: 'a3',
        compress: true,
    });
    const pageWidth = jsPdf.internal.pageSize.getWidth();
    const pageHeight = jsPdf.internal.pageSize.getHeight();

    jsPdf.setFontSize(22);
    jsPdf.setLineHeightFactor(1.15);
    jsPdf.setFont('OpenSans', 'bold');

    if (diagramMetadata.diagramName) {
        jsPdf.text(diagramMetadata.diagramName, 24, 36);
    }

    jsPdf.setFontSize(15);
    if (diagramMetadata?.lastModified && diagramMetadata?.lastModifiedBy) {
        jsPdf.text(getLastModifiedText(diagramMetadata.lastModified, diagramMetadata.lastModifiedBy), 24, 60);
    }

    let splitDescription;
    let yPosition = 70;
    if (diagramMetadata.description) {
        splitDescription = jsPdf.splitTextToSize(diagramMetadata.description, A3_SIZE.width * DESCRIPTION_WIDTH);
        jsPdf.setFont('OpenSans', 'normal');

        jsPdf.setFontSize(14);
        jsPdf.text(splitDescription, 24, 80);
        yPosition = splitDescription.length * 12 + 80;
    }
    const widthRatio = (pageWidth - 48) / image.width;
    const heightRatio = (pageHeight - yPosition) / image.height;
    const ratio = widthRatio > heightRatio ? heightRatio : widthRatio;

    const canvasWidth = image.width * ratio;
    let marginX = (pageWidth - canvasWidth) / 2;

    marginX = marginX > 24 ? marginX : 24;
    await jsPdf.addImage(image, marginX, yPosition, size.width * ratio, size.height * ratio);

    //we either open print dialog or save pdf file
    if (print) {
        jsPdf.autoPrint();
        jsPdf.output('dataurlnewwindow');
    } else {
        jsPdf.save(`${filename}.pdf`);
    }
}

/**
 * Renders the given SVG element to a PNG image.
 * @param {SVGElement} svgElement
 * @param {Size} size
 * @param {Insets} margins
 * @return {Promise.<HTMLImageElement>}
 */
function renderSvgToPng(svgElement: Element, size: Size, margins: Insets): Promise<HTMLImageElement> {
    const targetCanvas = document.createElement('canvas');
    const targetContext = targetCanvas.getContext('2d');

    const svgString = SvgExport.exportSvgString(svgElement);
    const svgUrl = SvgExport.encodeSvgDataUrl(svgString);

    return new Promise((resolve) => {
        // The SVG image is now used as the source of an HTML image element,
        // which is then rendered onto a Canvas element.

        // An image that gets the export SVG in the Data URL format
        const svgImage = new Image();
        svgImage.onload = () => {
            targetContext!.clearRect(0, 0, targetCanvas.width, targetCanvas.height);
            targetCanvas.width = size.width + (margins.left + margins.right);
            targetCanvas.height = size.height + (margins.top + margins.bottom);
            try {
                targetContext!.drawImage(svgImage, margins.left, margins.top);
                // When the svg image has been rendered to the Canvas,
                // the raster image can be exported from the Canvas.
                const pngImage = new Image();
                // The following 'toDataURL' function throws a security error in IE
                pngImage.src = targetCanvas.toDataURL('image/png');
                pngImage.onload = () => {
                    resolve(pngImage);
                };
            } catch (_error) {
                // eslint-disable-next-line no-console
                console.error('Export failed');
            }
        };
        svgImage.src = svgUrl;
    });
}

/**
 * Helper function that opens new window and renders img. Once rendered, print dialog is opened.
 * @param {string} src
 */
function printImage(src: string) {
    const win = window.open('about:blank', '_new');
    win?.document.open();
    win?.document.write(`
        <html>
            <head>
            </head>
            <body onload="window.print()" onafterprint="window.close()">
                <img src="${src}"/>
            </body>
        </html>`);
    win?.document.close();
}

type SVGProps = {
    width: number;
    height: number;
    svgElement: SVGSVGElement;
};

/**
 * Helper function to create last modified text
 * @param {string} lastModifiedDate
 * @param {string} lastModifiedBy
 */
function getLastModifiedText(lastModifiedDate: string, lastModifiedBy: string) {
    const lastModified = DateTime.fromFormat(lastModifiedDate, PROTECHT_DATE_TIME_FORMAT);

    return `${strings('common:label.lastModified')} ${lastModifiedBy}, ${lastModified.toFormat(
        store.getState().app.currentUser?.dateFormatPattern ?? DEFAULT_DATE_FORMAT,
    )}`;
}
