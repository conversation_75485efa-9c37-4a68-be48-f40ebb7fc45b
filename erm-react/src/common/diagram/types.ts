import { IPortLocationModelParameter } from 'yfiles';

export type BaseNodeData = {
    id: number;
    name: string;
};

export type BaseLinkData = {
    id: number;
    from: number;
    to: number;
};

// base model for nodes and edges
export type BaseModel = {
    key: number;
    position?: { x: number; y: number };
};

export type PortLocations = {
    sourcePortLocation: IPortLocationModelParameter;
    targetPortLocation: IPortLocationModelParameter;
};
