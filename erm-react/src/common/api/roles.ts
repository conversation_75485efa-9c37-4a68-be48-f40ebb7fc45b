import { baseInjected<PERSON>pi as baseIRolesApi } from 'api/generated/iroles';
import { baseInjectedApi as baseIntRolesApi } from 'api/generated/introles';

export const iRolesApi = baseIRolesApi.enhanceEndpoints({
    addTagTypes: ['dashboards'],
    endpoints: {
        rcGetRepositoryHierarchyUsingGet: {
            providesTags: ['dashboards'],
        },
    },
});

export const intRolesApi = baseIntRolesApi.enhanceEndpoints({
    addTagTypes: ['roles'],
    endpoints: {
        rrsiGetRolesUsingPost: {
            providesTags: ['roles'],
        },
    },
});

export const { useRcGetRepositoryHierarchyUsingGetQuery } = iRolesApi;
export const { useLazyRrsiGetRolesUsingPostQuery } = intRolesApi;
