import { baseInjectedApi } from 'api/generated/businessunits';

export const enhancedBusinessUnits = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['businessUnits'],
});

export const businessUnitsApi = enhancedBusinessUnits.injectEndpoints({
    endpoints: (builder) => ({
        getBusinessUnitsByIds: builder.query({
            query: (queryArg) => ({ url: '/v1/api/businessunits/tree', method: 'POST', body: queryArg.body }),
            providesTags: ['businessUnits'],
        }),
    }),
});

export const {
    useBursGetRootSimpleUsingGetQuery,
    useLazyBursGetRootSimpleUsingGetQuery,
    useLazyBursFilterBusinessUnitsUsingGetQuery,
    useLazyBursGetBuSimpleUsingGetQuery,
    useLazyBursGetBuTreeUsingGetQuery,
    useLazyBursGetAllChildrenIdsUsingGetQuery,
    useBursGetBusinessUnitsByIdsUsingPostMutation,
    useGetBusinessUnitsByIdsQuery,
    useFilterBusinessUnitsMutation,
    useBursGetBuSimpleUsingGetQuery,
} = businessUnitsApi;
