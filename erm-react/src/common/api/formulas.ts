import { baseInjected<PERSON>pi as baseFormulasApi } from 'api/generated/formulas';
import { baseInjectedApi as baseIFormulasApi } from 'api/generated/iformulas';

export const enhancedFormulasApi = baseFormulasApi.enhanceEndpoints({
    addTagTypes: ['formulas', 'formulaEvaluation'],
});

export const formulasApi = enhancedFormulasApi.injectEndpoints({
    endpoints: () => ({}),
});

export const iFormulasApi = baseIFormulasApi.enhanceEndpoints({
    addTagTypes: ['formulas'],
    endpoints: {
        frsiEvaluateFormulaColumnUsingPost: {
            invalidatesTags: (result, error) => (!error ? ['formulas'] : []),
        },
    },
});

export const { useFrsvEvaluateAllFormulasUsingPostMutation, useFrsvEvaluateFormulaColumnUsingPostMutation } = formulasApi;

export const { useFrsiEvaluateFormulaColumnUsingPostMutation } = iFormulasApi;
