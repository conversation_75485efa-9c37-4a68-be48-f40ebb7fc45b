import { baseInjectedApi } from 'api/generated/attachments';
import { UploadTempAttachmentApiResponse } from 'api/generated/types';

const attachmentsApiWithTag = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['attachments'],
    endpoints: {},
});

export const attachmentsApi = attachmentsApiWithTag.injectEndpoints({
    endpoints: (build) => ({
        // needs to be injected as new endpoint because uploadTempAttachment does not have query argument at the moment
        // can be removed when fixed
        uploadTempAttachment1: build.mutation<UploadTempAttachmentApiResponse, FormData>({
            query: (body) => ({ url: '/v1/api/attachments/temp', method: 'POST', body }),
        }),
    }),
});

export const { useUploadTempAttachment1Mutation, useLinkTempAttachmentsMutation, useArsDeleteAttachmentUsingDeleteMutation } = attachmentsApi;
