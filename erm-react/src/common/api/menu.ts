import { cacheProfiles } from 'api/cacheProfiles';
import { baseInjectedApi } from 'api/generated/menu';
import { Item, UmrsGetMenuUsingGetApiResponse } from 'api/generated/types';
import { isNewMyComplianceEntry } from 'config';
import { NavItemType } from 'ui/components/AppNavigationBar/types';

export const menuApi = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['menu'],
    endpoints: {
        umrsGetMenuUsingGet: {
            providesTags: ['menu'],
            ...cacheProfiles.staticData,
            transformResponse: (response: UmrsGetMenuUsingGetApiResponse) => {
                const mapData = (items: Item[]): Item[] => {
                    return items.map((item) => {
                        // if react page should be used for my compliance,
                        // we need to change the 'action' and 'id' so correct page is opened from the menu
                        if (isNewMyComplianceEntry && item.action === NavItemType.MY_COMPLIANCE_ENTRY_OLD) {
                            return { ...item, action: 'MyComplianceEntryPage', id: item.id === 'MY_COMPLIANCE_ENTRY' ? 'MY_COMPLIANCE_ENTRY_REACT' : item.id };
                        }

                        if (item.items) {
                            return {
                                ...item,
                                items: mapData(item.items),
                            };
                        }

                        return item;
                    });
                };

                const mappedItems = mapData(response.items);

                return { ...response, items: mappedItems };
            },
        },
    },
});

export const { useUmrsGetMenuUsingGetQuery } = menuApi;
