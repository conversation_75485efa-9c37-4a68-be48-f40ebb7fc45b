import axios from 'axios';

import { API_BASE_URL } from 'config';
import { ApiError } from './errorHandling';
import store from 'store';

// THIS COULD BE DELETED AFTER CALLS WILL BE REPLACED BY RTK
const authenticatedClient = axios.create({
    baseURL: API_BASE_URL,
    paramsSerializer: {
        indexes: null,
        encode: function encode(val) {
            /*
                custom implementation for not decode '[' ']' automatically back
                replace(/%5B/gi, '[').
                replace(/%5D/gi, ']');
            */
            if (val) {
                return encodeURIComponent(val).replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+');
            }
            return val;
        },
    },
});

const refreshTokenAttempts = new Map<string, number>();

export const updateRefreshTokenAttempts = (url, attempts) => {
    refreshTokenAttempts.set(url, attempts);
};

export const deleteRefreshTokenAttempts = (url) => {
    refreshTokenAttempts.delete(url);
};

export const getRefreshTokenAttempts = (): Map<string, number> => {
    return refreshTokenAttempts;
};

authenticatedClient.interceptors.request.use(
    async (config) => {
        const { token } = store.getState().app;

        config.headers.setAuthorization(`Bearer ${token}`);
        config.withCredentials = true;
        return config;
    },
    async (error) => {
        await Promise.reject(error);
    },
);

authenticatedClient.interceptors.response.use(
    (response) => {
        if (response.status >= 200 && response.status < 300) {
            // after successful call, reset counter
            if (response.config.url && refreshTokenAttempts.has(response.config.url)) {
                deleteRefreshTokenAttempts(response.config.url);
            }

            return response.data ?? response;
        } else {
            throw new ApiError(response.status, response.statusText, response.data);
        }
    },
    (error) => {
        throw error; // error has to be thrown otherwise it does not reach useAsync hook
    },
);

export const isCancel = (error): boolean => {
    return axios.isCancel(error);
};

export default authenticatedClient;
