import { ErrorResponse } from '../types';
import { getErrorParamValue } from './errorHandling';
import { isUndefined } from 'lodash';

describe('getErrorParamValue', () => {
    const response: ErrorResponse = {
        errorParams: [
            { paramName: 'name1', paramValue: 'strValue1' },
            { paramName: 'name2', paramValue: '123' },
        ],
        message: 'Some error occurred',
    };

    it('Should return string value', async () => {
        const actual = getErrorParamValue(response, 'name1', 'string');
        expect(actual).toBe('strValue1');
    });

    it('Should return number value', async () => {
        const actual = getErrorParamValue(response, 'name2', 'number');
        expect(actual).toBe(123);
    });

    it('Should return undefined', async () => {
        const result1 = getErrorParamValue(response, 'nonExistingName', 'number');
        expect(isUndefined(result1)).toBe(true);

        const result2 = getErrorParamValue(response, 'name1', 'number');
        expect(isUndefined(result2)).toBe(true);

        const result3 = getErrorParamValue({ message: 'Some error occurred' }, 'name3', 'number');
        expect(isUndefined(result3)).toBe(true);
    });
});
