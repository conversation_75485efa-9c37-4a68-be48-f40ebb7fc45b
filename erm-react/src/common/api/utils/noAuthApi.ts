import axios from 'axios';

import { API_BASE_URL } from 'config';
import { ApiError } from './errorHandling';

const unAuthenticatedClient = axios.create({
    baseURL: API_BASE_URL,
    paramsSerializer: { indexes: null },
});

unAuthenticatedClient.interceptors.response.use(
    (response) => {
        if (response.status >= 200 && response.status < 300) {
            return response.data ?? response;
        } else {
            throw new ApiError(response.status, response.statusText, response.data);
        }
    },
    async (error) => {
        console.log(error);
    },
);

export default unAuthenticatedClient;
