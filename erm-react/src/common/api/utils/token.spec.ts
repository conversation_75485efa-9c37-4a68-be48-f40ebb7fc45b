import store from 'store';
import { authenticateUser } from './token';

jest.mock('common/api/utils/rtkApi', () => {
    const mockResponse = {
        data: { token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MDI0MTgyMzV9.uzmFqbFTyopzrG61-cl0ny7quDTkx8T75nFJFAaXC_M', psk: 'psk' },
    };

    const tokenResponse = {
        data: { jwt: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MDI0MTgyMzV9.uzmFqbFTyopzrG61-cl0ny7quDTkx8T75nFJFAaXC_M' },
    };

    const initiateLoginMock = jest.fn(() => () => ({
        unwrap: () => Promise.resolve(mockResponse.data),
    }));
    const initiateRefreshTokenMock = jest.fn(() => () => ({
        unwrap: () => Promise.resolve(tokenResponse.data),
    }));

    return {
        authApi: {
            endpoints: {
                loginAndGetPsk: {
                    matchFulfilled: jest.fn(),
                    initiate: initiateLoginMock,
                },
                refreshToken: {
                    matchFulfilled: jest.fn(),
                    initiate: initiateRefreshTokenMock,
                },
            },
        },
    };
});

describe('authenticateUser', () => {
    const dispatchSpy = jest.spyOn(store, 'dispatch');

    it('successfully authenticates and sets JWT token in store', async () => {
        const result = await authenticateUser();

        const expectedDate = new Date('2023-12-12T22:57:15.000+01:00');
        const resultDate = new Date(result.expiration);

        console.log("result" + JSON.stringify(result))
        expect(result.token).toEqual('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MDI0MTgyMzV9.uzmFqbFTyopzrG61-cl0ny7quDTkx8T75nFJFAaXC_M');
        expect(resultDate.getTime()).toBe(expectedDate.getTime());
        expect(dispatchSpy).toHaveBeenCalled();
    });
});
