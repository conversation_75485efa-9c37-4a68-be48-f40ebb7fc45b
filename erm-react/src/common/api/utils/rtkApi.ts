import { LoginResponse, authApi as importedauthApi } from 'api/authApi';
import { encodeJWT } from './token';
import { DateTime } from 'luxon';

export const authApi = importedauthApi.injectEndpoints({
    endpoints: (build) => ({
        loginAndGetPsk: build.mutation<LoginResponse, { username: string; password: string }>({
            queryFn: async (credentials, api, extraOptions, baseQuery) => {
                const response = await baseQuery({
                    url: '/login',
                    method: 'POST',
                    body: `j_username=${credentials.username}&j_password=${credentials.password}&jwt=1&normal=true`,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                });
                if (response.error) {
                    return { error: response.error };
                }

                const loginResponse: LoginResponse = response.data as LoginResponse;
                if (!loginResponse) {
                    return { error: { status: 'CUSTOM_ERROR', error: 'Invalid response data' } };
                }

                return { data: loginResponse };
            },
        }),
        refreshToken: build.mutation({
            queryFn: async (arg, queryApi, extraOptions, baseQuery) => {
                const { username, psk } = arg;
                const ltype = 'true' === ProtechtDictionary['sso'] + '' ? 'issso' : 'w3';
                const jwtParam = encodeJWT({ con: DateTime.now().toMillis(), lid: username, lty: ltype }, psk);
                const formData = new URLSearchParams();
                formData.append('jwt', jwtParam);

                const response = await baseQuery({
                    url: '/v1/api/get_token',
                    method: 'POST',
                    body: formData,
                    headers: { 'content-type': 'application/x-www-form-urlencoded' },
                });

                if (response.error) {
                    return { error: response.error };
                }

                const data = response.data;
                return { data };
            },
        }),
    }),
});
