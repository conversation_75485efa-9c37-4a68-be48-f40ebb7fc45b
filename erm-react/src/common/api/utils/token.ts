import jsrs from 'jsrsasign';
import { initReactBridgeBase } from 'common/utils/bridge';
import store from 'store';
import { LoginResponse, RefreshTokenResponse } from 'api/authApi';
import appSlice from 'app/reducer';
import { authApi } from './rtkApi';
import { DateTime } from 'luxon';
import { JWT_INFO } from 'common/constants';
import { API_BASE_URL, ERM_CONTEXT, isProduction } from 'config';
import axios, { AxiosResponse } from 'axios';
import { ApiError, handleError } from './errorHandling';

const MAX_REFRESH_ATTEMPTS = 3;

const refreshTokenAttempts = new Map<string, number>();

export const updateRefreshTokenAttempts = (url, attempts) => {
    refreshTokenAttempts.set(url, attempts);
};

export const deleteRefreshTokenAttempts = (url) => {
    refreshTokenAttempts.delete(url);
};

export const getRefreshTokenAttempts = (): Map<string, number> => {
    return refreshTokenAttempts;
};

const getHostStringFromUrl = (url: string): string => {
    return url.substring(url.indexOf('://') + 3).replace(/[./:]/g, '-');
};

export const encodeJWT = (payload, secret) => {
    const oHeader = { alg: 'HS512' };

    let hex, i;

    let pskHex = '';

    if (secret) {
        for (i = 0; i < secret.length; i++) {
            hex = secret.charCodeAt(i).toString(16);
            pskHex += ('' + hex).slice(-4);
        }
    }
    return jsrs.KJUR.jws.JWS.sign('HS512', oHeader, payload, pskHex, null);
};

if (!window.ReactBridge || !window.ReactBridge.Login) {
    initReactBridgeBase();
}

window.ReactBridge!.Login!.reloginRefresh = async () => {
    try {
        await authenticateUser();
    } catch (error) {
        console.error('Relogin refresh failed', error);
    }
};

const axiosInstance = axios.create({
    baseURL: API_BASE_URL,
});

const publicAxiosInstance = axios.create({
    baseURL: ERM_CONTEXT,
});

export const getToken = async (): Promise<string | null> => {
    const username = ProtechtDictionary.loggedUserLogin || process.env.REACT_APP_API_USERNAME;
    const host = getHostStringFromUrl(ProtechtDictionary.siteUrl);

    let jwt = JWT_INFO[`token_${host}_${username}`];

    const tokenExpiration = JWT_INFO[`tokenExpiration_${host}_${username}`];
    const tokenExpired = tokenExpiration ? new Date(tokenExpiration) < new Date() : true;
    const psk = await getPsk();

    if ((!jwt || tokenExpired) && username && psk) {
        jwt = await refreshToken(username, psk);
    }

    return jwt;
};

export const refreshToken = async (usernameParam?: string, pskParam?: string): Promise<string> => {
    const username = usernameParam || ProtechtDictionary.loggedUserLogin || process.env.REACT_APP_API_USERNAME;
    const host = getHostStringFromUrl(ProtechtDictionary.siteUrl);
    const psk = pskParam || (await getPsk());

    const response = await getNewJwt(username!, psk);
    const jwt = response.jwt;

    // TOKEN
    JWT_INFO[`token_${host}_${username}`] = jwt || '';

    // EXPIRATION
    const expirationVal = getExpirationFromTokenMillis(jwt);
    const expirationStorageKey = `tokenExpiration_${host}_${username}`;

    if (expirationVal) {
        const expirationDate = new Date(expirationVal * 1000) || '';
        JWT_INFO[expirationStorageKey] = expirationDate.toString(); // TODO move from storage
    } else {
        delete JWT_INFO[expirationStorageKey];
    }
    return jwt;
};

export const getPsk = async () => {
    if (isProduction) {
        return ProtechtDictionary.psk;
    } else {
        if (process.env.REACT_APP_API_USERNAME && process.env.REACT_APP_API_PASSWORD) {
            if (localStorage.getItem('dev_psk')) {
                return localStorage.getItem('dev_psk');
            } else {
                const psk = (await login(process.env.REACT_APP_API_USERNAME, process.env.REACT_APP_API_PASSWORD)).psk;
                localStorage.setItem('dev_psk', psk);
                return psk;
            }
        } else {
            throw new Error('Please check your .env.dev file and provide values for REACT_APP_API_USERNAME and REACT_APP_API_PASSWORD');
        }
    }
};

export const getNewJwt = async (username: string, psk: string) => {
    const ltype = 'true' === ProtechtDictionary['sso'] + '' ? 'issso' : 'w3';
    const jwtParam = encodeJWT({ con: Date.now(), lid: username, lty: ltype }, psk);

    return axiosInstance
        .post(
            '/v1/api/get_token',
            {
                jwt: jwtParam,
            },
            {
                headers: { 'content-type': 'application/x-www-form-urlencoded' },
            },
        )
        .then(checkStatus)
        .then(parseJSON)
        .catch(handleError);
};

const login = (username: string, password: string) => {
    return publicAxiosInstance
        .post('/login', `j_username=${username}&j_password=${password}&jwt=1&normal=true`)
        .then(checkStatus)
        .then(parseJSON)
        .catch(handleError);
};

const isTokenExpired = (tokenExpiration) => {
    const currentTimeUTC = DateTime.utc();
    const tokenExpirationDateTime = DateTime.fromISO(tokenExpiration).toUTC();
    return tokenExpirationDateTime < currentTimeUTC;
};

export const refreshTokenIfNeeded = async () => {
    const state = store.getState();
    let { token, tokenExpiration } = state.app;

    if (!token || isTokenExpired(tokenExpiration)) {
        const newTokenData = await authenticateUser();
        token = newTokenData.token;
        tokenExpiration = newTokenData.expiration;
        store.dispatch(appSlice.actions.setToken({ token, tokenExpiration }));
    }

    return token;
};

const checkStatus = (response: AxiosResponse) => {
    if (response.status >= 200 && response.status < 300) {
        // after successful call, reset counter
        if (response.config.url && refreshTokenAttempts.has(response.config.url)) {
            deleteRefreshTokenAttempts(response.config.url);
        }

        return response;
    } else {
        throw new ApiError(response.status, response.statusText, response.data);
    }
};

const parseJSON = (response) => (response.data !== undefined ? response.data : response);

const getExpirationFromTokenMillis = (t: string) => {
    try {
        return jsrs.KJUR.jws.JWS.readSafeJSONString(jsrs.b64utoutf8(t.split('.')[1])).exp;
    } catch (_e) {
        return null;
    }
};

export const authenticateUser = async () => {
    try {
        const username = ProtechtDictionary.loggedUserLogin || process.env.REACT_APP_API_USERNAME || '';
        const host = getHostStringFromUrl(ProtechtDictionary.siteUrl);
        const state = store.getState();

        let jwt = state.app.token;
        let expiration;

        const refreshTokenKey = `refresh_${host}_${username}`;
        let attempts = refreshTokenAttempts.get(refreshTokenKey) || 0;

        if (!jwt || attempts < MAX_REFRESH_ATTEMPTS) {
            updateRefreshTokenAttempts(refreshTokenKey, attempts);
            attempts++;

            let pskResult;
            if (isProduction) {
                pskResult = ProtechtDictionary.psk;
            } else {
                const pskResponse = (await store
                    .dispatch(
                        authApi.endpoints.loginAndGetPsk.initiate({
                            username,
                            password: process.env.REACT_APP_API_PASSWORD!,
                        }),
                    )
                    .unwrap()) as LoginResponse;
                pskResult = pskResponse.psk;
            }
            if (pskResult) {
                const refreshResult = (await store
                    .dispatch(
                        authApi.endpoints.refreshToken.initiate({
                            username,
                            psk: pskResult,
                        }),
                    )
                    .unwrap()) as RefreshTokenResponse;

                if (refreshResult.jwt) {
                    jwt = refreshResult.jwt;
                    expiration = DateTime.fromSeconds(getExpirationFromTokenMillis(jwt)).toISO();
                    store.dispatch(appSlice.actions.setToken({ jwt, expiration }));

                    deleteRefreshTokenAttempts(refreshTokenKey);
                } else {
                    throw new Error('Failed to refresh token.');
                }
            } else {
                throw new Error('Failed to fetch PSK.');
            }
        } else if (attempts >= MAX_REFRESH_ATTEMPTS) {
            throw new Error('Maximum token refresh attempts exceeded.');
        } else {
            expiration = state.app.tokenExpiration;
        }

        return { token: jwt, expiration };
    } catch (error) {
        console.error('Authentication failed', error);
        throw error;
    }
};
