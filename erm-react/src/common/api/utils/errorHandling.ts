import { AxiosError } from 'axios';
import { ErrorResponse } from '../types';
import { getRefreshTokenAttempts, updateRefreshTokenAttempts } from './api';
import { refreshToken } from './token';
import authenticatedClient from './api';

export function ApiError(status: number, statusText: string, response: ErrorResponse): void {
    this.status = status;
    this.statusText = statusText;
    this.response = response;
}

/**
 * Type name of error response parameter value.
 */
type ErrorValueTypeName = 'string' | 'number';
/**
 * Error parameter value object type.
 */
type ErrorValueObjectType<T> = T extends 'string' ? string : T extends 'number' ? number : never;

/**
 * Get error parameter value defined by given parameter name from given error response.
 *
 * @param response error response
 * @param paramName parameter name
 * @param type value type (for more types extend ErrorValueTypeName and ErrorValueObjectType)
 *
 * @returns error parameter value if found, undefined otherwise
 */
export const getErrorParamValue = <T extends ErrorValueTypeName>(response: ErrorResponse, paramName: string, type: T): ErrorValueObjectType<T> | undefined => {
    // find value for given parameter name
    const valueStr = response.errorParams?.find((param) => param.paramName === paramName)?.paramValue;
    // return string type value
    if (valueStr && type === 'string') {
        return valueStr as ErrorValueObjectType<T>;
    }
    // return number type value
    if (valueStr && type === 'number') {
        const valueNum = Number(valueStr);
        return isNaN(valueNum) ? undefined : (valueNum as ErrorValueObjectType<T>);
    }
};

export const handleError = async (error: AxiosError<ErrorResponse>) => {
    if (error.response) {
        const statusCode: number = error.response.status;

        // retry request
        let attempts = (error.config?.url && getRefreshTokenAttempts().get(error.config.url)) || 0;
        if ((statusCode === 401 || statusCode === 403) && attempts < 3) {
            error.config?.url && updateRefreshTokenAttempts(error.config.url, ++attempts);
            await refreshToken();
            // token is injected via interceptor
            return authenticatedClient({ ...error.config });
        }

        throw new ApiError(error.response.status, error.response.statusText, error.response.data);
    } else {
        throw error;
    }
};
