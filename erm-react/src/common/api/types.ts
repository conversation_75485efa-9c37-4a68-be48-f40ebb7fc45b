import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { SortType } from 'ui/types';

export type ErrorResponse = {
    data?: {
        message: string;
        requestId: string;
    };
    message: string;
    httpCode?: number;
    errCode?: string;
    errorParams?: ErrorParam[];
    displayServerMessage?: boolean;
    statusText?: string;
};

export type ErrorParam = {
    paramName: string;
    paramValue: string;
};

export type ApiError = {
    status: number;
    statusText: string;
    response: ErrorResponse;
};

export type ApiErrorContextType = {
    error: ApiError | undefined;
    addError: (error: ApiError) => void;
    removeError: () => void;
};

export type RequestOptions = {
    method: string;
    requestParams?: RequestParams | Record<string, unknown>;
    data?: unknown;
    headers?: Record<string, string>;
    onUploadProgress?: () => void;
    signal?: AbortSignal;
    withCredentials?: boolean;
};

export type RequestParams = BasicRequestParams | BowTieSearchRequestParams | SearchRequestParams | RegisterEntryRequestParams | RolesRequestParams;

export type BasicRequestParams = {
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderType?: SortType;
    groupBy?: string;
    viewId?: number;
    page?: number;
};

export type BowTieSearchRequestParams = {
    size?: number;
    page?: number;
    'sort-by'?: string;
    'sort-dir'?: string;
    'filter-by'?: string;
    'filter-value'?: string;
    bus?: number[];
    tags?: number[];
    view?: number;
};

export type RegisterEntryRequestParams = BasicRequestParams & {
    keys?: Array<string>;
    values?: Array<string>;
    myTaskStates?: number[];
    stateId?: string;
    selectedItems?: number[];
};

type RolesRequestParamsBase = {
    query?: string;
    type?: string;
    dir?: 'ASC' | 'DESC';
    sort?: 'id' | 'name' | 'userCount' | 'userTypeLevel';
};

export type RolesRequestParams = RolesRequestParamsBase & {
    limit: number;
    page: number;
};

export type RolesRequestParamsRest = RolesRequestParamsBase & {
    'page-size'?: number;
    'page-number'?: number;
};

export interface PagingResult<T> {
    totalCount: number;
    records?: T[];
    maxPage?: number;
}

// instead of axios cancel token types.
export interface Cancel {
    message: string;
}
