import { DateTime } from 'luxon';

export const NAME = 'common';

export enum FileType {
    JSON = 'json',
    PNG = 'png',
    SVG = 'svg',
    PDF = 'pdf',
}

export const MIN_TABLE_COLUMN_WIDTH = 150;

export const LAZY_LOAD_LIMIT = 24;

export const JWT_INFO = {};

export const DEFAULT_DATE_FORMAT = 'dd/MM/yyyy';
export const DEFAULT_DATE_TIME_FORMAT = 'dd/MM/yyyy hh:mm:ss a';
export const DEFAULT_TIME_FORMAT = 'hh:mm:ss a';
export const REGISTER_DATE_FORMAT = 'yyyy-MM-dd';
export const PROTECHT_DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss.SSS';
export const REGISTER_CONSTRAINTS_DATE_FORMAT = 'dd-MM-yyyy';
export const backButtonTestId = 'back-button';

export const START_OF_TODAY = DateTime.now().startOf('day');
export const END_OF_TODAY = DateTime.now().endOf('day');

export enum LanguageCode {
    EN_AU = 'en_AU',
}
