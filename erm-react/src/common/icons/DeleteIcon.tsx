import { Delete } from '@protecht/ui-library/library/components/SVGIcons';
import { styled } from '@mui/material/styles';
import { defaultStyledOptions } from '@protecht/ui-library/library/utils/defaultStyledOptions';

export const DeleteIcon = styled(
    Delete,
    defaultStyledOptions,
)<{ $disabled: boolean }>(({ $disabled }) => ({
    ...($disabled && {
        '& path': {
            fill: 'currentColor',
        },
    }),
}));
