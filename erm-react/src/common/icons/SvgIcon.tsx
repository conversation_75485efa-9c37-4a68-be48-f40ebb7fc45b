import React from 'react';
import { ReactSVG } from 'react-svg';

interface SvgIconProps {
    url?: string;
    icon?: React.FC<React.SVGProps<SVGSVGElement>>;
    width?: number;
    height?: number;
    color?: string;
    className?: string;
}

const SvgIcon: React.FC<SvgIconProps> = ({ url, icon: Icon, width = 18, height = 18, color, className = '', ...other }) => {
    if (url) {
        return (
            <ReactSVG
                src={url}
                className={`react-svg-wrapper ${className}`}
                beforeInjection={(svg) => {
                    svg.setAttribute('style', `width: ${width}px; height: ${height}px`);
                }}
                {...other}
            />
        );
    }

    if (Icon) {
        return (
            <Icon
                width={width}
                height={height}
                style={color ? { fill: color } : undefined}
                className={className}
            />
        );
    }

    return null;
};

export default SvgIcon;
