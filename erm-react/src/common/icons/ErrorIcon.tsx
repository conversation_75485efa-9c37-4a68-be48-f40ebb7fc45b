import { faExclamation, faOctagon } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useTheme } from '@mui/material/styles';
import React from 'react';

export const ErrorIcon: React.FC = () => {
    const theme = useTheme();

    return (
        <span className="fa-layers fa-fw">
            <FontAwesomeIcon
                icon={faOctagon}
                color={theme.palette.error.main}
            />
            <FontAwesomeIcon
                icon={faExclamation}
                inverse
                transform="shrink-6"
            />
        </span>
    );
};
