import React from 'react';
import { faInfoCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { SizeProp } from '@fortawesome/fontawesome-svg-core';
import { primaryColor } from '@protecht/ui-library/library/theme/colors/colors';

export const InfoIcon: React.FC<{ size?: SizeProp; style?: React.CSSProperties; onClick?: () => void; color?: string }> = ({ size, style, onClick, color }) => {
    return (
        <FontAwesomeIcon
            icon={faInfoCircle}
            color={color ?? primaryColor}
            size={size}
            style={style}
            onClick={onClick}
        />
    );
};
