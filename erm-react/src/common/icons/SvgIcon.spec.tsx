import React from 'react';
import { render, screen } from 'test/utils/rtl';
import { waitFor } from '@testing-library/react';
import SvgIcon from './SvgIcon';
import AlertCaution from './svgs/AlertCaution.svg';

jest.mock('./svgs/AlertCaution.svg', () => {
    const SvgAlertCaution = () => (
        <svg
            data-testid="SvgAlertCaution"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
        >
            <path
                fillRule="evenodd"
                d="M12 1.75a10.25 10.25 0 100 20.5 10.25 10.25 0 000-20.5zM13 16a1 1 0 11-2 0v-2a1 1 0 112 0v2zm0-4a1 1 0 11-2 0V7a1 1 0 112 0v5z"
                clipRule="evenodd"
            />
        </svg>
    );

    SvgAlertCaution.displayName = 'SvgAlertCaution';

    return SvgAlertCaution;
});

describe('<SvgIcon />', () => {
    const mockSvgDataUrl = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E";

    it('was rendered with url', async () => {
        render(<SvgIcon url={mockSvgDataUrl} />);
        await waitFor(() => {
            expect(screen.getByRole('img')).toBeInTheDocument();
        });

        expect(screen.getByRole('img')).toBeVisible();
    });

    it('was rendered with icon', async () => {
        render(<AlertCaution />);
        await waitFor(() => {
            const svgElement = screen.getByTestId('SvgAlertCaution');
            expect(svgElement).toBeInTheDocument();
        });
    });
});
