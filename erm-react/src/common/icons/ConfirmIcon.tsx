import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useTheme } from '@mui/material/styles';
import { faOctagon } from '@fortawesome/pro-regular-svg-icons';
import { faExclamation } from '@fortawesome/pro-solid-svg-icons';

export const ConfirmIcon: React.FC = () => {
    const theme = useTheme();

    return (
        <span className="fa-layers fa-fw">
            <FontAwesomeIcon
                icon={faOctagon}
                color={theme.palette.error.main}
            />
            <FontAwesomeIcon
                icon={faExclamation}
                color={theme.palette.error.main}
                transform="shrink-6"
            />
        </span>
    );
};
