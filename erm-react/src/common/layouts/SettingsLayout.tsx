import React, { PropsWithChildren } from 'react';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';

const Layout = styled(Grid)(({ theme }) => ({
    flex: 1,
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    overflowX: 'hidden',
    backgroundColor: theme.palette.protechtGrey?.grey_245,
}));

const ContentContainer = styled(Grid)({
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    overflowY: 'auto',
    padding: '24px',
    width: '100%',
    paddingBottom: '24px',
});

export const SettingsSection = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.background.default,
    borderRadius: '8px',
    minWidth: '60%',
    maxWidth: '90%',
    border: '1px solid',
    borderColor: theme.palette.protechtGrey?.grey_220,
    padding: '24px',
}));

const SettingsLayout: React.FC<PropsWithChildren> = ({ children }) => {
    return (
        <Layout
            item
            container
        >
            <ContentContainer
                item
                rowGap="24px"
            >
                {children}
            </ContentContainer>
        </Layout>
    );
};

export default SettingsLayout;
