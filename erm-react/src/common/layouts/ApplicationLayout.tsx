import React, { PropsWithChildren } from 'react';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';

const Layout = styled(Grid)({
    height: '100%',
    flexWrap: 'nowrap',
    overflow: 'hidden',
    flexDirection: 'column',
});

const ApplicationLayout: React.FC<PropsWithChildren> = (props) => {
    return <Layout container>{props.children}</Layout>;
};

export default ApplicationLayout;
