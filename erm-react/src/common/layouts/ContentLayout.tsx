import React, { PropsWithChildren, ReactNode } from 'react';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import { SxProps } from '@mui/system/styleFunctionSx';
import { defaultStyledOptions } from '@protecht/ui-library/library/utils/defaultStyledOptions';

type Props = {
    isSelector?: boolean;
    toolbar?: ReactNode;
    sx?: SxProps;
    disableScroll?: boolean;
};

const Layout = styled(Grid)({
    flex: 1,
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    overflowX: 'hidden',
    marginLeft: '24px',
    marginRight: '24px',
});

const ContentContainer = styled(
    Grid,
    defaultStyledOptions,
)<{ $scrollable?: boolean; $isSelector?: boolean }>(({ $isSelector = false, $scrollable = true }) => ({
    overflowY: $scrollable ? 'auto' : 'hidden',
    flex: 1,
    flexDirection: 'column',
    width: '100%',
    paddingBottom: $isSelector ? 0 : '24px',
    display: 'flex',
}));

const ContentLayout: React.FC<PropsWithChildren<Props>> = ({ isSelector, toolbar, children, sx, disableScroll }) => {
    return (
        <Layout
            item
            container
            sx={sx}
        >
            {toolbar}
            <ContentContainer
                item
                $scrollable={!disableScroll}
                $isSelector={isSelector}
            >
                {children}
            </ContentContainer>
        </Layout>
    );
};

export default ContentLayout;
