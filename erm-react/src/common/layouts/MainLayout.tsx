import React, { PropsWithChildren, ReactNode } from 'react';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import { defaultStyledOptions } from '@protecht/ui-library/library/utils/defaultStyledOptions';

export const SIDEBAR_WIDTH = 365;

type Props = {
    sidebar?: ReactNode;
    sidebarWidth?: number;
};

const Layout = styled(Grid)({
    height: '100%',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    overflow: 'hidden',
});

const SidebarContainer = styled(
    Grid,
    defaultStyledOptions,
)<{ $sidebarWidth?: number }>(({ theme, $sidebarWidth = SIDEBAR_WIDTH }) => ({
    display: 'flex',
    flexDirection: 'column',
    width: `${$sidebarWidth}px`,
    minWidth: `${$sidebarWidth}px`,
    height: '100%',
    [theme.breakpoints.down('sm')]: {
        width: '100%',
    },
    overflowY: 'auto',
    marginLeft: '24px',
}));

const MainLayout: React.FC<PropsWithChildren<Props>> = ({ sidebar, sidebarWidth, children }) => {
    return (
        <Layout
            item
            container
        >
            {sidebar && (
                <SidebarContainer
                    item
                    $sidebarWidth={sidebarWidth}
                >
                    {sidebar}
                </SidebarContainer>
            )}
            {children}
        </Layout>
    );
};

export default MainLayout;
