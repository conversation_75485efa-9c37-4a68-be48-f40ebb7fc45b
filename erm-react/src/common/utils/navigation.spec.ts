import store from 'store';
import { ReplaySubject } from 'rxjs';
import { WidgetKey, goToWidget } from './navigation';
import { setIsMenuHidden } from 'app/reducer';
import { isGwtWithoutMenu } from 'app/components/MainAppLayout/utils';
import { AppEventType, emitAppEvent } from 'app/NotificationService';

jest.mock('app/NotificationService', () => ({
    emitAppEvent: jest.fn(),
    appEvents: new ReplaySubject(),
    AppEventType: {
        NAVIGATION: 'navigation',
    },
}));

describe('goToWidget', () => {
    let originalGwtBridge: any;
    let dispatchSpy: jest.SpyInstance;

    beforeEach(() => {
        originalGwtBridge = window.GwtBridge;
        window.GwtBridge = {
            Navigate: {
                goToWidget: jest.fn(),
            },
        };
        dispatchSpy = jest.spyOn(store, 'dispatch');
    });

    afterEach(() => {
        window.GwtBridge = originalGwtBridge;
        jest.restoreAllMocks();
    });

    it('should navigate to the widget if GwtBridge.Navigate.goToWidget is defined', () => {
        goToWidget(WidgetKey.MarketplaceCenter);
        expect(window.GwtBridge?.Navigate?.goToWidget).toHaveBeenCalledWith(WidgetKey.MarketplaceCenter.toString());
        expect(dispatchSpy).toHaveBeenCalledWith(setIsMenuHidden(isGwtWithoutMenu(WidgetKey.MarketplaceCenter.toString())));
        expect(emitAppEvent).toHaveBeenCalledWith({
            type: AppEventType.NAVIGATION,
            widgetId: WidgetKey.MarketplaceCenter.toString(),
        });
    });

    it('should execute elseFunc if GwtBridge.Navigate.goToWidget is not defined', () => {
        window.GwtBridge = {
            Navigate: {},
        };
        const elseFunc = jest.fn();
        goToWidget(WidgetKey.MarketplaceCenter, elseFunc);
        expect(elseFunc).toHaveBeenCalled();
        expect(dispatchSpy).not.toHaveBeenCalled();
    });

    it('should not throw an error if elseFunc is not provided and GwtBridge.Navigate.goToWidget is not defined', () => {
        window.GwtBridge = {
            Navigate: {},
        };
        expect(() => goToWidget(WidgetKey.MarketplaceCenter)).not.toThrow();
        expect(dispatchSpy).not.toHaveBeenCalled();
    });
});
