import { isGwtWithoutMenu } from 'app/components/MainAppLayout/utils';
import { AppEventType, emitAppEvent } from 'app/NotificationService';
import { setIsMenuHidden } from 'app/reducer';
import store from 'store';

/**
 * Enumeration type representing GWT widgets.
 */
export enum WidgetKey {
    // It is necessary to update method 'public void goToWidget(String widgetKey)'
    // in 'com.protecht.gwt.client.widgets.react.SimpleReactContainer' GWT class
    // before new WidgetKey is added here !!!

    MarketplaceCenter = 'MarketplaceCenter',
    MarketplaceInstalledPackages = 'MarketplaceInstalledPackages',
    Resilience = 'Resilience',
    Bowtie = 'Bowtie',
}

/**
 * Navigate to GWT widget defined by given widget key, or execute given function
 * if navigation function is not defined in GwtBridge.
 *
 * @param widgetKey widget key
 * @param elseFunc function to execute if navigation function is not defined in
 * GwtBridge
 */
export const goToWidget = (widgetKey: WidgetKey, elseFunc?: () => void) => {
    if (window.GwtBridge?.Navigate?.goToWidget) {
        window.GwtBridge.Navigate.goToWidget(widgetKey.toString());
        store.dispatch(setIsMenuHidden(isGwtWithoutMenu(widgetKey.toString())));

        emitAppEvent({
            type: AppEventType.NAVIGATION,
            widgetId: widgetKey.toString(),
        });
    } else if (elseFunc) {
        elseFunc();
    }
};
