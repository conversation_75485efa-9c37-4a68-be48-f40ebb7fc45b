import { FileExtensionRest } from 'api/generated/types';
import { filterAllowedFileTypes, transformSystemExtensions } from './FileUtils';

describe('filterAllowedFileTypes', () => {
    const settingsFileTypes = {
        'image/jpg': ['.jpg', 'jpeg'],
        'image/png': ['.png'],
        'image/tiff': ['.tif', '.tiff'],
        'application/pdf': ['.pdf'],
        'text/html': ['htm'],
    };

    const userFileTypes = {
        'image/jpg': ['.jpg'],
        'image/png': ['.apng', '.png'],
        'image/tiff': ['*'],
        'application/pdf': ['.pdf'],
        'application/json': ['.json'],
        'text/html': ['.html'],
    };

    it('returns correct types when user types not defined', () => {
        expect(filterAllowedFileTypes(settingsFileTypes)).toEqual(settingsFileTypes);
    });

    it('returns correct types when user types are defined', () => {
        expect(filterAllowedFileTypes(settingsFileTypes, userFileTypes)).toEqual({
            'image/jpg': ['.jpg'],
            'image/png': ['.png'],
            'image/tiff': ['.tif', '.tiff'],
            'application/pdf': ['.pdf'],
        });
    });

    it('returns empty object when settings types are empty object', () => {
        expect(filterAllowedFileTypes({}, userFileTypes)).toEqual({});
    });
});

describe('transformSystemExtensions', () => {
    const fileExtensions: FileExtensionRest[] = [
        {
            id: 50,
            allowed: true,
            description: 'Microsoft OneNote document',
            name: 'ONE',
            supportedTypes: 'application/onenote; format=one',
            system: true,
        },
        {
            id: 62,
            allowed: true,
            description: 'SSL Certificate Request file',
            name: 'REQ',
            supportedTypes: 'Content inspection cannot be performed, unknown file type.',
            system: true,
        },
        {
            id: 27,
            allowed: true,
            description: 'JPEG image file',
            name: 'JPE',
            supportedTypes: 'image/jpeg',
            system: true,
        },
        {
            id: 30,
            allowed: true,
            description: 'JPEG 2000 image file',
            name: 'JPG2',
            supportedTypes: 'Content inspection cannot be performed, unknown file type.',
            system: true,
        },
        {
            id: 28,
            allowed: true,
            description: 'JPEG image file',
            name: 'JPEG',
            supportedTypes: 'image/jpeg',
            system: true,
        },
        {
            id: 72,
            allowed: true,
            description: 'Plain text file',
            name: 'TXT',
            supportedTypes: 'text/plain, message/news',
            system: true,
        },
        {
            id: 69,
            allowed: true,
            description: 'TIFF image file',
            name: 'TIF',
            supportedTypes: 'image/tiff',
            system: true,
        },
        {
            id: 70,
            allowed: true,
            description: 'TIFF image file',
            name: 'TIFF',
            supportedTypes: 'image/tiff',
            system: true,
        },
    ];
    it('returns empty object when data is empty array', () => {
        expect(transformSystemExtensions([])).toEqual({});
    });

    it('returns empty object when data is undefined', () => {
        expect(transformSystemExtensions()).toEqual({});
    });

    it('returns correct object', () => {
        expect(transformSystemExtensions(fileExtensions)).toEqual({
            'application/octet-stream': ['.req', '.jpg2'],
            'application/onenote': ['.one'],
            'image/jpeg': ['.jpe', '.jpeg'],
            'text/plain': ['.txt'],
            'image/tiff': ['.tif', '.tiff'],
            'message/news': ['.txt'],
        });
    });
});
