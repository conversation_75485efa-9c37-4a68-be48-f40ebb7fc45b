import { FileExtensionRest } from 'api/generated/types';
import { FileType } from 'common/constants';
import { Accept } from 'common/types';
import { WORMS_CLIENT_APP_BASE_URL } from 'config';
import _ from 'lodash';

export class FileUtils {
    static saveFile(fileContent: string, fileName: string): Promise<string> {
        return new Promise((resolve, reject) => {
            // extract file format
            const format = fileName.split('.').pop()?.toLowerCase();

            if (FileUtils.isFileConstructorAvailable()) {
                let blob;
                if (format === FileType.PDF) {
                    // encode content to make transparent images work correctly
                    const uint8Array = new Uint8Array(fileContent.length);
                    for (let i = 0; i < fileContent.length; i++) {
                        uint8Array[i] = fileContent.charCodeAt(i);
                    }
                    blob = new Blob([uint8Array], { type: 'text/plain; charset=x-user-defined' });
                }
                if (format === FileType.PNG) {
                    // save as binary data
                    const dataUrlParts = fileContent.split(',');
                    const bString = window.atob(dataUrlParts[1]);
                    const byteArray: number[] = [];
                    for (let i = 0; i < bString.length; i++) {
                        byteArray.push(bString.charCodeAt(i));
                    }
                    blob = new Blob([new Uint8Array(byteArray)], { type: 'image/png' });
                }
                if (format === FileType.SVG) {
                    blob = new Blob([fileContent], { type: 'image/svg+xml' });
                }
                if (format === FileType.JSON) {
                    blob = new Blob([fileContent], { type: 'application/json' });
                }
                if (blob) {
                    // workaround for supporting non-binary data
                    fileContent = URL.createObjectURL(blob);

                    const aElement = document.createElement('a');
                    aElement.setAttribute('href', fileContent);
                    aElement.setAttribute('download', fileName);
                    aElement.style.display = 'none';
                    document.body.appendChild(aElement);
                    aElement.click();
                    document.body.removeChild(aElement);

                    resolve('File saved successfully');
                    return;
                } else {
                    reject(new Error('File save failed: Save operation is not supported for given file type.'));
                }
            }
            reject(new Error('File save failed: Save operation is not supported by the browser.'));
        });
    }

    /**
     * @returns {boolean}
     */
    static isFileConstructorAvailable(): boolean {
        // Test whether required functions exist
        if (typeof window.URL !== 'function' || typeof window.Blob !== 'function' || typeof window.File !== 'function') {
            return false;
        }
        // Test whether the constructor works as expected
        try {
            // eslint-disable-next-line no-new
            new File(['Content'], 'fileName', {
                type: 'image/png',
                lastModified: Date.now(),
            });
        } catch (ignored) {
            return false;
        }
        // Everything is available
        return true;
    }
}

export const getAttachmentUrl = (fileId: string, open: boolean) => {
    return `${WORMS_CLIENT_APP_BASE_URL}/media/download/image?type=${open ? 'open' : 'save'}&id=${fileId}`;
};

export const getFileUrl = (uuid: string) => {
    return `${WORMS_CLIENT_APP_BASE_URL}/media/download/file?id=${uuid}`;
};

export const getImageUrl = (uuid: string) => {
    return `${WORMS_CLIENT_APP_BASE_URL}/media/download/image?id=${uuid}&type=open`;
};

export const getImageThumbnailUrl = (uuid: string, width?: number, height?: number) => {
    const imageWidth = width || 80;
    const imageHeight = height || imageWidth;
    return `${WORMS_CLIENT_APP_BASE_URL}/media/download/image?id=${uuid}&type=open&imageType=thumbnail&width=${imageWidth}&height=${imageHeight}`;
};

export const filterAllowedFileTypes = (settingsFileTypes: Accept, userFileTypes?: Accept): Accept => {
    if (!userFileTypes) {
        return settingsFileTypes;
    }

    const allowedTypes = {};

    Object.keys(userFileTypes).forEach((mimeType) => {
        if (settingsFileTypes[mimeType]) {
            const settingsExtensions = settingsFileTypes[mimeType];
            const userExtensions = userFileTypes[mimeType];

            if (userExtensions.includes('*')) {
                allowedTypes[mimeType] = settingsExtensions;
            } else {
                const allowedExtensions = _.intersection(settingsExtensions, userExtensions);
                if (allowedExtensions.length) {
                    allowedTypes[mimeType] = allowedExtensions;
                }
            }
        }
    });
    return allowedTypes;
};

export const transformSystemExtensions = (fileExtensions?: FileExtensionRest[]) => {
    const fileTypes: Accept = {};

    fileExtensions?.forEach((item) => {
        // TODO: remove this temporary workaround
        // For some file types, the backend returns the string
        // "Content inspection cannot be performed, unknown file type." in the 'supportedTypes' property.
        // In such cases, we set a fallback MIME type of application/octet-stream unless correct MIME types are returned from the backend.
        const mimeTypes = item.supportedTypes?.includes('unknown file type') ? ['application/octet-stream'] : item.supportedTypes?.split(', ') || [];

        mimeTypes.forEach((type) => {
            const extension = `.${item.name!.toLowerCase()}`;

            // clean the MIME type by removing additional parameters
            // e.g. 'application/onenote; format=one' should become 'application/onenote'
            const mimeType = type.split(';')[0].trim();

            if (!fileTypes[mimeType]) {
                fileTypes[mimeType] = [extension];
            } else {
                fileTypes[mimeType] = [...fileTypes[mimeType], extension];
            }
        });
    });

    return fileTypes;
};
