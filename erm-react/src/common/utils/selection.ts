export type Selection<K> = {
    original: K[];
    added: K[];
    removed: K[];
};

export const create = <K>(originalSelectionIds: K[]): Selection<K> => {
    return {
        original: originalSelectionIds || [],
        added: [],
        removed: [],
    };
};

export const copy = <K>(selection: Selection<K>): Selection<K> => {
    return {
        original: [...selection.original],
        added: [...selection.added],
        removed: [...selection.removed],
    };
};

export const hasChanges = <K>(selection: Selection<K>): boolean => {
    return selection.added.length > 0 || selection.removed.length > 0;
};
