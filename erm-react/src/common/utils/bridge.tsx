export const initReactBridgeBase = (): void => {
    if (!window.ReactBridge) {
        window.ReactBridge = {};
    }

    if (!window.ReactBridge.GlobalSettings) {
        window.ReactBridge.GlobalSettings = {};
    }

    if (!window.ReactBridge.QuestionnaireOverview) {
        window.ReactBridge.QuestionnaireOverview = {};
    }

    if (!window.ReactBridge.Resilience) {
        window.ReactBridge.Resilience = {};
    }

    if (!window.ReactBridge.VendorManagement) {
        window.ReactBridge.VendorManagement = {};
    }

    if (!window.ReactBridge.VendorPortal) {
        window.ReactBridge.VendorPortal = {};
    }

    if (!window.ReactBridge.Login) {
        window.ReactBridge.Login = {};
    }

    if (!window.ReactBridge.FrameworkConfiguration) {
        window.ReactBridge.FrameworkConfiguration = {};
    }

    if (!window.ReactBridge.Menu) {
        window.ReactBridge.Menu = {};
    }

    if (!window.ReactBridge.Workspace) {
        window.ReactBridge.Workspace = {};
    }
};
