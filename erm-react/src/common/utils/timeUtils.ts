import { Timestamp } from '../../api/generated/types';

/**
 * Formats a Timestamp object into a string according to the 'yyyy-MM-dd HH:mm:ss.SSS' format. This is useful
 * for converting Timestamp objects to a string format that can be used with DateTime libraries for further
 * manipulation or display. The function handles undefined values by providing default values, ensuring
 * the output is always a valid datetime string.
 *
 * @param {Timestamp} timestamp - The Timestamp object to format, containing optional properties such as year, month, date, hours, minutes, seconds, and nanos.
 * @returns {string} A string representing the formatted date and time, adhering to the 'yyyy-MM-dd HH:mm:ss.SSS' format.
 */
export function formatTimestamp(timestamp: Timestamp): string {
    const { year = 1970, month = 0, date = 1, hours = 0, minutes = 0, seconds = 0, nanos = 0 } = timestamp;

    const formattedMonth = String(month + 1).padStart(2, '0');
    const formattedDate = String(date).padStart(2, '0');
    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(seconds).padStart(2, '0');
    const formattedNanos = String(nanos).padStart(3, '0').slice(0, 3);

    return `${year}-${formattedMonth}-${formattedDate} ${formattedHours}:${formattedMinutes}:${formattedSeconds}.${formattedNanos}`;
}
