import { SearchRequestParams } from '@protecht/ui-library/library/types/types';

export const mapParamsFields = (params: SearchRequestParams, apiFields: Record<string, string>): SearchRequestParams => {
    const paramsCopy = { ...params };
    if (paramsCopy.orderBy) {
        paramsCopy.orderBy = apiFields[paramsCopy.orderBy] || paramsCopy.orderBy;
    }

    if (paramsCopy.groupBy) {
        paramsCopy.groupBy = apiFields[paramsCopy.groupBy] || paramsCopy.groupBy;
    }
    return paramsCopy;
};
