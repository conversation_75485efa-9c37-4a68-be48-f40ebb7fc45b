import { Meta, ColorPalette, ColorItem, Source, Canvas } from '@storybook/addon-docs';
import { darken, getContrastRatio, lighten } from '@mui/system';
import debounce from 'lodash/debounce';
import { light, dark } from '@mui/material/styles';
import Box from '@mui/material/Box';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useState, useEffect } from 'react';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import { ICONS } from './ermIcons';
import FieldWrapper from 'common/components/FieldWrapper';

<Meta title="Design Tokens/Icons" />

# ICONS used in Protecht

There are two approaches for displaying icons in our project.

## 1 Pre-imported and pre-loaded global icons - dynamic

Global icons are imported from FontAwesome into file named ermIcons.ts using script located in faIcons.js that reads icon used throughought ERM from '../../camilla/src/com/protecht/utils/fontawesome/icons.yml' file.
These icons are globally loaded and used when dynamically displaying icons loaded from BE. Information about which icon to display is parsed from string which is typically in form of: 'fas fa-heart'.
Only icons listed in the list below can be used dynamically. If you need to preload other icons, you have to preload them manually (e.g. check out initRolesAndPermissionsIcons functions which uses library.add() to preload icons).

<Source
    language="jsx"
    dark
    format={true}
    code={`
    import {library} from '@fortawesome/fontawesome-svg-core';
    // import icon
    import {faAccessibleIcon} from '@fortawesome/free-brands-svg-icons';
    // preload it for later use
    library.add(faAccessibleIcon);
    // use preloaded icon when displaying icon loaded from BE
    // use parseIconFromString from utils to parse icon name and prefix
    <FontAwesomeIcon icon={parseIconFromString(someIcon)}/>
`}
/>

## 2 Individual manually imported icons - static

Individual static icons are imported from FontAwesome and used in components directly as needed.

<Source
    language="jsx"
    dark
    format={true}
    code={`
    // import icon
    import {faAccessibleIcon} from '@fortawesome/free-brands-svg-icons';
    // use static icon directly
    <FontAwesomeIcon icon={faAccessibleIcon}/>
`}
/>

Check out FontAwesome docs for more information: https://fontawesome.com/docs/web/use-with/react/

### List of globally imported icons:

export const GlobalIconsShowcase = () => {
    const [iconSize, setIconSize] = useState(30);
    const [iconColor, setIconColor] = useState('#333');
    const [searchString, setSearchString] = useState(undefined);
    const [filteredIcons, setFilteredIcons] = useState(ICONS);
    useEffect(() => {
        if (searchString) {
            const filtered = Object.keys(ICONS)
                .filter((key) => key.includes(searchString))
                .reduce((cur, key) => {
                    return Object.assign(cur, { [key]: ICONS[key] });
                }, {});
            setFilteredIcons(filtered);
        } else {
            setFilteredIcons(ICONS);
        }
    }, [searchString]);
    return (
        <>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <FieldWrapper label="Icon size (px)">
                    <Input
                        value={iconSize}
                        onChange={(event) => setIconSize(event.target.value)}
                    ></Input>
                </FieldWrapper>
                <FieldWrapper label="Icon color">
                    <Input
                        value={iconColor}
                        onChange={(event) => setIconColor(event.target.value)}
                    ></Input>
                </FieldWrapper>
                <FieldWrapper label="Search within icons">
                    <Input
                        value={searchString}
                        onChange={debounce((event) => setSearchString(event.target.value), 300)}
                    ></Input>
                </FieldWrapper>
            </Box>
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, 150px)', gap: '10px' }}>
                {Object.keys(filteredIcons).map((availableIcon) => {
                    return (
                        <Box
                            key={availableIcon}
                            sx={{ padding: 1, border: '1px solid #eee', borderRadius: 1, alignItems: 'center', display: 'flex', flexDirection: 'column' }}
                        >
                            <Box sx={{ flex: 1, fontSize: `${iconSize}px`, color: iconColor }}>
                                <FontAwesomeIcon
                                    key={availableIcon}
                                    icon={filteredIcons[availableIcon].definition}
                                />
                            </Box>
                            <Box sx={{ fontSize: '10px' }}>{availableIcon}</Box>
                        </Box>
                    );
                })}
            </Box>
        </>
    );
};

<Canvas withSource="none">
    <GlobalIconsShowcase />
</Canvas>
