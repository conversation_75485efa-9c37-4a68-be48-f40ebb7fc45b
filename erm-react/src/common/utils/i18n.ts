import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import logsEn from 'i18n/en/logs.json';
import commonEn from 'i18n/en/common.json';
import bowtieEn from 'i18n/en/bowtie.json';
import marketplaceEn from 'i18n/en/marketplace.json';
import resilienceEn from 'i18n/en/resilience.json';
import libraryEn from 'i18n/en/library.json';
import registerEn from 'i18n/en/register.json';
import ermMessagesEn from 'i18n/en/ermMessages.json';
import ermConstantsEn from 'i18n/en/ermConstants.json';
import ermErrorsEn from 'i18n/en/ermErrors.json';
import viewEn from 'i18n/en/view.json';
import rolesAndPermissionsEn from 'i18n/en/rolesAndPermissions.json';
import vendorRiskManagementEn from 'i18n/en/vendorRiskManagement.json';
import questionnaireEn from 'i18n/en/questionnaire.json';
import errorCodesEn from 'i18n/en/errorCodes.json';
import pluralsEn from 'i18n/en/plurals.json';
import authEn from 'i18n/en/auth.json';
import metricsEn from 'i18n/en/metrics.json';
import userEn from 'i18n/en/user.json';
import cyberRiskEn from '../../i18n/en/cyberrisk.json';
import frameworksEn from 'i18n/en/frameworks.json';
import complianceEn from 'i18n/en/compliance.json';
import kriEn from 'i18n/en/kri.json';
import aiEn from 'i18n/en/ai.json';

void i18n.use(initReactI18next).init({
    resources: {
        en: {
            common: commonEn,
            cyberrisk: cyberRiskEn,
            logs: logsEn,
            bowtie: bowtieEn,
            library: libraryEn,
            marketplace: marketplaceEn,
            resilience: resilienceEn,
            register: registerEn,
            view: viewEn,
            ermMessages: ermMessagesEn,
            ermConstants: ermConstantsEn,
            ermErrors: ermErrorsEn,
            rolesAndPermissions: rolesAndPermissionsEn,
            vrm: vendorRiskManagementEn,
            questionnaire: questionnaireEn,
            errorCodes: errorCodesEn,
            plurals: pluralsEn,
            auth: authEn,
            metrics: metricsEn,
            user: userEn,
            frameworks: frameworksEn,
            compliance: complianceEn,
            kri: kriEn,
            ai: aiEn,
        },
    },
    lng: 'en',

    interpolation: {
        escapeValue: false, // react already safes from xss
    },
    ns: [
        'common',
        'cyberrisk',
        'logs',
        'bowtie',
        'marketplace',
        'resilience',
        'register',
        'view',
        'ermMessages',
        'ermConstants',
        'ermErrors',
        'rolesAndPermissions',
        'questionnaire',
        'vendorRiskManagement',
        'errorCodes',
        'plurals',
        'auth',
        'metrics',
        'user',
        'frameworks',
        'compliance',
        'kri',
    ],
    compatibilityJSON: 'v4',
    defaultNS: 'common',
});

export const strings = (keys, options = {}) => {
    return i18n.t(keys, { lng: 'en', ...options });
};

export const logs = (keys, options = {}) => {
    return i18n.t(keys, { ...options, lng: 'en' });
};

export const exists = (keys): boolean => {
    return i18n.exists(keys);
};

export default i18n;
