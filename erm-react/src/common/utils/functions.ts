import { IdWithNameRest } from 'api/generated/types';
import { IdWithName } from 'app/types';
import { FieldErrors } from 'react-hook-form/dist/types/errors';

export const isEmptyString = (value: string | null | undefined | unknown) => {
    return !value || (Array.isArray(value) && value.length === 0);
};

export const isBlankString = (value: string | null | undefined) => {
    return isEmptyString(value?.trim());
};

// it does base 64 url safe encoding
export const toBase64 = (val: any) => {
    if (!val) {
        return '';
    }

    let json = JSON.stringify(val);
    json = btoa(json);
    json = json.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    return json;
};

export const isIdsArrayChanged = (a: IdWithNameRest[] | undefined, b: IdWithNameRest[] | undefined) => {
    if (a === b) {
        return false;
    }
    // check emptiness for undefined
    if (typeof a === 'undefined' || typeof b === 'undefined') {
        return !(isEmptyArray(a) && isEmptyArray(b));
    }
    // check length
    if (a.length !== b.length) {
        return true;
    }
    // check ids (in their order)
    for (let i = 0; i < a.length; ++i) {
        if (a[i].id !== b[i].id) {
            return true;
        }
    }
    // arrays are unchanged
    return false;
};

export const isEmptyArray = (value: IdWithName[] | null | undefined | unknown) => {
    return !value || (Array.isArray(value) && value.length === 0);
};

export const calculatePasswordStrength = (password: string): number => {
    if (ProtechtDictionary.encouragePassphrase == 'true') {
        return ERM.passwordStrength(password);
    } else {
        return ERM.passwordScore(password);
    }
};

export const purgeObjectProperty = <T>(object: T, property: string): T => {
    if (Object.prototype.hasOwnProperty.call(object, property) && object[property] === undefined) {
        delete object[property];
    }
    return object;
};

export const getNumberValuesOfEnum = (e: any) => {
    return Object.keys(e)
        .map((k) => e[k])
        .filter((v) => typeof v === 'number');
};

export const hasRequiredErrors = (errors: FieldErrors) => {
    return Object.values(errors).some((error) => error?.type === 'required');
};
