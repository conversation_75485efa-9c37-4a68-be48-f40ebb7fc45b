import { NodeType } from 'bowtie/types';
import { DataGridColDef, DateWithFormat, SelectorType, SYSTEM_COLUMN } from 'common/types';
import { getControls, getRiskCauses, getRiskEvents, loadTagCategories, loadTags, suggestTags } from 'library/api';
import { ControlColDef } from 'library/components/Control/ControlDefinitions';
import { RiskCauseColDef } from 'library/components/RiskCause/RiskCauseDefinitions';
import { RiskEventColDef } from 'library/components/RiskEvent/RiskEventDefinitions';
import { TagColDef } from 'library/components/Tag/TagColDefinitions';
import { ColumnType } from 'register/types';
import { MenuItemType } from 'ui/types';
import { TagCategoryColDef } from 'library/components/TagCategory/TagCategoryDefinitions';
import { DateTime } from 'luxon';
import { GridValueGetterParams } from '@mui/x-data-grid-pro';
import { DEFAULT_DATE_FORMAT, DEFAULT_DATE_TIME_FORMAT, PROTECHT_DATE_TIME_FORMAT } from 'common/constants';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { ProtechtUserRest, ViewExpressionRest } from 'api/generated/types';
import { strings } from './i18n';
import { UserColDef } from 'library/components/User/UserDefinitions';
import { CountryColDef } from 'library/components/Country/CountryDefinitions';
import { StateColDef } from 'library/components/State/StateDefinitions';
import { KriColDef } from 'library/components/Kri/KriDefinitions';
import { RoleColDef } from 'library/components/Role/RoleDefinitions';

export const getSelectorMetadata = (columnType: ColumnType | SelectorType | NodeType) => {
    switch (columnType) {
        case ColumnType.BUSINESS_UNIT:
        case SelectorType.BUSINESS_UNIT:
            return {
                columnDefinition: [],
                title: strings('common:title.selectBusinessUnit'),
                titleMultiple: strings('common:title.selectBusinessUnitMultiple'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.businessUnit'),
                onDataLoad: undefined,
                deletedLabel: strings('ermConstants:bu_archived'),
                infoBubble: true,
                fontWeight: '400',
            };
        case ColumnType.TABLE:
            return {
                columnDefinition: [],
                title: strings('register:title.selectRegisterEntry'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.registerEntry'),
                onDataLoad: undefined,
                fontWeight: '600',
            };
        case ColumnType.CENTRAL_LIBRARY:
            return {
                columnDefinition: [],
                title: strings('library:title.selectCentralLibraryEntry'),
                titleMultiple: strings('library:title.selectCentralLibraryEntries'),
                suggestionField: 'name',
                placeholder: strings('library:label.centralLibraryPlaceholder'),
                onDataLoad: undefined,
                fontWeight: '600',
            };
        case ColumnType.ACTIONS:
            return {
                columnDefinition: [],
                title: strings('register:title.selectRegisterEntry'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.registerEntry'),
                onDataLoad: undefined,
                fontWeight: '600',
            };
        case ColumnType.CONTROL:
        case SelectorType.CONTROL:
        case NodeType.Control:
            return {
                columnDefinition: ControlColDef,
                fontWeight: '400',
                infoBubble: true,
                title: strings('library:title.selectRiskControl'),
                titleMultiple: strings('library:title.selectRiskControls'),
                suggestionField: 'name',
                placeholder: strings('library:placeholder.riskControl'),
                onDataLoad: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController?: AbortController) =>
                    getControls(params, expressions, [], abortController),
            };
        case ColumnType.RISK_CAUSE:
        case SelectorType.RISK_CAUSE:
        case NodeType.Cause:
            return {
                columnDefinition: RiskCauseColDef,
                fontWeight: '400',
                infoBubble: true,
                title: strings('library:title.selectRiskCause'),
                titleMultiple: strings('library:title.selectRiskCauses'),
                suggestionField: 'name',
                placeholder: strings('library:placeholder.riskCause'),
                onDataLoad: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController?: AbortController) =>
                    getRiskCauses(params, expressions, [], abortController),
            };
        case ColumnType.RISK_EVENT:
        case SelectorType.RISK_EVENT:
        case NodeType.MainRiskEvent:
        case NodeType.RiskEvent:
            return {
                columnDefinition: RiskEventColDef,
                fontWeight: '400',
                infoBubble: true,
                title: strings('library:title.selectRiskEvent'),
                titleMultiple: strings('library:title.selectRiskEvents'),
                suggestionField: 'name',
                placeholder: strings('library:placeholder.riskEvent'),
                onDataLoad: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController?: AbortController) =>
                    getRiskEvents(params, expressions, [], abortController),
            };
        case ColumnType.TAGS:
        case SelectorType.TAGS:
            return {
                columnDefinition: TagColDef,
                title: strings('ermConstants:title_tags_select'),
                suggestionField: 'preview',
                placeholder: strings('common:placeholder.addTag'),
                placeholderCreateNew: strings('common:placeholder.addNew'),
                defaultExpressionProperty: 'value',
                onDataLoad: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController?: AbortController) =>
                    loadTags(params, expressions, abortController),
            };
        case SelectorType.TAG_CATEGORY:
            return {
                columnDefinition: TagCategoryColDef,
                title: strings('ermConstants:title_tag_types_select'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.addCategory'),
                placeholderCreateNew: strings('common:placeholder.addNew'),
                defaultExpressionProperty: 'name',
                onDataLoad: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController?: AbortController) =>
                    loadTagCategories(params, expressions, abortController),
            };
        case SelectorType.TAGS_AND_CATEGORIES:
            return {
                columnDefinition: TagColDef,
                title: strings('ermConstants:title_tags_select'),
                suggestionField: 'preview',
                placeholder: strings('common:placeholder.addTag'),
                placeholderCreateNew: strings('common:placeholder.addNew'),
                defaultExpressionProperty: 'value',
                onDataLoad: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController?: AbortController) =>
                    suggestTags(params, expressions, abortController),
            };
        case ColumnType.USER:
        case SelectorType.USER:
            return {
                columnDefinition: UserColDef,
                title: strings('ermConstants:title_user_select'),
                titleMultiple: strings('ermConstants:title_users_select'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.user'),
                onDataLoad: undefined,
                deletedLabel: strings('ermConstants:status_deleted'),
                infoBubble: true,
                fontWeight: '400',
            };
        case ColumnType.COUNTRY:
            return {
                columnDefinition: CountryColDef,
                title: strings('register:title.selectCountry'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.country'),
                onDataLoad: undefined,
                fontWeight: '400',
            };
        case ColumnType.STATE:
            return {
                columnDefinition: StateColDef,
                title: strings('register:title.selectState'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.state'),
                onDataLoad: undefined,
                fontWeight: '400',
            };
        case SelectorType.ROLE:
            return {
                columnDefinition: RoleColDef,
                title: strings('library:title.selectRole'),
                suggestionField: 'name',
                placeholder: strings('common:placeholder.role'),
                onDataLoad: undefined,
                fontWeight: '400',
            };
        case SelectorType.KRI:
            return {
                columnDefinition: KriColDef,
                infoBubble: true,
                title: strings('kri:title.selectKri'),
                titleMultiple: strings('kri:title.selectKris'),
                suggestionField: 'name',
                placeholder: strings('library:placeholder.kri'),
                onDataLoad: undefined,
                fontWeight: '400',
            };
        case SelectorType.AUDIT_QUESTION:
            return {
                columnDefinition: [],
                infoBubble: true,
                title: strings('library:title.selectAuditQuestion'),
                titleMultiple: strings('library:title.selectAuditQuestions'),
                placeholder: strings('library:placeholder.auditQuestion'),
                onDataLoad: undefined,
                fontWeight: '400',
            };
        case SelectorType.BOWTIE:
            return {
                columnDefinition: [],
                infoBubble: true,
                title: strings('library:title.selectBowTieDiagram'),
                titleMultiple: strings('library:title.selectBowTieDiagrams'),
                placeholder: strings('library:placeholder.bowTie'),
                onDataLoad: undefined,
                fontWeight: '400',
            };
        case SelectorType.QUESTION:
            return {
                columnDefinition: [],
                infoBubble: true,
                title: strings('library:title.selectComplianceQuestion'),
                titleMultiple: strings('library:title.selectComplianceQuestions'),
                placeholder: strings('library:placeholder.complianceQuestion'),
                onDataLoad: undefined,
                fontWeight: '400',
            };
        default:
            return {
                columnDefinition: [],
                title: strings('common:button.select'),
                suggestionField: SYSTEM_COLUMN.ID,
                placeholder: strings('common:placeholder.select'),
                onDataLoad: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController?: AbortController) =>
                    loadTagCategories(params, expressions, abortController),
            };
    }
};

export const getSearchFields = (colDef: DataGridColDef[]): MenuItemType<string>[] => {
    return colDef
        .filter((col) => col.filterable !== false)
        .map((col) => {
            return {
                key: col.field,
                value: getApiField(col),
                label: col.headerName,
            };
        });
};

/**
 * Returns list of field names that should be used for API requests.
 * For static libraries (Risk Control, Risk Event, Risk Cause, User, Tag, Tag Category) there may be a mismatch between fields eg. note and descrition represents the same field.
 * We otain the description in the data, but we need to filter or sort by note
 *
 * @param colDef the list of column definitions
 * @returns list of the field names used for API requests
 */
export const getApiFields = (colDef: DataGridColDef[]) => {
    const apifields = {};
    colDef.forEach((col) => {
        apifields[col.field] = getApiField(col);
    });

    return apifields;
};

/**
 * Returns field name that should be used for API requests.
 * For static libraries (Risk Control, Risk Event, Risk Cause, User, Tag, Tag Category) there may be a mismatch between fields eg. note and descrition represents the same field.
 * We otain the description in the data, but we need to filter or sort by note
 *
 * @param column definition
 * @returns field name used for API requests
 */
export const getApiField = (column: DataGridColDef): string => {
    return column.apiField || column.field;
};

export const getDataField = (colDef: DataGridColDef[], field: string) => {
    const col = colDef.find((column) => column.apiField === field || column.field === field);
    return col?.field ?? field;
};

export const getDateWithFormat = (params: GridValueGetterParams): DateWithFormat => ({
    value: params.value,
    datePattern: params.row.dateFormatPattern,
    timePattern: params.row.timeFormatPattern,
});

/**
 * Date formatter function to format date values returned from API.
 *
 * The format defined in data object is used primarily, if it is defined. If it is not, the format from UserRest object is used. If
 * neither of them is defined, finally the DEFAULT_DATE_FORMAT/DEFAULT_DATE_TIME_FORMAT from common/constants.ts is used based on dateOnly
 * parameter.
 *
 * @param data date/time data (format pattern is optional)
 * @param user user data that should contain date/time formatting, or null
 * @param dateOnly true if only the date part should be returned
 */
export const dateFormatter = (data: DateWithFormat, user: ProtechtUserRest | null, dateOnly = false): string => {
    if (data && data.value) {
        // resolve final pattern
        let finalPattern: string | undefined;
        if (data.dateTimePattern) {
            finalPattern = data.dateTimePattern;
        } else {
            if (data.datePattern) {
                finalPattern = data.datePattern;
            }
            if (data.timePattern && !dateOnly) {
                if (finalPattern) {
                    finalPattern += `${finalPattern ? ' ' : ''}${data.timePattern}`;
                } else {
                    finalPattern = data.timePattern;
                }
            }
            if (user && !finalPattern) {
                finalPattern = user.dateFormatPattern;
                if (!dateOnly) {
                    if (finalPattern) {
                        finalPattern += `${finalPattern ? ' ' : ''}${user.timeFormatPattern}`;
                    } else {
                        finalPattern = user.timeFormatPattern;
                    }
                }
            }
        }

        if (!finalPattern) {
            finalPattern = dateOnly ? DEFAULT_DATE_FORMAT : DEFAULT_DATE_TIME_FORMAT;
        }

        let specifyTimeFormat = DateTime.fromFormat(data.value, PROTECHT_DATE_TIME_FORMAT);
        if (!specifyTimeFormat.isValid) {
            specifyTimeFormat = DateTime.fromFormat(data.value, 'yyyy-MM-dd');
        }

        return specifyTimeFormat.toFormat(finalPattern);
    }
    return data.value ?? '';
};
