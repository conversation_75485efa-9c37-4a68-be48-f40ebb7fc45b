import { parse, IconProp } from '@fortawesome/fontawesome-svg-core';

/**
 * Uses parse.icon from fontawesome core to parse icon name from the BE
 * @param iconName 'fas fa-icon' or 'fa-icon'
 * @returns [prefix, iconName]
 */
export const parseIconFromString = (iconName: string | undefined): IconProp | undefined => {
    if (!iconName) {
        return undefined;
    }
    return parse.icon(iconName);
};
