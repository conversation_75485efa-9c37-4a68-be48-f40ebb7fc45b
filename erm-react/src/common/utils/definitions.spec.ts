import { dateFormatter } from './definitions';
import { DateWithFormat } from '../types';
import { ProtechtUserRest } from 'api/generated/types';

describe('dateFormatter', () => {
    const currentUser = {
        dateFormatPattern: 'dd.MM.yyyy',
        timeFormatPattern: 'hh : mm : ss a',
    } as ProtechtUserRest;

    it('Should return formatted datetime using date.dateTimePattern', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
            dateTimePattern: 'dd/MM/yyyy hh:mm:ss a',
        };

        const actual = dateFormatter(date, currentUser);
        expect(actual).toBe('23/12/2023 09:34:54 PM');
    });

    it('Should return formatted datetime using date.datePattern and date.timePattern', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
            datePattern: 'dd/MM/yyyy',
            timePattern: 'hh:mm:ss a',
        };

        const actual = dateFormatter(date, currentUser);
        expect(actual).toBe('23/12/2023 09:34:54 PM');
    });

    it('Should return formatted date using date.datePattern and date.timePattern and dateOnly = true', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
            datePattern: 'dd/MM/yyyy',
            timePattern: 'hh:mm:ss a',
        };

        const actual = dateFormatter(date, currentUser, true);
        expect(actual).toBe('23/12/2023');
    });

    it('Should return formatted date using date.datePattern', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
            datePattern: 'dd/MM/yyyy',
        };

        const actual = dateFormatter(date, currentUser);
        expect(actual).toBe('23/12/2023');
    });

    it('Should return formatted time using date.timePattern', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
            timePattern: 'hh:mm:ss a',
        };

        const actual = dateFormatter(date, currentUser);
        expect(actual).toBe('09:34:54 PM');
    });

    it('Should return formatted datetime using current user formatting', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
        };

        const actual = dateFormatter(date, currentUser);
        expect(actual).toBe('23.12.2023 09 : 34 : 54 PM');
    });

    it('Should return formatted date using current user formatting and dateOnly = true', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
        };

        const actual = dateFormatter(date, currentUser, true);
        expect(actual).toBe('23.12.2023');
    });

    it('Should return formatted datetime using default formatting', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
        };

        const actual = dateFormatter(date, null);
        expect(actual).toBe('23/12/2023 09:34:54 PM');
    });

    it('Should return formatted date using default formatting and dateOnly = true', async () => {
        const date: DateWithFormat = {
            value: '2023-12-23 21:34:54.123',
        };

        const actual = dateFormatter(date, null, true);
        expect(actual).toBe('23/12/2023');
    });
});
