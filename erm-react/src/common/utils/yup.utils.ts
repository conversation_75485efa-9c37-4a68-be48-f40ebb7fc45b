import { DEFAULT_DATE_FORMAT } from 'common/constants';
import { DateTime } from 'luxon';
import store from 'store';
import * as Yup from 'yup';
import { strings } from './i18n';
import { ValidationData, ValidationParams } from 'register/utils';
import { ColumnType } from 'register/types';
import { usersApi } from 'user/rtkApi';
import { getDateFormat, getDateTimeFormat } from './date';

export type FieldValidation = {
    controlName: string;
    controlLabel: string;
    validationData: ValidationData;
    columnType?: ColumnType;
};

export type ValidationType = 'mixed' | 'string' | 'number' | 'boolean' | 'bool' | 'date' | 'object' | 'array' | 'ref' | 'lazy';

export const createValidationSchema = (fields: FieldValidation[] = []) => {
    const schema = fields.reduce((schema, field) => {
        const { validationType, validationTypeError, validations } = field.validationData;

        let validator: Yup.AnySchema;
        validator = Yup[validationType as string]().typeError(validationTypeError ?? strings('common:validators.invalidType'));

        // allow number inputs to have empty '' value and not cause type error, '' is used as default value for controlled components
        if (validationType === 'number') {
            validator = validator['typeError'](strings('common:validators.NaN', { value: '${originalValue}' })).transform((_, value) => {
                return value === '' ? undefined : Number(value);
            });
        }

        // allow date pickers to have empty value not causing type errors, '' is used as default value for controlled components
        if (validationType === 'date') {
            validator = validator['typeError'](getDateValidationMessage(field.columnType)).transform((currentValue, value) => {
                return value === '' || value === null ? undefined : currentValue;
            });
        }

        if (validationType === 'boolean') {
            validator = validator.transform((_, value) => {
                return value === '' ? false : value;
            });
        }

        if (validationType === 'mixed') {
            validator = validator.transform((_, value) => {
                return value === '' ? undefined : value;
            });
        }

        if (validations.size) {
            let validationsArray;
            if (validationType === 'array' || validationType === 'string') {
                validationsArray = processMultipleValidationsOfSameType(validations);
            } else {
                validationsArray = Array.from(validations.entries());
            }

            validator = validationsArray.reduce((fieldValidator, [type, params]) => {
                if (!fieldValidator[type]) {
                    return fieldValidator;
                }

                const validationParams = processParams(validationType, type, params, field);
                fieldValidator = validationParams.length ? fieldValidator[type](...validationParams) : fieldValidator[type]();

                return fieldValidator;
            }, validator);
        }

        return schema.concat(Yup.object().shape({ [field.controlName]: validator }));
    }, Yup.object().shape({}));

    return schema;
};

const getDateValidationMessage = (columnType?: ColumnType): string => {
    const { data: currentUser } = usersApi.endpoints.pursGetCurrentUserUsingGet.select()(store.getState() as any);

    switch (columnType) {
        case ColumnType.TIMESTAMP:
        case ColumnType.TIMESTAMP_WITH_TIMEZONE: {
            const format = getDateTimeFormat(currentUser?.dateFormatPattern?.toUpperCase(), currentUser?.timeFormatPattern);
            return strings('common:validators.notValidDateTimeFormat', { format });
        }
        case ColumnType.DATE:
        case ColumnType.DUE_DATE:
        default: {
            const format = getDateFormat(currentUser?.dateFormatPattern?.toUpperCase());
            return strings('common:validators.notValidDateFormat', { format });
        }
    }
};

/* Use this function when validations contains multiple validations of the same type
 * For example, when the same field has multiple test validations
 */
const processMultipleValidationsOfSameType = (validations: Map<string, unknown>) => {
    const validationsArray: [string, unknown][] = [];

    validations.forEach((params, type) => {
        if (Array.isArray(params) && Array.isArray(params[0])) {
            params.forEach((validationEntry) => {
                validationsArray.push([type, validationEntry]);
            });
        } else {
            validationsArray.push([type, params]);
        }
    });

    return validationsArray;
};

const processParams = (validationType: ValidationType, type: string, params: ValidationParams, field: FieldValidation): unknown[] => {
    switch (type) {
        case 'email': {
            return [strings('common:validators.invalidFormat', { name: field.controlLabel })];
        }
        case 'max': {
            if (validationType === 'string') {
                return [Number(params), strings('common:validators.maxLength', { value: params })];
            }

            if (validationType === 'date') {
                const date = params as DateTime;
                return [
                    date,
                    strings('common:validators.maxDate', { value: date.toFormat(store.getState().app.currentUser?.dateFormatPattern ?? DEFAULT_DATE_FORMAT) }),
                ];
            }

            return [Number(params), strings('common:validators.maxValue', { value: params })];
        }
        case 'min': {
            if (validationType === 'date') {
                const date = params as DateTime;
                return [
                    date,
                    strings('common:validators.minDate', { value: date.toFormat(store.getState().app.currentUser?.dateFormatPattern ?? DEFAULT_DATE_FORMAT) }),
                ];
            }
            return [Number(params), strings('common:validators.minValue', { value: params })];
        }
        case 'required': {
            return [strings('common:validators.requiredSimple')];
        }
        case 'test': {
            return params as unknown[];
        }
        case 'transform': {
            return params as unknown[];
        }
        default: {
            return [];
        }
    }
};

export const luxonDateTime = Yup.mixed().test('luxonDateTime', strings('common:validators.notValidDate'), (value) => {
    if (!value) {
        return true; // allow empty values
    }
    return DateTime.isDateTime(value);
});
