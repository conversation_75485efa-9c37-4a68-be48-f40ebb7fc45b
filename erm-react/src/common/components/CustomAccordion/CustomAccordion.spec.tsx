import React from 'react';
import { render, screen } from 'test/utils';
import user from '@testing-library/user-event';

import CustomAccordion from 'common/components/CustomAccordion';
import { AccordionArrowStyle, IconOrientation } from 'common/components/CustomAccordion/types';

const HEADER = 'Test Header';
const BODY = 'Test Body Content';

describe('<CustomAccordion />', () => {
    const mockOnExpand = jest.fn();
    const mockOnHeaderClick = jest.fn();

    const setup = (props?: Partial<React.ComponentProps<typeof CustomAccordion>>) => {
        const defaultProps = {
            headerContent: HEADER,
            bodyContent: BODY,
        };

        return render(
            <CustomAccordion
                {...defaultProps}
                {...props}
            />,
        );
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Default Behavior', () => {
        it('renders with default configuration', () => {
            setup();

            // Header should be visible
            expect(screen.getByText(HEADER)).toBeInTheDocument();

            // Body should not be visible initially
            expect(screen.queryByText(BODY)).not.toBeInTheDocument();

            // Default chevron icon should be present
            const expandIcon = screen.getByRole('img', {
                name: `${AccordionArrowStyle.CHEVRON}-${IconOrientation.RIGHT}`,
            });
            expect(expandIcon).toBeInTheDocument();
        });

        it('expands when clicking the expand icon', async () => {
            setup();

            const expandIcon = screen.getByRole('img', {
                name: `${AccordionArrowStyle.CHEVRON}-${IconOrientation.RIGHT}`,
            });

            await user.click(expandIcon);

            expect(screen.getByText(BODY)).toBeInTheDocument();
        });

        it('collapses when clicking the expand icon again', async () => {
            setup({ defaultOpen: true });

            // Body should be visible initially
            expect(screen.getByText(BODY)).toBeInTheDocument();

            const expandIcon = screen.getByRole('img', {
                name: `${AccordionArrowStyle.CHEVRON}-${IconOrientation.RIGHT}`,
            });

            await user.click(expandIcon);

            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
        });
    });

    describe('Arrow Styles', () => {
        it('renders with triangle arrow style', () => {
            setup({ arrowStyle: AccordionArrowStyle.TRIANGLE });

            const triangleIcon = screen.getByLabelText(`${AccordionArrowStyle.TRIANGLE}-${IconOrientation.RIGHT}`);
            expect(triangleIcon).toBeInTheDocument();
            expect(triangleIcon.tagName).toBe('svg');
        });

        it('renders with caret arrow style', () => {
            setup({ arrowStyle: AccordionArrowStyle.CARET });

            const caretIcon = screen.getByRole('img', {
                name: `${AccordionArrowStyle.CARET}-${IconOrientation.RIGHT}`,
            });
            expect(caretIcon).toBeInTheDocument();
        });

        it('renders with angle arrow style', () => {
            setup({ arrowStyle: AccordionArrowStyle.ANGLE });

            const angleIcon = screen.getByRole('img', {
                name: `${AccordionArrowStyle.ANGLE}-${IconOrientation.RIGHT}`,
            });
            expect(angleIcon).toBeInTheDocument();
        });

        it('renders with chevron arrow style (default)', () => {
            setup({ arrowStyle: AccordionArrowStyle.CHEVRON });

            const chevronIcon = screen.getByRole('img', {
                name: `${AccordionArrowStyle.CHEVRON}-${IconOrientation.RIGHT}`,
            });
            expect(chevronIcon).toBeInTheDocument();
        });
    });

    describe('Icon Orientation', () => {
        it('renders with down orientation when expanded', async () => {
            setup({
                arrowStyle: AccordionArrowStyle.TRIANGLE,
                isOpenStyle: IconOrientation.DOWN,
            });

            // Initially collapsed, should have right orientation class
            const expandIcon = screen.getByLabelText(`${AccordionArrowStyle.TRIANGLE}-${IconOrientation.DOWN}`);
            expect(expandIcon).toHaveClass('rotate-270'); // Right orientation

            await user.click(expandIcon);

            // After expansion, should have down orientation class
            const expandedIcon = screen.getByLabelText(`${AccordionArrowStyle.TRIANGLE}-${IconOrientation.DOWN}`);
            expect(expandedIcon).toHaveClass('rotate-0'); // Down orientation
        });

        it('renders with right orientation when collapsed', () => {
            setup({
                arrowStyle: AccordionArrowStyle.TRIANGLE,
                isOpenStyle: IconOrientation.DOWN,
            });

            const collapsedIcon = screen.getByLabelText(`${AccordionArrowStyle.TRIANGLE}-${IconOrientation.DOWN}`);
            expect(collapsedIcon).toHaveClass('rotate-270'); // Right orientation when collapsed
        });
    });

    describe('Expandable Property', () => {
        it('does not render expand icon when not expandable', () => {
            setup({ expandable: false });

            expect(screen.getByText(HEADER)).toBeInTheDocument();
            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
            expect(screen.queryByRole('img')).not.toBeInTheDocument();
        });

        it('shows body content when not expandable but defaultOpen is true', () => {
            setup({ expandable: false, defaultOpen: true });

            expect(screen.getByText(HEADER)).toBeInTheDocument();
            expect(screen.getByText(BODY)).toBeInTheDocument();
            expect(screen.queryByRole('img')).not.toBeInTheDocument();
        });
    });

    describe('Controlled Mode', () => {
        it('respects isExpanded prop in controlled mode', () => {
            setup({ isControlled: true, isExpanded: true });

            expect(screen.getByText(BODY)).toBeInTheDocument();
        });

        it('does not show body when isExpanded is false in controlled mode', () => {
            setup({ isControlled: true, isExpanded: false });

            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
        });

        it('calls onExpand when expand icon is clicked in controlled mode', async () => {
            setup({
                isControlled: true,
                isExpanded: false,
                onExpand: mockOnExpand,
            });

            const expandIcon = screen.getByRole('img');
            await user.click(expandIcon);

            expect(mockOnExpand).toHaveBeenCalledTimes(1);
        });

        it('does not change internal state in controlled mode', async () => {
            setup({
                isControlled: true,
                isExpanded: false,
                onExpand: mockOnExpand,
            });

            const expandIcon = screen.getByRole('img');
            await user.click(expandIcon);

            // Body should still not be visible since isExpanded is still false
            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
        });
    });

    describe('Header Interactions', () => {
        it('calls onHeaderClick when header is clicked', async () => {
            setup({ onHeaderClick: mockOnHeaderClick });

            const header = screen.getByText(HEADER);
            await user.click(header);

            expect(mockOnHeaderClick).toHaveBeenCalledTimes(1);
        });

        it('toggles accordion when headerToggle is true and header is clicked', async () => {
            setup({ headerToggle: true });

            const header = screen.getByText(HEADER);
            await user.click(header);

            expect(screen.getByText(BODY)).toBeInTheDocument();
        });

        it('does not toggle accordion when headerToggle is false and header is clicked', async () => {
            setup({ headerToggle: false });

            const header = screen.getByText(HEADER);
            await user.click(header);

            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
        });

        it('does not toggle when headerToggle is true but expandable is false', async () => {
            setup({ headerToggle: true, expandable: false });

            const header = screen.getByText(HEADER);
            await user.click(header);

            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
        });
    });

    describe('Disabled State', () => {
        it('does not respond to clicks when disabled', async () => {
            setup({ disabled: true, onExpand: mockOnExpand });

            const expandIcon = screen.getByRole('img');
            await user.click(expandIcon);

            expect(mockOnExpand).not.toHaveBeenCalled();
            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
        });

        it('does not respond to header clicks when disabled', async () => {
            setup({
                disabled: true,
                headerToggle: true,
                onHeaderClick: mockOnHeaderClick,
            });

            const header = screen.getByText(HEADER);
            await user.click(header);

            expect(mockOnHeaderClick).not.toHaveBeenCalled();
            expect(screen.queryByText(BODY)).not.toBeInTheDocument();
        });
    });

    describe('Content Rendering', () => {
        it('renders complex header content', () => {
            const complexHeader = (
                <div>
                    <span>Title</span>
                    <button>Action</button>
                </div>
            );

            setup({ headerContent: complexHeader });

            expect(screen.getByText('Title')).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Action' })).toBeInTheDocument();
        });

        it('renders complex body content', async () => {
            const complexBody = (
                <div>
                    <p>Paragraph 1</p>
                    <p>Paragraph 2</p>
                    <input placeholder="Test input" />
                </div>
            );

            setup({ bodyContent: complexBody, defaultOpen: true });

            expect(screen.getByText('Paragraph 1')).toBeInTheDocument();
            expect(screen.getByText('Paragraph 2')).toBeInTheDocument();
            expect(screen.getByPlaceholderText('Test input')).toBeInTheDocument();
        });
    });

    describe('Accessibility', () => {
        it('has proper aria labels for expand icons', () => {
            setup({ arrowStyle: AccordionArrowStyle.TRIANGLE });

            const expandIcon = screen.getByLabelText(`${AccordionArrowStyle.TRIANGLE}-${IconOrientation.RIGHT}`);
            expect(expandIcon).toHaveAttribute('aria-hidden', 'false');
        });

        it('maintains focus management when expanding/collapsing', async () => {
            setup();

            const expandIcon = screen.getByRole('img');
            expandIcon.focus();

            await user.click(expandIcon);

            // Icon should still be focusable after click
            expect(expandIcon).toBeInTheDocument();
        });
    });

    describe('Edge Cases', () => {
        it('handles undefined body content gracefully', () => {
            setup({ bodyContent: undefined, defaultOpen: true });

            expect(screen.getByText(HEADER)).toBeInTheDocument();
            // Should not crash when body content is undefined
        });

        it('handles null header content gracefully', () => {
            setup({ headerContent: null });

            // Should not crash when header content is null
            expect(screen.queryByText(HEADER)).not.toBeInTheDocument();
        });

        it('handles rapid toggle clicks', async () => {
            setup({ onExpand: mockOnExpand });

            const expandIcon = screen.getByRole('img');

            // Rapidly click multiple times
            await user.click(expandIcon);
            await user.click(expandIcon);
            await user.click(expandIcon);

            expect(mockOnExpand).toHaveBeenCalledTimes(3);
        });
    });
});
