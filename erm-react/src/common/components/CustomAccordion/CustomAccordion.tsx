import React, { useCallback } from 'react';
import { alpha, styled, SxProps, useTheme } from '@mui/material/styles';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleUp, faCaretUp, faChevronUp } from '@fortawesome/pro-solid-svg-icons';
import { AccordionArrowStyle, IconOrientation } from './types';
import { TriangleDown } from '@protecht/ui-library/library/components/SVGIcons';
import Box from '@mui/material/Box';
import { getIconRotation, getTriangleRotation } from './utils';
import { textPrimary } from '@protecht/ui-library/library/theme/colors/colors';

interface Props {
    defaultOpen?: boolean;
    arrowStyle?: AccordionArrowStyle;
    isOpenStyle?: IconOrientation;
    disabled?: boolean;
    expandable?: boolean;
    onExpand?: () => void;
    onHeaderClick?: () => void;
    onContextMenu?: (event: React.MouseEvent<HTMLDivElement>) => void;
    selected?: boolean;
    headerToggle?: boolean;
    headerContent: any;
    bodyContent: any;
    isControlled?: boolean;
    isExpanded?: boolean;
    headlineSx?: SxProps;
    isNodeSelectable?: boolean;
}

const WrapperAccordion = styled('div', {
    shouldForwardProp: (prop) => prop !== 'disabled',
})<{ disabled: boolean }>(({ disabled }) => ({
    display: 'flex',
    flexDirection: 'column',
    opacity: disabled ? 0.2 : 1,
}));

const Headline = styled('div', {
    shouldForwardProp: (prop) => prop !== 'headerToggle',
})<{ headerToggle: boolean }>(({ headerToggle }) => ({
    display: 'flex',
    flexDirection: 'row',
    cursor: headerToggle ? 'pointer' : 'default',
    padding: '3px 6px',
    overflow: 'hidden',
    '&.selected': {
        backgroundColor: alpha(textPrimary, 0.15),
    },
    '&.selectable:hover': {
        backgroundColor: alpha(textPrimary, 0.08),
    },
}));

const DropdownIcon = styled('div')(({ theme }) => ({
    '&:hover': {
        transform: 'scale(1.3)',
    },
    color: theme.palette.primary.main,
    width: '24px',
    alignSelf: 'center',
    cursor: 'pointer',
    padding: '5px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
}));

const NoIcon = styled('div')({
    width: '24px',
    padding: '5px',
    marginRight: '5px',
});

const CustomAccordion: React.FC<Props> = (props: Props) => {
    const {
        defaultOpen = false,
        arrowStyle = AccordionArrowStyle.CHEVRON,
        isOpenStyle = IconOrientation.RIGHT,
        disabled = false,
        expandable = true,
        headerToggle = false,
        selected,
        onExpand,
        onHeaderClick,
        onContextMenu,
        headerContent,
        bodyContent,
        isControlled = false,
        isExpanded = undefined,
        headlineSx,
        isNodeSelectable = !expandable,
    } = props;

    const theme = useTheme();

    const [isOpen, setIsOpen] = React.useState(defaultOpen || (isControlled && isExpanded));

    const handleToggle = useCallback(() => {
        if (onExpand) {
            onExpand();
        }
        if (!isControlled) {
            setIsOpen((prevOpen) => !prevOpen);
        }
    }, [isControlled, onExpand]);

    const handleClick = useCallback(() => {
        headerToggle && expandable && setIsOpen((prevOpen) => !prevOpen);
        if (onHeaderClick) {
            onHeaderClick();
        }
    }, [expandable, headerToggle, onHeaderClick]);

    const handleContextMenu = useCallback(
        (event: React.MouseEvent<HTMLDivElement>) => {
            if (onContextMenu) {
                event.preventDefault();
                if (expandable) {
                    onContextMenu(event);
                }
            }
        },
        [expandable, onContextMenu],
    );

    const getIcon = useCallback((type: AccordionArrowStyle) => {
        switch (type) {
            case AccordionArrowStyle.CARET:
                return faCaretUp;
            case AccordionArrowStyle.CHEVRON:
                return faChevronUp;
            default:
                return faAngleUp;
        }
    }, []);

    return (
        <WrapperAccordion disabled={disabled}>
            <Headline
                headerToggle={headerToggle}
                className={[selected ? 'selected' : '', isNodeSelectable ? 'selectable' : ''].join(' ')}
                onContextMenu={(event: any) => handleContextMenu(event)}
                sx={headlineSx}
            >
                {expandable && arrowStyle === AccordionArrowStyle.TRIANGLE && (
                    <TriangleDown
                        aria-hidden={false}
                        aria-label={`${arrowStyle}-${isOpenStyle}`}
                        className={getTriangleRotation(isOpen || (isControlled && isExpanded) ? isOpenStyle : IconOrientation.RIGHT)}
                        color={theme.palette.primary.main}
                        key={`${arrowStyle}-${isOpenStyle}`}
                        name="triangle-down"
                        onClick={!disabled ? handleToggle : undefined}
                        style={{ cursor: 'pointer', flexShrink: 0, marginTop: '4px' }}
                    />
                )}

                {expandable && arrowStyle !== AccordionArrowStyle.TRIANGLE && (
                    <DropdownIcon
                        key={`${arrowStyle}-${isOpenStyle}`}
                        onClick={!disabled ? handleToggle : undefined}
                    >
                        <FontAwesomeIcon
                            name="triangle-down"
                            aria-label={`${arrowStyle}-${isOpenStyle}`}
                            aria-hidden={false}
                            icon={getIcon(arrowStyle)}
                            className={getIconRotation(isOpen || (isControlled && isExpanded) ? isOpenStyle : IconOrientation.RIGHT)}
                        />
                    </DropdownIcon>
                )}
                {!expandable && <NoIcon />}
                <Box
                    sx={{ width: '100%', overflow: 'hidden', marginLeft: '4px' }}
                    onClick={!disabled ? handleClick : undefined}
                >
                    {headerContent}
                </Box>
            </Headline>
            <div style={{ marginLeft: '20px' }}>{(isOpen || (isControlled && isExpanded)) && bodyContent}</div>
        </WrapperAccordion>
    );
};

export default CustomAccordion;
