import React from 'react';
import { render, screen } from 'test/utils';
import IconPicker from 'common/components/IconPicker';
import { IconType } from 'common/components/IconPicker/types';
import { waitFor, within, act } from '@testing-library/react';
import { strings } from 'common/utils/i18n';
import userEvent from '@testing-library/user-event';

describe('<IconPicker />', () => {
    const onIconChanged = jest.fn();
    const iconStr = 'fab fa-accessible-icon';

    const setup = () => {
        render(
            <IconPicker
                iconStr={iconStr}
                iconType={IconType.PREDEFINED}
                onChange={onIconChanged}
            />,
        );
    };

    it('was rendered', () => {
        setup();
        expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('opens dialog', async () => {
        setup();
        expect(screen.getByRole('button')).toBeInTheDocument();
        // check dialog not in the document
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();

        // click the button to open the dialog
        await userEvent.click(screen.getByRole('button'));
        // check dialog visible
        expect(screen.queryByRole('dialog')).toBeVisible();
    });

    it('does not call onChange on dialog close when icon not changed', async () => {
        setup();
        // click the button to open the dialog
        expect(screen.getByRole('button')).toBeInTheDocument();
        await userEvent.click(screen.getByRole('button'));

        // check dialog visible
        const dialog = screen.getByRole('dialog');
        expect(dialog).toBeVisible();

        // click OK button to close the dialog
        const okButton = within(dialog).getByRole('button', { name: strings('common:button.ok') });
        await userEvent.click(okButton);

        // check onChange NOT called
        await waitFor(() => expect(onIconChanged).toHaveBeenCalledTimes(0));
        // check dialog NOT visible
        expect(dialog).not.toBeVisible();
    });

    it('calls onChange on dialog close when icon changed', async () => {
        setup();
        // click the button to open the dialog
        expect(screen.getByRole('button')).toBeInTheDocument();
        await userEvent.click(screen.getByRole('button'));

        // check dialog visible
        const dialog = screen.getByRole('dialog');
        expect(dialog).toBeVisible();

        // change icon
        const image = within(dialog).getByTestId('icon-far fa-address-book');

        act(() => {
            image.dispatchEvent(new MouseEvent('click', { bubbles: true }));
        });

        await waitFor(() => {
            expect(screen.getByTestId('selected-icon-far fa-address-book')).toBeInTheDocument();
        });

        // click OK button to close the dialog
        const okButton = within(dialog).getByRole('button', { name: strings('common:button.ok') });
        await userEvent.click(okButton);

        // check dialog NOT visible
        expect(dialog).not.toBeVisible();
    });
});
