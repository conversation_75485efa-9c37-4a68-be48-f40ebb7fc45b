import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICONS } from 'common/utils/icons/ermIcons';
import { strings } from 'common/utils/i18n';
import { IconType } from './types';
import IconButton from '@mui/material/IconButton';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import useTheme from '@mui/system/useTheme';
import { emphasize } from '@mui/system';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';

interface TabPanelProps {
    children?: React.ReactNode;
    index: any;
    value: any;
}

const TabPanel = (props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`tab-${index}`}
            aria-labelledby={`tab-panel-${index}`}
            style={{ height: 'calc(100% - 48px)' }}
            {...other}
        >
            {value === index && <div style={{ height: '100%' }}>{children}</div>}
        </div>
    );
};

type Props = {
    iconStr: string;
    iconType: IconType;
    onChange: (iconStr: string) => void;
};

const IconContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    overflowY: 'scroll',
    height: '100%',
    flexGrow: 1,
});

const StyledFaIcon = styled(FontAwesomeIcon, {
    shouldForwardProp: (prop) => prop != 'availableIcon',
})<{ availableIcon?: boolean }>(({ theme, availableIcon }) => ({
    minHeight: 32,
    minWidth: 32,
    padding: 8,
    '&:hover': {
        color: theme.palette.primary.main,
    },
    ...(availableIcon && {
        backgroundColor: emphasize(theme.palette.primary.main, 0.08),
        color: theme.palette.primary.contrastText,
        '&:hover': {
            color: theme.palette.primary.contrastText,
        },
    }),
}));

const TextLabel = styled(Typography)(({ theme }) => ({
    paddingTop: theme.spacing(1),
}));
const TextValue = styled(Typography)(({ theme }) => ({
    paddingLeft: theme.spacing(1),
}));

const IconPicker: React.FC<Props> = (props: Props) => {
    const { iconStr, iconType, onChange } = props;
    const theme = useTheme();

    const [visible, setVisible] = useState<boolean>(false);
    const [icon, setIcon] = useState<string>(iconStr);
    const [tab, setTab] = useState<IconType>(iconType);

    const handleOpen = () => {
        setIcon(iconStr);
        setVisible(true);
    };

    const handleClose = () => {
        setVisible(false);
    };

    const handleConfirm = () => {
        if (icon !== iconStr) {
            onChange(icon);
        }
        setVisible(false);
    };

    const handleTabChange = (_event: React.SyntheticEvent, newValue: IconType) => {
        setTab(newValue);
    };

    const iconData = ICONS[icon];

    return (
        <div>
            <IconButton
                aria-label="change-icon"
                onClick={handleOpen}
                size="large"
            >
                <FontAwesomeIcon icon={ICONS[iconStr]?.definition} />
            </IconButton>
            <Dialog
                visible={visible}
                title={strings('common:title.chooseIcon')}
                onOutsideClickClose={handleClose}
                height={480}
                width={792}
                dialogActions={
                    <DialogActions>
                        <Button
                            {...ButtonStyles.dialogButton}
                            variant={'secondary'}
                            onClick={handleClose}
                            dataTestId="button-cancel"
                        >
                            {strings('common:button.cancel')}
                        </Button>
                        <Button
                            {...ButtonStyles.dialogButton}
                            type="submit"
                            dataTestId="button-confirm"
                            disabled={!icon}
                            onClick={handleConfirm}
                        >
                            {strings('ermMessages:btn_okay')}
                        </Button>
                    </DialogActions>
                }
            >
                <Tabs
                    value={tab}
                    aria-label="icon-type-tabs"
                    onChange={handleTabChange}
                    style={{ height: 48 }}
                >
                    <Tab
                        label={strings('common:label.predefined')}
                        id="tab-predefined"
                        aria-controls="tab-panel-predefined"
                    />
                    <Tab
                        label={strings('common:label.custom')}
                        id="tab-custom"
                        aria-controls="tab-panel-custom"
                    />
                </Tabs>
                <TabPanel
                    index={IconType.PREDEFINED}
                    value={tab}
                >
                    <div style={{ display: 'flex', height: '100%' }}>
                        <IconContainer>
                            {Object.keys(ICONS).map((availableIcon) => {
                                return (
                                    <StyledFaIcon
                                        key={availableIcon}
                                        data-testid={availableIcon === icon ? 'selected-icon-' + availableIcon : 'icon-' + availableIcon}
                                        icon={ICONS[availableIcon].definition}
                                        availableIcon={Boolean(availableIcon)}
                                        onClick={() => {
                                            setIcon(availableIcon);
                                        }}
                                    />
                                );
                            })}
                        </IconContainer>
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                padding: 10,
                            }}
                        >
                            <div
                                style={{
                                    width: 150,
                                    paddingLeft: 32,
                                    paddingRight: 32,
                                    paddingBottom: 1,
                                    fontSize: 48,
                                    textAlign: 'center',
                                }}
                            >
                                <FontAwesomeIcon icon={iconData.definition} />
                            </div>
                            <div
                                style={{
                                    borderTop: `1px solid ${theme.palette.protechtGrey?.grey_231}`,
                                }}
                            >
                                <TextLabel variant="body3">{strings('common:label.iconName') + ':'}</TextLabel>
                                <TextValue variant="body1">{iconData.label}</TextValue>
                                <TextLabel variant="body3">{strings('common:label.categories') + ':'}</TextLabel>
                                <TextValue variant="body1">{iconData.categories.join(', ')}</TextValue>
                            </div>
                        </div>
                    </div>
                </TabPanel>
                <TabPanel
                    index={IconType.CUSTOM}
                    value={iconType}
                >
                    {/*TODO*/}
                </TabPanel>
            </Dialog>
        </div>
    );
};

export default IconPicker;
