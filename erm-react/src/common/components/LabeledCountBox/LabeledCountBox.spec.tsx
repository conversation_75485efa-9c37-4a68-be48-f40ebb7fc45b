import React from 'react';
import { render, screen } from 'test/utils';
import LabeledCountBox from './LabeledCountBox';

describe('Labeled count component <LabeledCountBox/>', () => {
    it('Should display LabeledCountBox with count and number', async () => {
        render(
            <LabeledCountBox
                label={'Test Label'}
                count={12345}
                countColor={'#222222'}
            />,
        );

        expect(screen.getByText('Test Label')).toBeInTheDocument();
        expect(screen.getByText('12345')).toBeInTheDocument();
    });
});
