import { StoryFn, Meta } from '@storybook/react';
import React from 'react';
import LabeledCountBox from './LabeledCountBox';

export default {
    title: 'components/UI/Labeled Count Box',
    component: LabeledCountBox,
    parameters: {
        docs: {
            description: {
                component: 'Box component containing colored count and label',
            },
        },
    },
} as Meta<typeof LabeledCountBox>;

const Template: StoryFn<typeof LabeledCountBox> = (args) => <LabeledCountBox {...args} />;

export const Default: StoryFn<typeof LabeledCountBox> = Template.bind({});
Default.args = {
    count: 123,
    label: 'Lorem ipsum',
};

export const WithColoredCount: StoryFn<typeof LabeledCountBox> = Template.bind({});
WithColoredCount.args = {
    ...Default.args,
    countColor: 'red',
};
