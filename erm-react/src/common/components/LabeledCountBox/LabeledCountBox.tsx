import React from 'react';
import Box from '@mui/material/Box';
import { BoxProps } from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import useTheme from '@mui/system/useTheme';

export type Props = BoxProps & {
    count?: number;
    countColor?: string;
    label: string;
};

const LabeledCountBox: React.FC<Props> = ({ count, countColor, label, sx, ...others }: Props) => {
    const theme = useTheme();

    return (
        <Box
            sx={{
                borderWidth: '1px',
                borderStyle: 'solid',
                borderColor: theme.palette.protechtGrey?.grey_238,
                padding: '16px',
                ...sx,
            }}
            {...others}
        >
            <Grid
                container
                direction={'column'}
                justifyContent={'center'}
                alignItems={'flex-start'}
            >
                <Grid
                    item
                    sx={{ minHeight: '48px' }}
                >
                    <Typography sx={{ fontSize: '32px', fontWeight: 600, color: countColor }}>{count}</Typography>
                </Grid>
                <Grid item>
                    <Typography variant={'h6'}>{label}</Typography>
                </Grid>
            </Grid>
        </Box>
    );
};

export default LabeledCountBox;
