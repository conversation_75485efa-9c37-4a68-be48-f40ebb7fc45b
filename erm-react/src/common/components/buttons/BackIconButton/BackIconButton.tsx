import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import IconButton from '@mui/material/IconButton';
import useTheme from '@mui/system/useTheme';
import { IconButtonProps } from '@mui/material/IconButton';
import { strings } from 'common/utils/i18n';

type Props = IconButtonProps & {
    iconColor?: string;
    dataTestId?: string;
};

const BackIconButton: React.FC<Props> = ({
    dataTestId,
    iconColor,
    color = 'secondary',
    title = strings('common:button.back'),
    children,
    sx,
    ...others
}: Props) => {
    const theme = useTheme();

    return (
        <IconButton
            {...others}
            color={color}
            title={title}
            data-testid={dataTestId ?? `button-${others.key}`}
            sx={{
                ...{
                    boxShadow: 'none',
                    padding: '3px',
                    '&:active': {
                        '& > *:first-of-type': {
                            color: iconColor ?? theme.palette.primary.main,
                        },
                    },
                    height: '40px',
                    width: '40px',
                    left: '-1px',
                },
                ...sx,
            }}
        >
            {children || (
                <FontAwesomeIcon
                    icon={faArrowLeft}
                    style={{ fontSize: '22px' }}
                    color={iconColor ?? theme.palette.primary.main}
                />
            )}
        </IconButton>
    );
};

export default BackIconButton;
