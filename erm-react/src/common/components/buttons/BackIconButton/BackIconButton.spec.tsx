import React from 'react';
import { render, screen } from 'test/utils';
import user from '@testing-library/user-event';
import BackIconButton from './BackIconButton';

describe('Back Icon Button <BackIconButton/>', () => {
    it('has a back icon button', async () => {
        const onClickMock = jest.fn();
        render(<BackIconButton onClick={onClickMock} />);
        const btn = screen.getByRole('button');
        expect(btn).toBeInTheDocument();
        await user.click(btn);
        expect(onClickMock).toHaveBeenCalled();
    });
});
