import styled from '@mui/system/styled';
import Button from '@protecht/ui-library/library/components/Button';

// TODO: replace this when the Button component is updated
// This is a temporary solution to override the styles for the Button
// It ensures that the button label remains visible even at small screen breakpoints
export const StyledButton = styled(Button)(({ theme }) => ({
    '& span:nth-of-type(2)': {
        display: 'block !important',
        [theme.breakpoints.down('sm')]: {
            display: 'block !important',
        },
    },
}));
