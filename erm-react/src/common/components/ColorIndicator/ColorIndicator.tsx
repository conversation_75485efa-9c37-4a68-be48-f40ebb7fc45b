import Box, { BoxProps } from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import React from 'react';
import { CUSTOM_VALUE } from './const';

export type ColorIndicatorProps = Omit<BoxProps, 'bgcolor' | 'width' | 'height' | 'borderRadius' | 'border'> & {
    value?: string;
};

export const ColorIndicator: React.FC<ColorIndicatorProps> = ({ value = '#fff', ...otherProps }) => {
    const theme = useTheme();

    return (
        <Box
            bgcolor={value === CUSTOM_VALUE ? '#ffffff' : value}
            width="24px"
            height="16px"
            borderRadius="2px"
            border={value === CUSTOM_VALUE ? `1px solid ${theme.palette.protechtGrey?.grey_192}` : undefined}
            {...otherProps}
        ></Box>
    );
};

export default ColorIndicator;
