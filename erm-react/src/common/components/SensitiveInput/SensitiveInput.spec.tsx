import React from 'react';
import { render, screen } from 'test/utils';
import SensitiveInput from './SensitiveInput';
import { labels as expectedLabels } from './const';
import type { SensitiveInputProps } from '@protecht/ui-library/library/components/Inputs/SensitiveInput';

const MockSensitiveInputComponent = jest.fn((props: SensitiveInputProps) => (
    <span
        data-testid="mock-sensitive-input"
        {...props}
    />
));

jest.mock('@protecht/ui-library/library/components/Inputs/SensitiveInput', () => ({
    __esModule: true,
    default: (props: SensitiveInputProps) => MockSensitiveInputComponent(props),
}));

describe('SensitiveInput Wrapper', () => {
    beforeEach(() => {
        MockSensitiveInputComponent.mockClear();
    });

    it('renders the underlying SensitiveInputComponent', () => {
        render(<SensitiveInput id="test-field" />);
        expect(screen.getByTestId('mock-sensitive-input')).toBeInTheDocument();
    });

    it('passes the correct labels prop to the underlying component', () => {
        render(<SensitiveInput id="test-field" />);
        expect(MockSensitiveInputComponent).toHaveBeenCalledTimes(1);
        const actualProps = MockSensitiveInputComponent.mock.calls[0][0];
        expect(actualProps.labels).toEqual(expectedLabels);
    });

    it('passes other props down to the underlying component', () => {
        const onClick = jest.fn();
        const testProps = {
            id: 'test-field',
            onClick: onClick,
        };

        render(<SensitiveInput {...testProps} />);

        expect(MockSensitiveInputComponent).toHaveBeenCalledTimes(1);
        const actualProps = MockSensitiveInputComponent.mock.calls[0][0];

        expect(actualProps.id).toBe(testProps.id);
        expect(actualProps.onClick).toBe(testProps.onClick);
    });
});
