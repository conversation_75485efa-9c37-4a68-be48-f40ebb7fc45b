import React from 'react';
import SensitiveInputComponent, { SensitiveInputProps } from '@protecht/ui-library/library/components/Inputs/SensitiveInput';

import { labels } from './const';

const SensitiveInput = React.forwardRef<HTMLInputElement, SensitiveInputProps>((props, ref) => {
    return (
        <SensitiveInputComponent
            ref={ref}
            labels={labels}
            {...props}
        />
    );
});

SensitiveInput.displayName = 'SensitiveInput';

export default SensitiveInput;
