import { render, screen } from 'test/utils';
import TextWithHyperlink from './TextWithHyperlink';
import React from 'react';
import { faArrowUpRightFromSquare } from '@fortawesome/pro-regular-svg-icons';

/**
 * TextWithHyperlink component tests.
 */
describe('<TextWithHyperlink/>', () => {
    const message = "Vendor's internet domain as entered here was not recognized in SecurityScorecard";
    const anchorText = 'SecurityScorecard';
    const hyperLink = 'https://platform.securityscorecard.io/#/scorecard/protechtgroup.com';

    it('was rendered with default params', async () => {
        render(
            <TextWithHyperlink
                text={message}
                hyperlink={hyperLink}
                anchorText={anchorText}
                anchorLastTextOccurrence={true}
            />,
        );

        const linkElement = screen.getByRole('link', { name: anchorText });
        expect(linkElement).toHaveAttribute('href', hyperLink);
        expect(linkElement).toHaveTextContent(anchorText);

        expect(document.body).toMatchSnapshot();
    });

    it('was rendered with icon at the end of anchor text', async () => {
        render(
            <TextWithHyperlink
                text={message}
                hyperlink={hyperLink}
                anchorText={anchorText}
                anchorLastTextOccurrence={true}
                addIconToAnchorTextEnd={true}
                iconToAdd={faArrowUpRightFromSquare}
                openHyperlinkInNewTab={true}
                underlineAnchorText={true}
            />,
        );

        const linkElement = screen.getByRole('link', { name: anchorText });
        expect(linkElement).toHaveAttribute('href', hyperLink);
        expect(linkElement).toHaveAttribute('target', '_blank');
        expect(linkElement).toHaveStyle('text-decoration: none;');
        expect(linkElement).toHaveTextContent(anchorText);

        expect(document.body).toMatchSnapshot();
    });
});
