import React from 'react';
import Box from '@mui/material/Box';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUpRightFromSquare } from '@fortawesome/pro-regular-svg-icons';
import { IconProp } from '@fortawesome/fontawesome-svg-core';

type Props = {
    text: string;
    anchorText: string | undefined;
    anchorLastTextOccurrence: boolean | true;
    underlineAnchorText?: boolean | false;
    addIconToAnchorTextEnd?: boolean | false;
    iconToAdd?: IconProp | undefined;
    hyperlink: string | undefined;
    openHyperlinkInNewTab?: boolean | true;
};

/**
 * This component allows displaying a provided text in which it contains a hyperlink.
 *
 * @param text The entire text to display including the anchor text
 * @param anchorText The anchor text to be hyperlinked
 * @param anchorLastTextOccurence Flag to control the anchoring of last text occurrence only. Otherwise, all matching text will be anchored.
 * @param underlineAnchorText Flag to control if the anchor text should be underlined
 * @param addIconToAnchorTextEnd Flag to add the fontawesome icon to the end of anchor text
 * @param iconToAdd The IconProp to be added to the end of anchor text
 * @param hyperlink The hyperlink
 * @param openHyperlinkInNewTab Flag to control if the browser should open the hyperlink in new browser tab
 * @constructor
 */
const TextWithHyperlink = ({
    text = '',
    anchorText = '',
    anchorLastTextOccurrence,
    underlineAnchorText,
    addIconToAnchorTextEnd,
    iconToAdd,
    hyperlink,
    openHyperlinkInNewTab,
}: Props): JSX.Element => {
    anchorText = anchorText.trim();

    if (!anchorText) {
        return <Box component={'span'}>{text}</Box>;
    }

    const regexMatchingAnchorText = new RegExp(`(${anchorText})`, 'gi');
    const parts = text.split(regexMatchingAnchorText).filter(String);

    return (
        <span>
            {parts.map((part, partIndex) => {
                const lastPartIndex = parts.length > 0 ? parts.length - 1 : 0;
                return regexMatchingAnchorText.test(part) && (anchorLastTextOccurrence ? partIndex == lastPartIndex : true) ? (
                    <a
                        key=""
                        href={hyperlink}
                        style={underlineAnchorText ? { textDecoration: 'none' } : {}}
                        target={openHyperlinkInNewTab ? '_blank' : undefined}
                        rel="noreferrer"
                    >
                        {anchorText}
                        {addIconToAnchorTextEnd ? (
                            <>
                                {' '}
                                <sup>
                                    <FontAwesomeIcon
                                        icon={iconToAdd ? iconToAdd : faArrowUpRightFromSquare}
                                        size="xs"
                                    />
                                </sup>
                            </>
                        ) : (
                            <></>
                        )}
                    </a>
                ) : (
                    <span key={partIndex}>{part}</span>
                );
            })}
        </span>
    );
};

export default TextWithHyperlink;
