import React, { FC, useRef } from 'react';
import { useDrag, useDrop, DropTargetMonitor } from 'react-dnd';
import { XYCoord } from 'dnd-core';
import ListItem from '@mui/material/ListItem';

export type ReorderableListItemProps = {
    id: string;
    index: number;
    type: string;
    moveItem: (dragIndex: number, hoverIndex: number) => void;
    onSelect: (event: React.MouseEvent<Element>) => void;
    onContextMenu: (event: React.MouseEvent<Element>) => void;
    children: React.ReactNode;
    selected: boolean;
    style?: any;
    className?: string;
};

type DragItem = {
    index: number;
    id: string;
    type: string;
};

const ReorderableListItem: FC<ReorderableListItemProps> = ({
    id,
    index,
    moveItem,
    style,
    children,
    selected,
    type,
    onSelect,
    onContextMenu,
    className,
}: ReorderableListItemProps) => {
    const ref = useRef<HTMLDivElement>(null);

    const [{ handlerId }, drop] = useDrop({
        accept: type,
        collect(monitor) {
            return {
                handlerId: monitor.getHandlerId(),
            };
        },
        hover(item: DragItem, monitor: DropTargetMonitor) {
            if (!ref.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;

            // Don't replace items with themselves
            if (dragIndex === hoverIndex) {
                return;
            }

            // Determine rectangle on screen
            const hoverBoundingRect = ref.current?.getBoundingClientRect();

            // Get vertical middle
            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

            // Determine mouse position
            const clientOffset = monitor.getClientOffset();

            // Get pixels to the top
            const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

            // Only perform the move when the mouse has crossed half of the items height
            // When dragging downwards, only move when the cursor is below 50%
            // When dragging upwards, only move when the cursor is above 50%

            // Dragging downwards
            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                return;
            }

            // Dragging upwards
            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                return;
            }

            // Time to actually perform the action
            moveItem(dragIndex, hoverIndex);

            // Note: we're mutating the monitor item here!
            // Generally it's better to avoid mutations,
            // but it's good here for the sake of performance
            // to avoid expensive index searches.
            item.index = hoverIndex;
        },
    });

    const [{ isDragging }, drag] = useDrag({
        type: type,
        item: () => {
            const idStr = String(id);
            return { id: idStr, index };
        },
        collect: (monitor: any) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    const opacity = isDragging ? 0 : 1;

    drag(drop(ref));

    return (
        <ListItem
            component="div"
            ref={ref}
            key={id}
            onClick={onSelect}
            onContextMenu={onContextMenu}
            data-handler-id={handlerId}
            style={{
                ...style,
                cursor: isDragging && 'move',
                opacity,
            }}
            selected={selected || isDragging}
            className={className}
        >
            {children}
        </ListItem>
    );
};

export default ReorderableListItem;
