import React from 'react';
import Typography from '@mui/material/Typography';
import Grid, { GridProps } from '@mui/material/Grid';
import { useTheme } from '@mui/material/styles';

type SectionProps = GridProps & {
    title: string;
    contentBorder?: boolean;
};

const Section: React.FC<SectionProps> = ({ children, title, contentBorder = true, ...other }) => {
    const theme = useTheme();

    return (
        <Grid
            data-testid="section"
            {...other}
            container
            direction="column"
            flexWrap="nowrap"
            maxHeight="100%"
        >
            <Grid
                item
                flex="0 0 auto"
            >
                <Typography
                    paddingY={1.4}
                    paddingX={2}
                    bgcolor={theme.palette.protechtGrey?.grey_238}
                    borderLeft={`6px solid ${theme.palette.protechtGrey?.grey_206}`}
                    variant="h5"
                    data-testid="section-title"
                >
                    {title}
                </Typography>
            </Grid>
            <Grid
                item
                flex="1 1 auto"
                sx={{ overflowY: 'auto' }}
                bgcolor={theme.palette.protechtGrey?.white}
                border={contentBorder ? '1px solid' : 'none'}
                borderTop="none"
                borderColor={theme.palette.protechtGrey?.grey_220}
                padding={2.8}
                data-testid="section-content"
            >
                {children}
            </Grid>
        </Grid>
    );
};

export default Section;
