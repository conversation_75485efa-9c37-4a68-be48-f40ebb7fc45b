import React, { ReactNode } from 'react';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import useTheme from '@mui/system/useTheme';

export type SidePanelProps = {
    title: string;
    children: ReactNode;
    actionButton?: ReactNode;
};

const SidePanel: React.FC<SidePanelProps> = (props: SidePanelProps) => {
    const theme = useTheme();
    const { title, children, actionButton } = props;

    return (
        <Grid
            container
            direction="column"
        >
            <Grid
                container
                justifyContent="space-between"
                sx={{ marginBottom: theme.spacing(1) }}
            >
                <Grid item>
                    <Typography variant="h8">{title}</Typography>
                </Grid>
                <Grid item>{actionButton}</Grid>
            </Grid>
            <Grid
                item
                style={{ width: '100%' }}
            >
                {children}
            </Grid>
        </Grid>
    );
};

export default SidePanel;
