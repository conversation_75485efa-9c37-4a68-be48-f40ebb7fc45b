import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

interface Props {
    text?: string;
    backgroundColor?: string;
    textColor?: string;
}

const Label: React.FC<Props> = ({ backgroundColor, textColor, text = '' }: Props) => {
    return (
        <Box
            sx={{
                borderRadius: '4px',
                backgroundColor,
            }}
            py={0.2}
            px={0.5}
        >
            <Typography
                variant="body1"
                color={textColor}
            >
                {text}
            </Typography>
        </Box>
    );
};

export default Label;
