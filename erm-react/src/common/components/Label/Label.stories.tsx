import { StoryFn, Meta } from '@storybook/react';
import React from 'react';
import Label from './Label';

export default {
    title: 'components/Forms/Input label',
    component: Label,
    parameters: {
        docs: {
            description: {
                component: 'Label component used as input label',
            },
        },
    },
} as Meta<typeof Label>;

const Template: StoryFn<typeof Label> = (args) => <Label {...args} />;

export const Default: StoryFn<typeof Label> = Template.bind({});
Default.args = {
    text: 'Lorem ipsum',
};

export const WithColoredText: StoryFn<typeof Label> = Template.bind({});
WithColoredText.args = {
    ...Default.args,
    textColor: 'red',
};

export const WithBackground: StoryFn<typeof Label> = Template.bind({});
WithBackground.args = {
    ...Default.args,
    backgroundColor: 'protechtGrey.grey_128',
    textColor: 'white',
};
