import React, { FC, useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { useMrsCreateUsingPostMutation, useLazyMrsFindByIdUsingGetQuery, useMrsUpdateUsingPutMutation } from 'metrics/rtkApi';
import { Condition, DisplayFunc, MetricFormValues, MetricRestResponse } from 'metrics/types';
import { useTmrsGetRegisterConfigUsingGetQuery } from 'register/rtkApi';
import { skipToken } from '@reduxjs/toolkit/query';
import { MetricRest, TableMetadataRest, ViewConditionConfig } from 'api/generated/types';
import useForm from 'common/hooks/forms/useForm';
import { MetricFormSchema } from 'metrics/metricValidationSchema';
import { getColorTypeFromMetricColors, isValidMetricId } from 'metrics/utils';
import { displayFuncOptions, EMPTY_CONDITION, widthOptions } from 'metrics/const';
import { MetricColorsType } from 'common/components/MetricsSettings/types';
import { getFieldFromRegisterByColumnName } from 'register/utils';
import { ColumnType, RegisterRest } from 'register/types';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { getListFieldItem } from 'register/components/RegisterField/ListRegisterField/utils';
import { getStylingSettings } from 'common/components/MetricsSettings/utils';
import { strings } from '../../utils/i18n';
import { useUnsavedChangesAlert } from 'common/hooks/useUnsavedChangesAlert';
import { DialogType } from '../../types';
import ApplicationLayout from '../../layouts/ApplicationLayout';
import ToolbarContainer from '../ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from '../ToolbarSpacing/ToolbarGroup';
import Typography from '@mui/material/Typography';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import MainLayout from '../../layouts/MainLayout';
import SettingsLayout, { SettingsSection } from '../../layouts/SettingsLayout';
import { FormProvider } from 'react-hook-form';
import MetricsSettings from 'common/components/MetricsSettings';
import useSnackbar from 'common/hooks/useSnackbar';
import { DEFAULT_METRIC_COLORS } from './const';

export interface MetricsDetailProps {
    feature: number;
    homePath: string;
    module: string; // FIXME: should be Module type
}

const MetricsDetail: FC<MetricsDetailProps> = ({ feature, homePath, module }) => {
    const { id: metricId } = useParams();
    const navigate = useNavigate();

    const [createMetric, { isSuccess: isCreateSuccess }] = useMrsCreateUsingPostMutation();
    const [updateMetric, { isSuccess: isUpdateSuccess }] = useMrsUpdateUsingPutMutation();

    const [trigger, { data: metricDetail }] = useLazyMrsFindByIdUsingGetQuery();
    const { data: sourceRegister } = useTmrsGetRegisterConfigUsingGetQuery(
        metricDetail?.metric?.context ? { tableName: metricDetail?.metric?.context } : skipToken,
    );

    const [selectedSourceRegister, setSelectedSourceRegister] = useState<TableMetadataRest | undefined>(undefined);

    const initDefaultValues = useCallback(async () => {
        if (isValidMetricId(metricId)) {
            const metricDetail = await trigger({ idOrName: metricId });
            const response = metricDetail.data as MetricRestResponse;
            const conditions = response.metric.configModel.internal[0]?.conditionConfig?.conditions as Condition[];
            const { size, ...colors } = response.metric.styleModel;
            const initialValues: MetricFormValues = {
                name: response.metric.name,
                context: response.metric.context,
                func: response.metric.func,
                width: size,
                conditions: conditions.length ? conditions.map((c) => ({ ...c, or: c.or?.toString() })) : [EMPTY_CONDITION],
                colors: {
                    ...colors,
                    scalesTxt: colors.scalesTxt ?? DEFAULT_METRIC_COLORS.scalesTxt,
                    scalesList: colors.scalesList ?? DEFAULT_METRIC_COLORS.scalesList,
                    scalesNum: colors.scalesNum ?? DEFAULT_METRIC_COLORS.scalesNum,
                    scalesOverdue: colors.scalesOverdue ?? DEFAULT_METRIC_COLORS.scalesOverdue,
                },
                metricColorsType: getColorTypeFromMetricColors(colors),
            };
            const isPercentageMetric = response.metric.func === DisplayFunc.PERCENT;
            const secondInternalConfig = response.metric.configModel.internal.at(1);
            if (isPercentageMetric && secondInternalConfig) {
                initialValues.sourceField = secondInternalConfig.conditionConfig.conditions[0].property;
                initialValues.operator = secondInternalConfig.conditionConfig.conditions[0].operator;
                initialValues.value = secondInternalConfig.conditionConfig.conditions[0].value;
            } else {
                initialValues.sourceField = response.metric.sourceField;
            }
            return initialValues;
        } else {
            // empty form values should be initialized to empty strings for correct form behavior, not undefined
            return {
                name: '',
                context: '',
                func: displayFuncOptions[0].value,
                width: widthOptions[0].value,
                conditions: [EMPTY_CONDITION],
                colors: DEFAULT_METRIC_COLORS,
                metricColorsType: MetricColorsType.COLOR,
            } as unknown as MetricFormValues;
        }
    }, [metricId]);

    const methods = useForm<MetricFormValues>({
        schema: MetricFormSchema,
        mode: 'onChange',
        defaultValues: initDefaultValues,
    });

    const {
        handleSubmit,
        formState: { isValid, isDirty, isSubmitted },
    } = methods;

    const { enqueueSuccess, enqueueError } = useSnackbar();

    const back = useCallback(() => {
        void navigate(homePath);
    }, [navigate]);

    const getConditionValue = useCallback(
        (columnName: string | undefined, value: string) => {
            const field =
                getFieldFromRegisterByColumnName(selectedSourceRegister! as RegisterRest, columnName!) ??
                (selectedSourceRegister?.statusColumn as SectionFieldMetaData);

            switch (field?.columnType) {
                case ColumnType.LIST:
                    return getListFieldItem(field, value)?.label || value;
                case ColumnType.REGISTER_STATE:
                    return selectedSourceRegister?.stateDefinition?.states?.find((s) => s.id === parseInt(value))?.name || value;
                default:
                    return value;
            }
        },
        [selectedSourceRegister],
    );

    const saveMetric = useCallback(
        async (formValues: MetricFormValues) => {
            const isPercentageMetric = formValues.func === DisplayFunc.PERCENT;

            const configModel: ViewConditionConfig = {
                internal: [
                    {
                        conditionConfig: {
                            conditions: formValues.conditions.map((c) => {
                                return {
                                    ...c,
                                    or: c.or === 'true',
                                    value: getConditionValue(c.property, c.value),
                                };
                            }),
                        },
                    },
                ],
            };
            if (isPercentageMetric) {
                // configModel.internal[1] has the same data model as conditions for metric evaluation
                // this part of model is used only when condition display function is used, sourceField, operator and value fields become part of the metric condition
                configModel.internal?.push({
                    conditionConfig: {
                        conditions: [
                            {
                                property: formValues.sourceField,
                                operator: formValues.operator,
                                or: false,
                                value: getConditionValue(formValues.sourceField, formValues.value!),
                            },
                        ],
                    },
                });
            }

            const requestBody: MetricRest = {
                configModel,
                context: formValues.context,
                name: formValues.name,
                func: formValues.func,
                styleModel: {
                    size: formValues.width,
                    ...getStylingSettings(formValues),
                },
                feature: feature,
                source: module,
            };

            if (!isPercentageMetric) {
                // when other than percentage function is used, source field is used as a simple value, no second internal conditions are used
                requestBody.sourceField = formValues.sourceField;
            }

            if (isValidMetricId(metricId)) {
                const id = parseInt(metricId!);
                const updateRequestBody = { ...requestBody, id };
                await updateMetric({ metricId: id, metricRest: updateRequestBody }).then((result: any) => {
                    if ('error' in result) {
                        const errorMessage = result.error?.data?.message || result.error?.message || strings('metrics:message.updateError');
                        enqueueError(errorMessage);
                    } else {
                        enqueueSuccess(strings('metrics:message.updateSuccess'));
                    }
                });
            } else {
                await createMetric({ metricRest: requestBody }).then((result: any) => {
                    if ('error' in result) {
                        const errorMessage = result.error?.data?.message || result.error?.message || strings('metrics:message.createError');
                        enqueueError(errorMessage);
                    } else {
                        enqueueSuccess(strings('metrics:message.createSuccess'));
                    }
                });
            }
        },
        [metricId, getConditionValue, enqueueError, enqueueSuccess, createMetric, updateMetric],
    );

    useUnsavedChangesAlert({
        blockNavigation: isDirty && !isSubmitted,
        onConfirm: handleSubmit(saveMetric),
        dialogType: !isValid ? DialogType.DISCARD : DialogType.UNSAVED,
    });

    useEffect(() => {
        if ((isCreateSuccess || isUpdateSuccess) && isSubmitted) {
            void navigate(homePath);
        }
    }, [isCreateSuccess, isUpdateSuccess, navigate, isSubmitted]);

    useEffect(() => {
        if (sourceRegister) {
            setSelectedSourceRegister(sourceRegister);
        }
    }, [sourceRegister]);

    return (
        <>
            <ApplicationLayout>
                <ToolbarContainer
                    disableGutters={false}
                    variant="regular"
                >
                    <ToolbarGroup
                        flex={1}
                        justifyContent="space-between"
                    >
                        <ToolbarGroup>
                            <Typography
                                variant="h1"
                                data-testid={'metric-settings-heading'}
                            >
                                {strings('metrics:title.metricsSettings')}
                            </Typography>
                        </ToolbarGroup>
                        <ToolbarGroup>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                variant="outlined"
                                onClick={back}
                            >
                                {!isDirty ? strings('ermConstants:close') : strings('common:button.cancel')}
                            </Button>
                            <Button
                                {...ButtonStyles.pageToolbarButton}
                                disabled={!isValid}
                                onClick={handleSubmit(saveMetric)}
                            >
                                {strings('common:button.save')}
                            </Button>
                        </ToolbarGroup>
                    </ToolbarGroup>
                </ToolbarContainer>
                <MainLayout>
                    <SettingsLayout>
                        <SettingsSection width="90%">
                            <FormProvider {...methods}>
                                <MetricsSettings
                                    module={feature}
                                    sourceFieldColumnName={metricDetail?.metric?.sourceField}
                                    selectedSourceRegister={selectedSourceRegister}
                                    setSelectedSourceRegister={setSelectedSourceRegister}
                                ></MetricsSettings>
                            </FormProvider>
                        </SettingsSection>
                    </SettingsLayout>
                </MainLayout>
            </ApplicationLayout>
        </>
    );
};

export default MetricsDetail;
