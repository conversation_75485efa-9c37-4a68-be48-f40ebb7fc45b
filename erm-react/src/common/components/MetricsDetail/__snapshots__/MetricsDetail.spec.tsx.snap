// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MetricsDetail renders the metric details create form 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metric-settings-heading"
          >
            Metrics Settings
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge <PERSON>utton-outlinedSizeLarge Mui<PERSON>utton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Close"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Close
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Save"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-fs5pge"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-1dcp0mw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="name"
                          >
                            Metric label
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-metric-name"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Metric label"
                              class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              id="input-metric-name"
                              name="name"
                              placeholder="enter a name / label for this metric"
                              tabindex="0"
                              type="text"
                              value=""
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="context"
                          >
                            Metric data source
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-aemixd-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-data-source"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl Mui-readOnly MuiInputBase-readOnly css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="Metric data source"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-readOnly MuiInputBase-readOnly css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                            id="input-data-source"
                            name="context"
                            placeholder="select the register the metric will be based on"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value="VRM - Vendor Register"
                          />
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
                >
                  Limit the metric to Register Entries that meet the following criteria
                </p>
                <div
                  class="MuiGrid-root MuiGrid-container css-1vcxjca-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
                  >
                    <ul
                      class="css-cuxljv"
                    >
                      <li
                        class="css-j6sz79"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-container css-1g8wflw-MuiGrid-root"
                        >
                          <div
                            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                          >
                            <button
                              aria-describedby="DndDescribedBy-0"
                              aria-disabled="false"
                              aria-roledescription="sortable"
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                              role="button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-bars "
                                data-icon="bars"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                          <div
                            class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-10zncv6-MuiGrid-root"
                          >
                            <div
                              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                            >
                              <div
                                class="MuiFormControl-root MuiTextField-root css-1m36lh0-MuiFormControl-root-MuiTextField-root"
                                data-testid="input-logical-operator"
                              >
                                <div
                                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                                >
                                  <input
                                    aria-invalid="false"
                                    class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                                    disabled=""
                                    id="input-logical-operator"
                                    tabindex="-1"
                                    type="text"
                                    value="If"
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                  >
                                    <legend
                                      class="css-13wgbfv"
                                    >
                                      <span
                                        class="notranslate"
                                      >
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round "
                                      color="#C0C0C0"
                                      data-icon="bracket-round"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M154.6 46.2c9.8 14.7 5.8 34.6-8.9 44.4C120.1 107.7 64 167 64 256s56.1 148.3 81.8 165.4c14.7 9.8 18.7 29.7 8.9 44.4s-29.7 18.7-44.4 8.9C71.9 449.1 0 371.8 0 256S71.9 62.9 110.2 37.4c14.7-9.8 34.6-5.8 44.4 8.9z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-ozdp81-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiBox-root css-i5q2k0"
                                  >
                                    <div
                                      class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                                      data-testid="input-register-field"
                                    >
                                      <div
                                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                                      >
                                        <input
                                          aria-invalid="false"
                                          aria-label="conditions.0.property"
                                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                                          id="input-register-field"
                                          name="conditions.0.property"
                                          placeholder="select a field"
                                          readonly=""
                                          tabindex="0"
                                          type="text"
                                          value=""
                                        />
                                        <div
                                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                                        >
                                          <div
                                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                            data-testid="openDialog"
                                          >
                                            <svg
                                              aria-hidden="true"
                                              class="svg-inline--fa fa-ellipsis fa-xl "
                                              color="#1B4AD5"
                                              data-icon="ellipsis"
                                              data-prefix="far"
                                              focusable="false"
                                              role="img"
                                              viewBox="0 0 448 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                                fill="currentColor"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                        <fieldset
                                          aria-hidden="true"
                                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                        >
                                          <legend
                                            class="css-13wgbfv"
                                          >
                                            <span
                                              class="notranslate"
                                            >
                                              ​
                                            </span>
                                          </legend>
                                        </fieldset>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1c97dam-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                                    data-testid="conditions.0.operator"
                                  >
                                    <div
                                      aria-controls=":r7:"
                                      aria-disabled="true"
                                      aria-expanded="false"
                                      aria-haspopup="listbox"
                                      aria-label="Operator"
                                      aria-labelledby="mui-component-select-conditions.0.operator"
                                      class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                                      id="mui-component-select-conditions.0.operator"
                                      role="combobox"
                                      tabindex="-1"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                                      >
                                        Operator
                                      </p>
                                    </div>
                                    <input
                                      aria-hidden="true"
                                      aria-invalid="false"
                                      class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                                      disabled=""
                                      name="conditions.0.operator"
                                      tabindex="-1"
                                      value=""
                                    />
                                    <svg
                                      aria-hidden="true"
                                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                                      data-testid="KeyboardArrowDownIcon"
                                      focusable="false"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                      />
                                    </svg>
                                    <fieldset
                                      aria-hidden="true"
                                      class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                    >
                                      <legend
                                        class="css-13wgbfv"
                                      >
                                        <span
                                          class="notranslate"
                                        >
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round-right "
                                      color="#C0C0C0"
                                      data-icon="bracket-round-right"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M37.4 46.2C27.6 61 31.5 80.8 46.3 90.6C71.9 107.7 128 167 128 256s-56.1 148.3-81.8 165.4c-14.7 9.8-18.7 29.7-8.9 44.4s29.7 18.7 44.4 8.9C120.1 449 192 371.8 192 256S120.1 62.9 81.8 37.4C67 27.6 47.2 31.5 37.4 46.2z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root css-17i9zmt-MuiGrid-root"
                            />
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div
                      id="DndDescribedBy-0"
                      style="display: none;"
                    >
                      
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                    </div>
                    <div
                      aria-atomic="true"
                      aria-live="assertive"
                      id="DndLiveRegion-0"
                      role="status"
                      style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                    />
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-19t1w01"
                    >
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jx8yas-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Add criteria"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Add criteria
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="func"
                          >
                            Using these Register entries, the Metric will display
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                      data-testid="func"
                    >
                      <div
                        aria-controls=":r3:"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-label="Using these Register entries, the Metric will display"
                        aria-labelledby="mui-component-select-func"
                        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                        id="mui-component-select-func"
                        role="combobox"
                        tabindex="0"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                        />
                      </div>
                      <input
                        aria-hidden="true"
                        aria-invalid="false"
                        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                        name="func"
                        tabindex="-1"
                        value=""
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                        data-testid="KeyboardArrowDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                        />
                      </svg>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-13wgbfv"
                        >
                          <span
                            class="notranslate"
                          >
                            ​
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-43yvyf"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="width"
                          >
                            Width of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                      role="radiogroup"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="0"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Standard
                          </span>
                        </span>
                      </label>
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="1"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Wide
                          </span>
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="colors"
                          >
                            Color of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiBox-root css-69028f"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 css-1ht4vxp-MuiTypography-root"
                        >
                          Single color
                        </p>
                        <div
                          class="MuiGrid-root MuiGrid-container css-1cwdt9v-MuiGrid-root"
                        >
                          <div
                            class="MuiBox-root css-dk1n60"
                          />
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MetricsDetail renders the metric details form for option: A count of register entries that meet the criteria 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metric-settings-heading"
          >
            Metrics Settings
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Close"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Close
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Save"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-fs5pge"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-1dcp0mw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="name"
                          >
                            Metric label
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-metric-name"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Metric label"
                              class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              id="input-metric-name"
                              name="name"
                              placeholder="enter a name / label for this metric"
                              tabindex="0"
                              type="text"
                              value=""
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="context"
                          >
                            Metric data source
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-aemixd-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-data-source"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl Mui-readOnly MuiInputBase-readOnly css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="Metric data source"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-readOnly MuiInputBase-readOnly css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                            id="input-data-source"
                            name="context"
                            placeholder="select the register the metric will be based on"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value="VRM - Vendor Register"
                          />
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
                >
                  Limit the metric to Register Entries that meet the following criteria
                </p>
                <div
                  class="MuiGrid-root MuiGrid-container css-1vcxjca-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
                  >
                    <ul
                      class="css-cuxljv"
                    >
                      <li
                        class="css-j6sz79"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-container css-1g8wflw-MuiGrid-root"
                        >
                          <div
                            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                          >
                            <button
                              aria-describedby="DndDescribedBy-1"
                              aria-disabled="false"
                              aria-roledescription="sortable"
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                              role="button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-bars "
                                data-icon="bars"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                          <div
                            class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-10zncv6-MuiGrid-root"
                          >
                            <div
                              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                            >
                              <div
                                class="MuiFormControl-root MuiTextField-root css-1m36lh0-MuiFormControl-root-MuiTextField-root"
                                data-testid="input-logical-operator"
                              >
                                <div
                                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                                >
                                  <input
                                    aria-invalid="false"
                                    class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                                    disabled=""
                                    id="input-logical-operator"
                                    tabindex="-1"
                                    type="text"
                                    value="If"
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                  >
                                    <legend
                                      class="css-13wgbfv"
                                    >
                                      <span
                                        class="notranslate"
                                      >
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round "
                                      color="#C0C0C0"
                                      data-icon="bracket-round"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M154.6 46.2c9.8 14.7 5.8 34.6-8.9 44.4C120.1 107.7 64 167 64 256s56.1 148.3 81.8 165.4c14.7 9.8 18.7 29.7 8.9 44.4s-29.7 18.7-44.4 8.9C71.9 449.1 0 371.8 0 256S71.9 62.9 110.2 37.4c14.7-9.8 34.6-5.8 44.4 8.9z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-ozdp81-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiBox-root css-i5q2k0"
                                  >
                                    <div
                                      class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                                      data-testid="input-register-field"
                                    >
                                      <div
                                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                                      >
                                        <input
                                          aria-invalid="false"
                                          aria-label="conditions.0.property"
                                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                                          id="input-register-field"
                                          name="conditions.0.property"
                                          placeholder="select a field"
                                          readonly=""
                                          tabindex="0"
                                          type="text"
                                          value=""
                                        />
                                        <div
                                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                                        >
                                          <div
                                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                            data-testid="openDialog"
                                          >
                                            <svg
                                              aria-hidden="true"
                                              class="svg-inline--fa fa-ellipsis fa-xl "
                                              color="#1B4AD5"
                                              data-icon="ellipsis"
                                              data-prefix="far"
                                              focusable="false"
                                              role="img"
                                              viewBox="0 0 448 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                                fill="currentColor"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                        <fieldset
                                          aria-hidden="true"
                                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                        >
                                          <legend
                                            class="css-13wgbfv"
                                          >
                                            <span
                                              class="notranslate"
                                            >
                                              ​
                                            </span>
                                          </legend>
                                        </fieldset>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1c97dam-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                                    data-testid="conditions.0.operator"
                                  >
                                    <div
                                      aria-controls=":rf:"
                                      aria-disabled="true"
                                      aria-expanded="false"
                                      aria-haspopup="listbox"
                                      aria-label="Operator"
                                      aria-labelledby="mui-component-select-conditions.0.operator"
                                      class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                                      id="mui-component-select-conditions.0.operator"
                                      role="combobox"
                                      tabindex="-1"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                                      >
                                        Operator
                                      </p>
                                    </div>
                                    <input
                                      aria-hidden="true"
                                      aria-invalid="false"
                                      class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                                      disabled=""
                                      name="conditions.0.operator"
                                      tabindex="-1"
                                      value=""
                                    />
                                    <svg
                                      aria-hidden="true"
                                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                                      data-testid="KeyboardArrowDownIcon"
                                      focusable="false"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                      />
                                    </svg>
                                    <fieldset
                                      aria-hidden="true"
                                      class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                    >
                                      <legend
                                        class="css-13wgbfv"
                                      >
                                        <span
                                          class="notranslate"
                                        >
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round-right "
                                      color="#C0C0C0"
                                      data-icon="bracket-round-right"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M37.4 46.2C27.6 61 31.5 80.8 46.3 90.6C71.9 107.7 128 167 128 256s-56.1 148.3-81.8 165.4c-14.7 9.8-18.7 29.7-8.9 44.4s29.7 18.7 44.4 8.9C120.1 449 192 371.8 192 256S120.1 62.9 81.8 37.4C67 27.6 47.2 31.5 37.4 46.2z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root css-17i9zmt-MuiGrid-root"
                            />
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div
                      id="DndDescribedBy-1"
                      style="display: none;"
                    >
                      
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                    </div>
                    <div
                      aria-atomic="true"
                      aria-live="assertive"
                      id="DndLiveRegion-1"
                      role="status"
                      style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                    />
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-19t1w01"
                    >
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jx8yas-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Add criteria"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Add criteria
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="func"
                          >
                            Using these Register entries, the Metric will display
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                      data-testid="func"
                    >
                      <div
                        aria-controls=":rb:"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-label="Using these Register entries, the Metric will display"
                        aria-labelledby="mui-component-select-func"
                        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                        id="mui-component-select-func"
                        role="combobox"
                        tabindex="0"
                      >
                        <div
                          class="MuiBox-root css-9ou7bg"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-1ldt5e-MuiTypography-root"
                          >
                            A count of register entries that meet the criteria
                          </p>
                        </div>
                      </div>
                      <input
                        aria-hidden="true"
                        aria-invalid="false"
                        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                        name="func"
                        tabindex="-1"
                        value="0"
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                        data-testid="KeyboardArrowDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                        />
                      </svg>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-13wgbfv"
                        >
                          <span
                            class="notranslate"
                          >
                            ​
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-43yvyf"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="width"
                          >
                            Width of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                      role="radiogroup"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall Mui-checked MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="0"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-z3zsu8-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Standard
                          </span>
                        </span>
                      </label>
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="1"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Wide
                          </span>
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="colors"
                          >
                            Color of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiBox-root css-69028f"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 css-1ht4vxp-MuiTypography-root"
                        >
                          Single color
                        </p>
                        <div
                          class="MuiGrid-root MuiGrid-container css-1cwdt9v-MuiGrid-root"
                        >
                          <div
                            class="MuiBox-root css-dk1n60"
                          />
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MetricsDetail renders the metric details form for option: A field value 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metric-settings-heading"
          >
            Metrics Settings
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Cancel"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Cancel
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Save"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-fs5pge"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-1dcp0mw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="name"
                          >
                            Metric label
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-metric-name"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Metric label"
                              class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              id="input-metric-name"
                              name="name"
                              placeholder="enter a name / label for this metric"
                              tabindex="0"
                              type="text"
                              value=""
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="context"
                          >
                            Metric data source
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-aemixd-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-data-source"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl Mui-readOnly MuiInputBase-readOnly css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="Metric data source"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-readOnly MuiInputBase-readOnly css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                            id="input-data-source"
                            name="context"
                            placeholder="select the register the metric will be based on"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value="VRM - Vendor Register"
                          />
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
                >
                  Limit the metric to Register Entries that meet the following criteria
                </p>
                <div
                  class="MuiGrid-root MuiGrid-container css-1vcxjca-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
                  >
                    <ul
                      class="css-cuxljv"
                    >
                      <li
                        class="css-j6sz79"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-container css-1g8wflw-MuiGrid-root"
                        >
                          <div
                            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                          >
                            <button
                              aria-describedby="DndDescribedBy-2"
                              aria-disabled="false"
                              aria-roledescription="sortable"
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                              role="button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-bars "
                                data-icon="bars"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                          <div
                            class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-10zncv6-MuiGrid-root"
                          >
                            <div
                              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                            >
                              <div
                                class="MuiFormControl-root MuiTextField-root css-1m36lh0-MuiFormControl-root-MuiTextField-root"
                                data-testid="input-logical-operator"
                              >
                                <div
                                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                                >
                                  <input
                                    aria-invalid="false"
                                    class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                                    disabled=""
                                    id="input-logical-operator"
                                    tabindex="-1"
                                    type="text"
                                    value="If"
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                  >
                                    <legend
                                      class="css-13wgbfv"
                                    >
                                      <span
                                        class="notranslate"
                                      >
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round "
                                      color="#C0C0C0"
                                      data-icon="bracket-round"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M154.6 46.2c9.8 14.7 5.8 34.6-8.9 44.4C120.1 107.7 64 167 64 256s56.1 148.3 81.8 165.4c14.7 9.8 18.7 29.7 8.9 44.4s-29.7 18.7-44.4 8.9C71.9 449.1 0 371.8 0 256S71.9 62.9 110.2 37.4c14.7-9.8 34.6-5.8 44.4 8.9z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-ozdp81-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiBox-root css-i5q2k0"
                                  >
                                    <div
                                      class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                                      data-testid="input-register-field"
                                    >
                                      <div
                                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                                      >
                                        <input
                                          aria-invalid="false"
                                          aria-label="conditions.0.property"
                                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                                          id="input-register-field"
                                          name="conditions.0.property"
                                          placeholder="select a field"
                                          readonly=""
                                          tabindex="0"
                                          type="text"
                                          value=""
                                        />
                                        <div
                                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                                        >
                                          <div
                                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                            data-testid="openDialog"
                                          >
                                            <svg
                                              aria-hidden="true"
                                              class="svg-inline--fa fa-ellipsis fa-xl "
                                              color="#1B4AD5"
                                              data-icon="ellipsis"
                                              data-prefix="far"
                                              focusable="false"
                                              role="img"
                                              viewBox="0 0 448 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                                fill="currentColor"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                        <fieldset
                                          aria-hidden="true"
                                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                        >
                                          <legend
                                            class="css-13wgbfv"
                                          >
                                            <span
                                              class="notranslate"
                                            >
                                              ​
                                            </span>
                                          </legend>
                                        </fieldset>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1c97dam-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                                    data-testid="conditions.0.operator"
                                  >
                                    <div
                                      aria-controls=":rs:"
                                      aria-disabled="true"
                                      aria-expanded="false"
                                      aria-haspopup="listbox"
                                      aria-label="Operator"
                                      aria-labelledby="mui-component-select-conditions.0.operator"
                                      class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                                      id="mui-component-select-conditions.0.operator"
                                      role="combobox"
                                      tabindex="-1"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                                      >
                                        Operator
                                      </p>
                                    </div>
                                    <input
                                      aria-hidden="true"
                                      aria-invalid="false"
                                      class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                                      disabled=""
                                      name="conditions.0.operator"
                                      tabindex="-1"
                                      value=""
                                    />
                                    <svg
                                      aria-hidden="true"
                                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                                      data-testid="KeyboardArrowDownIcon"
                                      focusable="false"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                      />
                                    </svg>
                                    <fieldset
                                      aria-hidden="true"
                                      class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                    >
                                      <legend
                                        class="css-13wgbfv"
                                      >
                                        <span
                                          class="notranslate"
                                        >
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round-right "
                                      color="#C0C0C0"
                                      data-icon="bracket-round-right"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M37.4 46.2C27.6 61 31.5 80.8 46.3 90.6C71.9 107.7 128 167 128 256s-56.1 148.3-81.8 165.4c-14.7 9.8-18.7 29.7-8.9 44.4s29.7 18.7 44.4 8.9C120.1 449 192 371.8 192 256S120.1 62.9 81.8 37.4C67 27.6 47.2 31.5 37.4 46.2z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root css-17i9zmt-MuiGrid-root"
                            />
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div
                      id="DndDescribedBy-2"
                      style="display: none;"
                    >
                      
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                    </div>
                    <div
                      aria-atomic="true"
                      aria-live="assertive"
                      id="DndLiveRegion-2"
                      role="status"
                      style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                    />
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-19t1w01"
                    >
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jx8yas-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Add criteria"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Add criteria
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="func"
                          >
                            Using these Register entries, the Metric will display
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                      data-testid="func"
                    >
                      <div
                        aria-controls=":ro:"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-label="Using these Register entries, the Metric will display"
                        aria-labelledby="mui-component-select-func"
                        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                        id="mui-component-select-func"
                        role="combobox"
                        tabindex="0"
                      >
                        <div
                          class="MuiBox-root css-9ou7bg"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-1ldt5e-MuiTypography-root"
                          >
                            A field value
                          </p>
                        </div>
                      </div>
                      <input
                        aria-hidden="true"
                        aria-invalid="false"
                        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                        name="func"
                        tabindex="-1"
                        value="1"
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                        data-testid="KeyboardArrowDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                        />
                      </svg>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-13wgbfv"
                        >
                          <span
                            class="notranslate"
                          >
                            ​
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="sourceField"
                          >
                            From this Register field
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-source-field"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="From this Register field"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                            disabled=""
                            id="input-source-field"
                            name="sourceField"
                            placeholder="select a field"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value=""
                          />
                          <div
                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                          >
                            <div
                              class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                              data-testid="openDialog"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-ellipsis fa-xl "
                                color="#1B4AD5"
                                data-icon="ellipsis"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                  fill="currentColor"
                                />
                              </svg>
                            </div>
                          </div>
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="note"
                          >
                            Note
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Note"
                              class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              disabled=""
                              id=":r13:"
                              name="note"
                              placeholder=""
                              tabindex="-1"
                              type="text"
                              value="Metric will display ”–” if the source field is empty or not set, or if more than one Register Entry meets the criteria"
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-43yvyf"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="width"
                          >
                            Width of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                      role="radiogroup"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall Mui-checked MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="0"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-z3zsu8-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Standard
                          </span>
                        </span>
                      </label>
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="1"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Wide
                          </span>
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="colors"
                          >
                            Color of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiBox-root css-69028f"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 css-1ht4vxp-MuiTypography-root"
                        >
                          Single color
                        </p>
                        <div
                          class="MuiGrid-root MuiGrid-container css-1cwdt9v-MuiGrid-root"
                        >
                          <div
                            class="MuiBox-root css-dk1n60"
                          />
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MetricsDetail renders the metric details form for option: A percentage 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metric-settings-heading"
          >
            Metrics Settings
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Cancel"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Cancel
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Save"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-fs5pge"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-1dcp0mw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="name"
                          >
                            Metric label
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-metric-name"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Metric label"
                              class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              id="input-metric-name"
                              name="name"
                              placeholder="enter a name / label for this metric"
                              tabindex="0"
                              type="text"
                              value=""
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="context"
                          >
                            Metric data source
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-aemixd-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-data-source"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl Mui-readOnly MuiInputBase-readOnly css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="Metric data source"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-readOnly MuiInputBase-readOnly css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                            id="input-data-source"
                            name="context"
                            placeholder="select the register the metric will be based on"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value="VRM - Vendor Register"
                          />
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
                >
                  Limit the metric to Register Entries that meet the following criteria
                </p>
                <div
                  class="MuiGrid-root MuiGrid-container css-1vcxjca-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
                  >
                    <ul
                      class="css-cuxljv"
                    >
                      <li
                        class="css-j6sz79"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-container css-1g8wflw-MuiGrid-root"
                        >
                          <div
                            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                          >
                            <button
                              aria-describedby="DndDescribedBy-5"
                              aria-disabled="false"
                              aria-roledescription="sortable"
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                              role="button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-bars "
                                data-icon="bars"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                          <div
                            class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-10zncv6-MuiGrid-root"
                          >
                            <div
                              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                            >
                              <div
                                class="MuiFormControl-root MuiTextField-root css-1m36lh0-MuiFormControl-root-MuiTextField-root"
                                data-testid="input-logical-operator"
                              >
                                <div
                                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                                >
                                  <input
                                    aria-invalid="false"
                                    class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                                    disabled=""
                                    id="input-logical-operator"
                                    tabindex="-1"
                                    type="text"
                                    value="If"
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                  >
                                    <legend
                                      class="css-13wgbfv"
                                    >
                                      <span
                                        class="notranslate"
                                      >
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round "
                                      color="#C0C0C0"
                                      data-icon="bracket-round"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M154.6 46.2c9.8 14.7 5.8 34.6-8.9 44.4C120.1 107.7 64 167 64 256s56.1 148.3 81.8 165.4c14.7 9.8 18.7 29.7 8.9 44.4s-29.7 18.7-44.4 8.9C71.9 449.1 0 371.8 0 256S71.9 62.9 110.2 37.4c14.7-9.8 34.6-5.8 44.4 8.9z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-ozdp81-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiBox-root css-i5q2k0"
                                  >
                                    <div
                                      class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                                      data-testid="input-register-field"
                                    >
                                      <div
                                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                                      >
                                        <input
                                          aria-invalid="false"
                                          aria-label="conditions.0.property"
                                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                                          id="input-register-field"
                                          name="conditions.0.property"
                                          placeholder="select a field"
                                          readonly=""
                                          tabindex="0"
                                          type="text"
                                          value=""
                                        />
                                        <div
                                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                                        >
                                          <div
                                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                            data-testid="openDialog"
                                          >
                                            <svg
                                              aria-hidden="true"
                                              class="svg-inline--fa fa-ellipsis fa-xl "
                                              color="#1B4AD5"
                                              data-icon="ellipsis"
                                              data-prefix="far"
                                              focusable="false"
                                              role="img"
                                              viewBox="0 0 448 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                                fill="currentColor"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                        <fieldset
                                          aria-hidden="true"
                                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                        >
                                          <legend
                                            class="css-13wgbfv"
                                          >
                                            <span
                                              class="notranslate"
                                            >
                                              ​
                                            </span>
                                          </legend>
                                        </fieldset>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1c97dam-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                                    data-testid="conditions.0.operator"
                                  >
                                    <div
                                      aria-controls=":r29:"
                                      aria-disabled="true"
                                      aria-expanded="false"
                                      aria-haspopup="listbox"
                                      aria-label="Operator"
                                      aria-labelledby="mui-component-select-conditions.0.operator"
                                      class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                                      id="mui-component-select-conditions.0.operator"
                                      role="combobox"
                                      tabindex="-1"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                                      >
                                        Operator
                                      </p>
                                    </div>
                                    <input
                                      aria-hidden="true"
                                      aria-invalid="false"
                                      class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                                      disabled=""
                                      name="conditions.0.operator"
                                      tabindex="-1"
                                      value=""
                                    />
                                    <svg
                                      aria-hidden="true"
                                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                                      data-testid="KeyboardArrowDownIcon"
                                      focusable="false"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                      />
                                    </svg>
                                    <fieldset
                                      aria-hidden="true"
                                      class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                    >
                                      <legend
                                        class="css-13wgbfv"
                                      >
                                        <span
                                          class="notranslate"
                                        >
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round-right "
                                      color="#C0C0C0"
                                      data-icon="bracket-round-right"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M37.4 46.2C27.6 61 31.5 80.8 46.3 90.6C71.9 107.7 128 167 128 256s-56.1 148.3-81.8 165.4c-14.7 9.8-18.7 29.7-8.9 44.4s29.7 18.7 44.4 8.9C120.1 449 192 371.8 192 256S120.1 62.9 81.8 37.4C67 27.6 47.2 31.5 37.4 46.2z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root css-17i9zmt-MuiGrid-root"
                            />
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div
                      id="DndDescribedBy-5"
                      style="display: none;"
                    >
                      
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                    </div>
                    <div
                      aria-atomic="true"
                      aria-live="assertive"
                      id="DndLiveRegion-5"
                      role="status"
                      style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                    />
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-19t1w01"
                    >
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jx8yas-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Add criteria"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Add criteria
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="func"
                          >
                            Using these Register entries, the Metric will display
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                      data-testid="func"
                    >
                      <div
                        aria-controls=":r25:"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-label="Using these Register entries, the Metric will display"
                        aria-labelledby="mui-component-select-func"
                        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                        id="mui-component-select-func"
                        role="combobox"
                        tabindex="0"
                      >
                        <div
                          class="MuiBox-root css-9ou7bg"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-1ldt5e-MuiTypography-root"
                          >
                            A percentage
                          </p>
                        </div>
                      </div>
                      <input
                        aria-hidden="true"
                        aria-invalid="false"
                        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                        name="func"
                        tabindex="-1"
                        value="4"
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                        data-testid="KeyboardArrowDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                        />
                      </svg>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-13wgbfv"
                        >
                          <span
                            class="notranslate"
                          >
                            ​
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 MuiGrid-grid-xs-12 MuiGrid-grid-sm-10 css-1pxnm2a-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
                >
                  <div
                    class="MuiBox-root css-iwu3eg"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                          >
                            <label
                              for="sourceField"
                            >
                              Of register Entries where
                            </label>
                          </p>
                        </div>
                      </div>
                      <div
                        class="MuiBox-root css-i5q2k0"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-source-field"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Of register Entries where"
                              class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                              disabled=""
                              id="input-source-field"
                              name="sourceField"
                              placeholder="select a field"
                              readonly=""
                              tabindex="-1"
                              type="text"
                              value=""
                            />
                            <div
                              class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                            >
                              <div
                                class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                data-testid="openDialog"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="svg-inline--fa fa-ellipsis fa-xl "
                                  color="#1B4AD5"
                                  data-icon="ellipsis"
                                  data-prefix="far"
                                  focusable="false"
                                  role="img"
                                  viewBox="0 0 448 512"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                    fill="currentColor"
                                  />
                                </svg>
                              </div>
                            </div>
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-1nur1m3-MuiGrid-root"
                >
                  <div
                    class="MuiBox-root css-iwu3eg"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                          >
                            <label
                              for="operator"
                            >
                               
                            </label>
                          </p>
                        </div>
                      </div>
                      <div
                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                        data-testid="operator"
                      >
                        <div
                          aria-controls=":r2g:"
                          aria-disabled="true"
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-label="Operator"
                          aria-labelledby="mui-component-select-operator"
                          class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                          id="mui-component-select-operator"
                          role="combobox"
                          tabindex="-1"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                          >
                            Operator
                          </p>
                        </div>
                        <input
                          aria-hidden="true"
                          aria-invalid="false"
                          class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                          disabled=""
                          name="operator"
                          tabindex="-1"
                          value=""
                        />
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                          data-testid="KeyboardArrowDownIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                          />
                        </svg>
                        <fieldset
                          aria-hidden="true"
                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                        >
                          <legend
                            class="css-13wgbfv"
                          >
                            <span
                              class="notranslate"
                            >
                              ​
                            </span>
                          </legend>
                        </fieldset>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-43yvyf"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="width"
                          >
                            Width of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                      role="radiogroup"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall Mui-checked MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="0"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-z3zsu8-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Standard
                          </span>
                        </span>
                      </label>
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="1"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Wide
                          </span>
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="colors"
                          >
                            Color of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiBox-root css-69028f"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 css-1ht4vxp-MuiTypography-root"
                        >
                          Single color
                        </p>
                        <div
                          class="MuiGrid-root MuiGrid-container css-1cwdt9v-MuiGrid-root"
                        >
                          <div
                            class="MuiBox-root css-dk1n60"
                          />
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MetricsDetail renders the metric details form for option: A sum of field values 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metric-settings-heading"
          >
            Metrics Settings
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Cancel"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Cancel
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Save"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-fs5pge"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-1dcp0mw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="name"
                          >
                            Metric label
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-metric-name"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Metric label"
                              class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              id="input-metric-name"
                              name="name"
                              placeholder="enter a name / label for this metric"
                              tabindex="0"
                              type="text"
                              value=""
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="context"
                          >
                            Metric data source
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-aemixd-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-data-source"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl Mui-readOnly MuiInputBase-readOnly css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="Metric data source"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-readOnly MuiInputBase-readOnly css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                            id="input-data-source"
                            name="context"
                            placeholder="select the register the metric will be based on"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value="VRM - Vendor Register"
                          />
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
                >
                  Limit the metric to Register Entries that meet the following criteria
                </p>
                <div
                  class="MuiGrid-root MuiGrid-container css-1vcxjca-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
                  >
                    <ul
                      class="css-cuxljv"
                    >
                      <li
                        class="css-j6sz79"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-container css-1g8wflw-MuiGrid-root"
                        >
                          <div
                            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                          >
                            <button
                              aria-describedby="DndDescribedBy-4"
                              aria-disabled="false"
                              aria-roledescription="sortable"
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                              role="button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-bars "
                                data-icon="bars"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                          <div
                            class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-10zncv6-MuiGrid-root"
                          >
                            <div
                              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                            >
                              <div
                                class="MuiFormControl-root MuiTextField-root css-1m36lh0-MuiFormControl-root-MuiTextField-root"
                                data-testid="input-logical-operator"
                              >
                                <div
                                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                                >
                                  <input
                                    aria-invalid="false"
                                    class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                                    disabled=""
                                    id="input-logical-operator"
                                    tabindex="-1"
                                    type="text"
                                    value="If"
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                  >
                                    <legend
                                      class="css-13wgbfv"
                                    >
                                      <span
                                        class="notranslate"
                                      >
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round "
                                      color="#C0C0C0"
                                      data-icon="bracket-round"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M154.6 46.2c9.8 14.7 5.8 34.6-8.9 44.4C120.1 107.7 64 167 64 256s56.1 148.3 81.8 165.4c14.7 9.8 18.7 29.7 8.9 44.4s-29.7 18.7-44.4 8.9C71.9 449.1 0 371.8 0 256S71.9 62.9 110.2 37.4c14.7-9.8 34.6-5.8 44.4 8.9z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-ozdp81-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiBox-root css-i5q2k0"
                                  >
                                    <div
                                      class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                                      data-testid="input-register-field"
                                    >
                                      <div
                                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                                      >
                                        <input
                                          aria-invalid="false"
                                          aria-label="conditions.0.property"
                                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                                          id="input-register-field"
                                          name="conditions.0.property"
                                          placeholder="select a field"
                                          readonly=""
                                          tabindex="0"
                                          type="text"
                                          value=""
                                        />
                                        <div
                                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                                        >
                                          <div
                                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                            data-testid="openDialog"
                                          >
                                            <svg
                                              aria-hidden="true"
                                              class="svg-inline--fa fa-ellipsis fa-xl "
                                              color="#1B4AD5"
                                              data-icon="ellipsis"
                                              data-prefix="far"
                                              focusable="false"
                                              role="img"
                                              viewBox="0 0 448 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                                fill="currentColor"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                        <fieldset
                                          aria-hidden="true"
                                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                        >
                                          <legend
                                            class="css-13wgbfv"
                                          >
                                            <span
                                              class="notranslate"
                                            >
                                              ​
                                            </span>
                                          </legend>
                                        </fieldset>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1c97dam-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                                    data-testid="conditions.0.operator"
                                  >
                                    <div
                                      aria-controls=":r1q:"
                                      aria-disabled="true"
                                      aria-expanded="false"
                                      aria-haspopup="listbox"
                                      aria-label="Operator"
                                      aria-labelledby="mui-component-select-conditions.0.operator"
                                      class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                                      id="mui-component-select-conditions.0.operator"
                                      role="combobox"
                                      tabindex="-1"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                                      >
                                        Operator
                                      </p>
                                    </div>
                                    <input
                                      aria-hidden="true"
                                      aria-invalid="false"
                                      class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                                      disabled=""
                                      name="conditions.0.operator"
                                      tabindex="-1"
                                      value=""
                                    />
                                    <svg
                                      aria-hidden="true"
                                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                                      data-testid="KeyboardArrowDownIcon"
                                      focusable="false"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                      />
                                    </svg>
                                    <fieldset
                                      aria-hidden="true"
                                      class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                    >
                                      <legend
                                        class="css-13wgbfv"
                                      >
                                        <span
                                          class="notranslate"
                                        >
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round-right "
                                      color="#C0C0C0"
                                      data-icon="bracket-round-right"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M37.4 46.2C27.6 61 31.5 80.8 46.3 90.6C71.9 107.7 128 167 128 256s-56.1 148.3-81.8 165.4c-14.7 9.8-18.7 29.7-8.9 44.4s29.7 18.7 44.4 8.9C120.1 449 192 371.8 192 256S120.1 62.9 81.8 37.4C67 27.6 47.2 31.5 37.4 46.2z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root css-17i9zmt-MuiGrid-root"
                            />
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div
                      id="DndDescribedBy-4"
                      style="display: none;"
                    >
                      
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                    </div>
                    <div
                      aria-atomic="true"
                      aria-live="assertive"
                      id="DndLiveRegion-4"
                      role="status"
                      style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                    />
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-19t1w01"
                    >
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jx8yas-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Add criteria"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Add criteria
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="func"
                          >
                            Using these Register entries, the Metric will display
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                      data-testid="func"
                    >
                      <div
                        aria-controls=":r1m:"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-label="Using these Register entries, the Metric will display"
                        aria-labelledby="mui-component-select-func"
                        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                        id="mui-component-select-func"
                        role="combobox"
                        tabindex="0"
                      >
                        <div
                          class="MuiBox-root css-9ou7bg"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-1ldt5e-MuiTypography-root"
                          >
                            A sum of field values
                          </p>
                        </div>
                      </div>
                      <input
                        aria-hidden="true"
                        aria-invalid="false"
                        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                        name="func"
                        tabindex="-1"
                        value="3"
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                        data-testid="KeyboardArrowDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                        />
                      </svg>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-13wgbfv"
                        >
                          <span
                            class="notranslate"
                          >
                            ​
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="sourceField"
                          >
                            From this Register field
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-source-field"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="From this Register field"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                            disabled=""
                            id="input-source-field"
                            name="sourceField"
                            placeholder="select a field"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value=""
                          />
                          <div
                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                          >
                            <div
                              class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                              data-testid="openDialog"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-ellipsis fa-xl "
                                color="#1B4AD5"
                                data-icon="ellipsis"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                  fill="currentColor"
                                />
                              </svg>
                            </div>
                          </div>
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="note"
                          >
                            Note
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Note"
                              class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              disabled=""
                              id=":r21:"
                              name="note"
                              placeholder=""
                              tabindex="-1"
                              type="text"
                              value="Metric will display ”–” if no valid field values are available"
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-43yvyf"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="width"
                          >
                            Width of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                      role="radiogroup"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall Mui-checked MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="0"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-z3zsu8-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Standard
                          </span>
                        </span>
                      </label>
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="1"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Wide
                          </span>
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="colors"
                          >
                            Color of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiBox-root css-69028f"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 css-1ht4vxp-MuiTypography-root"
                        >
                          Single color
                        </p>
                        <div
                          class="MuiGrid-root MuiGrid-container css-1cwdt9v-MuiGrid-root"
                        >
                          <div
                            class="MuiBox-root css-dk1n60"
                          />
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MetricsDetail renders the metric details form for option: An average of field values 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metric-settings-heading"
          >
            Metrics Settings
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Cancel"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Cancel
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Save"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-fs5pge"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-1dcp0mw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="name"
                          >
                            Metric label
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-metric-name"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Metric label"
                              class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              id="input-metric-name"
                              name="name"
                              placeholder="enter a name / label for this metric"
                              tabindex="0"
                              type="text"
                              value=""
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="context"
                          >
                            Metric data source
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-aemixd-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-data-source"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl Mui-readOnly MuiInputBase-readOnly css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="Metric data source"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-readOnly MuiInputBase-readOnly css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                            id="input-data-source"
                            name="context"
                            placeholder="select the register the metric will be based on"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value="VRM - Vendor Register"
                          />
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                    <div
                      class="MuiBox-root css-1li2cig"
                    >
                      <svg
                        data-icon="arrow-mandatory"
                        fill="currentColor"
                        height="19px"
                        style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                        viewBox="0 0 24 24"
                        width="19px"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                          fill="#EE0700"
                        />
                      </svg>
                      <p
                        class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                        style="line-height: 100%;"
                      >
                        Required
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
                >
                  Limit the metric to Register Entries that meet the following criteria
                </p>
                <div
                  class="MuiGrid-root MuiGrid-container css-1vcxjca-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
                  >
                    <ul
                      class="css-cuxljv"
                    >
                      <li
                        class="css-j6sz79"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-container css-1g8wflw-MuiGrid-root"
                        >
                          <div
                            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                          >
                            <button
                              aria-describedby="DndDescribedBy-3"
                              aria-disabled="false"
                              aria-roledescription="sortable"
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                              role="button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-bars "
                                data-icon="bars"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                          <div
                            class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-10zncv6-MuiGrid-root"
                          >
                            <div
                              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                            >
                              <div
                                class="MuiFormControl-root MuiTextField-root css-1m36lh0-MuiFormControl-root-MuiTextField-root"
                                data-testid="input-logical-operator"
                              >
                                <div
                                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                                >
                                  <input
                                    aria-invalid="false"
                                    class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                                    disabled=""
                                    id="input-logical-operator"
                                    tabindex="-1"
                                    type="text"
                                    value="If"
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                  >
                                    <legend
                                      class="css-13wgbfv"
                                    >
                                      <span
                                        class="notranslate"
                                      >
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round "
                                      color="#C0C0C0"
                                      data-icon="bracket-round"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M154.6 46.2c9.8 14.7 5.8 34.6-8.9 44.4C120.1 107.7 64 167 64 256s56.1 148.3 81.8 165.4c14.7 9.8 18.7 29.7 8.9 44.4s-29.7 18.7-44.4 8.9C71.9 449.1 0 371.8 0 256S71.9 62.9 110.2 37.4c14.7-9.8 34.6-5.8 44.4 8.9z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-ozdp81-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiBox-root css-i5q2k0"
                                  >
                                    <div
                                      class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                                      data-testid="input-register-field"
                                    >
                                      <div
                                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                                      >
                                        <input
                                          aria-invalid="false"
                                          aria-label="conditions.0.property"
                                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                                          id="input-register-field"
                                          name="conditions.0.property"
                                          placeholder="select a field"
                                          readonly=""
                                          tabindex="0"
                                          type="text"
                                          value=""
                                        />
                                        <div
                                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                                        >
                                          <div
                                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                            data-testid="openDialog"
                                          >
                                            <svg
                                              aria-hidden="true"
                                              class="svg-inline--fa fa-ellipsis fa-xl "
                                              color="#1B4AD5"
                                              data-icon="ellipsis"
                                              data-prefix="far"
                                              focusable="false"
                                              role="img"
                                              viewBox="0 0 448 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                                fill="currentColor"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                        <fieldset
                                          aria-hidden="true"
                                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                        >
                                          <legend
                                            class="css-13wgbfv"
                                          >
                                            <span
                                              class="notranslate"
                                            >
                                              ​
                                            </span>
                                          </legend>
                                        </fieldset>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="MuiBox-root css-1li2cig"
                                  >
                                    <svg
                                      data-icon="arrow-mandatory"
                                      fill="currentColor"
                                      height="19px"
                                      style="margin-right: 4px; margin-top: 1px; margin-bottom: 1px;"
                                      viewBox="0 0 24 24"
                                      width="19px"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M23 12.025 10.132 1v6.3H1v9.349h9.191V23S23 11.954 23 12.025"
                                        fill="#EE0700"
                                      />
                                    </svg>
                                    <p
                                      class="MuiTypography-root MuiTypography-body3 css-1qfbqta-MuiTypography-root"
                                      style="line-height: 100%;"
                                    >
                                      Required
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1c97dam-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                                    data-testid="conditions.0.operator"
                                  >
                                    <div
                                      aria-controls=":r1b:"
                                      aria-disabled="true"
                                      aria-expanded="false"
                                      aria-haspopup="listbox"
                                      aria-label="Operator"
                                      aria-labelledby="mui-component-select-conditions.0.operator"
                                      class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                                      id="mui-component-select-conditions.0.operator"
                                      role="combobox"
                                      tabindex="-1"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                                      >
                                        Operator
                                      </p>
                                    </div>
                                    <input
                                      aria-hidden="true"
                                      aria-invalid="false"
                                      class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                                      disabled=""
                                      name="conditions.0.operator"
                                      tabindex="-1"
                                      value=""
                                    />
                                    <svg
                                      aria-hidden="true"
                                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                                      data-testid="KeyboardArrowDownIcon"
                                      focusable="false"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                      />
                                    </svg>
                                    <fieldset
                                      aria-hidden="true"
                                      class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                    >
                                      <legend
                                        class="css-13wgbfv"
                                      >
                                        <span
                                          class="notranslate"
                                        >
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round-right "
                                      color="#C0C0C0"
                                      data-icon="bracket-round-right"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M37.4 46.2C27.6 61 31.5 80.8 46.3 90.6C71.9 107.7 128 167 128 256s-56.1 148.3-81.8 165.4c-14.7 9.8-18.7 29.7-8.9 44.4s29.7 18.7 44.4 8.9C120.1 449 192 371.8 192 256S120.1 62.9 81.8 37.4C67 27.6 47.2 31.5 37.4 46.2z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root css-17i9zmt-MuiGrid-root"
                            />
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div
                      id="DndDescribedBy-3"
                      style="display: none;"
                    >
                      
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                    </div>
                    <div
                      aria-atomic="true"
                      aria-live="assertive"
                      id="DndLiveRegion-3"
                      role="status"
                      style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                    />
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-19t1w01"
                    >
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jx8yas-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Add criteria"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Add criteria
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="func"
                          >
                            Using these Register entries, the Metric will display
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                      data-testid="func"
                    >
                      <div
                        aria-controls=":r17:"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-label="Using these Register entries, the Metric will display"
                        aria-labelledby="mui-component-select-func"
                        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                        id="mui-component-select-func"
                        role="combobox"
                        tabindex="0"
                      >
                        <div
                          class="MuiBox-root css-9ou7bg"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-1ldt5e-MuiTypography-root"
                          >
                            An average of field values
                          </p>
                        </div>
                      </div>
                      <input
                        aria-hidden="true"
                        aria-invalid="false"
                        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                        name="func"
                        tabindex="-1"
                        value="2"
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                        data-testid="KeyboardArrowDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                        />
                      </svg>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-13wgbfv"
                        >
                          <span
                            class="notranslate"
                          >
                            ​
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="sourceField"
                          >
                            From this Register field
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-source-field"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="From this Register field"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                            disabled=""
                            id="input-source-field"
                            name="sourceField"
                            placeholder="select a field"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value=""
                          />
                          <div
                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                          >
                            <div
                              class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                              data-testid="openDialog"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-ellipsis fa-xl "
                                color="#1B4AD5"
                                data-icon="ellipsis"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                  fill="currentColor"
                                />
                              </svg>
                            </div>
                          </div>
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="note"
                          >
                            Note
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Note"
                              class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              disabled=""
                              id=":r1i:"
                              name="note"
                              placeholder=""
                              tabindex="-1"
                              type="text"
                              value="Metric will display ”–” if no valid field values are available"
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-43yvyf"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="width"
                          >
                            Width of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                      role="radiogroup"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall Mui-checked MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="0"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-z3zsu8-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Standard
                          </span>
                        </span>
                      </label>
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="1"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Wide
                          </span>
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="colors"
                          >
                            Color of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiBox-root css-69028f"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 css-1ht4vxp-MuiTypography-root"
                        >
                          Single color
                        </p>
                        <div
                          class="MuiGrid-root MuiGrid-container css-1cwdt9v-MuiGrid-root"
                        >
                          <div
                            class="MuiBox-root css-dk1n60"
                          />
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MetricsDetail renders the metric details update form 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
  >
    <div
      class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-1a492nn-MuiToolbar-root"
    >
      <div
        class="MuiBox-root css-qjw807"
      >
        <div
          class="MuiBox-root css-m39v42"
        >
          <h1
            class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
            data-testid="metric-settings-heading"
          >
            Metrics Settings
          </h1>
        </div>
        <div
          class="MuiBox-root css-m39v42"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1b09hc2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Close"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Close
            </span>
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-wuwvd2-MuiButtonBase-root-MuiButton-root"
            data-testid="button-Save"
            tabindex="0"
            type="button"
          >
            <span
              class="css-1d0doyg"
            >
              Save
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-jrd0b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-4q12wf-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-fs5pge"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-1dcp0mw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="name"
                          >
                            Metric label
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-container css-c9d07l-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-1s76rlx-MuiGrid-root"
                      >
                        <div
                          class="MuiFormControl-root MuiTextField-root css-f09qfy-MuiFormControl-root-MuiTextField-root"
                          data-testid="input-metric-name"
                          inputmode="text"
                        >
                          <div
                            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                          >
                            <input
                              aria-invalid="false"
                              aria-label="Metric label"
                              class="MuiInputBase-input MuiOutlinedInput-input css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                              id="input-metric-name"
                              name="name"
                              placeholder="enter a name / label for this metric"
                              tabindex="0"
                              type="text"
                              value="Test Metric"
                            />
                            <fieldset
                              aria-hidden="true"
                              class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                            >
                              <legend
                                class="css-13wgbfv"
                              >
                                <span
                                  class="notranslate"
                                >
                                  ​
                                </span>
                              </legend>
                            </fieldset>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="context"
                          >
                            Metric data source
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiFormControl-root MuiTextField-root css-aemixd-MuiFormControl-root-MuiTextField-root"
                        data-testid="input-data-source"
                      >
                        <div
                          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl Mui-readOnly MuiInputBase-readOnly css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                        >
                          <input
                            aria-invalid="false"
                            aria-label="Metric data source"
                            class="MuiInputBase-input MuiOutlinedInput-input Mui-readOnly MuiInputBase-readOnly css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                            id="input-data-source"
                            name="context"
                            placeholder="select the register the metric will be based on"
                            readonly=""
                            tabindex="-1"
                            type="text"
                            value="VRM - Vendor Register"
                          />
                          <fieldset
                            aria-hidden="true"
                            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                          >
                            <legend
                              class="css-13wgbfv"
                            >
                              <span
                                class="notranslate"
                              >
                                ​
                              </span>
                            </legend>
                          </fieldset>
                        </div>
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <p
                  class="MuiTypography-root MuiTypography-body3 css-blskvo-MuiTypography-root"
                >
                  Limit the metric to Register Entries that meet the following criteria
                </p>
                <div
                  class="MuiGrid-root MuiGrid-container css-1vcxjca-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
                  >
                    <ul
                      class="css-cuxljv"
                    >
                      <li
                        class="css-j6sz79"
                      >
                        <div
                          class="MuiGrid-root MuiGrid-container css-1g8wflw-MuiGrid-root"
                        >
                          <div
                            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                          >
                            <button
                              aria-describedby="DndDescribedBy-6"
                              aria-disabled="false"
                              aria-roledescription="sortable"
                              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-hebkf2-MuiButtonBase-root-MuiIconButton-root"
                              role="button"
                              tabindex="0"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="svg-inline--fa fa-bars "
                                data-icon="bars"
                                data-prefix="far"
                                focusable="false"
                                role="img"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M0 88C0 74.7 10.7 64 24 64l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 112C10.7 112 0 101.3 0 88zM0 248c0-13.3 10.7-24 24-24l400 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L24 272c-13.3 0-24-10.7-24-24zM448 408c0 13.3-10.7 24-24 24L24 432c-13.3 0-24-10.7-24-24s10.7-24 24-24l400 0c13.3 0 24 10.7 24 24z"
                                  fill="currentColor"
                                />
                              </svg>
                            </button>
                          </div>
                          <div
                            class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-10zncv6-MuiGrid-root"
                          >
                            <div
                              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                            >
                              <div
                                class="MuiFormControl-root MuiTextField-root css-1m36lh0-MuiFormControl-root-MuiTextField-root"
                                data-testid="input-logical-operator"
                              >
                                <div
                                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl css-ylxlv0-MuiInputBase-root-MuiOutlinedInput-root"
                                >
                                  <input
                                    aria-invalid="false"
                                    class="MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-rla5rm-MuiInputBase-input-MuiOutlinedInput-input"
                                    disabled=""
                                    id="input-logical-operator"
                                    tabindex="-1"
                                    type="text"
                                    value="If"
                                  />
                                  <fieldset
                                    aria-hidden="true"
                                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                  >
                                    <legend
                                      class="css-13wgbfv"
                                    >
                                      <span
                                        class="notranslate"
                                      >
                                        ​
                                      </span>
                                    </legend>
                                  </fieldset>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round "
                                      color="#1B4AD5"
                                      data-icon="bracket-round"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M154.6 46.2c9.8 14.7 5.8 34.6-8.9 44.4C120.1 107.7 64 167 64 256s56.1 148.3 81.8 165.4c14.7 9.8 18.7 29.7 8.9 44.4s-29.7 18.7-44.4 8.9C71.9 449.1 0 371.8 0 256S71.9 62.9 110.2 37.4c14.7-9.8 34.6-5.8 44.4 8.9z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-ozdp81-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiBox-root css-i5q2k0"
                                  >
                                    <div
                                      class="MuiFormControl-root MuiTextField-root css-focfbn-MuiFormControl-root-MuiTextField-root"
                                      data-testid="input-register-field"
                                    >
                                      <div
                                        class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
                                      >
                                        <input
                                          aria-invalid="false"
                                          aria-label="conditions.0.property"
                                          class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
                                          id="input-register-field"
                                          name="conditions.0.property"
                                          placeholder="select a field"
                                          readonly=""
                                          tabindex="0"
                                          type="text"
                                          value=""
                                        />
                                        <div
                                          class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
                                        >
                                          <div
                                            class="MuiInputAdornment-root MuiInputAdornment-positionEnd css-1laqsz7-MuiInputAdornment-root"
                                            data-testid="openDialog"
                                          >
                                            <svg
                                              aria-hidden="true"
                                              class="svg-inline--fa fa-ellipsis fa-xl "
                                              color="#1B4AD5"
                                              data-icon="ellipsis"
                                              data-prefix="far"
                                              focusable="false"
                                              role="img"
                                              viewBox="0 0 448 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M432 256a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zm-160 0a48 48 0 1 1 -96 0 48 48 0 1 1 96 0zM64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96z"
                                                fill="currentColor"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                        <fieldset
                                          aria-hidden="true"
                                          class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                        >
                                          <legend
                                            class="css-13wgbfv"
                                          >
                                            <span
                                              class="notranslate"
                                            >
                                              ​
                                            </span>
                                          </legend>
                                        </fieldset>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1c97dam-MuiGrid-root"
                            >
                              <div
                                class="MuiBox-root css-iwu3eg"
                              >
                                <div
                                  class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                    />
                                  </div>
                                  <div
                                    class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                                    data-testid="conditions.0.operator"
                                  >
                                    <div
                                      aria-controls=":r2o:"
                                      aria-disabled="true"
                                      aria-expanded="false"
                                      aria-haspopup="listbox"
                                      aria-label="Operator"
                                      aria-labelledby="mui-component-select-conditions.0.operator"
                                      class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                                      id="mui-component-select-conditions.0.operator"
                                      role="combobox"
                                      tabindex="-1"
                                    >
                                      <p
                                        class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-155kr79-MuiTypography-root"
                                      >
                                        Operator
                                      </p>
                                    </div>
                                    <input
                                      aria-hidden="true"
                                      aria-invalid="false"
                                      class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                                      disabled=""
                                      name="conditions.0.operator"
                                      tabindex="-1"
                                      value=""
                                    />
                                    <svg
                                      aria-hidden="true"
                                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                                      data-testid="KeyboardArrowDownIcon"
                                      focusable="false"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                                      />
                                    </svg>
                                    <fieldset
                                      aria-hidden="true"
                                      class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                                    >
                                      <legend
                                        class="css-13wgbfv"
                                      >
                                        <span
                                          class="notranslate"
                                        >
                                          ​
                                        </span>
                                      </legend>
                                    </fieldset>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root MuiGrid-item css-1kzlyh2-MuiGrid-root"
                            >
                              <div
                                class="MuiGrid-root css-s4l6k3-MuiGrid-root"
                              >
                                <div
                                  class="MuiBox-root css-iwu3eg"
                                >
                                  <div
                                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                                  >
                                    <div
                                      class="MuiGrid-root MuiGrid-container css-dkeyeg-MuiGrid-root"
                                    >
                                      <div
                                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                                      />
                                    </div>
                                    <svg
                                      aria-hidden="true"
                                      class="svg-inline--fa fa-bracket-round-right "
                                      color="#1B4AD5"
                                      data-icon="bracket-round-right"
                                      data-prefix="fas"
                                      focusable="false"
                                      role="img"
                                      viewBox="0 0 192 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M37.4 46.2C27.6 61 31.5 80.8 46.3 90.6C71.9 107.7 128 167 128 256s-56.1 148.3-81.8 165.4c-14.7 9.8-18.7 29.7-8.9 44.4s29.7 18.7 44.4 8.9C120.1 449 192 371.8 192 256S120.1 62.9 81.8 37.4C67 27.6 47.2 31.5 37.4 46.2z"
                                        fill="currentColor"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                              class="MuiGrid-root css-17i9zmt-MuiGrid-root"
                            />
                          </div>
                        </div>
                      </li>
                    </ul>
                    <div
                      id="DndDescribedBy-6"
                      style="display: none;"
                    >
                      
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  
                    </div>
                    <div
                      aria-atomic="true"
                      aria-live="assertive"
                      id="DndLiveRegion-6"
                      role="status"
                      style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
                    />
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-19t1w01"
                    >
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1jx8yas-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Add criteria"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Add criteria
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="func"
                          >
                            Using these Register entries, the Metric will display
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-99tbm5-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
                      data-testid="func"
                    >
                      <div
                        aria-controls=":r2k:"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-label="Using these Register entries, the Metric will display"
                        aria-labelledby="mui-component-select-func"
                        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                        id="mui-component-select-func"
                        role="combobox"
                        tabindex="0"
                      >
                        <div
                          class="MuiBox-root css-9ou7bg"
                        >
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-1ldt5e-MuiTypography-root"
                          >
                            A count of register entries that meet the criteria
                          </p>
                        </div>
                      </div>
                      <input
                        aria-hidden="true"
                        aria-invalid="false"
                        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                        name="func"
                        tabindex="-1"
                        value="0"
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
                        data-testid="KeyboardArrowDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
                        />
                      </svg>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-13wgbfv"
                        >
                          <span
                            class="notranslate"
                          >
                            ​
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-1mq8088-MuiGrid-root"
              >
                <hr
                  class="MuiDivider-root MuiDivider-fullWidth css-1vqc57n-MuiDivider-root"
                />
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-43yvyf"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="width"
                          >
                            Width of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiFormGroup-root MuiFormGroup-row MuiRadioGroup-root MuiRadioGroup-row css-1cnc9be-MuiFormGroup-root"
                      role="radiogroup"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall Mui-checked MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="0"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-z3zsu8-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Standard
                          </span>
                        </span>
                      </label>
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiButtonBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall PrivateSwitchBase-root MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall MuiRadio-root MuiRadio-colorPrimary MuiRadio-sizeSmall css-1h2ehti-MuiButtonBase-root-MuiRadio-root"
                          shape="circle"
                        >
                          <input
                            class="PrivateSwitchBase-input css-1m9pwf3"
                            name="width"
                            type="radio"
                            value="1"
                          />
                          <span
                            class="css-hyxlzm"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-el4jxv-MuiSvgIcon-root"
                              data-testid="RadioButtonUncheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1xner4v-MuiSvgIcon-root"
                              data-testid="RadioButtonCheckedIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-e8ctdu-MuiTypography-root"
                        >
                          <span
                            style="min-width: auto;"
                          >
                            Wide
                          </span>
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-6 css-ldwg7m-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-iwu3eg"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container css-k6wrnl-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-container css-wp4yn4-MuiGrid-root"
                    >
                      <div
                        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-1fa237l-MuiTypography-root"
                        >
                          <label
                            for="colors"
                          >
                            Color of metric
                          </label>
                        </p>
                      </div>
                    </div>
                    <div
                      class="MuiBox-root css-i5q2k0"
                    >
                      <div
                        class="MuiBox-root css-69028f"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 css-1ht4vxp-MuiTypography-root"
                        />
                        <div
                          class="MuiGrid-root MuiGrid-container css-1cwdt9v-MuiGrid-root"
                        />
                      </div>
                      <button
                        class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1ve30hn-MuiButtonBase-root-MuiButton-root"
                        data-testid="button-Select"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="css-1d0doyg"
                        >
                          Select
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
