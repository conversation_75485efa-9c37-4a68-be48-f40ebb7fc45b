import { metricColorScalePalette } from '@protecht/ui-library/library/theme/colors/colors';
import { MetricColors } from 'metrics/types';

export const DEFAULT_METRIC_COLOR = '#181818';

export const DEFAULT_METRIC_COLORS: MetricColors = {
    color: DEFAULT_METRIC_COLOR,
    greyIfEmpty: false,
    scalesTxt: [{ color: '', others: true }],
    scalesList: [{ color: '', txt: '', others: false }],
    scalesNum: [
        { color: '', lessThan: 0 },
        { color: '', greaterThan: 0 },
    ],
    scalesOverdue: {
        open: metricColorScalePalette.metricColors.grey,
        due: metricColorScalePalette.metricColors.orange,
        overdue: metricColorScalePalette.metricColors.red,
    },
};
