import { render, screen } from 'test/utils';
import { UserEvent } from '@testing-library/user-event';
import React from 'react';
import { createMemoryRouter, generatePath, RouterProvider } from 'react-router';
import { VendorRiskManagementPath } from 'vendorRiskManagement/routes/vrmRoutes';
import { mockConfiguration, mockedStore, mockMetricEntry, mockVendorRegister } from 'vendorRiskManagement/api.mock';
import { strings } from 'common/utils/i18n';
import { mockNavigate } from 'test/config/setupAfterEnv';
import { getMetricFunctionLabel } from 'metrics/utils';
import { DisplayFunc, MetricsModule, Module } from 'metrics/types';
import { displayFuncOptions } from 'metrics/const';
import { waitFor, within } from '@testing-library/react';

// for an unknown reason, using DataGridPro with client pagination and autoPageSize causes an issue in tests (works o.k. in browser)
// where no rows are loaded in the table, unless props are set to these mocked values
import type { DataGridProProps } from '@mui/x-data-grid-pro';
import MetricsDetail from './MetricsDetail';

jest.mock('@mui/x-data-grid-pro', () => {
    const actual = jest.requireActual('@mui/x-data-grid-pro');
    const MockedDataGridPro = React.forwardRef<HTMLDivElement, DataGridProProps>((props, ref) => {
        const { DataGridPro } = actual;
        return (
            <DataGridPro
                {...props}
                disableVirtualization={true}
                autoPageSize={false}
                ref={ref}
            />
        );
    });

    return {
        ...actual,
        DataGridPro: MockedDataGridPro,
    };
});

jest.mock('metrics/api', () => ({
    getMetricDataSource: jest.fn(() => Promise.resolve({ totalCount: 1, records: [mockVendorRegister] })),
}));

jest.mock('vendorRiskManagement/api', () => ({
    getVendorConfigDefinition: jest.fn(() => Promise.resolve(mockConfiguration)),
}));

jest.mock('register/api', () => ({
    getRegisterByTableName: jest.fn(() => Promise.resolve(mockVendorRegister)),
    getRegister: jest.fn(() => Promise.resolve(mockVendorRegister)),
}));

jest.mock('view/rtkApi', () => {
    const actualRtkApi = jest.requireActual('view/rtkApi');

    const mockOperators = {
        DATE: ['=', '<', '>', 'is set', 'is not set', 'last', 'current', 'next'],
        NUMBER: ['<', '>', '=', '<>', 'contains', 'not contains', 'is set', 'is not set'],
        BOOLEAN_PRIMITIVE: ['is true', 'is false'],
        ATTACHMENT: ['is set', 'is not set'],
        COMPOUND_STRING: ['contains', 'not contains', 'is set', 'is not set'],
        STRING: ['=', '<>', 'contains', 'not contains', 'starts with', 'not starts with', 'ends with', 'not ends with', 'in', 'not in', 'is set', 'is not set'],
        BOOLEAN: ['is true', 'is false', 'is set', 'is not set'],
    };
    const mockData = {
        status: 'fulfilled',
        data: mockOperators,
        isUnitialized: false,
        isError: false,
        isLoading: false,
        isSuccess: true,
    };

    const mockSelect = jest.fn().mockImplementation(() => {
        return () => mockData;
    });
    const mockAsyncThunkAction = {
        matchFulfilled: jest.fn(),
        select: mockSelect,
    };

    return {
        ...actualRtkApi,
        viewsApi: {
            endpoints: {
                vrsGetExpressionContextUsingGet: mockAsyncThunkAction,
            },
        },
        useVrsGetExpressionContextUsingGetQuery: () => ({
            data: mockOperators,
            isLoading: false,
            isSuccess: true,
        }),
    };
});

jest.mock('register/rtkApi', () => ({
    ...jest.requireActual('register/rtkApi'),
    useTmrsGetRegisterConfigUsingGet1Query: () => ({ data: mockVendorRegister, isLoading: false, isSuccess: true, isError: false }),
    useTmrsGetRegisterConfigUsingGetQuery: () => ({ data: mockVendorRegister, isLoading: false, isSuccess: true, isError: false }),
}));

const mockCreateMetric = jest.fn().mockImplementation(() => Promise.resolve({}));
const mockUpdateMetric = jest.fn().mockImplementation(() => Promise.resolve({}));

const mockMetricRest = {
    metric: mockMetricEntry,
};
jest.mock('metrics/rtkApi', () => ({
    ...jest.requireActual('metrics/rtkApi'),
    useLazyMrsFindByIdUsingGetQuery: () => [
        jest.fn(() => Promise.resolve({ data: mockMetricRest })),
        {
            data: mockMetricRest,
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],

    useMrsCreateUsingPostMutation: () => [
        mockCreateMetric,
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],
    useMrsUpdateUsingPutMutation: () => [
        mockUpdateMetric,
        {
            isLoading: false,
            isSuccess: true,
            isError: false,
        },
    ],
}));

const registerFieldLabel1 = `${mockVendorRegister.sections[0].label} | ${mockVendorRegister.sections[0].fields[0].label}`;
const registerFieldLabel2 = `${mockVendorRegister.sections[1].label} | ${mockVendorRegister.sections[1].fields[0].label}`;

const selectOption = async (user: UserEvent, dataTestId: string, option: string) => {
    const selectInput = screen.getByTestId(dataTestId);
    await user.click(within(selectInput).getByRole('combobox'));
    const selectedOption = screen.getByRole('option', { name: option });
    await user.click(selectedOption);
    await waitFor(() => {
        expect(selectInput).not.toHaveClass('Mui-focused');
    });
};

const DISPLAY_OPTIONS = displayFuncOptions.map((option) => option.label);

const renderMetricsDetail = (isCreate = false) => {
    const path = isCreate ? VendorRiskManagementPath.CREATE_METRIC : VendorRiskManagementPath.EDIT_METRIC;

    const routes = [
        {
            path: path,
            element: (
                <MetricsDetail
                    module={Module.vrm}
                    feature={MetricsModule.VRM}
                    homePath={VendorRiskManagementPath.VENDOR_METRICS}
                />
            ),
        },
    ];

    const router = createMemoryRouter(routes, {
        initialEntries: [generatePath(path, { id: mockMetricEntry.id.toString() })],
    });

    return render(<RouterProvider router={router} />, mockedStore);
};

const renderMetricsDetailCreateForm = () => {
    return renderMetricsDetail(true);
};

const renderMetricsDetailUpdateForm = () => {
    return renderMetricsDetail();
};

describe('MetricsDetail', () => {
    it('renders the metric details create form', async () => {
        const view = renderMetricsDetailCreateForm();
        await waitFor(() => expect(view.container).toBeInTheDocument());
        expect(view.container).toMatchSnapshot();
    });

    it.each(DISPLAY_OPTIONS)('renders the metric details form for option: %s', async (option) => {
        const { user, container } = renderMetricsDetailCreateForm();

        await selectOption(user, 'func', option);

        expect(container).toMatchSnapshot();
    });

    it('renders the metric details update form', async () => {
        const view = renderMetricsDetailUpdateForm();

        // wait for data to be loaded
        expect(await screen.findByLabelText(/operator/i)).toBeInTheDocument();
        expect(await screen.findByTestId('button-Add criteria')).toBeInTheDocument();

        expect(view.container).toMatchSnapshot();
    });

    it('navigates back to the metrics list', async () => {
        const { user } = renderMetricsDetailCreateForm();

        await user.click(screen.getByTestId('button-Close'));

        expect(mockNavigate).toHaveBeenCalled();
    });

    it('creates the metric', async () => {
        const { user } = renderMetricsDetailCreateForm();

        const selectButtons = screen.getAllByTestId('button-Select');

        // metric label
        await user.type(screen.getByLabelText(strings('metrics:label.name')), 'Test Metric');

        // data source
        await user.click(selectButtons[0]);
        const dialog1 = screen.getByTestId('dialog-Select a Register');

        await waitFor(async () => {
            await user.click(within(dialog1).getByText(mockVendorRegister.label));
        });
        await user.click(within(dialog1).getByTestId('button-confirm'));

        await waitFor(() => {
            expect(dialog1).not.toBeInTheDocument();
        });

        await waitFor(() => {
            expect(screen.getByLabelText(strings('metrics:label.context'))).toHaveValue(mockVendorRegister.label);
        });

        // criteria field
        await user.click(screen.getByTestId('openDialog'));
        const fieldSelectorDialog = screen.getByTestId(`dialog-Select a field from "${mockVendorRegister.label}"`);
        const registerField1 = within(fieldSelectorDialog).getByText(registerFieldLabel1);
        const registerField2 = within(fieldSelectorDialog).getByText(registerFieldLabel2);

        expect(registerField1).toBeVisible();
        expect(registerField2).toBeVisible();
        expect(within(fieldSelectorDialog).getByText(strings('common:label.selectSourceField', { label: mockVendorRegister.label }))).toBeInTheDocument();

        await user.click(registerField1);
        await user.click(within(fieldSelectorDialog).getByText(strings('common:button.select')));

        // criteria operator
        await selectOption(user, 'conditions.0.operator', 'equals');

        // criteria value
        const value = screen.getByLabelText(/conditions\.0\.value/i);
        await user.type(value, 'test');

        // metric will display
        await selectOption(user, 'func', getMetricFunctionLabel(DisplayFunc.COUNT, true));

        // width of metric
        await user.click(screen.getByText(strings('metrics:label.standardWidth')));

        // color of the metric
        await user.click(selectButtons[1]);
        const colorScaleDialog = screen.getByTestId('dialog-Metric Color');
        await user.click(within(colorScaleDialog).getByTestId('color-selector-field'));
        await user.click(within(screen.getByTestId('contextMenu')).getByText(/black/i));
        await user.click(within(colorScaleDialog).getByTestId('button-confirm'));

        // save the metric
        await user.click(screen.getByTestId('button-Save'));
        expect(mockCreateMetric).toHaveBeenCalled();
    }, 70000);

    it('loads and updates the metric', async () => {
        const { user } = renderMetricsDetailUpdateForm();

        const metricLabelInput = screen.getByLabelText(strings('metrics:label.name')) as HTMLInputElement;

        await waitFor(() => {
            expect(metricLabelInput).toHaveValue(mockMetricEntry.name);
        });

        await user.clear(metricLabelInput);
        await user.type(metricLabelInput, 'New Label');

        await waitFor(() => {
            expect(metricLabelInput.value.trim()).toBe('New Label');
        });

        await user.click(screen.getByTestId('button-Save'));

        expect(mockUpdateMetric).toHaveBeenCalled();
    });
});
