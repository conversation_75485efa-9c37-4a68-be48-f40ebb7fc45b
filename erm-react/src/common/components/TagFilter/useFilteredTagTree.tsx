import { TagTreeItemRest } from 'api/generated/types';
import { useEffect, useState } from 'react';

const useFilteredTagTree = (searchValue: string, tagTree?: TagTreeItemRest[]) => {
    const [filteredTagTree, setFilteredTagTree] = useState<TagTreeItemRest[]>();

    useEffect(() => {
        if (!tagTree) {
            return;
        }

        if (!searchValue) {
            setFilteredTagTree(tagTree);
        } else {
            const lowerCaseSearchValue = searchValue.toLowerCase();
            const filteredCategories = tagTree?.reduce((tree, category) => {
                if (category.name?.toLowerCase().includes(lowerCaseSearchValue)) {
                    tree.push(category);
                } else {
                    const filteredTagNames = category.children?.filter((tagLabel) => tagLabel.name?.toLowerCase().includes(lowerCaseSearchValue));
                    if (filteredTagNames?.length) {
                        tree.push({ ...category, children: filteredTagNames });
                    }
                }

                return tree;
            }, [] as TagTreeItemRest[]);

            setFilteredTagTree(filteredCategories);
        }
    }, [searchValue, tagTree]);

    return filteredTagTree;
};

export default useFilteredTagTree;
