import React from 'react';
import { render, screen } from 'test/utils/rtl';
import TagFilter from './TagFilter';
import { TagContext, TagOperator } from '@protecht/ui-library/library/types';
import { mockedTagTree } from './mocks';
import { waitFor } from '@testing-library/react';
import { strings } from 'common/utils/i18n';

jest.mock('library/rtkApi', () => ({
    ...jest.requireActual('library/rtkApi'),
    useTrsGetTagTreeUsingGetQuery: jest.fn(() => ({
        data: mockedTagTree,
        isLoading: false,
        isSuccess: true,
        isError: false,
    })),
}));

const mockedContext = TagContext.BOWTIE;

const onChange = jest.fn();

const renderComponent = (selectedTagIds: number[] = [], selectedOperator = TagOperator.Or) => {
    return render(
        <TagFilter
            tagContext={mockedContext}
            selectedOperator={selectedOperator}
            selectedTagIds={selectedTagIds}
            onChange={onChange}
        />,
    );
};

describe('<TagFilter/>', () => {
    it('renders tag tree selector', () => {
        renderComponent();
        const tagCategory = 'Actions';
        const tag = 'Control answered in negative';
        expect(screen.getByText(tagCategory)).toBeInTheDocument();
        expect(screen.getByText(tag)).toBeInTheDocument();
    });

    it('filter tag tree according to search value - searching for tag category', async () => {
        const { user } = renderComponent();
        const search = screen.getByRole('textbox');
        expect(search).toBeInTheDocument();

        const tagCategory = 'Actions';
        await user.type(search, tagCategory);

        await waitFor(() => {
            expect(search).toHaveValue(tagCategory);
        });

        expect(screen.getByText(tagCategory)).toBeInTheDocument();

        await waitFor(() => {
            const otherTagCategory = 'Risk Cause Level 1';
            expect(screen.queryByText(otherTagCategory)).not.toBeInTheDocument();
        });
    });

    it('filter tag tree according to search value - searching for tag', async () => {
        const { user } = renderComponent();
        const search = screen.getByRole('textbox');
        expect(search).toBeInTheDocument();

        const tag = 'AML/CTF Act';
        await user.type(search, tag);

        await waitFor(() => {
            expect(search).toHaveValue(tag);
        });

        expect(screen.getByText(tag)).toBeInTheDocument();

        await waitFor(() => {
            const otherTag = 'dog';
            expect(screen.queryByText(otherTag)).not.toBeInTheDocument();
        });
    });

    it('selects parent tag category tree node when all its tags are selected', async () => {
        const { user } = renderComponent();

        const tag = 'Control answered in negative';
        await user.click(screen.getByText(tag));

        const categoryCheckbox = screen.getAllByRole('checkbox')[0];
        expect(categoryCheckbox).toBeChecked();
    });

    it('parent tag category tree node is indeterminate when some of its tags are unselected', async () => {
        const { user } = renderComponent();

        const tag = 'banana';
        await user.click(screen.getByText(tag));

        const categoryCheckbox = screen.getAllByRole('checkbox')[4];
        expect(categoryCheckbox).toHaveAttribute('data-indeterminate', 'true');
    });

    it('unselects parent tag category tree node when all its tags are unselected', async () => {
        const { user } = renderComponent();

        const tag = 'Control answered in negative';
        await user.click(screen.getByText(tag));

        const categoryCheckbox = screen.getAllByRole('checkbox')[0];
        expect(categoryCheckbox).toBeChecked();

        await user.click(screen.getByText(tag));

        expect(categoryCheckbox).not.toBeChecked();
    });

    it('preloads selected tags on init', () => {
        renderComponent([10060]);

        const categoryCheckbox = screen.getAllByRole('checkbox')[1];
        expect(categoryCheckbox).toBeChecked();
    });

    it('preloads selected tag operator on init', () => {
        renderComponent([], TagOperator.And);

        const categoryCheckbox = screen.getAllByRole('radio')[1];
        expect(categoryCheckbox).toBeChecked();
    });

    it('updates tag filter params when user selects item from tag tree', async () => {
        const { user } = renderComponent([], TagOperator.Or);

        const tag = 'Control answered in negative';
        await user.click(screen.getByText(tag));

        expect(onChange).toHaveBeenCalledWith(expect.objectContaining({ tagIds: expect.arrayContaining([10060]) }));
    });

    it('displays information about number of selected items', async () => {
        const { user } = renderComponent([], TagOperator.Or);

        const tag = 'Control answered in negative';
        await user.click(screen.getByText(tag));

        const infoMessage = strings('common:message.tagSelectorShowingItems', { currentCount: 13, totalCount: 13, selectedCount: 1 });

        expect(screen.getByText(infoMessage)).toBeInTheDocument();
    });
});
