import React, { ForwardedRef, forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { TagContext } from 'common/types';
import TagTreeSelector from './TagTreeSelector';
import { strings } from 'common/utils/i18n';
import RadioGroup from '@protecht/ui-library/library/components/RadioGroup';
import { useTrsGetTagTreeUsingGetQuery } from 'library/rtkApi';
import Search from '@protecht/ui-library/library/components/Inputs/Search';
import { TagTreeItemRest } from 'api/generated/types';
import useFilteredTagTree from './useFilteredTagTree';
import { filterOperators } from './const';
import { TagFilterParams, TagOperator } from '@protecht/ui-library/library/types';

export type Props = {
    tagContext: TagContext;
    selectedOperator?: string;
    selectedTagIds?: number[];
    onChange: (filter: TagFilterParams) => void;
};

export type TagFilterRef = {
    refresh: () => void;
};

const TagFilter = (props: Props, ref: ForwardedRef<TagFilterRef | undefined>) => {
    const { selectedOperator = TagOperator.Or } = props;

    const [selected, setSelected] = useState<TagTreeItemRest[]>([]);
    const [tagOperator, setTagOperator] = useState<TagOperator>(filterOperators.find((o) => o.value === selectedOperator)?.value || TagOperator.Or);
    const [data, setData] = useState<TagTreeItemRest[]>([]);

    const {
        data: tagTree,
        isFetching: isLoading,
        refetch,
    } = useTrsGetTagTreeUsingGetQuery({
        tagContext: props.tagContext,
    });

    const [searchValue, setSearchValue] = useState('');
    const onSearchValueChanged = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchValue(event.target.value);
    }, []);

    const filteredTagTree = useFilteredTagTree(searchValue, tagTree);

    const hasSelectedChild = useCallback((item: TagTreeItemRest, selectedIds?: number[]): boolean => {
        if (selectedIds === undefined || selectedIds.length === 0) {
            return false;
        }
        if (item.children) {
            if (item.children.some((child) => selectedIds.includes(child.id!))) {
                return true;
            }
            return item.children.some((child) => hasSelectedChild(child, selectedIds));
        }
        return false;
    }, []);

    const flatten = useCallback((nodes?: TagTreeItemRest[]): TagTreeItemRest[] => {
        if (!nodes) {
            return [];
        }
        return nodes.map((parent) => [parent, ...flatten(parent.children)]).reduce((a, b) => a.concat(b), []);
    }, []);

    const initSelected = useCallback(
        (data: TagTreeItemRest[]) => {
            if (!props.selectedTagIds || props.selectedTagIds.length === 0) {
                return;
            }

            const flattenData = flatten(data);
            const selectedTags = flattenData.filter((item) => {
                return (
                    (item.nodeType === 'tag' && props.selectedTagIds?.includes(item.id!)) ||
                    (item.nodeType === 'category' && item.children?.map((child) => child.id).every((childId) => props.selectedTagIds?.includes(childId!)))
                );
            });
            setSelected(selectedTags);
        },
        [props.selectedTagIds, flatten],
    );

    useEffect(() => {
        if (tagTree) {
            setData(tagTree);
            initSelected(tagTree);
        }
    }, [tagTree, initSelected]);

    // The `refresh` function provided to callers of this component as with `useImperativeHandle`
    // can simply call the `refetch` function from RTK Query.
    useImperativeHandle(ref, () => ({
        refresh() {
            void refetch();
        },
    }));

    const getTagFilterParams = (operator: TagOperator, selectedItems: TagTreeItemRest[]): TagFilterParams => {
        return {
            tagOperator: operator,
            tagIds: selectedItems.filter((item) => item.nodeType !== 'category').map((item) => item.id!),
        };
    };

    const handleFilterOperatorChanged = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value === TagOperator.And ? TagOperator.And : TagOperator.Or;
        setTagOperator(value);
        props.onChange(getTagFilterParams(value, selected));
    };

    const onTreeItemClicked = (treeItem: TagTreeItemRest, isSelection: boolean) => {
        let newSelected: TagTreeItemRest[] = [];

        if (isSelection) {
            // new item was selected
            newSelected = [...selected, treeItem];
            // if parent node is checked, check all it's children
            if (treeItem.children && treeItem.children.length > 0) {
                newSelected.push(...treeItem.children);
            } else {
                // select parent category if all its child tags are selected
                const parentCategory = data.find((item) => item.nodeType === 'category' && item.id === treeItem.tagType?.id);
                const childrenCount = newSelected.filter((item) => item.nodeType === 'tag' && item.tagType?.id === parentCategory?.id).length;
                if (parentCategory?.children?.length === childrenCount) {
                    newSelected.push(parentCategory);
                }
            }
        } else {
            // item was unselected
            const unselectedItems = treeItem.children ? [treeItem, ...treeItem.children] : [treeItem];
            const unselectedItemIds = unselectedItems.map((item) => item.id);
            newSelected = selected.filter((item) => !unselectedItemIds.includes(item.id));

            //no need to execute this code for category item
            if (treeItem.nodeType === 'tag') {
                // unselect parent category if none of its child tags are selected
                const selctedParentCategory = selected.find((item) => item.nodeType === 'category' && item.id === treeItem.tagType?.id);
                const childrenCount = newSelected.filter((item) => item.nodeType === 'tag' && item.tagType?.id === treeItem.tagType?.id).length;
                if (!!selctedParentCategory && childrenCount === 0) {
                    newSelected = newSelected.filter((item) => item.nodeType !== 'category' || item.id !== treeItem.tagType?.id);
                }
            }
        }

        // remove duplicates
        const distinct = [...new Set(newSelected)];
        setSelected(distinct);
        props.onChange(getTagFilterParams(tagOperator, distinct));
    };

    return (
        <Grid
            container
            direction="column"
            height="100%"
            flexWrap="nowrap"
            overflow="hidden"
        >
            <Grid
                item
                mb="6px"
            >
                <Search
                    searchPlaceholder={strings('common:label.tagFilterPlaceholder')}
                    searchValue={searchValue}
                    onValueChanged={onSearchValueChanged}
                />
            </Grid>
            <Grid
                item
                my="6px"
            >
                <Grid
                    container
                    gap="12px"
                    height="32px"
                    display="flex"
                    alignItems="center"
                >
                    <Grid item>
                        <Typography>{strings('ermConstants:tree_filter')}</Typography>
                    </Grid>
                    <Grid item>
                        <RadioGroup
                            options={filterOperators}
                            aria-label="tag-filter"
                            defaultValue={tagOperator}
                            onChange={handleFilterOperatorChanged}
                            row
                            sx={{
                                '&.MuiFormGroup-root': {
                                    marginLeft: 0,
                                },
                                '& .MuiFormControlLabel-root': {
                                    marginRight: '12px',
                                },
                            }}
                        />
                    </Grid>
                </Grid>
            </Grid>
            <Grid
                item
                flex={1}
                overflow="hidden"
            >
                <TagTreeSelector
                    isLoading={isLoading}
                    data={filteredTagTree}
                    selected={selected}
                    onTreeItemClicked={onTreeItemClicked}
                />
            </Grid>
        </Grid>
    );
};

export const ForwardedTagFilter = forwardRef(TagFilter);
export default ForwardedTagFilter;
