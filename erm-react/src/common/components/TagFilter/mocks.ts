export const mockedTagTree = [
    {
        id: 10007,
        name: 'Actions',
        nodeType: 'category',
        children: [
            {
                id: 10060,
                name: 'Control answered in negative',
                tagType: {
                    id: 10007,
                    name: 'Actions',
                    tagIds: [
                        10064, 10062, 10060, 10061, 10063, 10066, 10065, 10070, 10059, 10058, 10056, 10057, 10053, 10055, 10054, 10067, 10071, 10068, 10069,
                        10072,
                    ],
                },
                nodeType: 'tag',
            },
        ],
    },
    {
        id: 10103,
        name: 'Causes',
        nodeType: 'category',
        children: [
            {
                id: 10175,
                name: 'External',
                tagType: {
                    id: 10103,
                    name: 'Causes',
                    tagIds: [10175, 10161, 10160, 10164],
                },
                nodeType: 'tag',
            },
        ],
    },
    {
        id: 20660,
        name: 'dog',
        nodeType: 'category',
        children: [
            {
                id: 55081,
                name: 'banana',
                tagType: {
                    id: 20660,
                    name: 'dog',
                    tagIds: [55081, 55100, 27980],
                },
                nodeType: 'tag',
            },
            {
                id: 55100,
                name: 'cat',
                tagType: {
                    id: 20660,
                    name: 'dog',
                    tagIds: [55081, 55100, 27980],
                },
                nodeType: 'tag',
            },
            {
                id: 27980,
                name: 'coat',
                tagType: {
                    id: 20660,
                    name: 'dog',
                    tagIds: [55081, 55100, 27980],
                },
                nodeType: 'tag',
            },
        ],
    },
    {
        id: 10000,
        name: 'Regulatory',
        context: 'control',
        nodeType: 'category',
        children: [
            {
                id: 10003,
                name: 'AML/CTF Act',
                tagType: {
                    id: 10000,
                    name: 'Regulatory',
                    context: 'control',
                    tagIds: [10003, 10015, 10035, 10026, 10010, 10006, 10034, 10011, 10029, 10022, 10008, 10020, 10039, 10038, 10033, 71542, 68240],
                },
                nodeType: 'tag',
            },
            {
                id: 10006,
                name: 'Corporations Act',
                tagType: {
                    id: 10000,
                    name: 'Regulatory',
                    context: 'control',
                    tagIds: [10003, 10015, 10035, 10026, 10010, 10006, 10034, 10011, 10029, 10022, 10008, 10020, 10039, 10038, 10033, 71542, 68240],
                },
                nodeType: 'tag',
            },
            {
                id: 10008,
                name: 'FSRA',
                tagType: {
                    id: 10000,
                    name: 'Regulatory',
                    context: 'control',
                    tagIds: [10003, 10015, 10035, 10026, 10010, 10006, 10034, 10011, 10029, 10022, 10008, 10020, 10039, 10038, 10033, 71542, 68240],
                },
                nodeType: 'tag',
            },
        ],
    },
    {
        id: 10004,
        name: 'Risk Cause Level 1',
        context: 'riskcause',
        nodeType: 'category',
        children: [
            {
                id: 10040,
                name: 'External',
                tagType: {
                    id: 10004,
                    name: 'Risk Cause Level 1',
                    context: 'riskcause',
                    tagIds: [10040, 10042, 10041, 10043],
                },
                nodeType: 'tag',
            },
            {
                id: 10042,
                name: 'Internal Processes',
                tagType: {
                    id: 10004,
                    name: 'Risk Cause Level 1',
                    context: 'riskcause',
                    tagIds: [10040, 10042, 10041, 10043],
                },
                nodeType: 'tag',
            },
            {
                id: 10041,
                name: 'People',
                tagType: {
                    id: 10004,
                    name: 'Risk Cause Level 1',
                    context: 'riskcause',
                    tagIds: [10040, 10042, 10041, 10043],
                },
                nodeType: 'tag',
            },
            {
                id: 10043,
                name: 'Systems',
                tagType: {
                    id: 10004,
                    name: 'Risk Cause Level 1',
                    context: 'riskcause',
                    tagIds: [10040, 10042, 10041, 10043],
                },
                nodeType: 'tag',
            },
        ],
    },
    {
        id: 1,
        name: 'System',
        context: 'system',
        nodeType: 'category',
        children: [
            {
                id: 1,
                name: 'Obsolete',
                tagType: {
                    id: 1,
                    name: 'System',
                    context: 'system',
                    tagIds: [1],
                },
                nodeType: 'tag',
            },
        ],
    },
];
