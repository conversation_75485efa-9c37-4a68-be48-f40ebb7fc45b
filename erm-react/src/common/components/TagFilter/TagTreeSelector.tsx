import React, { useCallback, useEffect, useMemo, useState } from 'react';
import CustomAccordion from 'common/components/CustomAccordion';
import Box from '@mui/material/Box';
import { AccordionArrowStyle, IconOrientation } from '../CustomAccordion/types';
import LabeledCheckbox from '@protecht/ui-library/library/components/LabeledCheckbox';
import { TagTreeItemRest } from 'api/generated/types';
import { styled, useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import Loading from '@protecht/ui-library/library/components/Loading';

export type Props = {
    data?: TagTreeItemRest[];
    isLoading?: boolean;
    selected: TagTreeItemRest[];
    onTreeItemClicked: (item: TagTreeItemRest, isSelection: boolean) => void;
};

const StyledContainer = styled(Box)(({ theme }) => ({
    position: 'relative',
    border: `1px solid ${theme.palette.protechtGrey?.grey_206}`,
    overflow: 'auto',
    flex: 1,
    ...theme.mixins?.scrollbar?.(true),
}));

const TagTreeSelector: React.FC<Props> = ({ data, isLoading, selected, onTreeItemClicked }: Props) => {
    const [expandedCategories, setExpandedCategories] = useState<number[]>([]);

    useEffect(() => {
        if (data) {
            setExpandedCategories(data.map((category) => category.id!));
        }
    }, [data]);

    const isSelected = useCallback(
        (item: TagTreeItemRest): boolean => {
            return (selected && selected.some((s) => s.id === item.id)) || false;
        },
        [selected],
    );

    const isIndeterminate = useCallback(
        (item: TagTreeItemRest) => {
            if (item.nodeType === 'tag') {
                return false;
            }

            const tagIds = item.children?.map((tag) => tag.id);
            const selectedIds = selected.map((selected) => selected.id);

            const someChecked = tagIds?.some((id) => selectedIds.includes(id));
            const someUnChecked = tagIds?.some((id) => !selectedIds.includes(id));

            return someChecked && someUnChecked;
        },
        [selected],
    );

    const expandedTagsCount = useMemo(() => {
        const expanded = data?.filter((category) => expandedCategories.includes(category.id!));
        const expandedTags = expanded?.reduce((acc, category) => {
            return acc + (category.children?.length ?? 0);
        }, 0);

        return expandedTags;
    }, [data, expandedCategories]);

    const totalCount = useMemo(() => {
        const numberOfTags = data?.reduce((acc, category) => {
            return acc + (category.children?.length ?? 0);
        }, 0);

        return numberOfTags;
    }, [data]);

    const selectedTagsCount = useMemo(() => {
        return selected.filter((item) => item.nodeType === 'tag').length;
    }, [selected]);

    const theme = useTheme();

    const renderItem = useCallback(
        (item: TagTreeItemRest, index: number) => {
            const selected = isSelected(item);
            return (
                <CustomAccordion
                    headlineSx={{
                        '&:hover': {
                            backgroundColor: theme.palette.protechtGrey?.grey_245,
                        },
                    }}
                    key={`accordion-${item.id}-${index}`}
                    arrowStyle={AccordionArrowStyle.TRIANGLE}
                    isOpenStyle={IconOrientation.DOWN}
                    expandable={(item.children && item.children.length > 0) || false}
                    headerContent={
                        <LabeledCheckbox
                            onClick={() => onTreeItemClicked(item, !selected)}
                            key={`checkbox-${item.id}`}
                            checked={selected}
                            label={item.name}
                            checkboxSx={{ padding: '0 8px' }}
                            labelSx={{ paddingLeft: 0, overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}
                            sx={{ overflow: 'hidden', width: '100%' }}
                            indeterminate={isIndeterminate(item)}
                        />
                    }
                    bodyContent={item.children ? item.children.map((child, index) => renderItem(child, index)) : null}
                    onExpand={() => {
                        setExpandedCategories((prev) => {
                            if (item.id && prev.includes(item.id)) {
                                return prev.filter((id) => id != item.id);
                            } else if (item.id) {
                                return [...prev, item.id];
                            }

                            return prev;
                        });
                    }}
                    defaultOpen={true}
                />
            );
        },
        [isIndeterminate, isSelected, onTreeItemClicked, theme.palette.protechtGrey?.grey_245],
    );

    return (
        <Box
            height="100%"
            display="flex"
            flexDirection="column"
        >
            <StyledContainer>
                {isLoading && <Loading message={strings('common:message.loading')} />}
                {!isLoading && data?.map((item, index) => renderItem(item, index))}
            </StyledContainer>
            {!isLoading && (
                <Typography padding="6px 8px">
                    {strings('common:message.tagSelectorShowingItems', { currentCount: expandedTagsCount, totalCount, selectedCount: selectedTagsCount })}
                </Typography>
            )}
        </Box>
    );
};

export default TagTreeSelector;
