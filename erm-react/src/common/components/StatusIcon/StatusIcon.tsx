import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileTimes, faLockAlt } from '@fortawesome/pro-solid-svg-icons';
import { ObjectStatus } from 'app/types';
import useTheme from '@mui/system/useTheme';

interface Props {
    status: ObjectStatus | null | undefined;
}

const StatusIcon: React.FC<Props> = ({ status }: Props) => {
    const theme = useTheme();

    switch (Number(status)) {
        case ObjectStatus.Deleted:
            return (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        fontSize: '12px',
                        paddingRight: '4px',
                        color: theme.palette.error.main,
                    }}
                >
                    <FontAwesomeIcon icon={faFileTimes} />
                </div>
            );
        case ObjectStatus.Disabled:
        case ObjectStatus.PasswordExpired:
        case ObjectStatus.PasswordInvalidAttempts:
            return (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        fontSize: '12px',
                        paddingRight: '4px',
                        color: theme.palette.warning.main,
                    }}
                >
                    <FontAwesomeIcon icon={faLockAlt} />
                </div>
            );
        default:
            return <div />;
    }
};

export default StatusIcon;
