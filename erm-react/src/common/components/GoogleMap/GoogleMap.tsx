import React, { FC, useCallback, useEffect, useRef, useState } from 'react';
import Box from '@mui/material/Box';
import useGoogleMap, { MapControls } from 'common/hooks/useGoogleMap';
import { styled } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import { requestFullscreen } from './utils';
import { MagnifyMinus, MagnifyPlus, Maximize } from '@protecht/ui-library/library/components/SVGIcons';

type Props = {
    mapHeight: string;
    defaultCenter: google.maps.LatLngLiteral;
    defaultZoom: number;
    mapOptions: google.maps.MapOptions | null;
    markerPosition?: google.maps.LatLngLiteral;
    disableControls?: boolean;
    onClick?: (event: google.maps.MapMouseEvent, map: google.maps.Map) => void;
};

const StyledIconButton = styled(IconButton)({
    minWidth: 0,
    width: 28,
    height: 28,
    padding: 0,
});

const GoogleMap: FC<Props> = ({ mapHeight, mapOptions, onClick, markerPosition, defaultZoom, defaultCenter, disableControls = false }) => {
    const mapContainerRef = useRef<HTMLDivElement>(null);
    const [mapControls, setMapControls] = useState<MapControls[] | undefined>(undefined);
    const mounted = useRef(false);

    useEffect(() => {
        mounted.current = true;

        return () => {
            mounted.current = false;
        };
    }, []);

    useEffect(() => {
        if (mounted.current && !disableControls) {
            const fullScreenEl = document.getElementById('map-control-full-screen');
            const zoomControls = document.getElementById('map-control-zoom-buttons');

            if (zoomControls && fullScreenEl) {
                setMapControls([
                    { controlElement: fullScreenEl, controlPosition: google.maps.ControlPosition.TOP_RIGHT },
                    { controlElement: zoomControls, controlPosition: google.maps.ControlPosition.RIGHT_BOTTOM },
                ]);
            }
        }
    }, [mounted.current]);

    const { map, marker } = useGoogleMap(mapContainerRef, { center: defaultCenter, zoom: defaultZoom }, markerPosition, mapControls);

    useEffect(() => {
        if (map) {
            map.setOptions(mapOptions);

            const currentCenter = map.getCenter();
            if (
                mapOptions?.center !== undefined &&
                mapOptions?.center !== null &&
                (currentCenter?.lat() !== mapOptions?.center?.lat || currentCenter?.lng() !== mapOptions?.center?.lng)
            ) {
                map.panTo(mapOptions.center);
            }
        }
    }, [defaultCenter, map, mapOptions]);

    useEffect(() => {
        let listener: google.maps.MapsEventListener | null = null;
        if (map && onClick) {
            listener = map.addListener('click', (event) => {
                onClick && onClick(event, map);
            });
        }

        return () => {
            if (listener) {
                listener.remove();
            }
        };
    }, [map, onClick]);

    useEffect(() => {
        if (marker) {
            marker.setPosition(markerPosition);
        }
    }, [marker, markerPosition, map]);

    const handleZoomIn = useCallback(() => {
        if (map) {
            map.setZoom(map.getZoom()! - 1);
        }
    }, [map]);

    const handleZoomOut = useCallback(() => {
        if (map) {
            map.setZoom(map.getZoom()! + 1);
        }
    }, [map]);

    const handleFullScreen = useCallback(() => {
        if (map) {
            const elementToSendFullscreen = map.getDiv().firstChild;

            if (document?.fullscreenElement === elementToSendFullscreen) {
                void document.exitFullscreen();
            } else {
                requestFullscreen(elementToSendFullscreen);
            }
        }
    }, [map]);

    return (
        <>
            <Box
                ref={mapContainerRef}
                data-testid="google-map"
                sx={{ height: mapHeight, width: '100%' }}
            />

            {!disableControls && (
                <Box display="none">
                    <StyledIconButton
                        color="primary"
                        size="small"
                        id="map-control-full-screen"
                        data-testid="map-control-full-screen"
                        sx={{ margin: '6px' }}
                        onClick={handleFullScreen}
                    >
                        <Maximize />
                    </StyledIconButton>

                    <Box
                        id="map-control-zoom-buttons"
                        data-testid="map-control-zoom-buttons"
                        display="flex"
                        flexDirection="column"
                        gap="6px"
                        margin="6px"
                    >
                        <StyledIconButton
                            color="primary"
                            size="small"
                            onClick={handleZoomOut}
                        >
                            <MagnifyPlus />
                        </StyledIconButton>
                        <StyledIconButton
                            color="primary"
                            size="small"
                            onClick={handleZoomIn}
                        >
                            <MagnifyMinus />
                        </StyledIconButton>
                    </Box>
                </Box>
            )}
        </>
    );
};
export default GoogleMap;
