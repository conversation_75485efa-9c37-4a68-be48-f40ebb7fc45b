import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import GoogleMap from './GoogleMap';
import user from '@testing-library/user-event';

describe('GoogleMap Component', () => {
    const defaultProps = {
        mapHeight: '400px',
        defaultCenter: { lat: -34.397, lng: 150.644 },
        defaultZoom: 8,
        mapOptions: { gestureHandling: 'greedy', disableDefaultUI: true },
        markerPosition: { lat: -35.397, lng: 151.644 },
        disableControls: false,
        onClick: jest.fn(),
    };

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should render the map container', () => {
        render(<GoogleMap {...defaultProps} />);
        const mapContainer = screen.getByTestId('google-map');
        expect(mapContainer).toBeInTheDocument();
        expect(mapContainer).toHaveStyle(`height: ${defaultProps.mapHeight}`);
    });

    it('should call onClick when the map is clicked', async () => {
        render(<GoogleMap {...defaultProps} />);
        const mapContainer = screen.getByTestId('google-map');
        await user.click(mapContainer);
        expect(defaultProps.onClick).toHaveBeenCalled();
    });

    it('should render custom UI controls when disableCustomUI is false', async () => {
        render(<GoogleMap {...defaultProps} />);
        const fullScreenButton = screen.getByTestId('map-control-full-screen');
        const zoomControls = screen.getByTestId('map-control-zoom-buttons');

        expect(fullScreenButton).toBeInTheDocument();
        expect(zoomControls).toBeInTheDocument();
    });

    it('should create map with default mapOptions', () => {
        render(<GoogleMap {...defaultProps} />);

        expect(google.maps.Map).toHaveBeenCalledWith(
            expect.anything(),
            expect.objectContaining({ zoom: defaultProps.defaultZoom, center: defaultProps.defaultCenter }),
        );
    });

    it('should create marker with provided position', () => {
        render(<GoogleMap {...defaultProps} />);

        expect(google.maps.Marker).toHaveBeenCalledWith(expect.objectContaining({ position: defaultProps.markerPosition }));
    });
});
