const createMockFuncsFromArray = (instance: any, names: any[] = []) => {
    names.forEach((name) => {
        instance[name] = jest.fn().mockName(name);
    });
};

const mockArrayWithClear = () => {
    const array = [];
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    array.clear = jest.fn();
    return array;
};

const createGoogleMapsMock = () => {
    const createMVCObject = (instance) => {
        const listeners = {};
        instance.listeners = listeners;

        instance.addListener = jest
            .fn((event, fn) => {
                listeners[event] = listeners[event] || [];
                listeners[event].push(fn);

                if (instance?.mapDiv) {
                    instance.mapDiv.addEventListener(event, fn);
                }
                return {
                    remove: () => {
                        const index = listeners[event].indexOf(fn);

                        if (index !== -1) {
                            listeners[event].splice(index, 1);
                        }
                    },
                };
            })
            .mockName('addListener');

        createMockFuncsFromArray(instance, ['bindTo', 'get', 'notify', 'set', 'setValues', 'unbind', 'unbindAll']);
    };

    const maps = {
        ControlPosition: {
            TOP_LEFT: 1,
            TOP_CENTER: 2,
            TOP: 2,
            TOP_RIGHT: 3,
            LEFT_CENTER: 4,
            LEFT: 5,
            LEFT_TOP: 5,
            LEFT_BOTTOM: 6,
            RIGHT: 7,
            RIGHT_CENTER: 8,
            RIGHT_BOTTOM: 9,
            BOTTOM_LEFT: 10,
            BOTTOM: 11,
            BOTTOM_CENTER: 11,
            BOTTOM_RIGHT: 12,
            CENTER: 13,
        },
        Data: jest.fn().mockImplementation(function(options) {
            this.options = options;
            createMVCObject(this);
            createMockFuncsFromArray(this, ['setControlPosition', 'setControls', 'setDrawingMode', 'setMap', 'setStyle']);
        }),
        Map: jest.fn().mockImplementation(function(mapDiv, opts) {
            this.mapDiv = mapDiv;
            this.opts = opts;
            this.controls = {
                3: mockArrayWithClear(),
                9: mockArrayWithClear(),
            };
            createMVCObject(this);
            createMockFuncsFromArray(this, [
                'setCenter',
                'setClickableIcons',
                'setHeading',
                'setMapTypeId',
                'setOptions',
                'setStreetView',
                'setTilt',
                'getZoom',
                'setZoom',
                'fitBounds',
                'getBounds',
                'panToBounds',
            ]);
            this.getDiv = jest.fn().mockImplementation(function() {
                return this.mapDiv;
            });
            this.getCenter = jest.fn().mockImplementation(() => {
                return { lat: () => 0, lng: () => 0 };
            });
            this.panTo = jest.fn();
        }),
        MapTypeControlStyle: {
            DEFAULT: 0,
            DROPDOWN_MENU: 2,
            HORIZONTAL_BAR: 1,
            INSET: 3,
            INSET_LARGE: 4,
        },
        MapTypeId: {
            HYBRID: 'hybrid',
            ROADMAP: 'roadmap',
            SATELLITE: 'satellite',
            TERRAIN: 'terrain',
        },
        Marker: jest.fn().mockImplementation(function(opts) {
            this.opts = opts;
            createMVCObject(this);
            createMockFuncsFromArray(this, ['setMap', 'setOpacity', 'setOptions', 'setPosition', 'setShape', 'setTitle', 'setVisible', 'setZIndex']);
        }),
        event: {
            clearInstanceListeners: jest.fn().mockName('clearInstanceListeners'),
        },
    };

    return maps;
};

export default createGoogleMapsMock;
