import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import LinksList, { LinkListItemIcon, LinkListItemInfoBubble, LinkListItemTitle } from '@protecht/ui-library/library/components/LinksList';
import { IdOnly, IdWithNameAndStatusRest, ObjectStatus } from 'app/types';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import { ColumnType } from '@protecht/ui-library/library/types';
import useTheme from '@mui/system/useTheme';
import BusinessUnitInfoBubbleContent from 'common/components/Form/FormFields/InputSelectorPickerField/InfoBubbleContent/BusinessUnitInfoBubbleContent';
import UserInfoBubbleContent from 'common/components/Form/FormFields/InputSelectorPickerField/InfoBubbleContent/UserInfoBubbleContent';
import { getSelectorMetadata } from 'common/utils/definitions';
import {
    BowtieOutlined,
    BusinessUnit,
    Control,
    Kri,
    MapPin,
    Question,
    RiskCause,
    RiskEvent,
    User,
    Role,
} from '@protecht/ui-library/library/components/SVGIcons';
import { SelectorType } from 'common/types';
import Box from '@mui/material/Box';
import AuditQuestionInfoBubbleContent from '../Form/FormFields/InputSelectorPickerField/InfoBubbleContent/AuditQuestionInfoBubbleContent/AuditQuestionInfoBubbleContent';
import ComplianceQuestionInfoBubbleContent from '../Form/FormFields/InputSelectorPickerField/InfoBubbleContent/ComplianceQuestionInfoBubbleContent/ComplianceQuestionInfoBubbleContent';
import BowtieInfoBubbleContent from '../Form/FormFields/InputSelectorPickerField/InfoBubbleContent/BowtieInfoBubbleContent/BowtieInfoBubbleContent';
import KriInfoBubbleContent from '../Form/FormFields/InputSelectorPickerField/InfoBubbleContent/KriInfoBubbleContent/KriInfoBubbleContent';
import ControlInfoBubbleContent from '../Form/FormFields/InputSelectorPickerField/InfoBubbleContent/ControlInfoBubbleContent/ControlInfoBubbleContent';
import RiskEventInfoBubbleContent from '../Form/FormFields/InputSelectorPickerField/InfoBubbleContent/RiskEventInfoBubbleContent/RiskEventInfoBubbleContent';
import RiskCauseInfoBubbleContent from '../Form/FormFields/InputSelectorPickerField/InfoBubbleContent/RiskCauseInfoBubbleContent/RiskCauseInfoBubbleContent';

type Props<T> = {
    selected: T[];
    type: ColumnType | SelectorType;
    onItemRemove?: (item: IdOnly) => void;
    readOnly?: boolean;
    onSelectedItemClick?: (item: IdOnly) => void;
    onItemHover?: (item: IdOnly) => void;
    disableHover?: boolean;
    disabled?: boolean;
    startIcon?: React.ReactNode;
};

const LinksListBuilder = <T extends IdWithNameAndStatusRest>({
    onItemRemove,
    selected,
    type,
    readOnly,
    onSelectedItemClick,
    onItemHover,
    disableHover = false,
    disabled = false,
    startIcon,
}: Props<T>) => {
    const theme = useTheme();
    const { deletedLabel, infoBubble, fontWeight } = useMemo(() => getSelectorMetadata(type), [type]);

    const titleColor = type === ColumnType.TABLE ? theme.palette.primary.main : theme.palette.protechtGrey?.black;
    const isDisabledHover = disableHover || type === ColumnType.COUNTRY || type === ColumnType.STATE;

    const renderInfoBubble = useCallback(
        (selected: IdOnly) => {
            switch (type) {
                case ColumnType.BUSINESS_UNIT:
                    return (
                        <LinkListItemInfoBubble
                            content={<BusinessUnitInfoBubbleContent selected={[selected]} />}
                            maxWidth="none"
                        />
                    );
                case ColumnType.USER:
                    return (
                        <LinkListItemInfoBubble
                            content={<UserInfoBubbleContent selected={[selected]} />}
                            maxWidth="none"
                        />
                    );
                case ColumnType.RISK_CAUSE:
                    return (
                        <LinkListItemInfoBubble
                            content={<RiskCauseInfoBubbleContent selected={selected} />}
                            maxWidth="none"
                        />
                    );
                case ColumnType.RISK_EVENT:
                    return (
                        <LinkListItemInfoBubble
                            content={<RiskEventInfoBubbleContent selected={selected} />}
                            maxWidth="none"
                        />
                    );
                case ColumnType.CONTROL:
                    return (
                        <LinkListItemInfoBubble
                            content={<ControlInfoBubbleContent selected={selected} />}
                            maxWidth="none"
                        />
                    );
                case SelectorType.KRI:
                    return (
                        <LinkListItemInfoBubble
                            content={<KriInfoBubbleContent selected={selected} />}
                            maxWidth="none"
                        />
                    );
                case SelectorType.AUDIT_QUESTION:
                    return (
                        <LinkListItemInfoBubble
                            content={<AuditQuestionInfoBubbleContent selected={selected} />}
                            maxWidth="none"
                        />
                    );
                case SelectorType.BOWTIE:
                    return (
                        <LinkListItemInfoBubble
                            content={<BowtieInfoBubbleContent selected={selected} />}
                            maxWidth="none"
                        />
                    );
                case SelectorType.QUESTION:
                    return (
                        <LinkListItemInfoBubble
                            content={<ComplianceQuestionInfoBubbleContent selected={selected} />}
                            maxWidth="none"
                        />
                    );
                default:
                    return;
            }
        },
        [type],
    );

    const icon = useMemo(() => {
        const dataTestIdAttribute = { 'data-testid': `links-list-icon-${type}` };

        switch (type) {
            case ColumnType.BUSINESS_UNIT:
                return <BusinessUnit {...dataTestIdAttribute} />;
            case ColumnType.USER:
            case ColumnType.SIGN_OFF:
                return <User {...dataTestIdAttribute} />;
            case ColumnType.COUNTRY:
            case ColumnType.STATE:
                return <MapPin {...dataTestIdAttribute} />;
            case ColumnType.RISK_CAUSE:
                return <RiskCause {...dataTestIdAttribute} />;
            case ColumnType.RISK_EVENT:
                return <RiskEvent {...dataTestIdAttribute} />;
            case ColumnType.CENTRAL_LIBRARY:
            case ColumnType.CONTROL:
                return <Control {...dataTestIdAttribute} />;
            case SelectorType.KRI:
                return <Kri {...dataTestIdAttribute} />;
            case SelectorType.AUDIT_QUESTION:
            case SelectorType.QUESTION:
                return (
                    <Question
                        {...dataTestIdAttribute}
                        viewBox="2 2 20 20"
                    />
                );
            case SelectorType.BOWTIE:
                return <BowtieOutlined {...dataTestIdAttribute} />;
            case ColumnType.ROLE:
                return (
                    <Role
                        {...dataTestIdAttribute}
                        color={theme.palette.protechtGrey?.grey_146}
                    />
                );
            default:
                return;
        }
    }, [type]);

    const handleHover = useCallback(
        (item: T) => {
            onItemHover?.(item);
        },
        [onItemHover],
    );

    return (
        <LinksList<T>
            items={selected}
            keyExtractor={(item) => item.id}
            disableHover={isDisabledHover}
            renderContent={(item) => {
                return (
                    <Box
                        sx={{ display: 'flex', width: '100%', gap: 1 }}
                        onMouseEnter={() => handleHover(item)}
                    >
                        {startIcon}
                        {icon && (
                            <LinkListItemIcon
                                icon={icon}
                                color={item.status === ObjectStatus.Deleted ? theme.palette.protechtGrey?.grey_206 : undefined}
                            />
                        )}
                        <LinkListItemTitle
                            title={
                                onSelectedItemClick ? (
                                    <span
                                        onClick={() => onSelectedItemClick?.(item)}
                                        style={{
                                            cursor: disabled ? 'default' : 'pointer',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            color: `${disabled && theme.palette.protechtGrey?.grey_178}`,
                                            fontWeight: `${fontWeight}`,
                                        }}
                                    >
                                        {item.name}
                                    </span>
                                ) : (
                                    <span
                                        style={{
                                            cursor: `${disabled && 'default'}`,
                                            color: `${disabled && theme.palette.protechtGrey?.grey_178}`,
                                            fontWeight: `${fontWeight}`,
                                        }}
                                    >
                                        {item.name}
                                    </span>
                                )
                            }
                            color={item.status === ObjectStatus.Deleted ? theme.palette.protechtGrey?.grey_146 : titleColor}
                            fontWeight={fontWeight}
                        />
                        {item.status === ObjectStatus.Deleted && (
                            <Typography
                                variant="body2"
                                color="accentColors.red"
                            >
                                {deletedLabel}
                            </Typography>
                        )}
                        {item.status === ObjectStatus.Disabled && (
                            <Typography
                                variant="body2"
                                color="accentColors.analyticsOrange"
                            >
                                {strings('ermConstants:status_locked')}
                            </Typography>
                        )}
                        {infoBubble && renderInfoBubble(item)}
                    </Box>
                );
            }}
            onItemRemove={(itemToRemove: IdOnly) => {
                onItemRemove?.(itemToRemove);
            }}
            removeType="clear"
            readonly={readOnly || disabled}
            clearTooltip={strings('common:button.clear')}
        />
    );
};
export default LinksListBuilder;
