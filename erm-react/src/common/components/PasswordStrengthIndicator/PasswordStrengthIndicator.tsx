import React, { ForwardRefRenderFunction } from 'react';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';
import { linearProgressClasses } from '@mui/material/LinearProgress';
import { LinearProgressProps } from '@mui/material/LinearProgress';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';

const BorderLinearProgress = styled(LinearProgress, {
    shouldForwardProp: (prop) => prop !== 'bcolor',
})<{ bcolor: string }>(({ bcolor }) => ({
    height: 8,
    borderRadius: 4,
    border: `1px solid ${bcolor}`,
    backgroundColor: 'white',
    [`&.${linearProgressClasses.colorPrimary}`]: {
        backgroundColor: 'white',
    },
    [`& .${linearProgressClasses.bar}`]: {
        borderRadius: 4,
        backgroundColor: bcolor,
    },
}));

const PasswordStrengthIndicator: ForwardRefRenderFunction<LinearProgressProps> = ({ value = 0, ...otherPros }: LinearProgressProps, ref) => {
    const passwordStrengthProps = ERM.passwordStrengthProps(value);

    return (
        <Box sx={{ position: 'relative' }}>
            {value > 0 && (
                <Typography
                    variant="body2"
                    sx={{ position: 'absolute', right: 0, bottom: '15px' }}
                >
                    {passwordStrengthProps.name}
                </Typography>
            )}
            <BorderLinearProgress
                {...otherPros}
                ref={ref}
                variant="determinate"
                value={value * 20}
                bcolor={passwordStrengthProps.color}
            />
        </Box>
    );
};

export default React.forwardRef(PasswordStrengthIndicator);
