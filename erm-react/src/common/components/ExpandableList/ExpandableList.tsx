import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import useTheme from '@mui/system/useTheme';
import React from 'react';
import { ExpandableListColumn, ExpandableListRows } from './types';
import ExpandableListHeader from './ExpandableListHeader';
import ExpandableItems from './ExpandableItems';
import { LoadingOnRender } from '../LoadingOnRender';

type Props<T> = {
    columns: ExpandableListColumn<T>[];
    rows: ExpandableListRows<T>;
    shouldLoadData: boolean;
    filterSearchString?: string | undefined;
    expanded: string[];
    onExpandedChanged: (id: string, expanded: boolean) => void;
};

const ExpandableListContainer = styled(Box)(({ theme }) => ({
    border: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
    borderBottom: 0,
}));

function ExpandableList<T>({ columns, rows, shouldLoadData = false, filterSearchString = undefined, expanded, onExpandedChanged }: Props<T>): JSX.Element {
    const theme = useTheme();

    return (
        <ExpandableListContainer>
            <ExpandableListHeader<T> columns={columns}></ExpandableListHeader>
            <LoadingOnRender
                loaderStyling={{
                    height: '200px',
                    borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
                }}
            >
                <ExpandableItems<T>
                    shouldLoadData={shouldLoadData}
                    rows={rows}
                    columns={columns}
                    level={1}
                    filterSearchString={filterSearchString}
                    expanded={expanded}
                    onExpandedChanged={onExpandedChanged}
                ></ExpandableItems>
            </LoadingOnRender>
        </ExpandableListContainer>
    );
}

export default ExpandableList;
