import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React from 'react';
import { ExpandableListColumn } from './types';

type Props<T> = {
    columns: ExpandableListColumn<T>[];
};

const ExpandableListHeaderContainer = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.protechtGrey?.grey_245,
    height: 40,
    display: 'flex',
    alignItems: 'center',
    borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
    '& > *:not(:first-of-type)': {
        borderLeft: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
    },
}));

function ExpandableListHeader<T>({ columns }: Props<T>): JSX.Element {
    return (
        <ExpandableListHeaderContainer>
            {columns.map((column, index) => (
                <Box
                    key={index}
                    sx={{
                        flex: column.flex ?? 1,
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                    }}
                >
                    <Typography
                        variant="body2"
                        px={2}
                    >
                        {column.title}
                    </Typography>
                </Box>
            ))}
        </ExpandableListHeaderContainer>
    );
}

export default ExpandableListHeader;
