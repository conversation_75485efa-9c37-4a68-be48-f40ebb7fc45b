export type ExpandableListColumn<T> = {
    title: string;
    identifier: keyof NonExpandableListRow<T>;
    flex?: string | number;
};

export type ExpandableListRowsLoader<T> = () => Promise<AsyncExpandableListRows<T>>;

export type AsyncExpandableListRows<T> = {
    expandable?: AsyncExpandableListRow<T>[];
    nonExpandable?: AsyncNonExpandableListRow<T>[];
};

export type ExpandableListRows<T> = {
    expandable?: ExpandableListRow<T>[];
    nonExpandable?: NonExpandableListRow<T>[];
};

export type ExpandableListRowBase = {
    id: string;
    name: string;
    title: string | JSX.Element;
    secondaryAction?: JSX.Element;
};

export type AsyncExpandableListRow<T> = ExpandableListRowBase & {
    rows: ExpandableListRowsLoader<T>;
};

export type ExpandableListRow<T> = ExpandableListRowBase & {
    rows: ExpandableListRows<T>;
};

export type NonExpandableListRowBase<T> = {
    id: string;
    name: string;
    title: string | JSX.Element;
} & T;

export type AsyncNonExpandableListRow<T> = NonExpandableListRowBase<T> & {
    rows?: ExpandableListRowsLoader<T>;
};

export type NonExpandableListRow<T> = NonExpandableListRowBase<T> & {
    rows?: ExpandableListRows<T>;
};

export type ExpandableAccordionRef = {
    hasSearchResults: () => boolean;
};
