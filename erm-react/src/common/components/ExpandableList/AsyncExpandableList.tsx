import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import React from 'react';
import { ExpandableListColumn, ExpandableListRowsLoader } from './types';
import ExpandableListHeader from './ExpandableListHeader';
import AsyncExpandableAccordion from './AsyncExpandableAccordion';

type Props<T> = {
    columns: ExpandableListColumn<T>[];
    rows: ExpandableListRowsLoader<T>;
    shouldLoadData: boolean;
    filterSearchString?: string | undefined;
    expanded: string[];
    onExpandedChanged: (id: string, expanded: boolean) => void;
};

const ExpandableListContainer = styled(Box)(({ theme }) => ({
    border: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
    borderBottom: 0,
}));

function AsyncExpandableList<T>({ columns, rows, shouldLoadData = false, filterSearchString = undefined, expanded, onExpandedChanged }: Props<T>): JSX.Element {
    return (
        <ExpandableListContainer>
            <ExpandableListHeader<T> columns={columns}></ExpandableListHeader>
            <AsyncExpandableAccordion<T>
                shouldLoadData={shouldLoadData}
                rows={rows}
                columns={columns}
                level={1}
                filterSearchString={filterSearchString}
                expanded={expanded}
                onExpandedChanged={onExpandedChanged}
            ></AsyncExpandableAccordion>
        </ExpandableListContainer>
    );
}

export default AsyncExpandableList;
