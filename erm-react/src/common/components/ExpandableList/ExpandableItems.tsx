import Box from '@mui/material/Box';
import React, { Fragment } from 'react';
import { AsyncExpandableListRows, ExpandableListColumn, ExpandableListRows } from './types';
import NonExpandableAccordionRow from './NonExpandableAccordionRow';
import AsyncExpandableAccordion from './AsyncExpandableAccordion';
import ExpandableAccordion from './ExpandableAccordion';

type Props<T> = {
    columns: ExpandableListColumn<T>[];
    rows: ExpandableListRows<T> | AsyncExpandableListRows<T>;
    shouldLoadData: boolean;
    level: number;
    isAsync?: boolean;
    filterSearchString?: string | undefined;
    expanded: string[];
    onExpandedChanged: (id: string, expanded: boolean) => void;
};

const ExpandableItems = <T,>(props: Props<T>): JSX.Element => {
    const { rows, columns, isAsync = false, shouldLoadData = false, level = 1, filterSearchString } = props;

    const getNonExpandableRows = () => {
        if (!rows || !rows.nonExpandable || rows.nonExpandable.length === 0) {
            return null;
        }

        return (
            <Box>
                {rows.nonExpandable.map((nonExpandableRow, index) => {
                    return (
                        <Fragment key={index}>
                            <NonExpandableAccordionRow
                                level={level}
                                rowData={nonExpandableRow}
                                columns={columns}
                            ></NonExpandableAccordionRow>
                            {nonExpandableRow.rows && isAsync && (
                                <AsyncExpandableAccordion<T>
                                    filterSearchString={filterSearchString}
                                    rows={nonExpandableRow.rows}
                                    columns={columns}
                                    level={level + 1}
                                    shouldLoadData={true}
                                    expanded={props.expanded}
                                    onExpandedChanged={props.onExpandedChanged}
                                />
                            )}
                            {nonExpandableRow.rows && !isAsync && (
                                <ExpandableItems<T>
                                    filterSearchString={filterSearchString}
                                    rows={nonExpandableRow.rows}
                                    columns={columns}
                                    level={level + 1}
                                    shouldLoadData={true}
                                    expanded={props.expanded}
                                    onExpandedChanged={props.onExpandedChanged}
                                />
                            )}
                        </Fragment>
                    );
                })}
            </Box>
        );
    };

    const getExpandableRows = () => {
        return rows?.expandable?.map((row) => (
            <Fragment key={row.id}>
                <ExpandableAccordion
                    level={level}
                    columns={columns}
                    row={row}
                    filterSearchString={filterSearchString}
                    expanded={props.expanded}
                    onExpandedChanged={props.onExpandedChanged}
                />
            </Fragment>
        ));
    };

    return (
        <>
            {shouldLoadData && getNonExpandableRows()}
            {shouldLoadData && getExpandableRows()}
        </>
    );
};

export default ExpandableItems;
