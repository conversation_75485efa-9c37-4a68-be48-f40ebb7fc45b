import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import React from 'react';
import { ExpandableListColumn, NonExpandableListRow } from './types';

type Props<T> = {
    columns: ExpandableListColumn<T>[];
    rowData: NonExpandableListRow<T>;
    level: number;
};

const StyledRow = styled(Box)(({ theme }) => ({
    minHeight: '48px',
    display: 'flex',
    alignItems: 'flex-start',
    borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
    '& > *': {
        padding: '12px 0',
    },
    '& > *:not(:first-of-type)': {
        height: '100%',
        minHeight: '48px',
        display: 'flex',
        alignItems: 'center',
        '& > *:first-of-type': {
            marginLeft: theme.spacing(2),
        },
    },
}));

const StyledDivider = styled('div', {
    shouldForwardProp: (prop) => prop !== 'displayLeftDivider' && prop !== 'displayRightDivider' && prop !== 'columnFlex',
})<{ displayLeftDivider: boolean; displayRightDivider: boolean; columnFlex: number | string | undefined }>(
    ({ theme, displayLeftDivider, displayRightDivider, columnFlex }) => ({
        position: 'relative',
        flex: columnFlex,
        minHeight: '48px',
        '&:after': displayRightDivider
            ? {
                  content: '""',
                  position: 'absolute',
                  borderLeft: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
                  right: -1,
                  top: 0,
                  height: '100%',
              }
            : undefined,
        '&:before': displayLeftDivider
            ? {
                  content: '""',
                  position: 'absolute',
                  borderLeft: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
                  left: 0,
                  top: 0,
                  height: '100%',
              }
            : undefined,
    }),
);

const NonExpandableAccordionRow = <T,>({ rowData, columns, level }: Props<T>): JSX.Element => {
    return (
        <StyledRow>
            {columns.map((column, index) => (
                <StyledDivider
                    key={index}
                    displayLeftDivider={index > 0}
                    displayRightDivider={index < columns.length - 1}
                    columnFlex={column.flex}
                >
                    <Box sx={{ typography: 'body1', pl: index === 0 ? `${(level - 1) * 3 * 8 + 22}px` : 0, width: '100%' }}>
                        {rowData[column.identifier] as JSX.Element}
                    </Box>
                </StyledDivider>
            ))}
        </StyledRow>
    );
};

export default NonExpandableAccordionRow;
