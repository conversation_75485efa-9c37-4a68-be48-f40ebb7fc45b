import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { alpha, styled } from '@mui/material/styles';
import useTheme from '@mui/system/useTheme';
import React, { useRef } from 'react';
import { ExpandableListColumn, ExpandableListRow } from './types';
import Highlighted from '../Highlighted/Highlighted';
import ExpandableItems from './ExpandableItems';
import { Accordion } from '@protecht/ui-library/library/components/Accordion';
import { ChevronRight } from '@protecht/ui-library/library/components/SVGIcons';

type Props<T> = {
    row: ExpandableListRow<T>;
    columns: ExpandableListColumn<T>[];
    level: number;
    filterSearchString?: string | undefined;
    expanded: string[];
    onExpandedChanged: (id: string, expanded: boolean) => void;
};

const StyledAccordion = styled(Accordion)(({ theme }) => ({
    backgroundColor: theme.palette.background.default,
    '&:before': {
        display: 'none',
    },
}));

const StyledAccordionSummary = styled(AccordionSummary, {
    shouldForwardProp: (prop) => prop !== 'level',
})<{ level: number }>(({ theme, level }) => ({
    paddingLeft: theme.spacing(level * 3),
    backgroundColor: theme.palette.background.default,
    flexDirection: 'row-reverse',
    borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_220}`,
    '&:hover': {
        backgroundImage: `linear-gradient(${theme.palette.protechtGrey?.grey_245} 0 0)`,
    },
    '&:focus': {
        backgroundImage: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)} 0 0)`,
    },
    '& .MuiAccordionSummary-expandIconWrapper': {
        fontSize: '14px',
        color: theme.palette.primary.main,
    },
    '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
        transform: 'rotate(90deg)',
    },
    '& .MuiAccordionSummary-content': {
        marginLeft: '13px',
    },
}));

const StyledAccordionDetails = styled(AccordionDetails)(() => ({
    padding: 0,
}));

const ExpandableAccordion = <T,>({ row, columns, level, filterSearchString, onExpandedChanged, expanded }: Props<T>): JSX.Element => {
    const theme = useTheme();
    const accordionRef = useRef<null | HTMLDivElement>(null);

    const onChange = (_, expanded) => {
        onExpandedChanged(row.id, expanded);
        if (expanded && accordionRef.current) {
            setTimeout(
                () =>
                    accordionRef.current?.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'start',
                    }),
                1,
            );
        }
    };

    const isOpened = expanded.includes(row.id);
    return (
        <div ref={accordionRef}>
            <StyledAccordion
                key={row.id}
                onChange={onChange}
                expanded={isOpened}
            >
                <StyledAccordionSummary
                    expandIcon={
                        <ChevronRight
                            data-icon="chevron-right"
                            style={{ color: ProtechtDictionary.accentColor }}
                            width="20px"
                            height="20px"
                        />
                    }
                    level={level}
                >
                    <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2">
                            <Highlighted
                                text={row.name}
                                highlight={filterSearchString}
                                highlightStyling={{
                                    fontWeight: 'bold',
                                    background: alpha(theme.palette.accentColors?.yellow || theme.palette.primary.main, 0.3),
                                }}
                            ></Highlighted>
                        </Typography>
                        {row.secondaryAction}
                    </Box>
                </StyledAccordionSummary>
                <StyledAccordionDetails>
                    {row.rows && (
                        <ExpandableItems<T>
                            filterSearchString={filterSearchString}
                            level={level + 1}
                            shouldLoadData={isOpened}
                            rows={row.rows}
                            columns={columns}
                            expanded={expanded}
                            onExpandedChanged={onExpandedChanged}
                        />
                    )}
                </StyledAccordionDetails>
            </StyledAccordion>
        </div>
    );
};

export default ExpandableAccordion;
