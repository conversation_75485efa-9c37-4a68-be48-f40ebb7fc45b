import { AsyncExpandableListRows, ExpandableListColumn, ExpandableListRowsLoader } from './types';
import React, { useEffect } from 'react';
import useAsync, { AsyncStatus } from '../../hooks/useAsync/useAsync';
import Box from '@mui/material/Box';
import Loading from '../Loading';
import ExpandableItems from './ExpandableItems';
import { LoaderType } from '../Loading/Loading';

type Props<T> = {
    columns: ExpandableListColumn<T>[];
    rows: ExpandableListRowsLoader<T>;
    shouldLoadData: boolean;
    level: number;
    filterSearchString?: string | undefined;
    expanded: string[];
    onExpandedChanged: (id: string, expanded: boolean) => void;
};

const AsyncExpandableAccordion = <T,>(props: Props<T>): JSX.Element => {
    const { rows, columns, shouldLoadData = false, level = 1, filterSearchString, expanded, onExpandedChanged } = props;

    const { asyncLoad, isLoading, response: loadedData, status } = useAsync<AsyncExpandableListRows<T>>({ status: AsyncStatus.IDLE });

    useEffect(() => {
        if (shouldLoadData && status === AsyncStatus.IDLE) {
            void asyncLoad(rows());
        }
    }, [asyncLoad, rows, shouldLoadData, status]);

    if (isLoading) {
        return (
            <Box sx={{ p: 2 }}>
                <Loading loaderType={LoaderType.Slim} />
            </Box>
        );
    }

    return (
        <>
            {loadedData && (
                <ExpandableItems<T>
                    rows={loadedData}
                    columns={columns}
                    shouldLoadData={shouldLoadData}
                    level={level}
                    filterSearchString={filterSearchString}
                    isAsync={true}
                    expanded={expanded}
                    onExpandedChanged={onExpandedChanged}
                />
            )}
        </>
    );
};

export default AsyncExpandableAccordion;
