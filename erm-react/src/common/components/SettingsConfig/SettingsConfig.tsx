import React, { useEffect, useState } from 'react';
import * as Yup from 'yup';
import useFormValidationResolver from 'common/hooks/forms/useFormValidationResolver';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { FieldValidation, createValidationSchema } from 'common/utils/yup.utils';
import { SectionInputType, SettingsSection } from './SettingsConfigItem';
import { SettingsGroup, SettingsItem, SettingsItemActions } from '@protecht/ui-library/library/components/Settings';
import { VRMVendorSettingsNames } from 'vendorRiskManagement/types';
import Button from '@protecht/ui-library/library/components/Button';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';

type Props = {
    title?: string;
    sections?: SettingsSection[];
    onSubmit: (formValue: unknown) => void;
};

const SettingsConfig: React.FC<Props> = ({ title, sections, onSubmit }) => {
    const [schema, setSchema] = useState<Yup.BaseSchema>();
    const resolver = useFormValidationResolver(schema);

    const formMethods = useForm({
        mode: 'onChange',
        resolver,
    });

    const { handleSubmit, control, formState, reset } = formMethods;
    const { isDirty, isValid } = formState;

    useEffect(() => {
        const sectionsInputs = sections?.filter((section) => !!section.type && section.type !== SectionInputType.BOOLEAN);
        const sectionsToggles = sections?.filter((section) => section.enableToggle);
        let formFields: FieldValidation[] = [];
        const initialValues = {};

        if (sectionsInputs?.length) {
            formFields = [
                ...sectionsInputs.map((section) => {
                    const validations = new Map<string, unknown>();

                    if (section.required) {
                        validations.set('required', section.required);
                    } else {
                        validations.set('optional', true);
                    }

                    return {
                        controlName: `${section.name}.value`,
                        controlLabel: section.label,
                        validationData: {
                            validationType: 'string' as const,
                            validations,
                        },
                    };
                }),
            ];

            sectionsInputs.map((section) => {
                initialValues[section.name] = { ...initialValues[section.name], value: section.value };
            });
        }

        if (sectionsToggles) {
            formFields = [
                ...formFields,
                ...sectionsToggles.map((section) => {
                    const validations = new Map<string, unknown>();
                    validations.set('boolean', null);

                    return {
                        controlName: `${section.name}.toggle`,
                        controlLabel: `${section.label} toggle`,
                        validationData: {
                            validationType: 'boolean' as const,
                            validations,
                        },
                    };
                }),
            ];

            sectionsToggles.map((section) => {
                initialValues[section.name] = { ...initialValues[section.name], toggle: !!section.toggleValue };
            });
        }

        setSchema(createValidationSchema(formFields));

        reset(initialValues);
    }, [sections, reset]);

    const formValues = useWatch({ control });
    useEffect(() => {
        if (isDirty && isValid) {
            void handleSubmit(onSubmit, (errors) => {
                console.error('Form errors', { errors });
            })();
        }
    }, [formValues, handleSubmit, isDirty, isValid, onSubmit]);

    return (
        <FormProvider {...formMethods}>
            {sections && (
                <SettingsGroup title={title}>
                    {sections.map((section, index) => {
                        let actions = SettingsItemActions.None;
                        if (section.enableToggle) {
                            actions |= SettingsItemActions.Toggle;
                        }
                        if (section.enableReset) {
                            actions |= SettingsItemActions.Reset;
                        }

                        if (section.actionButtonCallback) {
                            return (
                                <SettingsItem
                                    key={index}
                                    section={section}
                                    actions={actions}
                                >
                                    <Button
                                        name={`${section.name}.value`}
                                        variant="outlined"
                                        onClick={section.actionButtonCallback}
                                        size="large"
                                    >
                                        {section.actionButtonLabel}
                                    </Button>
                                </SettingsItem>
                            );
                        }

                        return (
                            <SettingsItem
                                key={index}
                                section={section}
                                actions={actions}
                            >
                                {section.name !== VRMVendorSettingsNames.VendorUserSSO && (
                                    <InputField
                                        value={section.value}
                                        name={`${section.name}.value`}
                                    />
                                )}
                            </SettingsItem>
                        );
                    })}
                </SettingsGroup>
            )}
        </FormProvider>
    );
};

export default SettingsConfig;
