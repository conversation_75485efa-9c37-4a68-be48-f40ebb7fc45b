import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@protecht/ui-library/library/components/Button';
import SwitchField from 'common/components/Form/FormFields/SwitchField';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import React from 'react';

// todo add more input types
export enum SectionInputType {
    TEXTBOX = 'textbox',
    BOOLEAN = 'java.lang.Boolean',
    STRING = 'java.lang.String',
}

export type SettingsSection = {
    id: number;
    name: string;
    enableToggle?: boolean;
    enableReset?: boolean;
    toggleValue?: boolean;
    label: string;
    description: string;
    actionButtonLabel?: string;
    actionButtonCallback?: () => void;
    type?: SectionInputType;
    editable?: boolean;
    required?: boolean;
    internal?: boolean;
    value?: any;
    category?: string;
};

type Props = {
    section: SettingsSection;
};

const SettingsConfigItem: React.FC<Props> = ({ section }) => {
    const renderSectionFormInput = (name: string, type: SectionInputType) => {
        // todo render other inputs according to input type and move Setting Section to separate file
        switch (type) {
            case SectionInputType.TEXTBOX:
                return (
                    <InputField
                        emptyLabelSpacing={false}
                        name={name}
                    />
                );
            case SectionInputType.STRING:
                return (
                    <InputField
                        clearable
                        name={name}
                        emptyLabelSpacing={false}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <Box>
            <Box
                display="flex"
                justifyContent="space-between"
                mb="10px"
            >
                <Typography variant="h6">{section.label}</Typography>
                {section.enableToggle && (
                    <Box>
                        <SwitchField
                            emptyLabelSpacing={false}
                            name={`${section.name}-toggle`}
                        ></SwitchField>
                    </Box>
                )}
            </Box>
            <Typography
                variant="body1"
                mb="16px"
            >
                {section.description}
            </Typography>
            {section.actionButtonLabel && (
                <Button
                    variant="outlined"
                    onClick={section.actionButtonCallback}
                    size="large"
                >
                    {section.actionButtonLabel}
                </Button>
            )}
            {section.type && renderSectionFormInput(section.name, section.type)}
        </Box>
    );
};

export default SettingsConfigItem;
