import React from 'react';
import { screen, render } from 'test/utils';
import ObjectState from './ObjectState';

describe('<ObjectState/>', () => {
    const setup = () => {
        return render(<ObjectState objectState={'test-text'} />);
    };

    it('was rendered', () => {
        const view = setup();
        expect(view.container).toMatchSnapshot();
        const titleText = screen.getByText('test-text');
        expect(titleText).toBeInTheDocument();
    });
});
