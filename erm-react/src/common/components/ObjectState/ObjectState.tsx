import React from 'react';
import Typography from '@mui/material/Typography';

interface Props {
    objectState?: string;
}

// TODO: enhance in the future for Register Entry state
export default function ObjectState(props: Props): JSX.Element {
    const { objectState } = props;

    if (!objectState) {
        return <div />;
    }

    return (
        <Typography
            variant="h1"
            color="protechtGrey.grey_146"
            data-testid="state-heading"
        >
            {objectState}
        </Typography>
    );
}
