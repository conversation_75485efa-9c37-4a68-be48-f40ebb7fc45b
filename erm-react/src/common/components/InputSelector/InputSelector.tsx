import React, { useCallback, useMemo, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlusCircle } from '@fortawesome/pro-solid-svg-icons';
import InputAdornment from '@mui/material/InputAdornment';
import useTheme from '@mui/system/useTheme';
import { IdOnly } from 'app/types';
import SuggestionsInput from '../SuggestionsInput';
import Selector from '../Selector';
import { getSelectorMetadata } from 'common/utils/definitions';
import { DataGridColDef, SelectorType } from 'common/types';

import { ColumnType } from 'register/types';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { strings } from 'common/utils/i18n';
import Grid from '@mui/material/Grid';
import { IdWithNameRest } from 'api/generated/types';
import InputLookup, { LOOKUP_SUPPORTED_FIELDS } from '../SuggestionsInput/InputLookup';
import { InputProps } from '@mui/material/Input';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

type Props<T> = {
    columnDefinition?: DataGridColDef[];
    createMessage?: string;
    disabled?: boolean;
    displayValue?: string;
    filterData?: any;
    multiselect?: boolean;
    placeholder?: string;
    readOnly?: boolean;
    selected?: T[];
    selectedInputOverride?: React.ReactNode;
    showSelectButton?: boolean;
    suggestionField?: string;
    type: ColumnType | SelectorType;
    lookupDisabled?: boolean;
    onChange?: (selected: number[]) => void;
    onCreateNew?: () => void;
    onSelect?: (selected: T[]) => void;
    setInitialValue?: (value?: string) => void;
    onDataLoad?: (query: string) => Promise<T[]>;
    onDataClear?: () => void;
    inputProps?: Partial<InputProps>;
};

const InputSelector = <T extends IdWithNameRest>({
    disabled,
    displayValue,
    multiselect = false,
    placeholder,
    readOnly,
    selected = [],
    selectedInputOverride,
    showSelectButton = true,
    type,
    lookupDisabled = false,
    onChange,
    onCreateNew,
    onSelect,
    onDataLoad,
    onDataClear,
    inputProps,
    ...otherProps
}: Props<T>): JSX.Element => {
    const theme = useTheme();
    const containerRef = React.useRef<HTMLDivElement>(null);

    const [isFocused, setIsFocused] = useState(false);
    const [selectorVisible, setSelectorVisible] = useState<boolean>(false);
    const selectorMetadata = useMemo(() => getSelectorMetadata(type), [type]);

    const openSelector = () => {
        setIsFocused(false);
        setSelectorVisible(true);
    };

    const closeSelector = () => {
        setSelectorVisible(false);
    };

    const submit = (selected: T[]) => {
        onSelect?.(selected);

        const ids = selected.map((item) => item.id).filter((id): id is number => id !== undefined);

        onChange?.(ids);
        closeSelector();
    };

    const onItemSelected = useCallback(
        (item: IdOnly) => {
            if (!selected.some((selectedItem) => selectedItem.id === item.id)) {
                onSelect?.([...selected, item as T]);
            }
        },
        [onSelect, selected],
    );

    const handleCreateNewInSelector = useCallback(() => {
        closeSelector();
        onCreateNew?.();
    }, [onCreateNew]);

    const transformedSelected = selected.filter((item) => item.id !== undefined).map((item) => ({ id: item.id! })) as IdOnly[];
    const containerWidth = useMemo(() => containerRef.current?.offsetWidth, [containerRef.current?.offsetWidth]);

    const renderLookup = useMemo(() => {
        if (onDataLoad && LOOKUP_SUPPORTED_FIELDS.includes(type as ColumnType)) {
            return (
                <InputLookup
                    value={displayValue}
                    readOnly={readOnly || lookupDisabled || disabled}
                    multiselect={multiselect}
                    suggestionField={selectorMetadata.suggestionField || 'name'}
                    inputPlaceholder={placeholder || selectorMetadata.placeholder}
                    onDataLoad={onDataLoad}
                    onDataClear={onDataClear}
                    onSelect={onItemSelected}
                    containerWidth={containerWidth}
                    focused={isFocused}
                />
            );
        } else {
            return (
                <SuggestionsInput
                    value={displayValue}
                    suggestionField={selectorMetadata.suggestionField || 'name'}
                    inputPlaceholder={placeholder || selectorMetadata.placeholder}
                    inputProps={{
                        startAdornment: (
                            <InputAdornment
                                style={{ pointerEvents: 'none' }}
                                position="start"
                            >
                                <FontAwesomeIcon
                                    icon={faPlusCircle}
                                    color={theme.palette.primary.main}
                                />
                            </InputAdornment>
                        ),
                    }}
                    focused={isFocused}
                    onDataLoad={selectorMetadata.onDataLoad}
                    onSelect={onItemSelected}
                    selected={transformedSelected}
                    multiselect={multiselect}
                    setInitialValue={otherProps.setInitialValue}
                    defaultExpressionProperty={selectorMetadata?.defaultExpressionProperty}
                    readOnly={readOnly}
                />
            );
        }
    }, [
        containerWidth,
        displayValue,
        isFocused,
        multiselect,
        onItemSelected,
        otherProps.createMessage,
        otherProps.setInitialValue,
        placeholder,
        readOnly,
        selectorMetadata,
        theme.palette.primary.main,
        type,
        transformedSelected,
        onDataLoad,
        lookupDisabled,
        onDataClear,
        inputProps,
    ]);

    return (
        <>
            <Grid
                container
                spacing="10px"
                ref={containerRef}
            >
                <Grid
                    item
                    flex={1}
                    overflow="hidden"
                >
                    {selected?.length > 0 && selectedInputOverride ? selectedInputOverride : renderLookup}
                </Grid>
                {showSelectButton && !readOnly && (
                    <Grid
                        item
                        flex="0 0 auto"
                    >
                        <Button
                            {...ButtonStyles.inputButton}
                            disabled={disabled}
                            onClick={openSelector}
                            variant="outlined"
                            dataTestId="button-select"
                        >
                            {strings('common:button.select')}
                        </Button>
                    </Grid>
                )}
                {!readOnly && onCreateNew && (
                    <Grid item>
                        <Button
                            {...ButtonStyles.inputButton}
                            disabled={disabled || readOnly}
                            onClick={onCreateNew}
                            variant="outlined"
                            startIcon={<Add />}
                            dataTestId="button-new"
                        >
                            {strings('common:button.new')}
                        </Button>
                    </Grid>
                )}
            </Grid>
            {selectorVisible && (
                <Selector<T>
                    visible={true}
                    onClose={closeSelector}
                    onSubmit={submit}
                    onCreateNew={handleCreateNewInSelector}
                    columnDefinition={selectorMetadata.columnDefinition}
                    title={multiselect ? selectorMetadata.titleMultiple || selectorMetadata.title : selectorMetadata.title}
                    type={type}
                    multiselect={multiselect}
                    selected={selected}
                    filterData={otherProps.filterData}
                    onDataClear={onDataClear}
                />
            )}
        </>
    );
};

export default InputSelector;
