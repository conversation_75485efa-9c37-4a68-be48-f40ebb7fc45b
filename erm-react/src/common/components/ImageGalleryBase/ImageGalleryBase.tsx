import React, { FC, MouseEventHandler } from 'react';
import { faAngleLeft, faAngleRight, faExpandArrowsAlt } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';
import { useTheme } from '@mui/material/styles';
import classNames from 'classnames';
import ReactImageGallery, { ReactImageGalleryItem } from 'react-image-gallery';
import ImageGallery, { ReactImageGalleryProps } from 'react-image-gallery';

export const GalleryButton = styled(IconButton)(({ theme }) => ({
    boxShadow: 'none',
    padding: '3px !important',
    '&:active': {
        '& > *:first-of-type': {
            color: theme.palette.primary.main,
        },
    },
    '& .image-gallery-icon': {
        filter: 'none',
    },
}));

export const ImageGalleryPreview = styled('div')({
    marginBottom: '24px',
    '& > img': {
        objectFit: 'contain',
        width: '85%',
        height: '40vh',
        margin: '0px 8px',
    },
});

const GalleryFullscreen = styled('div')({
    height: 'calc(95vh - 15px)',
    width: 'calc(100% - 100px)',
    '& > div:nth-child(odd)': {
        height: 'calc(100% - 10vh)',
        width: '100vw',
        '& > img': {
            width: 'calc(100% - 74px)',
            height: '100%',
            objectFit: 'contain',
            padding: '10px',
        },
    },
});

const GalleryFullscreenDescription = styled('div')({
    fontFamily: '"Open Sans", "Helvetica Neue", helvetica, arial, verdana, sans-serif;',
    width: 'calc(100% - 100px)',
    lineHeight: 1,
    textAlign: 'left',
    whiteSpace: 'break-spaces',
    '& > p': {
        padding: '0.5rem',
        fontSize: '1rem',
        color: 'white',
    },
    '& > p:nth-child(odd)': {
        fontWeight: 'bold',
    },
});

interface ImageGalleryBaseProps extends ReactImageGalleryProps {
    isGalleryFullScreen?: boolean;
}

interface ReactImageGalleryItemProps extends ReactImageGalleryItem {
    fileName: string;
}

const ImageGalleryBase = React.forwardRef<ReactImageGallery, ImageGalleryBaseProps>((props, ref) => {
    const theme = useTheme();

    const RenderLeftNav = (onClick, disabled) => {
        return (
            <GalleryButton
                type="button"
                className={classNames('image-gallery-icon image-gallery-left-nav')}
                disabled={disabled}
                onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onClick(e);
                }}
                aria-label="Previous Slide"
                color={'secondary'}
                sx={{
                    '&:active': {
                        '& > *:first-of-type': {
                            color: theme.palette.primary.main,
                        },
                    },
                    '& .image-gallery-icon': {
                        filter: 'none',
                    },
                }}
            >
                <FontAwesomeIcon
                    icon={faAngleLeft}
                    size="3x"
                    color={theme.palette.primary.main}
                />
            </GalleryButton>
        );
    };

    const RenderRightNav: FC = (onClick: (e) => void, disabled) => {
        return (
            <GalleryButton
                type="button"
                className={'image-gallery-icon image-gallery-right-nav'}
                disabled={disabled}
                onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onClick(e);
                }}
                aria-label="Next Slide"
                color={'secondary'}
                sx={{
                    '&:active': {
                        '& > *:first-of-type': {
                            color: theme.palette.primary.main,
                        },
                    },
                }}
            >
                <FontAwesomeIcon
                    icon={faAngleRight}
                    size="3x"
                    color={theme.palette.primary.main}
                />
            </GalleryButton>
        );
    };

    const RenderGalleryItem: FC = (item: ReactImageGalleryItemProps) => {
        return props.isGalleryFullScreen ? (
            <GalleryFullscreen>
                <div>
                    <img
                        src={item.original}
                        alt={item.thumbnail}
                    />
                </div>
                <GalleryFullscreenDescription>
                    <p>{item.fileName}</p>
                    <p>{item.description}</p>
                </GalleryFullscreenDescription>
            </GalleryFullscreen>
        ) : (
            <ImageGalleryPreview>
                <img
                    title="Screen Gallery"
                    src={item.original}
                    alt={item.thumbnail}
                />
            </ImageGalleryPreview>
        );
    };

    const RenderFullscreenButton: FC = (onClick: MouseEventHandler, isFullscreen) => {
        return (
            <GalleryButton
                tabIndex={0}
                type="button"
                className={'image-gallery-icon image-gallery-fullscreen-button'}
                onClick={onClick}
                aria-label="Fullscreen"
                color={'secondary'}
                sx={{
                    padding: '3px',
                    boxShadow: 'none',
                    bottom: isFullscreen ? '0px' : 'unset',
                    top: isFullscreen ? 'unset' : '0px',
                    '&:active': {
                        '& > *:first-of-type': {
                            color: theme.palette.primary.main,
                        },
                    },
                }}
            >
                <FontAwesomeIcon
                    icon={faExpandArrowsAlt}
                    size="2x"
                    color={theme.palette.primary.main}
                />
            </GalleryButton>
        );
    };

    return (
        <ImageGallery
            ref={ref}
            renderLeftNav={RenderLeftNav}
            renderRightNav={RenderRightNav}
            renderItem={RenderGalleryItem}
            renderFullscreenButton={RenderFullscreenButton}
            useBrowserFullscreen={false}
            {...props}
        />
    );
});

ImageGalleryBase.displayName = 'ImageGalleryBase';

export default ImageGalleryBase;
