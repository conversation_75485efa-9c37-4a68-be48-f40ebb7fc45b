import React from 'react';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { FieldIndicatorType } from 'app/types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationCircle } from '@fortawesome/pro-solid-svg-icons';

interface Props {
    label?: string;
    indicatorType?: FieldIndicatorType;
    message?: string;
    children?: React.ReactNode;
    fullWidth?: boolean;
}

const StyledBox = styled(Box)(() => ({
    marginTop: '4px',
    float: 'none',
    font: '11px Open Sans, Helvetica Neue, helvetica, arial, verdana, sans-serif',
    wordWrap: 'break-word',
}));

const StyledContainer = styled(Container, {
    shouldForwardProp: (prop) => prop !== 'fullWidth',
})<{ fullWidth: boolean }>(({ fullWidth }) => ({
    width: '100%',
    ...(fullWidth && { maxWidth: '100% !important' }),
}));

/**
 * todo to be removed, use FormField from ui-library
 * @deprecated
 */
const FieldWrapper: React.FC<Props> = (props: Props) => {
    const { label, indicatorType = FieldIndicatorType.EMPTY, message, children, fullWidth = false } = props;

    return (
        <StyledContainer
            fullWidth={fullWidth}
            disableGutters
            data-testid={`field-wrapper-${label}`}
        >
            <Typography
                variant="body3"
                color="protechtGrey.darkBlack"
                sx={{ marginBottom: 1 }}
            >
                {label}
            </Typography>
            {children}
            {indicatorType === FieldIndicatorType.ERROR && (
                <StyledBox
                    sx={{ color: 'error.main' }}
                    component={'span'}
                >
                    <FontAwesomeIcon icon={faExclamationCircle} />
                    {message && ` ${message}`}
                </StyledBox>
            )}
            {indicatorType === FieldIndicatorType.INFO && (
                <StyledBox
                    sx={{ color: 'primary.main' }}
                    component={'span'}
                >
                    <FontAwesomeIcon icon={faExclamationCircle} />
                    {message && ` ${message}`}
                </StyledBox>
            )}
        </StyledContainer>
    );
};

export default FieldWrapper;
