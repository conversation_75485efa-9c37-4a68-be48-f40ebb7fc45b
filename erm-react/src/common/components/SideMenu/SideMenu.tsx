import React, { Key, PropsWithChildren, ReactNode, Ref, useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import ListSubheader from '@mui/material/ListSubheader';
import Typography from '@mui/material/Typography';
import { ArrowNavigation } from 'common/hooks/arrowNavigationTyped';
import { useLocation, useNavigate } from 'react-router';
import {
    BasicMenuItem,
    CustomMenuSection,
    ExpandableMenuItem,
    MenuTitle,
    NavigationMenuItem,
    NavigationMenuItemType,
    ScrollableMenuSection,
} from 'common/types';
import { styled, useTheme } from '@mui/material/styles';
import SideMenuNode from './SideMenuNode';
import Loading from '../Loading';
import { LoaderType } from '../Loading/Loading';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import InfoBubble from '@protecht/ui-library/library/components/InfoBubble';

const hasPathnameChanged = (pathname: string, search, item?: NavigationMenuItem, ignoreSearch?: boolean) => {
    if (!item) {
        return false;
    }
    if (item.type !== NavigationMenuItemType.ITEM && !(item as BasicMenuItem).pathname && item.id) {
        return !pathname.includes(item.id.toString());
    }
    if (item) {
        if (ignoreSearch) {
            return `${(item as BasicMenuItem).pathname}${search}` !== pathname;
        } else {
            return (item as BasicMenuItem).pathname !== pathname;
        }
    }
    return false;
};

export enum SideMenuType {
    DEFAULT = 'DEFAULT',
    CHECKBOX = 'CHECKBOX',
    RADIO = 'RADIO',
}

export type Props = PropsWithChildren<{
    menuType?: SideMenuType;
    title?: string;
    titleIcon?: IconProp;
    titleIconTooltipText?: string;
    titleAction?: ReactNode;
    items: NavigationMenuItem[];
    emptyItemsMsg?: ReactNode;
    loading?: boolean;
    dataTestId?: string;
    keepSelectedItemHighlighted?: boolean;
    preselectByPathname?: boolean;
    preselectedItems?: NavigationMenuItem[] | NavigationMenuItem;
    listOfIgnoredSearches?: string[];
}>;

export type MappedNavigationMenuItem = NavigationMenuItem & {
    parent?: Key | null;
    expanded?: boolean;
    nestedItems?: MappedNavigationMenuItem[];
};

export type NavigationMenuNode = {
    item: NavigationMenuItem;
    parent?: Key | null;
    expanded?: boolean;
};

const StyledList = styled(List)(({ theme }) => ({
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    padding: 0,
    '& .MuiList-subheader': {
        paddingBottom: theme.spacing(2),
    },
    '& .MuiListSubheader-root': {
        lineHeight: '18px',
        paddingBottom: '12px',
    },
}));
const StyledListSubHeader = ({
    title,
    titleAction,
    titleIcon,
    titleIconTooltipText,
}: {
    title?: string | ReactNode;
    titleAction?: ReactNode;
    titleIcon?: IconProp;
    titleIconTooltipText?: string;
}) => {
    return (
        <ListSubheader
            disableGutters
            sx={{
                paddingTop: '9px !important',
                paddingBottom: '14px !important',
            }}
        >
            <Typography
                variant="h6a"
                color="text.primary"
                sx={{
                    fontSize: '15px',
                    lineHeight: '19px !important',
                }}
            >
                {title}
                {titleIcon && (
                    <InfoBubble
                        trigger={'click'}
                        dataTestId={titleIconTooltipText}
                        content={<>{titleIconTooltipText}</>}
                    ></InfoBubble>
                )}
            </Typography>
            {titleAction}
        </ListSubheader>
    );
};

const SideMenu: React.FC<Props> = ({
    title,
    titleAction,
    titleIcon,
    titleIconTooltipText,
    items = [],
    menuType = SideMenuType.DEFAULT,
    loading,
    dataTestId = 'side-menu',
    keepSelectedItemHighlighted = true,
    preselectByPathname = true,
    preselectedItems,
    emptyItemsMsg,
    listOfIgnoredSearches,
}) => {
    const theme = useTheme();
    const navigate = useNavigate();
    const { pathname, search } = useLocation();
    const [selected, setSelected] = useState<NavigationMenuNode[]>([]);

    const fullPath = useMemo(() => {
        return `${pathname}${search}`;
    }, [pathname, search]);

    const [menuNodes, setMenuNodes] = useState<Map<Key, NavigationMenuNode>>(new Map());
    const [containsExpandableNodes, setContainsExpandableNodes] = useState<boolean>(false);

    const shouldIgnoreSearch = useMemo(() => listOfIgnoredSearches?.some((s) => search.includes(s)), [listOfIgnoredSearches, search]);

    const findItemByPathname = useCallback(
        (items: NavigationMenuNode[], pathname: string, fullPath: string, ignoreSearch?: boolean): NavigationMenuNode | undefined => {
            for (const node of items.values()) {
                const { item } = node;
                const itemPathname = 'pathname' in item && item.pathname ? item.pathname : undefined;

                if (
                    (!ignoreSearch && itemPathname === fullPath) ||
                    (ignoreSearch && itemPathname === pathname) ||
                    (item.id && pathname.includes(item.id.toString()))
                ) {
                    return node;
                }
            }
            return undefined;
        },
        [],
    );

    const getMenuNodeStructure = useCallback(
        (map: Map<Key, NavigationMenuNode>, items: NavigationMenuItem[], parentKey: Key | null = null) => {
            const itemNodes = Array.from(menuNodes.values());
            const currItemFromPath = findItemByPathname(itemNodes, pathname, fullPath, shouldIgnoreSearch);
            const expandedNodes = getExpandedNodes(menuNodes);

            items.forEach((item: NavigationMenuItem) => {
                map.set(item.key, {
                    item,
                    parent: parentKey,
                    expanded: currItemFromPath?.parent === item.key || expandedNodes.has(item.key),
                });
                if (item.type === NavigationMenuItemType.EXPANDABLE_ITEM || item.type === NavigationMenuItemType.SCROLLABLE_SECTION) {
                    getMenuNodeStructure(map, (item as ExpandableMenuItem).nestedItems, item.key);
                }
            });
        },
        [menuNodes, pathname, fullPath, findItemByPathname, shouldIgnoreSearch],
    );

    useEffect(() => {
        setContainsExpandableNodes(items.some((item) => item.type === NavigationMenuItemType.EXPANDABLE_ITEM));
        const menuNodeStructure = new Map<Key, any>();
        getMenuNodeStructure(menuNodeStructure, items, null);
        setMenuNodes(menuNodeStructure);
        // getMenuNodeStructure - causes infinite re-render
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [items]);

    /**
     * Preselect array or single item
     */
    useEffect(() => {
        if (Array.isArray(preselectedItems) && preselectedItems.length) {
            setSelected(preselectedItems.map((preselectedItem) => ({ item: preselectedItem })));
        } else if (!Array.isArray(preselectedItems) && preselectedItems) {
            setSelected([{ item: preselectedItems }]);
        }
    }, [preselectedItems]);

    const checkForParentExpandableNodes = useCallback(
        (node: NavigationMenuNode, expandableParentNodes: Set<Key> = new Set()): Set<Key> => {
            if (node.parent) {
                expandableParentNodes.add(node.parent);
                const parentMenuItem = menuNodes.get(node.parent);
                if (parentMenuItem) {
                    checkForParentExpandableNodes(parentMenuItem, expandableParentNodes);
                }
            }

            return expandableParentNodes;
        },
        [menuNodes],
    );

    /**
     * Select option on refresh or pathname change
     */
    useLayoutEffect(() => {
        if (menuNodes.size && preselectByPathname && (!selected.length || hasPathnameChanged(fullPath, search, selected[0].item, shouldIgnoreSearch))) {
            const itemNodes = Array.from(menuNodes.values());
            const itemToSelect = findItemByPathname(itemNodes, pathname, fullPath, shouldIgnoreSearch);
            if (itemToSelect) {
                setSelected([itemToSelect]);
                const parentNodes = checkForParentExpandableNodes(itemToSelect);
                if (parentNodes.size) {
                    setMenuNodes((previousNodes: Map<Key, NavigationMenuNode>) => {
                        const newNodes = new Map(previousNodes);
                        for (const key of newNodes.keys()) {
                            newNodes.set(key, { ...previousNodes.get(key)!, expanded: parentNodes.has(key) });
                        }
                        return newNodes;
                    });
                }
            } else if (selected.length && hasPathnameChanged(fullPath, search, selected[0].item)) {
                setSelected([]);
            }
        }
    }, [pathname, fullPath, menuNodes, preselectByPathname, findItemByPathname, checkForParentExpandableNodes, selected, shouldIgnoreSearch, search]);

    const getExpandedNodes = (menuNodes: Map<Key, NavigationMenuNode>): Set<Key> => {
        const expandedNodes: Set<Key> = new Set();

        for (const key of menuNodes.keys()) {
            const node = menuNodes.get(key)!;

            if (node.item.type === NavigationMenuItemType.EXPANDABLE_ITEM && node.expanded) {
                expandedNodes.add(node.item.key);
            }
        }

        return expandedNodes;
    };

    const handleExpandedNodes = useCallback(
        (node: NavigationMenuNode) => {
            if (containsExpandableNodes) {
                let keepExpanded: Set<Key> = getExpandedNodes(menuNodes);

                if (node.parent) {
                    keepExpanded = new Set([...keepExpanded, ...checkForParentExpandableNodes(node)]);
                }

                setMenuNodes((previousNodes: Map<Key, NavigationMenuNode>) => {
                    const newNodes = new Map(previousNodes);

                    if (node.item.type === NavigationMenuItemType.EXPANDABLE_ITEM && !keepExpanded.has(node.item.key)) {
                        keepExpanded.add(node.item.key);
                    } else {
                        // keepExpanded = keepExpanded.filter((expandedNodeKey) => expandedNodeKey !== node.item.key);
                        keepExpanded.delete(node.item.key);
                    }

                    for (const key of newNodes.keys()) {
                        newNodes.set(key, { ...previousNodes.get(key)!, expanded: keepExpanded.has(key) });
                    }
                    return newNodes;
                });
            }
        },
        [menuNodes, checkForParentExpandableNodes, containsExpandableNodes],
    );

    const handleSelect = useCallback(
        async (node: NavigationMenuNode) => {
            handleExpandedNodes(node);
            if (node.item.type === NavigationMenuItemType.EXPANDABLE_ITEM) {
                return;
            }

            if ('onSelect' in node.item) {
                const retainSelection = await node.item.onSelect?.(node.item);
                if (retainSelection) {
                    return;
                }
            }

            if ('pathname' in node.item && node.item.pathname && node.item.pathname !== fullPath) {
                void navigate(node.item.pathname);
            }

            if (menuType !== SideMenuType.CHECKBOX) {
                setSelected([node]);
            } else {
                setSelected((previousSelected) => {
                    const found = previousSelected.find((i) => i.item.key === node.item.key);
                    return found ? previousSelected.filter((i) => i.item.key !== found.item.key) : [...previousSelected, node];
                });
            }
        },
        [handleExpandedNodes, menuType, navigate, fullPath],
    );

    const isSelected = useCallback(({ item: { key } }: NavigationMenuNode): boolean => selected.some((i) => i.item.key === key), [selected]);

    const isExpanded = useCallback(({ item: { key } }: NavigationMenuNode): boolean => Boolean(menuNodes.get(key)?.expanded), [menuNodes]);

    const getRef = useCallback(
        (items: NavigationMenuItem[] | undefined, index: number, intersectionObserver: Ref<HTMLDivElement>) =>
            items?.length === index + 1 ? intersectionObserver : null,
        [],
    );

    const renderMenuItem = useCallback(
        (node: NavigationMenuNode, index: number, ref?: Ref<HTMLDivElement>) => {
            let nextIndex = index + 1;
            switch (node.item.type) {
                case NavigationMenuItemType.NESTED_TITLE: {
                    return {
                        node: (
                            <StyledListSubHeader
                                title={(node.item as MenuTitle).label}
                                key={node.item.key}
                            />
                        ),
                        nextIndex: index,
                    };
                }
                case NavigationMenuItemType.DIVIDER: {
                    return {
                        node: (
                            <StyledDivider
                                key={node.item.key}
                                sx={{
                                    borderColor: theme.palette.protechtGrey?.grey_220,
                                    margin: '12px',
                                }}
                            />
                        ),
                        nextIndex: index,
                    };
                }
                case NavigationMenuItemType.EXPANDABLE_ITEM: {
                    return {
                        node: (
                            <Box key={`${node.item.key}_${index}`}>
                                <SideMenuNode
                                    node={node}
                                    index={index}
                                    selected={isSelected(node)}
                                    expanded={isExpanded(node)}
                                    keepSelectedItemHighlighted={keepSelectedItemHighlighted}
                                    menuType={menuType}
                                    onSelect={handleSelect}
                                >
                                    {(node.item as ExpandableMenuItem).nestedItems?.map((nestedItem: NavigationMenuItem) => {
                                        const result = renderMenuItem(menuNodes.get(nestedItem.key)!, nextIndex);
                                        nextIndex = result.nextIndex;
                                        return result.node;
                                    })}
                                </SideMenuNode>
                            </Box>
                        ),
                        nextIndex,
                    };
                }
                case NavigationMenuItemType.SCROLLABLE_SECTION: {
                    const item = node.item as ScrollableMenuSection;
                    return {
                        node: (
                            <Box
                                key={`${item.key}_${index}`}
                                sx={
                                    item.customStyling || {
                                        overflow: 'auto',
                                        height: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                    }
                                }
                            >
                                <Box
                                    sx={{ height: '100%', overflow: 'auto' }}
                                    ref={item.containerRef}
                                >
                                    {item.nestedItems?.map((nestedItem: NavigationMenuItem, childIndex) => {
                                        const result = renderMenuItem(
                                            menuNodes.get(nestedItem.key)!,
                                            nextIndex,
                                            getRef(item.nestedItems, childIndex, item.intersectionObserver as Ref<HTMLDivElement>),
                                        );
                                        nextIndex = result.nextIndex;
                                        return result.node;
                                    })}
                                    {item.isSectionLoading && (
                                        <div>
                                            <Loading
                                                loaderType={LoaderType.Slim}
                                                inline
                                            />
                                        </div>
                                    )}
                                </Box>
                            </Box>
                        ),
                        nextIndex,
                    };
                }
                case NavigationMenuItemType.CUSTOM_SECTION: {
                    const item = node.item as CustomMenuSection;
                    return {
                        node: <React.Fragment key={item.key}>{item.content}</React.Fragment>,
                        nextIndex: index,
                    };
                }
                case NavigationMenuItemType.ITEM:
                default: {
                    return {
                        node: (
                            <Box
                                key={`${node.item.key}_${index}`}
                                ref={ref}
                            >
                                <SideMenuNode
                                    node={node}
                                    index={index}
                                    selected={isSelected(node)}
                                    keepSelectedItemHighlighted={keepSelectedItemHighlighted}
                                    menuType={menuType}
                                    onSelect={handleSelect}
                                />
                            </Box>
                        ),
                        nextIndex,
                    };
                }
            }
        },
        [getRef, handleSelect, isExpanded, isSelected, keepSelectedItemHighlighted, menuNodes, menuType],
    );

    const renderMenu = (menu: NavigationMenuNode[]) => {
        let curIndex = 0;
        const res: ReactNode[] = [];
        for (const item of menu) {
            const { node, nextIndex } = renderMenuItem(item, curIndex);
            res.push(node);
            curIndex = nextIndex;
        }
        return res;
    };

    const findInitialIndex = useCallback(
        () =>
            Array.from(menuNodes.values()).findIndex((i) => {
                return 'disabled' in i.item && !i.item.disabled && !i.item.ignoreArrowNavigation;
            }),
        [menuNodes],
    );

    return (
        <StyledList
            subheader={
                title && (
                    <StyledListSubHeader
                        title={title}
                        titleAction={titleAction}
                        titleIcon={titleIcon}
                        titleIconTooltipText={titleIconTooltipText}
                    />
                )
            }
            data-testid={dataTestId}
        >
            {!loading && (
                <ArrowNavigation
                    reInitOnDeactivate
                    initialIndex={[0, findInitialIndex()]}
                    focuscolor={theme.palette.primary.main}
                >
                    {menuNodes.size ? renderMenu(Array.from(menuNodes.values()).filter((node) => node.parent === null)) : emptyItemsMsg}
                </ArrowNavigation>
            )}
        </StyledList>
    );
};

export default SideMenu;
