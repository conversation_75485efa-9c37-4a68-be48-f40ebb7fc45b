import { StoryFn, Meta } from '@storybook/react';
import React from 'react';
import SideMenu, { SideMenuType } from './SideMenu';
import { BrowserRouter } from 'react-router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClock } from '@fortawesome/pro-solid-svg-icons';
import { NavigationMenuItemType } from 'common/types';
import Box from '@mui/material/Box';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

export default {
    title: 'components/UI/Side Menu',
    component: SideMenu,
    decorators: [
        (Story) => (
            <BrowserRouter>
                <Story />
            </BrowserRouter>
        ),
    ],
} as Meta<typeof SideMenu>;

const Template: StoryFn<typeof SideMenu> = (args) => (
    <Box sx={{ width: '300px' }}>
        <SideMenu {...args} />
    </Box>
);

const mockFunction = () => {
    // eslint-disable-next-line no-console
    console.log('pressed');
};

export const DefaultMenu: StoryFn<typeof SideMenu> = Template.bind({});
DefaultMenu.args = {
    title: 'Example side menu with plain text as menu items',
    menuType: SideMenuType.DEFAULT,
    items: [
        {
            key: '1',
            label: 'first item',
            onSelect: mockFunction,
        },
        {
            key: '2',
            label: 'second item',
            onSelect: mockFunction,
        },
        {
            key: '3',
            label: 'third item',
            onSelect: mockFunction,
        },
    ],
};

export const WithIcons: StoryFn<typeof SideMenu> = Template.bind({});
WithIcons.args = {
    ...DefaultMenu.args,
    title: 'Example side menu with icons',
    items: [
        {
            key: '1',
            label: 'first item',
            onSelect: mockFunction,
            icon: <FontAwesomeIcon icon={faClock} />,
        },
        {
            key: '2',
            label: 'second item',
            onSelect: mockFunction,
            icon: (
                <Add
                    width={20}
                    height={20}
                />
            ),
        },
        {
            key: '3',
            label: 'third item',
            onSelect: mockFunction,
        },
    ],
};

export const WithDivider: StoryFn<typeof SideMenu> = Template.bind({});
WithDivider.args = {
    ...DefaultMenu.args,
    title: 'Example side menu with divider',
    items: [
        {
            key: '1',
            label: 'first item',
            onSelect: mockFunction,
            icon: <FontAwesomeIcon icon={faClock} />,
        },
        {
            key: '2',
            label: 'second item',
            onSelect: mockFunction,
            icon: (
                <Add
                    width={20}
                    height={20}
                />
            ),
        },
        {
            key: '3',
            label: 'third item',
            onSelect: mockFunction,
        },
        {
            key: '4',
            type: NavigationMenuItemType.DIVIDER,
        },
        {
            key: '5',
            label: 'other section item',
            onSelect: mockFunction,
        },
    ],
};

export const WithNestedItems: StoryFn<typeof SideMenu> = Template.bind({});
WithNestedItems.args = {
    ...DefaultMenu.args,
    title: 'Example side menu with nested sections',
    items: [
        {
            key: '1',
            label: 'first item',
            onSelect: mockFunction,
            icon: <FontAwesomeIcon icon={faClock} />,
        },
        {
            key: '2',
            label: 'second item',
            onSelect: mockFunction,
            icon: (
                <Add
                    width={20}
                    height={20}
                />
            ),
        },
        {
            key: '3',
            label: 'third item',
            type: NavigationMenuItemType.EXPANDABLE_ITEM,
            nestedItems: [
                {
                    key: '3-1',
                    label: 'nested section item',
                    onSelect: mockFunction,
                    level: 1,
                },
                {
                    key: '3-2',
                    label: 'another nested section item',
                    onSelect: mockFunction,
                    level: 1,
                    type: NavigationMenuItemType.EXPANDABLE_ITEM,
                    nestedItems: [
                        {
                            key: '3-2-1',
                            label: 'further nesting',
                            onSelect: mockFunction,
                            level: 2,
                        },
                    ],
                },
            ],
        },
        {
            key: '4',
            type: NavigationMenuItemType.DIVIDER,
        },
        {
            key: '5',
            label: 'other section item',
            onSelect: mockFunction,
        },
        {
            key: '6',
            label: 'sixth item',
            type: NavigationMenuItemType.EXPANDABLE_ITEM,
            nestedItems: [
                {
                    key: '6-1',
                    label: 'nested section item',
                    onSelect: mockFunction,
                    level: 1,
                },
                {
                    key: '6-2',
                    label: 'another nested section item',
                    onSelect: mockFunction,
                    level: 1,
                    type: NavigationMenuItemType.EXPANDABLE_ITEM,
                    nestedItems: [
                        {
                            key: '6-2-1',
                            label: 'further nesting',
                            onSelect: mockFunction,
                            level: 2,
                        },
                    ],
                },
            ],
        },
    ],
};

export const WithCheckboxes: StoryFn<typeof SideMenu> = Template.bind({});
WithCheckboxes.args = {
    ...DefaultMenu.args,
    title: 'Example side menu with checkboxes',
    menuType: SideMenuType.CHECKBOX,
};

export const WithRadioButtons: StoryFn<typeof SideMenu> = Template.bind({});
WithRadioButtons.args = {
    ...WithCheckboxes.args,
    menuType: SideMenuType.RADIO,
};
