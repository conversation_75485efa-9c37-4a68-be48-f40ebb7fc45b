import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useArrowNavigationWithFocusState } from 'common/hooks/arrowNavigationTyped';
import { alpha, styled } from '@mui/material/styles';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Radio from '@mui/material/Radio';
import ListItemButton from '@mui/material/ListItemButton';
import Collapse from '@mui/material/Collapse';
import { NavigationMenuNode, SideMenuType } from './SideMenu';
import { BasicMenuItem, ExpandableMenuItem } from 'common/types';
import Checkbox from '@protecht/ui-library/library/components/Checkbox';
import { ChevronDown, ChevronUp } from '@protecht/ui-library/library/components/SVGIcons';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { useIsOverflowing } from '@protecht/ui-library/library/hooks/useIsOverflowing';

interface Props {
    index: number;
    node: NavigationMenuNode;
    selected?: boolean;
    expanded?: boolean;
    onSelect: (item: NavigationMenuNode) => void;
    menuType?: SideMenuType;
    keepSelectedItemHighlighted?: boolean;
    children?: any;
    keepHoveredItemTransparent?: boolean;
    color?: string;
}

const StyledListItemButton = styled(ListItemButton, {
    shouldForwardProp: (prop) => prop !== 'node' && prop !== 'menuType' && prop !== 'keepSelectedItemHighlighted' && prop !== 'keepHoveredItemTransparent',
})<{
    node: NavigationMenuNode;
    menuType: SideMenuType;
    selected: boolean;
    keepSelectedItemHighlighted: boolean;
    keepHoveredItemTransparent: boolean;
}>(({ theme, node, menuType, selected, keepSelectedItemHighlighted, keepHoveredItemTransparent }) => {
    const icon = 'icon' in node.item && node.item.icon;
    const level = 'level' in node.item && node.item.level;
    return {
        height: '38px',
        padding: '8.5px 8px 8.5px 16px',
        ...(level && { paddingLeft: theme.spacing(2 + 4 * level) }),
        alignItems: 'center',
        color: theme.palette.text.primary,
        borderColor: 'transparent',
        borderLeft: !icon && menuType === SideMenuType.DEFAULT ? '4px solid transparent' : undefined,
        ...((menuType === SideMenuType.CHECKBOX || !keepSelectedItemHighlighted) && { backgroundColor: 'transparent' }),
        '& .MuiListItemText-root': {
            margin: 0,
            flex: 1,
        },
        '& .MuiRadio-root, & .MuiCheckbox-root': {
            padding: 0,
            ...(!selected && {
                color: theme.palette.protechtGrey?.grey_128,
            }),
        },
        '& .MuiTypography-body2': {
            fontWeight: selected ? '600' : '400',
            fontSize: '15px',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            lineHeight: '21px',
        },
        '& .MuiListItemIcon-root': {
            marginTop: 0,
            minWidth: '24px',
            marginRight: '8px',
            justifyContent: 'center',
            fontSize: '16px',
            ...(selected && {
                color: theme.palette.primary.main,
            }),
        },
        ...(selected && {
            backgroundColor: alpha(theme.palette.primary.main, 0.08) + ' !important',
            borderColor: !icon && menuType === SideMenuType.DEFAULT ? theme.palette.primary.main : undefined,
            borderLeft: !icon && menuType === SideMenuType.DEFAULT ? `4px solid ${theme.palette.primary.main}` : undefined,
            '&:focus': {
                backgroundImage: 'none',
            },
        }),
        ...(keepHoveredItemTransparent && {
            backgroundColor: 'white !important',
            '&:focus': {
                backgroundColor: 'white !important',
                backgroundImage: 'none !important',
            },
        }),

        ...(!selected && {
            '&:hover': {
                backgroundColor: 'transparent',
                backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.06) 0 0)',
                '& .MuiCheckbox-root': {
                    color: theme.palette.primary.main,
                    strokeWidth: '2px',
                    stroke: theme.palette.primary.main,
                },
                '& .MuiRadio-root': {
                    color: theme.palette.primary.main,
                    strokeWidth: '2px',
                    stroke: theme.palette.primary.main,
                },
            },
        }),

        ...(selected && {
            '&:hover': {
                backgroundImage: 'none',
                strokeWidth: 0,
                '& .MuiCheckbox-root': {
                    color: theme.palette.protechtGrey?.black,
                },
            },
        }),
    };
});

const SideMenuNode: React.FC<Props> = (props: Props) => {
    const {
        index,
        node,
        menuType = SideMenuType.DEFAULT,
        keepSelectedItemHighlighted = true,
        selected = true,
        onSelect,
        expanded,
        keepHoveredItemTransparent = false,
    } = props;

    const item = node.item as BasicMenuItem | ExpandableMenuItem;
    const icon = 'icon' in node.item ? node.item.icon : undefined;
    const pathname = 'pathname' in node.item ? node.item.pathname : undefined;
    const nestedItems = 'nestedItems' in node.item ? node.item.nestedItems : undefined;

    const {
        active,
        selected: selectedArrowNavigation,
        setFocusFromOutside,
        focusProps: { ref, tabIndex, onClick },
    } = useArrowNavigationWithFocusState(0, index);

    const [tabIndexItem, setTabIndexItem] = useState(tabIndex);
    const labelRef = useRef<HTMLDivElement>(null);
    const isOverflowing = useIsOverflowing(labelRef);

    useEffect(() => {
        setTabIndexItem(tabIndex);
        if (selectedArrowNavigation && active) {
            ref?.current?.focus();
        }
    }, [selectedArrowNavigation, active]);

    const onFocus = useCallback(() => {
        setFocusFromOutside(0, index);
    }, [index, setFocusFromOutside]);

    const renderIcon = () => {
        if (!icon && menuType === SideMenuType.DEFAULT) {
            return <div />;
        }

        return (
            <ListItemIcon>
                {icon ||
                    (menuType === SideMenuType.CHECKBOX ? (
                        <Checkbox
                            key={item.key?.toString()}
                            checked={selected}
                        />
                    ) : (
                        <Radio
                            size="small"
                            checked={selected}
                        />
                    ))}
            </ListItemIcon>
        );
    };

    const itemSelected = useCallback(
        (item: NavigationMenuNode) => {
            onClick();
            onSelect(item);
            onFocus();
        },
        [onSelect, onClick, onFocus],
    );

    return (
        <>
            <StyledListItemButton
                node={node}
                keepSelectedItemHighlighted={keepSelectedItemHighlighted}
                menuType={menuType}
                ref={ref}
                tabIndex={tabIndexItem}
                selected={selected}
                dense
                disableGutters
                onFocus={onFocus}
                onKeyDown={(event) => {
                    if (event.key === 'Enter') {
                        itemSelected(node);
                    }
                }}
                onMouseDown={(event) => {
                    if (event.button === 0) {
                        itemSelected(node);
                    }
                }}
                disabled={item.disabled || (!pathname && !onSelect && !nestedItems?.length)}
                keepHoveredItemTransparent={keepHoveredItemTransparent}
            >
                {renderIcon()}
                <Tooltip
                    title={item.label}
                    disableHoverListener={!isOverflowing}
                    style={{ overflow: 'hidden', width: '100%' }}
                >
                    <ListItemText
                        primary={item.label}
                        data-testid={`sideMenu-item-${item.label}`}
                        primaryTypographyProps={{ ref: labelRef }}
                    />
                </Tooltip>
                {item.secondaryLabel ?? <></>}
                {nestedItems?.length &&
                    (expanded ? (
                        <ChevronUp style={{ color: ProtechtDictionary.accentColor }} />
                    ) : (
                        <ChevronDown style={{ color: ProtechtDictionary.accentColor }} />
                    ))}
            </StyledListItemButton>
            {nestedItems?.length && (
                <Collapse
                    mountOnEnter
                    unmountOnExit
                    in={expanded}
                >
                    {props.children}
                </Collapse>
            )}
        </>
    );
};

export default SideMenuNode;
