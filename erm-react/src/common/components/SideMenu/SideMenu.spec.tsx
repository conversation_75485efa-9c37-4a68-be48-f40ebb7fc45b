import React from 'react';
import { render, screen } from 'test/utils';
import { MemoryRouter } from 'react-router';
import { Props } from './SideMenu';
import { DefaultMenu, WithCheckboxes, WithDivider, WithIcons, WithNestedItems, WithRadioButtons } from './SideMenu.stories';
import { BasicMenuItem } from 'common/types';

describe('SideMenu', () => {
    it('Should display default SideMenu', async () => {
        render(
            <MemoryRouter initialEntries={['/worms/client/app/react/default/rolesAndPermissions/userType/2']}>
                <DefaultMenu
                    {...(DefaultMenu.args as Props)}
                    title="Some title"
                />
                ,
            </MemoryRouter>,
        );

        expect(screen.getByText('Some title')).toBeInTheDocument();
        DefaultMenu.args!.items!.forEach((item) => {
            expect(screen.getByText((item as BasicMenuItem).label as string)).toBeInTheDocument();
        });
    });

    it('Should display SideMenu with icons', async () => {
        render(
            <MemoryRouter initialEntries={['/worms/client/app/react/default/rolesAndPermissions/userType/2']}>
                <WithIcons {...(WithIcons.args as Props)} />,
            </MemoryRouter>,
        );

        const svgEl = screen.getAllByRole('img', { hidden: true })[0];
        expect(svgEl.classList.toString()).toContain('fa-clock');
    });

    it('Should display SideMenu with divider', async () => {
        render(
            <MemoryRouter initialEntries={['/worms/client/app/react/default/rolesAndPermissions/userType/2']}>
                <WithDivider {...(WithDivider.args as Props)} />,
            </MemoryRouter>,
        );

        expect(screen.getByRole('separator')).toBeInTheDocument();
    });

    it('Should display SideMenu with nested items', async () => {
        const { user } = render(
            <MemoryRouter initialEntries={['/worms/client/app/react/default/rolesAndPermissions/userType/2']}>
                <WithNestedItems {...(WithNestedItems.args as Props)} />,
            </MemoryRouter>,
        );

        const listItem = screen.getByText(/third item/i);
        let nestedItem = screen.queryByText(/nested section item/i);

        expect(nestedItem).not.toBeInTheDocument();

        await user.click(listItem);

        nestedItem = (await screen.findAllByText(/nested section item/i))[0];
        const secondNestedItem = screen.getAllByText(/nested section item/i)[1];
        let furtherNestedElement = screen.queryByText(/further nesting/i);

        expect(nestedItem).toBeVisible();
        expect(secondNestedItem).toBeVisible();

        expect(furtherNestedElement).not.toBeInTheDocument();

        await user.click(secondNestedItem);

        furtherNestedElement = (await screen.findAllByText(/further nesting/i))[0];

        expect(furtherNestedElement).toBeVisible();
    });

    it('Should display SideMenu with checkboxes', async () => {
        render(
            <MemoryRouter initialEntries={['/worms/client/app/react/default/rolesAndPermissions/userType/2']}>
                <WithCheckboxes {...(WithCheckboxes.args as Props)} />,
            </MemoryRouter>,
        );

        const checkbox = screen.getAllByRole('checkbox')[0];
        expect(checkbox).toBeInTheDocument();
    });

    it('Should display SideMenu with radio buttons', async () => {
        render(
            <MemoryRouter initialEntries={['/worms/client/app/react/default/rolesAndPermissions/userType/2']}>
                <WithRadioButtons {...(WithRadioButtons.args as Props)} />,
            </MemoryRouter>,
        );

        const radio = screen.getAllByRole('radio')[0];
        expect(radio).toBeInTheDocument();
    });
});
