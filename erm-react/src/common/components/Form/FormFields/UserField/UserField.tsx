import React, { useCallback } from 'react';

import { ColumnType } from '@protecht/ui-library/library/types';

import { ProtechtUserRest, ViewExpressionRest } from 'api/generated/types';
import { IdWithNameAndStatusRest } from 'app/types';
import { ExpressionType } from 'view/types';
import { useLazyGetUsersSearchQuery } from 'user/rtkApi';
import { SEARCH_RESULT_LIMIT } from 'common/components/SuggestionsInput/InputLookup';
import useFormContext from 'common/hooks/forms/useFormContext';
import useSnackbar from 'common/hooks/useSnackbar';
import InputSelectorPickerField, { InputSelectorPickerFieldProps } from 'common/components/Form/FormFields/InputSelectorPickerField/InputSelectorPickerField';
import { UserFilterData } from 'library/components/User/UserLayout';
import { USER_CATEGORY } from 'library/components/User/types';
import { UserColDef } from 'library/components/User/UserDefinitions';

type User = ProtechtUserRest & IdWithNameAndStatusRest;

type UserFieldProps = Omit<InputSelectorPickerFieldProps<User>, 'type' | 'filterData' | 'selectedItems' | 'onDataLoad'> & {
    filterData?: UserFilterData;
};

const UserField = (props: UserFieldProps) => {
    const { name, filterData, preferArrayForSingleSelect = false, onDataClear, ...rest } = props;
    const { enqueueError } = useSnackbar();
    const [triggerSearch] = useLazyGetUsersSearchQuery();
    const { watch, trigger, setSearchError, clearSearchError } = useFormContext();
    const selected = watch(name);

    const handleSearch = useCallback(
        async (query: string) => {
            clearSearchError(name);

            const expressions: ViewExpressionRest[] = [];
            expressions.push({
                value: query,
                expression: ExpressionType.CONTAINS,
                property: 'name',
                type: 'STRING',
            });

            if (filterData?.filter) {
                expressions.push({
                    property: filterData.filter.filteredField,
                    value: filterData.filter.filterValue as string,
                    expression: filterData.filter.isExactMatch ? ExpressionType.EQUAL : ExpressionType.CONTAINS,
                    type: UserColDef.find((col) => col.field === filterData.filter!.filteredField)?.filterType ?? 'STRING',
                });
            }

            filterData?.customFilters?.forEach((filter) => {
                expressions.push(filter);
            });

            if (filterData?.userCategory !== undefined && filterData.userCategory !== USER_CATEGORY.ALL) {
                expressions.push({
                    property: 'categoryVector',
                    value: filterData.userCategory,
                    expression: ExpressionType.EQUAL,
                    type: 'STRING',
                });
            }

            return await triggerSearch({
                userFilterContext: {
                    expressions,
                },
                limit: SEARCH_RESULT_LIMIT,
                roleFilterId: filterData?.roleFilter ? parseInt(filterData.roleFilter) : undefined,
                permissionFilterId: filterData?.permissionFilter ? parseInt(filterData.permissionFilter) : undefined,
            })
                .unwrap()
                .then((res) => {
                    if (Array.isArray(res.records) && res.records.length > 0) {
                        return res.records as User[];
                    } else {
                        setSearchError(name);
                        return [] as User[];
                    }
                })
                .catch((error) => {
                    enqueueError(error.data.message);
                    return [];
                })
                .finally(() => {
                    void trigger(name);
                });
        },
        [name, filterData, trigger, triggerSearch, setSearchError, enqueueError],
    );

    const handleClear = useCallback(() => {
        clearSearchError(name);
        onDataClear?.();
        void trigger(name);
    }, [name, trigger, clearSearchError, onDataClear]);

    return (
        <InputSelectorPickerField<User>
            type={ColumnType.USER}
            name={name}
            selectedItems={selected}
            onDataLoad={handleSearch}
            onDataClear={handleClear}
            filterData={filterData}
            preferArrayForSingleSelect={preferArrayForSingleSelect}
            {...rest}
        />
    );
};

export default UserField;
