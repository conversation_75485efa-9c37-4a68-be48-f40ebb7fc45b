import React from 'react';
import Form<PERSON>ield, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { useTheme } from '@mui/material/styles';

interface IconFieldProps extends FormFieldBaseProps {
    activeColor?: string;
    icon: IconProp;
}

export default function IconField({ activeColor, icon, ...formFieldProps }: IconFieldProps) {
    const theme = useTheme();

    return (
        <FormField
            {...formFieldProps}
            renderField={(field) => (
                <FontAwesomeIcon
                    icon={icon}
                    color={field.value ? activeColor ?? theme.palette.primary.main : theme.palette.protechtGrey?.grey_192}
                    onClick={() => field.onChange(!field.value)}
                ></FontAwesomeIcon>
            )}
        />
    );
}
