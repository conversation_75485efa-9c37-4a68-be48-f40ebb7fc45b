import React, { useCallback } from 'react';
import Grid from '@mui/material/Grid';

import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { ColumnType } from '@protecht/ui-library/library/types';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';

import { strings } from 'common/utils/i18n';
import { usePursGetCurrentUserUsingGetQuery } from 'user/rtkApi';
import LinksListBuilder from 'common/components/LinksListBuilder';

enum ClearOption {
    'NOBODY' = 'nobody',
    'EVERYONE' = 'everyone',
    'SIGNED' = 'signed',
}

type SignOffFieldProps = FormFieldBaseProps & {
    clearOption?: ClearOption;
    placeholder?: string;
};

const SignOffField: React.FC<SignOffFieldProps> = ({ clearOption = ClearOption.SIGNED, readOnly, placeholder, ...fieldProps }) => {
    const { data: currentUser } = usePursGetCurrentUserUsingGetQuery();

    const canClear = useCallback(
        (userId: number): boolean => {
            switch (clearOption) {
                case ClearOption.EVERYONE:
                    return true;
                case ClearOption.NOBODY:
                    return false;
                case ClearOption.SIGNED:
                    return userId === currentUser?.id;
                default:
                    return false;
            }
        },
        [clearOption, currentUser?.id],
    );

    return (
        <FormField
            {...fieldProps}
            readOnly={readOnly}
            renderField={({ ref: _ref, ...field }) => {
                return (
                    <Grid
                        container
                        spacing="10px"
                    >
                        <Grid
                            item
                            flex="1"
                        >
                            {field.value ? (
                                <LinksListBuilder
                                    type={ColumnType.SIGN_OFF}
                                    selected={[field.value]}
                                    readOnly
                                    disableHover={true}
                                />
                            ) : (
                                <Input
                                    placeholder={placeholder || strings('common:label.selectSign')}
                                    disabled
                                />
                            )}
                        </Grid>
                        {!readOnly && ((field.value && canClear(field.value.id)) || !field.value) && (
                            <Grid
                                item
                                flex="0 0 auto"
                            >
                                {!readOnly && field.value && canClear(field.value.id) && (
                                    <Button
                                        {...ButtonStyles.inputButton}
                                        onClick={() => field.onChange('')}
                                        variant="outlined"
                                        dataTestId="button-sign"
                                    >
                                        {strings('common:button.unsign')}
                                    </Button>
                                )}
                                {!readOnly && !field.value && (
                                    <Button
                                        {...ButtonStyles.inputButton}
                                        onClick={() => field.onChange({ name: currentUser?.name, id: currentUser?.id })}
                                        variant="outlined"
                                        dataTestId="button-sign"
                                    >
                                        {strings('common:button.sign')}
                                    </Button>
                                )}
                            </Grid>
                        )}
                    </Grid>
                );
            }}
        />
    );
};

export default SignOffField;
