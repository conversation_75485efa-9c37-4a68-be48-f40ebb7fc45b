import React from 'react';
import _ from 'lodash';

import <PERSON><PERSON>ield, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import RegisterEntriesTableSelector from 'common/components/RegisterEntriesTableSelector';
import { ViewExpressionRest } from 'api/generated/types';
import { SearchEntriesParams } from 'common/components/RegisterEntriesTableSelector/RegisterEntriesTableSelector';

type RegisterEntriesTableFieldProps = FormFieldBaseProps & {
    registerId: number;
    entryFormContainer: Element | null;
    multiselect?: boolean;
    hideAddButton?: boolean;
    height?: number;
    isAnonymous?: boolean;
    context?: string;
    additionalExpressions?: ViewExpressionRest[];
    searchEntriesParams: SearchEntriesParams;
};

const RegisterEntriesTableField: React.FC<RegisterEntriesTableFieldProps> = ({
    registerId,
    entryFormContainer,
    multiselect = false,
    hideAddButton = false,
    isAnonymous = false,
    height,
    readOnly,
    context,
    additionalExpressions,
    searchEntriesParams,
    ...formFieldProps
}) => {
    return (
        <>
            <FormField
                {...formFieldProps}
                readOnly={readOnly}
                hideFieldLabel
                renderField={({ ref: _ref, value, onChange }) => {
                    return (
                        <RegisterEntriesTableSelector
                            registerId={registerId}
                            multiselect={multiselect}
                            context={context}
                            selectedEntries={value ?? []}
                            onSelectedChange={onChange}
                            height={height}
                            hideAddButton={hideAddButton}
                            label={formFieldProps.label}
                            labelTooltip={formFieldProps.tooltip}
                            readOnly={readOnly}
                            entryFormContainer={entryFormContainer}
                            isAnonymous={isAnonymous}
                            additionalExpressions={additionalExpressions}
                            searchEntriesParams={searchEntriesParams}
                        />
                    );
                }}
            />
        </>
    );
};

export default RegisterEntriesTableField;
