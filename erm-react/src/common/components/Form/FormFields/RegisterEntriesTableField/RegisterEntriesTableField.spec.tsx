import React from 'react';
import { FormProvider } from 'react-hook-form';
import { render, screen } from 'test/utils/rtl';
import RegisterEntriesTableSelector from 'common/components/RegisterEntriesTableSelector';
import RegisterEntriesTableField from './RegisterEntriesTableField';
import useForm from 'common/hooks/forms/useForm';
import { waitFor } from '@testing-library/react';

jest.mock('common/components/RegisterEntriesTableSelector', () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="register-entries-table-selector">selector</div>),
}));

const formFieldProps = {
    name: 'someName',
    label: 'Test Label',
    tooltip: 'Test Tooltip',
};
const registerId = 1;
const entryFormContainer = document.createElement('div');
const multiselect = true;
const hideAddButton = true;
const isAnonymous = false;
const height = 200;
const readOnly = false;

const mockSearchEntriesParams = {
    parentTableName: 'some-table',
    parentEntryId: 12345,
    subtableColumn: 'some-column',
};

const ComponentWrapper: React.FC<{ isAnonymous?: boolean }> = () => {
    const methods = useForm({
        defaultValues: {
            someName: [
                { id: 1, name: 'name1' },
                { id: 2, name: 'name2' },
            ],
        },
    });

    return (
        <FormProvider {...methods}>
            <RegisterEntriesTableField
                {...formFieldProps}
                registerId={registerId}
                entryFormContainer={entryFormContainer}
                multiselect={multiselect}
                hideAddButton={hideAddButton}
                isAnonymous={isAnonymous}
                height={height}
                readOnly={readOnly}
                searchEntriesParams={mockSearchEntriesParams}
            />
        </FormProvider>
    );
};

describe('RegisterEntriesTableField Component', () => {
    test('renders RegisterEntriesTableSelector with correct props', async () => {
        render(<ComponentWrapper />);

        expect(RegisterEntriesTableSelector).toHaveBeenCalledWith(
            expect.objectContaining({
                registerId,
                multiselect,
                selectedEntries: [
                    { id: 1, name: 'name1' },
                    { id: 2, name: 'name2' },
                ],
                height,
                hideAddButton,
                label: formFieldProps.label,
                labelTooltip: formFieldProps.tooltip,
                readOnly,
                entryFormContainer,
                isAnonymous,
            }),
            {},
        );
        await waitFor(() => {
            expect(screen.getByTestId('register-entries-table-selector')).toBeInTheDocument();
        });
    });

    test('does not render form field label and tooltip', async () => {
        render(<ComponentWrapper />);

        await waitFor(() => {
            expect(screen.queryByText(formFieldProps.label)).not.toBeInTheDocument();
        });
        expect(screen.queryByTestId('field-tooltip')).not.toBeInTheDocument();
    });
});
