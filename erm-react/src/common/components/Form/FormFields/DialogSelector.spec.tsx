import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DialogSelector from './DialogSelector';

const mockRenderDialog = jest.fn(() => <div data-testid="mock-dialog">Mock Dialog</div>);

const defaultProps = {
    dataTestId: 'test-dialog-selector',
    placeholder: 'Select an option',
    readOnly: false,
    renderDialog: mockRenderDialog,
};

describe('DialogSelector', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Component Rendering', () => {
        it('renders StyledInput when overrideInputComponent is undefined', () => {
            render(<DialogSelector {...defaultProps} />);

            const styledInput = screen.getByTestId('test-dialog-selector-input');
            expect(styledInput).toBeInTheDocument();
        });

        it('renders overrideInputComponent when provided instead of StyledInput', () => {
            const customInput = (
                <input
                    data-testid="custom-input"
                    placeholder="Custom input"
                />
            );

            render(
                <DialogSelector
                    {...defaultProps}
                    overrideInputComponent={customInput}
                />,
            );

            const customInputElement = screen.getByTestId('custom-input');
            const styledInput = screen.queryByTestId('test-dialog-selector-input');

            expect(customInputElement).toBeInTheDocument();
            expect(styledInput).not.toBeInTheDocument();
        });

        it("renders 'Select' button when not readOnly", () => {
            render(<DialogSelector {...defaultProps} />);

            const selectButton = screen.getByTestId('test-dialog-selector-select-button');
            expect(selectButton).toBeInTheDocument();
        });

        it("does not render 'Select' button when readOnly", () => {
            render(
                <DialogSelector
                    {...defaultProps}
                    readOnly={true}
                />,
            );

            const styledInput = screen.getByTestId('test-dialog-selector-input');
            const selectButton = screen.queryByTestId('test-dialog-selector-select-button');

            expect(styledInput).toBeInTheDocument();
            expect(selectButton).not.toBeInTheDocument();
        });
    });

    describe('Dialog Interaction', () => {
        describe('when not in readOnly mode', () => {
            it("opens dialog when 'Select' button is clicked", async () => {
                const user = userEvent.setup();
                render(<DialogSelector {...defaultProps} />);

                const selectButton = screen.getByTestId('test-dialog-selector-select-button');
                await user.click(selectButton);

                expect(mockRenderDialog).toHaveBeenCalled();
            });

            it('opens dialog when StyledInput component is clicked', async () => {
                const user = userEvent.setup();
                render(<DialogSelector {...defaultProps} />);

                const styledInput = screen.getByTestId('test-dialog-selector-input');
                await user.click(styledInput);

                expect(mockRenderDialog).toHaveBeenCalled();
            });
        });

        describe('when in readOnly mode', () => {
            it('does not open dialog when StyledInput component is clicked', async () => {
                const user = userEvent.setup();
                render(
                    <DialogSelector
                        {...defaultProps}
                        readOnly={true}
                    />,
                );

                const styledInput = screen.getByTestId('test-dialog-selector-input');
                await user.click(styledInput);

                expect(mockRenderDialog).not.toHaveBeenCalled();
            });
        });
    });

    describe('Tab Navigation', () => {
        describe('when overrideInputComponent is not provided', () => {
            it("skips readonly StyledInput component and focuses the 'Select' button when not readOnly", async () => {
                const user = userEvent.setup();

                // Render the component with a focusable element before it to test tab navigation
                render(
                    <div>
                        <button data-testid="previous-element">Previous Element</button>
                        <DialogSelector {...defaultProps} />
                        <button data-testid="next-element">Next Element</button>
                    </div>,
                );

                const previousElement = screen.getByTestId('previous-element');
                const styledInput = screen.getByTestId('test-dialog-selector-input');
                const selectButton = screen.getByTestId('test-dialog-selector-select-button');
                const nextElement = screen.getByTestId('next-element');

                previousElement.focus();
                expect(previousElement).toHaveFocus();

                await user.tab();

                expect(styledInput).not.toHaveFocus();
                expect(selectButton).toHaveFocus();

                await user.tab();
                expect(nextElement).toHaveFocus();
            });

            it('skips focusing DialogSelector altogether when readOnly', async () => {
                const user = userEvent.setup();

                render(
                    <div>
                        <button data-testid="previous-element">Previous Element</button>
                        <DialogSelector
                            {...defaultProps}
                            readOnly={true}
                        />
                        <button data-testid="next-element">Next Element</button>
                    </div>,
                );

                const previousElement = screen.getByTestId('previous-element');
                const nextElement = screen.getByTestId('next-element');

                previousElement.focus();
                expect(previousElement).toHaveFocus();

                await user.tab();
                expect(nextElement).toHaveFocus();
            });
        });

        describe('when overrideInputComponent is provided', () => {
            it("focuses any focusable item in overrideInputComponent before focusing 'Select' button when not readOnly", async () => {
                const user = userEvent.setup();
                const customInput = (
                    <input
                        data-testid="custom-input"
                        placeholder="Custom input"
                    />
                );

                render(
                    <div>
                        <button data-testid="previous-element">Previous Element</button>
                        <DialogSelector
                            {...defaultProps}
                            overrideInputComponent={customInput}
                        />
                        <button data-testid="next-element">Next Element</button>
                    </div>,
                );

                const previousElement = screen.getByTestId('previous-element');
                const customInputElement = screen.getByTestId('custom-input');
                const selectButton = screen.getByTestId('test-dialog-selector-select-button');

                previousElement.focus();
                expect(previousElement).toHaveFocus();

                await user.tab();
                expect(customInputElement).toHaveFocus();

                await user.tab();
                expect(selectButton).toHaveFocus();
            });

            it('can focus only focusable elements in overrideInputComponent when readOnly', async () => {
                const user = userEvent.setup();
                const customInput = (
                    <input
                        data-testid="custom-input"
                        placeholder="Custom input"
                        readOnly
                    />
                );

                render(
                    <div>
                        <button data-testid="previous-element">Previous Element</button>
                        <DialogSelector
                            {...defaultProps}
                            overrideInputComponent={customInput}
                            readOnly={true}
                        />
                        <button data-testid="next-element">Next Element</button>
                    </div>,
                );

                const previousElement = screen.getByTestId('previous-element');
                const customInputElement = screen.getByTestId('custom-input');
                const nextElement = screen.getByTestId('next-element');

                previousElement.focus();
                expect(previousElement).toHaveFocus();

                await user.tab();
                expect(customInputElement).toHaveFocus();

                await user.tab();
                expect(nextElement).toHaveFocus();
            });
        });
    });
});
