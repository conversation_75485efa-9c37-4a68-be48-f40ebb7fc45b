import React, { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import FormControlLabel from '@mui/material/FormControlLabel';
import MaterialRadioGroup, { RadioGroupProps } from '@mui/material/RadioGroup';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import Radio from '@protecht/ui-library/library/components/Radio';
import { RadioProps } from '@mui/material/Radio';
import { InputColorSwatch } from '@protecht/ui-library/library/components/ColorSwatch';
import { ControllerRenderProps } from 'react-hook-form';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import { strings } from 'common/utils/i18n';
import { EMPTY_VALUE } from '@protecht/ui-library/library/constants';

export type RadioGroupOption = {
    value: string | number;
    label: ReactNode;
    disabled?: boolean;
    color?: string;
    labelWidth?: number;
};

type CustomRadioGroupProps = {
    options: RadioGroupOption[];
    onChange?: (value: string | number, isValueFromInput: boolean) => void;
    shape?: RadioProps['shape'];
    size?: RadioProps['size'];
    displayInputWithLastOption?: boolean;
    disabled?: boolean;
    clearable?: boolean;
    sx?: RadioProps['sx'];
};

type RadioGroupFieldProps = Omit<RadioGroupProps, 'onChange'> & FormFieldBaseProps & CustomRadioGroupProps;

const RadioGroupField: React.FC<RadioGroupFieldProps> = ({
    name,
    disabled: groupDisabled,
    displayInputWithLastOption,
    emptyLabelSpacing,
    label,
    tooltip,
    fullWidth = false,
    ...radioProps
}) => {
    return (
        <FormField
            name={name}
            label={label}
            emptyLabelSpacing={emptyLabelSpacing}
            fullWidth={fullWidth}
            disabled={groupDisabled}
            tooltip={tooltip}
            readOnly={radioProps.readOnly}
            renderField={(field) => (
                <RenderComponent
                    field={field}
                    displayInputWithLastOption={displayInputWithLastOption}
                    disabled={groupDisabled}
                    {...radioProps}
                />
            )}
        />
    );
};

type Props = Partial<RadioGroupFieldProps> & {
    field: ControllerRenderProps<any>;
};

const RenderComponent: React.FC<Props> = ({
    displayInputWithLastOption,
    field,
    options,
    disabled,
    readOnly,
    onChange,
    shape = 'circle',
    size = 'small',
    clearable = false,
    ...radioProps
}) => {
    const [isLastOptionSelected, setIsLastOptionSelected] = useState<boolean>(false);
    const [radioGroupValue, setRadioGroupValue] = useState<string | number>('');
    const [inputValue, setInputValue] = useState<string | number>('');
    const anyOptionHasColor = options?.some((option) => option.color);
    const isUserSelection = useRef<boolean>(false);

    const renderLabel = useCallback(
        (item: RadioGroupOption) => {
            if (anyOptionHasColor) {
                return (
                    <>
                        <InputColorSwatch
                            disabled={item.disabled || disabled}
                            color={item.color}
                            marginRight="6px"
                        />
                        <span style={{ minWidth: item.labelWidth ? `${item.labelWidth}px` : 'auto' }}>{item.label}</span>
                    </>
                );
            }
            return <span style={{ minWidth: item.labelWidth ? `${item.labelWidth}px` : 'auto' }}>{item.label}</span>;
        },
        [disabled, anyOptionHasColor],
    );

    useEffect(() => {
        if (!isLastOptionSelected) {
            setInputValue(EMPTY_VALUE);
        }
    }, [isLastOptionSelected]);

    const isFieldValuePresentInOptions = useMemo(() => options?.some((item) => item.value === field.value), [options, field.value]);

    useEffect(() => {
        if (isFieldValuePresentInOptions && field.value !== undefined && !radioGroupValue) {
            setRadioGroupValue(field.value);
        } else if (displayInputWithLastOption && field.value && !isFieldValuePresentInOptions) {
            setRadioGroupValue(options?.at(-1)?.value ?? EMPTY_VALUE);
            setIsLastOptionSelected(true);
            setInputValue(field.value);
        } else if (field.value === EMPTY_VALUE) {
            setRadioGroupValue(EMPTY_VALUE);
            setIsLastOptionSelected(false);
            setInputValue(EMPTY_VALUE);
        }
    }, [field.value, isFieldValuePresentInOptions, options, radioGroupValue, displayInputWithLastOption, field.name]);

    useEffect(() => {
        if (displayInputWithLastOption && isUserSelection.current && isLastOptionSelected) {
            isUserSelection.current = false;
        }
    }, [displayInputWithLastOption, isLastOptionSelected]);

    const classNames = useMemo(() => {
        let classname = '';

        if (radioProps?.className) {
            classname += radioProps.className;
        }

        if (readOnly) {
            classname += ' Mui-readOnly';
        }

        return classname;
    }, [radioProps.className, readOnly]);

    const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLDivElement>) => {
        if (event.code === 'Space') {
            event.preventDefault();
            const target = event.target as HTMLInputElement;

            if (target && target.type === 'radio') {
                target.click();
            }
        }
    }, []);

    return (
        <>
            <MaterialRadioGroup
                {...radioProps}
                {...field}
                value={radioGroupValue}
                className={classNames}
                onKeyDown={handleKeyDown}
            >
                {options?.map((item, index) => (
                    <FormControlLabel
                        key={`${index}_${item.value}`}
                        label={renderLabel(item)}
                        value={item.value}
                        disabled={item.disabled || readOnly}
                        control={
                            <Radio
                                size={size}
                                color={'primary'}
                                disabled={disabled}
                                shape={shape}
                                className={readOnly ? 'Mui-readOnly' : undefined}
                            />
                        }
                        onClick={(event: React.MouseEvent<HTMLLabelElement, MouseEvent>) => {
                            const target = event.target as HTMLElement;
                            // Avoid running the click event twice
                            if (target.tagName !== 'INPUT') {
                                return;
                            }

                            isUserSelection.current = true;

                            if (!readOnly && !clearable) {
                                field.onChange(item.value);
                                onChange?.(item.value, false);
                                setIsLastOptionSelected(item.value === options?.at(-1)?.value ? true : false);
                                setRadioGroupValue(item.value);
                            } else if (!readOnly && clearable) {
                                if (item.value === radioGroupValue) {
                                    field.onChange(EMPTY_VALUE);
                                    onChange?.(EMPTY_VALUE, false);
                                    setIsLastOptionSelected(false);
                                    setRadioGroupValue(EMPTY_VALUE);
                                } else {
                                    field.onChange(item.value);
                                    onChange?.(item.value, false);
                                    setIsLastOptionSelected(item.value === options?.at(-1)?.value ? true : false);
                                    setRadioGroupValue(item.value);
                                }
                            }
                        }}
                    />
                ))}
            </MaterialRadioGroup>

            {displayInputWithLastOption && isLastOptionSelected && (
                <Input
                    autoFocus={isUserSelection.current}
                    placeholder={strings('common:label.pleaseTypeHere')}
                    value={inputValue}
                    sx={{ mt: 1 }}
                    onChange={(event) => {
                        const value = event.target.value ?? EMPTY_VALUE;
                        field.onChange(value);
                        onChange?.(value, true);
                        setInputValue(value);
                    }}
                ></Input>
            )}
        </>
    );
};

export default RadioGroupField;
