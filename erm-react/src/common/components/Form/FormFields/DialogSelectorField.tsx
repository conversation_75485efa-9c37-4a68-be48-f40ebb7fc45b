import React, { useState } from 'react';
import <PERSON><PERSON>ield, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import Box from '@mui/material/Box';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { strings } from 'common/utils/i18n';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import { styled, useTheme } from '@mui/material/styles';
import InputAdornment from '@mui/material/InputAdornment';
import { faEllipsis } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';

export const StyledInput = styled(Input)(({ theme }) => ({
    '& .MuiInputBase-root, & .MuiInputBase-root.Mui-focused': {
        [`& fieldset,
          &:hover fieldset,
          &:focus fieldset,
          & .MuiOutlinedInput-notchedOutline,
          &:hover .MuiOutlinedInput-notchedOutline,
          &:focus .MuiOutlinedInput-notchedOutline`]: {
            borderColor: theme.palette.protechtGrey?.grey_231,
            borderWidth: '1px',
        },
    },
    [`& .MuiInputBase-root:not(.Mui-disabled):not(.Mui-readOnly),
      & .MuiInputBase-root:not(.Mui-disabled):not(.Mui-readOnly) .MuiInputBase-input`]: {
        cursor: 'pointer',
    },
    '& .Mui-disabled': {
        cursor: 'default',
        WebkitTextFillColor: theme.palette.protechtGrey?.grey_178,
    },
}));

const StyledOnlyInput = styled(Input)(({ theme }) => ({
    '& .MuiInputBase-root': {
        background: theme.palette.protechtGrey?.grey_250,
        borderColor: theme.palette.protechtGrey?.grey_178,
        borderRadius: '4px',
        color: theme.palette.primary.main,
    },
    [`& .MuiInputBase-root:not(.Mui-disabled):not(.Mui-readOnly),
      & .MuiInputBase-root:not(.Mui-disabled):not(.Mui-readOnly) .MuiInputBase-input`]: {
        cursor: 'pointer',
    },
    '& .MuiInputBase-input:not(:placeholder-shown)': {
        fontWeight: 600,
    },
}));

export interface DialogSelectorFieldProps extends FormFieldBaseProps {
    displayValue?: string;
    placeholder?: string;
    clearable?: boolean;
    selectButtonDataTestId?: string;
    icon?: IconProp;
    renderDialog: (props) => React.ReactElement;
    onlyInput?: boolean;
    dataTestId?: string;
    onInputClear?: () => void;
}

const DialogSelectorField: React.FC<DialogSelectorFieldProps> = ({
    displayValue,
    placeholder = '',
    clearable = false,
    disabled,
    selectButtonDataTestId,
    renderDialog,
    icon,
    onlyInput = false,
    dataTestId,
    onInputClear,
    ...formFieldProps
}) => {
    const [openedDialog, setOpenedDialog] = useState(false);
    const theme = useTheme();

    const handleOnClick = () => {
        if (!disabled) {
            setOpenedDialog(true);
        }
    };

    const InputComponent = onlyInput ? StyledOnlyInput : StyledInput;
    const additionalProps = onlyInput
        ? {
              inputProps: { 'aria-label': formFieldProps.label, readOnly: true, onClick: handleOnClick },
              InputProps: {
                  endAdornment: (
                      <InputAdornment
                          position="end"
                          data-testid="openDialog"
                          onClick={handleOnClick}
                      >
                          <FontAwesomeIcon
                              icon={faEllipsis}
                              color={theme.palette.primary.main}
                              size="xl"
                          />
                      </InputAdornment>
                  ),
              },
              leftBorder: false,
              clearable: clearable,
          }
        : {
              inputProps: { 'aria-label': formFieldProps.label, onClick: handleOnClick },
              InputProps: { readOnly: true },
              readOnlyWithClearable: clearable,
          };

    const renderIconOrBtn = icon ? (
        <FontAwesomeIcon
            icon={icon}
            color={theme.palette.primary.main}
            onClick={handleOnClick}
        ></FontAwesomeIcon>
    ) : (
        <Button
            {...ButtonStyles.inputButton}
            disabled={disabled}
            dataTestId={selectButtonDataTestId}
            onClick={handleOnClick}
            variant="outlined"
        >
            {strings('common:button.select')}
        </Button>
    );

    return (
        <>
            <FormField
                {...formFieldProps}
                disabled={disabled}
                renderField={(field) => (
                    <Box sx={{ display: 'flex', gap: '10px' }}>
                        <InputComponent
                            {...field}
                            {...additionalProps}
                            onChange={(e) => {
                                field.onChange(e.target?.value ? e : '');
                                if (!e.target.value) {
                                    onInputClear?.();
                                }
                            }}
                            value={displayValue ?? field.value}
                            placeholder={placeholder}
                            disabled={disabled}
                            leftBorder={false}
                            dataTestId={dataTestId}
                            clearable={true}
                        />
                        {!onlyInput && renderIconOrBtn}
                    </Box>
                )}
            />
            {openedDialog &&
                renderDialog({
                    setOpen: () => {
                        setOpenedDialog(false);
                    },
                    onClose: () => {
                        setOpenedDialog(false);
                    },
                })}
        </>
    );
};

export default DialogSelectorField;
