import React, { useMemo } from 'react';
import DialogSelectorField from 'common/components/Form/FormFields/DialogSelectorField';
import RegisterFieldSelector from 'common/components/RegisterFieldSelector';
import { RegisterRest } from 'register/types';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types/types';
import { strings } from 'common/utils/i18n';
import { RegisterFieldSelectorColDef } from './constants';
import { createLabelWithSection, getFieldsWithLabels } from './utils';
import { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';

interface RegisterFieldSelectorFieldProps extends FormFieldBaseProps {
    fields: SectionFieldMetaData[];
    register: RegisterRest | undefined;
    selectedField: SectionFieldMetaData | undefined;
    onSelect: (selectedField: SectionFieldMetaData) => void;
    onlyInput?: boolean;
    disabled?: boolean;
    infoMessage?: string;
    placeholder?: string;
    dataTestId?: string;
}

const RegisterFieldSelectorField: React.FC<RegisterFieldSelectorFieldProps> = ({
    fields,
    register,
    selectedField,
    onSelect,
    onlyInput = false,
    disabled = false,
    infoMessage,
    dataTestId,
    ...formFieldProps
}) => {
    const sourceFields = useMemo(() => {
        if (register) {
            return getFieldsWithLabels(fields, register);
        } else {
            return [];
        }
    }, [fields, register]);
    const fieldDisplayValue = useMemo(() => {
        if (register && selectedField) {
            return createLabelWithSection(selectedField, register);
        } else {
            return '';
        }
    }, [register, selectedField]);

    return (
        <DialogSelectorField
            {...formFieldProps}
            disabled={!register || disabled}
            onlyInput={onlyInput}
            displayValue={fieldDisplayValue}
            dataTestId={dataTestId}
            renderDialog={(props) => {
                return (
                    <RegisterFieldSelector
                        {...props}
                        title={strings('common:label.selectSourceField', { label: register?.label })}
                        columns={RegisterFieldSelectorColDef}
                        data={sourceFields}
                        selected={selectedField}
                        onSelect={onSelect}
                        infoMessage={infoMessage}
                    />
                );
            }}
        />
    );
};

export default RegisterFieldSelectorField;
