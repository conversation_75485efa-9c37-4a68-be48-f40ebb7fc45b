import { SectionFieldMetaData } from '@protecht/ui-library/library/types/types';
import { RegisterRest } from 'register/types';
import { getSectionNameOfField } from 'register/utils';

export const createLabelWithSection = (field: SectionFieldMetaData, register: RegisterRest): string => {
    const sectionName = getSectionNameOfField(register, field.id);
    return sectionName ? `${sectionName} | ${field.label ?? field.columnName}` : `${field.label ?? field.columnName}`;
};

export const getFieldsWithLabels = (fields: SectionFieldMetaData[], register: RegisterRest): SectionFieldMetaData[] => {
    return fields.map((field) => ({
        ...field,
        labelWithSection: createLabelWithSection(field, register),
    }));
};
