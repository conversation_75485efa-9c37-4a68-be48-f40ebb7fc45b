import { DataGridColDef } from 'common/types';
import { strings } from 'common/utils/i18n';
import { FilterType } from 'view/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { getColumnTypeLabel } from 'register/utils';

export const RegisterFieldSelectorColDef: DataGridColDef[] = [
    {
        field: 'labelWithSection',
        headerName: strings('common:label.fieldSection'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
    },
    {
        field: 'columnType',
        headerName: strings('common:label.fieldType'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 0,
        valueFormatter: ({ value }) => getColumnTypeLabel(value),
    },
];
