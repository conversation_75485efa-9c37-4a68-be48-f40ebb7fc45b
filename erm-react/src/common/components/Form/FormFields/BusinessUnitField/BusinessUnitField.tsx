import React, { useCallback } from 'react';

import { ColumnType } from '@protecht/ui-library/library/types';

import { IdWithNameAndStatusRest } from 'app/types';
import { ExpressionType } from 'view/types';

import useFormContext from 'common/hooks/forms/useFormContext';
import { useFilterBusinessUnitsMutation } from 'common/api/businessUnits';
import useSnackbar from 'common/hooks/useSnackbar';
import { BusinessUnitSimpleRest, ViewExpressionRest } from 'api/generated/types';
import { SEARCH_RESULT_LIMIT } from 'common/components/SuggestionsInput/InputLookup';
import { BusinessUnitFilterData } from 'common/components/BusinessUnitSelector/BusinessUnitSelector';
import InputSelectorPickerField, { InputSelectorPickerFieldProps } from 'common/components/Form/FormFields/InputSelectorPickerField/InputSelectorPickerField';

type BusinessUnit = BusinessUnitSimpleRest & IdWithNameAndStatusRest;

type BusinessUnitFieldProps = Omit<InputSelectorPickerFieldProps<BusinessUnit>, 'type' | 'filterData' | 'selectedItems' | 'onDataLoad'> & {
    filterData?: BusinessUnitFilterData;
};

const BusinessUnitField = (props: BusinessUnitFieldProps) => {
    const { name, filterData, preferArrayForSingleSelect = false, onDataClear, ...rest } = props;
    const { enqueueError } = useSnackbar();
    const [triggerSearch] = useFilterBusinessUnitsMutation();
    const { watch, trigger, setSearchError, clearSearchError } = useFormContext();
    const selected = watch(name);

    const getMatchingBUs = useCallback((node: BusinessUnitSimpleRest[] | undefined, arr: BusinessUnitSimpleRest[], matchingIds: number[]) => {
        node?.forEach((tree) => {
            if (matchingIds?.includes(tree.id!)) {
                arr.push(tree);
            }
            if (Array.isArray(tree?.children) && tree?.children.length > 0) {
                getMatchingBUs(tree.children, arr, matchingIds);
            }
        });
    }, []);

    const handleSearch = useCallback(
        async (query: string) => {
            clearSearchError(name);

            const expressions: ViewExpressionRest[] = [];
            expressions.push({
                value: query,
                expression: ExpressionType.CONTAINS,
                property: 'name',
                type: 'STRING',
            });

            if (filterData?.filterValue) {
                expressions.push({
                    property: 'name',
                    value: 'nested',
                    expression: ExpressionType.CONTAINS,
                    type: 'STRING',
                });
            }

            if (filterData?.matchingBusinessunits) {
                expressions.push({
                    property: 'id',
                    value: filterData?.matchingBusinessunits?.join(';'),
                    expression: ExpressionType.IN,
                    type: 'NUMBER',
                });
            }

            if (filterData?.id) {
                // TODO: add filtering based on filterData.id
            }

            return await triggerSearch({ filterContextRest: { expressions }, limit: SEARCH_RESULT_LIMIT })
                .unwrap()
                .then((res) => {
                    const matchingIds = res?.response?.matchingBusinessunits;

                    if (Array.isArray(matchingIds) && matchingIds?.length > 0) {
                        const matchingBUs: BusinessUnitSimpleRest[] = [];

                        getMatchingBUs(res?.response?.businessUnitSimpleRests, matchingBUs, matchingIds);
                        return matchingBUs.slice(0, SEARCH_RESULT_LIMIT) as BusinessUnit[];
                    } else {
                        setSearchError(name);
                        return [] as BusinessUnit[];
                    }
                })
                .catch((error) => {
                    enqueueError(error.data.message);
                    return [];
                })
                .finally(() => {
                    void trigger(name);
                });
        },
        [
            filterData?.filterValue,
            filterData?.id,
            filterData?.matchingBusinessunits,
            name,
            setSearchError,
            triggerSearch,
            getMatchingBUs,
            enqueueError,
            trigger,
        ],
    );

    const handleClear = useCallback(() => {
        clearSearchError(name);
        onDataClear?.();
        void trigger(name);
    }, [name, trigger, clearSearchError, onDataClear]);

    return (
        <InputSelectorPickerField<BusinessUnit>
            type={ColumnType.BUSINESS_UNIT}
            name={name}
            selectedItems={selected}
            onDataLoad={handleSearch}
            onDataClear={handleClear}
            filterData={filterData}
            preferArrayForSingleSelect={preferArrayForSingleSelect}
            {...rest}
        />
    );
};

export default BusinessUnitField;
