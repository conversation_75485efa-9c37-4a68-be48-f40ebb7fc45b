import React, { useCallback, useEffect, useState } from 'react';
import Box from '@mui/system/Box';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import styled from '@mui/system/styled';
import { ColorSelectorType } from './types';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { strings } from 'common/utils/i18n';
import MetricColorScaleSelector from 'common/components/MetricsSettings/MetricColorScales/MetricColorScaleSelector';
import { MetricColors } from 'metrics/types';
import { useFormContext } from 'react-hook-form';
import Typography from '@mui/material/Typography';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { MetricColorsType } from 'common/components/MetricsSettings/types';
import { getColorScaleDescription } from './utils';
import { getColorScaleSelectorType } from 'common/components/MetricsSettings/utils';
import { getColorIndicators } from 'metrics/utils';
import ColorIndicatorList from 'common/components/ColorIndicatorList';

type ColorScaleFieldProps = FormFieldBaseProps & {
    selectedSourceField?: SectionFieldMetaData;
};

const StyledColorScaleInput = styled(Box)(({ theme }) => ({
    width: '100%',
    display: 'flex',
    columnGap: '10px',
    alignContent: 'center',
    border: '1px solid',
    padding: '5px 10px',
    borderColor: theme.palette.protechtGrey.grey_231,
    alignItems: 'center',
}));

const ColorScaleField: React.FC<ColorScaleFieldProps> = ({ disabled, selectedSourceField, ...formFieldProps }) => {
    const [colorSelectorDialogOpened, setColorSelectorDialogOpened] = useState<boolean>(false);
    const [selectorType, setSelectorType] = useState<ColorSelectorType>(ColorSelectorType.DEFAULT);

    const { watch } = useFormContext();
    const metricColorsType: MetricColorsType = watch('metricColorsType');
    const metricColors: MetricColors | undefined = watch('colors');

    useEffect(() => {
        setSelectorType(getColorScaleSelectorType(selectedSourceField?.columnType));
    }, [selectedSourceField]);

    const colorDescription = getColorScaleDescription(metricColorsType, selectorType);

    const colorDisplay = useCallback(() => {
        const colors = metricColors ? getColorIndicators(metricColors, metricColorsType, selectedSourceField?.columnType) : [];
        return <ColorIndicatorList colors={colors} />;
    }, [metricColorsType, metricColors, selectedSourceField]);

    return (
        <>
            <FormField
                {...formFieldProps}
                disabled={disabled}
                renderField={() => (
                    <Box sx={{ display: 'flex', gap: '10px' }}>
                        <StyledColorScaleInput>
                            <Typography
                                variant="body1"
                                whiteSpace="nowrap"
                            >
                                {colorDescription}
                            </Typography>
                            {colorDisplay()}
                        </StyledColorScaleInput>
                        <Button
                            {...ButtonStyles.inputButton}
                            disabled={disabled}
                            onClick={() => {
                                setColorSelectorDialogOpened(true);
                            }}
                            variant="outlined"
                        >
                            {strings('common:button.select')}
                        </Button>
                    </Box>
                )}
            />
            {colorSelectorDialogOpened && (
                <MetricColorScaleSelector
                    title={strings('metrics:colorSelectorDialog.title')}
                    selectedSourceField={selectedSourceField}
                    selectorType={selectorType}
                    onClose={() => {
                        setColorSelectorDialogOpened(false);
                    }}
                    onConfirm={() => {
                        setColorSelectorDialogOpened(false);
                    }}
                />
            )}
        </>
    );
};

export default ColorScaleField;
