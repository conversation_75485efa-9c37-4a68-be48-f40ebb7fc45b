import { MetricColorsType } from 'common/components/MetricsSettings/types';
import { ColorSelectorType } from './types';
import { strings } from 'common/utils/i18n';

export const getColorScaleDescription = (metricColorsType: MetricColorsType, scaleType?: ColorSelectorType) => {
    return metricColorsType === MetricColorsType.COLOR
        ? strings(`metrics:colorScaleDescription.${metricColorsType}`)
        : strings(`metrics:colorScaleDescription.${scaleType}`);
};
