import React, { useCallback, useMemo } from 'react';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { ColumnType } from '@protecht/ui-library/library/types';
import { SelectorType } from 'common/types';
import useFormContext from 'common/hooks/forms/useFormContext';
import { IdWithNameAndStatusRest } from 'app/types';
import Box from '@mui/system/Box';
import styled from '@mui/system/styled';
import { getSelectorMetadata } from 'common/utils/definitions';
import Selector from 'common/components/Selector';
import DialogSelector from './DialogSelector';
import LinksListBuilder from 'common/components/LinksListBuilder';
import { UserFilterData } from 'library/components/User/UserLayout';
import { BusinessUnitFilterData } from 'common/components/BusinessUnitSelector/BusinessUnitSelector';
import { MSLibraryFieldFilterData } from 'register/components/RegisterField/MultiSelectLibraryFields/types';
import { TableFilterData } from 'register/components/RegisterField/TableRegisterField/types';

const StyledBox = styled(Box)(() => ({
    width: '100%',
    maxHeight: '218px',
    overflow: 'auto',
}));

export type InputSelectorPickerFieldProps<T> = FormFieldBaseProps & {
    type: ColumnType | SelectorType;
    filterData?: UserFilterData | BusinessUnitFilterData | MSLibraryFieldFilterData | TableFilterData;
    persistViewState?: boolean;
    onHover?: (item: T) => void;
};

const MultiSelectorPickerField = <T extends IdWithNameAndStatusRest>({
    type,
    readOnly,
    filterData,
    persistViewState,
    onHover,
    ...formFieldProps
}: InputSelectorPickerFieldProps<T>) => {
    const { watch } = useFormContext();
    const selectedValues = watch(formFieldProps.name);
    const selectorMetadata = useMemo(() => getSelectorMetadata(type), [type]);

    const overrideInputComponent = useCallback(
        (onChange: (e: any) => void) => {
            return selectedValues && selectedValues.length > 0 ? (
                <StyledBox tabIndex={-1}>
                    <LinksListBuilder<T>
                        onItemRemove={(item) => {
                            const newItems = selectedValues.filter((selected) => selected.id !== item.id);
                            onChange(newItems);
                        }}
                        onItemHover={onHover}
                        selected={selectedValues}
                        type={type}
                        readOnly={readOnly}
                    />
                </StyledBox>
            ) : undefined;
        },
        [onHover, readOnly, selectedValues, type],
    );

    return (
        <FormField
            {...formFieldProps}
            renderField={({ onChange }) => {
                return (
                    <DialogSelector
                        placeholder={selectorMetadata.placeholder}
                        dataTestId="multiselector-picker"
                        overrideInputComponent={overrideInputComponent(onChange)}
                        readOnly={readOnly}
                        renderDialog={({ _setOpen, onClose }) => (
                            <Selector
                                title={selectorMetadata.titleMultiple || selectorMetadata.title}
                                visible={true}
                                multiselect={true}
                                filterData={filterData}
                                persistViewState={persistViewState}
                                selected={selectedValues || []}
                                onSubmit={(currentSelection) => {
                                    onChange(currentSelection);
                                    onClose();
                                }}
                                type={type}
                                onClose={onClose}
                            />
                        )}
                    />
                );
            }}
        />
    );
};

export default MultiSelectorPickerField;
