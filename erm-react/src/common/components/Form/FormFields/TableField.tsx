import React from 'react';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { IdOnly } from 'app/types';
import { Table } from '@protecht/ui-library/library/components/Table';
import { TableProps } from '@protecht/ui-library/library/components/Table/Table';
import Box from '@mui/material/Box';

type Props<T> = {
    formFieldProps: FormFieldBaseProps;
    tableProps: TableProps<T>;
    tableHeight?: number;
};

export const TableField = <T extends IdOnly>({ formFieldProps, tableProps, tableHeight }: Props<T>) => {
    return (
        <FormField
            {...formFieldProps}
            renderField={({ ref: _ref, ...field }) => (
                <Box
                    width={'100%'}
                    height={tableHeight}
                >
                    <Table
                        {...field}
                        {...tableProps}
                        selected={field.value}
                        onSelect={(selection) => field.onChange(selection)}
                    />
                </Box>
            )}
        />
    );
};
