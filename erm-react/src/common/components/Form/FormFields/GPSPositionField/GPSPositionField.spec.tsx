import React from 'react';
import useForm from 'common/hooks/forms/useForm';
import { FormProvider } from 'react-hook-form';
import GPSPositionField from './GPSPositionField';
import { waitFor } from '@testing-library/react';
import { render, screen } from 'test/utils';

const formFieldProps = {
    name: 'field1',
    label: 'Test Label',
    tooltip: 'Test Tooltip',
};

const GPSPositionFieldWrapper: React.FC<{ readonly?: boolean }> = ({ readonly = false }) => {
    const methods = useForm({
        defaultValues: {
            field1: { lat: 1, lng: 1 },
        },
    });

    return (
        <FormProvider {...methods}>
            <GPSPositionField
                {...formFieldProps}
                readOnly={readonly}
                defaultCenterPosition={{ lat: 1, lng: 1 }}
                defaultZoom={8}
            />
        </FormProvider>
    );
};

beforeAll(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    global.navigator.geolocation = {
        getCurrentPosition: jest.fn(),
    } as jest.Mocked<Geolocation>;
});

describe('GPSPositionField Component', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should display the coordinates', async () => {
        render(<GPSPositionFieldWrapper />);
        const mapContainer = screen.getByTestId('google-map');
        expect(mapContainer).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.getByText(`Co-ordinates 1, 1`)).toBeInTheDocument();
        });
    });

    it('should call setCurrentGPSLocation when the button is clicked', async () => {
        const { user } = render(<GPSPositionFieldWrapper />);

        const button = screen.getByText(/set current location/i);
        await user.click(button);
        expect(navigator.geolocation.getCurrentPosition).toHaveBeenCalled();
    });

    it('should open the dialog when the adjust button is clicked', async () => {
        const { user } = render(<GPSPositionFieldWrapper />);
        const button = screen.getByText('Adjust');
        await user.click(button);
        await waitFor(() => {
            expect(screen.getByText('Save')).toBeInTheDocument();
        });
    });

    it('should clear the marker position when the clear button is clicked', async () => {
        const { user } = render(<GPSPositionFieldWrapper />);
        await waitFor(() => {
            expect(screen.getByText('Co-ordinates 1, 1')).toBeInTheDocument();
        });
        const button = screen.getByText('Clear');
        await user.click(button);
        await waitFor(() => {
            expect(screen.queryByText('Co-ordinates 1, 1')).not.toBeInTheDocument();
        });
    });

    it('should not render buttons when field is readonly', async () => {
        render(<GPSPositionFieldWrapper readonly={true} />);
        await waitFor(() => {
            expect(screen.queryByTestId('gps-position-field-buttons')).not.toBeInTheDocument();
        });
    });
});
