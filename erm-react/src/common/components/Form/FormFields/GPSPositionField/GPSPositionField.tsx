import React, { FC, useEffect, useState } from 'react';
import <PERSON><PERSON>ield, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import Box from '@mui/material/Box';
import { useFormContext } from 'react-hook-form';
import Typography from '@mui/material/Typography';
import useSnackbar from 'common/hooks/useSnackbar';
import GPSPositionDialog from 'common/components/GPSPositionDialog';
import GoogleMap from 'common/components/GoogleMap';
import { strings } from 'common/utils/i18n';
import Button from '@protecht/ui-library/library/components/Button';
import { MapPin } from '@protecht/ui-library/library/components/SVGIcons';
import { StyledButton } from 'common/components/buttons/StyledButton';

type Props = FormFieldBaseProps & {
    mapHeight?: number;
    defaultCenterPosition: google.maps.LatLngLiteral;
    defaultZoom: number;
};

const DEFAULT_MAP_HEIGHT = 200;

const GPSPositionField: FC<Props> = ({ mapHeight, defaultCenterPosition, defaultZoom, ...fieldProps }) => {
    const { enqueueError } = useSnackbar();
    const { watch } = useFormContext();
    const selectedValue = watch(fieldProps.name);

    const [markerPosition, setMarkerPosition] = useState<google.maps.LatLngLiteral | undefined>(undefined);
    const [centerPosition, setCenterPosition] = useState<google.maps.LatLngLiteral | undefined>(defaultCenterPosition);
    const [zoom, setZoom] = useState<number | undefined>(defaultZoom);

    const [dialogOpen, setDialogOpen] = useState(false);

    useEffect(() => {
        if (selectedValue) {
            setCenterPosition(selectedValue);
            setMarkerPosition(selectedValue);
        } else {
            setMarkerPosition(undefined);
        }
    }, [selectedValue, setCenterPosition, setMarkerPosition]);

    // todo pre-load user location for faster transition to current location
    const setCurrentGPSLocation = (onChange: (...event: any[]) => void) => {
        // Check if Geolocation is supported
        if (navigator.geolocation) {
            // Request the user's position
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    setCenterPosition({ lat: position.coords.latitude, lng: position.coords.longitude });
                    setMarkerPosition({ lat: position.coords.latitude, lng: position.coords.longitude });
                    onChange({ lat: position.coords.latitude, lng: position.coords.longitude });
                },
                (error) => {
                    enqueueError(error.message);
                },
            );
        }
    };

    return (
        <FormField
            {...fieldProps}
            renderField={(field) => (
                <>
                    <GoogleMap
                        mapHeight={`${mapHeight ? mapHeight : DEFAULT_MAP_HEIGHT}px`}
                        defaultZoom={defaultZoom || 8}
                        defaultCenter={defaultCenterPosition}
                        mapOptions={{ center: centerPosition, zoom: zoom, gestureHandling: 'none', disableDefaultUI: true }}
                        markerPosition={markerPosition}
                        disableControls={true}
                    />

                    {markerPosition && (
                        <Typography
                            variant="body1"
                            sx={{ marginTop: '10px' }}
                        >
                            {strings('common:message.coordinates', { latitude: markerPosition?.lat, longitude: markerPosition?.lng })}
                        </Typography>
                    )}

                    {!fieldProps.readOnly && (
                        <Box
                            sx={{ marginTop: '10px', display: 'flex', gap: '10px', width: '100%' }}
                            data-testid={'gps-position-field-buttons'}
                        >
                            <StyledButton
                                variant="secondary"
                                size="medium"
                                onClick={() => setCurrentGPSLocation(field.onChange)}
                                startIcon={<MapPin />}
                            >
                                {strings('common:button.setCurrentLocation')}
                            </StyledButton>
                            <Button
                                variant="secondary"
                                size="medium"
                                onClick={() => setDialogOpen(true)}
                            >
                                {strings('common:button.adjust')}
                            </Button>
                            <Button
                                variant="secondary"
                                size="medium"
                                onClick={() => {
                                    setMarkerPosition(undefined);
                                    field.onChange(undefined);
                                }}
                            >
                                {strings('common:button.clear')}
                            </Button>
                        </Box>
                    )}

                    {dialogOpen && (
                        <GPSPositionDialog
                            open={true}
                            defaultCenterPosition={centerPosition || defaultCenterPosition}
                            defaultZoom={zoom || defaultZoom}
                            selectedPosition={markerPosition}
                            onClose={() => setDialogOpen(false)}
                            onPositionSave={(position, zoom) => {
                                setCenterPosition(position);
                                setMarkerPosition(position);
                                setZoom(zoom);
                                field.onChange(position);
                                setDialogOpen(false);
                            }}
                        />
                    )}
                </>
            )}
        />
    );
};

export default GPSPositionField;
