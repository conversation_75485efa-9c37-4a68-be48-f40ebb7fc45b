import React, { useCallback } from 'react';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import { useFieldArray, useFormContext } from 'react-hook-form';
import ExpressionBuilder from '../ExpressionBuilder';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { ViewExpression } from 'view/types';
import { DataGridColDef } from 'common/types';
import { useSelector } from 'react-redux';
import { strings } from 'common/utils/i18n';
import { viewsApi } from 'view/rtkApi';

interface ExpressionBuilderFieldProps extends FormFieldBaseProps {
    columns: DataGridColDef[];
}

export default function ExpressionBuilderField({ name, label, columns }: ExpressionBuilderFieldProps) {
    const { isLoading, isError } = useSelector(viewsApi.endpoints.vrsGetExpressionContextUsingGet.select());
    const { control } = useFormContext();
    const {
        fields: expressions,
        append: appendExpressions,
        remove: removeExpressions,
        update: updateExpressions,
    } = useFieldArray({
        name: 'expressions',
        control,
    });

    const findExpressionIndexById = useCallback(
        (expressionId: string) => {
            return expressions.findIndex((currExpr: ViewExpression) => expressionId === currExpr.id);
        },
        [expressions],
    );

    const handleUpdateExpression = useCallback(
        (updatedExpression) => {
            const exprIndex = findExpressionIndexById(updatedExpression.id);
            if (exprIndex >= 0) {
                updateExpressions(exprIndex, updatedExpression);
            }
        },
        [findExpressionIndexById, updateExpressions],
    );

    const handleRemoveExpression = useCallback(
        (expressionId: string) => {
            const exprIndex = findExpressionIndexById(expressionId);
            if (exprIndex >= 0) {
                removeExpressions(exprIndex);
            }
        },
        [findExpressionIndexById, removeExpressions],
    );

    if (isLoading) {
        return (
            <Box
                display="flex"
                flex={1}
                justifyContent="center"
            >
                <CircularProgress
                    size={20}
                    data-testid="expression-builder-loading"
                />
            </Box>
        );
    }

    if (isError) {
        return <div>{strings('view:messages.error.loadingOperators')}</div>;
    }

    return (
        <FormField
            name={name}
            label={label}
            renderField={(_field) => (
                <ExpressionBuilder
                    columns={columns}
                    expressions={expressions}
                    onAppendExpression={appendExpressions}
                    onUpdateExpression={handleUpdateExpression}
                    onRemoveExpression={handleRemoveExpression}
                />
            )}
        />
    );
}
