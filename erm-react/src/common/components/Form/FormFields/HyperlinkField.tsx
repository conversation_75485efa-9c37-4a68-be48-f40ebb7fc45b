import React, { useState } from 'react';
import Form<PERSON>ield, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import Grid from '@mui/material/Grid';
import { strings } from 'common/utils/i18n';
import useFormContext from 'common/hooks/forms/useFormContext';
import { ColumnType, Hyperlink } from 'register/types';
import { StyledInput } from './DialogSelectorField';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import AddExternalLinkDialog from 'common/components/AddExternalLinkDialog';
import LinksList, { LinkListItemIcon, LinkListItemTitle } from '@protecht/ui-library/library/components/LinksList';
import { Globe } from '@protecht/ui-library/library/components/SVGIcons';
import useTheme from '@mui/system/useTheme';
import ConfirmationDialog from '@protecht/ui-library/library/components/ConfirmationDialog';
import { AlertType } from '@protecht/ui-library/library/types';

type Props = FormFieldBaseProps & {
    height?: string;
};

const DEFAULT_MAX_HEIGHT = '218';

const HyperlinkField: React.FC<Props> = ({ height, ...fieldProps }) => {
    const [linkDialogOpen, setLinkDialogOpen] = useState(false);
    const [linkForEdit, setLinkForEdit] = useState<Hyperlink | null | undefined>(undefined);
    const [editLinkIndex, setEditLinkIndex] = useState<number | undefined>(undefined);
    const [deleteHyperLinkData, setDeleteHyperLinkData] = useState<{ item: Hyperlink; index: number } | undefined>(undefined);

    const { watch } = useFormContext();
    const selectedValues: Hyperlink[] = watch(fieldProps.name);

    const theme = useTheme();

    const handleEditLink = (link: Hyperlink, index: number) => {
        setEditLinkIndex(index);
        setLinkForEdit(link);
        setLinkDialogOpen(true);
    };

    const handleAddLink = () => {
        if (fieldProps.readOnly) {
            return;
        }
        setLinkForEdit(null);
        setLinkDialogOpen(true);
    };

    const handleOpenLink = (link: Hyperlink) => {
        window.open(link.linkUrl, '_blank', 'noopener,noreferrer');
    };

    return (
        <FormField
            {...fieldProps}
            renderField={({ ref: _ref, ...field }) => {
                return (
                    <>
                        <Grid
                            container
                            spacing="10px"
                            display="flex"
                            flexWrap="nowrap"
                        >
                            <Grid
                                item
                                overflow="hidden"
                                width="100%"
                            >
                                {selectedValues?.length > 0 ? (
                                    <LinksList<Hyperlink>
                                        items={field.value}
                                        keyExtractor={(item, index) => `${index}${item.linkName}${item.linkUrl}`}
                                        onItemEdit={(item, index) => {
                                            handleEditLink(item, index);
                                        }}
                                        disableHover={true}
                                        maxHeight={Number(height || DEFAULT_MAX_HEIGHT)}
                                        renderContent={(item) => (
                                            <>
                                                <LinkListItemIcon
                                                    icon={<Globe data-testid={`links-list-icon-${ColumnType.HYPERLINK}`} />}
                                                    color={theme.palette.primary.main}
                                                />
                                                <LinkListItemTitle
                                                    title={
                                                        <span
                                                            onClick={() => handleOpenLink?.(item)}
                                                            style={{
                                                                cursor: fieldProps.readOnly ? 'default' : 'pointer',
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                                color: `${fieldProps.readOnly && theme.palette.protechtGrey?.grey_178}`,
                                                            }}
                                                        >
                                                            {item.linkName}
                                                        </span>
                                                    }
                                                    color={theme.palette.primary.main}
                                                    fontWeight={600}
                                                />
                                            </>
                                        )}
                                        onItemRemove={(itemToRemove, index) => {
                                            setDeleteHyperLinkData({ item: itemToRemove, index: index });
                                        }}
                                        removeType="remove"
                                        readonly={fieldProps.readOnly}
                                        clearTooltip={strings('common:button.clear')}
                                    />
                                ) : (
                                    <StyledInput
                                        leftBorder={false}
                                        placeholder={strings('common:placeholder.link')}
                                        inputProps={{ onClick: handleAddLink }}
                                        InputProps={{ readOnly: true }}
                                        dataTestId="hyperlink-input"
                                    />
                                )}
                            </Grid>
                            <Grid item>
                                {!fieldProps.readOnly && (
                                    <Button
                                        {...ButtonStyles.inputButton}
                                        variant="secondary"
                                        onClick={handleAddLink}
                                        dataTestId="hyperlink-select-button"
                                    >
                                        {strings('ermConstants:label_add')}
                                    </Button>
                                )}
                            </Grid>
                        </Grid>
                        {linkDialogOpen && linkForEdit !== undefined && (
                            <AddExternalLinkDialog
                                visible={true}
                                linkForEdit={linkForEdit}
                                onSubmit={(link) => {
                                    let newLinks: Hyperlink[];
                                    if (linkForEdit) {
                                        newLinks = selectedValues.map((selected, index) => (index === editLinkIndex ? link : selected));
                                    } else {
                                        newLinks = [...selectedValues, { linkUrl: link.linkUrl, linkName: link.linkName || link.linkUrl }];
                                    }
                                    field.onChange(newLinks);
                                }}
                                onClose={() => {
                                    setLinkForEdit(undefined);
                                    setEditLinkIndex(undefined);
                                    setLinkDialogOpen(false);
                                }}
                            />
                        )}
                        {deleteHyperLinkData && (
                            <ConfirmationDialog
                                visible={true}
                                type={AlertType.Error}
                                onClose={() => setDeleteHyperLinkData(undefined)}
                                onConfirm={() => {
                                    const newItems = selectedValues.filter((_, i) => i !== deleteHyperLinkData?.index);
                                    field.onChange(newItems);
                                    setDeleteHyperLinkData(undefined);
                                }}
                                title={strings('common:title.deleteExternalLink')}
                                message={strings('common:message.deleteExternalLinkMsg', { linkName: deleteHyperLinkData.item.linkName })}
                                buttonLabelCancel={strings('common:button.cancel')}
                                buttonLabelConfirm={strings('common:button.delete')}
                            />
                        )}
                    </>
                );
            }}
        />
    );
};
export default HyperlinkField;
