import { Attachment, FileDropzoneLabels } from '@protecht/ui-library/library/components/FileDropzone/types';
import { strings } from 'common/utils/i18n';

export const labels: FileDropzoneLabels = {
    inputPlaceholderMultiple: strings('common:attachments.inputPlaceholderMultiple'),
    inputPlaceholderSingle: strings('common:attachments.inputPlaceholderSingle'),
    buttonSingle: strings('common:attachments.select'),
    buttonMultiple: strings('common:attachments.add'),
    maximumSize: strings('common:attachments.maximumSize'),
    uploadDialog: {
        title: strings('common:attachments.uploadDialog.title'),
        buttonConfirm: strings('common:attachments.uploadDialog.buttonConfirm'),
        buttonCancel: strings('common:attachments.uploadDialog.buttonCancel'),
        uploadInProgress: strings('common:attachments.uploadDialog.uploadInProgress'),
        uploadComplete: strings('common:attachments.uploadDialog.uploadComplete'),
        uploadCancelled: strings('common:attachments.uploadDialog.uploadCancelled'),
        filesProgress: strings('common:attachments.uploadDialog.filesProgress'),
        errorSingle: strings('common:attachments.uploadDialog.errorSingle'),
        errorMultiple: strings('common:attachments.uploadDialog.errorMultiple'),
        uploadFailed: strings('common:attachments.uploadDialog.uploadFailed'),
        allSuccess: strings('common:attachments.uploadDialog.allSuccess'),
    },
    errors: {
        tooLargeFile: strings('common:attachments.errors.tooLargeFile'),
        tooSmallFile: strings('common:attachments.errors.tooSmallFile'),
        tooLargeFileUpload: strings('common:attachments.errors.tooLargeFileUpload'),
        tooSmallFileUpload: strings('common:attachments.errors.tooSmallFileUpload'),
        tooManyFiles: strings('common:attachments.errors.tooManyFiles'),
        uploadFailed: strings('common:attachments.errors.uploadFailed'),
        invalidFile: strings('common:attachments.errors.invalidFile'),
    },
    addAttachment: strings('common:attachments.addAttachment'),
    deleteAttachment: strings('common:button.delete'),
    clearAttachment: strings('common:button.clear'),
    addLink: strings('common:attachments.addLink'),
    internalLinkDialog: {
        addTitle: strings('common:attachments.internalLinkDialog.addTitle'),
        editTitle: strings('common:attachments.internalLinkDialog.editTitle'),
        buttonCancel: strings('common:attachments.internalLinkDialog.buttonCancel'),
        buttonAdd: strings('common:attachments.internalLinkDialog.buttonAdd'),
        label: strings('common:attachments.internalLinkDialog.label'),
        link: strings('common:attachments.internalLinkDialog.link'),
    },
    confirmDelete: {
        confirmDeleteAttachment: (_attachment: Attachment) => strings('common:attachments.confirmDeleteDialog.deleteAttachmentTitle'), // todo add condition according to att. type
        confirmationMessage: (attachment: Attachment) => strings('common:attachments.confirmDeleteDialog.confirmMessage', { name: attachment.name }),
        buttonCancel: strings('common:button.cancel'),
        buttonDelete: strings('common:button.delete'),
    },
    sorting: {
        buttonLabel: strings('common:attachments.sorting.buttonLabel'),
        options: {
            newToOld: strings('common:attachments.sorting.options.newToOld'),
            oldToNew: strings('common:attachments.sorting.options.oldToNew'),
            nameAZ: strings('common:attachments.sorting.options.nameAZ'),
            nameZA: strings('common:attachments.sorting.options.nameZA'),
        },
    },
};
