import React, { useCallback } from 'react';
import AttachmentFieldComponent, { AttachmentFieldProps as AttachmentFieldPropsUI } from '@protecht/ui-library/library/components/FormFields/AttachmentField';
import { labels } from './const';
import { useUploadTempAttachment1Mutation } from 'common/api/attachments';
import { getReactRoot, WORMS_CLIENT_APP_BASE_URL } from 'config';
import useFilesSettings from 'common/hooks/useFilesSettings';

export type AttachmentFieldProps = AttachmentFieldPropsUI & {
    uploadFiles?: boolean;
};

const AttachmentField: React.FC<AttachmentFieldProps> = ({ maxSize, uploadFiles = false, accept, ...props }) => {
    const { maxFileSize, allowedFileTypes } = useFilesSettings(maxSize, accept);
    const [uploadAttachment] = useUploadTempAttachment1Mutation({ selectFromResult: () => ({}) });

    const uploadHandler = useCallback(
        (file: File, { setAbortHandler }) => {
            const formData = new FormData();
            formData.append('file', file);
            const response = uploadAttachment(formData);
            setAbortHandler({ abort: response.abort });
            return response.unwrap();
        },
        [uploadAttachment],
    );

    const uploadHandlerMapData = useCallback((uuid: string) => ({ uuid }), []);

    return (
        <AttachmentFieldComponent
            {...props}
            appBaseUrl={WORMS_CLIENT_APP_BASE_URL}
            labels={labels}
            maxSize={maxFileSize}
            accept={allowedFileTypes}
            uploadHandler={uploadFiles ? uploadHandler : undefined}
            uploadHandlerMapData={uploadFiles ? uploadHandlerMapData : undefined}
            rootContainer={getReactRoot()}
        />
    );
};

export default AttachmentField;
