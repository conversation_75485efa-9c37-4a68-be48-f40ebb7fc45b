import React, { FC, useCallback, useState } from 'react';
import Box from '@mui/material/Box';
import { strings } from 'common/utils/i18n';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { StyledInput } from './DialogSelectorField';

type Props = {
    dataTestId?: string;
    placeholder?: string;
    readOnly?: boolean;
    overrideInputComponent?: React.ReactNode;
    renderDialog: (props) => React.ReactElement;
};

const DialogSelector: FC<Props> = ({ dataTestId, placeholder, readOnly, overrideInputComponent, renderDialog }) => {
    const [openedDialog, setOpenedDialog] = useState(false);

    const handleOnClick = useCallback(() => {
        if (!readOnly) {
            setOpenedDialog(true);
        }
    }, [readOnly]);

    return (
        <>
            <Box sx={{ display: 'flex', gap: '10px', maxWidth: '100%' }}>
                {overrideInputComponent ? (
                    overrideInputComponent
                ) : (
                    <StyledInput
                        placeholder={placeholder}
                        leftBorder={false}
                        dataTestId={`${dataTestId}-input`}
                        onClick={!readOnly ? handleOnClick : undefined}
                        inputProps={{ tabIndex: -1 }}
                        InputProps={{ readOnly: true }}
                    />
                )}
                {!readOnly && (
                    <Button
                        {...ButtonStyles.inputButton}
                        dataTestId={`${dataTestId}-select-button`}
                        onClick={handleOnClick}
                        variant="outlined"
                    >
                        {strings('ermConstants:label_add')}
                    </Button>
                )}
            </Box>

            {openedDialog &&
                renderDialog({
                    setOpen: () => {
                        setOpenedDialog(false);
                    },
                    onClose: () => {
                        setOpenedDialog(false);
                    },
                })}
        </>
    );
};

export default DialogSelector;
