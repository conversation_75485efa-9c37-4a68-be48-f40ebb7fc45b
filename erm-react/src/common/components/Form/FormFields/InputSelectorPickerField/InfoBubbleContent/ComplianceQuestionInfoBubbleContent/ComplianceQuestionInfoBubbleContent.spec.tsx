import React from 'react';
import { render, screen } from '@testing-library/react';
import ComplianceQuestionInfoBubbleContent from './ComplianceQuestionInfoBubbleContent';
import { strings } from 'common/utils/i18n';
import { IdOnly } from '@protecht/ui-library/library/types';

jest.mock('library/components/ComplianceQuestion/rtkApi', () => ({
    useCqrsGetControlUsingGetQuery: jest.fn(),
}));

const { useCqrsGetControlUsingGetQuery } = jest.requireMock('library/components/ComplianceQuestion/rtkApi');

describe('ComplianceQuestionInfoBubbleContent', () => {
    it('renders no details message when data fields are empty', () => {
        useCqrsGetControlUsingGetQuery.mockReturnValueOnce({
            data: { description: '' },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<ComplianceQuestionInfoBubbleContent selected={selected} />);

        expect(screen.getByText(strings('common:label.noDetails'))).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders data fields when provided', () => {
        useCqrsGetControlUsingGetQuery.mockReturnValueOnce({
            data: {
                description: '<p>Mocked Description</p>',
                tags: [
                    { id: 1, name: 'Tag1' },
                    { id: 2, name: 'Tag2' },
                ],
                defaultFrequency: 'W',
                name: 'Mocked Name',
            },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<ComplianceQuestionInfoBubbleContent selected={selected} />);

        expect(screen.getByText('Mocked Description')).toBeInTheDocument();
        expect(screen.queryByText('<p>Mocked Description</p>')).not.toBeInTheDocument();
        expect(screen.getByText('Tag1')).toBeInTheDocument();
        expect(screen.getByText('Tag2')).toBeInTheDocument();
        expect(screen.getByText('Mocked Name')).toBeInTheDocument();
        expect(screen.getByText('Weekly')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders loading spinner when data is loading', () => {
        useCqrsGetControlUsingGetQuery.mockReturnValueOnce({
            isLoading: true,
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<ComplianceQuestionInfoBubbleContent selected={selected} />);

        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
