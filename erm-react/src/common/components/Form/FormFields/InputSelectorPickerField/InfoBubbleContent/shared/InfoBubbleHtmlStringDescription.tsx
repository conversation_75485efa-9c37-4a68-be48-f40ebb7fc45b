import React from 'react';
import DOMPurify from 'dompurify';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';

type Props = {
    description?: string;
};

const InfoBubbleHtmlStringDescription: React.FC<Props> = ({ description }) => {
    const sanitizedDescription = DOMPurify.sanitize(description ?? '');

    return (
        description && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('common:label.description')}
                </Typography>
                <Typography dangerouslySetInnerHTML={{ __html: sanitizedDescription }}></Typography>
            </Grid>
        )
    );
};

export default InfoBubbleHtmlStringDescription;
