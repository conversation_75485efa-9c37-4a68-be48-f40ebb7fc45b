import React from 'react';
import { render, screen } from '@testing-library/react';
import AuditQuestionInfoBubbleContent from './AuditQuestionInfoBubbleContent';
import { strings } from 'common/utils/i18n';
import { IdOnly } from '@protecht/ui-library/library/types';

jest.mock('library/components/AuditQuestion/rtkApi', () => ({
    useAqrsGetQuestionUsingGetQuery: jest.fn(),
}));

const { useAqrsGetQuestionUsingGetQuery } = jest.requireMock('library/components/AuditQuestion/rtkApi');

describe('AuditQuestionInfoBubbleContent', () => {
    it('renders no details message when data fields are empty', () => {
        useAqrsGetQuestionUsingGetQuery.mockReturnValueOnce({
            data: { description: '', tags: [] },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<AuditQuestionInfoBubbleContent selected={selected} />);

        expect(screen.getByText(strings('common:label.noDetails'))).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders correct fields when provided', () => {
        useAqrsGetQuestionUsingGetQuery.mockReturnValueOnce({
            data: {
                description: '<p>Mocked Description</p>',
                // tags: [
                //     { id: 1, name: 'Tag1' },
                //     { id: 2, name: 'Tag2' },
                // ],
            },
        });

        const selected: IdOnly = {
            id: 1,
        };

        const { container } = render(<AuditQuestionInfoBubbleContent selected={selected} />);

        expect(screen.getByText('Mocked Description')).toBeInTheDocument();
        // expect(screen.getByText('Tag1')).toBeInTheDocument();
        // expect(screen.getByText('Tag2')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders loading spinner when data is loading', () => {
        useAqrsGetQuestionUsingGetQuery.mockReturnValueOnce({
            isLoading: true,
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<AuditQuestionInfoBubbleContent selected={selected} />);

        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
