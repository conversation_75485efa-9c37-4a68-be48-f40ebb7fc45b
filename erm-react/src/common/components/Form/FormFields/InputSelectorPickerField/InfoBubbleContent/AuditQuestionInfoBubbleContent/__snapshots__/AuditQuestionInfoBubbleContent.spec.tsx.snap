// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AuditQuestionInfoBubbleContent renders correct fields when provided 1`] = `
<div>
  <div
    class="MuiBox-root css-7hn4fx"
  >
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 MuiGrid-direction-xs-column css-1rl1og4-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
      >
        <p
          class="MuiTypography-root MuiTypography-body2 css-6ijeiu-MuiTypography-root"
        >
          Description
        </p>
        <p
          class="MuiTypography-root MuiTypography-body1 css-ahj2mt-MuiTypography-root"
        >
          <p>
            Mocked Description
          </p>
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`AuditQuestionInfoBubbleContent renders loading spinner when data is loading 1`] = `
<div>
  <div
    class="MuiBox-root css-zj2es6"
  >
    <svg
      aria-hidden="true"
      class="svg-inline--fa fa-spinner fa-spin"
      data-icon="spinner"
      data-prefix="fad"
      data-testid="loading-spinner"
      focusable="false"
      role="img"
      viewBox="0 0 512 512"
      width="24px"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g
        class="fa-duotone-group"
      >
        <path
          class="fa-secondary"
          d="M60.9 403.1a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM208 464a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM369.1 75A48 48 0 1 0 437 142.9 48 48 0 1 0 369.1 75zm0 294.2A48 48 0 1 0 437 437a48 48 0 1 0 -67.9-67.9zM416 256a48 48 0 1 0 96 0 48 48 0 1 0 -96 0z"
          fill="currentColor"
        />
        <path
          class="fa-primary"
          d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM96 256A48 48 0 1 0 0 256a48 48 0 1 0 96 0zM75 142.9A48 48 0 1 0 142.9 75 48 48 0 1 0 75 142.9z"
          fill="currentColor"
        />
      </g>
    </svg>
  </div>
</div>
`;

exports[`AuditQuestionInfoBubbleContent renders no details message when data fields are empty 1`] = `
<div>
  <div
    class="MuiBox-root css-zj2es6"
  >
    <p
      class="MuiTypography-root MuiTypography-body1 css-ahj2mt-MuiTypography-root"
    >
      No details to show
    </p>
  </div>
</div>
`;
