import React, { FC } from 'react';
import { useKrirsGetRiskLibraryUsingGetQuery } from 'library/components/Kri/rtkApi';
import InfoBubbleHtmlStringDescription from '../shared/InfoBubbleHtmlStringDescription';
import InfoBubbleTags from '../shared/InfoBubbleTags';
import { IdOnly } from 'app/types';
import InfoBubbleWrapper from '../shared/InfoBubbleWrapper';

type Props = { selected: IdOnly };

const KriInfoBubbleContent: FC<Props> = ({ selected }) => {
    const { data: kriDetail, isLoading } = useKrirsGetRiskLibraryUsingGetQuery({ kriLibraryId: selected.id });

    const description = kriDetail?.description;
    const tags = kriDetail?.tags ?? [];
    const isEmpty = !description && tags.length === 0;

    return (
        <InfoBubbleWrapper
            isEmpty={isEmpty}
            isLoading={isLoading}
        >
            <InfoBubbleHtmlStringDescription description={description} />
            <InfoBubbleTags tags={tags} />
        </InfoBubbleWrapper>
    );
};

export default KriInfoBubbleContent;
