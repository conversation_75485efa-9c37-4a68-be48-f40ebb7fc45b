import React from 'react';
import { render, screen } from '@testing-library/react';
import RiskEventInfoBubbleContent from './RiskEventInfoBubbleContent';
import { strings } from 'common/utils/i18n';
import { IdOnly } from 'app/types';

jest.mock('library/components/RiskEvent/rtkApi', () => ({
    useRersGetRiskEventUsingGetQuery: jest.fn(),
}));

const { useRersGetRiskEventUsingGetQuery } = jest.requireMock('library/components/RiskEvent/rtkApi');

describe('RiskEventInfoBubbleContent', () => {
    it('renders no details message when data fields are empty', () => {
        useRersGetRiskEventUsingGetQuery.mockReturnValueOnce({
            data: { description: '', tags: [], owner: null },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<RiskEventInfoBubbleContent selected={selected} />);

        expect(screen.getByText(strings('common:label.noDetails'))).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders correct fields when provided', () => {
        useRersGetRiskEventUsingGetQuery.mockReturnValueOnce({
            data: {
                description: '<p>Mocked Description</p>',
                tags: [
                    { id: 1, name: 'Tag1' },
                    { id: 2, name: 'Tag2' },
                ],
                owner: { name: 'Mocked Owner' },
            },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<RiskEventInfoBubbleContent selected={selected} />);

        expect(screen.getByText('Mocked Description')).toBeInTheDocument();
        expect(screen.getByText('Tag1')).toBeInTheDocument();
        expect(screen.getByText('Tag2')).toBeInTheDocument();
        expect(screen.getByText('Mocked Owner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders loading spinner when data is loading', () => {
        useRersGetRiskEventUsingGetQuery.mockReturnValueOnce({
            isLoading: true,
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<RiskEventInfoBubbleContent selected={selected} />);

        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
