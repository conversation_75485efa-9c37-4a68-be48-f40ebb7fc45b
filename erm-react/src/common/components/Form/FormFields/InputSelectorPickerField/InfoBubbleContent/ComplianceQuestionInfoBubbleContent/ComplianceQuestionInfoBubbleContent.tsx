import React, { FC } from 'react';
import InfoBubbleHtmlStringDescription from '../shared/InfoBubbleHtmlStringDescription';
import InfoBubbleTags from '../shared/InfoBubbleTags';
import { useCqrsGetControlUsingGetQuery } from 'library/components/ComplianceQuestion/rtkApi';
import InfoBubbleDefaultFrequency from '../shared/InfoBubbleDefaultFrequency';
import InfoBubbleName from '../shared/InfoBubbleName';
import { IdOnly } from '@protecht/ui-library/library/types';
import InfoBubbleWrapper from '../shared/InfoBubbleWrapper';

type Props = { selected: IdOnly };

const ComplianceQuestionInfoBubbleContent: FC<Props> = ({ selected }) => {
    const { data: complianceQuestionDetail, isLoading } = useCqrsGetControlUsingGetQuery({ complianceQuestionId: selected.id });

    const defaultFrequency = complianceQuestionDetail?.defaultFrequency;
    const description = complianceQuestionDetail?.description;
    const name = complianceQuestionDetail?.name;
    const tags = complianceQuestionDetail?.tags ?? [];

    const isEmpty = !defaultFrequency && !description && !name && tags.length === 0;

    return (
        <InfoBubbleWrapper
            isEmpty={isEmpty}
            isLoading={isLoading}
        >
            <InfoBubbleDefaultFrequency defaultFrequency={defaultFrequency} />
            <InfoBubbleHtmlStringDescription description={description} />
            <InfoBubbleName name={name} />
            <InfoBubbleTags tags={tags} />
        </InfoBubbleWrapper>
    );
};

export default ComplianceQuestionInfoBubbleContent;
