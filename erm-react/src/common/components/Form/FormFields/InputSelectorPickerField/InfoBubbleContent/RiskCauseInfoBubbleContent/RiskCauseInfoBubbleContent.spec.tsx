import React from 'react';
import { render, screen } from '@testing-library/react';
import RiskCauseInfoBubbleContent from './RiskCauseInfoBubbleContent';
import { strings } from 'common/utils/i18n';
import { IdOnly } from 'app/types';

jest.mock('library/components/RiskCause/rtkApi', () => ({
    useRcrsGetRiskCauseUsingGetQuery: jest.fn(),
}));

const { useRcrsGetRiskCauseUsingGetQuery } = jest.requireMock('library/components/RiskCause/rtkApi');

describe('RiskCauseInfoBubbleContent', () => {
    it('renders no details message when data fields are empty', () => {
        useRcrsGetRiskCauseUsingGetQuery.mockReturnValueOnce({
            data: { description: '', tags: [] },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<RiskCauseInfoBubbleContent selected={selected} />);

        expect(screen.getByText(strings('common:label.noDetails'))).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders correct fields when provided', () => {
        useRcrsGetRiskCauseUsingGetQuery.mockReturnValueOnce({
            data: {
                description: '<p>Mocked Description</p>',
                tags: [
                    { id: 1, name: 'Tag1' },
                    { id: 2, name: 'Tag2' },
                ],
            },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<RiskCauseInfoBubbleContent selected={selected} />);

        expect(screen.getByText('Mocked Description')).toBeInTheDocument();
        expect(screen.getByText('Tag1')).toBeInTheDocument();
        expect(screen.getByText('Tag2')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders loading spinner when data is loading', () => {
        useRcrsGetRiskCauseUsingGetQuery.mockReturnValueOnce({
            isLoading: true,
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<RiskCauseInfoBubbleContent selected={selected} />);

        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
