import React, { FC } from 'react';
import { useRcrsGetRiskCauseUsingGetQuery } from 'library/components/RiskCause/rtkApi';
import InfoBubbleHtmlStringDescription from '../shared/InfoBubbleHtmlStringDescription';
import InfoBubbleTags from '../shared/InfoBubbleTags';
import InfoBubbleWrapper from '../shared/InfoBubbleWrapper';
import { IdOnly } from '@protecht/ui-library/library/types';

type Props = { selected: IdOnly };

const RiskCauseInfoBubbleContent: FC<Props> = ({ selected }) => {
    const { data: riskCauseDetail, isLoading } = useRcrsGetRiskCauseUsingGetQuery({ riskCauseId: selected.id });

    const description = riskCauseDetail?.description;
    const tags = riskCauseDetail?.tags ?? [];
    const isEmpty = !description && tags.length === 0;

    return (
        <InfoBubbleWrapper
            isLoading={isLoading}
            isEmpty={isEmpty}
        >
            <InfoBubbleHtmlStringDescription description={description} />
            <InfoBubbleTags tags={tags} />
        </InfoBubbleWrapper>
    );
};

export default RiskCauseInfoBubbleContent;
