import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import { ComplianceFrequency } from 'library/types';
import { getComplianceFrequencyLabel } from 'library/utils';
import React from 'react';

type Props = {
    defaultFrequency?: string;
};

const InfoBubbleDefaultFrequency: React.FC<Props> = ({ defaultFrequency }) => {
    return (
        defaultFrequency && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('ermConstants:frequency')}
                </Typography>
                <Typography>{getComplianceFrequencyLabel(defaultFrequency as ComplianceFrequency)}</Typography>
            </Grid>
        )
    );
};

export default InfoBubbleDefaultFrequency;
