import React, { FC } from 'react';
import InfoBubbleHtmlStringDescription from '../shared/InfoBubbleHtmlStringDescription';
import InfoBubbleCentralRiskName from '../shared/InfoBubbleCentralRiskName';
import { useBtrsGetDiagramUsingGetQuery } from 'bowtie/rtkApi';
import { IdOnly } from 'app/types';
import InfoBubbleWrapper from '../shared/InfoBubbleWrapper';

type Props = { selected: IdOnly };

const BowtieInfoBubbleContent: FC<Props> = ({ selected }) => {
    const { data: bowtieDetail, isLoading } = useBtrsGetDiagramUsingGetQuery({ id: selected.id });

    const description = bowtieDetail?.description;
    const centralRiskName = bowtieDetail?.centralRiskName;

    const isEmpty = !centralRiskName && !description;

    return (
        <InfoBubbleWrapper
            isLoading={isLoading}
            isEmpty={isEmpty}
        >
            <InfoBubbleHtmlStringDescription description={description} />
            <InfoBubbleCentralRiskName centralRiskName={centralRiskName} />
        </InfoBubbleWrapper>
    );
};

export default BowtieInfoBubbleContent;
