import React from 'react';
import { render, screen } from '@testing-library/react';
import ControlInfoBubbleContent from './ControlInfoBubbleContent';
import { strings } from 'common/utils/i18n';
import { IdOnly } from 'app/types';

jest.mock('library/components/Control/rtkApi', () => ({
    useCrsGetControlUsingGetQuery: jest.fn(),
}));

const { useCrsGetControlUsingGetQuery } = jest.requireMock('library/components/Control/rtkApi');

describe('ControlInfoBubbleContent', () => {
    it('renders no details message when data fields are empty', () => {
        useCrsGetControlUsingGetQuery.mockReturnValueOnce({
            data: { description: '', tags: [], defaultFrequency: null },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<ControlInfoBubbleContent selected={selected} />);

        expect(screen.getByText(strings('common:label.noDetails'))).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders correct fields when provided', () => {
        useCrsGetControlUsingGetQuery.mockReturnValueOnce({
            data: {
                description: '<p>Mocked Description</p>',
                tags: [
                    { id: 1, name: 'Tag1' },
                    { id: 2, name: 'Tag2' },
                ],
                defaultFrequency: 'W',
            },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<ControlInfoBubbleContent selected={selected} />);

        expect(screen.getByText('Mocked Description')).toBeInTheDocument();
        expect(screen.getByText('Tag1')).toBeInTheDocument();
        expect(screen.getByText('Tag2')).toBeInTheDocument();
        expect(screen.getByText('Weekly')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders loading spinner when data is loading', () => {
        useCrsGetControlUsingGetQuery.mockReturnValueOnce({
            isLoading: true,
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<ControlInfoBubbleContent selected={selected} />);

        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
