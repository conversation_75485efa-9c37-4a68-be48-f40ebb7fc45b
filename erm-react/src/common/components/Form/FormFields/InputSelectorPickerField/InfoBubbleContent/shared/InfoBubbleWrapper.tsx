import React, { FC, ReactNode } from 'react';
import Typography from '@mui/material/Typography';
import Loader from '../shared/Loader';
import { StyledInfoBubbleContent, StyledNoDetailsContent } from '../StyledComponents';
import { strings } from 'common/utils/i18n';

type InfoBubbleWrapperProps = {
    isLoading: boolean;
    isEmpty: boolean;
    children: ReactNode;
};

const InfoBubbleWrapper: FC<InfoBubbleWrapperProps> = ({ isLoading, isEmpty, children }) => {
    if (isLoading) {
        return <Loader />;
    }

    if (isEmpty) {
        return (
            <StyledNoDetailsContent>
                <Typography>{strings('common:label.noDetails')}</Typography>
            </StyledNoDetailsContent>
        );
    }

    return <StyledInfoBubbleContent>{children}</StyledInfoBubbleContent>;
};

export default InfoBubbleWrapper;
