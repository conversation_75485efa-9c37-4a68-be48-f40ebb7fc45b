import React from 'react';
import { render, screen } from '@testing-library/react';
import InfoBubbleTags from './InfoBubbleTags';
import { strings } from 'common/utils/i18n';

describe('InfoBubbleTags', () => {
    it('renders tags correctly when tags are provided', () => {
        const tags = [
            { id: 1, name: 'Tag1' },
            { id: 2, name: 'Tag2' },
        ];

        const { container } = render(<InfoBubbleTags tags={tags} />);

        expect(screen.getByText(strings('common:label.tags'))).toBeInTheDocument();
        expect(screen.getByText('Tag1')).toBeInTheDocument();
        expect(screen.getByText('Tag2')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders nothing when no tags are provided', () => {
        const { container } = render(<InfoBubbleTags tags={[]} />);
        expect(container).toBeEmptyDOMElement();
    });
});
