import React from 'react';
import { render, screen } from '@testing-library/react';
import KriInfoBubbleContent from './KriInfoBubbleContent';
import { strings } from 'common/utils/i18n';
import { IdOnly } from 'app/types';

jest.mock('library/components/Kri/rtkApi', () => ({
    useKrirsGetRiskLibraryUsingGetQuery: jest.fn(),
}));

const { useKrirsGetRiskLibraryUsingGetQuery } = jest.requireMock('library/components/Kri/rtkApi');

describe('KriInfoBubbleContent', () => {
    it('renders no details message when data fields are empty', () => {
        useKrirsGetRiskLibraryUsingGetQuery.mockReturnValueOnce({
            data: { description: '', tags: [] },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<KriInfoBubbleContent selected={selected} />);

        expect(screen.getByText(strings('common:label.noDetails'))).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders correct fields when provided', () => {
        useKrirsGetRiskLibraryUsingGetQuery.mockReturnValueOnce({
            data: {
                description: '<p>Mocked Description</p>',
                tags: [
                    { id: 1, name: 'Tag1' },
                    { id: 2, name: 'Tag2' },
                ],
            },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<KriInfoBubbleContent selected={selected} />);

        expect(screen.getByText('Mocked Description')).toBeInTheDocument();
        expect(screen.getByText('Tag1')).toBeInTheDocument();
        expect(screen.getByText('Tag2')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders loading spinner when data is loading', () => {
        useKrirsGetRiskLibraryUsingGetQuery.mockReturnValueOnce({
            isLoading: true,
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<KriInfoBubbleContent selected={selected} />);

        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
