import React from 'react';
import { render, screen } from '@testing-library/react';
import InfoBubbleHtmlStringDescription from './InfoBubbleHtmlStringDescription';
import { strings } from 'common/utils/i18n';

describe('InfoBubbleHtmlStringDescription', () => {
    it('sanitizes the description correctly', () => {
        const maliciousDescription = '<img src="x" onerror="alert(\'not safe\')" />Safe text';
        const { container } = render(<InfoBubbleHtmlStringDescription description={maliciousDescription} />);

        expect(container.innerHTML).not.toContain('onerror');
        expect(container.innerHTML).toContain('Safe text');
    });

    it('renders the correct label and description and matches the snapshot', () => {
        const description = '<p>Test Description</p>';
        const { container } = render(<InfoBubbleHtmlStringDescription description={description} />);

        expect(screen.getByText(strings('common:label.description'))).toBeInTheDocument();
        expect(container.innerHTML).toContain('Test Description');
        expect(container).toMatchSnapshot();
    });

    it('renders nothing when no description is provided', () => {
        const { container } = render(<InfoBubbleHtmlStringDescription description={''} />);
        expect(container).toBeEmptyDOMElement();
    });
});
