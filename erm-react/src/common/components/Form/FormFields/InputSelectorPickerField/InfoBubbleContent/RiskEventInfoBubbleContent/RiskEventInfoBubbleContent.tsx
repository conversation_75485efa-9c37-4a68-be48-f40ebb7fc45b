import React, { FC } from 'react';
import { useRersGetRiskEventUsingGetQuery } from 'library/components/RiskEvent/rtkApi';
import { IdOnly } from 'app/types';
import InfoBubbleHtmlStringDescription from '../shared/InfoBubbleHtmlStringDescription';
import InfoBubbleTags from '../shared/InfoBubbleTags';
import InfoBubbleRiskOwner from '../shared/InfoBubbleRiskOwner';
import InfoBubbleWrapper from '../shared/InfoBubbleWrapper';

type Props = { selected: IdOnly };

const RiskEventInfoBubbleContent: FC<Props> = ({ selected }) => {
    const { data: riskEventDetail, isLoading } = useRersGetRiskEventUsingGetQuery({ riskEventId: selected.id });

    const description = riskEventDetail?.description;
    const riskOwner = riskEventDetail?.owner;
    const tags = riskEventDetail?.tags ?? [];
    const isEmpty = !description && !riskOwner && tags.length === 0;

    return (
        <InfoBubbleWrapper
            isLoading={isLoading}
            isEmpty={isEmpty}
        >
            <InfoBubbleHtmlStringDescription description={description} />
            <InfoBubbleRiskOwner riskOwner={riskOwner} />
            <InfoBubbleTags tags={tags} />
        </InfoBubbleWrapper>
    );
};

export default RiskEventInfoBubbleContent;
