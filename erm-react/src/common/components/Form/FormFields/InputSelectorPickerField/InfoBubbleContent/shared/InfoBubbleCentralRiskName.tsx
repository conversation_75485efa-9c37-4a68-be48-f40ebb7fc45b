import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import React from 'react';

type Props = {
    centralRiskName?: string;
};

const InfoBubbleCentralRiskName: React.FC<Props> = ({ centralRiskName }) => {
    return (
        centralRiskName && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('library:label.centralRiskName')}
                </Typography>
                <Typography>{centralRiskName}</Typography>
            </Grid>
        )
    );
};

export default InfoBubbleCentralRiskName;
