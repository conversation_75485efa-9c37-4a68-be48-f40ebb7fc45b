import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import React from 'react';

type Props = {
    description?: string;
};

const InfoBubbleDescription: React.FC<Props> = ({ description }) => {
    return (
        description && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('common:label.description')}
                </Typography>
                <Typography>{description}</Typography>
            </Grid>
        )
    );
};

export default InfoBubbleDescription;
