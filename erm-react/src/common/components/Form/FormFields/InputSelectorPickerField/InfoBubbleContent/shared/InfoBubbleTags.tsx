import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import ChipsArray from '@protecht/ui-library/library/components/ChipsArray';
import { IdWithNameRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import React from 'react';

type Props = {
    tags: IdWithNameRest[];
};

const InfoBubbleTags: React.FC<Props> = ({ tags }) => {
    return (
        tags?.length > 0 && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('common:label.tags')}
                </Typography>
                <ChipsArray
                    displayField="name"
                    data={tags}
                    keyExtractor={(item, index) => `${item.name}-${index}`}
                    readOnly={true}
                    sx={{
                        boxShadow: 'none !important',
                        border: '0 !important',
                        background: 'none',
                        padding: 0,
                    }}
                />
            </Grid>
        )
    );
};

export default InfoBubbleTags;
