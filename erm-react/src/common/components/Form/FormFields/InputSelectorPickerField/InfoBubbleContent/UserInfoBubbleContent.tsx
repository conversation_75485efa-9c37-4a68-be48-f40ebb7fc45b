import React, { ReactNode, useCallback, useEffect } from 'react';
import { ProtechtUserRest } from 'api/generated/types';
import Box from '@mui/material/Box';
import { BusinessUnit, Email, Mobile, Position, User, Vendor } from '@protecht/ui-library/library/components/SVGIcons';
import useTheme from '@mui/system/useTheme';
import { usePursGetUserUsingGetQuery } from 'user/rtkApi';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import { IdOnly } from 'app/types';
import useSnackbar from 'common/hooks/useSnackbar';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/pro-duotone-svg-icons';

type Props = {
    selected: IdOnly[];
};

const UserInfoBubbleContent: React.FC<Props> = ({ selected }) => {
    const theme = useTheme();
    const { enqueueError } = useSnackbar();

    const isFullUser = Boolean(
        selected.length > 0 &&
            selected[0].id &&
            (selected[0] as ProtechtUserRest).email &&
            (selected[0] as ProtechtUserRest).name &&
            (selected[0] as ProtechtUserRest).businessUnit?.name,
    );

    const { data: userData, isError, isLoading } = usePursGetUserUsingGetQuery({ userId: selected[0].id }, { skip: selected.length === 0 || isFullUser });

    useEffect(() => {
        if (isError) {
            enqueueError(strings('user:message.errorLoadingUsers'));
        }
    }, [enqueueError, isError]);

    const renderUserNode = useCallback(
        (icon: ReactNode, property: string, description?: string) => {
            return (
                <Box
                    display="flex"
                    alignItems="center"
                    height="38px"
                    key={property}
                >
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginRight: '10px', width: '24px' }}>{icon}</Box>
                    <Typography
                        variant="body1"
                        marginRight="10px"
                        color={theme.palette.protechtGrey.darkBlack}
                    >
                        {property}
                    </Typography>

                    {description && (
                        <Typography
                            variant="body1"
                            color={theme.palette.protechtGrey.grey_146}
                            noWrap
                        >
                            {description}
                        </Typography>
                    )}
                </Box>
            );
        },
        [theme],
    );

    const renderUserInfo = useCallback(
        (userData: ProtechtUserRest) => {
            const userInfoBubbleDef = [
                {
                    icon: (
                        <Email
                            color={theme.palette.protechtGrey.grey_146}
                            width={20}
                            height={20}
                        />
                    ),
                    property: userData.email,
                },
                {
                    icon: <Mobile color={theme.palette.protechtGrey.grey_146} />,
                    property: userData.mobile,
                },
                {
                    icon: <Position color={theme.palette.protechtGrey.grey_146} />,
                    property: userData.position,
                    description: strings('user:label.position'),
                },
                {
                    icon: <User color={theme.palette.protechtGrey.grey_146} />,
                    property: userData.manager?.name,
                    description: strings('user:label.manager'),
                },
                userData.categoryVector === 1
                    ? {
                          icon: <BusinessUnit color={theme.palette.protechtGrey.grey_146} />,
                          property: userData.businessUnit?.name,
                          description: strings('user:label.primaryBusinessUnit'),
                      }
                    : {
                          icon: <Vendor color={theme.palette.protechtGrey.grey_146} />,
                          property: userData.businessUnit?.name,
                          description: strings('vrm:title.vendor'),
                      },
            ];

            return (
                <Box
                    display="flex"
                    flexDirection="column"
                >
                    {userInfoBubbleDef.map((item) => {
                        if (item.property) {
                            return renderUserNode(item.icon, item.property, item.description);
                        }
                    })}
                </Box>
            );
        },
        [renderUserNode, theme.palette.protechtGrey.grey_146],
    );

    return (
        <Box
            display="flex"
            flexDirection="column"
            minWidth="230px"
            alignItems="center"
        >
            {(isFullUser && selected?.[0]) || (userData && !isLoading) ? (
                renderUserInfo(userData || (selected[0] as ProtechtUserRest))
            ) : (
                <FontAwesomeIcon
                    icon={faSpinner}
                    className={'fa-spin'}
                    color={theme.palette.protechtGrey.grey_146}
                    width="24px"
                />
            )}
        </Box>
    );
};

export default UserInfoBubbleContent;
