// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InfoBubbleHtmlStringDescription renders the correct label and description and matches the snapshot 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
  >
    <p
      class="MuiTypography-root MuiTypography-body2 css-6ijeiu-MuiTypography-root"
    >
      Description
    </p>
    <p
      class="MuiTypography-root MuiTypography-body1 css-ahj2mt-MuiTypography-root"
    >
      <p>
        Test Description
      </p>
    </p>
  </div>
</div>
`;
