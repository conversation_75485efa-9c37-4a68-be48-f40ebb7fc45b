import React from 'react';
import { render, screen } from '@testing-library/react';
import BowtieInfoBubbleContent from './BowtieInfoBubbleContent';
import { strings } from 'common/utils/i18n';
import { IdOnly } from 'app/types';

jest.mock('bowtie/rtkApi', () => ({
    useBtrsGetDiagramUsingGetQuery: jest.fn(),
}));

const { useBtrsGetDiagramUsingGetQuery } = jest.requireMock('bowtie/rtkApi');

describe('BowtieInfoBubbleContent', () => {
    it('renders no details message when data fields are empty', () => {
        useBtrsGetDiagramUsingGetQuery.mockReturnValueOnce({
            data: { description: '', centralRiskName: '' },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<BowtieInfoBubbleContent selected={selected} />);

        expect(screen.getByText(strings('common:label.noDetails'))).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders correct fields when provided', () => {
        useBtrsGetDiagramUsingGetQuery.mockReturnValueOnce({
            data: {
                description: '<p>Mocked Description</p>',
                centralRiskName: 'Mocked Central Risk Name',
            },
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<BowtieInfoBubbleContent selected={selected} />);

        expect(screen.getByText('Mocked Description')).toBeInTheDocument();
        expect(screen.getByText('Mocked Central Risk Name')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('renders loading spinner when data is loading', () => {
        useBtrsGetDiagramUsingGetQuery.mockReturnValueOnce({
            isLoading: true,
        });

        const selected: IdOnly = { id: 1 };

        const { container } = render(<BowtieInfoBubbleContent selected={selected} />);

        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
