import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/pro-duotone-svg-icons';
import { useTheme } from '@mui/material/styles';
import { StyledNoDetailsContent } from '../StyledComponents';

const Loader: React.FC = () => {
    const theme = useTheme();

    return (
        <StyledNoDetailsContent>
            <FontAwesomeIcon
                icon={faSpinner}
                className={'fa-spin'}
                color={theme.palette.protechtGrey?.grey_146}
                width="24px"
                data-testid="loading-spinner"
            />
        </StyledNoDetailsContent>
    );
};

export default Loader;
