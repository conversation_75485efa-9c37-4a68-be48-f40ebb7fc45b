import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import React, { PropsWithChildren } from 'react';

export const StyledInfoBubbleContent: React.FC<PropsWithChildren> = ({ children }) => {
    return (
        <Box sx={{ width: '330px', padding: '8px 0' }}>
            <Grid
                container
                spacing={2}
                direction="column"
            >
                {children}
            </Grid>
        </Box>
    );
};

export const StyledNoDetailsContent: React.FC<PropsWithChildren> = ({ children }) => {
    return (
        <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            minWidth="155px"
            padding="8px 0"
        >
            {children}
        </Box>
    );
};
