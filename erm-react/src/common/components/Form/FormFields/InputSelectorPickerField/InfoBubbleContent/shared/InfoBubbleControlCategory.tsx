import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { IdWithNameRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import React from 'react';

type Props = {
    controlCategory?: IdWithNameRest;
};

const InfoBubbleControlCategory: React.FC<Props> = ({ controlCategory }) => {
    return (
        controlCategory && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('library:label.category')}
                </Typography>
                <Typography>{controlCategory.name}</Typography>
            </Grid>
        )
    );
};

export default InfoBubbleControlCategory;
