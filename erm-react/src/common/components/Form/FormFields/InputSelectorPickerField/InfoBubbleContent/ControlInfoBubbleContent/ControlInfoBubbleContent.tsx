import React, { FC } from 'react';
import { useCrsGetControlUsingGetQuery } from 'library/components/Control/rtkApi';
import InfoBubbleHtmlStringDescription from '../shared/InfoBubbleHtmlStringDescription';
import InfoBubbleTags from '../shared/InfoBubbleTags';
import InfoBubbleDefaultFrequency from '../shared/InfoBubbleDefaultFrequency';
import { IdOnly } from 'app/types';
import InfoBubbleWrapper from '../shared/InfoBubbleWrapper';

type Props = { selected: IdOnly };

const ControlInfoBubbleContent: FC<Props> = ({ selected }) => {
    const { data: controlDetail, isLoading } = useCrsGetControlUsingGetQuery({ riskControlId: selected.id });

    const description = controlDetail?.description;
    const tags = controlDetail?.tags ?? [];
    const defaultFrequency = controlDetail?.defaultFrequency;
    const isEmpty = !description && !defaultFrequency && tags.length === 0;

    return (
        <InfoBubbleWrapper
            isLoading={isLoading}
            isEmpty={isEmpty}
        >
            <InfoBubbleHtmlStringDescription description={description} />
            <InfoBubbleDefaultFrequency defaultFrequency={defaultFrequency} />
            <InfoBubbleTags tags={tags} />
        </InfoBubbleWrapper>
    );
};

export default ControlInfoBubbleContent;
