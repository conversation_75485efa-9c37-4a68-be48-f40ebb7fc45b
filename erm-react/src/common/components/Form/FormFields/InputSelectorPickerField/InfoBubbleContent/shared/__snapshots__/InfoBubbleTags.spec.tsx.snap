// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InfoBubbleTags renders tags correctly when tags are provided 1`] = `
<div>
  <div
    class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
  >
    <p
      class="MuiTypography-root MuiTypography-body2 css-6ijeiu-MuiTypography-root"
    >
      Tags
    </p>
    <div
      style="margin: 0px;"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 css-1xfwe41-MuiPaper-root"
        tabindex="-1"
      >
        <div
          class="MuiChip-root MuiChip-filled MuiChip-sizeMedium MuiChip-colorPrimary MuiChip-filledPrimary css-1hutku-MuiChip-root"
          tabindex="-1"
        >
          <span
            class="MuiChip-label MuiChip-labelMedium css-6od3lo-MuiChip-label"
          >
            Tag1
          </span>
        </div>
        <div
          class="MuiChip-root MuiChip-filled MuiChip-sizeMedium MuiChip-colorPrimary MuiChip-filledPrimary css-1hutku-MuiChip-root"
          tabindex="-1"
        >
          <span
            class="MuiChip-label MuiChip-labelMedium css-6od3lo-MuiChip-label"
          >
            Tag2
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;
