import React, { ReactElement, useCallback, useEffect } from 'react';
import { IdOnly } from 'app/types';
import { BusinessUnitSimpleRest } from 'api/generated/types';
import Box from '@mui/material/Box';
import { BusinessUnit } from '@protecht/ui-library/library/components/SVGIcons';
import useTheme from '@mui/system/useTheme';
import { useGetBusinessUnitsByIdsQuery } from 'common/api/businessUnits';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/pro-duotone-svg-icons';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import useSnackbar from 'common/hooks/useSnackbar';
import { strings } from 'common/utils/i18n';

type Props = {
    selected: IdOnly[];
};

const BusinessUnitInfoBubbleContent = ({ selected }: Props) => {
    const { enqueueError } = useSnackbar();
    const theme = useTheme();
    const { data: BUData, isLoading, isError } = useGetBusinessUnitsByIdsQuery({ body: selected.map((item) => item.id!) });
    const isSmallerScreen = useMediaQuery('(max-width:500px)');

    useEffect(() => {
        if (isError) {
            enqueueError(strings('library:message.errorLoadingBUs'));
        }
    }, [enqueueError, isError]);

    const renderBUNode = useCallback(
        (node: BusinessUnitSimpleRest, level: number) => {
            return (
                <Box
                    display="flex"
                    alignContent="center"
                    height="38px"
                    minWidth="230px"
                    key={node.id}
                >
                    <Box
                        display="flex"
                        alignItems="center"
                        paddingLeft={`${level * 28}px`}
                        width="100%"
                    >
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginRight: '10px' }}>
                            <BusinessUnit color={theme.palette.protechtGrey.grey_146} />
                        </Box>
                        <Typography noWrap>{node.name}</Typography>
                    </Box>
                </Box>
            );
        },
        [theme],
    );

    const renderBUInfo = useCallback(
        (node: BusinessUnitSimpleRest, level = 0, arr: ReactElement[] = []): React.JSX.Element[] => {
            arr.push(renderBUNode(node, level));
            if (Array.isArray(node.children) && node.children.length > 0) {
                node.children.forEach((child) => renderBUInfo(child, level + 1, arr));
            }
            return arr;
        },
        [renderBUNode],
    );

    return BUData?.businessUnitSimpleRests?.[0] && !isLoading ? (
        <Box
            minWidth={isSmallerScreen ? '350px' : '100%'}
            width={isSmallerScreen ? '350px' : '100%'}
        >
            {renderBUInfo(BUData?.businessUnitSimpleRests?.[0])}
        </Box>
    ) : (
        <Box
            display="flex"
            flexDirection="column"
            minWidth="350px"
            alignItems="center"
        >
            <FontAwesomeIcon
                icon={faSpinner}
                className={'fa-spin'}
                color={theme.palette.protechtGrey.grey_146}
                width="24px"
            />
        </Box>
    );
};

export default BusinessUnitInfoBubbleContent;
