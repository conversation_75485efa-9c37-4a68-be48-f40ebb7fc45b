import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { IdWithNameRest } from 'api/generated/types';
import { strings } from 'common/utils/i18n';
import React from 'react';

type Props = {
    riskOwner?: IdWithNameRest;
};

const InfoBubbleRiskOwner: React.FC<Props> = ({ riskOwner }) => {
    return (
        riskOwner && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('library:label.riskOwner')}
                </Typography>
                <Typography>{riskOwner.name}</Typography>
            </Grid>
        )
    );
};

export default InfoBubbleRiskOwner;
