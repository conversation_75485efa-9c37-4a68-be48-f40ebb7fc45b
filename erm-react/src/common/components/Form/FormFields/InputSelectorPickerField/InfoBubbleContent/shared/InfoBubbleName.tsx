import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import React from 'react';

type Props = {
    name?: string;
};

const InfoBubbleName: React.FC<Props> = ({ name }) => {
    return (
        name && (
            <Grid item>
                <Typography
                    variant="body2"
                    minHeight="24px"
                    marginBottom="2px"
                >
                    {strings('common:label.name')}
                </Typography>
                <Typography>{name}</Typography>
            </Grid>
        )
    );
};

export default InfoBubbleName;
