import React, { FC } from 'react';
import { useAqrsGetQuestionUsingGetQuery } from 'library/components/AuditQuestion/rtkApi';
import InfoBubbleHtmlStringDescription from '../shared/InfoBubbleHtmlStringDescription';
import InfoBubbleTags from '../shared/InfoBubbleTags';
import InfoBubbleWrapper from '../shared/InfoBubbleWrapper';
import { IdOnly } from '@protecht/ui-library/library/types';

type Props = { selected: IdOnly };

const AuditQuestionInfoBubbleContent: FC<Props> = ({ selected }) => {
    const { data: auditQuestionDetail, isLoading } = useAqrsGetQuestionUsingGetQuery({ questionId: selected.id });

    const description = auditQuestionDetail?.description;
    // const tags = auditQuestionDetail?.tags ?? []; // model does not contain tags currently
    const tags = [];
    const isEmpty = !description && tags.length === 0;

    return (
        <InfoBubbleWrapper
            isLoading={isLoading}
            isEmpty={isEmpty}
        >
            <InfoBubbleHtmlStringDescription description={description} />
            <InfoBubbleTags tags={tags} />
        </InfoBubbleWrapper>
    );
};

export default AuditQuestionInfoBubbleContent;
