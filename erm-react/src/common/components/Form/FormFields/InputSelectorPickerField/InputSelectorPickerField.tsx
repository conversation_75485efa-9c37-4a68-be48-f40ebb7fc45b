import React, { useCallback } from 'react';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import InputSelector from 'common/components/InputSelector';
import { ColumnType } from '@protecht/ui-library/library/types';
import { IdWithNameAndStatusRest } from 'app/types';
import LinksListBuilder from 'common/components/LinksListBuilder';
import { InputProps } from '@mui/material/Input';

export type InputSelectorPickerFieldProps<T> = FormFieldBaseProps & {
    filterData?: any;
    multiselect?: boolean;
    preferArrayForSingleSelect?: boolean;
    placeholder?: string;
    selectedItems?: T | T[] | null;
    showSelectButton?: boolean;
    type: ColumnType;
    lookupDisabled?: boolean;
    disabled?: boolean;
    onCreateNew?: () => void;
    onSelectedItemClick?: (item: T) => void;
    onSelect?: (value: T | T[] | null) => void;
    onDataLoad?: (query: string) => Promise<T[]>;
    onDataClear?: () => void;
    valueMapping?: (item: T) => any;
    inputProps?: Partial<InputProps>;
    startIcon?: React.ReactNode;
};

const InputSelectorPickerField = <T extends IdWithNameAndStatusRest>(props: InputSelectorPickerFieldProps<T>) => {
    const {
        filterData,
        multiselect = false,
        placeholder,
        readOnly,
        selectedItems = [],
        showSelectButton = true,
        type,
        lookupDisabled = false,
        // Temporary prop to handle cases where arrays are also used for single selectors.
        // This helps prevent breaking existing functionality in other fields using this component.
        // TODO: Remove this prop once single selectors no longer use arrays.
        preferArrayForSingleSelect = true,
        disabled = false,
        valueMapping,
        onCreateNew,
        onDataLoad,
        onDataClear,
        onSelectedItemClick,
        onSelect,
        inputProps,
        startIcon,
        ...formFieldProps
    } = props;

    const handleNewValues = useCallback(
        (items: T[], onChange: (e: any) => void) => {
            const newItems = valueMapping ? items.map(valueMapping) : items;
            if (multiselect === false && !preferArrayForSingleSelect) {
                const newValue = newItems[0] || null;
                onChange(newValue);
                onSelect?.(newValue);
            } else {
                onChange(newItems);
                onSelect?.(newItems);
            }
        },
        [valueMapping, onSelect, preferArrayForSingleSelect, multiselect],
    );

    const selectedInputOverride = useCallback(
        (onChange: (e: any) => void) => {
            return (
                <LinksListBuilder
                    selected={!selectedItems ? [] : Array.isArray(selectedItems) ? selectedItems : [selectedItems]}
                    type={type}
                    readOnly={readOnly}
                    onItemRemove={(itemToRemove) => {
                        const newItems = Array.isArray(selectedItems) ? selectedItems.filter((item) => item.id !== itemToRemove.id) : [];
                        handleNewValues(newItems, onChange);
                    }}
                    disableHover={true}
                    onSelectedItemClick={onSelectedItemClick}
                    disabled={disabled}
                    startIcon={startIcon}
                />
            );
        },
        [onSelectedItemClick, readOnly, selectedItems, type, disabled, startIcon, handleNewValues],
    );

    return (
        <FormField
            {...formFieldProps}
            renderField={({ ref: _ref, value, onChange, ...field }) => {
                if (preferArrayForSingleSelect && (!value || value.length === 0) && Array.isArray(selectedItems) && selectedItems.length > 0) {
                    onSelect?.([]);
                }

                return (
                    <InputSelector<T>
                        {...field}
                        displayValue={Array.isArray(selectedItems) ? selectedItems[0]?.name : selectedItems?.name}
                        selected={Array.isArray(selectedItems) ? selectedItems : selectedItems ? [selectedItems] : []}
                        type={type}
                        multiselect={multiselect}
                        placeholder={placeholder}
                        onSelect={(items: T[]) => {
                            handleNewValues(items, onChange);
                        }}
                        disabled={disabled}
                        onCreateNew={onCreateNew}
                        selectedInputOverride={selectedInputOverride(onChange)}
                        filterData={filterData}
                        readOnly={readOnly}
                        showSelectButton={showSelectButton}
                        onDataLoad={onDataLoad}
                        onDataClear={onDataClear}
                        lookupDisabled={lookupDisabled}
                        inputProps={inputProps}
                    ></InputSelector>
                );
            }}
        />
    );
};

export default InputSelectorPickerField;
