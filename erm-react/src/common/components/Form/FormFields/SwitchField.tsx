import React from 'react';
import <PERSON><PERSON>ield, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import Switch from '@protecht/ui-library/library/components/Switch';

export default function SwitchField({ disabled, ...formFieldProps }: FormFieldBaseProps) {
    return (
        <FormField
            {...formFieldProps}
            disabled={disabled}
            renderField={({ value, ...field }) => (
                <Switch
                    {...field}
                    checked={value}
                    disabled={disabled}
                />
            )}
        />
    );
}
