import React from 'react';
import SensitiveInputFieldComponent, { SensitiveInputFieldProps } from '@protecht/ui-library/library/components/FormFields/SensitiveInputField';

import { labels } from 'common/components/SensitiveInput/const';

const SensitiveInputField: React.FC<SensitiveInputFieldProps> = (props) => {
    return (
        <SensitiveInputFieldComponent
            labels={labels}
            {...props}
        />
    );
};

export default SensitiveInputField;
