import React from 'react';
import { render, screen } from 'test/utils';
import SensitiveInputField from './SensitiveInputField';
import { labels as expectedLabels } from 'common/components/SensitiveInput/const';
import type { SensitiveInputFieldProps } from '@protecht/ui-library/library/components/FormFields/SensitiveInputField';

const MockSensitiveInputFieldComponent = jest.fn((props: SensitiveInputFieldProps) => (
    <span
        data-testid="mock-sensitive-input"
        {...props}
    />
));

jest.mock('@protecht/ui-library/library/components/FormFields/SensitiveInputField', () => ({
    __esModule: true,
    default: (props: SensitiveInputFieldProps) => MockSensitiveInputFieldComponent(props),
}));

describe('SensitiveInputField Wrapper', () => {
    beforeEach(() => {
        MockSensitiveInputFieldComponent.mockClear();
    });

    it('renders the underlying SensitiveInputFieldComponent', () => {
        render(<SensitiveInputField id="test-field" />);
        expect(screen.getByTestId('mock-sensitive-input')).toBeInTheDocument();
    });

    it('passes the correct labels prop to the underlying component', () => {
        render(<SensitiveInputField id="test-field" />);
        expect(MockSensitiveInputFieldComponent).toHaveBeenCalledTimes(1);
        const actualProps = MockSensitiveInputFieldComponent.mock.calls[0][0];
        expect(actualProps.labels).toEqual(expectedLabels);
    });

    it('passes other props down to the underlying component', () => {
        const onClick = jest.fn();
        const testProps = {
            id: 'test-field',
            onClick: onClick,
        };

        render(<SensitiveInputField {...testProps} />);

        expect(MockSensitiveInputFieldComponent).toHaveBeenCalledTimes(1);
        const actualProps = MockSensitiveInputFieldComponent.mock.calls[0][0];

        expect(actualProps.id).toBe(testProps.id);
        expect(actualProps.onClick).toBe(testProps.onClick);
    });
});
