import { Attachment, FileDropzoneLabels } from '@protecht/ui-library/library/components/FileDropzone/types';
import { ImageGalleryItem, ImageGalleryLabels } from '@protecht/ui-library/library/components/ImageGallery/types';
import { labels as attachmentLabels } from 'common/components/Form/FormFields/AttachmentField/const';
import { strings } from 'common/utils/i18n';

export const labels: FileDropzoneLabels = {
    ...attachmentLabels,
    inputPlaceholderSingle: strings('common:imageGallery.inputPlaceholderSingle'),
    inputPlaceholderMultiple: strings('common:imageGallery.inputPlaceholderMultiple'),
    uploadDialog: {
        ...attachmentLabels.uploadDialog,
        title: strings('common:imageGallery.uploadDialog.title'),
        uploadInProgress: strings('common:imageGallery.uploadDialog.uploadInProgress'),
        allSuccess: strings('common:imageGallery.uploadDialog.allSuccess'),
    },
    confirmDelete: {
        ...attachmentLabels.confirmDelete,
        confirmationMessage: (attachment: Attachment) => strings('common:imageGallery.confirmDeleteDialog.confirmMessage', { name: attachment.name }),
        confirmDeleteAttachment: (_attachment: Attachment) => strings('common:imageGallery.confirmDeleteDialog.deleteAttachmentTitle'), // todo add condition according to att. type,
    },
    sorting: {
        ...attachmentLabels.sorting,
        options: {
            ...attachmentLabels.sorting.options,
            nameAZ: strings('common:imageGallery.sorting.options.nameAZ'),
            nameZA: strings('common:imageGallery.sorting.options.nameZA'),
        },
    },
};

export const galleryLabels: ImageGalleryLabels = {
    buttonClose: strings('common:button.close'),
    confirmDelete: {
        title: strings('common:imageGallery.confirmDeleteDialog.deleteAttachmentTitle'),
        message: (item: ImageGalleryItem) => strings('common:imageGallery.confirmDeleteDialog.confirmMessage', { name: item.description }),
        buttonCancel: strings('common:button.cancel'),
        buttonDelete: strings('common:button.delete'),
    },
};
