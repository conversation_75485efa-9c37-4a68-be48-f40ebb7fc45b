import React, { useCallback } from 'react';
import ImageGalleryFieldComponent, {
    ImageGalleryFieldProps as ImageGalleryFieldPropsUI,
} from '@protecht/ui-library/library/components/FormFields/ImageGalleryField';
import { usePursGetCurrentUserUsingGetQuery } from 'user/rtkApi';
import { labels, galleryLabels } from './const';
import { useUploadTempAttachment1Mutation } from 'common/api/attachments';
import useFilesSettings from 'common/hooks/useFilesSettings';
import useUserDateTimeFormat from 'common/hooks/useUserDateTimeFormat';

export type ImageGalleryFieldProps = ImageGalleryFieldPropsUI & {
    uploadFiles?: boolean;
};

const ImageGalleryField: React.FC<ImageGalleryFieldProps> = ({ maxSize, uploadFiles = false, accept, ...props }) => {
    const { maxFileSize, allowedFileTypes } = useFilesSettings(maxSize, accept);
    const { data: currentUser } = usePursGetCurrentUserUsingGetQuery();
    const dateTimeFormat = useUserDateTimeFormat();

    const [uploadAttachment] = useUploadTempAttachment1Mutation({ selectFromResult: () => ({}) });

    const uploadHandler = useCallback(
        (file: File, { setAbortHandler }) => {
            const formData = new FormData();
            formData.append('file', file);
            const response = uploadAttachment(formData);
            setAbortHandler({ abort: response.abort });
            return response.unwrap();
        },
        [uploadAttachment],
    );

    const uploadHandlerMapData = useCallback((uuid: string) => ({ uuid }), []);

    return (
        <ImageGalleryFieldComponent
            {...props}
            labels={labels}
            galleryLabels={galleryLabels}
            maxSize={maxFileSize}
            accept={allowedFileTypes}
            createdBy={currentUser?.name}
            dateTimeFormat={dateTimeFormat}
            uploadHandler={uploadFiles ? uploadHandler : undefined}
            uploadHandlerMapData={uploadFiles ? uploadHandlerMapData : undefined}
        />
    );
};

export default ImageGalleryField;
