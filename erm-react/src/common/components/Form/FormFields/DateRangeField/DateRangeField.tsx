import React, { useEffect, useLayoutEffect, useState } from 'react';
import Form<PERSON>ield, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { DATE_RANGE_OPTIONS } from './const';
import Grid from '@mui/material/Grid';
import { getDuration } from 'view/utils';
import { ControllerRenderProps } from 'react-hook-form';
import { DurationType } from 'view/types';
import NumberInput from '@protecht/ui-library/library/components/NumberInput';
import Select from '@protecht/ui-library/library/components/Inputs/Select';

const DateRangeFieldComponent: React.FC<ControllerRenderProps<any>> = (field) => {
    const [isInitialized, setIsInitialized] = useState(false);
    const [count, setCount] = useState<string | undefined>('');
    const [duration, setDuration] = useState<DurationType | undefined>();

    useEffect(() => {
        if (!isInitialized && !field.value && typeof duration === 'undefined') {
            const duration = DATE_RANGE_OPTIONS[0].value;
            setDuration(duration);
        }
    }, [duration, field, isInitialized]);

    useEffect(() => {
        // load initial values from BE, parse values
        if (!isInitialized && field.value) {
            const { count: newCount, duration: newDuration } = getDuration(field.value);
            setCount(newCount);
            setDuration(newDuration);

            setIsInitialized(true);
        }
    }, [field.value, isInitialized]);

    useLayoutEffect(() => {
        if (isInitialized && !field.value) {
            setIsInitialized(false);
            setCount('');
            setDuration(undefined);
        }
    }, [field.value, isInitialized]);

    const handleChange = (count: string | undefined, duration: string | undefined) => {
        if (count !== undefined && count !== null && count !== '' && duration) {
            field.onChange(`\${${duration}}${count}`);
        } else {
            field.onChange('');
        }
    };

    return (
        <Grid
            container
            direction="row"
            gap={1}
        >
            <Grid
                item
                flex={1}
            >
                <NumberInput
                    value={count}
                    onChange={(value) => {
                        setCount(value);
                        handleChange(value, duration);
                    }}
                />
            </Grid>
            <Grid
                item
                flex={1}
            >
                <Select
                    sx={{ width: '100%' }}
                    variant="outlined"
                    displayEmpty
                    options={DATE_RANGE_OPTIONS}
                    value={duration}
                    onChange={(newDurationValue: DurationType) => {
                        setDuration(newDurationValue);
                        handleChange(count, newDurationValue);
                    }}
                />
            </Grid>
        </Grid>
    );
};

const DateRangeField: React.FC<FormFieldBaseProps> = (props) => {
    return (
        <FormField
            {...props}
            renderField={(field) => <DateRangeFieldComponent {...field} />}
        />
    );
};

export default DateRangeField;
