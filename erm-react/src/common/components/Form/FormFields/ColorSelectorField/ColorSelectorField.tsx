import React, { useCallback, useEffect, useMemo, useState } from 'react';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import { strings } from 'common/utils/i18n';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import useTheme from '@mui/system/useTheme';
import Box from '@mui/system/Box';
import ContextMenu from '@protecht/ui-library/library/components/ContextMenu';
import Button from '@protecht/ui-library/library/components/Button';
import DialogColorPicker from '@protecht/ui-library/library/components/DialogColorPicker';
import { ColorOption } from '@protecht/ui-library/library/components/DialogColorPicker/types';
import { useFormContext } from 'react-hook-form';
import ColorIndicator, { CUSTOM_VALUE } from 'common/components/ColorIndicator';
import { getReactRoot } from 'config';
import { ChevronDown } from '@protecht/ui-library/library/components/SVGIcons';

const ColorSelectorField: React.FC<FormFieldBaseProps> = (formFieldProps) => {
    const [selectedColor, setSelectedColor] = useState<ColorOption | undefined>(undefined);
    const [isSelectedColorCustom, setIsSelectedColorCustom] = useState<boolean>(false);
    const [customColorDialogOpened, setCustomColorDialogOpened] = useState<boolean>(false);
    const [selectedColorIndex, setSelectedColorIndex] = useState<number | undefined>(undefined);

    const { setValue, watch } = useFormContext();
    const theme = useTheme();

    const metricThemeColors: Array<[string, string]> = useMemo(() => {
        return Object.entries(theme.palette.metricColors);
    }, [theme]);

    const updateFormColorValue = useCallback(
        (colorValue: string) => {
            setValue(formFieldProps.name, colorValue, { shouldValidate: true, shouldDirty: true });
        },
        [formFieldProps.name, setValue],
    );

    const colorOptions: ContextMenuItem[] = useMemo(() => {
        return [
            ...Object.keys(theme.palette.metricColors).map((color) => ({
                label: strings(`metrics:colorSelectorDialog.colors.${color}`),
                action: () => {
                    updateFormColorValue(theme.palette.metricColors[color]);
                },
                content: (
                    <ColorIndicator
                        mr="10px"
                        value={theme.palette.metricColors[color]}
                    />
                ),
            })),
            { divider: true },
            {
                label: strings('metrics:colorSelectorDialog.colors.custom'),
                action: () => setCustomColorDialogOpened(true),
                content: (
                    <ColorIndicator
                        mr="10px"
                        value={isSelectedColorCustom && selectedColor?.hex ? selectedColor.hex : CUSTOM_VALUE}
                    />
                ),
            },
        ];
    }, [theme.palette.metricColors, isSelectedColorCustom, selectedColor, updateFormColorValue]);
    const colorOptionsLength = colorOptions.length;

    const currentColorValue: string = watch(formFieldProps.name);
    useEffect(() => {
        if (currentColorValue) {
            const themeColor = metricThemeColors.find(([_, color]) => color === currentColorValue);
            const themeColorIndex = metricThemeColors.findIndex(([_, color]) => color === currentColorValue);
            if (themeColor) {
                const [themeColorKey, themeColorValue] = themeColor;
                setSelectedColor({
                    value: strings(`metrics:colorSelectorDialog.colors.${themeColorKey}`),
                    hex: themeColorValue,
                    label: strings(`metrics:colorSelectorDialog.colors.${themeColorKey}`),
                });
                setIsSelectedColorCustom(false);
                setSelectedColorIndex(themeColorIndex);
            } else {
                setSelectedColor({
                    value: 'custom_color',
                    hex: currentColorValue,
                    label: 'Custom Color',
                });
                setIsSelectedColorCustom(true);
                setSelectedColorIndex(colorOptionsLength - 1);
            }
        }
    }, [metricThemeColors, currentColorValue, colorOptionsLength]);

    const renderValue = useCallback(() => {
        if (!selectedColor?.value) {
            return strings('metrics:customColorDialog.selectColor');
        }

        return (
            <Box
                display="flex"
                alignItems="center"
            >
                <ColorIndicator
                    mr="10px"
                    value={selectedColor.hex}
                />
                <Box>{selectedColor.label}</Box>
            </Box>
        );
    }, [selectedColor]);

    const onDialogClose = useCallback(() => {
        setCustomColorDialogOpened(false);
        setSelectedColor(undefined);
        setIsSelectedColorCustom(false);
    }, []);

    const onDialogConfirm = useCallback(
        (selectedColorOption: ColorOption) => {
            updateFormColorValue(selectedColorOption.hex);
            setCustomColorDialogOpened(false);
        },
        [updateFormColorValue],
    );

    return (
        <>
            <FormField
                {...formFieldProps}
                renderField={() => (
                    <ContextMenu
                        baseElement={
                            <Button
                                size="large"
                                fullWidth={true}
                                variant={'secondary'}
                                sx={{ justifyContent: 'space-between' }}
                                endIcon={<ChevronDown />}
                                dataTestId="color-selector-field"
                            >
                                {renderValue()}
                            </Button>
                        }
                        menuAnchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                        items={colorOptions}
                        showCheckMarks={true}
                        selectedIndex={selectedColorIndex}
                        rootContainer={getReactRoot()}
                    />
                )}
            />
            {customColorDialogOpened && (
                <DialogColorPicker
                    title={strings('metrics:customColorDialog.title')}
                    visible={true}
                    onClose={onDialogClose}
                    onConfirm={onDialogConfirm}
                />
            )}
        </>
    );
};

export default ColorSelectorField;
