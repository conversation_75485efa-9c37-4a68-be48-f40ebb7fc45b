import React from 'react';
import FormField, { FormFieldBaseProps } from '@protecht/ui-library/library/components/FormFields';
import TagPicker from 'library/components/Tag/TagPicker';

export type TagFieldProps = FormFieldBaseProps;

const TagField: React.FC<TagFieldProps> = ({ readOnly, ...formFieldProps }) => {
    return (
        <FormField
            {...formFieldProps}
            renderField={({ ref: _ref, value, onChange, ...field }) => {
                return (
                    <TagPicker
                        {...field}
                        readOnly={readOnly}
                        selectedTags={value}
                        onTagsSelected={onChange}
                    />
                );
            }}
        />
    );
};

export default TagField;
