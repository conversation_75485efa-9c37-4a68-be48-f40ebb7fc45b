import { strings } from 'common/utils/i18n';
import { operatorRequiresValue } from 'metrics/utils';
import * as Yup from 'yup';

export const ExpressionElementSchema = Yup.object().shape({
    property: Yup.string().required(strings('view:validators.notSelectedProperty')),
    expression: Yup.string().required(strings('view:validators.notSelectedOperator')),
    value: Yup.string()
        .optional()
        .when('expression', {
            is: (expression) => operatorRequiresValue(expression),
            then: Yup.string().required(strings('view:validators.emptyValue')),
        }),
});
