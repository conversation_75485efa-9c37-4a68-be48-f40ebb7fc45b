import React, { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import { DurationType, FilterType } from 'view/types';
import { DurationMapDefinitions, CalendarDurationDefinitions } from './definitions';
import { DateTime } from 'luxon';
import { strings } from 'common/utils/i18n';
import { getDuration } from 'view/utils';
import Select from '@protecht/ui-library/library/components/Inputs/Select';
import DatePicker from '@protecht/ui-library/library/components/Inputs/DateInputs/DatePicker';

const StyledTextField = styled(TextField)(({ theme }) => ({
    display: 'flex',
    flex: 1,
    width: 'auto',
    '& .MuiInputBase-root': {
        borderRadius: '0px',
        backgroundColor: theme.palette.background.default,
    },
}));

const StyledComplexDateTextField = styled(TextField)(({ theme }) => ({
    flex: 1,
    marginRight: theme.spacing(1),
}));

const DateComplexWrapper = styled(Box)(() => ({
    display: 'flex',
    flex: 1,
    width: 'auto',
    backgroundColor: 'palette.background.default',
}));

export type ExpressionInputProps = {
    value?: string;
    onValueChange: (value: string) => void;
    type: FilterType;
};

const ExpressionInput: React.FC<ExpressionInputProps> = (props: ExpressionInputProps) => {
    const { value = '', type = FilterType.STRING, onValueChange } = props;

    const [count, setCount] = useState<string>();
    const [duration, setDuration] = useState<DurationType | ''>();

    useEffect(() => {
        if ((type === FilterType.DATE_COMPLEX || type === FilterType.DATE_SIMPLE) && value.startsWith('$')) {
            // in case of DATE_COMPLEX or DATE_SIMPLE type, the value is eg. ${YEAR}4 or ${CALENDAR_WEEK}0
            const { count: newCount, duration: newDuration } = getDuration(value);

            if (newDuration !== duration) {
                setDuration(newDuration);
            }

            if (newCount !== count) {
                setCount(newCount);
            }
        }
        /**
         * Exclude deps: [duration, count]
         * Reason: potential infinite re-render, as we're setting these values
         * */
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [type, value]);

    useEffect(() => {
        if (duration) {
            onValueChange(`\${${duration}}${count || 0}`);
        }
    }, [count, duration]);

    const handleDateChanged = (value) => {
        if (value === undefined) {
            return;
        } else {
            onValueChange(DateTime.fromISO(value).toISODate() || '');
        }
    };

    const handleCountChanged = (event) => {
        const value = event.target.value;
        if (value === '' || /^[0-9\b]+$/.test(value)) {
            setCount(value);
        }
    };

    switch (type) {
        case FilterType.DATE: {
            return (
                <DatePicker
                    value={DateTime.fromISO(value)}
                    onChange={handleDateChanged}
                ></DatePicker>
            );
        }
        case FilterType.DATE_COMPLEX:
            return (
                <DateComplexWrapper
                    display="flex"
                    flexDirection="row"
                >
                    <StyledComplexDateTextField
                        value={count || '0'}
                        onChange={handleCountChanged}
                        variant="outlined"
                        placeholder={strings('view:placeholder.value')}
                    />
                    <Select
                        value={duration || ''}
                        onChange={(value: DurationType) => setDuration(value)}
                        variant="outlined"
                        placeholder="value"
                        displayEmpty
                        sx={{
                            flex: 2,
                        }}
                        options={[
                            {
                                value: '',
                                label: strings('view:placeholder.value'),
                                disabled: true,
                            },
                            ...DurationMapDefinitions.map((item) => {
                                return {
                                    key: item.value,
                                    value: item.value,
                                    label: item.label,
                                };
                            }),
                        ]}
                    ></Select>
                </DateComplexWrapper>
            );
        case FilterType.DATE_SIMPLE:
            return (
                <Select
                    value={duration || ''}
                    onChange={(value: DurationType) => setDuration(value)}
                    variant="outlined"
                    placeholder="value"
                    displayEmpty
                    sx={{
                        display: 'flex',
                        flex: 1,
                        width: 'auto',
                        backgroundColor: 'palette.background.default',
                    }}
                    options={[
                        {
                            value: '',
                            label: strings('view:placeholder.value'),
                            disabled: true,
                        },
                        ...CalendarDurationDefinitions.map((item) => {
                            return {
                                key: item.label,
                                value: item.value,
                                label: item.label,
                            };
                        }),
                    ]}
                ></Select>
            );
        default:
            return (
                <StyledTextField
                    value={value}
                    placeholder={strings('view:placeholder.value')}
                    onChange={(event) => onValueChange(event.target.value as string)}
                    variant="outlined"
                />
            );
    }
};

export default ExpressionInput;
