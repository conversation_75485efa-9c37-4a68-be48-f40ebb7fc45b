import React from 'react';
import Box from '@mui/material/Box';
import ExpressionElement from './ExpressionElement';
import { strings } from 'common/utils/i18n';
import { DataGridColDef } from 'common/types';
import { ExpressionType, ViewExpression } from 'view/types';
import { v4 as uuid } from 'uuid';
import Button from '@protecht/ui-library/library/components/Button';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

interface ExpressionBuilderProps {
    columns: DataGridColDef[];
    expressions: ViewExpression[];
    onUpdateExpression: (expression: ViewExpression) => void;
    onAppendExpression: (expression: ViewExpression) => void;
    onRemoveExpression: (expressionId: string) => void;
}

const ExpressionBuilder: React.FunctionComponent<ExpressionBuilderProps> = (props: ExpressionBuilderProps) => {
    const { onUpdateExpression, onAppendExpression, onRemoveExpression } = props;

    return (
        <Box sx={{ width: '100%' }}>
            {props.expressions.map((item, index) => {
                return (
                    <ExpressionElement
                        key={`${item.id}`}
                        expression={item}
                        inputIndex={index}
                        propertyList={props.columns}
                        isLastInArray={index === props.expressions.length - 1}
                        onAddExpression={onAppendExpression}
                        onExpressionChange={onUpdateExpression}
                        onRemoveExpression={onRemoveExpression}
                    />
                );
            })}
            {props.expressions.length === 0 && (
                <Button
                    size="large"
                    aria-label="filter-add-expression"
                    onClick={() => {
                        onAppendExpression({ id: uuid(), property: '', expression: ExpressionType.EMPTY, value: '' });
                    }}
                    sx={{
                        textTransform: 'uppercase',
                    }}
                    startIcon={<Add />}
                >
                    {strings('view:button.addExpression')}
                </Button>
            )}
        </Box>
    );
};

export default ExpressionBuilder;
