import { FilterType, ExpressionType } from 'view/types';

export const getValueInputType = (type: FilterType | undefined, expression: ExpressionType): FilterType => {
    if (type === FilterType.DATE) {
        if (expression === ExpressionType.LAST || expression === ExpressionType.NEXT) {
            return FilterType.DATE_COMPLEX;
        } else if (expression === ExpressionType.CURRENT) {
            return FilterType.DATE_SIMPLE;
        }
        return FilterType.DATE;
    }
    return FilterType.STRING;
};
