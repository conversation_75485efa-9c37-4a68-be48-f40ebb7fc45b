import { DurationType } from 'view/types';
import { strings } from 'common/utils/i18n';

const DurationMapDefinitions = [
    {
        label: strings('view:time.day'),
        value: DurationType.DAY,
    },
    {
        label: strings('view:time.week'),
        value: DurationType.WEEK,
    },
    {
        label: strings('view:time.month'),
        value: DurationType.MONTH,
    },
    {
        label: strings('view:time.year'),
        value: DurationType.YEAR,
    },
    {
        label: strings('view:time.calendarDay'),
        value: DurationType.CALENDAR_DAY,
    },
    {
        label: strings('view:time.calendarWeek'),
        value: DurationType.CALENDAR_WEEK,
    },
    {
        label: strings('view:time.calendarMonth'),
        value: DurationType.CALENDAR_MONTH,
    },
    {
        label: strings('view:time.calendarYear'),
        value: DurationType.CALENDAR_YEAR,
    },
];

const CalendarDurationDefinitions = [
    {
        label: strings('view:time.calendarDay'),
        value: DurationType.CALENDAR_DAY,
    },
    {
        label: strings('view:time.calendarWeek'),
        value: DurationType.CALENDAR_WEEK,
    },
    {
        label: strings('view:time.calendarMonth'),
        value: DurationType.CALENDAR_MONTH,
    },
    {
        label: strings('view:time.calendarYear'),
        value: DurationType.CALENDAR_YEAR,
    },
];

export { DurationMapDefinitions, CalendarDurationDefinitions };
