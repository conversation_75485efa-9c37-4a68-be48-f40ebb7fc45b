import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import useTheme from '@mui/system/useTheme';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrashAlt } from '@fortawesome/pro-solid-svg-icons';
import { ExpressionType, FilterType, ViewExpression } from 'view/types';
import { strings } from 'common/utils/i18n';
import ExpressionInput from './ExpressionInput';
import { DataGridColDef } from 'common/types';
import { getApiField } from 'common/utils/definitions';
import { ControllerRenderProps, FormProvider, useForm, useFormContext } from 'react-hook-form';
import { v4 as uuid } from 'uuid';
import useFormValidationResolver from 'common/hooks/forms/useFormValidationResolver';
import Button from '@protecht/ui-library/library/components/Button';
import FormField, { SelectField } from '@protecht/ui-library/library/components/FormFields';
import OperatorSelector from 'common/components/Conditions/OperatorSelector';
import { ExpressionElementSchema } from './ExpressionElementSchema';
import { getValueInputType } from './utils';
import { viewsApi } from 'view/rtkApi';
import { useSelector } from 'react-redux';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

export interface ExpressionElementProps {
    expression: ViewExpression;
    propertyList?: DataGridColDef[];
    inputIndex: number;
    isLastInArray?: boolean;
    onExpressionChange: (newValue: ViewExpression) => void;
    onRemoveExpression: (expressionId: string) => void;
    onAddExpression: (exprData: ViewExpression) => void;
}

export type WithField<FieldName extends string, FieldValue, T> = T & Record<FieldName, FieldValue>;

const ExpressionElement: React.FC<ExpressionElementProps> = ({ inputIndex, expression, propertyList = [], onRemoveExpression, ...props }) => {
    const theme = useTheme();
    const { data: operatorList, isSuccess } = useSelector(viewsApi.endpoints.vrsGetExpressionContextUsingGet.select());

    const resolver = useFormValidationResolver(ExpressionElementSchema);

    const methods = useForm<ViewExpression>({
        mode: 'onChange',
        defaultValues: expression,
        resolver,
    });
    const { getValues, setValue, handleSubmit, watch, trigger, formState } = methods;
    const { isValid } = formState;
    const { setValue: setMainFormValue } = useFormContext();

    const watchProperty = watch('property');
    const watchExpression = watch('expression');
    const watchValue = watch('value');
    const valueInputVisible = watchExpression !== ExpressionType.IS_SET && watchExpression !== ExpressionType.IS_NOT_SET;

    const [selectedPropertyFilterType, setSelectedPropertyFilterType] = useState<FilterType | undefined>();

    const [valueInputType, setValueInputType] = useState<FilterType>(getValueInputType(selectedPropertyFilterType, watchExpression || ExpressionType.EMPTY));

    useEffect(() => {
        const { filterType } = propertyList.find((item) => getApiField(item) === watchProperty) ?? {};
        setSelectedPropertyFilterType(filterType);
        // If operator list has changed after switching the type,
        // and we can no longer find the matching operator, reset back to empty
        if (isSuccess && operatorList && filterType) {
            const operators = operatorList[filterType] || [];
            if (watchExpression && operators.length && !operators.includes(watchExpression)) {
                setValue('expression', ExpressionType.EMPTY, { shouldValidate: true, shouldDirty: true });
                setValue('value', '', { shouldValidate: true, shouldDirty: true });
                setMainFormValue(`expressions[${inputIndex}].expression`, ExpressionType.EMPTY, { shouldValidate: true, shouldDirty: true });
                setMainFormValue(`expressions[${inputIndex}].value`, '', { shouldValidate: true, shouldDirty: true });
            }
        }

        setValueInputType(getValueInputType(filterType, watchExpression || ExpressionType.EMPTY));
    }, [watchProperty, watchExpression, watchValue, propertyList, operatorList, trigger, getValues, setValue, getValueInputType]);

    const renderInput = () => {
        if (valueInputVisible) {
            return (
                <Grid
                    item
                    xs={12}
                    sm={4}
                >
                    <FormField
                        name="value"
                        fullWidth={false}
                        fullHeight={false}
                        emptyLabelSpacing={false}
                        hideFieldLabel={true}
                        renderField={(field) => (
                            <ExpressionInput
                                value={field.value}
                                onValueChange={(value) => onValueChanged(field, value)}
                                type={valueInputType}
                            />
                        )}
                    />
                </Grid>
            );
        } else {
            return <div style={{ display: 'flex', flex: 1 }} />;
        }
    };

    const selectPropertyOptions = useMemo(
        () => [
            {
                value: '',
                label: strings('view:placeholder.property'),
                disabled: true,
            },
            ...propertyList.map((item) => {
                const apiField = getApiField(item);
                return {
                    key: apiField,
                    value: apiField,
                    label: item.headerName,
                };
            }),
        ],
        [propertyList],
    );

    const onValueChanged = useCallback(
        (field: ControllerRenderProps<any>, value: string) => {
            field.onChange(value);
            setMainFormValue(`expressions[${inputIndex}].value`, value, { shouldValidate: true, shouldDirty: true });
            void trigger(['property', 'expression']);
        },
        [setMainFormValue, trigger, inputIndex],
    );

    const onPropertyChanged = useCallback(
        (selectedProperty: string) => {
            setMainFormValue(`expressions[${inputIndex}].property`, selectedProperty, { shouldValidate: true, shouldDirty: true });
        },
        [setMainFormValue, inputIndex],
    );

    const onOperatorChanged = useCallback(
        (expression: ExpressionType) => {
            if (expression === ExpressionType.IS_SET || expression === ExpressionType.IS_NOT_SET) {
                setValue('value', '');
                setMainFormValue(`expressions[${inputIndex}].value`, '', { shouldValidate: true, shouldDirty: true });
            }

            setMainFormValue(`expressions[${inputIndex}].expression`, expression, { shouldValidate: true, shouldDirty: true });
        },
        [setValue, setMainFormValue, inputIndex],
    );

    return (
        <FormProvider {...methods}>
            <Grid
                container
                flex={1}
                p={1}
                sx={{
                    backgroundColor: theme.palette.protechtGrey?.grey_245,
                    borderRadius: '4px',
                }}
            >
                <Grid
                    container
                    item
                    spacing={1}
                    flexWrap="nowrap"
                >
                    <Grid
                        container
                        item
                        flex={1}
                        spacing={1}
                        overflow="hidden"
                    >
                        <Grid
                            item
                            xs={12}
                            sm={5}
                        >
                            <SelectField
                                name="property"
                                variant="outlined"
                                displayEmpty
                                hideFieldLabel={true}
                                sx={{
                                    display: 'flex',
                                    backgroundColor: theme.palette.background.default,
                                }}
                                options={selectPropertyOptions}
                                onChange={onPropertyChanged}
                            />
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sm={3}
                        >
                            <OperatorSelector
                                name="expression"
                                emptyLabelSpacing={false}
                                selectedRegisterFieldFilterType={selectedPropertyFilterType}
                                disabled={!selectedPropertyFilterType}
                                onChange={onOperatorChanged}
                                hideFieldLabel={true}
                            />
                        </Grid>
                        {renderInput()}
                    </Grid>
                    <Grid
                        item
                        display="flex"
                        alignItems="center"
                        height="40px"
                    >
                        <IconButton
                            onClick={() => onRemoveExpression(expression.id?.toString() || '')}
                            size="small"
                            aria-label="remove expression"
                            component="span"
                        >
                            <FontAwesomeIcon
                                icon={faTrashAlt}
                                color={theme.palette.error.main}
                            />
                        </IconButton>
                    </Grid>
                </Grid>
            </Grid>
            {props.isLastInArray && (
                <Button
                    size="large"
                    aria-label="filter-add-expression"
                    onClick={handleSubmit((data) => {
                        // Update the current & add new
                        props.onExpressionChange(data);
                        props.onAddExpression({ id: uuid(), property: '', expression: ExpressionType.EMPTY, value: '' });
                    })}
                    sx={{
                        textTransform: 'uppercase',
                        marginTop: '8px',
                    }}
                    disabled={!isValid}
                    startIcon={<Add />}
                >
                    {strings('view:button.addExpression')}
                </Button>
            )}
        </FormProvider>
    );
};

export default React.memo(ExpressionElement);
