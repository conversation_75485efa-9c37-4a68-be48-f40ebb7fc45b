import React from 'react';
import Selector from './RegisterFieldSelector';
import { render, screen } from 'test/utils/rtl';
import { waitFor } from '@testing-library/react';

const colDef = [
    {
        field: 'id',
        headerName: 'ID',
        hide: true,
    },
    {
        field: 'column1',
        headerName: 'Column 1',
        flex: 1,
    },
    {
        field: 'column2',
        headerName: 'Column 2',
        flex: 1,
    },
];

const data = [
    { id: 1, column1: 'Text 1', column2: 'Text 2' },
    { id: 2, column1: 'Text 3', column2: 'Text 4' },
    { id: 3, column1: 'Text 5', column2: 'Text 6' },
];

// eslint-disable-next-line jest/no-disabled-tests
describe.skip('<RegisterFieldSelector />', () => {
    const onSelect = jest.fn();
    const selected = data[0];

    const setup = () => {
        return render(
            <Selector
                title={'Some title'}
                columns={colDef}
                data={data}
                selected={selected}
                onClose={jest.fn()}
                onSelect={onSelect}
            />,
        );
    };

    it('was rendered', () => {
        setup();

        expect(screen.getByText('Some title')).toBeInTheDocument();

        expect(screen.getByText('Column 1')).toBeInTheDocument();
        expect(screen.getByText('Column 2')).toBeInTheDocument();

        expect(screen.getByText('Text 1')).toBeInTheDocument();
        expect(screen.getByText('Text 2')).toBeInTheDocument();
        expect(screen.getByText('Text 3')).toBeInTheDocument();
        expect(screen.getByText('Text 4')).toBeInTheDocument();
        // TODO: investigate why the last row is not rendered (maybe something with pagination?)
        // TODO: answer: can be because this table has height of 68, one line is 34 so only 2 items are being displayed
        // expect(screen.getByText('Text 5')).toBeInTheDocument();
        // expect(screen.getByText('Text 6')).toBeInTheDocument();

        // eslint-disable-next-line testing-library/no-node-access
        const selected = document.querySelector('[aria-selected="true"]');
        expect(selected).toBeInTheDocument();
        expect(selected).toHaveAttribute('data-rowindex', '0');
    });

    it('was confirmed', async () => {
        const { user } = setup();

        const confirmButton = screen.getByRole('button', { name: 'Select' });

        await user.click(confirmButton);

        await waitFor(() => {
            expect(onSelect).toHaveBeenCalled();
        });
    });
});
