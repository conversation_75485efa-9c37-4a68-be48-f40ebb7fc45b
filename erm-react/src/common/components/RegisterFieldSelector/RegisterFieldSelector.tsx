import React, { useCallback, useEffect, useState } from 'react';
import { DataGridColDef } from 'common/types';
import { GridPaginationModel, GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { strings } from 'common/utils/i18n';
import { IdOnly } from 'app/types';
import { MenuItemType } from 'ui/types';
import SearchByField from 'common/components/SearchByField';
import { DEFAULT_FIELD } from 'library/components/TagCategory/TagCategoryDefinitions';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import { Table } from '@protecht/ui-library/library/components/Table';
import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import SearchByFieldToolbarGroup from '../SearchByField/SearchByFieldToolbarGroup';

type Props<T> = {
    title: string;
    columns: DataGridColDef[];
    data: T[];
    selected?: T;
    infoMessage?: string;
    onClose: () => void;
    onSelect: (selected: T) => void;
};

const RegisterFieldSelector = <T extends IdOnly>({ title, columns = [], data = [], selected, infoMessage, onClose, onSelect }: Props<T>): JSX.Element => {
    const [currentSelection, setCurrentSelection] = useState<T | undefined>(selected);
    const [options] = useState<MenuItemType<string>[]>(columns.map((col) => ({ label: col.headerName, value: col.field })));
    const [searchByField, setSearchByField] = useState<string | undefined>('');
    const [searchValue, setSearchValue] = useState('');
    const [filteredItems, setFilteredItems] = useState<T[] | undefined>(undefined);
    const [page, setPage] = useState<number | undefined>(0);
    const [params, setParams] = useState<SearchRequestParams | undefined>(undefined);
    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({ page: 0, pageSize: 10 });
    const theme = useTheme();
    const handleSelection = (selectionModel: GridRowSelectionModel) => {
        const selectedId = selectionModel[selectionModel.length - 1];
        setCurrentSelection(data.find((item) => selectedId === item.id));
    };

    useEffect(() => {
        if (options.length) {
            setSearchByField(options[0].value);
        }
    }, [options]);

    useEffect(() => {
        if (searchByField && searchValue?.length) {
            setPage(undefined);
            const filtered = data.filter((row) => row[searchByField].toString().toLowerCase().includes(searchValue.toLowerCase()));
            setFilteredItems(filtered);
        } else {
            setFilteredItems(data);
        }
    }, [searchByField, searchValue, data]);

    useEffect(() => {
        if (page === undefined) {
            setPage(0);
        }
    }, [page]);

    const confirmSelection = () => {
        if (currentSelection) {
            onSelect(currentSelection);
        }
        onClose();
    };

    const onValueChanged = useCallback((value) => {
        setSearchValue(value);
    }, []);

    const onPropertyChanged = useCallback((value) => {
        setSearchByField(value);
    }, []);

    return (
        <Dialog
            title={title}
            visible={true}
            width={1012}
            height={674}
            onOutsideClickClose={onClose}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!currentSelection}
                        onClick={confirmSelection}
                    >
                        {strings('ermConstants:button_label_select')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                direction="column"
                sx={{ height: '100%', width: '100%', overflow: 'hidden', flexWrap: 'nowrap' }}
            >
                <Grid
                    item
                    sx={{ flex: '0 0 auto' }}
                >
                    <ToolbarContainer>
                        <SearchByFieldToolbarGroup maxWidth="100%">
                            <SearchByField
                                fields={options}
                                dataTestId="input-field-Search"
                                searchValue={searchValue}
                                searchField={searchByField || DEFAULT_FIELD}
                                onValueChanged={onValueChanged}
                                onPropertyChanged={onPropertyChanged}
                            />
                        </SearchByFieldToolbarGroup>
                    </ToolbarContainer>
                </Grid>
                <Grid
                    item
                    sx={{
                        flex: '1 1 auto',
                        overflow: 'auto',
                    }}
                >
                    <Table<T>
                        paginationMode={'client'}
                        autoPageSize
                        disableColumnMenu
                        disableMultipleRowSelection={true}
                        hideFooterSelectedRowCount
                        columnHeaderHeight={ThemeConfig.rowHeight}
                        rowHeight={ThemeConfig.rowHeight}
                        columns={columns}
                        rows={filteredItems || []}
                        params={params}
                        onParamsChanged={setParams}
                        paginationModel={paginationModel}
                        onPaginationModelChange={setPaginationModel}
                        totalCount={filteredItems?.length || 0}
                        rowSelectionModel={currentSelection ? [currentSelection.id] : []}
                        onRowSelectionModelChange={handleSelection}
                        loading={filteredItems === undefined}
                        loadingMessage={strings('common:message.loading')}
                        multiselect={false}
                    />
                </Grid>
                {infoMessage && (
                    <Grid
                        item
                        pt="24px"
                        sx={{ flex: '0 0 auto' }}
                    >
                        <Box sx={{ border: '1px solid ' + theme.palette.protechtGrey?.grey_231, padding: 1 }}>
                            <Typography>{infoMessage}</Typography>
                        </Box>
                    </Grid>
                )}
            </Grid>
        </Dialog>
    );
};

export default RegisterFieldSelector;
