import React, { forwardRef } from 'react';

import Box from '@mui/material/Box';
import Checkbox from '@protecht/ui-library/library/components/Checkbox';
import { styled } from '@mui/material/styles';

import { OptimisedGridRenderCellParams } from './CellWithTooltip';

export const CellContent = styled('div')({
    display: 'flex',
    width: 'max-content',
});

type CheckBoxCellProps = Omit<OptimisedGridRenderCellParams, 'value'> & { value: boolean };

const CheckBoxCell = (params: CheckBoxCellProps) => {
    const RenderCellDefault = forwardRef<HTMLDivElement, CheckBoxCellProps>((props, ref) => {
        const value = props.value;

        return (
            <CellContent
                ref={ref}
                style={{ width: '100%' }}
            >
                <Box
                    alignItems="center"
                    display="flex"
                    flex={1}
                    justifyContent="center"
                >
                    <Checkbox
                        checked={value}
                        disabled={true}
                    />
                </Box>
            </CellContent>
        );
    });

    RenderCellDefault.displayName = 'renderCheckBoxCell';

    return <RenderCellDefault {...params} />;
};

export default CheckBoxCell;
