import React, { useEffect, useRef, useState } from 'react';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { styled } from '@mui/material/styles';
import { EntryField } from 'register/types';

export const CellContent = styled('div')({
    display: 'flex',
    width: 'max-content',
});

export const CellContentPlainText = styled('div')({
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});

export type TooltipTitle = string | React.ReactElement | React.ReactFragment;

export const CellContentSpaceBetween = styled(CellContent)({
    justifyContent: 'space-between',
    width: 'auto',
    minWidth: 'max-content',
});

export interface CellWithTooltipProps {
    tooltipTitle?: TooltipTitle;
    alwaysShowTooltip?: boolean;
    cellBoundsTolerance?: number;
}

export interface OptimisedGridRenderCellParams {
    value: string;
    entryFieldValue?: EntryField;
    formattedValue: string;
    computedCellWidth: number;
}

/**
 * Use for plain-text cells with ellipsis content overflow.
 *
 * We pass computedCellWidth as prop here as well in order to trigger
 * re-render when the cell is resized.
 * */
export const cellWithTooltip = <T extends OptimisedGridRenderCellParams>(RenderCell: React.FC<T>) => {
    return TooltipWrapper;

    function TooltipWrapper(props: T & CellWithTooltipProps) {
        const cellContentRef = useRef<HTMLDivElement>(null);
        const [enableHover, setEnableHover] = useState(false);

        useEffect(() => {
            if (cellContentRef.current) {
                setEnableHover(props.alwaysShowTooltip || cellContentRef.current.offsetWidth < cellContentRef.current.scrollWidth);
            }
        }, [props]);

        return (
            <Tooltip
                disableHoverListener={!enableHover}
                title={props.tooltipTitle}
                style={{ width: '100%' }}
            >
                <RenderCell
                    ref={cellContentRef}
                    {...props}
                />
            </Tooltip>
        );
    }
};

const CellRenderDefaultFn = (params: OptimisedGridRenderCellParams & { tooltipTitle?: TooltipTitle }) => {
    const value = params.formattedValue ?? params.value ?? '';

    const RenderCellDefault = cellWithTooltip(
        React.forwardRef<HTMLDivElement, OptimisedGridRenderCellParams>(function Render(_, ref) {
            return <CellContentPlainText ref={ref}>{value}</CellContentPlainText>;
        }),
    );

    return (
        <RenderCellDefault
            {...params}
            tooltipTitle={params.tooltipTitle ?? value ?? ''}
        />
    );
};

export const CellRenderDefault = React.memo(CellRenderDefaultFn);
