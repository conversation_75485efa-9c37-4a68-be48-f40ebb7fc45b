import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { alpha } from '@mui/material/styles';
import StatusIcon from 'common/components/StatusIcon';
import { HtmlFieldCellRenderer } from 'library/utils';
import React, { useEffect, useRef, useState } from 'react';
import { Hyperlink } from 'register/types';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { CellContent, CellWithTooltipProps, OptimisedGridRenderCellParams, TooltipTitle } from './CellWithTooltip';
import hash from 'hash-it';
import { getListItems } from 'register/components/RegisterField/ListRegisterField/utils';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types/types';
import { getScaleSet } from 'app/selectors';
import store from 'store';
import { RiskMatrixRatingRest } from 'api/generated/types';

export const richCellWithTooltip = <T extends OptimisedGridRenderCellParams>(RenderCell: React.FC<T>) => {
    return TooltipWrapper;

    function TooltipWrapper(props: T & CellWithTooltipProps) {
        const cellContentRef = useRef<HTMLDivElement>(null);
        const [enableHover, setEnableHover] = useState(false);

        useEffect(() => {
            if (cellContentRef.current) {
                setEnableHover(props.alwaysShowTooltip || cellContentRef.current.offsetWidth < cellContentRef.current.scrollWidth);
            }
        }, [props]);

        return (
            <Tooltip
                disableHoverListener={!enableHover}
                title={props.tooltipTitle}
                style={{
                    width: '100%',
                }}
            >
                <RenderCell
                    ref={cellContentRef}
                    {...props}
                />
            </Tooltip>
        );
    }
};

const RichCellRenderDefault = (params: OptimisedGridRenderCellParams & { tooltipTitle?: TooltipTitle }) => {
    const RenderRichCell = richCellWithTooltip(
        React.forwardRef<HTMLDivElement, OptimisedGridRenderCellParams>(function Render(_, ref) {
            return (
                <CellContent
                    style={{
                        height: '21px',
                        maxWidth: '100%',
                        display: 'inline-block',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                    }}
                    ref={ref}
                >
                    <HtmlFieldCellRenderer htmlValue={params.value} />
                </CellContent>
            );
        }),
    );

    return (
        <RenderRichCell
            {...params}
            tooltipTitle={<HtmlFieldCellRenderer htmlValue={params.value} />}
        />
    );
};

const RichCellRenderTable = (params: OptimisedGridRenderCellParams & { tooltipTitle?: TooltipTitle }) => {
    const RenderRichCellTable = (params: OptimisedGridRenderCellParams & { tooltipTitle?: TooltipTitle }) => {
        const displayValues = params.entryFieldValue?.parameters?.displayValues || [];
        const statuses = params.entryFieldValue?.parameters?.statuses ? params.entryFieldValue?.parameters?.statuses.map((status) => parseInt(status)) : [];
        const simpleValues = params.entryFieldValue?.simpleValue || [];
        const tooltipTitle =
            params.tooltipTitle ||
            simpleValues.reduce((acc, value, index) => acc + (displayValues[index] || value || '') + (index < simpleValues.length - 1 ? ', ' : ''), '') ||
            '';

        return richCellWithTooltip(
            React.forwardRef<HTMLDivElement, OptimisedGridRenderCellParams>(function Render(_, ref) {
                return (
                    <CellContent ref={ref}>
                        {simpleValues.map((value: string, index: number) => (
                            <Box
                                key={`${value}_${index}`}
                                display="flex"
                                flexDirection={'row'}
                                justifyContent="center"
                                alignItems="center"
                            >
                                <StatusIcon status={statuses[index]} />
                                <Typography variant="body1">
                                    {displayValues[index] && displayValues[index] !== 'null' ? displayValues[index] : value || ''}
                                </Typography>
                                <Typography variant="body1">{index < simpleValues.length - 1 ? ', ' : ''}</Typography>
                            </Box>
                        ))}
                    </CellContent>
                );
            }),
        )({
            value: params.value,
            formattedValue: params.formattedValue,
            computedCellWidth: params.computedCellWidth,
            tooltipTitle,
        });
    };

    return (
        <RenderRichCellTable
            value={params.value}
            formattedValue={params.formattedValue}
            entryFieldValue={params.entryFieldValue}
            computedCellWidth={params.computedCellWidth}
        />
    );
};

type RichCellRenderListProps = OptimisedGridRenderCellParams & {
    field: SectionFieldMetaData;
};

const RichCellRenderList = (params: RichCellRenderListProps) => {
    const listItem = getListItems(params.field).find((item) => item.value === params.value);

    return (
        <ColorCell
            bgColor={listItem?.backgroundColor}
            label={listItem?.label ?? params.value}
            value={params.value}
            formattedValue={params.formattedValue}
            computedCellWidth={params.computedCellWidth}
        />
    );
};

type ColorCellProps = OptimisedGridRenderCellParams & {
    bgColor?: string;
    label?: string;
    textColor?: string;
};

export const ColorCell = ({ bgColor, label, textColor, ...params }: ColorCellProps) => {
    const RenderColorCell = richCellWithTooltip(
        React.forwardRef<HTMLDivElement, OptimisedGridRenderCellParams>(function Render(_, ref) {
            return (
                <CellContent
                    style={{ width: '100%' }}
                    ref={ref}
                >
                    {bgColor ? (
                        <Box
                            display="flex"
                            px={0.5}
                            flex={1}
                            bgcolor={alpha(bgColor, 0.5)}
                            height="26px"
                            justifyContent="center"
                            alignItems="center"
                        >
                            <Typography
                                variant="body1"
                                color={textColor ?? 'protechtGrey.darkBlack'}
                            >
                                {label}
                            </Typography>
                        </Box>
                    ) : (
                        label
                    )}
                </CellContent>
            );
        }),
    );

    return (
        <RenderColorCell
            value={params.value}
            formattedValue={params.formattedValue}
            entryFieldValue={params.entryFieldValue}
            computedCellWidth={params.computedCellWidth}
        />
    );
};

type RichCellRenderRiskRatingProps = OptimisedGridRenderCellParams & {
    parentField?: SectionFieldMetaData;
};

const RichCellRenderRiskRating = (params: RichCellRenderRiskRatingProps) => {
    const riskRatingId = params.value;
    let rating: RiskMatrixRatingRest | undefined;
    if (params.parentField && riskRatingId) {
        const scaleSet = getScaleSet(store.getState(), params.parentField.constraintProperties?.['scale-set']?.value);
        rating = scaleSet?.ratings?.find((rating) => `${rating.id}` === `${riskRatingId}`);
    }

    return (
        <ColorCell
            bgColor={rating?.color ? `#${rating.color}` : undefined}
            label={rating?.label}
            value={params.value}
            formattedValue={params.formattedValue}
            computedCellWidth={params.computedCellWidth}
        />
    );
};

const RichCellRenderHyperlink = (params: OptimisedGridRenderCellParams & { tooltipTitle?: TooltipTitle }) => {
    const value = params.value?.toString();
    const links = ((value && JSON.parse(value)) || []) as Array<Hyperlink>;
    const tooltipTitle = params.tooltipTitle || (
        <ul>
            {links.map((curr, i) => (
                <li key={i}>
                    <a
                        href={curr.linkUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        {curr.linkName || curr.linkUrl}
                    </a>
                </li>
            ))}
        </ul>
    );

    const RenderRichCellHyperlink = richCellWithTooltip(
        React.forwardRef<HTMLDivElement, OptimisedGridRenderCellParams>(function Render(_, ref) {
            return (
                <CellContent ref={ref}>
                    {links && links.length > 0 && (
                        <Box
                            display="flex"
                            flex={1}
                            flexDirection="row"
                        >
                            {links.map((link, i) => {
                                return (
                                    <span key={`${i}_${hash(link.linkUrl)}`}>
                                        <a
                                            href={link.linkUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            {link.linkName || link.linkUrl}
                                        </a>
                                        <span style={{ marginRight: '4px' }}>{i + 1 < links.length ? ',' : ''}</span>
                                    </span>
                                );
                            })}
                        </Box>
                    )}
                </CellContent>
            );
        }),
    );

    return (
        <RenderRichCellHyperlink
            value={params.value}
            formattedValue={params.formattedValue}
            entryFieldValue={params.entryFieldValue}
            computedCellWidth={params.computedCellWidth}
            tooltipTitle={tooltipTitle}
        />
    );
};

export const RichCellRenderDefaultComponent = React.memo(RichCellRenderDefault);
export const RichCellRenderTableComponent = React.memo(RichCellRenderTable);
export const RichCellRenderListComponent = React.memo(RichCellRenderList);
export const RichCellRenderHyperlinkComponent = React.memo(RichCellRenderHyperlink);
export const RichCellRenderRiskRatingComponent = React.memo(RichCellRenderRiskRating);
