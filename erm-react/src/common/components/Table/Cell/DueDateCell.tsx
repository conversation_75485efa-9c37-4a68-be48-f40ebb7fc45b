import React, { forwardRef } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import useTheme from '@mui/system/useTheme';
import { EntryField } from 'register/types';
import { DUE_DATE_STATUS } from '@protecht/ui-library/library/types';

export const CellContent = styled('div')({
    display: 'flex',
    width: 'max-content',
});

export interface OptimisedGridRenderCellParams {
    value: EntryField;
    entryFieldValue?: EntryField;
    formattedValue: string;
    computedCellWidth?: number;
}

const CellRenderDefaultFn = (params: OptimisedGridRenderCellParams) => {
    const theme = useTheme();
    const entryFieldValue = params.value && (params.value as EntryField);
    const dueStatus = entryFieldValue?.parameters?.status[0];
    let dueDateStatusColor: string | undefined = undefined;
    switch (dueStatus) {
        case DUE_DATE_STATUS.OVER_DUE:
            dueDateStatusColor = theme.palette.accentColors?.red;
            break;
        case DUE_DATE_STATUS.DUE_NOW:
            dueDateStatusColor = theme.palette.accentColors?.orange;
            break;
        default:
            dueDateStatusColor = undefined;
    }

    const RenderCellDefault = forwardRef<HTMLDivElement, OptimisedGridRenderCellParams>((props, ref) => {
        const text: EntryField | string = props?.formattedValue ?? props.value?.simpleValue?.[0] ?? '';

        return (
            <CellContent ref={ref}>
                <Box
                    display="flex"
                    px={0.5}
                    flex={1}
                    height="26px"
                    justifyContent="center"
                    alignItems="center"
                >
                    <Typography
                        variant={dueDateStatusColor ? 'body2' : 'body1'}
                        color={dueDateStatusColor || 'protechtGrey.darkBlack'}
                    >
                        {text}
                    </Typography>
                </Box>
            </CellContent>
        );
    });

    RenderCellDefault.displayName = 'renderDueDateCell';

    return <RenderCellDefault {...params} />;
};

export const DueDateCellRenderComponent = React.memo(CellRenderDefaultFn);
