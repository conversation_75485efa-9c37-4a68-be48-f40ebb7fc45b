import React, { ReactElement, useMemo } from 'react';
import { GridRenderCellParams } from '@mui/x-data-grid-pro';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { CellContent, CellWithTooltipProps } from 'common/components/Table/Cell/CellWithTooltip';
import ImageGalleryBase from 'common/components/ImageGalleryBase';
import { WORMS_CLIENT_APP_BASE_URL } from 'config';

export const cellWithInteractiveTooltip = (renderCell: ReactElement) => {
    return TooltipWrapper;

    function TooltipWrapper({ tooltipTitle }: CellWithTooltipProps) {
        return (
            <Tooltip
                title={tooltipTitle}
                disableFocusListener
                disableTouchListener
                style={{ width: '100%' }}
                additionalStyles={{
                    maxWidth: '600px',
                }}
                leaveDelay={100}
                placement="left-start"
            >
                {renderCell}
            </Tooltip>
        );
    }
};

const InteractiveTooltipCellRender = (params: GridRenderCellParams) => {
    const value = params.formattedValue ?? params.value ?? '';
    const imageIds = params.row[params.field]?.parameters?.uuids;
    const images = imageIds
        ? imageIds.map((uuid) => ({
              original: `${WORMS_CLIENT_APP_BASE_URL}/media/download/image?id=${uuid}`,
              thumbnail: `${WORMS_CLIENT_APP_BASE_URL}/media/download/image?id=${uuid}&type=open&imageType=thumbnail&width=100&height=100`,
          }))
        : [];

    const CellToRender = useMemo(() => {
        return <CellContent>{Number(value) > 0 && <span>{value}</span>}</CellContent>;
    }, [value]);

    return cellWithInteractiveTooltip(CellToRender)({
        ...params,
        tooltipTitle:
            images.length > 0 ? (
                <ImageGalleryBase
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    }}
                    items={images}
                    showPlayButton={false}
                    showBullets={false}
                    renderFullscreenButton={() => <></>}
                />
            ) : undefined,
        alwaysShowTooltip: images.length > 0,
    });
};

export const InteractiveTooltipCellRenderComponent = React.memo(InteractiveTooltipCellRender);
