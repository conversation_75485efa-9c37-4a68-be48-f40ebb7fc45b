import React from 'react';
import { mockEntries, mockRegister, mockViews } from './mocks';
import RegisterEntriesSelector from './RegisterEntriesSelector';
import { render, screen } from 'test/utils';
import * as registerApi from 'register/rtkApi';
import { PermissionCodes } from 'common/types';

const mockRegisterId = 12345;
const mockOnSelect = jest.fn();
const mockOnCreateNew = jest.fn();
const mockOnLoadingChange = jest.fn();

jest.mock('view/rtkApi', () => ({
    ...jest.requireActual('view/rtkApi'),
    useVrsGetViewsUsingGetQuery: jest.fn(() => ({
        data: mockViews,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
}));

const setup = (onCreateNew = true) => {
    return render(
        <RegisterEntriesSelector
            registerId={mockRegisterId}
            onSelect={mockOnSelect}
            onLoadingChange={mockOnLoadingChange}
            onCreateNew={onCreateNew ? mockOnCreateNew : undefined}
        />,
    );
};

describe('RegisterEntriesSelector', () => {
    beforeEach(() => {
        jest.spyOn(registerApi, 'useTmrsGetRegisterConfigUsingGet1Query').mockReturnValue({
            data: mockRegister,
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: mockEntries,
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('calls register config api with correct register id', () => {
        setup();

        expect(registerApi.useTmrsGetRegisterConfigUsingGet1Query).toHaveBeenCalledWith({ id: mockRegisterId });
    });

    it('renders loading when loading entries', () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: true,
            isSuccess: false,
            isError: false,
            refetch: jest.fn(),
        });

        setup();

        expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('renders result when loaded', () => {
        setup();

        expect(screen.getByText('Listing 1-1 items of 1 items in total')).toBeInTheDocument();
    });

    it('renders 0 results when no entries found', () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: { totalCount: 0, records: [] },
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });
        setup();

        expect(screen.getByText('Nothing to list')).toBeInTheDocument();
    });

    it('calls onLoadingChange with true when loading entries', () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: true,
            isSuccess: false,
            isError: false,
            refetch: jest.fn(),
        });
        setup();

        expect(mockOnLoadingChange).toHaveBeenCalledWith(true);
    });

    it('calls onLoadingChange with false when entries loaded', () => {
        setup();

        expect(mockOnLoadingChange).toHaveBeenCalledWith(false);
    });

    it('calls onSelect with correct data when clicking on the row', async () => {
        const { user } = setup();

        await user.click(screen.getByRole('cell', { name: '1000022' }));

        expect(mockOnSelect).toHaveBeenCalledWith(
            [{ id: 1000022, name: 'Some control title 1' }],
            [{ col_324530: { fieldId: '179645', fieldName: 'col_324530', label: 'Control Title', simpleValue: ['Some control title 1'] }, id: 1000022 }],
        );
    });

    it('does not render new button when onCreateNew not provided', async () => {
        setup(false);

        expect(screen.queryByTestId('button-new')).not.toBeInTheDocument();
    });

    it('does not render new button when onCreateNew is provided but user does not have correct permission', async () => {
        jest.spyOn(registerApi, 'useTmrsGetRegisterConfigUsingGet1Query').mockReturnValue({
            data: { ...mockRegister, userPermissionCodesForRegister: [PermissionCodes.REGISTER_DATA_IMPORT] },
            isFetching: true,
            isSuccess: false,
            isError: false,
            refetch: jest.fn(),
        });
        setup(true);

        expect(screen.queryByTestId('button-new')).not.toBeInTheDocument();
    });

    it('renders new button when onCreateNew is provided and user has correct permission', async () => {
        jest.spyOn(registerApi, 'useTmrsGetRegisterConfigUsingGet1Query').mockReturnValue({
            data: { ...mockRegister, userPermissionCodesForRegister: [PermissionCodes.REGISTER_DATA_ADD] },
            isFetching: true,
            isSuccess: false,
            isError: false,
            refetch: jest.fn(),
        });
        setup(true);

        expect(screen.getByTestId('button-new')).toBeInTheDocument();
    });
});
