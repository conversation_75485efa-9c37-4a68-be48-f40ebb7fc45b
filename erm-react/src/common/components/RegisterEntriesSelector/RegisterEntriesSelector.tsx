import React, { useCallback, useState } from 'react';

import Box from '@mui/material/Box';
import { SxProps } from '@mui/material/styles';

import useContainerQuery from '@protecht/ui-library/library/hooks/useContainerQuery';

import { IdOnly } from 'app/types';

import ContentLayout from 'common/layouts/ContentLayout';
import CreateNewEntryButtonComponent from '../EntriesTable/shared/CreateNewEntryButtonComponent';
import { DEFAULT_ORDER_FIELD, DEFAULT_SEARCH_FIELD } from 'register/definitions/RegisterDefinitions';
import { IdWithNameRest, PaginRestResultRegisterDataRest, TableMetadataRest, ViewExpressionRest } from 'api/generated/types';
import { getIdentityFieldValue, registerHasPermission } from 'register/utils';
import { PermissionCodes } from 'common/types';
import RegisterEntriesTable from '../EntriesTable/RegisterEntriesTable/RegisterEntriesTable';
import SearchByFieldComponent from '../EntriesTable/RegisterEntriesTable/SearchByFieldComponent';
import { SortType } from 'ui/types';
import TableComponent from '../EntriesTable/RegisterEntriesTable/TableComponent';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import ViewSelectorComponent from '../EntriesTable/RegisterEntriesTable/ViewSelectorComponent';
import SearchByFieldToolbarGroup from '../SearchByField/SearchByFieldToolbarGroup';
import { SearchRequestParams } from '@protecht/ui-library/library/types';

type RegisterEntriesSelectorProps<T> = {
    additionalExpressions?: ViewExpressionRest[];
    context?: string;
    excludedIds?: number[];
    layoutSx?: SxProps;
    multiselect?: boolean;
    registerId: number;
    selected?: IdWithNameRest[];
    viewsActionsDisabled?: boolean;
    onCreateNew?: () => void;
    onLoadingChange?: (isLoading: boolean) => void;
    onSelect: (selectedWithNames: IdWithNameRest[], selected: T[]) => void;
    fetchData?: (requestParams: SearchRequestParams, expressions?: ViewExpressionRest[]) => Promise<PaginRestResultRegisterDataRest | undefined>;
};

const RegisterEntriesSelector = <T extends IdOnly>({
    additionalExpressions,
    context,
    excludedIds,
    layoutSx,
    multiselect = false,
    registerId,
    selected = [],
    viewsActionsDisabled,
    onCreateNew,
    onLoadingChange,
    onSelect,
    fetchData,
}: RegisterEntriesSelectorProps<T>) => {
    const [containerRef, isNarrowView] = useContainerQuery('max-width:450px');

    const [hasAddPermission, setHasAddPermission] = useState(false);
    const [identityColumnName, setIdentityColumnName] = useState('');

    const onRegisterLoaded = useCallback((register: TableMetadataRest) => {
        setHasAddPermission(registerHasPermission(PermissionCodes.REGISTER_DATA_ADD, register));
        setIdentityColumnName(register?.identityColumn?.columnName ?? '');
    }, []);

    const handleSelectEntries = useCallback(
        (selection: T[]) => {
            const newSelectionWithNames: IdWithNameRest[] = selection.map((item) => {
                const identityColumn = identityColumnName ? item[identityColumnName] : undefined;
                const name = getIdentityFieldValue(identityColumn);

                return {
                    id: item.id,
                    name: name ?? item.id.toString(),
                };
            });

            onSelect(newSelectionWithNames, selection);
        },
        [identityColumnName, onSelect],
    );

    return (
        <RegisterEntriesTable
            additionalExpressions={additionalExpressions}
            excludedIds={excludedIds}
            registerId={registerId}
            onLoadingChange={onLoadingChange}
            onRegisterLoaded={onRegisterLoaded}
            fetchData={fetchData}
        >
            <ContentLayout
                toolbar={
                    <ToolbarContainer sx={{ maxWidth: '100%' }}>
                        <ToolbarGroup
                            justifyContent="space-between"
                            flex={1}
                            sx={{ maxWidth: '100%' }}
                        >
                            <SearchByFieldToolbarGroup maxWidth="100%">
                                <SearchByFieldComponent defaultSearchField={DEFAULT_SEARCH_FIELD} />
                            </SearchByFieldToolbarGroup>
                            <ViewSelectorComponent
                                context={context}
                                defaultOrderField={DEFAULT_ORDER_FIELD}
                                defaultOrderType={SortType.ASC}
                                defaultSearchField={DEFAULT_SEARCH_FIELD}
                                identityColumnName={identityColumnName}
                                isNarrowView={isNarrowView}
                                viewsActionsDisabled={viewsActionsDisabled}
                            />
                            {onCreateNew && hasAddPermission && <CreateNewEntryButtonComponent onCreateNew={onCreateNew} />}
                        </ToolbarGroup>
                    </ToolbarContainer>
                }
                sx={layoutSx}
            >
                <Box
                    sx={{ height: '100%' }}
                    ref={containerRef}
                >
                    <TableComponent
                        multiselect={multiselect}
                        selected={selected.map((item) => ({ id: item.id! }))}
                        onSelect={handleSelectEntries}
                    />
                </Box>
            </ContentLayout>
        </RegisterEntriesTable>
    );
};

export default RegisterEntriesSelector;
