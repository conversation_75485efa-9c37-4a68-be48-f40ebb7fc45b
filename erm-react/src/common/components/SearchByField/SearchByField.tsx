import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Typography from '@mui/material/Typography';
import { MenuItemType } from 'ui/types';
import { strings } from 'common/utils/i18n';
import TableToolbarDivider, { ToolbarDividerType } from '../ToolbarSpacing/ToolbarDivider';
import Box from '@mui/material/Box';
import Select from '@protecht/ui-library/library/components/Inputs/Select';
import Search from '@protecht/ui-library/library/components/Inputs/Search';

interface Props {
    fields: MenuItemType<string>[];
    searchValue: string;
    searchField: string;
    onValueChanged: (value: string) => void;
    onPropertyChanged: (property?: string) => void;
    disabled?: boolean;
    dataTestId?: string;
    hideSelectField?: boolean;
    searchPlaceholder?: string;
}

const SearchByField: React.FC<Props> = (props: Props) => {
    const {
        fields,
        searchField,
        onValueChanged,
        onPropertyChanged,
        disabled = false,
        searchValue = '',
        dataTestId,
        hideSelectField = false,
        searchPlaceholder,
    } = props;
    const [value, setValue] = useState<string>(searchValue);

    const getSelectedField = useCallback(
        (fieldValue: string): string | undefined => {
            return fields.find((field) => field.value === fieldValue)?.value ?? fields?.[0]?.value;
        },
        [fields],
    );

    const [selectedField, setSelectedField] = useState<string | undefined>();

    useEffect(() => {
        const newSearchField = getSelectedField(searchField);
        onPropertyChanged(newSearchField);
        setSelectedField(newSearchField);
    }, [fields, searchField, getSelectedField, onPropertyChanged]);

    useEffect(() => {
        setValue(searchValue);
    }, [searchValue]);

    const fieldOptions = useMemo<MenuItemType<string>[]>(() => {
        return fields.map((item) => {
            return {
                value: item.value,
                label: item.label,
            };
        });
    }, [fields]);

    const handleValueChanged = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = event.target.value;
        setValue(newValue);
        onValueChanged(newValue);
    };

    const handleFieldChanged = (value: string) => {
        const selectedField = getSelectedField(value);
        setSelectedField(selectedField);
        onPropertyChanged(selectedField);
    };

    return (
        <>
            <Box sx={{ minWidth: '150px', display: 'flex', flex: 1 }}>
                <Search
                    searchValue={value}
                    onValueChanged={handleValueChanged}
                    disabled={disabled}
                    dataTestId={dataTestId}
                    searchPlaceholder={searchPlaceholder ?? strings('common:placeholder.search')}
                />
            </Box>
            {!hideSelectField && (
                <>
                    <TableToolbarDivider type={ToolbarDividerType.CUSTOM}>
                        <Typography
                            variant="body2"
                            color={disabled ? 'text.disabled' : 'text.primary'}
                        >
                            {strings('common:label.in')}
                        </Typography>
                    </TableToolbarDivider>
                    <Select
                        disabled={disabled}
                        value={selectedField ?? ''}
                        onChange={(value: string) => handleFieldChanged(value)}
                        options={fieldOptions}
                        sx={{ height: '28px', minWidth: '90px', width: 'auto', overflow: 'hidden' }}
                        MenuProps={{
                            sx: {
                                maxWidth: '300px',
                            },
                        }}
                        dataTestId="fieldSelector"
                    />
                </>
            )}
        </>
    );
};

export default SearchByField;
