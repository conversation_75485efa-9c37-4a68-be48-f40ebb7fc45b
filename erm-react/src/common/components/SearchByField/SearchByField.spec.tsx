import { render, screen } from 'test/utils/rtl';
import SearchByField from './SearchByField';
import React from 'react';

const mockedFields = [
    {
        value: 'field1',
        label: 'field1',
    },
    {
        value: 'field2',
        label: 'field2',
    },
];

const onValueChanged = jest.fn();
const onPropertyChanged = jest.fn();

const renderComponent = (hideSelectField = false) => {
    render(
        <SearchByField
            fields={mockedFields}
            hideSelectField={hideSelectField}
            searchValue="test"
            searchField="field1"
            onValueChanged={onValueChanged}
            onPropertyChanged={onPropertyChanged}
        />,
    );
};

describe('<SearchByField/>', () => {
    it('renders Select Field selector when hideSelectField prop is false', () => {
        renderComponent();

        const fieldSelector = screen.getByTestId('fieldSelector');
        expect(fieldSelector).toBeInTheDocument();

        expect(document.body).toMatchSnapshot();
    });

    it('hides Select Field selector when hideSelectField prop is true', () => {
        renderComponent(true);

        const fieldSelector = screen.queryByTestId('fieldSelector');
        expect(fieldSelector).not.toBeInTheDocument();
    });
});
