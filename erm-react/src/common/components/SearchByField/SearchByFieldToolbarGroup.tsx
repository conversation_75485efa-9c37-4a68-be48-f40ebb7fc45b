import React, { FC, PropsWithChildren } from 'react';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import { SxProps } from '@mui/material/styles';

type Props = {
    maxWidth?: string;
    sx?: SxProps;
};

const SearchByFieldToolbarGroup: FC<PropsWithChildren<Props>> = ({ maxWidth = '500px', sx, children }) => {
    return (
        <ToolbarGroup
            flex="1 1 auto"
            maxWidth={maxWidth}
            minWidth="275px"
            sx={sx}
        >
            {children}
        </ToolbarGroup>
    );
};

export default SearchByFieldToolbarGroup;
