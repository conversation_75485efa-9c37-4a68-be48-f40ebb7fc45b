// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SearchByField/> renders Select Field selector when hideSelectField prop is false 1`] = `
<body>
  <div>
    <div
      class="MuiBox-root css-174r0v0"
    >
      <div
        class="MuiFormControl-root MuiTextField-root css-1u097hj-MuiFormControl-root-MuiTextField-root"
      >
        <div
          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd css-1il8eg7-MuiInputBase-root-MuiOutlinedInput-root"
        >
          <input
            aria-invalid="false"
            class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd css-ygtdtl-MuiInputBase-input-MuiOutlinedInput-input"
            id=":r1:"
            placeholder="search"
            tabindex="0"
            type="text"
            value="test"
          />
          <div
            class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1laqsz7-MuiInputAdornment-root"
          >
            <div
              class="MuiBox-root css-kiw6dm"
              data-testid="input-clear-button"
            >
              <div
                class="MuiBox-root css-uzvlaq"
              >
                <svg
                  data-icon="clear-outlined"
                  data-testid="input-clear-icon"
                  fill="currentColor"
                  height="18"
                  viewBox="0 0 24 24"
                  width="18"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2C6.47581 2 2 6.47581 2 12C2 17.5242 6.47581 22 12 22C17.5242 22 22 17.5242 22 12C22 6.47581 17.5242 2 12 2ZM12 20.0645C7.54435 20.0645 3.93548 16.4556 3.93548 12C3.93548 7.54435 7.54435 3.93548 12 3.93548C16.4556 3.93548 20.0645 7.54435 20.0645 12C20.0645 16.4556 16.4556 20.0645 12 20.0645ZM16.1048 9.49194L13.5968 12L16.1048 14.5081C16.2944 14.6976 16.2944 15.004 16.1048 15.1935L15.1935 16.1048C15.004 16.2944 14.6976 16.2944 14.5081 16.1048L12 13.5968L9.49194 16.1048C9.30242 16.2944 8.99597 16.2944 8.80645 16.1048L7.89516 15.1935C7.70565 15.004 7.70565 14.6976 7.89516 14.5081L10.4032 12L7.89516 9.49194C7.70565 9.30242 7.70565 8.99597 7.89516 8.80645L8.80645 7.89516C8.99597 7.70565 9.30242 7.70565 9.49194 7.89516L12 10.4032L14.5081 7.89516C14.6976 7.70565 15.004 7.70565 15.1935 7.89516L16.1048 8.80645C16.2944 8.99597 16.2944 9.30242 16.1048 9.49194Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
            </div>
            <svg
              color="#1B4AD5"
              data-icon="search"
              fill="currentColor"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.465 13.329h-.599l-.212-.205a4.9 4.9 0 0 0 1.19-3.202 4.921 4.921 0 1 0-2.059 4 5 5 0 0 0 .34-.268l.204.212v.599L17.872 19 19 17.872zm-4.543 0a3.403 3.403 0 0 1-3.408-3.407 3.403 3.403 0 0 1 3.408-3.408 3.403 3.403 0 0 1 3.407 3.408 3.403 3.403 0 0 1-3.407 3.407"
                fill="currentColor"
              />
            </svg>
          </div>
          <fieldset
            aria-hidden="true"
            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
          >
            <legend
              class="css-13wgbfv"
            >
              <span
                class="notranslate"
              >
                ​
              </span>
            </legend>
          </fieldset>
        </div>
      </div>
    </div>
    <div
      class="MuiBox-root css-0"
    >
      <p
        class="MuiTypography-root MuiTypography-body2 css-c5c3h7-MuiTypography-root"
      >
        in
      </p>
    </div>
    <div
      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary css-13bb4z2-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
      data-testid="fieldSelector"
    >
      <div
        aria-controls=":r2:"
        aria-expanded="false"
        aria-haspopup="listbox"
        class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-j5h97z-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
        role="combobox"
        tabindex="0"
      >
        <div
          class="MuiBox-root css-9ou7bg"
        >
          <p
            class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-1ldt5e-MuiTypography-root"
          >
            field1
          </p>
        </div>
      </div>
      <input
        aria-hidden="true"
        aria-invalid="false"
        class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
        tabindex="-1"
        value="field1"
      />
      <svg
        aria-hidden="true"
        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-e6po5i-MuiSvgIcon-root-MuiSelect-icon"
        data-testid="KeyboardArrowDownIcon"
        focusable="false"
        viewBox="0 0 24 24"
      >
        <path
          d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"
        />
      </svg>
      <fieldset
        aria-hidden="true"
        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
      >
        <legend
          class="css-13wgbfv"
        >
          <span
            class="notranslate"
          >
            ​
          </span>
        </legend>
      </fieldset>
    </div>
  </div>
</body>
`;
