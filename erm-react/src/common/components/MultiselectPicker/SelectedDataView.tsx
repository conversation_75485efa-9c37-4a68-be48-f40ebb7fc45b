import React from 'react';
import { useTheme } from '@mui/material/styles';
import { isEmpty } from 'lodash';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import { IdWithNameRest } from 'api/generated/types';

type Props<T> = {
    isLoading?: boolean;
    selected?: T[];
    renderContent?: (item: T) => React.ReactElement;
    onRemove?: (item: T) => void;
};

const SelectedWrapper = styled('div')(({ theme }) => ({
    maxHeight: '177px',
    backgroundColor: theme.palette.protechtGrey?.grey_245,
    border: '1px solid ' + theme.palette.protechtGrey?.grey_231,
    borderRadius: '2px',
    overflow: 'auto',
}));

const ItemWrapper = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.background.default,
    borderRadius: '2px',
    padding: '6px 12px',
    margin: '6px',
    '&:hover': {
        '.actions': { opacity: 1 },
    },
}));

const Actions = styled('div')({
    opacity: 0,
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
});

const SelectedDataView = <T extends IdWithNameRest>(props: Props<T>): JSX.Element => {
    const theme = useTheme();

    const { isLoading = false, selected = [], renderContent, onRemove } = props;

    const renderActions = (item: T) => {
        if (!onRemove) {
            return <div />;
        }

        return (
            <Box
                display="flex"
                alignItems="center"
                onClick={() => onRemove(item)}
            >
                <FontAwesomeIcon
                    icon={faTrashAlt}
                    color={theme.palette.error.main}
                />
            </Box>
        );
    };

    const renderItem = (item: T) => {
        return (
            <ItemWrapper
                key={item.id}
                display="flex"
                flex={1}
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
            >
                {renderContent?.(item) || <Typography variant="body1">{item.name}</Typography>}
                <Actions className="actions">{renderActions(item)}</Actions>
            </ItemWrapper>
        );
    };

    const renderEmpty = () => {
        return (
            <Box
                display="flex"
                flex={1}
                justifyContent="center"
                alignItems="center"
                p={1}
            >
                <Typography
                    variant="body1"
                    color="textSecondary"
                >
                    No items
                </Typography>
            </Box>
        );
    };

    if (isLoading) {
        return (
            <Box
                display={'flex'}
                flex={1}
                justifyContent="center"
            >
                <CircularProgress size={20} />
            </Box>
        );
    }

    return <SelectedWrapper>{isEmpty(selected) ? renderEmpty() : selected.map((item) => renderItem(item))}</SelectedWrapper>;
};

export default SelectedDataView;
