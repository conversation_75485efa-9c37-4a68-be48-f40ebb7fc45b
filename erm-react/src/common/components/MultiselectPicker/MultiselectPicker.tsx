import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';

import InputSelector from 'common/components/InputSelector';
import { ColumnType } from 'register/types';
import SelectedDataView from './SelectedDataView';
import { SelectorType } from 'common/types';
import { IdWithNameRest } from 'api/generated/types';
import { IdOnly } from 'app/types';

type MultiPickerProps<T> = {
    selected?: IdWithNameRest[];
    onSelect: (currentSelection: IdWithNameRest[]) => void;
    editable?: boolean;
    type: ColumnType | SelectorType;
    onDataLoad?: (query: string) => Promise<T[]>;
};

const MultiselectPicker = <T extends IdOnly>(props: MultiPickerProps<T>) => {
    // TODO: the prop doesnt make sense for this field
    const { selected = [], onSelect, editable: _editable = true } = props;
    const [currentSelection, setCurrentSelection] = useState<IdWithNameRest[]>(selected);

    useEffect(() => {
        setCurrentSelection(selected);
    }, [selected]);

    useEffect(() => {
        onSelect(currentSelection);
    }, [currentSelection, onSelect]);

    const onItemRemoval = (item: IdWithNameRest) => {
        setCurrentSelection(currentSelection.filter((i) => i.id !== item.id));
    };

    return (
        <>
            <Box pb={1}>
                <InputSelector
                    multiselect={true}
                    selected={currentSelection}
                    onSelect={(newSelection) => setCurrentSelection(newSelection)}
                    onDataLoad={props.onDataLoad}
                    type={props.type}
                    filterData={props.type === SelectorType.BUSINESS_UNIT ? undefined : []}
                />
            </Box>
            <SelectedDataView
                selected={currentSelection}
                onRemove={onItemRemoval}
            />
        </>
    );
};

export default MultiselectPicker;
