import React, { useCallback, useEffect, useState } from 'react';
import { PagingResult } from 'common/api/types';
import Grid from '@mui/material/Grid';
import { RegisterRest } from 'register/types';
import { getSearchFields } from 'common/utils/definitions';
import { useTheme } from '@mui/material/styles';
import useSearchExpression from 'common/hooks/useSearchExpression';
import Search from '@protecht/ui-library/library/components/Inputs/Search';
import useAsync from 'common/hooks/useAsync';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { Table } from '@protecht/ui-library/library/components/Table';
import { strings } from 'common/utils/i18n';
import { getMetricDataSource } from 'metrics/api';
import { colDef } from './const';
import { DataSourceScope, DataSource } from './types';
import { getRegister } from 'register/api';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';

type Props = {
    selected?: RegisterRest;
    onClose: () => void;
    onSelect: (dataSource: RegisterRest) => void;
    moduleName: DataSourceScope;
};

const DataSourceSelector: React.FC<Props> = ({ selected, onClose, onSelect, moduleName }: Props) => {
    const theme = useTheme();

    const [searchField] = getSearchFields(colDef);

    const [selectedDataSource, setSelectedDataSource] = useState<DataSource | undefined>(
        selected
            ? {
                  id: selected.id,
                  tableName: selected.tableName,
                  label: selected.label,
              }
            : undefined,
    );

    const [searchValue, setSearchValue] = useState<string>('');
    const searchExpression = useSearchExpression(searchValue, searchField?.value);

    const { asyncLoad: loadDataSource, response: dataSource, isLoading } = useAsync<PagingResult<RegisterRest>>();
    const { asyncLoad: loadDataSourceDetail } = useAsync<RegisterRest>();

    const [params, setParams] = useState<SearchRequestParams | undefined>(undefined);

    useEffect(() => {
        if (params && searchExpression) {
            void loadDataSource(getMetricDataSource(moduleName, { expressions: searchExpression.value ? [searchExpression] : [] }, params));
        }
    }, [params, searchExpression, loadDataSource]);

    const confirmSelection = useCallback(async () => {
        if (selectedDataSource) {
            const response = await loadDataSourceDetail(getRegister(selectedDataSource.id));
            if (response) {
                onSelect(response);
                onClose();
            }
        }
    }, [loadDataSourceDetail, onClose, onSelect, selectedDataSource]);

    const onSelectionChange = useCallback((selection: DataSource) => {
        setSelectedDataSource(selection[0]);
    }, []);

    return (
        <Dialog
            visible={true}
            width={900}
            height={674}
            onOutsideClickClose={onClose}
            title={strings('register:title.selectRegister')}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!selectedDataSource || isLoading}
                        onClick={confirmSelection}
                    >
                        {strings('ermConstants:button_label_select')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                spacing={1}
                sx={{ height: '100%' }}
            >
                <Grid
                    item
                    xs={12}
                >
                    <Search
                        key="register-selector-search-by"
                        aria-label="register-selector-search-by"
                        dataTestId={'register-selector-field-search'}
                        searchValue={searchValue}
                        onValueChanged={(event) => {
                            setSearchValue(event.target.value);
                        }}
                        searchPlaceholder={strings('common:placeholder.search')}
                    />
                </Grid>
                <Grid
                    item
                    xs={12}
                    sx={{ height: 'calc(100% - ' + theme.spacing(4) + ')' }}
                >
                    <Table
                        columns={colDef}
                        multiselect={false}
                        rows={dataSource?.records ?? []}
                        totalCount={dataSource?.totalCount || 0}
                        loading={isLoading}
                        params={params}
                        onParamsChanged={setParams}
                        selected={selectedDataSource ? [selectedDataSource] : []}
                        onSelect={onSelectionChange}
                    />
                </Grid>
            </Grid>
        </Dialog>
    );
};

export default DataSourceSelector;
