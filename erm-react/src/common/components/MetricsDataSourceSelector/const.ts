import { DataGridColDef, FilterType } from '@protecht/ui-library/library/types';
import { MIN_TABLE_COLUMN_WIDTH } from 'common/constants';
import { strings } from 'common/utils/i18n';

export const colDef: DataGridColDef[] = [
    {
        field: 'label',
        headerName: strings('common:label.registerName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        groupable: false,
    },
];

export const DEFAULT_SEARCH_FIELD = 'label';
