export const calculateTableHeight = (rowHeight: number, multiselect: boolean, dataHeight?: number) => {
    const MIN_HEIGHT = 102;
    const BUFFER = 2;

    // single select:
    // ignore height prop and calculate height:
    // 2 rows without footer (1 header row + 1 data row) + buffer
    if (!multiselect) {
        return rowHeight * 2 + BUFFER;
    }

    // multi select with height lower than minimum:
    // 5 rows (1 header row + 3 data rows + 1 footer row) + buffer
    if (!dataHeight || dataHeight <= MIN_HEIGHT) {
        return rowHeight * 5 + BUFFER;
    }

    // multiselect with height higher than minimum:
    // given height + 2 rows (1 header row + 1 footer row) + buffer
    return dataHeight + rowHeight * 2 + BUFFER;
};
