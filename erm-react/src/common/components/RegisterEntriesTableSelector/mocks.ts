import { PaginRestResultRegisterDataRest, TableMetadataRest, ViewRestResponse } from 'api/generated/types';
import { PermissionCodes } from 'common/types';

export const mockViews: ViewRestResponse = {
    views: [],
};

export const mockRegister: TableMetadataRest = {
    id: 12345,
    tableName: 'table_1234',
    userPermissionCodesForRegister: [PermissionCodes.REGISTER_DATA_ADD],
    identityColumn: {
        id: 179645,
        label: 'Control Title',
        columnName: 'col_324530',
        filterType: 'STRING',
        hideLabel: false,
        required: true,
        archived: false,
        columnType: 'MULTILINE_TEXT',
        layoutRow: 1,
        layoutColumn: 'left',
        constraintProperties: {
            rows: {
                value: '1',
            },
        },
        uuid: '884D847B-0DB1-42B5-BA3B-E1333C9BF079',
    },
    sections: [
        {
            id: 1,
            label: 'CORE',
            fields: [
                {
                    id: 3,
                    label: 'Create Date',
                    columnName: 'createdate',
                    filterType: 'DATE',
                    hideLabel: false,
                    required: true,
                    archived: false,
                    columnType: 'TIMESTAMP',
                    layoutRow: 2,
                    layoutColumn: 'both',
                    uuid: '5B9EE905-20E7-413D-8081-C9B21D527FEE',
                },
                {
                    id: 5,
                    label: 'Last Modified Date',
                    columnName: 'lastmodifieddate',
                    filterType: 'DATE',
                    hideLabel: false,
                    required: true,
                    archived: false,
                    columnType: 'TIMESTAMP',
                    layoutRow: 4,
                    layoutColumn: 'both',
                    uuid: 'D822AD97-7DE3-4CF9-BEC8-39287A16CC24',
                },
                {
                    id: 7,
                    label: 'Business Unit',
                    columnName: 'businessunit',
                    filterType: 'STRING',
                    hideLabel: false,
                    required: true,
                    archived: false,
                    columnType: 'BUSINESS_UNIT',
                    layoutRow: 1,
                    layoutColumn: 'left',
                    uuid: 'BB774B75-6835-4C85-B070-F06DABBA89F4',
                },
                {
                    id: 4,
                    label: 'Created By',
                    columnName: 'createdby',
                    filterType: 'STRING',
                    hideLabel: false,
                    required: true,
                    archived: false,
                    columnType: 'USER_INFO',
                    layoutRow: 0,
                    layoutColumn: 'right',
                },
                {
                    id: 6,
                    label: 'Last Modified By',
                    columnName: 'lastmodifiedby',
                    filterType: 'STRING',
                    hideLabel: false,
                    required: true,
                    archived: false,
                    columnType: 'USER_INFO',
                    layoutRow: 1,
                    layoutColumn: 'right',
                },
                {
                    id: 12,
                    label: 'UUID',
                    columnName: 'uuid',
                    filterType: 'STRING',
                    hideLabel: false,
                    required: false,
                    archived: false,
                    columnType: 'SINGLELINE_TEXT',
                    layoutRow: 12,
                    layoutColumn: 'both',
                },
            ],
        },

        {
            id: 108248,
            label: 'Sample section',
            applicationId: 3422,
            order: 0,
            editable: true,
            scope: 'Application',
            fields: [
                {
                    id: 179645,
                    label: 'Control Title 2',
                    columnName: 'col_324530',
                    filterType: 'STRING',
                    hideLabel: false,
                    required: false,
                    archived: false,
                    columnType: 'SINGLELINE_TEXT',
                    layoutRow: 1,
                    layoutColumn: 'left',
                    constraintProperties: {
                        character_length: {
                            value: '250',
                        },
                    },
                    uuid: '621D4D0C-024F-4E3A-9317-F8EF4BEFB6A6',
                },
                {
                    id: 179648,
                    label: 'Another Field',
                    columnName: 'col_324540',
                    filterType: 'STRING',
                    hideLabel: false,
                    required: false,
                    archived: false,
                    columnType: 'SINGLELINE_TEXT',
                    layoutRow: 2,
                    layoutColumn: 'left',
                    constraintProperties: {
                        character_length: {
                            value: '250',
                        },
                    },
                    uuid: '20CB231D-E8E6-4EF7-8697-6F97B6EFE931',
                },
            ],
        },
    ],
};

export const mockEntries = {
    totalCount: 2,
    records: [
        {
            skipSystemDates: false,
            record: {
                label: 'Control Register',
                id: 1000022,
                sections: [
                    {
                        editable: true,
                        label: 'CORE',
                    },
                    {
                        editable: true,
                        label: 'Control - Details',
                        fields: [
                            {
                                label: 'Control Title',
                                fieldId: '179645',
                                fieldName: 'col_324530',
                                simpleValue: ['Some control title 1'],
                            },
                        ],
                    },
                    {
                        editable: true,
                        label: 'Control - Owner Self Assessment',
                    },
                    {
                        editable: true,
                        label: 'Control - Testing',
                    },
                    {
                        editable: true,
                        label: 'Control - ERC Review',
                    },
                    {
                        editable: true,
                        label: 'Control - OneSum migration data',
                    },
                    {
                        editable: true,
                        label: 'Control - GRACE Legacy',
                    },
                ],
                applicationName: 'Registers',
                status: 'Current',
                deleted: false,
            },
            editableByCurrentUser: true,
            completed: true,
        },
        {
            skipSystemDates: false,
            record: {
                label: 'Control Register',
                id: 1000023,
                sections: [
                    {
                        editable: true,
                        label: 'CORE',
                    },
                    {
                        editable: true,
                        label: 'Control - Details',
                        fields: [
                            {
                                label: 'Control Title',
                                fieldId: '179645',
                                fieldName: 'col_324530',
                                simpleValue: ['Some control title 1'],
                            },
                        ],
                    },
                    {
                        editable: true,
                        label: 'Control - Owner Self Assessment',
                    },
                    {
                        editable: true,
                        label: 'Control - Testing',
                    },
                    {
                        editable: true,
                        label: 'Control - ERC Review',
                    },
                    {
                        editable: true,
                        label: 'Control - OneSum migration data',
                    },
                    {
                        editable: true,
                        label: 'Control - GRACE Legacy',
                    },
                ],
                applicationName: 'Registers',
                status: 'Current',
                deleted: false,
            },
            editableByCurrentUser: true,
            completed: true,
        },
    ],
    maxPage: 50,
} as PaginRestResultRegisterDataRest;

export const mockEntriesSingle = {
    totalCount: 1,
    records: [mockEntries.records?.[0]],
    maxPage: 50,
} as PaginRestResultRegisterDataRest;
