import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import { GridRowParams } from '@mui/x-data-grid';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { Portal } from '@mui/base/Portal';

import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { Table } from '@protecht/ui-library/library/components/Table';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import Divider from '@protecht/ui-library/library/components/StyledDivider';
import useContainerQuery from '@protecht/ui-library/library/hooks/useContainerQuery';

import { IdWithNameAndStatusRest, ShowAll } from 'app/types';
import { useView } from 'view/hooks/useView';
import ViewSelector from 'view/components/ViewSelector';
import { SortType } from 'ui/types';
import { ViewExpressionRest, ViewRest } from 'api/generated/types';
import { ColumnType, RegisterEntryRest, RegisterRest, RegisterType } from 'register/types';
import { DEFAULT_ORDER_FIELD, getRegisterColDef } from 'register/definitions/RegisterDefinitions';
import { getEntryTableRow, registerHasPermission } from 'register/utils';
import { useGetRegisterEntriesSearchPostQuery, useTmrsGetRegisterConfigUsingGet1Query } from 'register/rtkApi';
import { strings } from 'common/utils/i18n';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import { PermissionCodes, SYSTEM_COLUMN } from 'common/types';
import Selector from 'common/components/Selector';
import { getSelectorMetadata } from 'common/utils/definitions';
import { ExpressionType } from 'view/types';
import RegisterEntry from 'register/components/RegisterEntry';
import { calculateTableHeight } from './utils';
import { getErrorCode } from 'api/utils';
import { SHOW_ALL } from 'view/components/ViewSelector/ViewSelector';
import NoPermissionMessage from 'register/components/RegisterField/TableRegisterField/NoPermissionMessage';
import { isRegisterReadOnly } from 'register/components/RegisterField/TableRegisterField/utils';
import { ClearIcon } from 'common/icons/ClearIcon';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';
import LabelWithTooltip from '../LabelWithTooltip/LabelWithTooltip';

export type SearchEntriesParams = {
    parentTableName?: string;
    parentEntryId?: number;
    subtableColumn?: string;
};

type RegisterEntriesSelectorProps = {
    registerId: number;
    entryFormContainer: Element | null;
    multiselect?: boolean;
    height?: number;
    context?: string;
    hideAddButton?: boolean;
    selectedEntries: IdWithNameAndStatusRest[];
    isAnonymous?: boolean;
    readOnly?: boolean;
    onSelectedChange: (selected: IdWithNameAndStatusRest[]) => void;
    label?: string;
    labelTooltip?: string;
    additionalExpressions?: ViewExpressionRest[];
    searchEntriesParams: SearchEntriesParams;
};

const RegisterEntriesTableSelector: React.FC<RegisterEntriesSelectorProps> = ({
    registerId,
    entryFormContainer,
    multiselect = false,
    label,
    labelTooltip,
    height,
    hideAddButton = false,
    isAnonymous = false,
    readOnly = false,
    selectedEntries,
    onSelectedChange,
    additionalExpressions,
    context,
    searchEntriesParams,
}) => {
    const theme = useTheme();
    const [entriesSelectorVisible, setEntriesSelectorVisible] = useState<boolean>(false);
    const [entryFormVisible, setEntryFormVisible] = useState<boolean>(false);
    const [entryFormId, setEntryFormId] = useState<number | undefined>(undefined);
    const [selectedView, setSelectedView] = useState<ViewRest | ShowAll | undefined>(undefined);
    const [requestParams, setRequestParams] = useState<SearchRequestParams | undefined>(undefined);
    const [currentUserSelection, setCurrentUserSelection] = useState<IdWithNameAndStatusRest[]>([]);
    const [viewParamsLoaded, setViewParamsLoaded] = useState<boolean>(false);
    const [viewExp, setViewExp] = useState<ViewExpressionRest[] | undefined>(undefined);
    const [hasScrollX, setHasScrollX] = useState<boolean>(false);
    const [containerRef, isNarrowView] = useContainerQuery('max-width:450px');

    const selectorMetadata = useMemo(() => getSelectorMetadata(ColumnType.TABLE), []);
    const { data: register, isError: isErrorRegister } = useTmrsGetRegisterConfigUsingGet1Query({ id: registerId });
    const hasAddNewPermission = registerHasPermission(PermissionCodes.REGISTER_DATA_ADD, register);
    const viewsContext = context || register?.tableName || '';

    const registerColDef = useMemo(() => {
        if (register) {
            return getRegisterColDef(register as RegisterRest);
        } else {
            return [];
        }
    }, [register]);

    const { columns, columnVisibilityModel, viewParams, viewExpressions } = useView(registerColDef, requestParams, isNarrowView ? SHOW_ALL : selectedView);

    const viewColumns = useMemo(() => {
        if (isNarrowView) {
            return columns.map((column) => ({
                ...column,
                hidden: column.field !== (register?.identityColumn ? register?.identityColumn.columnName : SYSTEM_COLUMN.ID),
            }));
        }

        return columns;
    }, [columns, isNarrowView, register?.identityColumn]);

    const apiExpressions = useMemo(() => {
        const expressions: ViewExpressionRest[] = [];

        expressions.push({
            id: 0,
            expression: ExpressionType.IN,
            property: SYSTEM_COLUMN.ID,
            type: 'STRING',
            value: selectedEntries.length > 0 ? selectedEntries.map((entry) => entry.id).join(';') : '',
        });

        if (viewExp) {
            viewExp.forEach((expression) => expressions.push(expression));
        }
        return expressions;
    }, [viewExp, selectedEntries]);

    const {
        data: entries,
        isFetching: loadingEntries,
        refetch: refetchEntries,
        isError: isErrorEntries,
        error: registerEntriesError,
    } = useGetRegisterEntriesSearchPostQuery(
        {
            regId: registerId,
            ...requestParams,
            ...searchEntriesParams,
            body: apiExpressions,
        },
        {
            skip: !viewParamsLoaded,
        },
    );

    const hasTableRecords = (entries?.records ?? []).length > 0;

    // hardcoded row height to 34 for now
    // TODO: we need to refactor setting height for table rows to not use ThemeConfig from gwt
    const tableHeight = useMemo(() => calculateTableHeight(34, multiselect, height), [height, multiselect]);

    const deleteButtonDisabled = isErrorEntries || (multiselect ? currentUserSelection.length === 0 : selectedEntries.length === 0);

    useEffect(() => {
        if (viewParams !== undefined) {
            setViewParamsLoaded(true);
            setRequestParams(viewParams);
            setViewExp(viewExpressions);
        }
    }, [viewParams, viewExpressions]);

    const handleTableFieldSelectEntries = useCallback((selection: IdWithNameAndStatusRest[]) => {
        setCurrentUserSelection(selection);
    }, []);

    const handleSelectView = useCallback((view: ViewRest | null | undefined) => {
        setSelectedView(view);
    }, []);

    const handleTableParamsChange = useCallback(
        (params: SearchRequestParams) => {
            setRequestParams({ ...requestParams, ...params });
        },
        [requestParams],
    );

    const onCreateNewEntry = useCallback(() => {
        setEntryFormId(undefined);
        setEntriesSelectorVisible(false);
        setEntryFormVisible(true);
    }, []);

    const onEditExistingEntry = useCallback((id: number) => {
        setEntryFormId(id);
        setEntryFormVisible(true);
    }, []);

    const handleSaveEntry = useCallback(
        (data: IdWithNameAndStatusRest) => {
            setEntryFormVisible(false);
            if (selectedEntries.findIndex((entry) => entry.id === data.id) === -1) {
                const newSelectedEntries = multiselect ? [...selectedEntries, data] : [data];
                onSelectedChange(newSelectedEntries);
            }
            void refetchEntries();
        },
        [refetchEntries, selectedEntries, multiselect, onSelectedChange],
    );

    const handleCancelEntry = useCallback(() => {
        setEntryFormVisible(false);
    }, []);

    const handleDeleteEntries = useCallback(() => {
        setCurrentUserSelection([]);
        const newEntries = multiselect ? selectedEntries.filter((entry) => currentUserSelection.findIndex((selected) => selected.id === entry.id) === -1) : [];
        onSelectedChange(newEntries);
    }, [multiselect, currentUserSelection, onSelectedChange, selectedEntries]);

    const handleSelectEntries = useCallback(
        (entries: IdWithNameAndStatusRest[]) => {
            setEntriesSelectorVisible(false);
            onSelectedChange(entries);
        },
        [onSelectedChange],
    );

    const handleTableSizeChange = useCallback((dimensions) => {
        setHasScrollX(dimensions.hasScrollX);
    }, []);

    const isSubTableReadOnly = useMemo(() => {
        if (register) {
            return isRegisterReadOnly(register);
        }
    }, [register]);

    if (isErrorRegister || getErrorCode(registerEntriesError) === 403) {
        return (
            <NoPermissionMessage
                fieldLabel={label}
                labelTooltip={labelTooltip}
                registerLabel={register?.label}
                isErrorRegister={!!isErrorRegister}
            />
        );
    }

    return (
        <>
            <Grid
                container
                direction="column"
                flexWrap="nowrap"
                ref={containerRef}
            >
                <Grid
                    item
                    style={{ maxWidth: '100%' }}
                >
                    <ToolbarContainer>
                        <ToolbarGroup
                            justifyContent="space-between"
                            flex={1}
                            overflow="hidden"
                        >
                            <LabelWithTooltip
                                label={label}
                                tooltip={labelTooltip}
                            />
                            <ViewSelector
                                key="selectView"
                                hide={isNarrowView}
                                selectedView={isNarrowView ? SHOW_ALL : selectedView}
                                onViewSelected={handleSelectView}
                                context={viewsContext}
                                columns={registerColDef}
                                defaultOrderBy={requestParams?.orderBy || DEFAULT_ORDER_FIELD}
                                defaultOrderType={requestParams?.orderType || SortType.ASC}
                                editDisabled={isAnonymous}
                                forceRefetch={false}
                                register={register as RegisterRest}
                            />
                            {!isNarrowView && (!readOnly || hasTableRecords) && (
                                <Divider
                                    orientation="vertical"
                                    sx={{ height: '20px' }}
                                    margin={0}
                                />
                            )}
                            {!readOnly && hasTableRecords && (
                                <IconButton
                                    size="small"
                                    disabled={deleteButtonDisabled}
                                    color={'primary'}
                                    data-testid="button-delete"
                                    onClick={handleDeleteEntries}
                                >
                                    <ClearIcon
                                        $disabled={deleteButtonDisabled}
                                        height={20}
                                        width={20}
                                        color={deleteButtonDisabled ? theme.palette.protechtGrey?.grey_192 : theme.palette.accentColors?.red}
                                    />
                                </IconButton>
                            )}
                            {hasTableRecords && (
                                <Button
                                    {...ButtonStyles.tableToolbarButton}
                                    variant="outlined"
                                    dataTestId="button-open"
                                    disabled={isErrorEntries || (multiselect ? currentUserSelection.length !== 1 : selectedEntries.length === 0)}
                                    onClick={() => onEditExistingEntry(multiselect ? currentUserSelection[0].id : selectedEntries[0].id)}
                                >
                                    {strings('common:button.open')}
                                </Button>
                            )}
                            {!hideAddButton && !readOnly && register?.registerType !== RegisterType.PRIVATE && (
                                <Button
                                    {...ButtonStyles.tableToolbarButton}
                                    variant="outlined"
                                    onClick={() => setEntriesSelectorVisible(true)}
                                    dataTestId="button-add"
                                >
                                    {multiselect ? strings('common:button.add') : strings('common:button.select')}
                                </Button>
                            )}
                            {!readOnly && hasAddNewPermission && (
                                <Button
                                    {...ButtonStyles.tableToolbarButton}
                                    variant="outlined"
                                    onClick={onCreateNewEntry}
                                    dataTestId="button-new"
                                    startIcon={<Add />}
                                >
                                    {strings('common:button.new')}
                                </Button>
                            )}
                        </ToolbarGroup>
                    </ToolbarContainer>
                </Grid>
                <Grid
                    item
                    flex="0 0 auto"
                    width="100%"
                    minHeight={!multiselect && (loadingEntries || hasScrollX) ? tableHeight + 16 : undefined}
                    height={multiselect ? tableHeight + 16 : hasScrollX ? tableHeight + 16 : tableHeight}
                >
                    <Table
                        columns={viewParamsLoaded ? viewColumns : []}
                        columnVisibilityModel={columnVisibilityModel}
                        rows={
                            loadingEntries
                                ? []
                                : entries?.records?.map((entry) => getEntryTableRow(entry.record as RegisterEntryRest, register as RegisterRest)) || []
                        }
                        pageSize={!multiselect ? 1 : undefined}
                        loading={loadingEntries || !viewParamsLoaded}
                        totalCount={entries?.totalCount || 0}
                        params={requestParams}
                        onParamsChanged={handleTableParamsChange}
                        multiselect={multiselect}
                        selected={currentUserSelection}
                        onSelect={handleTableFieldSelectEntries}
                        checkboxSelection={false}
                        onRowDoubleClick={(params: GridRowParams) => onEditExistingEntry(Number(params.id))}
                        noRowsToDisplayMsg={isErrorEntries ? strings('register:error.entries') : ''}
                        hideFooter={!multiselect}
                        onContentSizeChange={handleTableSizeChange}
                    />
                </Grid>
            </Grid>
            {entryFormVisible && (
                <Portal container={() => entryFormContainer}>
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            zIndex: 2,
                            backgroundColor: 'white',
                        }}
                    >
                        <RegisterEntry
                            tableName={register?.tableName}
                            entryId={entryFormId?.toString()}
                            isCreate={!entryFormId}
                            isAnonymous={isAnonymous}
                            onCancel={handleCancelEntry}
                            onSave={handleSaveEntry}
                            isReadOnly={readOnly || isSubTableReadOnly}
                            isSubEntry={true}
                        />
                    </Box>
                </Portal>
            )}
            {entriesSelectorVisible && (
                <Selector
                    visible={true}
                    onClose={() => setEntriesSelectorVisible(false)}
                    onSubmit={handleSelectEntries}
                    onCreateNew={hasAddNewPermission ? onCreateNewEntry : undefined}
                    title={selectorMetadata.title}
                    type={ColumnType.TABLE}
                    multiselect={multiselect}
                    selected={selectedEntries}
                    filterData={{ registerId, additionalExpressions, viewsActionsDisabled: isAnonymous, context: viewsContext }}
                />
            )}
        </>
    );
};

export default RegisterEntriesTableSelector;
