import React from 'react';
import { mockEntries, mockEntriesSingle, mockRegister, mockViews } from './mocks';
import RegisterEntriesTableSelector from './RegisterEntriesTableSelector';
import RegisterEntry from 'register/components/RegisterEntry';
import Selector from 'common/components/Selector';
import { render, screen } from 'test/utils';
import * as registerApi from 'register/rtkApi';
import { PermissionCodes } from 'common/types';
import { fireEvent } from '@testing-library/react';
import { ColumnType, RegisterType } from 'register/types';
import { IdWithNameAndStatusRest } from 'app/types';

const mockRegisterId = 12345;
const mockRegisterName = 'Register name';
const mockLabel = 'Some label';
const mockLabelTooltip = 'Some tooltip';
const mockOnSelectedChange = jest.fn();

jest.mock('view/rtkApi', () => ({
    ...jest.requireActual('view/rtkApi'),
    useVrsGetViewsUsingGetQuery: jest.fn(() => ({
        data: mockViews,
        isLoading: false,
        isSuccess: true,
        isError: false,
        refetch: jest.fn(),
    })),
}));

jest.mock('register/components/RegisterEntry', () => {
    return {
        __esModule: true,
        default: jest.fn(({ onSave, onCancel }) => (
            <div data-testid="mock-register-entry-form">
                <button
                    onClick={() => onSave({ id: 876, name: 'Some new entry name' })}
                    data-testid="mock-entry-save"
                >
                    save
                </button>
                <button
                    onClick={onCancel}
                    data-testid="mock-entry-cancel"
                >
                    cancel
                </button>
            </div>
        )),
    };
});

jest.mock('common/components/Selector', () => {
    return {
        __esModule: true,
        default: jest.fn(({ onSubmit, onClose, onCreateNew }) => (
            <div data-testid="mock-selector">
                <button
                    onClick={() => onSubmit([{ id: 987, name: 'Some new entry name' }])}
                    data-testid="mock-selector-submit"
                >
                    submit
                </button>
                <button
                    onClick={onCreateNew}
                    data-testid="mock-selector-new"
                >
                    new
                </button>
                <button
                    onClick={onClose}
                    data-testid="mock-selector-cancel"
                >
                    cancel
                </button>
            </div>
        )),
    };
});

const mockSearchEntriesParams = {
    parentTableName: 'some-table',
    parentEntryId: 12345,
    subtableColumn: 'some-column',
};

type SetupOptions = {
    multiselect?: boolean;
    hideAddButton?: boolean;
    selectedEntries?: IdWithNameAndStatusRest[];
    isAnonymous?: boolean;
    readOnly?: boolean;
    label?: string;
    labelTooltip?: string;
};

const WrapperComponent = (options: SetupOptions = {}) => {
    const portalContainerRef = React.useRef(null);
    return (
        <>
            <RegisterEntriesTableSelector
                registerId={mockRegisterId}
                entryFormContainer={portalContainerRef.current}
                multiselect={options?.multiselect ?? true}
                height={400}
                hideAddButton={options?.hideAddButton ?? false}
                selectedEntries={options?.selectedEntries ?? []}
                isAnonymous={options?.isAnonymous ?? false}
                readOnly={options?.readOnly ?? false}
                onSelectedChange={mockOnSelectedChange}
                label={Object.prototype.hasOwnProperty.call(options, 'label') ? options?.label : mockLabel}
                labelTooltip={Object.prototype.hasOwnProperty.call(options, 'labelTooltip') ? options?.labelTooltip : mockLabelTooltip}
                searchEntriesParams={mockSearchEntriesParams}
            />
            <div
                ref={portalContainerRef}
                data-testid="mock-portal-container"
            ></div>
        </>
    );
};

describe('RegisterEntriesTableSelector', () => {
    beforeEach(() => {
        jest.spyOn(registerApi, 'useTmrsGetRegisterConfigUsingGet1Query').mockReturnValue({
            data: mockRegister,
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: mockEntries,
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('calls register config api with correct register id', () => {
        render(<WrapperComponent />);

        expect(registerApi.useTmrsGetRegisterConfigUsingGet1Query).toHaveBeenCalledWith({ id: mockRegisterId });
    });

    it('renders label and tooltip when provided', () => {
        render(<WrapperComponent />);

        expect(screen.getByTestId('label')).toHaveTextContent(mockLabel);
        expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    });

    // label and tooltip
    it('does not render label when not provided', () => {
        render(<WrapperComponent label={undefined} />);

        expect(screen.queryByTestId('label')).not.toBeInTheDocument();
        expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    });

    it('does not render tooltip when not provided', () => {
        render(<WrapperComponent labelTooltip={undefined} />);

        expect(screen.getByTestId('label')).toHaveTextContent(mockLabel);
        expect(screen.queryByTestId('tooltip')).not.toBeInTheDocument();
    });

    it('does not render label and tooltip when not provided', () => {
        render(
            <WrapperComponent
                label={undefined}
                labelTooltip={undefined}
            />,
        );

        expect(screen.queryByTestId('label')).not.toBeInTheDocument();
        expect(screen.queryByTestId('tooltip')).not.toBeInTheDocument();
    });

    // delete button
    it('does not render delete button in read only mode', () => {
        render(<WrapperComponent readOnly />);

        expect(screen.queryByTestId('button-delete')).not.toBeInTheDocument();
    });

    it('renders disabled delete button for single select when no selected entries', () => {
        render(
            <WrapperComponent
                selectedEntries={[]}
                multiselect={false}
            />,
        );

        expect(screen.getByTestId('button-delete')).toBeDisabled();
    });

    it('renders enabled delete button for single select when selected entries', () => {
        render(
            <WrapperComponent
                selectedEntries={[{ id: 1000022, name: mockRegisterName }]}
                multiselect={false}
            />,
        );

        expect(screen.getByTestId('button-delete')).toBeEnabled();
    });

    it('calls onSelectedChange properly when clicking on delete button for single select', async () => {
        const { user } = render(
            <WrapperComponent
                selectedEntries={[{ id: 123, name: mockRegisterName }]}
                multiselect={false}
            />,
        );

        await user.click(screen.getByTestId('button-delete'));

        expect(mockOnSelectedChange).toHaveBeenCalledWith([]);
    });

    it('renders disabled delete button for multi select when no user selection', () => {
        render(<WrapperComponent />);

        expect(screen.getByTestId('button-delete')).toBeDisabled();
    });

    it('renders enabled delete button for multi select when single user selection', async () => {
        const { user } = render(<WrapperComponent />);

        await user.click(screen.getByRole('cell', { name: '1000022' }));
        expect(screen.getByTestId('button-delete')).toBeEnabled();
    });

    it('renders enabled delete button for multi select when multiple user selection', async () => {
        render(<WrapperComponent />);
        fireEvent.click(screen.getByRole('cell', { name: '1000023' }), { ctrlKey: true });
        fireEvent.click(screen.getByRole('cell', { name: '1000022' }), { ctrlKey: true });
        expect(screen.getByTestId('button-delete')).toBeEnabled();
    });

    it('renders no delete button for multi select when error occurred', async () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: false,
            isSuccess: false,
            isError: true,
            refetch: jest.fn(),
        });

        render(<WrapperComponent />);
        expect(screen.queryByTestId('button-delete')).not.toBeInTheDocument();
    });

    it('renders no delete button for single select when error occurred', async () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: false,
            isSuccess: false,
            isError: true,
            refetch: jest.fn(),
        });

        render(<WrapperComponent multiselect={false} />);
        expect(screen.queryByTestId('button-delete')).not.toBeInTheDocument();
    });

    // open button
    it('renders disabled open button for single select when no selected entries', () => {
        render(
            <WrapperComponent
                selectedEntries={[]}
                multiselect={false}
            />,
        );

        expect(screen.getByTestId('button-open')).toBeDisabled();
    });

    it('renders no open button for single select when error occurred', () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: false,
            isSuccess: false,
            isError: true,
            refetch: jest.fn(),
        });

        render(
            <WrapperComponent
                selectedEntries={[{ id: 1000022, name: mockRegisterName }]}
                multiselect={false}
            />,
        );

        expect(screen.queryByTestId('button-open')).not.toBeInTheDocument();
    });

    it('renders enabled open button for single select when selected entries', () => {
        render(
            <WrapperComponent
                selectedEntries={[{ id: 1000022, name: mockRegisterName }]}
                multiselect={false}
            />,
        );

        expect(screen.getByTestId('button-open')).toBeEnabled();
    });

    it('renders disabled open button for multi select when no user selection', () => {
        render(<WrapperComponent />);

        expect(screen.getByTestId('button-open')).toBeDisabled();
    });

    it('renders enabled open button for multi select when single user selection', () => {
        render(<WrapperComponent />);

        fireEvent.click(screen.getByRole('cell', { name: '1000022' }), { ctrlKey: true });

        expect(screen.getByTestId('button-open')).toBeEnabled();
    });

    it('renders disabled open button for multi select when multiple user selection', () => {
        render(<WrapperComponent />);

        fireEvent.click(screen.getByRole('cell', { name: '1000023' }), { ctrlKey: true });
        fireEvent.click(screen.getByRole('cell', { name: '1000022' }), { ctrlKey: true });

        expect(screen.getByTestId('button-open')).toBeDisabled();
    });

    it('renders no open button for multi select when error occurred', () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: false,
            isSuccess: false,
            isError: true,
            refetch: jest.fn(),
        });

        render(<WrapperComponent />);

        expect(screen.queryByTestId('button-open')).not.toBeInTheDocument();
    });

    // add button
    it('does not render add button in read only mode', () => {
        render(<WrapperComponent readOnly={true} />);

        expect(screen.queryByTestId('button-add')).not.toBeInTheDocument();
    });

    it('does not render add button when hideAddButton is true', () => {
        render(<WrapperComponent hideAddButton />);

        expect(screen.queryByTestId('button-add')).not.toBeInTheDocument();
    });

    it('does not render add button when register is private', () => {
        jest.spyOn(registerApi, 'useTmrsGetRegisterConfigUsingGet1Query').mockReturnValue({
            data: { ...mockRegister, registerType: RegisterType.PRIVATE },
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        render(<WrapperComponent hideAddButton />);

        expect(screen.queryByTestId('button-add')).not.toBeInTheDocument();
    });

    it('renders add button with correct text for single select', () => {
        render(<WrapperComponent multiselect={false} />);

        expect(screen.getByTestId('button-add')).toHaveTextContent('Select');
    });

    it('renders add button with correct text for multi select', () => {
        render(<WrapperComponent />);

        expect(screen.getByTestId('button-add')).toHaveTextContent('Add');
    });

    // new button
    it('does not render new button in read only mode', () => {
        render(<WrapperComponent readOnly />);

        expect(screen.queryByTestId('button-new')).not.toBeInTheDocument();
    });

    it('does not render new button when register is missing correct permissions', async () => {
        jest.spyOn(registerApi, 'useTmrsGetRegisterConfigUsingGet1Query').mockReturnValue({
            data: { ...mockRegister, userPermissionCodesForRegister: [PermissionCodes.REGISTER_DATA_EXPORT] },
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        render(<WrapperComponent />);
        expect(screen.queryByTestId('button-new')).not.toBeInTheDocument();
    });

    it('renders new button when correct permissions available', () => {
        render(<WrapperComponent />);

        expect(screen.getByTestId('button-new')).toBeInTheDocument();
    });

    // table
    it('renders loading when loading entries', () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: true,
            isSuccess: false,
            isError: false,
            refetch: jest.fn(),
        });

        render(<WrapperComponent />);

        expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('renders result when loaded', () => {
        render(<WrapperComponent />);
        expect(screen.getByText('Listing 1-1 items of 2 items in total')).toBeInTheDocument();
    });

    it('renders error message when error occurred', () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: undefined,
            isFetching: false,
            isSuccess: false,
            isError: true,
            refetch: jest.fn(),
        });

        render(<WrapperComponent />);
        expect(screen.getByText('An error occurred while retrieving register entries')).toBeInTheDocument();
    });

    // entries selector dialog
    it('shows selector dialog and passes correct props when clicking on add button for single select', async () => {
        const { user } = render(
            <WrapperComponent
                selectedEntries={[{ id: 123, name: mockRegisterName }]}
                multiselect={false}
            />,
        );
        await user.click(screen.getByTestId('button-add'));
        expect(screen.getByTestId('mock-selector')).toBeInTheDocument();
        expect(Selector).toHaveBeenCalledWith(
            expect.objectContaining({
                multiselect: false,
                filterData: { registerId: mockRegisterId, viewsActionsDisabled: false, context: 'table_1234' },
                visible: true,
                title: 'Select Register Entry',
                type: ColumnType.TABLE,
                selected: [{ id: 123, name: mockRegisterName }],
            }),
            {},
        );
    });

    it('shows selector dialog and passes correct props when clicking on add button for multi select', async () => {
        const { user } = render(
            <WrapperComponent
                selectedEntries={[
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ]}
            />,
        );
        await user.click(screen.getByTestId('button-add'));
        expect(screen.getByTestId('mock-selector')).toBeInTheDocument();
        expect(Selector).toHaveBeenCalledWith(
            expect.objectContaining({
                multiselect: true,
                filterData: { registerId: mockRegisterId, viewsActionsDisabled: false, context: 'table_1234' },
                visible: true,
                title: 'Select Register Entry',
                type: ColumnType.TABLE,
                selected: [
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ],
            }),
            {},
        );
    });

    it('shows selector dialog and passes correct props when clicking on add button for anonymous type', async () => {
        const { user } = render(
            <WrapperComponent
                selectedEntries={[
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ]}
                isAnonymous
            />,
        );
        await user.click(screen.getByTestId('button-add'));
        expect(screen.getByTestId('mock-selector')).toBeInTheDocument();
        expect(Selector).toHaveBeenCalledWith(
            expect.objectContaining({
                multiselect: true,
                filterData: { registerId: mockRegisterId, viewsActionsDisabled: true, context: 'table_1234' },
                visible: true,
                title: 'Select Register Entry',
                type: ColumnType.TABLE,
                selected: [
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ],
            }),
            {},
        );
    });

    it('shows selector dialog and passes undefined for onCreateNew prop when missing permissions', async () => {
        jest.spyOn(registerApi, 'useTmrsGetRegisterConfigUsingGet1Query').mockReturnValue({
            data: { ...mockRegister, userPermissionCodesForRegister: [PermissionCodes.REGISTER_DATA_EXPORT] },
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        const { user } = render(
            <WrapperComponent
                selectedEntries={[
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ]}
            />,
        );
        await user.click(screen.getByTestId('button-add'));
        expect(screen.getByTestId('mock-selector')).toBeInTheDocument();
        expect(Selector).toHaveBeenCalledWith(
            expect.objectContaining({
                onCreateNew: undefined,
            }),
            {},
        );
    });

    it('hides selector dialog properly when canceling', async () => {
        const { user } = render(
            <WrapperComponent
                selectedEntries={[
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ]}
            />,
        );
        await user.click(screen.getByTestId('button-add'));
        expect(screen.getByTestId('mock-selector')).toBeInTheDocument();
        await user.click(screen.getByTestId('mock-selector-cancel'));
        expect(screen.queryByTestId('mock-selector')).not.toBeInTheDocument();
    });

    it('hides selector and calls onSelectedChange when adding entries', async () => {
        const { user } = render(
            <WrapperComponent
                selectedEntries={[
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ]}
            />,
        );
        await user.click(screen.getByTestId('button-add'));
        expect(screen.getByTestId('mock-selector')).toBeInTheDocument();
        await user.click(screen.getByTestId('mock-selector-submit'));
        expect(screen.queryByTestId('mock-selector')).not.toBeInTheDocument();
        expect(mockOnSelectedChange).toHaveBeenCalledWith([
            {
                id: 987,
                name: 'Some new entry name',
            },
        ]);
    });

    // entry form
    it('shows register entry form and passes correct props when clicking on new button from selector dialog', async () => {
        const { user } = render(
            <WrapperComponent
                selectedEntries={[
                    { id: 123, name: mockRegisterName },
                    { id: 456, name: mockRegisterName },
                ]}
            />,
        );
        await user.click(screen.getByTestId('button-add'));
        expect(screen.getByTestId('mock-selector')).toBeInTheDocument();
        await user.click(screen.getByTestId('mock-selector-new'));
        expect(screen.queryByTestId('mock-selector')).not.toBeInTheDocument();
        expect(screen.getByTestId('mock-register-entry-form')).toBeInTheDocument();
        expect(RegisterEntry).toHaveBeenCalledWith(
            expect.objectContaining({
                tableName: 'table_1234',
                entryId: undefined,
                isCreate: true,
                isAnonymous: false,
                isSubEntry: true,
            }),
            {},
        );
    });

    it('shows register entry form and passes correct props when clicking on new button from toolbar button', async () => {
        const { user } = render(<WrapperComponent />);
        await user.click(screen.getByTestId('button-new'));
        expect(screen.getByTestId('mock-register-entry-form')).toBeInTheDocument();
        expect(RegisterEntry).toHaveBeenCalledWith(
            expect.objectContaining({
                tableName: 'table_1234',
                entryId: undefined,
                isCreate: true,
                isAnonymous: false,
                isSubEntry: true,
            }),
            {},
        );
    });

    it('shows register entry form and passes correct props when clicking on open button for selected entry for multi selection table', async () => {
        const { user } = render(<WrapperComponent />);
        await user.click(screen.getByRole('cell', { name: '1000022' }));
        await user.click(screen.getByTestId('button-open'));
        expect(screen.getByTestId('mock-register-entry-form')).toBeInTheDocument();
        expect(RegisterEntry).toHaveBeenCalledWith(
            expect.objectContaining({
                tableName: 'table_1234',
                entryId: '1000022',
                isCreate: false,
                isAnonymous: false,
                isSubEntry: true,
            }),
            {},
        );
    });

    it('shows register entry form and passes correct props when clicking on item and open button for single selection table', async () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: mockEntriesSingle,
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        const { user } = render(
            <WrapperComponent
                multiselect={false}
                selectedEntries={[{ id: 1000022, name: mockRegisterName }]}
            />,
        );
        await user.click(screen.getByRole('cell', { name: '1000022' }));
        await user.click(screen.getByTestId('button-open'));
        expect(screen.getByTestId('mock-register-entry-form')).toBeInTheDocument();
        expect(RegisterEntry).toHaveBeenCalledWith(
            expect.objectContaining({
                tableName: 'table_1234',
                entryId: '1000022',
                isCreate: false,
                isAnonymous: false,
                isSubEntry: true,
            }),
            {},
        );
    });

    it('shows register entry form and passes correct props when clicking directly on open button for single selection table', async () => {
        jest.spyOn(registerApi, 'useGetRegisterEntriesSearchPostQuery').mockReturnValue({
            data: mockEntriesSingle,
            isFetching: false,
            isSuccess: true,
            isError: false,
            refetch: jest.fn(),
        });

        const { user } = render(
            <WrapperComponent
                multiselect={false}
                selectedEntries={[{ id: 1000022, name: mockRegisterName }]}
            />,
        );
        await user.click(screen.getByTestId('button-open'));
        expect(screen.getByTestId('mock-register-entry-form')).toBeInTheDocument();
        expect(RegisterEntry).toHaveBeenCalledWith(
            expect.objectContaining({
                tableName: 'table_1234',
                entryId: '1000022',
                isCreate: false,
                isAnonymous: false,
                isSubEntry: true,
            }),
            {},
        );
    });

    it('hides register entry form when canceled', async () => {
        const { user } = render(<WrapperComponent />);
        await user.click(screen.getByTestId('button-new'));
        expect(screen.getByTestId('mock-register-entry-form')).toBeInTheDocument();
        await user.click(screen.getByTestId('mock-entry-cancel'));
        expect(screen.queryByTestId('mock-register-entry-form')).not.toBeInTheDocument();
    });

    it('hides register entry form and selects new item when saved', async () => {
        const { user } = render(<WrapperComponent />);
        await user.click(screen.getByTestId('button-new'));
        expect(screen.getByTestId('mock-register-entry-form')).toBeInTheDocument();
        await user.click(screen.getByTestId('mock-entry-save'));
        expect(screen.queryByTestId('mock-register-entry-form')).not.toBeInTheDocument();
        expect(mockOnSelectedChange).toHaveBeenLastCalledWith([
            {
                id: 876,
                name: 'Some new entry name',
            },
        ]);
    });
});
