import { calculateTableHeight } from './utils';

describe('calculateTableHeight', () => {
    it('returns correct height for single select', () => {
        expect(calculateTableHeight(22, false, 500)).toBe(46);
    });

    it('returns correct height for multi select when data height is not specified', () => {
        expect(calculateTableHeight(30, true)).toBe(152);
    });

    it('returns correct height for multi select when data height is lower than minimum', () => {
        expect(calculateTableHeight(40, true, 101)).toBe(202);
    });

    it('returns correct height for multi select when data height is higher than minimum', () => {
        expect(calculateTableHeight(25, true, 500)).toBe(552);
    });
});
