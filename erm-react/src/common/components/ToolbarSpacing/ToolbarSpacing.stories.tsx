import React from 'react';
import { <PERSON>Fn, Meta } from '@storybook/react';
import Search<PERSON>yField from '../SearchByField/SearchByField';
import ToolbarContainer from './ToolbarContainer';
import ToolbarGroup from './ToolbarGroup';
import ToolbarDivider, { ToolbarDividerType } from './ToolbarDivider';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

export default {
    title: 'components/UI/Toolbar Spacing',
    component: ToolbarContainer,
    parameters: {
        docs: {
            description: {
                component:
                    'Toolbar spacing components provide basic wrappers for creation of table toolbars in order to keep their structure and spacing consistent.' +
                    'Three components were created: ToolbarContainer, ToolbarGroup and ToolbarDivider and they are implemented using flex layout.' +
                    'ToolbarContainer keeps groups spaced with 20px, ToolbarGroups keep toolbar items spaced with 10px gap.' +
                    'Use flex layout styling in order to create suitable implementations for different situations.' +
                    'Check ToolbarWireFrame to see the groups visually separated. These toolbar spacing components are used to create table toolbars and page title toolbars.',
            },
        },
    },
} as Meta<typeof ToolbarContainer>;

const DefaultTemplate: StoryFn<typeof ToolbarContainer> = () => (
    <ToolbarContainer>
        <ToolbarGroup
            flex={1}
            justifyContent="space-between"
        >
            <SearchByField
                searchValue=""
                searchField="id"
                fields={[
                    {
                        value: 'id',
                        label: 'id',
                    },
                    {
                        value: 'name',
                        label: 'name',
                    },
                ]}
                onPropertyChanged={(property) => {
                    // eslint-disable-next-line no-console
                    console.log('property changed', property);
                }}
                onValueChanged={(value) => {
                    // eslint-disable-next-line no-console
                    console.log('value changed', value);
                }}
            ></SearchByField>
            <ToolbarGroup>
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'secondary'}
                >
                    Action 1
                </Button>
                <ToolbarDivider type={ToolbarDividerType.DIVIDER} />
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'primary'}
                >
                    Action 1
                </Button>
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'primary'}
                >
                    Action 2
                </Button>
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'primary'}
                >
                    Action 3
                </Button>
            </ToolbarGroup>
        </ToolbarGroup>
    </ToolbarContainer>
);
export const Default: StoryFn<typeof ToolbarContainer> = DefaultTemplate.bind({});
Default.args = {};

const MockedToolbarItem = styled(Box)({
    border: '1px solid green',
    padding: '5px',
});

const WireFrameTemplate: StoryFn<typeof ToolbarContainer> = () => (
    <ToolbarContainer sx={{ border: '1px solid red', padding: '5px', justifyContent: 'space-between' }}>
        <ToolbarGroup sx={{ border: '1px solid blue', padding: '5px' }}>
            <MockedToolbarItem>Item 1</MockedToolbarItem>
            <MockedToolbarItem>Item 2</MockedToolbarItem>
            <MockedToolbarItem>Item 3</MockedToolbarItem>
            <MockedToolbarItem>Item 4</MockedToolbarItem>
            <ToolbarDivider type={ToolbarDividerType.DIVIDER} />
            <MockedToolbarItem>Item 5</MockedToolbarItem>
            <MockedToolbarItem>Item 6</MockedToolbarItem>
        </ToolbarGroup>
        <ToolbarGroup sx={{ border: '1px solid blue', padding: '5px' }}>
            <MockedToolbarItem>Item 7</MockedToolbarItem>
            <MockedToolbarItem>Item 8</MockedToolbarItem>
            <MockedToolbarItem>Item 9</MockedToolbarItem>
        </ToolbarGroup>
    </ToolbarContainer>
);

export const ToolbarWireFrame: StoryFn<typeof ToolbarContainer> = WireFrameTemplate.bind({});
ToolbarWireFrame.args = {};

const tableTitle = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.';
const WitTitleAndSearchBarTemplate: StoryFn<typeof ToolbarContainer> = () => (
    <ToolbarContainer>
        <ToolbarGroup
            flex={1}
            justifyContent="space-between"
        >
            <ToolbarGroup sx={{ flex: '1', minWidth: '48px', width: '48px' }}>
                <Typography
                    noWrap
                    variant="body2"
                >
                    <Tooltip
                        key={'lightTooltip-tableName'}
                        leaveTouchDelay={1000}
                        title={tableTitle}
                        aria-label={tableTitle}
                    >
                        <Box component={'span'}>{tableTitle}</Box>
                    </Tooltip>
                </Typography>
            </ToolbarGroup>
            <ToolbarGroup>
                <SearchByField
                    searchValue=""
                    searchField="id"
                    fields={[
                        {
                            value: 'id',
                            label: 'id',
                        },
                        {
                            value: 'name',
                            label: 'name',
                        },
                    ]}
                    onPropertyChanged={(property) => {
                        // eslint-disable-next-line no-console
                        console.log('property changed', property);
                    }}
                    onValueChanged={(value) => {
                        // eslint-disable-next-line no-console
                        console.log('value changed', value);
                    }}
                ></SearchByField>
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'secondary'}
                >
                    Action 1
                </Button>
                <ToolbarDivider type={ToolbarDividerType.DIVIDER} />
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'primary'}
                >
                    Action 1
                </Button>
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'primary'}
                >
                    Action 2
                </Button>
                <Button
                    {...ButtonStyles.tableToolbarButton}
                    variant={'primary'}
                >
                    Action 3
                </Button>
            </ToolbarGroup>
        </ToolbarGroup>
    </ToolbarContainer>
);
export const WitTitleAndSearchBar: StoryFn<typeof ToolbarContainer> = WitTitleAndSearchBarTemplate.bind({});
WitTitleAndSearchBar.args = {};

const PageToolbarTemplate: StoryFn<typeof ToolbarContainer> = () => (
    <ToolbarContainer
        disableGutters={false}
        variant="regular"
    >
        <ToolbarGroup
            flex={1}
            justifyContent="space-between"
        >
            <Typography
                variant="h1"
                noWrap
            >
                Mocked Title
            </Typography>
            <Button
                {...ButtonStyles.tableToolbarButton}
                key="risk-cause-create-action"
                aria-label="risk-cause-create-action"
                startIcon={<Add />}
            >
                Add new
            </Button>
        </ToolbarGroup>
    </ToolbarContainer>
);
export const PageToolbar: StoryFn<typeof ToolbarContainer> = PageToolbarTemplate.bind({});
PageToolbar.args = {};
