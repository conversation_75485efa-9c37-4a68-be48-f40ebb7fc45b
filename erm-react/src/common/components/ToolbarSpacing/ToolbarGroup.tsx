import Box from '@mui/material/Box';
import { BoxProps } from '@mui/material/Box';
import React from 'react';

export type ToolbarGroupProps = BoxProps;

const TOOLBAR_ITEM_SPACING = '10px';

const ToolbarGroup = ({ children, ...props }: ToolbarGroupProps) => {
    return (
        <Box
            display="flex"
            gap={TOOLBAR_ITEM_SPACING}
            alignItems="center"
            flexWrap="nowrap"
            overflow="hidden"
            {...props}
        >
            {children}
        </Box>
    );
};
export default ToolbarGroup;
