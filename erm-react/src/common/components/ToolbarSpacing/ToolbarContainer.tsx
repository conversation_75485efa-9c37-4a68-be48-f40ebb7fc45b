import { ToolbarProps } from '@mui/material/Toolbar';
import React from 'react';
import StyledToolbar from 'ui/components/Toolbar';

const TOOLBAR_CONTAINER_SPACING = '20px';

const ToolbarContainer = ({ disableGutters = true, variant = 'dense', children, sx, ...props }: ToolbarProps) => {
    return (
        <StyledToolbar
            sx={{
                gap: TOOLBAR_CONTAINER_SPACING,
                ...sx,
            }}
            disableGutters={disableGutters}
            variant={variant}
            {...props}
        >
            {children}
        </StyledToolbar>
    );
};
export default ToolbarContainer;
