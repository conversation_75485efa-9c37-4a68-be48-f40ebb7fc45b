import Box from '@mui/material/Box';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import React, { ReactNode } from 'react';

export enum ToolbarDividerType {
    DIVIDER = 'DIVIDER',
    CUSTOM = 'CUSTOM',
}

type Props = {
    type: ToolbarDividerType;
    children?: ReactNode;
};

const TableToolbarDivider = ({ type, children }: Props) => {
    return (
        <Box>
            {type === ToolbarDividerType.DIVIDER ? (
                <StyledDivider
                    orientation="vertical"
                    margin={0}
                    sx={{ height: '20px' }}
                />
            ) : (
                children
            )}
        </Box>
    );
};

export default TableToolbarDivider;
