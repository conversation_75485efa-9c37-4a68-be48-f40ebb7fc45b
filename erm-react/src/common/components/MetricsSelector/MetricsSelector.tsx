import React, { useCallback, useMemo, useState } from 'react';
import useMetricsLoader from 'metrics/hooks/useMetricsLoader';
import useSearchExpression from 'common/hooks/useSearchExpression';
import { MetricRest } from 'metrics/types';
import { ViewExpressionRest } from 'common/../api/generated/types';
import { BasicRequestParams, IdWithName } from '@protecht/ui-library/library/types';
import { SortType } from 'ui/types';
import { useTheme } from '@mui/material/styles';
import { MetricsColDef } from 'metrics/colDef';
import { getSearchFields } from 'common/utils/definitions';
import ToolbarContainer from '../ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from '../ToolbarSpacing/ToolbarGroup';
import SearchByField from '../SearchByField';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { strings } from 'common/utils/i18n';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import DialogContentLayout from 'common/layouts/DialogContentLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import { Table } from '@protecht/ui-library/library/components/Table';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

type Props = {
    selected?: IdWithName[];
    onClose: () => void;
    onSelect: (selected: IdWithName[]) => void;
    expressions?: ViewExpressionRest[];
    selectionLimit?: number;
};

const MetricSelector: React.FC<Props> = ({ selected = [], onClose, onSelect, expressions, selectionLimit }) => {
    const [requestParams, setRequestParams] = useState<BasicRequestParams>({ orderBy: 'name', orderType: SortType.ASC });
    const [searchField, setSearchField] = useState<string>();
    const [searchValue, setSearchValue] = useState('');
    const searchExpression = useSearchExpression(searchValue, searchField);
    const [currentSelection, setCurrentSelection] = useState<IdWithName[]>(selected);

    const { loadedItems, totalCount, isLoading } = useMetricsLoader(requestParams, searchExpression, expressions);

    const confirmSelection = useCallback(() => {
        onSelect(currentSelection);
        onClose();
    }, [currentSelection, onSelect, onClose]);

    const theme = useTheme();
    const columns = useMemo(() => {
        return MetricsColDef.filter((col) => col.field === 'name' || col.field === 'func');
    }, []);

    const searchFields = getSearchFields(columns);

    const toolbar = useMemo(() => {
        return (
            <ToolbarContainer>
                <ToolbarGroup flex={1}>
                    <SearchByField
                        aria-label="search-by"
                        fields={searchFields}
                        searchField={searchField ?? 'id'}
                        searchValue={searchValue}
                        dataTestId="input-field-Search"
                        onValueChanged={(value: string) => {
                            setSearchValue(value);
                        }}
                        onPropertyChanged={(property?: string) => {
                            setSearchField(property);
                        }}
                    />
                </ToolbarGroup>
            </ToolbarContainer>
        );
    }, [searchFields, searchValue, searchField]);

    return (
        <Dialog
            visible={true}
            title={strings('metrics:title.selectMetrics')}
            width={720}
            height={613}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        disabled={!currentSelection}
                        dataTestId="button-confirm"
                        onClick={confirmSelection}
                    >
                        {strings('ermConstants:button_label_select')}
                    </Button>
                </DialogActions>
            }
        >
            <DialogContentLayout>
                <ContentLayout toolbar={toolbar}>
                    <Table
                        columns={columns}
                        rows={loadedItems ?? []}
                        totalCount={totalCount ?? 0}
                        loading={isLoading}
                        loadingMessage={strings('common:message.loading')}
                        params={requestParams}
                        onParamsChanged={(params: BasicRequestParams) => {
                            setRequestParams(params);
                        }}
                        selected={currentSelection}
                        onSelect={(selection: MetricRest[]) => {
                            setCurrentSelection(selection);
                        }}
                        selectionLimit={selectionLimit}
                    />

                    <Box sx={{ border: '1px solid ' + theme.palette.protechtGrey?.grey_231, padding: 1, marginTop: 3 }}>
                        <Typography>
                            {strings('metrics:message.metricsSelectorInfoMessage', {
                                currentSelection: currentSelection.length,
                                selectionLimit: selectionLimit,
                            })}
                        </Typography>
                    </Box>
                </ContentLayout>
            </DialogContentLayout>
        </Dialog>
    );
};

export default MetricSelector;
