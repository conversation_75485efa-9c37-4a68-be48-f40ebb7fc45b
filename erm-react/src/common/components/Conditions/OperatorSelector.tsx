import { strings } from 'common/utils/i18n';
import { ConditionOperator } from 'metrics/types';
import React, { useEffect, useMemo } from 'react';
import { FilterType } from 'view/types';
import { useVrsGetExpressionContextUsingGetQuery } from 'view/rtkApi';
import { useFormContext } from 'react-hook-form';
import { SelectField } from '@protecht/ui-library/library/components/FormFields';

type Props = {
    name: string;
    selectedRegisterFieldFilterType?: FilterType;
    onChange?: (operator: string) => void;
    emptyLabelSpacing?: boolean;
    disabled?: boolean;
    preselectValue?: boolean;
    dataTestId?: string;
    hideFieldLabel?: boolean;
};

const transformOperatorLabel = (type: FilterType, operator: ConditionOperator): string => {
    if (type === FilterType.DATE) {
        switch (operator) {
            case ConditionOperator.EQUAL:
                return strings('metrics:operatorLabels.on');
            case ConditionOperator.LESS_THAN:
                return strings('metrics:operatorLabels.before');
            case ConditionOperator.GREATER_THAN:
                return strings('metrics:operatorLabels.after');
            case ConditionOperator.LAST:
                return strings('metrics:operatorLabels.last');
            case ConditionOperator.CURRENT:
                return strings('metrics:operatorLabels.current');
            case ConditionOperator.NEXT:
                return strings('metrics:operatorLabels.next');
            default:
                return operator;
        }
    } else {
        switch (operator) {
            case ConditionOperator.EQUAL:
                return strings('metrics:operatorLabels.equals');
            case ConditionOperator.NOT_EQUAL:
                return strings('metrics:operatorLabels.notEqual');
            default:
                return operator;
        }
    }
};

const OperatorSelector: React.FC<Props> = ({
    selectedRegisterFieldFilterType,
    name,
    onChange,
    dataTestId,
    emptyLabelSpacing = false,
    disabled = false,
    preselectValue = false,
    hideFieldLabel,
}) => {
    const { data: allOperators, isSuccess } = useVrsGetExpressionContextUsingGetQuery();
    const { setValue, getValues } = useFormContext();
    const operatorValue = getValues(name);

    const filteredOperatorsByType = useMemo(() => {
        if (isSuccess && allOperators && selectedRegisterFieldFilterType) {
            return (allOperators[selectedRegisterFieldFilterType] ?? []).map((operator) => ({
                value: operator,
                label: transformOperatorLabel(selectedRegisterFieldFilterType, operator as ConditionOperator),
            }));
        } else {
            return [];
        }
    }, [selectedRegisterFieldFilterType, allOperators, isSuccess]);

    useEffect(() => {
        // Preselect the first option if no value is set
        if (preselectValue && !operatorValue && filteredOperatorsByType.length > 0) {
            const selectedValue = filteredOperatorsByType[0].value;
            setValue(name, selectedValue, { shouldDirty: true, shouldValidate: true });
            onChange && onChange(selectedValue);
        }
    }, [selectedRegisterFieldFilterType, filteredOperatorsByType, setValue, name, preselectValue, operatorValue, onChange]);

    return (
        <SelectField
            name={name}
            placeholder={strings('metrics:label.operator')}
            variant="outlined"
            displayEmpty
            emptyLabelSpacing={emptyLabelSpacing}
            options={filteredOperatorsByType}
            disabled={disabled}
            onChange={onChange}
            dataTestId={dataTestId}
            hideFieldLabel={hideFieldLabel}
        />
    );
};

export default OperatorSelector;
