import useMutationObservable from 'common/hooks/useMutationObservable';
import React, { ReactNode, useCallback, useRef, useState } from 'react';
import Loading from '../Loading';
import Box from '@mui/material/Box';

type Props = {
    children: ReactNode;
    loaderStyling?: React.CSSProperties;
};

const observerConfig = {
    childList: true,
};

const LoadingOnRender = ({ children, loaderStyling }: Props): JSX.Element | null => {
    const ref = useRef<HTMLDivElement>(null);
    const [isRenderFinished, setRenderFinished] = useState<boolean>(false);

    const onParentMutation = useCallback((mutationList: MutationRecord[]) => {
        const renderMutationObserved = mutationList.some((mutation) => mutation.type === 'childList');
        setRenderFinished(renderMutationObserved);
    }, []);

    useMutationObservable(ref.current, onParentMutation, observerConfig);

    return (
        <>
            {isRenderFinished ? null : (
                <Box sx={{ position: 'relative', ...loaderStyling }}>
                    <Loading />
                </Box>
            )}
            <div ref={ref}>{children}</div>
        </>
    );
};

export default LoadingOnRender;
