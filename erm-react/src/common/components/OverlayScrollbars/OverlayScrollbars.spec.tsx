import React from 'react';
import { render, screen } from 'test/utils';
import OverlayScrollbars from './OverlayScrollbars';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';

jest.mock('overlayscrollbars-react', () => ({
    OverlayScrollbarsComponent: jest.fn(({ children }) => <div data-testid="overlay-scrollbars">{children}</div>),
}));

describe('OverlayScrollbars', () => {
    it('renders correctly with children', () => {
        render(
            <OverlayScrollbars>
                <div>Test Content</div>
            </OverlayScrollbars>,
        );

        expect(screen.getByTestId('overlay-scrollbars')).toBeInTheDocument();
        expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('applies options correctly', () => {
        render(<OverlayScrollbars options={{ scrollbars: { autoHide: 'leave' } }} />);
        expect(OverlayScrollbarsComponent).toHaveBeenCalledWith(
            expect.objectContaining({
                options: expect.objectContaining({
                    scrollbars: expect.objectContaining({
                        autoHide: 'leave',
                        clickScroll: true,
                    }),
                }),
            }),
            {},
        );
    });
});
