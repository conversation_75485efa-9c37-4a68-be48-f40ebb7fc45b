import React from 'react';
import { OverlayScrollbarsComponent, OverlayScrollbarsComponentRef, OverlayScrollbarsComponentProps } from 'overlayscrollbars-react';

export type OverlayScrollbarsProps = Omit<OverlayScrollbarsComponentProps, 'defer'>;
export type OverlayScrollbarsRef = OverlayScrollbarsComponentRef;

const OverlayScrollbars = React.forwardRef<OverlayScrollbarsComponentRef, OverlayScrollbarsProps>((props, ref) => {
    const { children, options, ...other } = props;

    return (
        <OverlayScrollbarsComponent
            ref={ref}
            {...other}
            defer
            options={{
                ...options,
                scrollbars: {
                    ...(options && options.scrollbars ? options.scrollbars : {}),
                    clickScroll: true,
                },
            }}
        >
            {children}
        </OverlayScrollbarsComponent>
    );
});

OverlayScrollbars.displayName = 'OverlayScrollbars';

export default OverlayScrollbars;
