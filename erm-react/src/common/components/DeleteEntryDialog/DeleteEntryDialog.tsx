import DialogActions from '@mui/material/DialogActions';
import { AlertType } from 'common/types';
import { strings } from 'common/utils/i18n';
import React from 'react';
import AlertDialog from 'ui/components/AlertDialog';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';

type Props = {
    visible: boolean;
    question: string;
    onConfirm: () => void;
    onClose: () => void;
};

const DeleteEntryDialog = ({ visible, question, onConfirm, onClose }: Props) => {
    const options = {
        type: AlertType.Warning,
        title: strings('common:title.deleteEntry'),
        contentText: question,
        onClose,
        actions: (
            <DialogActions>
                <Button
                    {...ButtonStyles.dialogButton}
                    variant={'secondary'}
                    onClick={onClose}
                    dataTestId="button-cancel"
                >
                    {strings('ermConstants:no')}
                </Button>
                <Button
                    {...ButtonStyles.dialogButton}
                    onClick={onConfirm}
                    dataTestId="button-confirm"
                >
                    {strings('ermConstants:yes')}
                </Button>
            </DialogActions>
        ),
    };
    return (
        <AlertDialog
            visible={visible}
            data={options}
        />
    );
};

export default DeleteEntryDialog;
