import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import Loading, { LoaderType } from './Loading';

export default {
    title: 'components/UI/Loaders',
    component: Loading,
    decorators: [],
} as Meta<typeof Loading>;

const Template: StoryFn<typeof Loading> = (args) => <Loading {...args} />;

export const Default: StoryFn<typeof Loading> = Template.bind({});
Default.args = {};

export const DefaultInline: StoryFn<typeof Loading> = Template.bind({});
DefaultInline.args = {
    inline: true,
};

export const Slim: StoryFn<typeof Loading> = Template.bind({});
Slim.args = {
    loaderType: LoaderType.Slim,
};
