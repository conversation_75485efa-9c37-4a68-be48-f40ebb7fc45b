// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Loading /> was rendered 1`] = `
<div>
  <div
    class="css-1ek9nky"
  >
    <div
      class="css-1yihh2g"
    >
      <div
        class="icon"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-spinner icon loadingSpinner"
          data-icon="spinner"
          data-prefix="fad"
          focusable="false"
          role="img"
          viewBox="0 0 512 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            class="fa-duotone-group"
          >
            <path
              class="fa-secondary"
              d="M60.9 403.1a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM208 464a48 48 0 1 0 96 0 48 48 0 1 0 -96 0zM369.1 75A48 48 0 1 0 437 142.9 48 48 0 1 0 369.1 75zm0 294.2A48 48 0 1 0 437 437a48 48 0 1 0 -67.9-67.9zM416 256a48 48 0 1 0 96 0 48 48 0 1 0 -96 0z"
              fill="currentColor"
            />
            <path
              class="fa-primary"
              d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM96 256A48 48 0 1 0 0 256a48 48 0 1 0 96 0zM75 142.9A48 48 0 1 0 142.9 75 48 48 0 1 0 75 142.9z"
              fill="currentColor"
            />
          </g>
        </svg>
      </div>
      <p
        class="MuiTypography-root MuiTypography-body1 css-1tnh15z-MuiTypography-root"
      >
        Is loading
      </p>
    </div>
  </div>
</div>
`;
