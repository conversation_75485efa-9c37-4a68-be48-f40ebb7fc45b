import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/pro-duotone-svg-icons';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { strings } from 'common/utils/i18n';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { SxProps } from '@mui/system/styleFunctionSx';

export enum LoaderType {
    Default,
    Slim,
}

type Props = {
    message?: string | null;
    dataTestId?: string | null;
    inline?: boolean;
    loaderType?: LoaderType;
    sx?: SxProps;
};

const StyledLoadingContainer = styled('div', {
    shouldForwardProp: (prop) => prop !== 'inline' && prop !== 'loaderType',
})<{ inline: boolean; loaderType: LoaderType }>(({ inline, loaderType }) => ({
    zIndex: 2,
    position: 'relative',
    width: inline ? 'auto' : '100%',
    height: inline ? 'auto' : '100%',
    ...(!inline && {
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
    }),
    minHeight: '31px',
    '.icon': {
        color: 'white',
        fontSize: '14px',
        marginRight: '10px',
        ...(loaderType === LoaderType.Slim && {
            fontSize: '14px',
            marginRight: '5px',
        }),
        '.loadingSpinner': {
            fontSize: '20px',
            animationName: 'spin',
            animationDuration: '2000ms',
            animationIterationCount: 'infinite',
            animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
            ...(loaderType === LoaderType.Slim && {
                fontSize: '14px',
            }),
        },
    },
    '@keyframes spin': {
        from: {
            transform: 'rotate(0deg)',
        },
        to: {
            transform: 'rotate(360deg)',
        },
    },
}));

const StyledLoadingBox = styled('div', {
    shouldForwardProp: (prop) => prop !== 'loaderType',
})<{ loaderType: LoaderType }>(({ loaderType }) => ({
    position: 'absolute',
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%)',
    borderRadius: '3px',
    padding: loaderType === LoaderType.Default ? '32px' : '5px 32px',
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    backgroundColor: '#262626',
    boxShadow: '0 4px 8px 0 #00000040',
}));

const Loading: React.FC<Props> = ({ message = strings('common:message.loading'), dataTestId, inline = false, loaderType = LoaderType.Default, sx }: Props) => (
    <StyledLoadingContainer
        inline={inline}
        data-testid={dataTestId}
        loaderType={loaderType}
        sx={sx}
    >
        <StyledLoadingBox loaderType={loaderType}>
            <div className="icon">
                <FontAwesomeIcon
                    className="icon loadingSpinner"
                    icon={faSpinner as IconProp}
                />
            </div>
            <Typography
                variant="body1"
                color="primary.contrastText"
            >
                {message}
            </Typography>
        </StyledLoadingBox>
    </StyledLoadingContainer>
);

export default Loading;
