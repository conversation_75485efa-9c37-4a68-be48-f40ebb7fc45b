import React from 'react';
import { render, screen } from 'test/utils';

import Loading from 'common/components/Loading';

describe('<Loading />', () => {
    const setup = () => {
        return render(<Loading message={'Is loading'} />);
    };

    it('was rendered', () => {
        const view = setup();
        expect(screen.getByText('Is loading')).toBeInTheDocument();
        expect(view.container).toMatchSnapshot();
    });
});
