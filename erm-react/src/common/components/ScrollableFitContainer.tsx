import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import { defaultStyledOptions } from '@protecht/ui-library/library/utils/defaultStyledOptions';

export const ScrollableFitContainer = styled(
    Box,
    defaultStyledOptions,
)<{ $barHeights: number }>(({ theme, $barHeights }) => ({
    display: 'flex',
    flexGrow: '1',
    overflowY: 'auto',
    flexDirection: 'column',
    height: `calc(100vh - ${$barHeights}px)`,
    ...theme.mixins?.scrollbar?.(true),
}));
