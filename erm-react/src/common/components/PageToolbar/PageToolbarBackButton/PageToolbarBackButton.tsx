import React from 'react';
import { useTheme } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';

export type PageToolbarBackButtonProps = {
    onBack: () => void;
};

const PageToolbarBackButton: React.FC<PageToolbarBackButtonProps> = ({ onBack }) => {
    const theme = useTheme();

    return (
        <IconButton
            color="secondary"
            size="large"
            onClick={onBack}
            data-testid="pageToolbar-backButton"
            sx={{ marginRight: '6px', marginLeft: '-10px', flex: '0 0 auto' }}
        >
            <FontAwesomeIcon
                icon={faArrowLeft}
                color={theme.palette.primary.main}
            />
        </IconButton>
    );
};

export default PageToolbarBackButton;
