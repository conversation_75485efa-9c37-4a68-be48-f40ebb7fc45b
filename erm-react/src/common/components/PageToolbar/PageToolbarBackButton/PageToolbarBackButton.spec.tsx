import React from 'react';
import { render, screen } from 'test/utils';
import PageToolbarBackButton from './PageToolbarBackButton';

describe('<PageToolbarBackButton />', () => {
    it('renders with default props', () => {
        const mockOnBack = jest.fn();
        const { container } = render(<PageToolbarBackButton onBack={mockOnBack} />);

        expect(screen.getByTestId('pageToolbar-backButton')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('handles click events', () => {
        const mockOnBack = jest.fn();
        render(<PageToolbarBackButton onBack={mockOnBack} />);

        const button = screen.getByTestId('pageToolbar-backButton');
        button.click();

        expect(mockOnBack).toHaveBeenCalledTimes(1);
    });

    it('calls onBack when clicked multiple times', () => {
        const mockOnBack = jest.fn();
        render(<PageToolbarBackButton onBack={mockOnBack} />);

        const button = screen.getByTestId('pageToolbar-backButton');
        button.click();
        button.click();
        button.click();

        expect(mockOnBack).toHaveBeenCalledTimes(3);
    });

    it('renders the back arrow icon', () => {
        const mockOnBack = jest.fn();
        render(<PageToolbarBackButton onBack={mockOnBack} />);

        const button = screen.getByTestId('pageToolbar-backButton');
        expect(button).toBeInTheDocument();

        // Check that the FontAwesome icon is rendered
        const icon = button.querySelector('svg');
        expect(icon).toBeInTheDocument();
    });

    it('has proper accessibility attributes', () => {
        const mockOnBack = jest.fn();
        render(<PageToolbarBackButton onBack={mockOnBack} />);

        const button = screen.getByTestId('pageToolbar-backButton');
        expect(button).toHaveAttribute('type', 'button');
        expect(button).toBeEnabled();
    });

    it('is keyboard accessible', () => {
        const mockOnBack = jest.fn();
        render(<PageToolbarBackButton onBack={mockOnBack} />);

        const button = screen.getByTestId('pageToolbar-backButton');
        expect(button).toHaveAttribute('tabindex', '0');
    });

    it('has correct button styling', () => {
        const mockOnBack = jest.fn();
        const { container } = render(<PageToolbarBackButton onBack={mockOnBack} />);

        const button = screen.getByTestId('pageToolbar-backButton');
        expect(button).toBeInTheDocument();

        // Test that it renders with snapshot for visual regression
        expect(container.firstChild).toMatchSnapshot();
    });
});
