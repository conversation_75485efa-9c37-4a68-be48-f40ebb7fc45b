import React from 'react';
import { render, screen } from 'test/utils';
import PageToolbar from './PageToolbar';
import BreadcrumbNavigator from 'common/components/BreadcrumbNavigator';

// Mock BreadcrumbNavigator component
jest.mock('common/components/BreadcrumbNavigator', () => {
    return {
        __esModule: true,
        default: jest.fn(({ navigationItems }) => (
            <div data-testid="breadcrumb-navigator">
                {navigationItems?.map((item: any, index: number) => (
                    <span
                        key={index}
                        data-testid={`breadcrumb-${index}`}
                    >
                        {item.label}
                    </span>
                ))}
            </div>
        )),
    };
});

const mockBreadcrumbs = [
    { label: 'Home', pathname: '/' },
    { label: 'Section', pathname: '/section' },
    { label: 'Current Page', pathname: '/current' },
];

describe('<PageToolbar />', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    it('renders with default props', () => {
        const { container } = render(<PageToolbar />);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders children correctly', () => {
        render(
            <PageToolbar>
                <div data-testid="toolbar-content">Test Content</div>
            </PageToolbar>,
        );

        expect(screen.getByTestId('toolbar-content')).toBeInTheDocument();
        expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('renders back button when onBack is provided', () => {
        const mockOnBack = jest.fn();
        render(<PageToolbar onBack={mockOnBack} />);

        expect(screen.getByTestId('pageToolbar-backButton')).toBeInTheDocument();
    });

    it('does not render back button when onBack is not provided', () => {
        render(<PageToolbar />);

        expect(screen.queryByTestId('pageToolbar-backButton')).not.toBeInTheDocument();
    });

    it('calls onBack when back button is clicked', () => {
        const mockOnBack = jest.fn();
        render(<PageToolbar onBack={mockOnBack} />);

        const backButton = screen.getByTestId('pageToolbar-backButton');
        backButton.click();

        expect(mockOnBack).toHaveBeenCalledTimes(1);
    });

    it('renders breadcrumbs when provided', () => {
        render(<PageToolbar breadcrumbs={mockBreadcrumbs} />);

        expect(BreadcrumbNavigator).toHaveBeenCalledWith({ navigationItems: mockBreadcrumbs }, {});
        expect(screen.getByTestId('breadcrumb-navigator')).toBeInTheDocument();
        expect(screen.getByText('Home')).toBeInTheDocument();
        expect(screen.getByText('Section')).toBeInTheDocument();
        expect(screen.getByText('Current Page')).toBeInTheDocument();
    });

    it('does not render breadcrumbs when not provided', () => {
        render(<PageToolbar />);

        expect(BreadcrumbNavigator).not.toHaveBeenCalled();
        expect(screen.queryByTestId('breadcrumb-navigator')).not.toBeInTheDocument();
    });

    it('renders with border when withBorder is true', () => {
        const { container } = render(<PageToolbar withBorder={true} />);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders without border when withBorder is false', () => {
        const { container } = render(<PageToolbar withBorder={false} />);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with all features combined', () => {
        const mockOnBack = jest.fn();
        const { container } = render(
            <PageToolbar
                onBack={mockOnBack}
                breadcrumbs={mockBreadcrumbs}
                withBorder={true}
            >
                <div data-testid="combined-content">Combined Content</div>
            </PageToolbar>,
        );

        // Check all features are present
        expect(screen.getByTestId('pageToolbar-backButton')).toBeInTheDocument();
        expect(BreadcrumbNavigator).toHaveBeenCalledWith({ navigationItems: mockBreadcrumbs }, {});
        expect(screen.getByTestId('breadcrumb-navigator')).toBeInTheDocument();
        expect(screen.getByText('Home')).toBeInTheDocument();
        expect(screen.getByTestId('combined-content')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('passes through additional Box props', () => {
        const { container } = render(
            <PageToolbar
                data-testid="custom-toolbar"
                className="custom-class"
            >
                <div>Content</div>
            </PageToolbar>,
        );

        expect(screen.getByTestId('custom-toolbar')).toBeInTheDocument();
        expect(container.firstChild).toHaveClass('custom-class');
    });

    it('handles multiple children', () => {
        render(
            <PageToolbar>
                <div data-testid="child-1">Child 1</div>
                <div data-testid="child-2">Child 2</div>
                <div data-testid="child-3">Child 3</div>
            </PageToolbar>,
        );

        expect(screen.getByTestId('child-1')).toBeInTheDocument();
        expect(screen.getByTestId('child-2')).toBeInTheDocument();
        expect(screen.getByTestId('child-3')).toBeInTheDocument();
    });

    it('handles complex toolbar content', () => {
        const mockOnBack = jest.fn();
        render(
            <PageToolbar
                onBack={mockOnBack}
                breadcrumbs={mockBreadcrumbs}
            >
                <h1>Page Title</h1>
                <div>
                    <button>Action 1</button>
                    <button>Action 2</button>
                </div>
            </PageToolbar>,
        );

        expect(screen.getByText('Page Title')).toBeInTheDocument();
        expect(screen.getByText('Action 1')).toBeInTheDocument();
        expect(screen.getByText('Action 2')).toBeInTheDocument();
        expect(screen.getByTestId('pageToolbar-backButton')).toBeInTheDocument();
        expect(BreadcrumbNavigator).toHaveBeenCalledWith({ navigationItems: mockBreadcrumbs }, {});
        expect(screen.getByText('Home')).toBeInTheDocument();
    });

    it('renders empty toolbar correctly', () => {
        const { container } = render(<PageToolbar />);

        // Should render the basic structure even when empty
        expect(container.firstChild).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });
});
