// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PageToolbarTitle renders with additional props 1`] = `
<h1
  class="MuiTypography-root MuiTypography-h1 MuiTypography-alignCenter MuiTypography-noWrap custom-class css-1b87nt0-MuiTypography-root"
  data-testid="pageToolbar-title"
>
  Styled Title
</h1>
`;

exports[`PageToolbarTitle renders with component override 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-2bl176-MuiTypography-root"
  data-testid="pageToolbar-title"
>
  Div Title
</div>
`;

exports[`PageToolbarTitle renders with default props 1`] = `
<h1
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-2bl176-MuiTypography-root"
  data-testid="pageToolbar-title"
>
  Test Title
</h1>
`;

exports[`PageToolbarTitle renders with noWrap disabled 1`] = `
<h1
  class="MuiTypography-root MuiTypography-h1 css-1x5g5mi-MuiTypography-root"
  data-testid="pageToolbar-title"
>
  No Wrap Title
</h1>
`;
