import React from 'react';
import Typography, { TypographyProps } from '@mui/material/Typography';

export type PageToolbarTitleProps = Omit<TypographyProps, 'variant'>;

const PageToolbarTitle: React.FC<PageToolbarTitleProps> = (props) => {
    return (
        <Typography
            variant="h1"
            noWrap
            data-testid="pageToolbar-title"
            {...props}
        />
    );
};

export default PageToolbarTitle;
