import React from 'react';
import { render, screen } from 'test/utils';
import PageToolbarTitle from './PageToolbarTitle';

describe('PageToolbarTitle', () => {
    it('renders with default props', () => {
        const { container } = render(<PageToolbarTitle>Test Title</PageToolbarTitle>);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with additional props', () => {
        const { container } = render(
            <PageToolbarTitle
                color="primary"
                align="center"
                className="custom-class"
            >
                Styled Title
            </PageToolbarTitle>,
        );
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with noWrap disabled', () => {
        const { container } = render(<PageToolbarTitle noWrap={false}>No Wrap Title</PageToolbarTitle>);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with component override', () => {
        const { container } = render(<PageToolbarTitle component="div">Div Title</PageToolbarTitle>);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('has correct test id for accessibility', () => {
        render(<PageToolbarTitle>Test Title</PageToolbarTitle>);
        expect(screen.getByTestId('pageToolbar-title')).toBeInTheDocument();
    });

    it('displays the correct text content', () => {
        render(<PageToolbarTitle>My Page Title</PageToolbarTitle>);
        expect(screen.getByTestId('pageToolbar-title')).toHaveTextContent('My Page Title');
    });

    it('handles complex children', () => {
        render(
            <PageToolbarTitle>
                <span>Title with </span>
                <strong>emphasis</strong>
            </PageToolbarTitle>,
        );

        expect(screen.getByText('Title with')).toBeInTheDocument();
        expect(screen.getByText('emphasis')).toBeInTheDocument();
    });
});
