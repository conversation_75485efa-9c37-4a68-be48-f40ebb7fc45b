// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PageToolbar /> renders empty toolbar correctly 1`] = `
<div
  class="MuiBox-root css-hu8qr2"
>
  <div
    class="MuiBox-root css-138ieew"
  >
    <div
      class="MuiBox-root css-1a99p2u"
    />
  </div>
</div>
`;

exports[`<PageToolbar /> renders with all features combined 1`] = `
<div
  class="MuiBox-root css-1w0cxss"
>
  <button
    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorSecondary MuiIconButton-sizeLarge css-1dq23oi-MuiButtonBase-root-MuiIconButton-root"
    data-testid="pageToolbar-backButton"
    tabindex="0"
    type="button"
  >
    <svg
      aria-hidden="true"
      class="svg-inline--fa fa-arrow-left "
      color="#1B4AD5"
      data-icon="arrow-left"
      data-prefix="fas"
      focusable="false"
      role="img"
      viewBox="0 0 448 512"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"
        fill="currentColor"
      />
    </svg>
  </button>
  <div
    class="MuiBox-root css-138ieew"
  >
    <div
      class="MuiBox-root css-1a99p2u"
    >
      <div
        data-testid="combined-content"
      >
        Combined Content
      </div>
    </div>
    <div
      data-testid="breadcrumb-navigator"
    >
      <span
        data-testid="breadcrumb-0"
      >
        Home
      </span>
      <span
        data-testid="breadcrumb-1"
      >
        Section
      </span>
      <span
        data-testid="breadcrumb-2"
      >
        Current Page
      </span>
    </div>
  </div>
</div>
`;

exports[`<PageToolbar /> renders with border when withBorder is true 1`] = `
<div
  class="MuiBox-root css-1w0cxss"
>
  <div
    class="MuiBox-root css-138ieew"
  >
    <div
      class="MuiBox-root css-1a99p2u"
    />
  </div>
</div>
`;

exports[`<PageToolbar /> renders with default props 1`] = `
<div
  class="MuiBox-root css-hu8qr2"
>
  <div
    class="MuiBox-root css-138ieew"
  >
    <div
      class="MuiBox-root css-1a99p2u"
    />
  </div>
</div>
`;

exports[`<PageToolbar /> renders without border when withBorder is false 1`] = `
<div
  class="MuiBox-root css-hu8qr2"
>
  <div
    class="MuiBox-root css-138ieew"
  >
    <div
      class="MuiBox-root css-1a99p2u"
    />
  </div>
</div>
`;
