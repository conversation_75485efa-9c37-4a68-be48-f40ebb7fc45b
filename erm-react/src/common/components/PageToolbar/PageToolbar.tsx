import React, { PropsWithChildren } from 'react';
import Box, { BoxProps } from '@mui/material/Box';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

import BreadcrumbNavigator, { Breadcrumb } from 'common/components/BreadcrumbNavigator';

import PageToolbarGroup from './PageToolbarGroup';
import PageToolbarBackButton from './PageToolbarBackButton';
import { MIN_TOOLBAR_HEIGHT, SMALL_SCREEN_MAX_WIDTH } from './const';

export type PageToolbarProps = BoxProps & {
    breadcrumbs?: Breadcrumb[];
    onBack?: () => void;
    withBorder?: boolean;
};

const PageToolbar: React.FC<PropsWithChildren<PageToolbarProps>> = ({ breadcrumbs, onBack, children, withBorder, ...other }) => {
    const isSmallScreen = useMediaQuery(`(max-width:${SMALL_SCREEN_MAX_WIDTH}px)`);
    const theme = useTheme();

    return (
        <Box
            display="flex"
            paddingY={1.5}
            paddingX={isSmallScreen ? 2 : 3}
            maxWidth="100%"
            borderBottom={withBorder ? `1px solid ${theme.palette.protechtGrey?.grey_231}` : 0}
            {...other}
        >
            {onBack && <PageToolbarBackButton onBack={onBack} />}
            <PageToolbarGroup
                flexDirection="column"
                alignItems="stretch"
                gap={0}
                flex="1"
                minHeight={`${MIN_TOOLBAR_HEIGHT}px`}
            >
                <PageToolbarGroup>{children}</PageToolbarGroup>
                {breadcrumbs && <BreadcrumbNavigator navigationItems={breadcrumbs} />}
            </PageToolbarGroup>
        </Box>
    );
};

export default PageToolbar;
