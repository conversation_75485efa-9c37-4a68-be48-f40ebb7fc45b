import React from 'react';
import { render, screen } from 'test/utils';
import PageToolbarGroup from './PageToolbarGroup';
import { MIN_TOOLBAR_HEIGHT } from '../const';

describe('<PageToolbarGroup />', () => {
    it('renders with default props', () => {
        const { container } = render(
            <PageToolbarGroup data-testid="toolbar-group">
                <span>Test Content</span>
            </PageToolbarGroup>,
        );
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders children correctly', () => {
        render(
            <PageToolbarGroup data-testid="toolbar-group">
                <span data-testid="child">Test Child</span>
            </PageToolbarGroup>,
        );

        expect(screen.getByTestId('toolbar-group')).toBeInTheDocument();
        expect(screen.getByTestId('child')).toBeInTheDocument();
        expect(screen.getByText('Test Child')).toBeInTheDocument();
    });

    it('applies default gap and minHeight styles', () => {
        render(<PageToolbarGroup data-testid="toolbar-group">Content</PageToolbarGroup>);

        const toolbarGroup = screen.getByTestId('toolbar-group');
        expect(toolbarGroup).toHaveStyle('gap: 10px');
        expect(toolbarGroup).toHaveStyle(`min-height: ${MIN_TOOLBAR_HEIGHT}px`);
    });

    it('forwards additional props to the underlying component', () => {
        const { container } = render(
            <PageToolbarGroup
                data-testid="toolbar-group"
                id="custom-id"
                className="custom-class"
            >
                Test Content
            </PageToolbarGroup>,
        );

        const toolbarGroup = screen.getByTestId('toolbar-group');
        expect(toolbarGroup).toHaveAttribute('id', 'custom-id');
        expect(toolbarGroup).toHaveClass('custom-class');
        expect(container.firstChild).toMatchSnapshot();
    });

    it('allows overriding default props', () => {
        const { container } = render(
            <PageToolbarGroup
                data-testid="toolbar-group"
                gap="20px"
                minHeight="60px"
            >
                Content
            </PageToolbarGroup>,
        );

        const toolbarGroup = screen.getByTestId('toolbar-group');
        expect(toolbarGroup).toHaveStyle('gap: 20px');
        expect(toolbarGroup).toHaveStyle('min-height: 60px');
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders as a div element by default', () => {
        render(<PageToolbarGroup data-testid="toolbar-group">Content</PageToolbarGroup>);

        const toolbarGroup = screen.getByTestId('toolbar-group');
        expect(toolbarGroup.tagName).toBe('DIV');
    });
});
