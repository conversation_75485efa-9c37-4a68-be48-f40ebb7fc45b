import React from 'react';
import ToolbarGroup, { ToolbarGroupProps } from 'common/components/ToolbarSpacing/ToolbarGroup';
import { MIN_TOOLBAR_HEIGHT } from '../const';

export type PageToolbarGroupProps = ToolbarGroupProps;

const PageToolbarGroup: React.FC<PageToolbarGroupProps> = (props) => {
    return (
        <ToolbarGroup
            gap="10px"
            minHeight={`${MIN_TOOLBAR_HEIGHT}px`}
            {...props}
        />
    );
};

export default PageToolbarGroup;
