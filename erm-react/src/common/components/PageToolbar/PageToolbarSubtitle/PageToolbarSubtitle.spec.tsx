import React from 'react';
import { render, screen } from 'test/utils';
import PageToolbarSubtitle from './PageToolbarSubtitle';

describe('<PageToolbarSubtitle />', () => {
    it('renders with default props', () => {
        const { container } = render(<PageToolbarSubtitle>Test Subtitle</PageToolbarSubtitle>);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with children text', () => {
        render(<PageToolbarSubtitle>Test Subtitle</PageToolbarSubtitle>);
        expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
        expect(screen.getByTestId('pageToolbar-subtitle')).toBeInTheDocument();
    });

    it('renders as a div element with h1 variant styling', () => {
        render(<PageToolbarSubtitle>Test Subtitle</PageToolbarSubtitle>);
        const subtitle = screen.getByTestId('pageToolbar-subtitle');
        expect(subtitle.tagName).toBe('DIV');
    });

    it('forwards additional props to Typography component', () => {
        const { container } = render(
            <PageToolbarSubtitle
                id="custom-id"
                className="custom-class"
                {...({ 'data-custom': 'custom-value' } as any)}
            >
                Test Subtitle
            </PageToolbarSubtitle>,
        );

        const subtitle = screen.getByTestId('pageToolbar-subtitle');
        expect(subtitle).toHaveAttribute('id', 'custom-id');
        expect(subtitle).toHaveClass('custom-class');
        expect(subtitle).toHaveAttribute('data-custom', 'custom-value');
        expect(container.firstChild).toMatchSnapshot();
    });

    it('overrides default props when passed', () => {
        const { container } = render(
            <PageToolbarSubtitle
                component="span"
                noWrap={false}
            >
                Test Subtitle
            </PageToolbarSubtitle>,
        );

        const subtitle = screen.getByTestId('pageToolbar-subtitle');
        expect(subtitle.tagName).toBe('SPAN');
        expect(container.firstChild).toMatchSnapshot();
    });

    it('handles empty children', () => {
        const { container } = render(<PageToolbarSubtitle></PageToolbarSubtitle>);
        const subtitle = screen.getByTestId('pageToolbar-subtitle');
        expect(subtitle).toBeInTheDocument();
        expect(subtitle).toBeEmptyDOMElement();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('handles React node children', () => {
        const ReactNodeChild = <span data-testid="child-element">Child Element</span>;
        const { container } = render(<PageToolbarSubtitle>{ReactNodeChild}</PageToolbarSubtitle>);

        expect(screen.getByTestId('pageToolbar-subtitle')).toBeInTheDocument();
        expect(screen.getByTestId('child-element')).toBeInTheDocument();
        expect(screen.getByText('Child Element')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('handles multiple children', () => {
        const { container } = render(
            <PageToolbarSubtitle>
                <span
                    key="1"
                    data-testid="child-1"
                >
                    First
                </span>
                <span
                    key="2"
                    data-testid="child-2"
                >
                    Second
                </span>
            </PageToolbarSubtitle>,
        );

        expect(screen.getByTestId('pageToolbar-subtitle')).toBeInTheDocument();
        expect(screen.getByTestId('child-1')).toBeInTheDocument();
        expect(screen.getByTestId('child-2')).toBeInTheDocument();
        expect(screen.getByText('First')).toBeInTheDocument();
        expect(screen.getByText('Second')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('applies custom styling props', () => {
        const { container } = render(
            <PageToolbarSubtitle
                sx={{ fontSize: '20px', fontWeight: 'bold' }}
                style={{ textDecoration: 'underline' }}
            >
                Test Subtitle
            </PageToolbarSubtitle>,
        );

        const subtitle = screen.getByTestId('pageToolbar-subtitle');
        expect(subtitle).toHaveStyle('text-decoration: underline');
        expect(container.firstChild).toMatchSnapshot();
    });

    it('handles long text content', () => {
        const longText = 'This is a very long subtitle text that should be truncated with ellipsis when noWrap is applied';
        const { container } = render(<PageToolbarSubtitle>{longText}</PageToolbarSubtitle>);

        expect(screen.getByText(longText)).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('is accessible and visible', () => {
        render(<PageToolbarSubtitle>Test Subtitle</PageToolbarSubtitle>);
        const subtitle = screen.getByTestId('pageToolbar-subtitle');

        expect(subtitle).toBeVisible();
        expect(subtitle).toBeInTheDocument();
    });

    it('renders with custom component and maintains semantic structure', () => {
        const { container } = render(<PageToolbarSubtitle component="p">Custom component subtitle</PageToolbarSubtitle>);

        const subtitle = screen.getByTestId('pageToolbar-subtitle');
        expect(subtitle.tagName).toBe('P');
        expect(screen.getByText('Custom component subtitle')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('applies padding left styling', () => {
        render(<PageToolbarSubtitle>Test Subtitle</PageToolbarSubtitle>);
        const subtitle = screen.getByTestId('pageToolbar-subtitle');
        expect(subtitle).toHaveStyle('padding-left: 10px');
    });

    it('renders with different children types', () => {
        const { container } = render(
            <PageToolbarSubtitle>
                <div>
                    <strong>Bold Text</strong>
                    <em>Italic Text</em>
                </div>
            </PageToolbarSubtitle>,
        );

        expect(screen.getByText('Bold Text')).toBeInTheDocument();
        expect(screen.getByText('Italic Text')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });
});
