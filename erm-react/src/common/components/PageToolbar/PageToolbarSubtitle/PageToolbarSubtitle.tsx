import React from 'react';
import Typography, { TypographyProps } from '@mui/material/Typography';

export type PageToolbarSubtitleProps = Omit<TypographyProps, 'variant'>;

const PageToolbarSubtitle: React.FC<PageToolbarSubtitleProps> = (props) => {
    return (
        <Typography
            variant="h1"
            component="div"
            color="protechtGrey.grey_146"
            noWrap
            paddingLeft="10px"
            data-testid="pageToolbar-subtitle"
            {...props}
        />
    );
};

export default PageToolbarSubtitle;
