// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PageToolbarSubtitle /> applies custom styling props 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-y0xyg8-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
  style="text-decoration: underline;"
>
  Test Subtitle
</div>
`;

exports[`<PageToolbarSubtitle /> forwards additional props to Typography component 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap custom-class css-1wtqngq-MuiTypography-root"
  data-custom="custom-value"
  data-testid="pageToolbar-subtitle"
  id="custom-id"
>
  Test Subtitle
</div>
`;

exports[`<PageToolbarSubtitle /> handles React node children 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1wtqngq-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
>
  <span
    data-testid="child-element"
  >
    Child Element
  </span>
</div>
`;

exports[`<PageToolbarSubtitle /> handles empty children 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1wtqngq-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
/>
`;

exports[`<PageToolbarSubtitle /> handles long text content 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1wtqngq-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
>
  This is a very long subtitle text that should be truncated with ellipsis when noWrap is applied
</div>
`;

exports[`<PageToolbarSubtitle /> handles multiple children 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1wtqngq-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
>
  <span
    data-testid="child-1"
  >
    First
  </span>
  <span
    data-testid="child-2"
  >
    Second
  </span>
</div>
`;

exports[`<PageToolbarSubtitle /> overrides default props when passed 1`] = `
<span
  class="MuiTypography-root MuiTypography-h1 css-1izevwk-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
>
  Test Subtitle
</span>
`;

exports[`<PageToolbarSubtitle /> renders with custom component and maintains semantic structure 1`] = `
<p
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1wtqngq-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
>
  Custom component subtitle
</p>
`;

exports[`<PageToolbarSubtitle /> renders with default props 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1wtqngq-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
>
  Test Subtitle
</div>
`;

exports[`<PageToolbarSubtitle /> renders with different children types 1`] = `
<div
  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-1wtqngq-MuiTypography-root"
  data-testid="pageToolbar-subtitle"
>
  <div>
    <strong>
      Bold Text
    </strong>
    <em>
      Italic Text
    </em>
  </div>
</div>
`;
