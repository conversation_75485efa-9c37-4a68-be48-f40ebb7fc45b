import React from 'react';
import { render, screen } from 'test/utils';
import PageToolbarActionButton from './PageToolbarActionButton';

const mockIcon = <div data-testid="mock-icon">Icon</div>;

describe('<PageToolbarActionButton />', () => {
    it('renders with default props', () => {
        const { container } = render(<PageToolbarActionButton label="Test Button" />);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with label text', () => {
        render(<PageToolbarActionButton label="Test Button" />);

        expect(screen.getByTestId('pageToolbar-actionButton')).toBeInTheDocument();
        expect(screen.getByText('Test Button')).toBeInTheDocument();
    });

    it('renders with custom variant', () => {
        const { container } = render(
            <PageToolbarActionButton
                label="Primary Button"
                variant="primary"
            />,
        );

        expect(screen.getByTestId('pageToolbar-actionButton')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with start icon', () => {
        const { container } = render(
            <PageToolbarActionButton
                label="Button with Icon"
                icon={mockIcon}
            />,
        );

        expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
        expect(screen.getByText('Button with Icon')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with end icon', () => {
        const { container } = render(
            <PageToolbarActionButton
                label="Button with End Icon"
                endIcon={mockIcon}
            />,
        );

        expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
        expect(screen.getByText('Button with End Icon')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('hides label when hideLabel is true', () => {
        const { container } = render(
            <PageToolbarActionButton
                label="Hidden Label"
                hideLabel={true}
            />,
        );

        expect(screen.queryByText('Hidden Label')).not.toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with custom data test id', () => {
        render(
            <PageToolbarActionButton
                label="Custom Button"
                dataTestId="custom-action-button"
            />,
        );

        expect(screen.getByTestId('custom-action-button')).toBeInTheDocument();
    });

    it('handles disabled state', () => {
        const { container } = render(
            <PageToolbarActionButton
                label="Disabled Button"
                disabled={true}
            />,
        );

        const button = screen.getByTestId('pageToolbar-actionButton');
        expect(button).toBeDisabled();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('handles click events', () => {
        const mockClick = jest.fn();
        render(
            <PageToolbarActionButton
                label="Clickable Button"
                onClick={mockClick}
            />,
        );

        const button = screen.getByTestId('pageToolbar-actionButton');
        button.click();
        expect(mockClick).toHaveBeenCalledTimes(1);
    });

    it('uses secondary variant by default', () => {
        const { container } = render(<PageToolbarActionButton label="Default Button" />);

        const button = screen.getByTestId('pageToolbar-actionButton');
        expect(button).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });
});
