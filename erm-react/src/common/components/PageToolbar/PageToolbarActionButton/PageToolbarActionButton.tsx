import React from 'react';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { CustomVariant } from '@protecht/ui-library/library/components/Button/Button';
import { SxProps, Theme } from '@mui/material/styles';

export type PageToolbarActionButtonProps = {
    label: string;
    variant?: CustomVariant;
    onClick?: () => void;
    disabled?: boolean;
    dataTestId?: string;
    icon?: React.ReactNode;
    endIcon?: React.ReactNode;
    hideLabel?: boolean;
    forceMinWidth?: boolean;
    sx?: SxProps<Theme>;
};

const PageToolbarActionButton: React.FC<PageToolbarActionButtonProps> = ({
    label,
    variant = 'secondary',
    icon,
    hideLabel = false,
    forceMinWidth = false,
    sx,
    dataTestId = 'pageToolbar-actionButton',
    ...other
}) => {
    return (
        <Button
            {...ButtonStyles.pageToolbarButton}
            hideTextOnSmallScreen={false}
            startIcon={icon}
            variant={variant}
            aria-label={hideLabel ? label : undefined}
            dataTestId={dataTestId}
            sx={{ ...ButtonStyles.pageToolbarButton.sx, minWidth: forceMinWidth ? ButtonStyles.pageToolbarButton.sx.minWidth : 'none', ...sx }}
            {...other}
        >
            {hideLabel ? '' : label}
        </Button>
    );
};

export default PageToolbarActionButton;
