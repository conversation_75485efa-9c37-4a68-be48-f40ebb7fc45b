// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PageToolbarActionButton /> handles disabled state 1`] = `
<button
  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge <PERSON>on-outlinedSizeLarge <PERSON>on-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge <PERSON>utton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1wgq4av-MuiButtonBase-root-MuiButton-root"
  data-testid="pageToolbar-actionButton"
  disabled=""
  tabindex="-1"
  type="button"
>
  <span
    class="css-1d0doyg"
  >
    Disabled Button
  </span>
</button>
`;

exports[`<PageToolbarActionButton /> hides label when hideLabel is true 1`] = `
<button
  aria-label="Hidden Label"
  class="MuiButtonBase-root Mu<PERSON><PERSON>utton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge Mui<PERSON>utton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1wgq4av-MuiButtonBase-root-MuiButton-root"
  data-testid="pageToolbar-actionButton"
  tabindex="0"
  type="button"
/>
`;

exports[`<PageToolbarActionButton /> renders with custom variant 1`] = `
<button
  class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-11v4jx-MuiButtonBase-root-MuiButton-root"
  data-testid="pageToolbar-actionButton"
  tabindex="0"
  type="button"
>
  <span
    class="css-1d0doyg"
  >
    Primary Button
  </span>
</button>
`;

exports[`<PageToolbarActionButton /> renders with default props 1`] = `
<button
  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1wgq4av-MuiButtonBase-root-MuiButton-root"
  data-testid="pageToolbar-actionButton"
  tabindex="0"
  type="button"
>
  <span
    class="css-1d0doyg"
  >
    Test Button
  </span>
</button>
`;

exports[`<PageToolbarActionButton /> renders with end icon 1`] = `
<button
  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1wgq4av-MuiButtonBase-root-MuiButton-root"
  data-testid="pageToolbar-actionButton"
  tabindex="0"
  type="button"
>
  <span
    class="css-1d0doyg"
  >
    Button with End Icon
  </span>
  <span
    class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
  >
    <div
      data-testid="mock-icon"
    >
      Icon
    </div>
  </span>
</button>
`;

exports[`<PageToolbarActionButton /> renders with start icon 1`] = `
<button
  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-13z3jq8-MuiButtonBase-root-MuiButton-root"
  data-testid="pageToolbar-actionButton"
  tabindex="0"
  type="button"
>
  <span
    class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-vpap0f-MuiButton-startIcon"
  >
    <div
      data-testid="mock-icon"
    >
      Icon
    </div>
  </span>
  <span
    class="css-1d0doyg"
  >
    Button with Icon
  </span>
</button>
`;

exports[`<PageToolbarActionButton /> uses secondary variant by default 1`] = `
<button
  class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1wgq4av-MuiButtonBase-root-MuiButton-root"
  data-testid="pageToolbar-actionButton"
  tabindex="0"
  type="button"
>
  <span
    class="css-1d0doyg"
  >
    Default Button
  </span>
</button>
`;
