// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PageToolbarDropdown /> renders with custom button label 1`] = `
<div
  data-testid="context-menu"
>
  <button
    class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge Mu<PERSON>utton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root Mu<PERSON><PERSON>utton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge Mu<PERSON>on-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1wgq4av-MuiButtonBase-root-MuiButton-root"
    data-testid="pageToolbar-dropdown"
    tabindex="0"
    type="button"
  >
    <span
      class="css-1d0doyg"
    >
      Custom Actions
    </span>
    <span
      class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
    >
      <div
        data-testid="chevron-down-icon"
      />
    </span>
  </button>
  <div
    data-testid="menu-items"
  >
    <div
      data-testid="menu-item"
    >
      Option 1
    </div>
    <div
      data-testid="menu-item"
    >
      Option 2
    </div>
  </div>
</div>
`;

exports[`<PageToolbarDropdown /> renders with default props 1`] = `
<div
  data-testid="context-menu"
>
  <button
    class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1wgq4av-MuiButtonBase-root-MuiButton-root"
    data-testid="pageToolbar-dropdown"
    tabindex="0"
    type="button"
  >
    <span
      class="css-1d0doyg"
    >
      More
    </span>
    <span
      class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
    >
      <div
        data-testid="chevron-down-icon"
      />
    </span>
  </button>
  <div
    data-testid="menu-items"
  >
    <div
      data-testid="menu-item"
    >
      Option 1
    </div>
    <div
      data-testid="menu-item"
    >
      Option 2
    </div>
  </div>
</div>
`;

exports[`<PageToolbarDropdown /> renders with hidden button label 1`] = `
<div
  data-testid="context-menu"
>
  <button
    aria-label="Actions"
    class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1cxqs7b-MuiButtonBase-root-MuiButton-root"
    data-testid="pageToolbar-dropdown"
    tabindex="0"
    type="button"
  >
    <span
      class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeLarge css-4egbtc-MuiButton-endIcon"
    >
      <div
        data-testid="chevron-down-icon"
      />
    </span>
  </button>
  <div
    data-testid="menu-items"
  >
    <div
      data-testid="menu-item"
    >
      Option 1
    </div>
    <div
      data-testid="menu-item"
    >
      Option 2
    </div>
  </div>
</div>
`;
