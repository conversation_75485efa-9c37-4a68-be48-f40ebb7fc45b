import React from 'react';
import { render, screen } from 'test/utils';
import PageToolbarDropdown from './PageToolbarDropdown';

// Mock the ui-library components
jest.mock('@protecht/ui-library/library/components/ContextMenu', () => {
    return ({ baseElement, items }: any) => (
        <div data-testid="context-menu">
            {baseElement}
            <div data-testid="menu-items">
                {items.map((item: any, index: number) => (
                    <div
                        key={index}
                        data-testid="menu-item"
                    >
                        {item.label}
                    </div>
                ))}
            </div>
        </div>
    );
});

jest.mock('@protecht/ui-library/library/components/SVGIcons', () => ({
    ChevronDown: () => <div data-testid="chevron-down-icon" />,
}));

const mockItems = [
    { label: 'Option 1', onClick: jest.fn() },
    { label: 'Option 2', onClick: jest.fn() },
];

describe('<PageToolbarDropdown />', () => {
    it('renders with default props', () => {
        const { container } = render(<PageToolbarDropdown items={mockItems} />);
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with default "More" button label', () => {
        render(<PageToolbarDropdown items={mockItems} />);

        expect(screen.getByTestId('pageToolbar-dropdown')).toBeInTheDocument();
        expect(screen.getByText('More')).toBeInTheDocument();
    });

    it('renders with custom button label', () => {
        const { container } = render(
            <PageToolbarDropdown
                items={mockItems}
                buttonLabel="Custom Actions"
            />,
        );

        expect(screen.getByText('Custom Actions')).toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders menu items correctly', () => {
        render(<PageToolbarDropdown items={mockItems} />);

        expect(screen.getByTestId('menu-items')).toBeInTheDocument();
        expect(screen.getByText('Option 1')).toBeInTheDocument();
        expect(screen.getByText('Option 2')).toBeInTheDocument();
    });

    it('renders with hidden button label', () => {
        const { container } = render(
            <PageToolbarDropdown
                items={mockItems}
                hideButtonLabel={true}
                buttonLabel="Actions"
            />,
        );

        expect(screen.queryByText('Actions')).not.toBeInTheDocument();
        expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with custom data test id', () => {
        render(
            <PageToolbarDropdown
                items={mockItems}
                dataTestId="custom-dropdown"
            />,
        );

        expect(screen.getByTestId('custom-dropdown')).toBeInTheDocument();
    });

    it('includes chevron down icon', () => {
        render(<PageToolbarDropdown items={mockItems} />);

        expect(screen.getByTestId('chevron-down-icon')).toBeInTheDocument();
    });
});
