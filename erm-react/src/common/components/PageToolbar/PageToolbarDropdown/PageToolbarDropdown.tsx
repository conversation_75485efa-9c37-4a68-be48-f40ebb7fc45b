import React from 'react';

import ContextMenu from '@protecht/ui-library/library/components/ContextMenu';
import { ContextMenuItem } from '@protecht/ui-library/library/components/ContextMenu/types';
import { ChevronDown } from '@protecht/ui-library/library/components/SVGIcons';

import { getReactRoot } from 'config';
import { strings } from 'common/utils/i18n';
import PageToolbarActionButton from '../PageToolbarActionButton';

export type PageToolbarDropdownProps = {
    buttonLabel?: string;
    hideButtonLabel?: boolean;
    items: ContextMenuItem[];
    dataTestId?: string;
};

const PageToolbarDropdown: React.FC<PageToolbarDropdownProps> = ({
    buttonLabel = strings('common:button.more'),
    hideButtonLabel = false,
    dataTestId = 'pageToolbar-dropdown',
    items,
}) => {
    return (
        <ContextMenu
            baseElement={
                <PageToolbarActionButton
                    label={buttonLabel}
                    hideLabel={hideButtonLabel}
                    endIcon={<ChevronDown />}
                    dataTestId={dataTestId}
                    aria-label={hideButtonLabel ? buttonLabel : undefined}
                />
            }
            menuAnchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            menuTransformOrigin={{ horizontal: 'right', vertical: 'top' }}
            items={items}
            rootContainer={getReactRoot()}
        />
    );
};

export default PageToolbarDropdown;
