import React, { useCallback, useEffect, useRef, useState } from 'react';
import { isEqual, merge, uniqBy } from 'lodash';
import useTheme from '@mui/system/useTheme';
import { styled } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import ListItemText from '@mui/material/ListItemText';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import { TreeItem, SimpleTreeView as TreeView } from '@mui/x-tree-view';

import Search from '@protecht/ui-library/library/components/Inputs/Search';
import { TriangleDown, TriangleRight } from '@protecht/ui-library/library/components/SVGIcons';
import { defaultStyledOptions } from '@protecht/ui-library/library/utils/defaultStyledOptions';

import { IdWithName } from 'app/types';
import { BusinessUnitSimpleRest, BusinessUnitSimpleTreeFilteredRest, IdWithNameAndStatusRest, IdWithNameRest, ViewExpressionRest } from 'api/generated/types';
import {
    useBursGetBusinessUnitsByIdsUsingPostMutation,
    useFilterBusinessUnitsMutation,
    useLazyBursGetAllChildrenIdsUsingGetQuery,
    useLazyBursGetBuSimpleUsingGetQuery,
    useLazyBursGetBuTreeUsingGetQuery,
    useLazyBursGetRootSimpleUsingGetQuery,
} from 'common/api/businessUnits';
import { strings } from 'common/utils/i18n';
import Loading, { LoaderType } from 'common/components/Loading/Loading';
import useSnackbar from 'common/hooks/useSnackbar';
import { getActiveBUs, getAllNodesIds, getStringId, getVisibleCount, insertInTree, shouldContinueExpanding, shouldExpandSelection } from './utils';
import BusinessUnitNode from './BusinessUnitNode';
import { ExpressionType } from 'view/types';

export type BusinessUnitFilterData = {
    id?: number;
    filterValue?: string;
    matchingBusinessunits?: number[];
};

type BusinessUnitSelectorProps = {
    filterData?: BusinessUnitFilterData;
    multiple?: boolean;
    selected?: IdWithNameAndStatusRest[];
    onSelect: (selected: IdWithNameAndStatusRest[]) => void;
    onLoadingChange?: (isLoading: boolean) => void;
};

const BodyGrid = styled(Grid)(({ theme }) => ({
    flex: 1,
    border: '1px solid ' + theme.palette.protechtGrey?.grey_178,
    marginTop: '10px',
    overflow: 'auto',
    padding: '6px 10px',
}));

const DropdownIconWrapper = styled('div')(() => ({
    '&:hover': {
        transform: 'scale(1.2)',
    },
    display: 'flex',
    justifyContent: 'center',
}));

const StyledTreeItem = styled(
    TreeItem,
    defaultStyledOptions,
)<{ $level: number }>(({ theme, $level }) => ({
    color: theme.palette.text.secondary,
    minWidth: '100%',
    width: 'max-content',
    '& .MuiTreeItem-root': {
        width: '100%',
    },
    '& .MuiTreeItem-content.Mui-focused': {
        background: 'none',
    },
    '& .MuiTreeItem-content:hover': {
        background: theme.palette.protechtGrey?.grey_250,
        '& .MuiTreeItem-content:hover': {
            background: 'none',
        },
    },
    '& .MuiTreeItem-content.Mui-selected, & .MuiTreeItem-content.Mui-selected.Mui-focused': {
        background: theme.palette.protechtGrey?.grey_220,

        '.MuiTreeItem-content': {
            background: 'none',
        },
    },
    '& .MuiTreeItem-content': {
        height: '38px',
        gap: 0,
        borderRadius: 0,
        padding: '7px 6px',
        '.MuiTreeItem-iconContainer': {
            width: '24px',
            height: '24px',
            alignItems: 'center',
            marginRight: 0,
        },
    },
    '& .MuiTreeItem-label': {
        width: '100%',
        paddingLeft: '6px',
    },
    '& .MuiCollapse-root': {
        margin: 0,
        width: '100%',
    },
    '& .MuiCollapse-wrapperInner .MuiTreeItem-content': {
        width: '100%',
        paddingLeft: `${($level + 1) * 30 + 6}px`,
    },
}));

const BusinessUnitSelector: React.FC<BusinessUnitSelectorProps> = ({
    multiple = false,
    selected = [],
    onSelect,
    onLoadingChange,
    filterData,
}: BusinessUnitSelectorProps) => {
    const theme = useTheme();
    const initialSelection = useRef<IdWithNameRest[]>([]);

    const initSelectedValues = useCallback(() => {
        const activeBUs = getActiveBUs(selected);
        initialSelection.current = activeBUs;
        return activeBUs;
    }, [selected]);

    const [totalCount, setTotalCount] = useState<number>(0);
    const [visibleCount, setVisibleCount] = useState<number>(0);
    const [selection, setSelection] = useState<IdWithNameRest[]>(initSelectedValues);

    const [expanded, setExpanded] = useState<number[]>([]);
    const [BUTree, setBUTree] = useState<BusinessUnitSimpleRest | undefined>(undefined);

    const [itemExpanding, setItemExpanding] = useState<BusinessUnitSimpleRest | undefined>();
    const [itemCollapsing, setItemCollapsing] = useState<BusinessUnitSimpleRest | undefined>();
    const [isLoadingPathToSelected, setIsLoadingPathToSelected] = useState(false);

    const { enqueueError } = useSnackbar();

    const [errorMessage, setErrorMessage] = useState<string | undefined>();

    const [searchValue, setSearchValue] = useState('');
    const [searchResult, setSearchResult] = useState<number[]>([]);

    const [contextChild, setContextChild] = useState<BusinessUnitSimpleRest>();
    const [xPos, setXPos] = useState<number>(0);
    const [yPos, setYPos] = useState<number>(0);

    const [isLoadingTree, setIsLoadingTree] = useState(true);
    const [triggerGetRootSimple] = useLazyBursGetRootSimpleUsingGetQuery();
    const [triggerGetBuSimple] = useLazyBursGetBuSimpleUsingGetQuery();
    const [triggerGetBuTree] = useLazyBursGetBuTreeUsingGetQuery();
    const [triggerGetAllChildrenIds] = useLazyBursGetAllChildrenIdsUsingGetQuery();
    const [triggerGetBusinessUnitsByIds, { isLoading: isLoadingBuIds }] = useBursGetBusinessUnitsByIdsUsingPostMutation();
    const [triggerSearch, { isLoading: isSearching }] = useFilterBusinessUnitsMutation();

    useEffect(() => {
        setVisibleCount(getVisibleCount(expanded, BUTree));
    }, [BUTree, expanded]);

    /**
     * When opening the BU selector with a preselected node, the tree should expand starting from the root node,
     * gradually revealing the parent nodes until the selected node is presented to the user.
     * This approach helps in minimizing the data load from the backend by only requesting essential information,
     * omitting the loading of unrelated tree branches, sibling data, or deeply nested sections.
     * While the current method achieves the desired outcome, a more efficient solution would involve implementing
     * a dedicated backend endpoint tailored for this functionality.
     */
    const expandSelectedItemPath = useCallback(
        async (tree: BusinessUnitSimpleRest) => {
            setIsLoadingPathToSelected(true);

            for (const selectedItem of initialSelection.current) {
                const expandSelection = await shouldExpandSelection(tree, selectedItem.id!, filterData);

                if (!expandSelection) {
                    setIsLoadingPathToSelected(false);
                    return;
                }

                const pathToSelectedNode: number[] = [];
                let nodeId = selectedItem.id!;
                let continueLoading = true;
                let treeWithSelectedNode: BusinessUnitSimpleRest | null = null;

                while (continueLoading) {
                    await triggerGetBuSimple({ businessunitId: nodeId })
                        .unwrap()
                        .then((treeNode) => {
                            continueLoading = shouldContinueExpanding(treeNode, tree.id!, filterData);

                            if (continueLoading) {
                                nodeId = treeNode.parentID!;
                                pathToSelectedNode.push(treeNode.parentID!);
                            }

                            if (treeWithSelectedNode === null) {
                                treeWithSelectedNode = treeNode;
                            } else {
                                const previousNestedTree = { ...treeWithSelectedNode };
                                treeWithSelectedNode = {
                                    ...treeNode,
                                    children: treeNode.children?.map((child) => (child.id! === treeWithSelectedNode?.id ? previousNestedTree : child)),
                                };
                            }
                        })
                        .catch(() => {
                            enqueueError(strings('library:message.errorLoadingBUs'));
                            continueLoading = false;
                        });
                }
                if (treeWithSelectedNode) {
                    setBUTree((previous) => {
                        if (filterData?.filterValue && filterData?.matchingBusinessunits) {
                            return merge(previous, treeWithSelectedNode);
                        }
                        return merge({}, previous, treeWithSelectedNode);
                    });
                    setExpanded((previous) => [...previous, ...pathToSelectedNode]);
                }

                setIsLoadingPathToSelected(false);
            }
        },
        [enqueueError, filterData, triggerGetBuSimple],
    );

    const getRequest = useCallback(async () => {
        if (filterData) {
            if (filterData?.id) {
                return triggerGetBuSimple({ businessunitId: filterData.id }).unwrap();
            } else if (filterData.filterValue) {
                return triggerGetBusinessUnitsByIds({ body: filterData?.matchingBusinessunits ?? [] })
                    .unwrap()
                    .then((res) => res?.businessUnitSimpleRests?.[0]);
            }
        }
        return triggerGetRootSimple()
            .unwrap()
            .then((res) => res[0]);
    }, [filterData, triggerGetBuSimple, triggerGetBusinessUnitsByIds, triggerGetRootSimple]);

    useEffect(() => {
        getRequest()
            .then((res) => {
                setTotalCount((res?.numberOfChildren ?? 0) + 1);
                setBUTree(res);
                setExpanded(res ? [res.id!] : []);
                setContextChild(undefined);

                if (res && initialSelection.current.length > 0) {
                    void expandSelectedItemPath(res);
                }
            })
            .catch((rej) => {
                setTotalCount(0);
                setErrorMessage(rej.error || strings('library:message.errorExpandingRootBU'));
            })
            .finally(() => {
                setIsLoadingTree(false);
            });
    }, [triggerGetRootSimple, triggerGetBuTree, expandSelectedItemPath, filterData, triggerGetBuSimple, getRequest]);

    // disable confirm button when loading/searching business units
    // or when new user selection is not different from pre-selected items
    useEffect(() => {
        const oldSelectedIds = initialSelection.current ? initialSelection.current.map((item) => item.id).sort() : [];
        const newSelectedIds = selection.map((item) => item.id).sort();

        onLoadingChange?.(isLoadingTree || isSearching || isEqual(oldSelectedIds, newSelectedIds));
    }, [isLoadingTree, isSearching, onLoadingChange, selection]);

    const isItemSelected = useCallback((id?: number) => selection.some((item) => item.id === id), [selection]);

    const insertChildren = useCallback(
        (node: BusinessUnitSimpleRest) => {
            if (BUTree?.children) {
                const newChildren = insertInTree(node, BUTree.id!, BUTree.children);
                setBUTree((prevTree) => ({ ...prevTree, children: newChildren }));
            }
        },
        [BUTree?.children, BUTree?.id],
    );

    const expandAllChildren = useCallback(
        (node: BusinessUnitSimpleRest) => {
            if (!node.children || node.children.length === 0) {
                return;
            }

            setExpanded([...expanded, node.id!, ...getAllNodesIds(node!.children!)]);
        },
        [expanded],
    );

    const handleSelect = useCallback(
        (item: BusinessUnitSimpleRest) => {
            const { id, name } = item;
            if (!multiple) {
                const newObj = isItemSelected(id) ? [] : [{ id, name }];
                setSelection(newObj);
            } else {
                if (isItemSelected(id)) {
                    setSelection((prev) => prev.filter((objId: IdWithName) => objId.id !== id));
                } else {
                    setSelection((prev) => [...prev, { id, name }]);
                }
            }
        },
        [isItemSelected, multiple],
    );

    const handleItemCollapse = useCallback((item: BusinessUnitSimpleRest) => setItemCollapsing(item), []);
    const handleItemExpand = useCallback((item: BusinessUnitSimpleRest) => setItemExpanding(item), []);

    const handleAPIResult = useCallback(
        (res: BusinessUnitSimpleTreeFilteredRest) => {
            if (res.businessUnitSimpleRests) {
                setBUTree(res.businessUnitSimpleRests[0]);
                expandAllChildren(res.businessUnitSimpleRests[0]);
            }
            if (res.matchingBusinessunits) {
                setSearchResult(res.matchingBusinessunits);
            }
        },
        [expandAllChildren],
    );

    const handleResetTree = useCallback(() => {
        const BUBackup = BUTree ? { ...BUTree } : undefined;
        setSearchResult([]);
        setErrorMessage(undefined);

        getRequest()
            .then((res) => {
                setBUTree(res);
                setTotalCount((res?.numberOfChildren ?? 0) + 1);
                setExpanded(res ? [res.id!] : []);
            })
            .catch((rej) => {
                setErrorMessage(rej.error || strings('library:message.errorExpandingRootBU'));
                if (BUBackup) {
                    setBUTree(BUBackup);
                } else {
                    setTotalCount(0);
                }
            });
    }, [BUTree, getRequest]);

    const handleExpandChildren = async () => {
        if (contextChild) {
            setItemExpanding(contextChild);
            setErrorMessage(undefined);
            triggerGetBuTree({ businessunitId: contextChild.id! })
                .unwrap()
                .then((res) => {
                    insertChildren(res);
                    expandAllChildren(res);
                })
                .catch((rej) => {
                    setErrorMessage(rej.error || strings('library:message.errorExpandingBUChild'));
                    setItemExpanding(undefined);
                })
                .finally(() => {
                    setContextChild(undefined);
                });
        }
    };

    const handleSelectChildrenIds = async () => {
        if (contextChild) {
            setErrorMessage(undefined);
            triggerGetAllChildrenIds({ businessunitId: contextChild.id! })
                .unwrap()
                .then((res) => {
                    setSelection(uniqBy([...selection, ...res, { id: contextChild.id, name: contextChild.name }], 'id'));
                })
                .catch((rej) => {
                    setErrorMessage(rej.error || strings('library:message.errorSelectingBUs'));
                })
                .finally(() => {
                    setContextChild(undefined);
                });
        }
    };

    const handleDeselectChildrenIds = async () => {
        if (contextChild) {
            setErrorMessage(undefined);
            triggerGetAllChildrenIds({ businessunitId: contextChild.id! })
                .unwrap()
                .then((res) => {
                    setSelection([...selection.filter((item) => !res.some((ritem) => ritem.id === item.id) && item.id !== contextChild.id)]);
                })
                .catch((rej) => {
                    setErrorMessage(rej.error || strings('library:message.errorDeselectingBUs'));
                })
                .finally(() => {
                    setContextChild(undefined);
                });
        }
    };

    const handleContextMenuClose = () => {
        setXPos(0);
        setYPos(0);
        setContextChild(undefined);
    };

    const onContextMenu = (event: React.MouseEvent<HTMLElement>, child: BusinessUnitSimpleRest) => {
        event.preventDefault();
        setContextChild(child);

        setXPos(event.clientX);
        setYPos(event.clientY);
    };

    const onContextMenuRendered = (event: MouseEvent) => {
        event.preventDefault();
        if (xPos && yPos) {
            handleContextMenuClose();
        }
    };

    useEffect(() => {
        if (itemExpanding && !contextChild) {
            if (itemExpanding.children && itemExpanding.children.length > 0) {
                setExpanded([...expanded, itemExpanding.id!]);
            } else {
                setErrorMessage(undefined);
                triggerGetBuSimple({ businessunitId: itemExpanding.id! })
                    .unwrap()
                    .then((res) => {
                        insertChildren(res);
                        setExpanded([...expanded, res.id!]);
                    })
                    .catch((rej) => {
                        setErrorMessage(rej.error || strings('library:message.errorExpandingBU'));
                        setItemExpanding(undefined);
                    });
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [itemExpanding]);

    useEffect(() => {
        setItemExpanding(undefined);
        setItemCollapsing(undefined);
    }, [expanded]);

    useEffect(() => {
        if (itemCollapsing) {
            setExpanded(expanded.filter((item) => item !== itemCollapsing.id));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [itemCollapsing]);

    const handleSearch = async (searchValue: string) => {
        if (searchValue.length > 1) {
            const BUBackup = BUTree ? { ...BUTree } : undefined;
            setBUTree(undefined);
            setErrorMessage(undefined);
            const expressions: ViewExpressionRest[] = [];

            if (filterData?.filterValue) {
                expressions.push({
                    property: 'name',
                    value: filterData?.filterValue,
                    expression: ExpressionType.CONTAINS,
                    type: 'STRING',
                });
            }
            expressions.push({
                property: 'name',
                value: searchValue,
                expression: ExpressionType.CONTAINS,
                type: 'STRING',
            });

            // todo add support to search not from root node but from nested filtered node
            triggerSearch({ filterContextRest: { expressions } })
                .unwrap()
                .then((res) => {
                    if (res?.response) {
                        handleAPIResult(res.response);
                    }
                })
                .catch((rej) => {
                    setErrorMessage(rej.error || strings('library:message.errorDuringBUSearch'));
                    if (BUBackup) {
                        setBUTree(BUBackup);
                    }
                });
        } else if (searchValue.length === 0) {
            handleResetTree();
        }
    };

    useEffect(() => {
        document.addEventListener('click', handleContextMenuClose);
        return () => {
            document.removeEventListener('click', handleContextMenuClose);
        };
    });

    useEffect(() => {
        onSelect(selection);
    }, [selection, onSelect]);

    const renderTree = useCallback(
        (nodes: BusinessUnitSimpleRest, level = 0) => {
            if (nodes) {
                return (
                    <StyledTreeItem
                        key={nodes.id}
                        itemId={getStringId(nodes.id)}
                        label={
                            <BusinessUnitNode
                                child={nodes}
                                checked={isItemSelected(nodes.id)}
                                itemExpanding={itemExpanding}
                                multiple={multiple}
                                searchResult={searchResult}
                                searchValue={searchValue}
                                handleSelect={handleSelect}
                                onContextMenu={onContextMenu}
                            />
                        }
                        $level={level}
                        slots={{
                            expandIcon: () => (
                                <DropdownIconWrapper>
                                    <TriangleRight
                                        color={theme.palette.primary.main}
                                        aria-label="triangle-right"
                                        aria-hidden={false}
                                        onClick={(_e) => handleItemExpand(nodes)}
                                    />
                                </DropdownIconWrapper>
                            ),
                            collapseIcon: () => (
                                <DropdownIconWrapper>
                                    <TriangleDown
                                        color={theme.palette.primary.main}
                                        aria-label="triangle-down"
                                        aria-hidden={false}
                                        onClick={(_e) => handleItemCollapse(nodes)}
                                    />
                                </DropdownIconWrapper>
                            ),
                        }}
                    >
                        {Array.isArray(nodes.children) && nodes.children.length !== 0 ? (
                            nodes.children.map((node) => renderTree(node, level + 1))
                        ) : nodes.numberOfChildren ? (
                            <StyledTreeItem
                                itemId={`${nodes.id}-dummy`}
                                $level={level}
                                label={
                                    <Loading
                                        loaderType={LoaderType.Slim}
                                        inline
                                    />
                                }
                            />
                        ) : null}
                    </StyledTreeItem>
                );
            }
        },
        [handleItemCollapse, handleItemExpand, handleSelect, isItemSelected, itemExpanding, multiple, searchResult, searchValue, theme.palette.primary.main],
    );

    return (
        <>
            <Grid
                container
                direction="column"
                sx={{
                    margin: '0 auto',
                    flexWrap: 'nowrap',
                    height: '100%',
                }}
            >
                <Grid
                    item
                    container
                    spacing={2}
                >
                    <Grid
                        item
                        flex="1 1 auto"
                    >
                        <Search
                            searchValue={searchValue}
                            onValueChanged={(event) => {
                                setSearchValue(event.target.value || '');
                                void handleSearch(event.target.value || '');
                            }}
                            autoFocus
                            dataTestId="bu-search"
                            searchPlaceholder={strings('common:placeholder.searchFor')}
                        />
                    </Grid>
                </Grid>
                <BodyGrid item>
                    <TreeView
                        expandedItems={expanded.map((id) => getStringId(id))}
                        multiSelect={multiple}
                        selectedItems={multiple ? selection.map((item) => getStringId(item.id)) : getStringId(selection[0]?.id)}
                        itemChildrenIndentation={0}
                        sx={{ height: '100%' }}
                    >
                        {(isLoadingTree || isSearching || isLoadingBuIds || isLoadingPathToSelected) && <Loading />}
                        {!isLoadingTree && !isSearching && !isLoadingBuIds && !isLoadingPathToSelected && BUTree && renderTree(BUTree)}
                    </TreeView>
                </BodyGrid>
                <Grid
                    item
                    padding="10px 0"
                >
                    <Typography variant="body1">
                        {strings('common:message.showingItems', { currentCount: visibleCount, totalCount })}
                        {selection.length > 0 && ` ${strings('common:message.countSelected', { count: selection.length })}`}
                    </Typography>
                </Grid>
                {errorMessage && (
                    <Grid item>
                        <Typography
                            variant="body1"
                            color="error"
                        >
                            {errorMessage}
                        </Typography>
                    </Grid>
                )}
            </Grid>
            <Menu
                keepMounted
                open={yPos !== 0 && xPos !== 0}
                onClose={handleContextMenuClose}
                anchorReference="anchorPosition"
                anchorPosition={yPos && xPos ? { top: yPos, left: xPos } : undefined}
                onContextMenu={(event: any) => onContextMenuRendered(event)}
                sx={{
                    '& .MuiList-padding': {
                        padding: 0,
                        borderRadius: 0,
                    },
                }}
            >
                {multiple ? (
                    [
                        <MenuItem
                            key="bu_selectChildrenIds"
                            onClick={(_e) => handleSelectChildrenIds()}
                        >
                            <ListItemText primary={strings('ermConstants:label_select_children')} />
                        </MenuItem>,
                        <MenuItem
                            key="bu_deSelectChildrenIds"
                            onClick={(_e) => handleDeselectChildrenIds()}
                        >
                            <ListItemText primary={strings('ermConstants:label_deselect_children')} />
                        </MenuItem>,
                        <MenuItem
                            key="bu_expandChildren"
                            onClick={(_e) => handleExpandChildren()}
                        >
                            <ListItemText primary={strings('ermConstants:label_expand_children')} />
                        </MenuItem>,
                    ]
                ) : (
                    <MenuItem onClick={(_e) => handleExpandChildren()}>
                        <ListItemText primary={strings('ermConstants:label_expand_children')} />
                    </MenuItem>
                )}
            </Menu>
        </>
    );
};

export default BusinessUnitSelector;
