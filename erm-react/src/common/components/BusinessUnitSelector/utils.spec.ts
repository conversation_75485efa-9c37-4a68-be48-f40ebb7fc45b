import { BusinessUnitSimpleRest } from 'api/generated/types';
import { getVisibleCount } from './utils';

const node = {
    id: 1,
    name: 'Node 1',
    children: [
        { id: 2, name: 'Node 2', children: [{ id: 3, name: 'Node 3', children: [] }] },
        {
            id: 4,
            name: 'Node 4',
            children: [
                { id: 5, name: 'Node 5', children: [] },
                { id: 6, name: 'Node 6', children: [] },
            ],
        },
    ],
} as BusinessUnitSimpleRest;

describe('getVisibleCount', () => {
    it('returns 0 when node is undefined', () => {
        expect(getVisibleCount([], undefined)).toEqual(0);
    });
    it('returns 1 when node is not expanded', () => {
        expect(getVisibleCount([], node)).toEqual(1);
    });

    it('returns correct count when node is expanded', () => {
        expect(getVisibleCount([1], node)).toEqual(3);
    });

    it('returns correct count when some children are expanded', () => {
        expect(getVisibleCount([1, 2], node)).toEqual(4);
    });

    it('returns correct count when all nodes are expanded', () => {
        expect(getVisibleCount([1, 2, 3, 4], node)).toEqual(6);
    });
});
