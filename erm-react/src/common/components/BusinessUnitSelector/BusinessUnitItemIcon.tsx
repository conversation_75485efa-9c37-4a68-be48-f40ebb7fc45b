import React from 'react';
import { faSpinner } from '@fortawesome/pro-solid-svg-icons';
import { useTheme } from '@mui/material/styles';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Box from '@mui/material/Box';
import { BusinessUnitSimpleRest } from 'api/generated/types';
import { BusinessUnit } from '@protecht/ui-library/library/components/SVGIcons';

type Props = {
    childId?: number;
    itemExpanding?: BusinessUnitSimpleRest;
};

export const BusinessUnitItemIcon: React.FC<Props> = ({ itemExpanding, childId }) => {
    const theme = useTheme();

    return childId === itemExpanding?.id ? (
        <FontAwesomeIcon
            icon={faSpinner}
            className={'fa-spin'}
            color={theme.palette.protechtGrey?.grey_146}
            width="24px"
        />
    ) : (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <BusinessUnit color={theme.palette.protechtGrey?.grey_146} />
        </Box>
    );
};
