import store from 'store';
import { businessUnitsApi } from 'common/api/businessUnits';
import { BusinessUnitSimpleRest, IdWithNameAndStatusRest } from 'api/generated/types';
import { ObjectStatus } from 'app/types';
import { BusinessUnitFilterData } from './BusinessUnitSelector';

export const shouldExpandSelection = async (tree: BusinessUnitSimpleRest, selectedBUId: number, filterData?: BusinessUnitFilterData) => {
    if (filterData?.id) {
        const response = await store
            .dispatch(businessUnitsApi.endpoints.bursGetAllChildrenIdsUsingGet.initiate({ businessunitId: filterData.id }, { subscribe: false }))
            .unwrap();
        const validBUIds = response.map((item) => item.id);
        validBUIds.unshift(filterData.id);
        return validBUIds.some((id) => id === selectedBUId);
    }
    if (filterData?.filterValue) {
        return filterData?.matchingBusinessunits?.some((id) => id === selectedBUId);
    }

    return tree.id !== selectedBUId && tree.children?.every((child) => child.id !== selectedBUId);
};

export const getActiveBUs = (BUs?: IdWithNameAndStatusRest[]) => BUs?.filter((item) => item.status !== ObjectStatus.Deleted) ?? [];

export const getStringId = (id?: number) => (id ? id.toString() : '');

export const shouldContinueExpanding = (currentNode: BusinessUnitSimpleRest, rootNodeId: number, filterData?: BusinessUnitFilterData) => {
    if (!filterData?.id && currentNode.parentID) {
        return true;
    }

    if (filterData?.id || (filterData?.filterValue && currentNode.id && rootNodeId !== currentNode.id)) {
        return true;
    }

    return false;
};

export const insertInTree = (insert: BusinessUnitSimpleRest, nodeId: number, children?: BusinessUnitSimpleRest[]): BusinessUnitSimpleRest[] => {
    if (insert.id === nodeId) {
        return insert.children ?? [];
    }

    const newChildren = (children ?? []).map((child) => {
        if (child.numberOfChildren !== 0) {
            return { ...child, children: insertInTree(insert, child.id!, child.children) };
        } else {
            return child;
        }
    });

    return newChildren;
};

export const getAllNodesIds = (tree: BusinessUnitSimpleRest[]): number[] => {
    const init: number[] = [];
    return tree.reduce((acc, cur) => {
        acc.push(cur.id!);
        if (cur.children && cur.children.length > 0) {
            acc = [...acc, ...getAllNodesIds(cur.children)];
        }
        return acc;
    }, init);
};

export const getVisibleCount = (expanded: number[], node?: BusinessUnitSimpleRest): number => {
    if (!node) {
        return 0;
    }
    if (!expanded.includes(node.id!)) {
        return 1; // Only count the node itself if it's not expanded
    }
    return 1 + (node.children?.reduce((acc, child) => acc + getVisibleCount(expanded, child), 0) || 0);
};
