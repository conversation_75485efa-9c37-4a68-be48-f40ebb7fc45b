import React from 'react';
import { render, screen } from 'test/utils/rtl';
import { waitFor } from '@testing-library/react';
import BusinessUnitSelector from 'common/components/BusinessUnitSelector';
import { BusinessUnitSimpleRest } from 'api/generated/types';

const mockData = [
    {
        id: 1,
        name: 'parentBU',
        children: [
            {
                id: 2,
                name: 'childBULevel2',
                children: [
                    {
                        id: 3,
                        name: 'childBULevel3',
                        children: undefined,
                    },
                ],
            },
        ],
        numberOfChildren: 2,
    },
];

const mockTriggerGetRootSimple = jest.fn().mockReturnValue({ unwrap: () => Promise.resolve(mockData) });
const mockTriggerGetBuSimple = jest.fn().mockImplementation(({ businessunitId }) => {
    let response: BusinessUnitSimpleRest;
    if (businessunitId === 3) {
        response = {
            id: 3,
            name: 'childBULevel3',
            parentID: 2,
            numberOfChildren: 0,
        };
    }

    if (businessunitId === 2) {
        response = {
            id: 2,
            name: 'childBULevel2',
            numberOfChildren: 1,
            parentID: 1,
            children: [
                {
                    id: 3,
                    name: 'childBULevel3',
                    parentID: 2,
                    numberOfChildren: 0,
                },
            ],
        };
    }

    if (businessunitId === 1) {
        response = {
            id: 1,
            name: 'parentBU',
            numberOfChildren: 1,
            children: [
                {
                    id: 2,
                    name: 'childBULevel2',
                    parentID: 1,
                    numberOfChildren: 1,
                },
            ],
        };
    }
    return {
        unwrap: () => Promise.resolve(response),
    };
});

jest.mock('common/api/businessUnits', () => {
    const actualRtkApi = jest.requireActual('common/api/businessUnits');
    return {
        ...actualRtkApi,
        useLazyBursGetRootSimpleUsingGetQuery: () => [mockTriggerGetRootSimple, { isFetching: false }],
        useLazyBursGetBuSimpleUsingGetQuery: () => [mockTriggerGetBuSimple, { isFetching: false }],
    };
});

describe('<BusinessUnitSelector />', () => {
    it('was rendered with provided data', async () => {
        render(
            <BusinessUnitSelector
                onSelect={jest.fn()}
                selected={[]}
            />,
        );
        //we need to wait for API call
        await waitFor(() => expect(mockTriggerGetRootSimple).toHaveBeenCalled());
        //we need to await state update to display data
        expect(await screen.findByText('parentBU', {}, { timeout: 3000 })).toBeInTheDocument();
        // only top item should be expanded
        expect(screen.getByLabelText('triangle-down')).toBeInTheDocument();
        expect(screen.getByText('childBULevel2')).toBeInTheDocument();
    });

    it('expands path to selected BU on opening BU selector', async () => {
        render(
            <BusinessUnitSelector
                onSelect={jest.fn()}
                selected={[
                    {
                        id: 3,
                        name: 'childBULevel3',
                    },
                ]}
            />,
        );
        await waitFor(() => expect(mockTriggerGetRootSimple).toHaveBeenCalled());
        await waitFor(() => expect(mockTriggerGetBuSimple).toHaveBeenCalled());
        expect(await screen.findByText('parentBU', {}, { timeout: 3000 })).toBeInTheDocument();
        expect(screen.getByText('childBULevel2')).toBeInTheDocument();
        expect(screen.getByText('childBULevel3')).toBeInTheDocument();
    });
});
