import Box from '@mui/material/Box';
import { BusinessUnitSimpleRest } from 'api/generated/types';
import React from 'react';
import { BusinessUnitItemIcon } from './BusinessUnitItemIcon';
import { BusinessUnitHeaderLabel } from './BusinessUnitHeaderLabel';
import LabeledCheckbox from '@protecht/ui-library/library/components/LabeledCheckbox';

type Props = {
    checked: boolean;
    child: BusinessUnitSimpleRest;
    itemExpanding?: BusinessUnitSimpleRest;
    multiple: boolean;
    searchResult: number[];
    searchValue: string;
    handleSelect: (item: BusinessUnitSimpleRest) => void;
    onContextMenu: (e: React.MouseEvent, child: BusinessUnitSimpleRest) => void;
};

const BusinessUnitNode: React.FC<Props> = ({ checked, child, itemExpanding, multiple, searchResult, searchValue, handleSelect, onContextMenu }) => {
    const { name, id, numberOfChildren } = child;

    return (
        <Box
            onContextMenu={(e) => {
                if (numberOfChildren && numberOfChildren > 0) {
                    onContextMenu(e, child);
                } else {
                    e.preventDefault();
                }
            }}
            display="flex"
            alignContent="center"
            height="38px"
            onClick={(_e) => handleSelect(child)}
        >
            {multiple ? (
                <LabeledCheckbox
                    label={
                        <Box
                            display="flex"
                            alignItems="center"
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                        >
                            <BusinessUnitItemIcon
                                childId={child.id}
                                itemExpanding={itemExpanding}
                            />
                            <BusinessUnitHeaderLabel
                                name={name}
                                id={id!}
                                searchResult={searchResult}
                                searchValue={searchValue}
                            />
                        </Box>
                    }
                    checked={checked}
                />
            ) : (
                <Box
                    display="flex"
                    alignItems="center"
                >
                    <BusinessUnitItemIcon
                        childId={child.id}
                        itemExpanding={itemExpanding}
                    />
                    <BusinessUnitHeaderLabel
                        name={name}
                        id={id!}
                        searchResult={searchResult}
                        searchValue={searchValue}
                    />
                </Box>
            )}
        </Box>
    );
};

export default BusinessUnitNode;
