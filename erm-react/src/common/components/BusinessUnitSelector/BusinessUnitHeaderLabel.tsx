import React, { useCallback } from 'react';
import Box from '@mui/material/Box';
import Highlighted from 'common/components/Highlighted';

type Props = {
    name: string;
    id: number;
    searchResult: number[];
    searchValue: string;
};

export const BusinessUnitHeaderLabel: React.FC<Props> = ({ name, id, searchResult, searchValue }) => {
    const isItemSearched = useCallback((id?: number) => searchResult.some((sId) => sId === id), [searchResult]);

    return (
        <Box marginLeft="6px">
            {searchResult && isItemSearched(id) ? (
                <Highlighted
                    text={name}
                    highlight={searchValue}
                    highlightStyling={{
                        backgroundColor: '#e6e208',
                    }}
                ></Highlighted>
            ) : (
                <span>{name || ' '}</span>
            )}
        </Box>
    );
};
