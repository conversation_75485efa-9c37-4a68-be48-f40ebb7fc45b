import React, { useEffect } from 'react';
import useForm from 'common/hooks/forms/useForm';
import Grid from '@mui/material/Grid';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import * as Yup from 'yup';
import { FormProvider } from 'react-hook-form';
import { strings } from 'common/utils/i18n';
import { getReactRoot } from 'config';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { Hyperlink } from 'register/types';

const defaultUrlScheme = 'https://';

const HyperLinkSchema = Yup.object().shape({
    linkName: Yup.string().optional(),
    linkUrl: Yup.string()
        .transform((val) => (val === defaultUrlScheme ? '' : val))
        .url('Invalid URL')
        .required(strings('common:validators.requiredSimple')),
});

type Props = {
    visible: boolean;
    linkForEdit?: Hyperlink | null;
    onSubmit: (data: Hyperlink) => void;
    onClose: () => void;
};

const AddExternalLinkDialog: React.FC<Props> = ({ visible, linkForEdit, onSubmit, onClose }) => {
    const methods = useForm({
        schema: HyperLinkSchema,
        mode: 'onChange',
        defaultValues: {
            linkName: linkForEdit?.linkName || '',
            linkUrl: linkForEdit?.linkUrl || defaultUrlScheme,
        },
    });
    const {
        control,
        reset,
        formState: { isValid, isDirty },
        handleSubmit,
    } = methods;

    useEffect(() => {
        reset({
            linkName: linkForEdit?.linkName || '',
            linkUrl: linkForEdit?.linkUrl || defaultUrlScheme,
        });
    }, [reset, linkForEdit]);

    const handleSave = (data: Hyperlink) => {
        onSubmit(data);
        onClose();
    };

    return (
        <Dialog
            visible={visible}
            title={linkForEdit ? strings('common:title.editExternalLink') : strings('common:title.addExternalLink')}
            width={480}
            dialogContainer={getReactRoot()}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('ermMessages:btn_cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        disabled={!isDirty || !isValid}
                        dataTestId="button-confirm"
                        onClick={handleSubmit(handleSave)}
                    >
                        {linkForEdit ? strings('ermMessages:btn_save') : strings('ermMessages:btn_okay')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                display="flex"
                flexDirection="column"
                spacing={2}
            >
                <FormProvider {...methods}>
                    <Grid item>
                        <InputField
                            control={control}
                            name="linkName"
                            label={strings('common:label.linkName')}
                            placeholder={strings('common:placeholder.linkName')}
                        />
                    </Grid>
                    <Grid item>
                        <InputField
                            control={control}
                            name="linkUrl"
                            label={strings('common:label.linkUrl')}
                            placeholder={strings('common:placeholder.linkUrl')}
                        />
                    </Grid>
                </FormProvider>
            </Grid>
        </Dialog>
    );
};

export default AddExternalLinkDialog;
