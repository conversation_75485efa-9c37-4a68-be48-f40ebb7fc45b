import DialogActions from '@mui/material/DialogActions';
import { AlertType, DialogType } from 'common/types';
import { strings } from 'common/utils/i18n';
import React, { ReactNode, useMemo } from 'react';
import AlertDialog from 'ui/components/AlertDialog';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Box from '@mui/material/Box';

export type ConfirmationAlertProps = {
    visible?: boolean;
    onConfirm?: (params?: any) => void | Promise<void>;
    onClose?: () => void;
    onBack?: () => void;
    icon?: ReactNode;
    title?: string;
    contentText?: string;
    customContent?: ReactNode;
    confirmButtonLabel?: string;
    cancelButtonLabel?: string | null;
    type?: AlertType;
    isCancelPrimary?: boolean;
    dialogType?: DialogType;
};

const ConfirmationAlert = ({
    visible = false,
    onConfirm,
    onClose,
    onBack,
    title = strings('common:title.confirmation'),
    contentText = strings('common:message.unsavedChangesDiscard'),
    icon,
    customContent,
    confirmButtonLabel = strings('ermConstants:yes'),
    cancelButtonLabel = strings('ermConstants:no'),
    type = AlertType.Info,
    isCancelPrimary = false,
    dialogType,
}: ConfirmationAlertProps) => {
    const options = useMemo(
        () => ({
            type,
            title,
            contentText,
            onClose,
            icon,
            customContent,
            actions: (
                <DialogActions>
                    {dialogType === DialogType.UNSAVED && (
                        <Box
                            display="flex"
                            flex={1}
                            justifyContent="space-between"
                        >
                            <Button
                                {...ButtonStyles.dialogButton}
                                sx={{ marginLeft: '67px' }}
                                variant={'secondary'}
                                onClick={onBack}
                                dataTestId="button-back"
                            >
                                {strings('common:button.dontSave')}
                            </Button>
                            <Box
                                display="flex"
                                gap={1.5}
                            >
                                {cancelButtonLabel !== null && (
                                    <Button
                                        {...ButtonStyles.dialogButton}
                                        variant={isCancelPrimary ? 'primary' : 'secondary'}
                                        onClick={onClose}
                                        dataTestId="button-cancel"
                                    >
                                        {cancelButtonLabel}
                                    </Button>
                                )}
                                <Button
                                    {...ButtonStyles.dialogButton}
                                    onClick={onConfirm}
                                    dataTestId="button-confirm"
                                >
                                    {confirmButtonLabel}
                                </Button>
                            </Box>
                        </Box>
                    )}
                    {dialogType === DialogType.DISCARD && (
                        <Box
                            display="flex"
                            gap={1.5}
                        >
                            <Button
                                {...ButtonStyles.dialogButton}
                                variant={isCancelPrimary ? 'primary' : 'secondary'}
                                onClick={onClose}
                                dataTestId="button-cancel"
                            >
                                {cancelButtonLabel || strings('common:button.cancel')}
                            </Button>
                            <Button
                                {...ButtonStyles.dialogButton}
                                variant={isCancelPrimary ? 'secondary' : 'primary'}
                                onClick={onBack}
                                dataTestId="button-discard"
                            >
                                {confirmButtonLabel || strings('common:button.discard')}
                            </Button>
                        </Box>
                    )}
                    {!dialogType && (
                        <Box
                            display="flex"
                            gap={1.5}
                        >
                            {cancelButtonLabel !== null && (
                                <Button
                                    {...ButtonStyles.dialogButton}
                                    variant={isCancelPrimary ? 'primary' : 'secondary'}
                                    onClick={onClose}
                                    dataTestId="button-cancel"
                                >
                                    {cancelButtonLabel}
                                </Button>
                            )}
                            <Button
                                {...ButtonStyles.dialogButton}
                                variant={isCancelPrimary ? 'secondary' : 'primary'}
                                onClick={onConfirm}
                                dataTestId="button-confirm"
                            >
                                {confirmButtonLabel}
                            </Button>
                        </Box>
                    )}
                </DialogActions>
            ),
        }),
        [cancelButtonLabel, confirmButtonLabel, contentText, customContent, icon, onClose, onBack, onConfirm, title, type, dialogType, isCancelPrimary],
    );

    return (
        <AlertDialog
            visible={visible}
            data={options}
        />
    );
};

export default ConfirmationAlert;
