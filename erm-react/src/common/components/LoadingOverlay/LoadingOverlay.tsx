import React from 'react';
import Backdrop from '@mui/material/Backdrop';
import Loading from '../Loading/Loading';

type BackdropProps = {
    open: boolean;
    message?: string;
};

const LoadingOverlay: React.FC<BackdropProps> = ({ open = false, message }: BackdropProps) => {
    return (
        <>
            {' '}
            {open && (
                <Backdrop
                    sx={{
                        zIndex: (theme) => theme.zIndex.drawer + 1,
                        opacity: 0.8,
                    }}
                    open={open}
                >
                    {open && <Loading message={message} />}
                </Backdrop>
            )}
        </>
    );
};

export default LoadingOverlay;
