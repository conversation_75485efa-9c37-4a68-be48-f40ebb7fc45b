import React, { useRef } from 'react';
import Grid from '@mui/material/Grid';

import Tooltip from '@protecht/ui-library/library/components/Tooltip';
import { FieldLabel, FieldTooltip } from '@protecht/ui-library/library/components/FormFields';
import { useIsOverflowing } from '@protecht/ui-library/library/hooks/useIsOverflowing';

type LabelProps = {
    label?: string;
    tooltip?: string;
};

const LabelWithTooltip: React.FC<LabelProps> = ({ label, tooltip }) => {
    const labelRef = useRef(null);
    const isOverflowing = useIsOverflowing(labelRef);

    return (
        <Grid
            container
            flexWrap="nowrap"
            overflow="hidden"
        >
            {label && (
                <Grid
                    item
                    whiteSpace="nowrap"
                    overflow="hidden"
                    data-testid="label"
                    ref={labelRef}
                >
                    <Tooltip
                        title={label}
                        aria-label={label}
                        disableHoverListener={!isOverflowing}
                    >
                        <FieldLabel
                            label={label}
                            labelStyle={{ textOverflow: 'ellipsis', display: 'block', overflow: 'hidden' }}
                        />
                    </Tooltip>
                </Grid>
            )}
            {tooltip && (
                <Grid
                    item
                    data-testid="tooltip"
                >
                    <FieldTooltip title={tooltip} />
                </Grid>
            )}
        </Grid>
    );
};

export default LabelWithTooltip;
