import React from 'react';
import Box from '@mui/material/Box';

interface TabPanelProps {
    children?: React.ReactNode;
    index: any;
    isTabSelected: boolean;
}

const TabContent: React.FC<TabPanelProps> = ({ children, isTabSelected, index }) => {
    return (
        <Box
            role={'tabpanel'}
            hidden={!isTabSelected}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
        >
            {isTabSelected && <Box>{children}</Box>}
        </Box>
    );
};

export default TabContent;
