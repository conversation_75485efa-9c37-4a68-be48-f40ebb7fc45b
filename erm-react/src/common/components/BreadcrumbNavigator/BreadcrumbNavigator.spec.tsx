import React from 'react';

import { <PERSON>rowserRouter } from 'react-router';
import { render, screen } from 'test/utils';
import BreadcrumbNavigator from 'common/components/BreadcrumbNavigator';
import { Breadcrumb } from 'common/components/BreadcrumbNavigator/types';

describe('<BreadcrumbNavigator />', () => {
    const navigationItems: Breadcrumb[] = [
        { label: 'Breadcrumb#1', pathname: '/' },
        { label: 'Breadcrumb#2', pathname: '/' },
        { label: 'Breadcrumb#3', pathname: '/' },
    ];
    const setup = () => {
        return render(
            <BrowserRouter>
                <BreadcrumbNavigator navigationItems={navigationItems} />;
            </BrowserRouter>,
        );
    };

    it('was rendered', () => {
        const view = setup();
        expect(view.container).toBeInTheDocument();
        expect(view.container).toBeVisible();
    });

    it('has rendered all breadcrumbs', () => {
        setup();
        expect(screen.getByText('Breadcrumb#1')).toBeVisible();
        expect(screen.getByText('Breadcrumb#2')).toBeVisible();
        expect(screen.getByText('Breadcrumb#3')).toBeVisible();
    });
});
