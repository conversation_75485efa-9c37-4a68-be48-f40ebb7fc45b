import * as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link, { LinkProps } from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import { Link as RouterLink } from 'react-router';

import { Breadcrumb } from './types';

type BreadcrumbProps = {
    item: Breadcrumb;
};

const StyledLink = styled(Link)<LinkProps & { component?: React.ElementType; to?: string }>(({ theme }) => ({
    textDecoration: 'none',
    // gwt is overriding color of <a> tags
    color: `${theme.palette.protechtGrey?.grey_128} !important`,
    '&:visited, &:hover': {
        color: `${theme.palette.protechtGrey?.grey_128} !important`,
    },
}));

const BreadcrumbItem = ({ item }: BreadcrumbProps) => {
    if (item.disabled) {
        return (
            <Typography
                variant="subtitle2"
                color="protechtGrey.grey_128"
                component="span"
            >
                {item.label}
            </Typography>
        );
    }

    return (
        <StyledLink
            component={RouterLink}
            variant="subtitle2"
            to={item.pathname}
        >
            {item.label}
        </StyledLink>
    );
};

type BreadcrumbNavigatorProps = {
    navigationItems: Breadcrumb[];
};

const BreadcrumbNavigator: React.FC<BreadcrumbNavigatorProps> = (props: BreadcrumbNavigatorProps) => {
    return (
        <Breadcrumbs
            aria-label="navigation"
            separator={
                <StyledDivider
                    orientation="vertical"
                    flexItem
                    margin={0}
                    sx={{ height: '14px', borderColor: 'protechtGrey.grey_128' }}
                />
            }
        >
            {props.navigationItems.map((item) => (
                <BreadcrumbItem
                    key={item.pathname}
                    item={item}
                />
            ))}
        </Breadcrumbs>
    );
};

export default BreadcrumbNavigator;
