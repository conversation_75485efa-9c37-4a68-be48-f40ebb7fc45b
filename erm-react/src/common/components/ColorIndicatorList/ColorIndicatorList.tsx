import React from 'react';
import Grid from '@mui/material/Grid';
import ColorIndicator, { ColorIndicatorProps } from 'common/components/ColorIndicator';

type ColorIndicatorListProps = {
    colors: ColorIndicatorProps[];
    max?: number;
};

export const ColorIndicatorList: React.FC<ColorIndicatorListProps> = ({ colors, max }) => {
    const visibleColors = max ? colors.slice(0, max) : colors;

    return (
        <Grid
            container
            display="flex"
        >
            {visibleColors.map((color, index) => (
                <ColorIndicator
                    value={color.value}
                    key={`color-${index}`}
                    mr="3px"
                    my="1.5px"
                />
            ))}
        </Grid>
    );
};

export default ColorIndicatorList;
