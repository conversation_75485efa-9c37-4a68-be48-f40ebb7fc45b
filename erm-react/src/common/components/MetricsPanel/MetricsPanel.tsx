import React, { FC, useLayoutEffect, useMemo, useRef } from 'react';
import Grid from '@mui/material/Grid';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { ChevronLeft, ChevronRight } from '@protecht/ui-library/library/components/SVGIcons';
import MetricCard, { STANDARD_METRIC_CARD_WIDTH, WIDE_METRIC_CARD_WIDTH } from 'common/components/MetricCard';
import useTheme from '@mui/system/useTheme';
import { MetricRestResponse, MetricsWidth } from 'metrics/types';

const CARD_SPACING = 16;
const NAVIGATION_BUTTONS_WIDTH = 64;

export interface MetricsPanelProps {
    metrics?: MetricRestResponse[];
    isLoading: boolean;
    error?: string;
    onMetricCardClick?: (metricData: MetricRestResponse) => void;
    title: string;
    getEvaluation: (metric: MetricRestResponse) => MetricRestResponse | undefined;
}

export const METRIC_SEARCH = '?metricId=';

const MetricsPanel: FC<MetricsPanelProps> = ({ metrics, isLoading, error, onMetricCardClick, title, getEvaluation }) => {
    const theme = useTheme();

    const panelRef = useRef<HTMLDivElement>(null);

    const [currentIndex, setCurrentIndex] = React.useState(0);
    const [visibleCardsCount, setVisibleCardsCount] = React.useState(0);
    const [isOverflowing, setIsOverflowing] = React.useState(false);

    const metricsCount = useMemo(() => metrics?.length ?? 0, [metrics]);
    const isFirst = useMemo(() => currentIndex === 0, [currentIndex]);

    const metricSize = useMemo(
        () =>
            metrics?.reduce(
                (s, m) => {
                    m.metric.styleModel.size === MetricsWidth.SIZE1 ? (s.standard += 1) : (s.wide += 1);
                    return s;
                },
                { standard: 0, wide: 0 },
            ),
        [metrics],
    );
    useLayoutEffect(() => {
        if (panelRef.current && metricSize && metrics) {
            // content width is equal to the sum of all cards width + spaces between them
            const standardMetricCardWidth = metricSize?.standard * STANDARD_METRIC_CARD_WIDTH + metricSize?.standard * CARD_SPACING;
            const wideMetricCardWidth = metricSize?.wide * WIDE_METRIC_CARD_WIDTH + metricSize?.wide * CARD_SPACING;
            const isOverflowing = standardMetricCardWidth + wideMetricCardWidth > panelRef.current.clientWidth;
            setIsOverflowing(isOverflowing);

            let visibleCardsCount = isOverflowing ? 0 : metrics.length;

            // if the content is overflowing, we need to calculate the number of visible cards
            if (isOverflowing) {
                visibleCardsCount = metrics.reduce(
                    (acc, metric) => {
                        const cardWidth = metric.metric.styleModel.size === MetricsWidth.SIZE1 ? STANDARD_METRIC_CARD_WIDTH : WIDE_METRIC_CARD_WIDTH;
                        const containerWidth = panelRef.current!.clientWidth - NAVIGATION_BUTTONS_WIDTH - CARD_SPACING;

                        if (containerWidth < acc.currentCardsWidth + cardWidth + CARD_SPACING) {
                            return { ...acc };
                        }

                        return {
                            ...acc,
                            visibleCards: acc.visibleCards + 1,
                            currentCardsWidth: acc.currentCardsWidth + cardWidth + CARD_SPACING,
                        };
                    },
                    { visibleCards: 0, currentCardsWidth: 0 },
                ).visibleCards;
            }

            setVisibleCardsCount(visibleCardsCount);
        }
    }, [metricsCount, metricSize, metrics]);

    const content = useMemo(() => {
        if (isLoading) {
            return (
                <Grid item>
                    <CircularProgress size={20} />
                </Grid>
            );
        }

        if (error) {
            return <Typography color="error">{error}</Typography>;
        }

        return (
            <Grid
                item
                container
                direction="row"
                justifyContent={isOverflowing ? 'space-between' : 'flex-start'}
                alignItems="center"
                gap={`${CARD_SPACING}px`}
                wrap="nowrap"
            >
                {isOverflowing && !isFirst && (
                    <IconButton
                        color="secondary"
                        onClick={() => setCurrentIndex((previous) => previous - visibleCardsCount)}
                    >
                        <ChevronLeft color={theme.palette.primary.main} />
                    </IconButton>
                )}
                <Grid
                    item
                    container
                    gap={`${CARD_SPACING}px`}
                    wrap="nowrap"
                >
                    {metrics
                        ?.filter((_, index) => index >= currentIndex && index < currentIndex + visibleCardsCount)
                        .map((metricData) => {
                            return (
                                <Grid
                                    item
                                    key={metricData.metric.id}
                                >
                                    <MetricCard
                                        evaluation={getEvaluation(metricData)}
                                        metricName={metricData.metric.name}
                                        metricStyling={metricData.metric.styleModel}
                                        onClick={onMetricCardClick ? () => onMetricCardClick(metricData) : undefined}
                                    />
                                </Grid>
                            );
                        })}
                </Grid>
                {isOverflowing && !(currentIndex + visibleCardsCount >= metricsCount) && (
                    <IconButton
                        color="secondary"
                        onClick={() => setCurrentIndex((previous) => previous + visibleCardsCount)}
                    >
                        <ChevronRight color={theme.palette.primary.main} />
                    </IconButton>
                )}
            </Grid>
        );
    }, [isLoading, error, isOverflowing, isFirst, metrics, theme, currentIndex, visibleCardsCount, metricsCount, onMetricCardClick, getEvaluation]);

    return metrics?.length ? (
        <Grid
            ref={panelRef}
            item
            container
            gap="10px"
            direction="column"
            sx={{ margin: '5px 0 20px 0' }}
        >
            <Grid item>
                <Typography
                    color="protechtGrey.textPrimary"
                    variant="body2"
                >
                    {title}
                </Typography>
            </Grid>

            {content}
        </Grid>
    ) : null;
};

export default MetricsPanel;
