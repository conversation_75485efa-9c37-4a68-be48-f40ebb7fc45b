import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

type DescriptionBoxProps = {
    children: React.ReactNode;
};

const DescriptionBox: React.FC<DescriptionBoxProps> = ({ children }) => {
    return (
        <Box
            paddingX="10px"
            paddingY="6px"
            border="1px solid"
            borderColor="protechtGrey.grey_231"
        >
            <Typography>{children}</Typography>
        </Box>
    );
};

export default DescriptionBox;
