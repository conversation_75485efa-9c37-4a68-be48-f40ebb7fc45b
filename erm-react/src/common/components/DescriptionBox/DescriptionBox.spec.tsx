import React from 'react';
import { render, screen } from 'test/utils';
import DescriptionBox from './DescriptionBox';
import '@testing-library/jest-dom';

describe('DescriptionBox', () => {
    it('renders correctly', () => {
        const testMessage = 'This is a test description';
        const { container } = render(<DescriptionBox>{testMessage}</DescriptionBox>);

        expect(screen.getByText(testMessage)).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
