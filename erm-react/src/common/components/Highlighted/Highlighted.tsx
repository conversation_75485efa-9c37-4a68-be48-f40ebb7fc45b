import React from 'react';
import { CSSProperties } from '@emotion/serialize';
import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import useTheme from '@mui/system/useTheme';

type Props = {
    text: string;
    highlight: string | undefined;
    highlightStyling?: CSSProperties;
};

const StyledSpan = styled('span', {
    shouldForwardProp: (prop) => prop !== 'highlightStyling',
})<{ highlightStyling: CSSProperties }>(({ highlightStyling }: any) => ({
    ...highlightStyling,
}));

const Highlighted = ({ text = '', highlight = '', highlightStyling = undefined }: Props): JSX.Element => {
    const theme = useTheme();
    highlight = highlight.trim();
    if (!highlightStyling) {
        highlightStyling = {
            color: theme.palette.primary.main,
        };
    }

    if (!highlight) {
        return <Box component={'span'}>{text}</Box>;
    }

    const regex = new RegExp(`(${highlight})`, 'gi');
    const parts = text.split(regex);

    return (
        <span>
            {parts.filter(String).map((part, i) => {
                return regex.test(part) ? (
                    <StyledSpan
                        highlightStyling={highlightStyling!}
                        key={i}
                    >
                        {part}
                    </StyledSpan>
                ) : (
                    <span key={i}>{part}</span>
                );
            })}
        </span>
    );
};

export default Highlighted;
