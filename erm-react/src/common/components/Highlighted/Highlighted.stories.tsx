import Highlighted from './Highlighted';
import { StoryFn, Meta } from '@storybook/react';
import React from 'react';

export default {
    title: 'components/UI/Highlighted text',
    component: Highlighted,
    parameters: {
        docs: {
            description: {
                component: 'Highlight component is used to highlight part of displayed string, used in search scenarios, e.g. in suggestions input',
            },
        },
    },
} as Meta<typeof Highlighted>;

const Template: StoryFn<typeof Highlighted> = (args) => <Highlighted {...args} />;

export const Default: StoryFn<typeof Highlighted> = Template.bind({});
Default.args = {
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    highlight: 'ipsum dolor',
};

export const Custom: StoryFn<typeof Highlighted> = Template.bind({});
Custom.args = {
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    highlight: 'ipsum dolor',
    highlightStyling: { color: 'white', backgroundColor: '#b720bd', textDecoration: 'italic' },
};

export const NothingToHighlight: StoryFn<typeof Highlighted> = Template.bind({});
NothingToHighlight.args = {
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    highlight: 'Protecht',
};
