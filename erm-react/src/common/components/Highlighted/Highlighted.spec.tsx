import React from 'react';
import { render, screen } from 'test/utils';

import Highlighted from 'common/components/Highlighted';

describe('<Highlighted />', () => {
    it('was rendered with default styling', () => {
        const randomText = 'Friday is my favourite day of the week';
        const textToHighlight = 'friday';

        render(
            <Highlighted
                text={randomText}
                highlight={textToHighlight}
            />,
        );
        const highlightedPart = screen.getByText(/Friday/i);
        expect(highlightedPart).toBeInTheDocument();
        expect(screen.getByText(/is my favourite day of the week/i)).toBeInTheDocument();
        const styles = getComputedStyle(highlightedPart);
        expect(styles.color).not.toBe('');
    });

    it('was rendered with custom styling', () => {
        const randomText = 'Friday is my favourite day of the week';
        const textToHighlight = 'favourite';
        const defaultHighlightStyling = {
            backgroundColor: 'red',
            textDecoration: 'italic',
        };

        render(
            <Highlighted
                text={randomText}
                highlight={textToHighlight}
                highlightStyling={defaultHighlightStyling}
            />,
        );
        expect(screen.getByText(/Friday is my/i)).toBeInTheDocument();
        const highlightedPart = screen.getByText(/favourite/i);
        expect(highlightedPart).toBeInTheDocument();
        expect(screen.getByText(/day of the week/i)).toBeInTheDocument();
        const styles = getComputedStyle(highlightedPart);
        expect(styles.backgroundColor).toBe('red');
        expect(styles.textDecoration).toBe('italic');
    });

    it('was rendered without any highlight', () => {
        const randomText = 'Friday is my favourite day of the week';
        const textToHighlight = '';

        render(
            <Highlighted
                text={randomText}
                highlight={textToHighlight}
            />,
        );
        expect(screen.getByText(/Friday is my favourite day of the week/i)).toBeInTheDocument();
    });
});
