import React, { FC, useCallback, useState } from 'react';
import { RepositoryNodeBase } from 'rolesAndPermissions/types';
import Box from '@mui/material/Box';
import Loading from '../Loading';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import ContentLayout from 'common/layouts/ContentLayout';
import ToolbarContainer from '../ToolbarSpacing/ToolbarContainer';
import ReportSelector from 'vendorRiskManagement/components/VendorRiskManagement/VrmReporting/ReportSelector';

export interface WorkspaceReportingProps {
    dashboardOptions?: RepositoryNodeBase[];
    isLoading?: boolean;
    translationPrefix: string;
}

const WorkspaceReporting: FC<WorkspaceReportingProps> = ({ dashboardOptions, isLoading, translationPrefix }) => {
    const [selectedNode, setSelectedNode] = useState<RepositoryNodeBase>();

    const renderContent = useCallback(() => {
        if (isLoading) {
            return (
                <Box sx={{ display: 'flex', flex: 1, alignItems: 'center', justifyContent: 'center' }}>
                    <Loading inline />
                </Box>
            );
        }

        if (dashboardOptions?.length === 0) {
            return (
                <Typography
                    variant="body2"
                    style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        justifyContent: 'space-around',
                        alignItems: 'center',
                    }}
                >
                    {strings(`${translationPrefix}:reports.noReports`)}
                </Typography>
            );
        }

        if (selectedNode) {
            return (
                <iframe
                    src={`${ProtechtDictionary.siteUrl}/worms/client/app/Reports?op=vs&path=${selectedNode.path}&delivery=direct`}
                    style={{
                        border: 'none',
                        display: 'flex',
                        flex: 1,
                    }}
                    width="100%"
                    height="100%"
                    title={strings(`${translationPrefix}:reports.iframeTitle`)}
                />
            );
        }

        return null;
    }, [dashboardOptions?.length, isLoading, selectedNode]);

    return (
        <ContentLayout
            toolbar={
                <ToolbarContainer>
                    <Typography
                        variant="body2"
                        sx={{ flexGrow: 1 }}
                    >
                        {strings(`${translationPrefix}:reports.title`)}
                    </Typography>
                    {!isLoading && dashboardOptions?.length !== 0 && (
                        <ReportSelector
                            dataTestId={'report-selector-dropdown'}
                            key="selectReport"
                            dashboards={dashboardOptions || []}
                            onDashboardSelected={setSelectedNode}
                        />
                    )}
                </ToolbarContainer>
            }
        >
            {renderContent()}
        </ContentLayout>
    );
};

export default WorkspaceReporting;
