import React, { useEffect, useState, useRef } from 'react';
import { IdWithNameAndStatusRest } from 'app/types';

interface Props {
    visible: boolean;
    onClose: () => {
        /*do nothing*/
    };
    onSave: (selected: IdWithNameAndStatusRest[]) => {
        /*do nothing*/
    };
    selected: IdWithNameAndStatusRest[];
    data: IdWithNameAndStatusRest[];
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const MobileSelector: React.FC<Props> = ({ visible = false, onClose, onSave, selected = [], data = [] }: Props) => {
    const saveButtonRef = useRef<any>();
    const [currentSelection, setCurrentSelection] = useState(selected);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [searchValue, setSearchValue] = useState<string>('');

    useEffect(() => {
        const saveButtonComponent = saveButtonRef?.current?.cmp;

        saveButtonComponent?.el?.on('tap', save);

        return () => {
            //remove listeners
            saveButtonComponent?.el?.clearListeners();
        };
    }, []);

    useEffect(() => {
        const saveButtonComponent = saveButtonRef?.current?.cmp;
        saveButtonComponent?.el?.clearListeners();
        saveButtonComponent?.el?.on('tap', save);
    }, [currentSelection]);

    useEffect(() => {
        // store.filterBy((record) => record?.data?.name?.includes(searchValue));
    }, [searchValue]);

    const save = () => {
        onSave(currentSelection);
    };

    // const store = Ext.create('Ext.data.Store', {
    //     data: data
    // });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const onSelect = ({ sender, selected }) => {
        setCurrentSelection([selected.data]);
    };

    return (
        <div>mobile selector</div>
        // <Dialog
        //     displayed={visible}
        //     cls='selector'
        //     header={{
        //         style: {
        //             backgroundColor: 'white'
        //         },
        //         titleAlign: 'left'
        //     }}
        //     title={{
        //         text: 'Select items',
        //         cls: 'title'
        //     }}
        //     centered
        //     width='90%'
        //     height='90%'
        // >
        //     <Toolbar shadow={false} docked="top" layout='fit'>
        //         <Searchfield
        //             placeholder="Search"
        //             onChange={({sender, newValue, oldValue}) => setSearchValue(newValue)}
        //         />
        //     </Toolbar>
        //     <List
        //         // if height not set list is not scrollable
        //         height='100%'
        //         itemTpl="{name}"
        //         store={store}
        //         onSelect={onSelect}
        //         selection={store.getById(currentSelection[0]?.id)}
        //     />

        //     <Toolbar shadow={false} docked="bottom" layout={{type: 'hbox', align: 'center', pack: 'space-between'}}>
        //         <Button text="Cancel" onTap={onClose} width={120} style={{fontWeight: 'bold'}}/>
        //         <Button ref={saveButtonRef} text="Save" ui='action' width={120} style={{fontWeight: 'bold'}}/>
        //     </Toolbar>
        // </Dialog>
    );
};

export default MobileSelector;
