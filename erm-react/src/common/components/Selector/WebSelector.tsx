import React, { useEffect, useMemo, useState } from 'react';
import { DataGridColDef, SelectorType } from 'common/types';
import { IdOnly, IdWithName } from 'app/types';
import { ColumnType } from 'register/types';
import Tag from 'library/components/Tag';
import BusinessUnitSelector from 'common/components/BusinessUnitSelector';
import RegisterEntriesSelector from 'common/components/RegisterEntriesSelector';
import { NodeType } from 'bowtie/types';
import ControlLayout from 'library/components/Control/ControlLayout';
import RiskCauseLayout from 'library/components/RiskCause/RiskCauseLayout';
import RiskEventLayout from 'library/components/RiskEvent/RiskEventLayout';
import Box from '@mui/material/Box';
import DialogContentLayout from 'common/layouts/DialogContentLayout';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { IdWithNameRest, ViewExpressionRest } from 'api/generated/types';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { strings } from 'common/utils/i18n';
import { getReactRoot } from 'config';
import useMediaQuery from '@mui/material/useMediaQuery';
import UserLayout, { UserFilterData } from 'library/components/User/UserLayout';
import CountryLayout from 'library/components/Country/CountryLayout';
import StateLayout from 'library/components/State/StateLayout';
import KriLayout from 'library/components/Kri/KriLayout';
import AuditQuestionLayout from 'library/components/AuditQuestion/AuditQuestionLayout';
import BowtieLayout from 'library/components/Bowtie/BowtieLayout';
import RoleLayout from 'library/components/Role/RoleLayout';
import ComplianceQuestionLayout from 'library/components/ComplianceQuestion/ComplianceQuestionLayout';

export type SelectableItem = IdOnly | IdWithNameRest;

type Props<T extends SelectableItem> = {
    title: string;
    visible: boolean;
    onClose: () => void;
    onSubmit: (selected: T[] | null) => void;
    multiselect?: boolean;
    persistViewState?: boolean;
    selected: T[];
    columnDefinition?: DataGridColDef[];
    onDataLoad?: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController: AbortController) => Promise<any>;
    type: ColumnType | SelectorType | NodeType;
    filterData?: any;
    onCreateNew?: () => void;
    onDataClear?: () => void;
};

const WebSelector = <T extends SelectableItem>({
    visible = false,
    multiselect = false,
    persistViewState = true,
    selected = [],
    onCreateNew,
    onDataClear,
    ...otherProps
}: Props<T>): React.ReactElement => {
    const [currentSelection, setCurrentSelection] = useState<T[]>(selected);

    useEffect(() => {
        if (selected.length !== currentSelection.length) {
            setCurrentSelection(selected);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selected.length]);

    const [isLoading, setIsLoading] = useState(true);
    const onSubmit = async (): Promise<void> => {
        otherProps.onSubmit?.(currentSelection);
        onDataClear?.();
    };

    const onSelect = (data) => {
        setCurrentSelection(data);
    };

    const isSmallerScreen = useMediaQuery('(max-width:800px)');

    const dialogWidth = useMemo(() => {
        switch (otherProps.type) {
            case ColumnType.BUSINESS_UNIT:
            case SelectorType.BUSINESS_UNIT:
                return isSmallerScreen ? 342 : 600;
            case ColumnType.TABLE:
            case ColumnType.ACTIONS:
            case ColumnType.COUNTRY:
            case ColumnType.STATE:
                return 720;
            default:
                return undefined;
        }
    }, [otherProps.type, isSmallerScreen]);

    const dialogHeight = useMemo(() => {
        switch (otherProps.type) {
            case ColumnType.BUSINESS_UNIT:
            case SelectorType.BUSINESS_UNIT:
                return 574;
            default:
                return 764;
        }
    }, [otherProps.type]);

    const selector = useMemo(() => {
        const transformToIdOnlyArray = (items: T[]): IdOnly[] => {
            return items.map((item) => ({ id: item.id })).filter((item) => item.id !== undefined) as IdOnly[];
        };
        switch (otherProps.type) {
            case ColumnType.BUSINESS_UNIT:
            case SelectorType.BUSINESS_UNIT:
                return (
                    // TODO: remove workaround once selectors are refactored
                    <Box
                        flex={1}
                        p="24px"
                        pt="21px"
                        width={'100%'}
                    >
                        <BusinessUnitSelector
                            multiple={multiselect}
                            selected={currentSelection}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                            filterData={otherProps.filterData}
                        />
                    </Box>
                );
            case ColumnType.USER:
            case SelectorType.USER:
                return (
                    <UserLayout
                        selected={currentSelection}
                        onSelect={onSelect}
                        onLoadingChange={setIsLoading}
                        filterData={otherProps.filterData as UserFilterData}
                        multiselect={multiselect}
                    />
                );
            case ColumnType.TABLE:
            case ColumnType.CENTRAL_LIBRARY:
                return (
                    <RegisterEntriesSelector
                        registerId={otherProps.filterData.registerId as number}
                        excludedIds={otherProps.filterData.excludedIds as number[]}
                        additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                        context={otherProps.filterData.context}
                        viewsActionsDisabled={otherProps.filterData.viewsActionsDisabled}
                        multiselect={multiselect}
                        onSelect={onSelect}
                        selected={currentSelection}
                        onLoadingChange={setIsLoading}
                        onCreateNew={onCreateNew}
                        layoutSx={{ paddingTop: '20px' }}
                    />
                );
            case ColumnType.ACTIONS:
                return (
                    <RegisterEntriesSelector
                        registerId={otherProps.filterData.registerId as number}
                        excludedIds={otherProps.filterData.excludedIds as number[]}
                        additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                        context={otherProps.filterData.context}
                        viewsActionsDisabled={otherProps.filterData.viewsActionsDisabled}
                        multiselect={multiselect}
                        onSelect={onSelect}
                        selected={currentSelection}
                        onLoadingChange={setIsLoading}
                        onCreateNew={onCreateNew}
                    />
                );
            case ColumnType.COUNTRY:
                return (
                    <CountryLayout
                        selected={currentSelection as IdWithName[]}
                        onSelect={onSelect}
                        onLoadingChange={setIsLoading}
                    />
                );
            case ColumnType.STATE:
                return (
                    <StateLayout
                        selected={currentSelection as IdWithName[]}
                        onSelect={onSelect}
                        onLoadingChange={setIsLoading}
                        filterData={otherProps.filterData}
                    />
                );
            case ColumnType.CONTROL:
            case SelectorType.CONTROL:
            case NodeType.Control:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <ControlLayout
                            additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                            isSelector
                            multiselect={multiselect}
                            persistViewState={persistViewState}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                        />
                    </Box>
                );
            case ColumnType.RISK_CAUSE:
            case SelectorType.RISK_CAUSE:
            case NodeType.Cause:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <RiskCauseLayout
                            additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                            isSelector
                            multiselect={multiselect}
                            persistViewState={persistViewState}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                        />
                    </Box>
                );
            case ColumnType.RISK_EVENT:
            case SelectorType.RISK_EVENT:
            case NodeType.MainRiskEvent:
            case NodeType.RiskEvent:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <RiskEventLayout
                            additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                            isSelector
                            multiselect={multiselect}
                            persistViewState={persistViewState}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                        />
                    </Box>
                );
            case ColumnType.TAGS:
            case SelectorType.TAGS:
                return (
                    <Tag
                        onSelect={onSelect}
                        selected={transformToIdOnlyArray(currentSelection)}
                        isSelector
                        tagCategories={otherProps.filterData}
                        onLoadingChange={setIsLoading}
                    />
                );
            case SelectorType.TAGS_AND_CATEGORIES:
                return (
                    <Tag
                        onSelect={onSelect}
                        selected={transformToIdOnlyArray(currentSelection)}
                        isSelector
                        tagCategories={otherProps.filterData}
                        onLoadingChange={setIsLoading}
                    />
                );
            case SelectorType.ROLE:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <RoleLayout
                            multiselect={multiselect}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                            isSelector
                        />
                    </Box>
                );
            case SelectorType.KRI:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <KriLayout
                            additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                            isSelector
                            multiselect={multiselect}
                            persistViewState={persistViewState}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                        />
                    </Box>
                );
            case SelectorType.AUDIT_QUESTION:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <AuditQuestionLayout
                            additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                            isSelector
                            multiselect={multiselect}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={(data) => onSelect(data.map((item) => ({ ...item, name: item['question'] })))}
                            onLoadingChange={setIsLoading}
                        />
                    </Box>
                );
            case SelectorType.BOWTIE:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <BowtieLayout
                            additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                            isSelector
                            multiselect={multiselect}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                        />
                    </Box>
                );
            case ColumnType.QUESTION:
                return (
                    <Box
                        p="20px 0"
                        width="100%"
                    >
                        <ComplianceQuestionLayout
                            additionalExpressions={otherProps.filterData?.additionalExpressions as ViewExpressionRest[]}
                            isSelector
                            multiselect={multiselect}
                            selected={transformToIdOnlyArray(currentSelection)}
                            onSelect={onSelect}
                            onLoadingChange={setIsLoading}
                        />
                    </Box>
                );
            default:
                return <div>No content</div>;
        }
    }, [otherProps.type, otherProps.filterData, multiselect, currentSelection, onCreateNew, persistViewState]);

    return (
        <Dialog
            height={dialogHeight}
            width={dialogWidth}
            visible={visible}
            onOutsideClickClose={otherProps.onClose}
            title={otherProps.title}
            dialogContainer={getReactRoot()}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={otherProps.onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!currentSelection.length || isLoading}
                        onClick={onSubmit}
                    >
                        {strings('ermMessages:btn_okay')}
                    </Button>
                </DialogActions>
            }
        >
            <DialogContentLayout>{selector}</DialogContentLayout>
        </Dialog>
    );
};

export default WebSelector;
