import React from 'react';

import WebSelector, { SelectableItem } from './WebSelector';

import { DataGridColDef, SelectorType } from 'common/types';
import { ColumnType } from 'register/types';
import { NodeType } from 'bowtie/types';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { ViewExpressionRest } from 'api/generated/types';

type Props<T> = {
    title: string;
    visible: boolean;
    onClose: () => void;
    onSubmit: (selected: T[] | null) => void;
    onDataLoad?: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController: AbortController, registerId?: string) => Promise<any>;
    multiselect?: boolean;
    persistViewState?: boolean;
    selected: T[];
    columnDefinition?: DataGridColDef[];
    type: ColumnType | SelectorType | NodeType;
    filterData?: any;
    onCreateNew?: () => void;
    onDataClear?: () => void;
};

// TODO: refactor
const Selector = <T extends SelectableItem>(props: Props<T>): React.ReactElement => {
    return <WebSelector {...props} />;
};

export default Selector;
