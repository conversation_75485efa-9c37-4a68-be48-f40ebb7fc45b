import { DataGridColDef, SYSTEM_COLUMN } from '../../types';
import { strings } from '../../utils/i18n';
import { FilterType } from '../../../view/types';
import { MIN_TABLE_COLUMN_WIDTH } from '../../constants';

export const REGISTER_SELECTOR_DEFAULT_FIELD = 'label';

export const RegisterSettingsColDef: DataGridColDef[] = [
    {
        field: SYSTEM_COLUMN.ID,
        headerName: strings('ermConstants:id'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        hidden: true,
        filterable: false,
        groupable: false,
    },
    {
        field: 'label',
        headerName: strings('common:label.registerName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        groupable: false,
    },
    {
        field: 'applicationName',
        headerName: strings('resilience:label.applicationName'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        groupable: false,
    },
    {
        field: 'registerType',
        headerName: strings('resilience:label.registerType'),
        filterType: FilterType.STRING,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        groupable: false,
    },
    {
        field: 'entriesCount',
        headerName: strings('resilience:label.entriesCount'),
        filterType: FilterType.NUMBER,
        minWidth: MIN_TABLE_COLUMN_WIDTH,
        flex: 1,
        filterable: false,
        sortable: false,
        groupable: false,
    },
];
