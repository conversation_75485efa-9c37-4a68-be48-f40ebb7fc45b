import React, { useMemo, useState } from 'react';
import { DataGridColDef, SYSTEM_COLUMN } from 'common/types';
import Grid from '@mui/material/Grid';
import SearchByField from 'common/components/SearchByField';
import { RegisterRest } from 'register/types';
import { getApiFields, getSearchFields } from 'common/utils/definitions';
import { mapParamsFields } from 'common/utils/mappings';
import { LibraryDisplayChoice } from 'resilience/types';
import { useTheme } from '@mui/material/styles';
import useSearchExpression from 'common/hooks/useSearchExpression';
import { REGISTER_SELECTOR_DEFAULT_FIELD } from './constants';
import Search from '@protecht/ui-library/library/components/Inputs/Search';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { Table } from '@protecht/ui-library/library/components/Table';
import { strings } from 'common/utils/i18n';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { ExpressionType } from 'view/types';
import { useTmrsGetRegistersUsingPostQuery } from 'register/rtkApi';
import { ViewExpressionRest } from 'api/generated/types';
import SearchByFieldToolbarGroup from '../SearchByField/SearchByFieldToolbarGroup';

type Props = {
    title?: string;
    libraryDisplayChoice?: LibraryDisplayChoice;
    columns: DataGridColDef[];
    selected?: RegisterRest[];
    excludedIds?: number[];
    onClose: () => void;
    onSelect: (selected: RegisterRest[]) => void;
    setOpen?: (boolean) => void;
    multiSelect?: boolean;
    contractTypes?: number[];
    staticExpressions?: ViewExpressionRest[];
    prependElements?: React.ReactNode;
};

const RegisterSelector: React.FC<Props> = ({
    title,
    libraryDisplayChoice = LibraryDisplayChoice.LIBRARIES_INCLUDED,
    columns = [],
    selected = [],
    excludedIds = [],
    onClose,
    onSelect,
    setOpen,
    multiSelect,
    contractTypes,
    staticExpressions,
    prependElements,
}: Props) => {
    const theme = useTheme();
    const SEARCH_FIELDS = getSearchFields(columns);
    const API_FIELDS = getApiFields(columns);

    const [currentSelection, setCurrentSelection] = useState<RegisterRest[]>(selected);

    const [selectedSearchField, setSelectedSearchField] = useState<string | undefined>(REGISTER_SELECTOR_DEFAULT_FIELD);
    const [searchValue, setSearchValue] = useState<string>('');
    const searchExpression = useSearchExpression(searchValue, selectedSearchField);

    const [params, setParams] = useState<SearchRequestParams | undefined>(undefined);

    const mappedParams: SearchRequestParams = useMemo(() => {
        if (params) {
            return mapParamsFields(params, API_FIELDS);
        }
        return {};
    }, [params, API_FIELDS]);

    const expressions = useMemo(() => {
        const _expressions: ViewExpressionRest[] = [];
        if (staticExpressions) {
            _expressions.push(...staticExpressions);
        }
        if (searchExpression) {
            _expressions.push({ ...searchExpression, property: API_FIELDS[searchExpression.property!] });
        }

        if (excludedIds) {
            _expressions.push({
                id: 0,
                expression: ExpressionType.NOT_IN,
                property: SYSTEM_COLUMN.ID,
                type: 'STRING',
                value: excludedIds.join(';'),
            });
        }

        return _expressions;
    }, [staticExpressions, searchExpression, excludedIds]);

    const { data: response, isFetching } = useTmrsGetRegistersUsingPostQuery({
        ...mappedParams,
        contractTypes: contractTypes,
        libraryDisplayChoice: libraryDisplayChoice,
        filterContextRest: {
            expressions,
        },
    });

    const confirmSelection = () => {
        onSelect(currentSelection);
        setOpen && setOpen(false);
    };

    return (
        <Dialog
            visible={true}
            width={900}
            height={674}
            onOutsideClickClose={onClose}
            title={title || strings('resilience:title.registerSelector')}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!currentSelection || currentSelection.length <= 0 || isFetching}
                        onClick={confirmSelection}
                    >
                        {strings('ermConstants:button_label_select')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                spacing={1}
                sx={{ height: '100%' }}
            >
                {prependElements}
                <Grid
                    item
                    xs={12}
                >
                    {SEARCH_FIELDS.length === 1 ? (
                        <Search
                            key="register-selector-search-by"
                            aria-label="register-selector-search-by"
                            dataTestId={'register-selector-field-search'}
                            searchValue={searchValue}
                            onValueChanged={(event) => {
                                setSearchValue(event.target.value);
                            }}
                            searchPlaceholder={strings('common:placeholder.search')}
                        />
                    ) : (
                        <SearchByFieldToolbarGroup maxWidth="100%">
                            <SearchByField
                                dataTestId={'register-selector-field-search'}
                                key="register-selector-search-by"
                                aria-label="register-selector-search-by"
                                fields={SEARCH_FIELDS}
                                searchField={selectedSearchField || REGISTER_SELECTOR_DEFAULT_FIELD}
                                searchValue={searchValue}
                                onPropertyChanged={(property) => {
                                    setSelectedSearchField(property);
                                }}
                                onValueChanged={(value) => {
                                    setSearchValue(value);
                                }}
                            />
                        </SearchByFieldToolbarGroup>
                    )}
                </Grid>
                <Grid
                    item
                    xs={12}
                    sx={{ height: 'calc(100% - ' + theme.spacing(4) + ')' }}
                >
                    <Table
                        columns={columns}
                        rows={(response?.records as RegisterRest[]) ?? []}
                        totalCount={response?.totalCount || 0}
                        loading={isFetching}
                        params={params}
                        onParamsChanged={setParams}
                        multiselect={multiSelect}
                        selected={currentSelection}
                        onSelect={(selection: RegisterRest[]) => {
                            setCurrentSelection(selection);
                        }}
                    />
                </Grid>
            </Grid>
        </Dialog>
    );
};

export default RegisterSelector;
