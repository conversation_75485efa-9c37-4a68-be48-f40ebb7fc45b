import React, { FC, useCallback, useMemo, useRef, useState } from 'react';
import { strings } from 'common/utils/i18n';
import { getReactRoot } from 'config';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { TagTable } from 'library/types';
import { FormProvider } from 'react-hook-form';
import Grid from '@mui/material/Grid';
import { TagTypeRest } from 'api/generated/types';
import useForm from 'common/hooks/forms/useForm';
import Typography from '@mui/material/Typography';
import useTheme from '@mui/system/useTheme';
import useSnackbar from 'common/hooks/useSnackbar';
import { useTrsCreateTagTypeUsingPostMutation, useTrsCreateTagUsingPostMutation } from 'library/rtkApi';
import { formatTag } from 'library/components/Tag/TagPicker/utils';
import Tag<PERSON>ameField from 'library/components/Tag/TagPicker/TagNameField';
import TagCategoryField from 'library/components/Tag/TagPicker/TagCategoryField';
import { TagSchema } from 'library/components/Tag/TagPicker/CreateTagForm';

type Props = {
    open: boolean;
    onClose: () => void;
    onSuccess: (newTag: TagTable) => void;
};

const AddTagDialog: FC<Props> = (props: Props) => {
    const { open, onClose, onSuccess } = props;

    const theme = useTheme();
    const { enqueueError } = useSnackbar();

    const [createTag] = useTrsCreateTagUsingPostMutation();
    const [createTagType] = useTrsCreateTagTypeUsingPostMutation();

    const [categoryRecognized, setCategoryRecognized] = useState<TagTypeRest | undefined>(undefined);

    const formContext = useRef<{ tagName: 'tagAlreadyExists' | undefined }>({ tagName: undefined });

    const methods = useForm({
        schema: TagSchema,
        mode: 'onChange',
        context: formContext.current,
        defaultValues: {
            tagName: '',
            tagCategory: '',
        },
    });

    const {
        formState: { isValid, isDirty },
        handleSubmit,
    } = methods;

    const handleCreateNewTag = useCallback(
        async (data: { tagName: string; tagCategory: string }) => {
            try {
                let tagType = categoryRecognized;

                if (!categoryRecognized) {
                    tagType = await createTagType({ tagTypeRest: { name: data.tagCategory } }).unwrap();
                }

                const newTag = await createTag({
                    tagRest: {
                        value: data.tagName,
                        type: tagType,
                    },
                }).unwrap();

                onSuccess(formatTag(newTag));
            } catch (e) {
                if (e.status === 403) {
                    enqueueError(strings('common:message.tagPermissions'));
                } else {
                    enqueueError(strings('common:message.addTagError'));
                }
            }
        },
        [categoryRecognized, createTag, createTagType, enqueueError, onSuccess],
    );

    const infoMessage = useMemo(() => {
        return (
            <Grid>
                <Typography
                    variant="body2"
                    color={theme.palette.protechtGrey?.grey_128}
                >
                    {categoryRecognized ? strings('common:message.newTagCreated') : strings('common:message.newTagAndCategoryCreated')}
                </Typography>
            </Grid>
        );
    }, [categoryRecognized, theme.palette.protechtGrey?.grey_128]);

    return (
        <Dialog
            width={480}
            height={355}
            visible={open}
            title={strings('common:title.addTag')}
            dialogContainer={getReactRoot()}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!isDirty || !isValid}
                        onClick={handleSubmit(handleCreateNewTag)}
                    >
                        {strings('ermMessages:btn_okay')}
                    </Button>
                </DialogActions>
            }
        >
            <Grid
                container
                item
                width="100%"
                display="flex"
                flexDirection="column"
                gap="20px"
            >
                <FormProvider {...methods}>
                    <Grid item>
                        <TagCategoryField
                            onCategoryRecognized={setCategoryRecognized}
                            displayResultsInPopper={true}
                        />
                    </Grid>
                    <Grid item>
                        <TagNameField
                            formContext={formContext}
                            selectedCategory={categoryRecognized}
                            fieldLabel={strings('common:label.tag')}
                        />
                    </Grid>
                </FormProvider>
                {isDirty && isValid && infoMessage}
            </Grid>
        </Dialog>
    );
};

export default AddTagDialog;
