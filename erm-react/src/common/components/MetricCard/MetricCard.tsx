import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { MetricRestResponse, MetricsStyling, MetricsWidth } from 'metrics/types';
import React, { RefObject, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { defaultStyledOptions } from 'common/utils/cssUtils';

export const WIDE_METRIC_CARD_WIDTH = 178;
export const STANDARD_METRIC_CARD_WIDTH = 148;

const DEFAULT_FONT_SIZE = 32;
const FONT_SIZE_RECALCULATION_FACTOR = 0.6;

const StyledCard = styled(
    Box,
    defaultStyledOptions,
)<{ $isClickable: boolean; $metricSize?: MetricsWidth }>(({ theme, $isClickable, $metricSize }) => ({
    height: '126px',
    width: `${$metricSize === MetricsWidth.SIZE1 ? STANDARD_METRIC_CARD_WIDTH : WIDE_METRIC_CARD_WIDTH}px`,
    border: '1px solid ' + theme.palette.protechtGrey?.grey_238,
    borderRadius: 0,
    padding: '12px',
    gap: '2px',
    boxShadow: 'none',
    ...($isClickable && {
        cursor: 'pointer',
    }),
}));

const SimpleEvalContainer = styled(Box)(() => ({
    height: '50px',
    gap: '10px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
}));

const MetricNameContainer = styled(Box)(() => ({
    height: '55px',
    gap: '7px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
}));

type MetricCardProps = {
    metricName: string;
    evaluation?: MetricRestResponse;
    onClick?: (() => void) | undefined;
    metricStyling?: MetricsStyling;
};

const MetricCard: React.FC<MetricCardProps> = ({ onClick, evaluation, metricName, metricStyling }: MetricCardProps) => {
    const evalContainerRef: RefObject<HTMLDivElement> = useRef(null);
    const [fontSize, setFontSize] = useState(DEFAULT_FONT_SIZE);
    const [textOverflow, setTextOverflow] = useState(false);

    useEffect(() => {
        if (evalContainerRef.current) {
            const initialScrollWidth = evalContainerRef.current.scrollWidth;

            const calculateFontSize = (initialSize) => {
                const newSize =
                    initialSize * (1 - FONT_SIZE_RECALCULATION_FACTOR * ((initialScrollWidth - evalContainerRef.current!.clientWidth) / initialScrollWidth));
                return Math.max(19, Math.min(32, Math.round(newSize)));
            };

            const checkOverflow = (size) => {
                evalContainerRef.current!.style.fontSize = `${size}px`;
                return evalContainerRef.current!.scrollWidth > evalContainerRef.current!.clientWidth;
            };

            let currentSize = calculateFontSize(DEFAULT_FONT_SIZE);
            if (checkOverflow(currentSize)) {
                currentSize = calculateFontSize(currentSize);
            }

            setFontSize(currentSize);
            setTextOverflow(evalContainerRef.current.scrollWidth > evalContainerRef.current.clientWidth);
        }
    }, [metricName, evaluation]);

    const evalString = useMemo(() => {
        const evalResult = evaluation?.eval;
        if (!evalResult || evaluation?.evaluationError) {
            return '-';
        } else if (evalResult.formattedEval) {
            return evalResult.formattedEval;
        } else if (evalResult.simpleEval) {
            if (evalResult.symbolBefore) {
                return evalResult.symbol + ' ' + evalResult.simpleEval;
            }
            return evalResult.simpleEval + ' ' + evalResult.symbol;
        } else {
            return '';
        }
    }, [evaluation]);

    const isMetricEvalError = useMemo(() => evaluation?.evaluationError || evalString === '-', [evalString, evaluation?.evaluationError]);

    const handleOnClick = useCallback(() => {
        if (onClick && !isMetricEvalError) {
            onClick();
        }
    }, [isMetricEvalError, onClick]);

    // Temporary color fix for greyIfEmpty and "-" color
    const metricTitleColor = useMemo(() => {
        const evalResult = evaluation?.eval;

        if (metricStyling?.greyIfEmpty && evaluation?.eval?.simpleEval === '0') {
            return 'protechtGrey.grey_112';
        }
        if (!evalResult || evaluation?.evaluationError || (!evalResult?.formattedEval && !evalResult?.simpleEval)) {
            return 'protechtGrey.grey_178';
        }
    }, [evaluation, metricStyling?.greyIfEmpty]);

    return (
        <StyledCard
            onClick={handleOnClick}
            $isClickable={!isMetricEvalError && onClick !== undefined}
            $metricSize={metricStyling?.size}
        >
            <SimpleEvalContainer ref={evalContainerRef}>
                <Typography
                    color={metricTitleColor ?? evaluation?.applyStyle?.color ?? 'protechtGrey.textPrimary'}
                    fontWeight={600}
                    style={{
                        fontSize: `${fontSize}px`,
                        overflow: textOverflow ? 'hidden' : 'visible',
                        textOverflow: textOverflow ? 'ellipsis' : 'clip',
                        whiteSpace: textOverflow ? 'nowrap' : 'normal',
                        width: '100%',
                    }}
                    align="center"
                >
                    {evalString}
                </Typography>
            </SimpleEvalContainer>
            <MetricNameContainer>
                <Typography
                    color="protechtGrey.textPrimary"
                    variant="h6"
                    align="center"
                    sx={{ lineHeight: '19.5px', wordBreak: 'break-word', overflow: 'hidden' }}
                >
                    {metricName}
                </Typography>
            </MetricNameContainer>
        </StyledCard>
    );
};

export default MetricCard;
