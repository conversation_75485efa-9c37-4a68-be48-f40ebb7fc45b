import React, { FC, useMemo } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import LinearProgress from '@protecht/ui-library/library/components/LinearProgress';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faExclamationCircle } from '@fortawesome/pro-regular-svg-icons';
import useTheme from '@mui/system/useTheme';
import { IconDefinition } from '@fortawesome/fontawesome-common-types';
import { mainPalette } from '@protecht/ui-library/library/theme/colors/colors';
import { strings } from 'common/utils/i18n';
import { ProgressType, ProgressDialogProps } from './ProgressDialog';

type ProgressBarProps = Pick<ProgressDialogProps, 'progressVariant' | 'progressType' | 'totalCount' | 'processedCount' | 'processFinished' | 'infoMessage'> & {
    validationMessagesCount?: number;
};

const ProgressBar: FC<ProgressBarProps> = (props) => {
    const { infoMessage, totalCount, processedCount, progressVariant, progressType, processFinished, validationMessagesCount = 0 } = props;
    const theme = useTheme();

    const isFinished = useMemo(() => {
        if (progressType === ProgressType.Export && totalCount && processedCount) {
            return totalCount === processedCount;
        }
        if (progressType === ProgressType.Import || progressType === ProgressType.VERIFICATION) {
            return processFinished;
        }
    }, [progressType, totalCount, processedCount, processFinished]);

    const progressValue = useMemo(() => {
        if (totalCount && processedCount) {
            return Math.ceil((100 / totalCount) * processedCount);
        }
    }, [totalCount, processedCount]);

    const createCompletedMessage = (icon: IconDefinition, color: string, message: string) => (
        <Typography
            variant="body2"
            display="flex"
            alignItems="center"
        >
            <FontAwesomeIcon
                icon={icon}
                color={color}
                style={{ margin: '0 4px' }}
            />
            {message}
        </Typography>
    );

    const completedMessage = useMemo(() => {
        if (progressType === ProgressType.Import) {
            return !validationMessagesCount
                ? createCompletedMessage(faCheckCircle, mainPalette.success.dark, strings('register:progressDialog.allDataImported'))
                : createCompletedMessage(
                      faExclamationCircle,
                      theme.palette.error.main,
                      strings('register:progressDialog.errors', { errorsCount: validationMessagesCount }),
                  );
        }

        if (progressType === ProgressType.VERIFICATION) {
            return !validationMessagesCount
                ? createCompletedMessage(faCheckCircle, mainPalette.success.dark, strings('register:progressDialog.allDataVerified'))
                : createCompletedMessage(
                      faExclamationCircle,
                      theme.palette.error.main,
                      strings('register:progressDialog.errors', { errorsCount: validationMessagesCount }),
                  );
        }
    }, [progressType, validationMessagesCount, theme.palette.error.main]);

    const progressMessage = useMemo(() => {
        let baseMsg = `${processedCount} ${strings('common:of')} ${totalCount}`;

        if (isFinished && completedMessage) {
            baseMsg += ' -';
        }

        return baseMsg;
    }, [processedCount, totalCount, isFinished, completedMessage]);

    return (
        <Box>
            {infoMessage && (
                <Typography
                    variant="body2"
                    sx={{ mb: 2 }}
                >
                    {infoMessage}
                </Typography>
            )}

            <Box sx={{ width: '100%' }}>
                <LinearProgress
                    variant={progressVariant}
                    value={progressValue}
                />
            </Box>

            {totalCount !== undefined && processedCount !== undefined && (
                <Box
                    marginTop={2}
                    marginBottom={0}
                    alignItems="center"
                    display="flex"
                >
                    <Typography
                        variant="body1"
                        sx={{ mb: 0, color: theme.palette.protechtGrey.grey_128 }}
                    >
                        {progressMessage}
                    </Typography>
                    {isFinished && completedMessage}
                </Box>
            )}
        </Box>
    );
};

export default ProgressBar;
