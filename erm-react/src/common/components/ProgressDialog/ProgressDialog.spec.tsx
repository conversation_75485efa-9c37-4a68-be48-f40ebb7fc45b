import React from 'react';
import { render } from 'test/utils';
import ProgressDialog, { ProgressType } from './ProgressDialog';
import { ProcessingError, ProcessingErrorType } from 'vendorRiskManagement/types';

describe('ProgressDialog', () => {
    // Suppress specific DOM nesting warnings for these tests
    beforeEach(() => {
        jest.spyOn(console, 'error').mockImplementation((message) => {
            if (message && message.includes('validateDOMNesting')) {
                return;
            }
            // eslint-disable-next-line no-console
            console.warn(message);
        });
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });
    it('renders correctly with minimal props', () => {
        const { baseElement } = render(
            <ProgressDialog
                visible={true}
                title="Test Progress"
                progressType={ProgressType.Import}
                progressVariant="indeterminate"
                onClose={() => {}}
            />
        );
        expect(baseElement).toMatchSnapshot();
    });

    it('renders correctly with all props', () => {
        const mockValidationMessages: ProcessingError[] = [
            {
                row: 1,
                fieldName: 'Test Field',
                fieldValue: 'Test Value',
                errorMessage: 'Test Error Message',
                errorDetails: 'Test Error Details',
                type: ProcessingErrorType.ERROR,
            }
        ];

        const { baseElement } = render(
            <ProgressDialog
                visible={true}
                title="Test Progress"
                progressType={ProgressType.Import}
                progressVariant="determinate"
                onClose={() => {}}
                infoMessage="Test Info Message"
                totalCount={10}
                processedCount={5}
                noDialogActions={false}
                processFinished={false}
                validationMessages={mockValidationMessages}
            />
        );
        expect(baseElement).toMatchSnapshot();
    });

    it('renders correctly when finished', () => {
        const { baseElement } = render(
            <ProgressDialog
                visible={true}
                title="Test Progress"
                progressType={ProgressType.Import}
                progressVariant="determinate"
                onClose={() => {}}
                totalCount={10}
                processedCount={10}
                processFinished={true}
            />
        );
        expect(baseElement).toMatchSnapshot();
    });

    it('renders correctly with export type', () => {
        const { baseElement } = render(
            <ProgressDialog
                visible={true}
                title="Test Progress"
                progressType={ProgressType.Export}
                progressVariant="determinate"
                onClose={() => {}}
                totalCount={10}
                processedCount={5}
            />
        );
        expect(baseElement).toMatchSnapshot();
    });

    it('renders correctly with verification type', () => {
        const { baseElement } = render(
            <ProgressDialog
                visible={true}
                title="Test Progress"
                progressType={ProgressType.VERIFICATION}
                progressVariant="determinate"
                onClose={() => {}}
                totalCount={10}
                processedCount={5}
                processFinished={false}
            />
        );
        expect(baseElement).toMatchSnapshot();
    });
});