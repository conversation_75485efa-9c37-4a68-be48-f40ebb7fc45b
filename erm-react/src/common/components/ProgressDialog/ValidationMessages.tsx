import React, { FC } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationCircle } from '@fortawesome/pro-regular-svg-icons';
import useTheme from '@mui/system/useTheme';
import { ProcessingError } from 'vendorRiskManagement/types';
import { strings } from 'common/utils/i18n';

type ValidationMessagesProps = {
    validationMessages: ProcessingError[];
};

const ValidationMessages: FC<ValidationMessagesProps> = ({ validationMessages }) => {
    const theme = useTheme();

    if (!validationMessages) {
        return null;
    }

    return (
        <>
            <Typography
                variant="body2"
                sx={{ mb: 1, mt: 2 }}
            >
                {strings('register:progressDialog.errorsOrIssues')}
            </Typography>
            <Box
                height="126px"
                border={`1px solid ${theme.palette.protechtGrey.grey_220}`}
                padding={1}
                overflow="auto"
            >
                {validationMessages.map((msg, index) => (
                    <Typography
                        variant="body1"
                        key={index}
                        display="flex"
                    >
                        <FontAwesomeIcon
                            icon={faExclamationCircle}
                            color={theme.palette.error.main}
                            style={{ marginRight: '8px', paddingTop: '4px' }}
                        />
                        <Box>
                            <Box marginBottom="10px">{msg.errorMessage}</Box>
                            <Box
                                marginBottom="10px"
                                display="flex"
                                gap="3px"
                            >
                                <Box fontStyle={{ color: theme.palette.protechtGrey.grey_128 }}>{strings('register:progressDialog.row')}</Box>
                                {msg.row}
                                <Box fontStyle={{ color: theme.palette.protechtGrey.grey_128 }}>{strings('register:progressDialog.field')}</Box>
                                {msg.fieldName}
                                <Box fontStyle={{ color: theme.palette.protechtGrey.grey_128 }}>{strings('register:progressDialog.value')}</Box>
                                {msg.fieldValue}
                            </Box>
                        </Box>
                    </Typography>
                ))}
            </Box>
        </>
    );
};

export default ValidationMessages;
