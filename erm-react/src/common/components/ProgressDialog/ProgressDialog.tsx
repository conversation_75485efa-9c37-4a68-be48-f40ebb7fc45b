import React, { FC, ReactElement, useCallback } from 'react';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { strings } from 'common/utils/i18n';
import Grid from '@mui/material/Grid';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { ProcessingError } from 'vendorRiskManagement/types';
import ProgressBar from './ProgressBar';
import ValidationMessages from './ValidationMessages';
import { jsPDF } from '../../../../src/common/diagram/utils/jsPDF/jspdf.es';
import { renderToStaticMarkup } from 'react-dom/server';
import { DateTime } from 'luxon';
import useTheme from '@mui/system/useTheme';

export enum ProgressType {
    Import = 'IMPORT',
    Export = 'EXPORT',
    VERIFICATION = 'VERIFICATION',
}

export type ProgressDialogProps = {
    visible: boolean;
    title: string;
    progressType: ProgressType;
    progressVariant: 'determinate' | 'indeterminate' | 'buffer' | 'query';
    onClose: () => void;
    infoMessage?: string;
    totalCount?: number;
    processedCount?: number;
    noDialogActions?: boolean;
    processFinished?: boolean;
    validationMessages?: ProcessingError[];
    entityName?: string;
    failure?: string;
};

const PROGRESS_DIALOG_WIDTH = 684;

const ProgressDialog: FC<ProgressDialogProps> = (props) => {
    const {
        visible,
        title,
        infoMessage,
        totalCount,
        processedCount,
        progressVariant,
        noDialogActions,
        validationMessages,
        progressType,
        processFinished,
        onClose,
        entityName,
        failure,
    } = props;

    const isFinished =
        progressType === ProgressType.Export && totalCount && processedCount
            ? totalCount === processedCount
            : (progressType === ProgressType.Import || progressType === ProgressType.VERIFICATION) && processFinished;

    const theme = useTheme();

    /**
     * Renders validation errors as HTML for PDF export
     */
    const renderValidationErrors = useCallback(
        (errors: ProcessingError[] | string[], entityName?: string): ReactElement => {
            return (
                <div style={{ fontFamily: 'sans-serif', lineHeight: '150%', width: '100%' }}>
                    <>
                        <div style={{ fontSize: '20px', lineHeight: '150%', fontStyle: 'normal', fontWeight: 700, marginBottom: '16px' }}>
                            {strings('common:validation.errorLog')} {entityName || ''}
                        </div>
                        <div style={{ fontSize: '16px', lineHeight: '150%', fontStyle: 'normal', fontWeight: 400, marginBottom: '16px' }}>
                            {strings('common:validation.logCreatedDate') + DateTime.now().toFormat('yyyy-MM-dd HH:mm:ss')}
                        </div>
                        <div
                            style={{
                                color: theme.palette.error.main,
                                fontSize: '14px',
                                fontStyle: 'normal',
                                fontWeight: 700,
                                marginBottom: '15px',
                            }}
                        >
                            {strings('common:validation.errorsFound')}
                        </div>
                    </>

                    <div
                        style={{
                            backgroundColor: '#f5f5f5',
                            width: '775px',
                            paddingTop: '20px',
                            paddingBottom: '20px',
                        }}
                    >
                        {errors.map((error, index) => (
                            <Grid
                                container
                                key={`error-${index}`}
                            >
                                <Grid
                                    item
                                    style={{ width: '100%', marginLeft: '10px', marginRight: '10px' }}
                                >
                                    <span style={{ color: '#666666' }}>{strings('register:progressDialog.row')}</span>
                                    <span>{error.row}</span>
                                    <div style={{ color: theme.palette.error.main, marginBottom: '8px' }}>
                                        {typeof error === 'string' ? error : error.errorMessage || error.message}
                                    </div>
                                    {typeof error !== 'string' && error.row !== undefined && (
                                        <div style={{ marginBottom: '12px', fontSize: '13px', display: 'flex', gap: '8px' }}>
                                            <span style={{ color: '#666666' }}>{strings('register:progressDialog.row')}</span>
                                            <span>{error.row}</span>
                                            {error.fieldName && (
                                                <>
                                                    <span style={{ color: '#666666' }}>{strings('register:progressDialog.field')}</span>
                                                    <span>{error.fieldName}</span>
                                                </>
                                            )}
                                            {error.fieldValue && (
                                                <>
                                                    <span style={{ color: '#666666' }}>{strings('register:progressDialog.value')}</span>
                                                    <span>{error.fieldValue}</span>
                                                </>
                                            )}
                                        </div>
                                    )}
                                </Grid>
                            </Grid>
                        ))}
                    </div>
                </div>
            );
        },
        [theme.palette.error.main],
    );

    /**
     * Downloads validation errors as PDF
     */
    const onDownload = useCallback(async () => {
        if (!validationMessages?.length) {
            return;
        }

        // Create PDF document
        const doc = new jsPDF({
            orientation: 'p',
            unit: 'px',
            format: 'A4',
        } as any) as any;

        doc.setFontSize(14);
        const field = document.createElement('div');

        // Render validation errors as HTML
        const staticElement = renderToStaticMarkup(renderValidationErrors(validationMessages, entityName));

        field.innerHTML = staticElement;

        // Convert HTML to PDF
        await doc.html(field, {
            margin: [10, 10, 10, 10],
            callback: (doc) => doc,
            width: doc.internal.pageSize.getWidth(),
            height: doc.internal.pageSize.getHeight(),
            windowWidth: 810,
            windowHeight: 0,
            y: 0,
            autoPaging: 'text',
        });

        // Save PDF
        const fileName = entityName ? `${entityName}_validation_errors.pdf` : `validation_errors_${DateTime.now().toFormat('yyyyMMdd_HHmmss')}.pdf`;

        doc.save(fileName);
    }, [validationMessages, entityName]);

    return (
        <Dialog
            visible={visible}
            title={title}
            width={PROGRESS_DIALOG_WIDTH}
            height={ProgressType.Export === progressType ? 240 : 'auto'}
            dialogActions={
                noDialogActions ? (
                    <></>
                ) : (
                    <DialogActions sx={{ justifyContent: isFinished ? (validationMessages?.length ? 'space-between' : 'flex-end') : 'flex-start' }}>
                        {isFinished ? (
                            <>
                                {validationMessages?.length && (
                                    <Button
                                        {...ButtonStyles.dialogButton}
                                        variant={'secondary'}
                                        sx={{ ...ButtonStyles.dialogButton.sx, border: 'none' }}
                                        onClick={onDownload}
                                        dataTestId="button-download"
                                    >
                                        {strings('common:button.downloadErrors')}
                                    </Button>
                                )}

                                <Button
                                    {...ButtonStyles.dialogButton}
                                    variant={'secondary'}
                                    onClick={onClose}
                                    dataTestId="button-confirm"
                                >
                                    {strings('ermMessages:btn_okay')}
                                </Button>
                            </>
                        ) : (
                            <Button
                                {...ButtonStyles.dialogButton}
                                variant={'secondary'}
                                onClick={onClose}
                                dataTestId="button-cancel"
                            >
                                {strings('common:button.cancel')}
                            </Button>
                        )}
                    </DialogActions>
                )
            }
        >
            <Grid sx={{ width: '100%' }}>
                <ProgressBar
                    infoMessage={infoMessage}
                    totalCount={totalCount}
                    processedCount={processedCount}
                    progressVariant={progressVariant}
                    progressType={progressType}
                    processFinished={processFinished}
                    validationMessagesCount={(validationMessages?.length || failure?.length) ?? 0}
                />
                {validationMessages && <ValidationMessages validationMessages={validationMessages} />}
            </Grid>
        </Dialog>
    );
};

export default ProgressDialog;
