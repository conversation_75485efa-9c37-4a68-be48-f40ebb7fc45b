// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProgressDialog renders correctly when finished 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-1ksdxuj-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Test Progress"
    id="draggable-dialog-Test Progress"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Test Progress
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiGrid-root css-1k1ys70-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <div
                  class="MuiBox-root css-8atqhb"
                >
                  <span
                    aria-valuemax="100"
                    aria-valuemin="0"
                    aria-valuenow="100"
                    class="MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-determinate css-1yci2do-MuiLinearProgress-root"
                    role="progressbar"
                  >
                    <span
                      class="MuiLinearProgress-bar MuiLinearProgress-barColorPrimary MuiLinearProgress-bar1Determinate css-g4b68k-MuiLinearProgress-bar1"
                      style="transform: translateX(0%);"
                    />
                  </span>
                </div>
                <div
                  class="MuiBox-root css-1ot1tx4"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-rwcp1t-MuiTypography-root"
                  >
                    10 of 10 -
                  </p>
                  <p
                    class="MuiTypography-root MuiTypography-body2 css-tjrq0d-MuiTypography-root"
                  >
                    <svg
                      aria-hidden="true"
                      class="svg-inline--fa fa-circle-check "
                      color="#007E00"
                      data-icon="circle-check"
                      data-prefix="far"
                      focusable="false"
                      role="img"
                      style="margin: 0px 4px;"
                      viewBox="0 0 512 512"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0L369 209z"
                        fill="currentColor"
                      />
                    </svg>
                    All data imported
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-1ctmqla-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-confirm"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                OK
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;

exports[`ProgressDialog renders correctly with all props 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-1ksdxuj-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Test Progress"
    id="draggable-dialog-Test Progress"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Test Progress
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiGrid-root css-1k1ys70-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <p
                  class="MuiTypography-root MuiTypography-body2 css-uf8ssf-MuiTypography-root"
                >
                  Test Info Message
                </p>
                <div
                  class="MuiBox-root css-8atqhb"
                >
                  <span
                    aria-valuemax="100"
                    aria-valuemin="0"
                    aria-valuenow="50"
                    class="MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-determinate css-1yci2do-MuiLinearProgress-root"
                    role="progressbar"
                  >
                    <span
                      class="MuiLinearProgress-bar MuiLinearProgress-barColorPrimary MuiLinearProgress-bar1Determinate css-g4b68k-MuiLinearProgress-bar1"
                      style="transform: translateX(-50%);"
                    />
                  </span>
                </div>
                <div
                  class="MuiBox-root css-1ot1tx4"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-rwcp1t-MuiTypography-root"
                  >
                    5 of 10
                  </p>
                </div>
              </div>
              <p
                class="MuiTypography-root MuiTypography-body2 css-1dokung-MuiTypography-root"
              >
                Errors or issues
              </p>
              <div
                class="MuiBox-root css-1b13n0g"
              >
                <p
                  class="MuiTypography-root MuiTypography-body1 css-bz7fk3-MuiTypography-root"
                >
                  <svg
                    aria-hidden="true"
                    class="svg-inline--fa fa-circle-exclamation "
                    color="#DB2121"
                    data-icon="circle-exclamation"
                    data-prefix="far"
                    focusable="false"
                    role="img"
                    style="margin-right: 8px; padding-top: 4px;"
                    viewBox="0 0 512 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c-13.3 0-24 10.7-24 24l0 112c0 13.3 10.7 24 24 24s24-10.7 24-24l0-112c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"
                      fill="currentColor"
                    />
                  </svg>
                  <div
                    class="MuiBox-root css-0"
                  >
                    <div
                      class="MuiBox-root css-1r2f04i"
                    >
                      Test Error Message
                    </div>
                    <div
                      class="MuiBox-root css-emi0cg"
                    >
                      <div
                        class="MuiBox-root css-bcthhi"
                      >
                        CSV row: 
                      </div>
                      1
                      <div
                        class="MuiBox-root css-bcthhi"
                      >
                        Field: 
                      </div>
                      Test Field
                      <div
                        class="MuiBox-root css-bcthhi"
                      >
                        Value: 
                      </div>
                      Test Value
                    </div>
                  </div>
                </p>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-1f0j4cn-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-cancel"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Cancel
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;

exports[`ProgressDialog renders correctly with export type 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-nxxmii-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Test Progress"
    id="draggable-dialog-Test Progress"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Test Progress
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiGrid-root css-1k1ys70-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <div
                  class="MuiBox-root css-8atqhb"
                >
                  <span
                    aria-valuemax="100"
                    aria-valuemin="0"
                    aria-valuenow="50"
                    class="MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-determinate css-1yci2do-MuiLinearProgress-root"
                    role="progressbar"
                  >
                    <span
                      class="MuiLinearProgress-bar MuiLinearProgress-barColorPrimary MuiLinearProgress-bar1Determinate css-g4b68k-MuiLinearProgress-bar1"
                      style="transform: translateX(-50%);"
                    />
                  </span>
                </div>
                <div
                  class="MuiBox-root css-1ot1tx4"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-rwcp1t-MuiTypography-root"
                  >
                    5 of 10
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-1f0j4cn-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-cancel"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Cancel
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;

exports[`ProgressDialog renders correctly with minimal props 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-1ksdxuj-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Test Progress"
    id="draggable-dialog-Test Progress"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Test Progress
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiGrid-root css-1k1ys70-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <div
                  class="MuiBox-root css-8atqhb"
                >
                  <span
                    class="MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-indeterminate css-1yci2do-MuiLinearProgress-root"
                    role="progressbar"
                  >
                    <span
                      class="MuiLinearProgress-bar MuiLinearProgress-barColorPrimary MuiLinearProgress-bar1Indeterminate css-6ojac4-MuiLinearProgress-bar1"
                    />
                    <span
                      class="MuiLinearProgress-bar MuiLinearProgress-barColorPrimary MuiLinearProgress-bar2Indeterminate css-yicy1h-MuiLinearProgress-bar2"
                    />
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-1f0j4cn-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-cancel"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Cancel
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;

exports[`ProgressDialog renders correctly with verification type 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-1ksdxuj-MuiModal-root-MuiDialog-root"
    data-testid="dialog-Test Progress"
    id="draggable-dialog-Test Progress"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: none; transition: none;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-1sep8xo-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: none; transition: none;"
      tabindex="-1"
    >
      <div
        aria-describedby="dialog-description"
        aria-labelledby="dialog-title"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthFalse react-draggable css-xldtrb-MuiPaper-root-MuiDialog-paper"
        role="dialog"
        style="transform: translate(0px,0px);"
      >
        <div
          class="css-1obh2c1"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h2
              class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-13afjt4-MuiTypography-root-MuiDialogTitle-root"
              id="dialog-title"
            >
              Test Progress
            </h2>
          </div>
          <div
            class="MuiDialogContent-root css-ypiqx9-MuiDialogContent-root"
            id="dialog-description"
          >
            <div
              class="MuiGrid-root css-1k1ys70-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <div
                  class="MuiBox-root css-8atqhb"
                >
                  <span
                    aria-valuemax="100"
                    aria-valuemin="0"
                    aria-valuenow="50"
                    class="MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-determinate css-1yci2do-MuiLinearProgress-root"
                    role="progressbar"
                  >
                    <span
                      class="MuiLinearProgress-bar MuiLinearProgress-barColorPrimary MuiLinearProgress-bar1Determinate css-g4b68k-MuiLinearProgress-bar1"
                      style="transform: translateX(-50%);"
                    />
                  </span>
                </div>
                <div
                  class="MuiBox-root css-1ot1tx4"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-rwcp1t-MuiTypography-root"
                  >
                    5 of 10
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiDialogActions-root MuiDialogActions-spacing css-1f0j4cn-MuiDialogActions-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeLarge MuiButton-outlinedSizeLarge MuiButton-colorPrimary MuiButton-disableElevation css-1yvbxk4-MuiButtonBase-root-MuiButton-root"
              data-testid="button-cancel"
              tabindex="0"
              type="button"
            >
              <span
                class="css-1d0doyg"
              >
                Cancel
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;
