import React, { PropsWithChildren, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { UserPermission } from 'user/types';
import { useNavigate } from 'react-router';
import { PermissionRest } from 'api/generated/types';
import { usersApi } from 'user/rtkApi';

export type Props = PropsWithChildren<{
    roles?: string[];
    permissions?: Partial<UserPermission>[];
    redirectUrl?: string;
    ignoreRelatedObjectId?: boolean;
}>;

export const hasRole = (roles: string[], userRoles: string[]) => {
    return roles.some((role) => userRoles.includes(role));
};

export const hasPermission = (permissions: Partial<PermissionRest>[], userPermissions: PermissionRest[], ignoreRelatedObjectId?: boolean) => {
    if (!userPermissions) {
        return false;
    }
    const userPermissionStrings = new Set(userPermissions.map((up) => getPermissionString(up, ignoreRelatedObjectId)));

    return permissions.some((permission) => userPermissionStrings.has(getPermissionString(permission, ignoreRelatedObjectId)));
};

export const hasAllPermissions = (permissions: Partial<PermissionRest>[], userPermissions: PermissionRest[], ignoreRelatedObjectId?: boolean) => {
    if (!userPermissions || permissions.length === 0) {
        return false;
    }
    const userPermissionStrings = new Set(userPermissions.map((up) => getPermissionString(up, ignoreRelatedObjectId)));

    return permissions.every((permission) => userPermissionStrings.has(getPermissionString(permission)));
};

const getPermissionString = (permission: Partial<UserPermission>, ignoreRelatedObjectId?: boolean) => {
    const permissionStringArray: string[] = [];

    if (permission.applicationName) {
        permissionStringArray.push(permission.applicationName);
    }

    if (permission.relatedObjectId && !ignoreRelatedObjectId) {
        permissionStringArray.push(permission.relatedObjectId.toString());
    }

    if (permission.code) {
        permissionStringArray.push(permission.code);
    }

    return permissionStringArray.join('-');
};

const ProtectedContent: React.FC<Props> = ({ roles = [], permissions = [], redirectUrl = '', ignoreRelatedObjectId, children }) => {
    const navigate = useNavigate();
    const { data: user } = useSelector(usersApi.endpoints.pursGetCurrentUserUsingGet.select());
    const { data: userPermissions, isSuccess } = useSelector(usersApi.endpoints.pursGetUserPermissionsUsingGet.select({}));

    const userRoles = useMemo(() => {
        return user?.roles?.map((role) => role.name).filter((name): name is string => !!name) || [];
    }, [user]);

    useEffect(() => {
        if (userPermissions && redirectUrl && isSuccess && !hasPermission(permissions, userPermissions, ignoreRelatedObjectId) && !hasRole(roles, userRoles)) {
            void navigate(redirectUrl);
        }
    }, [redirectUrl, userPermissions, isSuccess, navigate, permissions, roles, userRoles, ignoreRelatedObjectId]);

    if (userPermissions && (hasRole(roles, userRoles) || hasPermission(permissions, userPermissions, ignoreRelatedObjectId))) {
        return <>{children}</>;
    }

    return null;
};

export default ProtectedContent;
