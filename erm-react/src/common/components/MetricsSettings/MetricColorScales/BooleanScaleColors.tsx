import Grid from '@mui/material/Grid';
import ColorSelectorField from 'common/components/Form/FormFields/ColorSelectorField/ColorSelectorField';
import { strings } from 'common/utils/i18n';
import React from 'react';
import { StyledOptionContainer } from './StyledOptionContainer';

const booleanOptions: string[] = ['unset', 'no', 'yes'];

const BooleanScaleColors: React.FC = () => {
    return (
        <Grid
            container
            spacing={1}
        >
            {booleanOptions.map((option) => (
                <Grid
                    item
                    container
                    display="flex"
                    gap={2}
                    key={option}
                >
                    <Grid
                        item
                        flex={1}
                    >
                        <StyledOptionContainer>{strings(`metrics:booleanColorScales.${option}`)}</StyledOptionContainer>
                    </Grid>
                    <Grid
                        item
                        flex={1}
                    >
                        <ColorSelectorField
                            name={`colors.scalesBool.${option}`}
                            emptyLabelSpacing={false}
                        ></ColorSelectorField>
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default BooleanScaleColors;
