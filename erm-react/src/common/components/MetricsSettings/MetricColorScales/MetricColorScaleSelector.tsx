import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { strings } from 'common/utils/i18n';
import { ColorSelectorType } from 'common/components/Form/FormFields/ColorScaleField/types';
import RadioGroupField, { RadioGroupOption } from 'common/components/Form/FormFields/RadioGroupField';
import ColorSelectorField from 'common/components/Form/FormFields/ColorSelectorField/ColorSelectorField';
import Grid from '@mui/material/Grid';
import NumericScaleColors from './NumericScaleColors';
import { MetricColorsType } from '../types';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import TextScaleColors from './TextScaleColors';
import ListFieldScaleColors from './ListFieldScaleColors';
import DueDateScaleColors from './DueDateScaleColors';
import BooleanScaleColors from './BooleanScaleColors';
import { MetricFormValues } from 'metrics/types';
import cloneDeep from 'lodash/cloneDeep';
import { dialogHeight } from './const';
import Dialog from '@protecht/ui-library/library/components/Dialog';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { getReactRoot } from 'config';
import BooleanField from '@protecht/ui-library/library/components/FormFields/BooleanField';

type Props = {
    title: string;
    selectorType: ColorSelectorType;
    selectedSourceField?: SectionFieldMetaData;
    onClose: () => void;
    onConfirm: () => void;
    dialogHeight?: number;
};

const StyledRadioGroupField = styled(RadioGroupField)({
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'initial',
    '& .MuiFormControlLabel-label': {
        width: '100%',
    },
    '& .MuiFormControlLabel-root': {
        marginRight: 0,
    },
    '.MuiFormControlLabel-root:nth-child(2):nth-of-type(2)': {
        alignItems: 'flex-start',
    },
    '& .MuiFormControlLabel-root:nth-child(2) > span:first-child': {
        marginTop: '4px',
    },
    '& .MuiFormControlLabel-root > .MuiTypography-root > span': {
        width: '100%',
    },
});

const StyledDialog = styled(Dialog)({
    '& .MuiDialogContent-root > MuiBox-root': {
        overflow: 'auto',
    },
});

const MetricColorScaleSelector: React.FC<Props> = ({ title, selectorType, selectedSourceField, onClose, onConfirm }) => {
    const { formState, control, watch, setValue } = useFormContext();
    const initialFormValues = useRef<MetricFormValues>();

    const formValues: MetricFormValues = watch() as MetricFormValues;

    useEffect(() => {
        if (!initialFormValues.current && formValues) {
            initialFormValues.current = cloneDeep(formValues);
        }
    }, [formValues]);

    const [isValid, setIsValid] = useState(false);
    useEffect(() => {
        const validateForm = async () => {
            const result = await control._executeSchema(['colors']);
            const containsErrors = Object.keys(result.errors).some((key: string) => key.includes('colors'));
            setIsValid(!containsErrors);
        };
        void validateForm();
    }, [formValues, formState, control]);

    const renderColorScaleForm = useCallback(() => {
        switch (selectorType) {
            case ColorSelectorType.CURRENCY_FIELD:
            case ColorSelectorType.NUMERIC_FIELD:
                return <NumericScaleColors />;
            case ColorSelectorType.STRING_FIELD:
                return <TextScaleColors />;
            case ColorSelectorType.LIST_FIELD:
                return <ListFieldScaleColors selectedSourceField={selectedSourceField} />;
            case ColorSelectorType.DUE_DATE_FIELD:
                return <DueDateScaleColors />;
            case ColorSelectorType.BOOLEAN_FIELD:
                return <BooleanScaleColors />;
            default:
                return null;
        }
    }, [selectorType, selectedSourceField]);

    const colorOptions = useCallback(
        (selectorType: ColorSelectorType): RadioGroupOption[] => [
            {
                value: MetricColorsType.COLOR,
                label: (
                    <Grid
                        gap={2}
                        display="flex"
                        alignItems="center"
                    >
                        <Grid
                            item
                            flex="0 0 auto"
                        >
                            {strings('metrics:colorSelectorDialog.singleColor')}
                        </Grid>
                        <Grid
                            item
                            flex={1}
                        >
                            <ColorSelectorField
                                name="colors.color"
                                emptyLabelSpacing={false}
                            />
                        </Grid>
                        <Grid
                            item
                            flex="0 0 auto"
                        >
                            <BooleanField
                                name={'colors.greyIfEmpty'}
                                label={strings('metrics:customColorDialog.grayIfZero')}
                                emptyLabelSpacing={false}
                            />
                        </Grid>
                    </Grid>
                ),
            },
            {
                value: MetricColorsType.SCALE,
                label: (
                    <>
                        <Box my={1}>{strings(`metrics:colorSelectorDialog.scale.${selectorType}`)}</Box>
                        {renderColorScaleForm()}
                    </>
                ),
            },
        ],
        [renderColorScaleForm],
    );

    const onDialogConfirm = useCallback(() => {
        onConfirm();
    }, [onConfirm]);

    const onDialogClose = useCallback(() => {
        setValue('metricColorsType', initialFormValues.current?.metricColorsType);
        setValue('colors', initialFormValues.current?.colors);
        onClose();
    }, [onClose, setValue]);

    const renderForm = useCallback(() => {
        switch (selectorType) {
            case ColorSelectorType.NUMERIC_FIELD:
            case ColorSelectorType.BOOLEAN_FIELD:
            case ColorSelectorType.DUE_DATE_FIELD:
            case ColorSelectorType.LIST_FIELD:
            case ColorSelectorType.STRING_FIELD:
            case ColorSelectorType.CURRENCY_FIELD:
                return (
                    <StyledRadioGroupField
                        name="metricColorsType"
                        label={strings('metrics:label.colors')}
                        options={colorOptions(selectorType)}
                    />
                );
            case ColorSelectorType.DEFAULT:
            default:
                return (
                    <>
                        <Grid
                            display="flex"
                            flex={1}
                            gap={2}
                            id={'default-color-selector'}
                        >
                            <Grid
                                item
                                flex={1}
                            >
                                <ColorSelectorField
                                    name="colors.color"
                                    emptyLabelSpacing={false}
                                    label={strings('metrics:label.colors')}
                                />
                            </Grid>
                            <Grid item>
                                <BooleanField
                                    name={'colors.greyIfEmpty'}
                                    label={strings('metrics:customColorDialog.grayIfZero')}
                                    emptyLabelSpacing={true}
                                />
                            </Grid>
                        </Grid>
                    </>
                );
        }
    }, [selectorType, colorOptions]);

    return (
        <StyledDialog
            visible={true}
            title={title}
            width={730}
            height={dialogHeight[selectorType] ?? 570}
            dialogContainer={getReactRoot()}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onDialogClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        disabled={!isValid}
                        dataTestId="button-confirm"
                        onClick={onDialogConfirm}
                    >
                        {strings('ermConstants:button_label_select')}
                    </Button>
                </DialogActions>
            }
        >
            {renderForm()}
        </StyledDialog>
    );
};

export default MetricColorScaleSelector;
