import Grid from '@mui/material/Grid';
import ColorSelectorField from 'common/components/Form/FormFields/ColorSelectorField/ColorSelectorField';
import { strings } from 'common/utils/i18n';
import React from 'react';
import { StyledOptionContainer } from './StyledOptionContainer';

const dueDateOptions: string[] = ['open', 'due', 'overdue'];

const DueDateScaleColors: React.FC = () => {
    return (
        <Grid
            container
            spacing={1}
        >
            {dueDateOptions.map((option) => (
                <Grid
                    item
                    container
                    display="flex"
                    gap={2}
                    key={option}
                >
                    <Grid
                        item
                        flex={1}
                    >
                        <StyledOptionContainer>{strings(`metrics:dueDateColorScales.${option}`)}</StyledOptionContainer>
                    </Grid>
                    <Grid
                        item
                        flex={1}
                    >
                        <ColorSelectorField
                            name={`colors.scalesOverdue.${option}`}
                            emptyLabelSpacing={false}
                        ></ColorSelectorField>
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default DueDateScaleColors;
