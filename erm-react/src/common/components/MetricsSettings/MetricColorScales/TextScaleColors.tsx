import { faCircleXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Button from '@protecht/ui-library/library/components/Button';
import ColorSelectorField from 'common/components/Form/FormFields/ColorSelectorField/ColorSelectorField';
import { strings } from 'common/utils/i18n';
import React, { useCallback } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { useTheme } from '@mui/material/styles';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import { StyledOptionContainer } from './StyledOptionContainer';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

const TextScaleColors: React.FC = () => {
    const { control } = useFormContext();

    const {
        fields: scaleColors,
        insert: insertValue,
        remove: removeValue,
    } = useFieldArray({
        name: 'colors.scalesTxt',
        control,
    });

    const theme = useTheme();

    const addNewValue = useCallback(
        (index: number) => {
            insertValue(index, {
                color: '',
                txt: '',
            });
        },
        [insertValue],
    );

    const onRemoveClicked = useCallback(
        (index: number) => {
            removeValue(index);
        },
        [removeValue],
    );

    return (
        <Box width={'100%'}>
            {scaleColors.map((color, index) => {
                return (
                    <Grid
                        container
                        display="flex"
                        gap={2}
                        key={color.id}
                    >
                        {index < scaleColors.length - 1 && (
                            <>
                                <Grid
                                    item
                                    flex={1}
                                >
                                    <InputField
                                        name={`colors.scalesTxt.${index}.txt`}
                                        emptyLabelSpacing={false}
                                        emptyErrorSpacing={false}
                                    ></InputField>
                                </Grid>
                                <Grid
                                    item
                                    flex={1}
                                >
                                    <ColorSelectorField
                                        name={`colors.scalesTxt.${index}.color`}
                                        emptyLabelSpacing={false}
                                    ></ColorSelectorField>
                                </Grid>
                                <Grid item>
                                    <IconButton
                                        onClick={() => onRemoveClicked(index)}
                                        sx={{ mb: 1 }}
                                    >
                                        <FontAwesomeIcon
                                            icon={faCircleXmark}
                                            color={theme.palette.error.main}
                                        />
                                    </IconButton>
                                </Grid>
                            </>
                        )}
                        {index === scaleColors.length - 1 && (
                            <>
                                <Grid
                                    item
                                    flex={1}
                                >
                                    <StyledOptionContainer>{strings('metrics:customColorDialog.allOthers')}</StyledOptionContainer>
                                </Grid>
                                <Grid
                                    item
                                    flex={1}
                                >
                                    <ColorSelectorField
                                        name={`colors.scalesTxt.${index}.color`}
                                        emptyLabelSpacing={false}
                                    ></ColorSelectorField>
                                </Grid>
                                <Grid item>
                                    <Box width={32}></Box>
                                </Grid>
                            </>
                        )}
                    </Grid>
                );
            })}
            <Button
                size="large"
                variant={'text'}
                onClick={() => addNewValue(scaleColors.length - 1)}
                startIcon={<Add />}
            >
                {strings('metrics:customColorDialog.addValue')}
            </Button>
        </Box>
    );
};

export default TextScaleColors;
