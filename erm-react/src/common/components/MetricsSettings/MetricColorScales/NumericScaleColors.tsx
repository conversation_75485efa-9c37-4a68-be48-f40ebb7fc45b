import { faCircleXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Button from '@protecht/ui-library/library/components/Button';
import ColorSelectorField from 'common/components/Form/FormFields/ColorSelectorField/ColorSelectorField';
import NumberField from '@protecht/ui-library/library/components/FormFields/NumberField';
import { strings } from 'common/utils/i18n';
import React, { useCallback } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { useTheme } from '@mui/material/styles';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

const NumericScaleColors: React.FC = () => {
    const { control } = useFormContext();

    const {
        fields: scaleColors,
        insert: insertBracket,
        remove: removeBracket,
    } = useFieldArray({
        name: 'colors.scalesNum',
        control,
    });

    const theme = useTheme();

    const addNewBracket = useCallback(
        (index: number) => {
            insertBracket(index, {
                color: '',
                low: 0,
                high: 0,
            });
        },
        [insertBracket],
    );

    const onRemoveClicked = useCallback(
        (index: number) => {
            removeBracket(index);
        },
        [removeBracket],
    );

    return (
        <Box
            width="100%"
            display="flex"
            flexDirection="column"
            gap="4px"
        >
            {scaleColors.map((interval, index) => {
                return (
                    <Grid
                        container
                        display="flex"
                        gap={2}
                        key={interval.id}
                    >
                        <Grid
                            item
                            width="265px"
                        >
                            <ColorSelectorField
                                name={`colors.scalesNum.${index}.color`}
                                emptyLabelSpacing={false}
                            />
                        </Grid>
                        <Grid
                            item
                            container
                            flex={1}
                            gap={1}
                            alignItems="flex-start"
                        >
                            {index === 0 && (
                                <>
                                    <Grid
                                        item
                                        xs={3}
                                        textAlign="right"
                                        paddingTop="10px"
                                        alignSelf="flex-start"
                                    >
                                        <Typography variant="body1">{strings('metrics:customColorDialog.lessThan')}</Typography>
                                    </Grid>
                                    <Grid
                                        item
                                        width="90px"
                                        xs={3}
                                    >
                                        <NumberField
                                            name={`colors.scalesNum.${index}.lessThan`}
                                            emptyLabelSpacing={false}
                                            emptyErrorSpacing={false}
                                        />
                                    </Grid>
                                </>
                            )}
                            {index > 0 && index <= scaleColors.length - 2 && (
                                <>
                                    <Grid
                                        item
                                        xs={3}
                                        textAlign="right"
                                        paddingTop="10px"
                                        alignSelf="flex-start"
                                    >
                                        <Typography variant="body1">{strings('metrics:customColorDialog.from')}</Typography>
                                    </Grid>
                                    <Grid
                                        item
                                        width="90px"
                                        xs={3}
                                    >
                                        <NumberField
                                            name={`colors.scalesNum.${index}.low`}
                                            emptyLabelSpacing={false}
                                            emptyErrorSpacing={false}
                                        />
                                    </Grid>
                                    <Grid
                                        item
                                        paddingTop="10px"
                                        alignSelf="flex-start"
                                    >
                                        <Typography variant="body1">{strings('metrics:customColorDialog.to')}</Typography>
                                    </Grid>
                                    <Grid
                                        item
                                        width="90px"
                                        xs={3}
                                    >
                                        <NumberField
                                            name={`colors.scalesNum.${index}.high`}
                                            emptyLabelSpacing={false}
                                            emptyErrorSpacing={false}
                                        />
                                    </Grid>
                                    <Grid
                                        item
                                        paddingTop="5px"
                                    >
                                        <IconButton onClick={() => onRemoveClicked(index)}>
                                            <FontAwesomeIcon
                                                icon={faCircleXmark}
                                                color={theme.palette.error.main}
                                            />
                                        </IconButton>
                                    </Grid>
                                </>
                            )}
                            {index === scaleColors.length - 1 && (
                                <>
                                    <Grid
                                        item
                                        xs={3}
                                        textAlign="right"
                                        paddingTop="10px"
                                        alignSelf="flex-start"
                                    >
                                        <Typography variant="body1">{strings('metrics:customColorDialog.greaterThan')}</Typography>
                                    </Grid>
                                    <Grid
                                        item
                                        width="90px"
                                        xs={3}
                                    >
                                        <NumberField
                                            name={`colors.scalesNum.${index}.greaterThan`}
                                            emptyLabelSpacing={false}
                                            emptyErrorSpacing={false}
                                        />
                                    </Grid>
                                </>
                            )}
                        </Grid>
                    </Grid>
                );
            })}
            <Button
                size="large"
                variant={'text'}
                onClick={() => addNewBracket(scaleColors.length - 1)}
                startIcon={<Add />}
                sx={{ color: theme.palette.primary.main, width: '132px' }}
            >
                {strings('metrics:customColorDialog.addBracket')}
            </Button>
        </Box>
    );
};

export default NumericScaleColors;
