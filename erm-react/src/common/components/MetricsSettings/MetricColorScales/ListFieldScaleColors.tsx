import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import React, { useEffect, useState } from 'react';
import { ListItem, SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { getListItems } from 'register/components/RegisterField/ListRegisterField/utils';
import Typography from '@mui/material/Typography';
import ColorIndicator from 'common/components/ColorIndicator';
import { DEFAULT_LIST_TEXT_COLOR } from './const';
import { StyledOptionContainer } from './StyledOptionContainer';
import { styled } from '@mui/material/styles';
import { MetricColorsType } from '../types';
import { useFormContext } from 'react-hook-form';
import { DEFAULT_METRIC_COLORS } from 'common/components/MetricsDetail/const';

type Props = {
    selectedSourceField?: SectionFieldMetaData;
};

const ReadOnlyListColor = styled(Box)(({ theme }) => ({
    border: '1px solid',
    borderColor: theme.palette.protechtGrey?.grey_220,
    height: '32px',
    padding: '8px 13px',
    display: 'flex',
    alignItems: 'center',
    borderRadius: '4px',
}));

const ListFieldScaleColors: React.FC<Props> = ({ selectedSourceField }) => {
    const [options, setOptions] = useState<ListItem[]>([]);
    useEffect(() => {
        if (selectedSourceField) {
            setOptions(getListItems(selectedSourceField));
        }
    }, [selectedSourceField]);

    const { setValue, watch } = useFormContext();
    const metricColorsType: MetricColorsType = watch('metricColorsType');

    useEffect(() => {
        if (metricColorsType === MetricColorsType.SCALE) {
            setValue(
                'colors.scalesList',
                options.map((option) => ({
                    txt: option.label,
                    color: option.backgroundColor || DEFAULT_LIST_TEXT_COLOR,
                })),
                { shouldDirty: true, shouldValidate: true },
            );
        } else {
            setValue('colors.scalesList', DEFAULT_METRIC_COLORS.scalesList, { shouldDirty: true, shouldValidate: true });
        }
    }, [metricColorsType, setValue, selectedSourceField, options]);

    return (
        <Box width={'100%'}>
            {options.map((option, index) => {
                return (
                    <Grid
                        container
                        display="flex"
                        gap={2}
                        key={index}
                        mb={1}
                    >
                        <Grid
                            item
                            flex={1}
                        >
                            <StyledOptionContainer>{option.label}</StyledOptionContainer>
                        </Grid>
                        <Grid
                            item
                            flex={1}
                        >
                            <ReadOnlyListColor>
                                <ColorIndicator
                                    mr="10px"
                                    value={option.backgroundColor || DEFAULT_LIST_TEXT_COLOR}
                                ></ColorIndicator>
                                <Typography variant="body2">{option.backgroundColor || DEFAULT_LIST_TEXT_COLOR}</Typography>
                            </ReadOnlyListColor>
                        </Grid>
                    </Grid>
                );
            })}
        </Box>
    );
};

export default ListFieldScaleColors;
