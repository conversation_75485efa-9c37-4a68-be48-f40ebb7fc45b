import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import { SelectField } from '@protecht/ui-library/library/components/FormFields';
import { SYSTEM_COLUMN } from 'common/types';
import { strings } from 'common/utils/i18n';
import { displayFuncOptions } from 'metrics/const';
import { DisplayFunc } from 'metrics/types';
import { operatorRequiresValue } from 'metrics/utils';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { ColumnType, RegisterRest } from 'register/types';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types/types';
import { getAllAuxiliaryFields, getAllNonAuxiliaryFields, getColumnAuxiliaryFields, getCoreFields } from 'register/utils';
import { MetricColorsType, RegisterFieldsByType } from '../types';
import { getAllRegisterFields, getDialogInputLabel } from '../utils';
import { CORE_FIELDS, FIELD_VALUE_FIELDS, ID_FIELD, PERCENTAGE_FIELDS, SUM_AVG_FIELDS } from '../const';
import OperatorSelector from 'common/components/Conditions/OperatorSelector';
import ConditionValue from 'metrics/components/ConditionsBuilder/ConditionValue';
import RegisterFieldSelectorField from 'common/components/Form/FormFields/RegisterFieldSelectorField';
import { DEFAULT_METRIC_COLORS } from 'common/components/MetricsDetail/const';
import { TableMetadataRest } from 'api/generated/types';

type Props = {
    selectedSourceRegister?: TableMetadataRest;
    selectedSourceField?: SectionFieldMetaData;
    setSelectedSourceField: (field?: SectionFieldMetaData) => void;
    sourceFieldColumnName?: string;
    dataTestId?: string;
};

const MetricDisplayFunction: React.FC<Props> = ({ selectedSourceField, setSelectedSourceField, selectedSourceRegister, sourceFieldColumnName, dataTestId }) => {
    const [initialSourceFieldColumnName, setInitialSourceFieldColumnName] = useState<string | undefined>(sourceFieldColumnName);
    const [availableSourceFields, setAvailableSourceFields] = useState<RegisterFieldsByType | undefined>(undefined);
    const [filteredSourceFields, setFilteredSourceFields] = useState<SectionFieldMetaData[]>([]);

    const { control, watch, setValue } = useFormContext();

    const displayFunc = watch('func');
    const context = watch('context');
    const operator = watch('operator');
    const value = watch('value');

    useEffect(() => {
        switch (displayFunc) {
            case DisplayFunc.AVG:
                setValue('note', strings('metrics:message.averageNote'));
                break;
            case DisplayFunc.FIELD_VALUE:
                setValue('note', strings('metrics:message.fieldValueNote'));
                break;
            case DisplayFunc.SUM:
                setValue('note', strings('metrics:message.sumNote'));
                break;
        }
    }, [displayFunc, setValue]);

    useEffect(() => {
        if (sourceFieldColumnName) {
            setInitialSourceFieldColumnName(sourceFieldColumnName);
        }
    }, [sourceFieldColumnName]);

    useEffect(() => {
        const allFields = getAllRegisterFields(availableSourceFields);
        if (initialSourceFieldColumnName && allFields.length) {
            if (initialSourceFieldColumnName === SYSTEM_COLUMN.ID) {
                setSelectedSourceField(ID_FIELD);
                setInitialSourceFieldColumnName(undefined);
            } else {
                const initialSourceField = allFields.find((field) => field.columnName === initialSourceFieldColumnName);
                if (initialSourceField) {
                    setSelectedSourceField(initialSourceField);
                    setInitialSourceFieldColumnName(undefined);
                }
            }
        }
    }, [sourceFieldColumnName, initialSourceFieldColumnName, availableSourceFields, setSelectedSourceField]);

    useEffect(() => {
        const sourceField = watch('sourceField');
        const allFields = getAllRegisterFields(availableSourceFields);

        if (sourceField && allFields.length && !selectedSourceField && availableSourceFields) {
            if (sourceField === SYSTEM_COLUMN.ID) {
                setSelectedSourceField(ID_FIELD);
                // Only set sourceFieldColumnType if it's not already set to avoid triggering isDirty
                if (!watch('sourceFieldColumnType')) {
                    setValue('sourceFieldColumnType', ID_FIELD.columnType, { shouldDirty: false });
                }
            } else {
                const field = allFields.find((field) => field.columnName === sourceField);
                if (field) {
                    setSelectedSourceField(field);
                    // Only set sourceFieldColumnType if it's not already set to avoid triggering isDirty
                    if (!watch('sourceFieldColumnType')) {
                        setValue('sourceFieldColumnType', field.columnType, { shouldDirty: false });
                    }
                }
            }
        }
    }, [availableSourceFields, selectedSourceField, setSelectedSourceField, setValue, watch]);

    // Handle the case where sourceField is already set in form but register data wasn't available yet
    useEffect(() => {
        const sourceField = watch('sourceField');
        const allFields = getAllRegisterFields(availableSourceFields);

        if (sourceField && allFields.length && !selectedSourceField && availableSourceFields) {
            if (sourceField === SYSTEM_COLUMN.ID) {
                setSelectedSourceField(ID_FIELD);
            } else {
                const field = allFields.find((field) => field.columnName === sourceField);
                if (field) {
                    setSelectedSourceField(field);
                }
            }
        }
    }, [availableSourceFields, selectedSourceField, setSelectedSourceField, watch]);

    useEffect(() => {
        if (selectedSourceRegister) {
            setAvailableSourceFields({
                coreFields: getCoreFields(selectedSourceRegister as RegisterRest, true),
                nonAuxiliaryFields: getAllNonAuxiliaryFields(selectedSourceRegister as RegisterRest, true),
                auxiliaryFields: getAllAuxiliaryFields(selectedSourceRegister as RegisterRest, true),
                statusField: (selectedSourceRegister as RegisterRest).statusColumn,
            });
        } else {
            setAvailableSourceFields(undefined);
        }
    }, [selectedSourceRegister]);

    useEffect(() => {
        if (!availableSourceFields) {
            setFilteredSourceFields([]);
        } else {
            const { auxiliaryFields, nonAuxiliaryFields, coreFields, statusField } = availableSourceFields;
            const filteredFields: SectionFieldMetaData[] = [];

            switch (displayFunc) {
                case DisplayFunc.AVG:
                case DisplayFunc.SUM:
                    filteredFields.push(...nonAuxiliaryFields.filter((field) => SUM_AVG_FIELDS.includes(field.columnType)));

                    filteredFields.push(
                        ...getColumnAuxiliaryFields(
                            nonAuxiliaryFields.find((field) => field.columnType === ColumnType.RISK_MATRIX),
                            auxiliaryFields,
                        ).filter((field) => [ColumnType.SCALE_CONSEQUENCE, ColumnType.INTEGER, ColumnType.SCALE_LIKELIHOOD].includes(field.columnType)),
                    );
                    break;
                case DisplayFunc.FIELD_VALUE:
                    filteredFields.push(...nonAuxiliaryFields.filter((field) => FIELD_VALUE_FIELDS.includes(field.columnType)));

                    filteredFields.push(
                        ...getColumnAuxiliaryFields(
                            nonAuxiliaryFields.find((field) => field.columnType === ColumnType.DUE_DATE),
                            auxiliaryFields,
                        ),
                    );

                    if (statusField) {
                        filteredFields.push(statusField);
                    }

                    break;
                case DisplayFunc.PERCENT:
                    filteredFields.push(...nonAuxiliaryFields.filter((field) => PERCENTAGE_FIELDS.includes(field.columnType)));
                    filteredFields.push(...coreFields.filter((field) => CORE_FIELDS.includes(field.columnName as SYSTEM_COLUMN)));

                    filteredFields.push(
                        ...getColumnAuxiliaryFields(
                            nonAuxiliaryFields.find((field) => field.columnType === ColumnType.DUE_DATE),
                            auxiliaryFields,
                        ),
                    );

                    filteredFields.push(
                        ...getColumnAuxiliaryFields(
                            nonAuxiliaryFields.find((field) => field.columnType === ColumnType.RISK_MATRIX),
                            auxiliaryFields,
                        ),
                    );

                    if (statusField) {
                        filteredFields.push(statusField);
                    }

                    break;
                default:
                    break;
            }
            setFilteredSourceFields(filteredFields);
        }
    }, [availableSourceFields, displayFunc]);

    const isConditionWithValue = useMemo(() => {
        return operatorRequiresValue(operator);
    }, [operator]);

    const renderDialogSelector = useCallback(() => {
        return (
            <RegisterFieldSelectorField
                name="sourceField"
                label={getDialogInputLabel(displayFunc)}
                placeholder={strings('common:label.selectField')}
                register={selectedSourceRegister as RegisterRest}
                selectedField={selectedSourceField}
                fields={filteredSourceFields}
                disabled={!context}
                infoMessage={strings('metrics:message.fieldSelectorNote')}
                onlyInput
                dataTestId={dataTestId}
                onSelect={(selectedField: SectionFieldMetaData) => {
                    setValue('sourceField', selectedField.columnName, { shouldDirty: true, shouldValidate: true });
                    setValue('sourceFieldColumnType', selectedField.columnType);
                    setValue('operator', '', { shouldDirty: true, shouldValidate: !!operator });
                    setValue('value', '', { shouldDirty: true });
                    setValue('colors', DEFAULT_METRIC_COLORS, { shouldDirty: true });
                    if (selectedField.columnType === ColumnType.DUE_DATE) {
                        setValue('metricColorsType', MetricColorsType.SCALE, { shouldDirty: true, shouldValidate: true });
                    } else {
                        setValue('metricColorsType', MetricColorsType.COLOR, { shouldDirty: true, shouldValidate: true });
                    }
                    setSelectedSourceField(selectedField);
                }}
            />
        );
    }, [displayFunc, selectedSourceRegister, selectedSourceField, filteredSourceFields, context, dataTestId, setValue, operator, setSelectedSourceField]);

    const renderFieldSelector = useCallback(() => {
        switch (displayFunc) {
            case DisplayFunc.AVG:
            case DisplayFunc.FIELD_VALUE:
            case DisplayFunc.SUM:
                return (
                    <Grid
                        item
                        xs={12}
                        sm={6}
                    >
                        {renderDialogSelector()}
                    </Grid>
                );
            case DisplayFunc.PERCENT:
                return (
                    <Grid
                        container
                        item
                        spacing={1}
                        xs={12}
                        sm={10}
                    >
                        <Grid
                            item
                            xs={12}
                            sm={6}
                        >
                            {renderDialogSelector()}
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sm={6}
                            display={'flex'}
                            gap={1}
                        >
                            <OperatorSelector
                                name="operator"
                                disabled={!selectedSourceField?.filterType}
                                selectedRegisterFieldFilterType={selectedSourceField?.filterType}
                                emptyLabelSpacing={true}
                                preselectValue
                                onChange={() => {
                                    setValue('value', '', { shouldDirty: !!value, shouldTouch: true, shouldValidate: true });
                                }}
                            />
                            {isConditionWithValue && (
                                <Box sx={{ flexGrow: 1 }}>
                                    <ConditionValue
                                        name="value"
                                        selectedRegisterField={selectedSourceField}
                                        selectedOperator={operator}
                                        emptyLabelSpacing={true}
                                        preselectValue
                                        selectedSourceRegister={selectedSourceRegister}
                                    ></ConditionValue>
                                </Box>
                            )}
                        </Grid>
                    </Grid>
                );
            default:
                return null;
        }
    }, [displayFunc, renderDialogSelector, selectedSourceField, isConditionWithValue, operator, selectedSourceRegister, setValue, value]);

    return (
        <>
            <Grid
                item
                xs={12}
                sm={6}
            >
                <SelectField
                    name="func"
                    label={strings('metrics:label.func')}
                    variant="outlined"
                    displayEmpty
                    options={displayFuncOptions}
                    onChange={(value: DisplayFunc) => {
                        setValue('sourceField', '', { shouldDirty: true, shouldValidate: value !== DisplayFunc.COUNT });
                        setValue('sourceFieldColumnType', undefined);
                        setValue('colors', DEFAULT_METRIC_COLORS, { shouldDirty: true });
                        setValue('metricColorsType', MetricColorsType.COLOR, { shouldDirty: true, shouldValidate: true });
                        setValue('operator', '', { shouldDirty: true, shouldValidate: !!operator });
                        setValue('value', '', { shouldDirty: true });
                        setSelectedSourceField(undefined);
                    }}
                />
            </Grid>
            {renderFieldSelector()}
            {(displayFunc === DisplayFunc.AVG || displayFunc === DisplayFunc.SUM || displayFunc === DisplayFunc.FIELD_VALUE) && (
                <Grid
                    item
                    xs={12}
                >
                    <InputField
                        name="note"
                        label="Note"
                        control={control}
                        disabled
                    />
                </Grid>
            )}
        </>
    );
};

export default MetricDisplayFunction;
