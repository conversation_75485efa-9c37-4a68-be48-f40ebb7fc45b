import { ColumnType, SectionFieldMetaData } from '@protecht/ui-library/library/types';
import { SYSTEM_COLUMN } from 'common/types';
import { FilterType } from 'view/types';

export const SUM_AVG_FIELDS = [
    ColumnType.SCALE_CONSEQUENCE,
    ColumnType.SCALE_LIKELIHOOD,
    ColumnType.COMPLEX_FORMULA,
    ColumnType.SIMPLE_FORMULA,
    ColumnType.INTEGER,
    ColumnType.QUESTION,
    ColumnType.CURRENCY,
];

export const FIELD_VALUE_FIELDS = [
    ColumnType.ATTACHMENT,
    ColumnType.CURRENCY,
    ColumnType.INTEGER,
    ColumnType.NUMERIC,
    ColumnType.LIST,
    ColumnType.SINGLELINE_TEXT,
    ColumnType.SLIDER,
    ColumnType.COUNTRY,
    ColumnType.STATE,
    ColumnType.DUE_DATE,
    ColumnType.SCALE_CONSEQUENCE,
    ColumnType.SCALE_LIKELIHOOD,
    ColumnType.CONTROL,
    ColumnType.RISK_CAUSE,
    ColumnType.RISK_EVENT,
    ColumnType.COMPLEX_FORMULA,
    ColumnType.SIMPLE_FORMULA,
    ColumnType.STRING_FORMULA,
    ColumnType.DATETIME_FORMULA,
    ColumnType.BOOLEAN,
    ColumnType.GPS_POSITION,
    ColumnType.HYPERLINK,
    ColumnType.MULTISELECT_LIST,
    ColumnType.TAGS,
    ColumnType.DATE,
    ColumnType.TIMESTAMP,
    ColumnType.TIMESTAMP_WITH_TIMEZONE,
];

export const PERCENTAGE_FIELDS = [
    ColumnType.ATTACHMENT,
    ColumnType.BOOLEAN,
    ColumnType.CURRENCY,
    ColumnType.DATE,
    ColumnType.TIMESTAMP,
    ColumnType.TIMESTAMP_WITH_TIMEZONE,
    ColumnType.NUMERIC,
    ColumnType.EMAIL,
    ColumnType.LIST,
    ColumnType.MULTILINE_TEXT,
    ColumnType.MULTISELECT_LIST,
    ColumnType.SINGLELINE_TEXT,
    ColumnType.SLIDER,
    ColumnType.INTEGER,
    ColumnType.BUSINESS_UNIT,
    ColumnType.COUNTRY,
    ColumnType.ROLE,
    ColumnType.STATE,
    ColumnType.SIGN_OFF,
    ColumnType.USER,
    ColumnType.DUE_DATE,
    ColumnType.SCALE_CONSEQUENCE,
    ColumnType.SCALE_LIKELIHOOD,
    ColumnType.CONTROL,
    ColumnType.RISK_CAUSE,
    ColumnType.RISK_EVENT,
    ColumnType.COMPLEX_FORMULA,
    ColumnType.SIMPLE_FORMULA,
    ColumnType.STRING_FORMULA,
    ColumnType.DATETIME_FORMULA,
    ColumnType.GPS_POSITION,
    ColumnType.HYPERLINK,
    ColumnType.RICH_TEXT,
    ColumnType.TAGS,
    ColumnType.TABLE,
    ColumnType.ACTIONS,
    ColumnType.QUESTION,
];

export const CORE_FIELDS = [
    SYSTEM_COLUMN.FIELD_CREATE_DATE,
    SYSTEM_COLUMN.FIELD_MODIFIED_DATE,
    SYSTEM_COLUMN.FIELD_BUSINESS_UNIT,
    SYSTEM_COLUMN.FIELD_CREATED_BY,
    SYSTEM_COLUMN.FIELD_MODIFIED_BY,
];

// id of register to be used as id "field" in register field selector, where columnName is the value sent to BE
// other values are integrated for the sake of typechecking
export const ID_FIELD: SectionFieldMetaData = {
    columnName: SYSTEM_COLUMN.ID,
    id: -1,
    label: 'ID',
    columnType: ColumnType.INTEGER,
    fieldLabel: 'id',
    hideLabel: false,
    description: 'id',
    filterType: FilterType.NUMBER,
    required: true,
    archived: false,
    defaultValue: '',
    layoutRow: 0,
    layoutColumn: 'left',
    layoutStrategy: undefined,
    constraintProperties: undefined,
};
