import React, { useEffect, useState } from 'react';
import Grid from '@mui/material/Grid';
import InputField from '@protecht/ui-library/library/components/FormFields/InputField';
import { strings } from 'common/utils/i18n';
import useFormContext from 'common/hooks/forms/useFormContext';
import DialogSelectorField from 'common/components/Form/FormFields/DialogSelectorField';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';
import StyledDivider from '@protecht/ui-library/library/components/StyledDivider';
import RadioGroupField from 'common/components/Form/FormFields/RadioGroupField';
import { ConditionsBuilder } from 'metrics/components/ConditionsBuilder';
import Typography from '@mui/material/Typography';
import MetricDisplayFunction from './MetricDisplayFunction/MetricDisplayFunction';
import ColorScaleField from 'common/components/Form/FormFields/ColorScaleField/ColorScaleField';
import DataSourceSelector, { DataSourceScope } from 'common/components/MetricsDataSourceSelector';
import { MetricColorsType } from './types';
import { columnHasScaleColors } from 'metrics/utils';
import { DEFAULT_METRIC_COLORS } from 'common/components/MetricsDetail/const';
import { widthOptions } from 'metrics/const';
import { TableMetadataRest } from 'api/generated/types';
import { MetricsModule } from 'metrics/types';

type Props = {
    module: MetricsModule;
    sourceFieldColumnName?: string;
    selectedSourceRegister?: TableMetadataRest;
    setSelectedSourceRegister: (register: TableMetadataRest) => void;
};

const GridDivider = () => (
    <Grid
        item
        xs={12}
    >
        <StyledDivider margin={0} />
    </Grid>
);

const METRICS_TO_DATASOURCE = {
    [MetricsModule.VRM]: DataSourceScope.VRM,
    [MetricsModule.CYBER_RISK]: DataSourceScope.CYBER_RISK,
};

const MetricsSettings = ({ module, sourceFieldColumnName, selectedSourceRegister, setSelectedSourceRegister }: Props) => {
    const [selectedSourceField, setSelectedSourceField] = useState<SectionFieldMetaData | undefined>(undefined);

    const { setValue, watch, getValues } = useFormContext();
    const metricColorsType: MetricColorsType = watch('metricColorsType');
    const sourceField = watch('sourceField');

    useEffect(() => {
        // set initial sourceFieldColumnType when it is not set
        if (!getValues('sourceFieldColumnType')) {
            setValue('sourceFieldColumnType', selectedSourceField?.columnType);
        }
    }, [selectedSourceField, setValue, getValues]);

    useEffect(() => {
        // set type COLOR when source field does not have column with scale color options
        if (metricColorsType === MetricColorsType.SCALE && selectedSourceField?.columnType && !columnHasScaleColors(selectedSourceField?.columnType)) {
            setValue('metricColorsType', MetricColorsType.COLOR);
        }
    }, [setValue, metricColorsType, selectedSourceField]);

    return (
        <Grid
            container
            spacing={'20px'}
        >
            <Grid
                item
                xs={12}
                sm={6}
            >
                <InputField
                    name="name"
                    label={strings('metrics:label.name')}
                    placeholder={strings('metrics:label.namePlaceholder')}
                    dataTestId="input-metric-name"
                />
            </Grid>
            <Grid
                item
                xs={12}
                sm={6}
            >
                <DialogSelectorField
                    name="context"
                    displayValue={selectedSourceRegister?.label}
                    placeholder={strings('metrics:label.contextPlaceholder')}
                    label={strings('metrics:label.context')}
                    dataTestId="input-data-source"
                    renderDialog={({ ref: _ref, ...props }) => (
                        <DataSourceSelector
                            {...props}
                            selectedDataSource={selectedSourceRegister}
                            onSelect={(selectedRegister) => {
                                if (selectedRegister) {
                                    setValue('context', selectedRegister.tableName, { shouldDirty: true, shouldValidate: true });
                                    setValue('sourceField', '', { shouldDirty: true, shouldValidate: !!sourceField });
                                    setValue('operator', '', { shouldDirty: true });
                                    setValue('value', '', { shouldDirty: true });
                                    setValue('colors', DEFAULT_METRIC_COLORS, { shouldDirty: true });
                                    setValue('metricColorsType', MetricColorsType.COLOR, { shouldDirty: true, shouldValidate: true });
                                    setValue('sourceFieldColumnType', undefined);
                                    setSelectedSourceField(undefined);
                                    setSelectedSourceRegister(selectedRegister as TableMetadataRest);
                                }
                            }}
                            moduleName={METRICS_TO_DATASOURCE[module]}
                        />
                    )}
                />
            </Grid>
            <Grid
                item
                xs={12}
            >
                <Typography
                    variant="body3"
                    color="protechtGrey.darkBlack"
                    sx={{ marginBottom: 1 }}
                >
                    {strings('metrics:label.conditions')}
                </Typography>
                <ConditionsBuilder selectedSourceRegister={selectedSourceRegister}></ConditionsBuilder>
            </Grid>
            <GridDivider />
            <MetricDisplayFunction
                selectedSourceRegister={selectedSourceRegister}
                selectedSourceField={selectedSourceField}
                setSelectedSourceField={(field: SectionFieldMetaData | undefined) => setSelectedSourceField(field)}
                sourceFieldColumnName={sourceFieldColumnName}
                dataTestId="input-source-field"
            />
            <GridDivider />
            <Grid
                item
                xs={12}
                sm={6}
            >
                <RadioGroupField
                    name="width"
                    label={strings('metrics:label.metricWidth')}
                    options={widthOptions}
                    row
                />
            </Grid>
            <Grid
                item
                xs={12}
                sm={6}
            >
                <ColorScaleField
                    name="colors"
                    label={strings('metrics:label.colors')}
                    selectedSourceField={selectedSourceField}
                />
            </Grid>
        </Grid>
    );
};

export default MetricsSettings;
