import { ColumnToColorSelectorMap, DisplayFunc, MetricColors, MetricColorsMap, MetricFormValues } from 'metrics/types';
import { MetricColorsType, RegisterFieldsByType } from './types';
import { strings } from 'common/utils/i18n';
import { ColumnType } from 'register/types';
import { ColorSelectorType } from 'common/components/Form/FormFields/ColorScaleField/types';
import { SectionFieldMetaData } from '@protecht/ui-library/library/types';

export const getDialogInputLabel = (displayFunc: DisplayFunc): string => {
    switch (displayFunc) {
        case DisplayFunc.AVG:
        case DisplayFunc.FIELD_VALUE:
        case DisplayFunc.SUM:
            return strings('metrics:label.valueField');
        case DisplayFunc.PERCENT:
            return strings('metrics:label.percentageOfEntries');
        default:
            return '';
    }
};

export const getAllRegisterFields = (fields?: RegisterFieldsByType): SectionFieldMetaData[] => [
    ...(fields?.auxiliaryFields ?? []),
    ...(fields?.coreFields ?? []),
    ...(fields?.nonAuxiliaryFields ?? []),
    ...(fields?.statusField ? [fields.statusField] : []),
];

export const getColorScaleSelectorType = (columnType?: ColumnType): ColorSelectorType => {
    if (columnType) {
        const colorSelectorType = ColumnToColorSelectorMap[columnType];

        if (colorSelectorType) {
            return colorSelectorType;
        }
    }

    return ColorSelectorType.DEFAULT;
};

export const getStylingSettings = (formValues: MetricFormValues): Partial<MetricColors> => {
    if (formValues.metricColorsType === MetricColorsType.SCALE) {
        const scaleType = getColorScaleSelectorType(formValues.sourceFieldColumnType);
        const metricColorProperty = MetricColorsMap[scaleType];

        if (metricColorProperty) {
            return { [metricColorProperty]: formValues.colors[metricColorProperty] };
        } else {
            return {};
        }
    }

    return {
        color: formValues.colors.color,
        greyIfEmpty: formValues.colors.greyIfEmpty,
    };
};
