import React from 'react';
import { render, screen } from 'test/utils';
import userEvent from '@testing-library/user-event';
import { act, waitFor } from '@testing-library/react';
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEllipsisH, faPlusCircle } from '@fortawesome/pro-solid-svg-icons';
import SuggestionsInput from 'common/components/SuggestionsInput/index';
import { PagingResult } from 'common/api/types';
import { restTagsToTable } from 'library/utils';
import InputAdornment from '@mui/material/InputAdornment';
import { TagRest } from 'api/generated/types';
import { mockAllIsIntersecting } from 'react-intersection-observer/test-utils';

library.add(faEllipsisH, faPlusCircle);

const convertToPagingResults = (value: any) => {
    const tags: PagingResult<TagRest> = value;
    const tagData = restTagsToTable(tags.records || []) || [];
    return {
        ...tags,
        records: tagData,
    };
};

beforeAll(() => {
    jest.useFakeTimers();
});

afterAll(() => {
    jest.useRealTimers();
});

const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

describe('<SuggestionsInput />', () => {
    const renderSut = () => render(<SuggestionsInput suggestionField={'preview'} />);

    it('was rendered and empty on first load', async () => {
        renderSut();
        const textBox = screen.getByRole('textbox');
        await waitFor(() => {
            expect(textBox).toBeInTheDocument();
        });
    });

    it('is empty on first load', async () => {
        renderSut();
        const textBox = screen.getByRole('textbox');

        await waitFor(() => {
            expect(textBox).toBeInTheDocument();
        });

        expect(textBox).toBeEmptyDOMElement();
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
    });

    it('does not show data when typed faster than debounce', async () => {
        renderSut();
        const textBox = screen.getByRole('textbox');

        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
        await user.type(textBox, 'te');
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
        await user.type(textBox, 'tes');
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
    });
});

describe('<SuggestionsInput /> with data', () => {
    const renderSut = async () => {
        render(
            <SuggestionsInput
                suggestionField={'preview'}
                onChange={onChange}
                onDataLoad={dataLoad}
            />,
        );
        textBox = screen.getByRole('textbox');
    };

    let textBox: HTMLElement;
    let onChange;
    let dataLoad;

    beforeEach(async () => {
        onChange = jest.fn();
        dataLoad = jest
            .fn()
            .mockResolvedValueOnce(
                convertToPagingResults({
                    totalCount: 4,
                    records: [
                        { id: 11437, value: 'Business Improvement', type: { id: 10065, name: 'Control Classification' } },
                        { id: 10140, value: 'Business and System Disruption', type: { id: 10062, name: 'Operational Risk' } },
                        { id: 11391, value: 'Business Continuity', type: { id: 10464, name: 'Risk Category 2' } },
                        { id: 11330, value: 'Business Unit', type: { id: 10463, name: 'Risk Register' } },
                    ],
                    maxPage: 50,
                }),
            )
            .mockResolvedValue(
                convertToPagingResults({
                    maxPage: 50,
                    records: [
                        { id: 10155, value: 'Human resource controls', type: { id: 10065, name: 'Control Classification' } },
                        { id: 10160, value: 'Key Control', type: { id: 10067, name: 'Control Key' } },
                        { id: 11327, value: 'Critical Control', type: { id: 10067, name: 'Control Key' } },
                        { id: 11340, value: 'Non-Critical Control', type: { id: 10067, name: 'Control Key' } },
                        { id: 10680, value: 'control', type: { id: 10360, name: 'hierarchy test' } },
                        { id: 11400, value: 'Financial Control', type: { id: 10464, name: 'Risk Category 2' } },
                    ],
                    totalCount: 6,
                }),
            );
    });

    it('show data once user stop typing', async () => {
        await renderSut();

        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
        await user.type(textBox, 'bus');

        expect(await screen.findByDisplayValue('bus')).toBeInTheDocument();

        await waitFor(() => expect(dataLoad).toHaveBeenCalled());
        expect(onChange).toHaveBeenCalled();
        expect(screen.getByRole('menu')).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.getAllByRole('menuitem')).toHaveLength(4);
        });
        expect(screen.getByTextContent('Control Classification:Business Improvement')).toBeInTheDocument();
    });

    it('show different data for different input', async () => {
        await renderSut();

        await user.type(textBox, 'bus');

        await waitFor(() => expect(dataLoad).toHaveBeenCalledTimes(1));
        await waitFor(() => {
            expect(screen.getAllByRole('menuitem')).toHaveLength(4);
        });
        expect(screen.getByTextContent('Control Classification:Business Improvement')).toBeInTheDocument();

        await user.clear(textBox);
        await user.type(textBox, 'control');

        await waitFor(() => expect(dataLoad).toHaveBeenCalledTimes(3));
        await waitFor(() => {
            expect(screen.getAllByRole('menuitem')).toHaveLength(6);
        });
        expect(screen.getByTextContent('Control Classification:Human resource controls')).toBeInTheDocument();
    });
});

describe('<SuggestionsInput /> with paged data', () => {
    const renderSut = () => {
        render(
            <SuggestionsInput
                suggestionField={'preview'}
                onDataLoad={dataLoad}
            />,
        );
    };

    let dataLoad;

    beforeEach(() => {
        dataLoad = jest
            .fn()
            .mockResolvedValueOnce(
                convertToPagingResults({
                    maxPage: 50,
                    records: [
                        { id: 10201, value: 'Sales & Contact Center', type: { id: 10080, name: 'Business Unit' } },
                        { id: 11414, value: 'Contractor Relations', type: { id: 10004, name: 'Causes' } },
                        { id: 10155, value: 'Human resource controls', type: { id: 10065, name: 'Control Classification' } },
                        { id: 10160, value: 'Key Control', type: { id: 10067, name: 'Control Key' } },
                        { id: 11327, value: 'Critical Control', type: { id: 10067, name: 'Control Key' } },
                        { id: 11340, value: 'Non-Critical Control', type: { id: 10067, name: 'Control Key' } },
                    ],
                    totalCount: 9,
                }),
            )
            .mockResolvedValueOnce(
                convertToPagingResults({
                    maxPage: 50,
                    records: [
                        { id: 10680, value: 'control', type: { id: 10360, name: 'hierarchy test' } },
                        { id: 11391, value: 'Business Continuity', type: { id: 10464, name: 'Risk Category 2' } },
                        { id: 11400, value: 'Financial Control', type: { id: 10464, name: 'Risk Category 2' } },
                    ],
                    totalCount: 9,
                }),
            );
    });

    // eslint-disable-next-line jest/no-disabled-tests
    it.skip('loads more data if available', async () => {
        renderSut();
        await user.type(screen.getByRole('textbox'), 'cont');
        act(() => {
            jest.advanceTimersByTime(1000);
        });
        await waitFor(() => expect(dataLoad).toHaveBeenCalledTimes(1));

        act(() => {
            mockAllIsIntersecting(true);
        });

        await waitFor(() => expect(dataLoad).toHaveBeenCalledTimes(2));
        await waitFor(() => {
            expect(screen.getAllByRole('menuitem')).toHaveLength(9);
        });
    });
});

describe('<SuggestionsInput /> without data', () => {
    const renderSut = async () => {
        render(
            <SuggestionsInput
                suggestionField={'name'}
                onDataLoad={dataLoad}
            />,
        );
        textBox = screen.getByRole('textbox');
    };

    let textBox: HTMLElement;
    let dataLoad;

    beforeEach(async () => {
        dataLoad = jest.fn(() => Promise.resolve([]));
    });

    it('show "No match"', async () => {
        await renderSut();

        await user.type(textBox, 'hTO59Y4Liw1N');
        expect(await screen.findByText('No match')).toBeInTheDocument();
    });
});

describe('<SuggestionsInput /> with adornments', () => {
    enum iconTitle {
        PLUS = 'plus icon',
        ELLIPSIS = 'ellipsis',
    }

    it('shows startAdornment and endAdornment', async () => {
        render(
            <SuggestionsInput
                suggestionField={'n/a'}
                multiselect={true}
                inputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <FontAwesomeIcon
                                icon={faPlusCircle}
                                title={iconTitle.PLUS}
                            />
                        </InputAdornment>
                    ),
                    endAdornment: (
                        <InputAdornment position="end">
                            <FontAwesomeIcon
                                icon={faEllipsisH}
                                title={iconTitle.ELLIPSIS}
                            />
                        </InputAdornment>
                    ),
                }}
            />,
        );

        await waitFor(() => {
            expect(screen.getByRole('img', { name: iconTitle.PLUS })).toBeInTheDocument();
        });
        await waitFor(() => {
            expect(screen.getByRole('img', { name: iconTitle.ELLIPSIS })).toBeInTheDocument();
        });

        expect(screen.getByRole('img', { name: iconTitle.PLUS })).toBeVisible();
        expect(screen.getByRole('img', { name: iconTitle.ELLIPSIS })).toBeVisible();
    });
});
