import React, { ChangeEvent, KeyboardEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import Popper from '@mui/material/Popper';
import { getReactRoot } from 'config';
import Paper from '@mui/material/Paper';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import ListItemText from '@mui/material/ListItemText';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import { alpha, styled, useTheme } from '@mui/material/styles';
import Highlighted from '../Highlighted';
import Box from '@mui/material/Box';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/pro-duotone-svg-icons';
import { InputProps } from '@mui/material/Input';
import { IdWithNameRest } from 'api/generated/types';
import debounce from 'lodash/debounce';
import { ColumnType } from 'register/types';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';
import ListItemIcon from '@mui/material/ListItemIcon';
import { darkBlack } from '@protecht/ui-library/library/theme/colors/colors';

const StyledMenuList = styled(MenuList)(() => ({
    overflow: 'auto',
    borderRadius: '4px',
    padding: '8px 0',
    boxShadow: '3px 3px 5px 0px #0000000',
}));

type Props<T> = {
    value?: string;
    readOnly?: boolean;
    suggestionField: string;
    multiselect?: boolean;
    inputProps?: Partial<InputProps>;
    inputPlaceholder?: string;
    onDataLoad: (value: string) => Promise<T[]>;
    onDataClear?: () => void;
    onSelect?: (selected: IdWithNameRest) => void;
    onChange?: (value: string) => void;
    containerWidth?: number;
    focused?: boolean;
    setInitialValue?: (value?: string) => void;
    displayResultsInPopper?: boolean;
    onCreateNew?: (inputValue: string | undefined) => void;
    createNewMessage?: string;
    autoFocus?: boolean;
};

export const SEARCH_RESULT_LIMIT = 5;

export const LOOKUP_SUPPORTED_FIELDS = [
    ColumnType.USER,
    ColumnType.BUSINESS_UNIT,
    ColumnType.TABLE,
    ColumnType.COUNTRY,
    ColumnType.STATE,
    ColumnType.ACTIONS,
    ColumnType.ROLE,
    ColumnType.CENTRAL_LIBRARY,
];

const InputLookup = <T extends IdWithNameRest>({
    value,
    readOnly,
    suggestionField,
    inputProps,
    inputPlaceholder,
    multiselect,
    onDataLoad,
    onDataClear,
    onSelect,
    onChange,
    containerWidth,
    focused,
    setInitialValue,
    displayResultsInPopper = true,
    onCreateNew,
    createNewMessage,
    autoFocus,
}: Props<T>) => {
    const theme = useTheme();
    const inputRef = useRef<HTMLInputElement>(null);

    const [inputValue, setInputValue] = useState<string | undefined>(value);
    const [searchResult, setSearchResult] = useState<T[]>([]);
    const [cursorIndex, setCursorIndex] = useState<number | undefined>(undefined);
    const [areResultsVisible, setAreResultsVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const handleValueChanged = useCallback(
        async (event: ChangeEvent<HTMLElement>) => {
            const val = (event.target as HTMLInputElement).value;
            setInputValue(val);

            if (val && val.length > 1) {
                setAreResultsVisible(true);

                const formattedResponse = await onDataLoad(val);

                if (formattedResponse.length === 0) {
                    setAreResultsVisible(false);
                }

                setSearchResult(formattedResponse);
            } else {
                setSearchResult([]);
                setAreResultsVisible(false);

                onDataClear?.();
            }

            setIsLoading(false);
            setInitialValue?.(val);
            onChange?.(val);
        },
        [setInitialValue, onChange, onDataLoad, onDataClear],
    );

    useEffect(() => {
        if (searchResult.length > 0 && cursorIndex === undefined && !isLoading) {
            setCursorIndex(0);
        }
    }, [cursorIndex, searchResult.length, isLoading]);

    const handleSelect = useCallback(
        (item: T) => {
            onSelect?.(item);
            setInputValue(item?.[suggestionField] || item);
            setAreResultsVisible(false);
        },
        [onSelect, suggestionField],
    );

    const handleOnKeyDown = useCallback(
        (event: KeyboardEvent) => {
            const { key } = event;

            if (key === 'Escape') {
                setAreResultsVisible(false);
            }

            if (key === 'ArrowUp') {
                if (cursorIndex === undefined) {
                    return;
                } else {
                    if (cursorIndex > 0) {
                        setCursorIndex(cursorIndex - 1);
                    } else {
                        setCursorIndex(searchResult?.length - 1);
                    }
                }
            }

            if (key === 'ArrowDown') {
                if (cursorIndex === undefined) {
                    setCursorIndex(0);
                } else {
                    if (cursorIndex < searchResult.length - 1) {
                        setCursorIndex(cursorIndex + 1);
                    } else {
                        setCursorIndex(0);
                    }
                }
            }

            if (key === 'Enter') {
                if (cursorIndex !== undefined) {
                    event.preventDefault();
                    setAreResultsVisible(false);
                    handleSelect(searchResult[cursorIndex]);
                    setCursorIndex(undefined);

                    if (multiselect) {
                        setInputValue('');
                    }
                }
            }
        },
        [cursorIndex, handleSelect, multiselect, searchResult],
    );

    const showMenuOnFocus = useCallback(() => {
        if (searchResult?.length) {
            setAreResultsVisible(true);
        }
    }, [searchResult?.length]);

    const foundExactMatch = useMemo(() => {
        return searchResult?.find((item) => item[suggestionField]?.toString()?.toLowerCase() === inputValue?.toLowerCase());
    }, [inputValue, searchResult, suggestionField]);

    const selectExactMatchOnBlur = useCallback(() => {
        if (inputValue && searchResult?.length === 1 && inputValue.toLowerCase() === searchResult[0][suggestionField]?.toString()?.toLowerCase()) {
            handleSelect(searchResult[0]);
        }
    }, [handleSelect, inputValue, searchResult, suggestionField]);

    const debouncedHandleValueChanged = useMemo(() => debounce(handleValueChanged, 300), [handleValueChanged]);

    const lookupResults = useMemo(() => {
        return (
            <StyledMenuList
                dense
                theme={theme}
            >
                {!isLoading &&
                    searchResult?.map((item, index) => {
                        const itemName = item[suggestionField].toString();
                        const escapedValue = inputValue?.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

                        return (
                            <MenuItem
                                key={`${item?.id}_${index}`}
                                onClick={() => handleSelect(item)}
                                tabIndex={-1}
                                sx={{
                                    whiteSpace: 'nowrap',
                                    height: '38px',
                                    padding: '0 10px 0 14px',
                                    '&:hover': {
                                        backgroundColor: alpha(theme.palette.protechtGrey?.black ?? darkBlack, 0.08),
                                    },
                                    '&:active': {
                                        backgroundColor: alpha(theme.palette.protechtGrey?.black ?? darkBlack, 0.15),
                                    },
                                }}
                                selected={index === cursorIndex}
                            >
                                <ListItemText
                                    sx={{
                                        '& > *': {
                                            textOverflow: 'ellipsis',
                                            overflow: 'hidden',
                                            maxWidth: '100%',
                                        },
                                    }}
                                >
                                    <Highlighted
                                        text={itemName}
                                        highlight={escapedValue}
                                        highlightStyling={{ backgroundColor: theme.palette?.accentColors?.yellow }}
                                    />
                                </ListItemText>
                            </MenuItem>
                        );
                    })}
                {onCreateNew && createNewMessage && !foundExactMatch && !isLoading && (
                    <MenuItem
                        key="create-new-item"
                        onClick={() => {
                            setAreResultsVisible(false);
                            onCreateNew(inputValue);
                        }}
                        tabIndex={-1}
                        sx={{
                            whiteSpace: 'nowrap',
                            height: '38px',
                            padding: '0 10px 0 14px',
                            '&:hover': {
                                backgroundColor: alpha(theme.palette.protechtGrey!.black!, 0.08),
                            },
                            '&:active': {
                                backgroundColor: alpha(theme.palette.protechtGrey!.black!, 0.15),
                            },
                        }}
                        selected={searchResult.length + 1 === cursorIndex}
                    >
                        <ListItemIcon>
                            <Add
                                width={20}
                                height={20}
                            />
                        </ListItemIcon>
                        <ListItemText>{createNewMessage}</ListItemText>
                    </MenuItem>
                )}
                {isLoading && (
                    <Box
                        display="flex"
                        justifyContent="center"
                    >
                        <FontAwesomeIcon
                            icon={faSpinner}
                            className={'fa-spin'}
                            color={theme.palette.protechtGrey?.grey_146}
                            width="24px"
                        />
                    </Box>
                )}
            </StyledMenuList>
        );
    }, [createNewMessage, cursorIndex, foundExactMatch, handleSelect, inputValue, isLoading, onCreateNew, searchResult, suggestionField, theme]);

    const inputWidth = inputRef.current?.getBoundingClientRect().width;

    const renderResults = useMemo(() => {
        if (displayResultsInPopper) {
            return (
                <Popper
                    style={{ width: containerWidth ? Number(containerWidth) - 10 : inputWidth }} // 10px marginLeft on input
                    open={true}
                    anchorEl={inputRef.current}
                    container={getReactRoot()}
                    placement="bottom-start"
                >
                    <Paper style={{ marginTop: '3px' }}>
                        <ClickAwayListener
                            onClickAway={() => setAreResultsVisible(false)}
                            mouseEvent={'onMouseDown'}
                        >
                            {lookupResults}
                        </ClickAwayListener>
                    </Paper>
                </Popper>
            );
        }

        return lookupResults;
    }, [containerWidth, displayResultsInPopper, inputWidth, lookupResults]);

    return (
        <>
            <Input
                value={inputValue}
                placeholder={inputPlaceholder}
                onChange={(event) => {
                    const val = (event.target as HTMLInputElement).value;

                    if (val && val.length > 1) {
                        setIsLoading(true);
                        setCursorIndex(undefined);
                    }

                    void debouncedHandleValueChanged(event);
                }}
                ref={inputRef}
                onKeyDown={handleOnKeyDown}
                clearable
                onFocus={showMenuOnFocus}
                autoFocus={autoFocus}
                onBlur={selectExactMatchOnBlur}
                focused={focused}
                InputProps={{
                    readOnly,
                    ...inputProps,
                }}
            />
            {areResultsVisible && renderResults}
        </>
    );
};

export default InputLookup;
