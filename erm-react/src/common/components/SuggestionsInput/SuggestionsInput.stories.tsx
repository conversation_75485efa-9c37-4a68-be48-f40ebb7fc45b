import React from 'react';
import { Meta, StoryFn } from '@storybook/react';
import { Provider } from 'react-redux';
import store from 'store';
import { PagingResult } from 'common/api/types';
import Box from '@mui/material/Box';
import { Title, Subtitle, Description, Primary, Controls, Stories, Source } from '@storybook/blocks';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import SuggestionsInput from './SuggestionsInput';
import { ViewExpressionRest } from 'api/generated/types';

export default {
    title: 'components/Inputs/Suggestions Input',
    component: SuggestionsInput,
    decorators: [
        (Story) => (
            <Provider store={store}>
                <Story />
            </Provider>
        ),
    ],
    parameters: {
        docs: {
            page: () => (
                <>
                    <Title />
                    <Subtitle />
                    <Description />
                    <Source
                        language="jsx"
                        dark
                        format={true}
                        code={`
                          const food = [
                            { name: 'orange', color: 'light orange' },
                            { name: 'apple', color: 'dark red' },
                            { name: 'lemon', color: 'crazy yellow' },
                            { name: 'cucumber', color: 'mint green' },
                            { name: 'kale', color: 'deep green' },
                            { name: 'banana', color: 'bright yellow' },
                            { name: 'grapes', color: 'greenish purple' },
                            { name: 'watermelon', color: 'green and red' },
                            { name: 'kiwi', color: 'green' },
                        ];
                          `}
                    />
                    <Primary />
                    <Controls />
                    <Stories />
                </>
            ),
            description: {
                component: 'Try to search within these items:',
            },
            source: {
                excludeDecorators: true,
            },
        },
    },
} as Meta<typeof SuggestionsInput>;

const Template: StoryFn<typeof SuggestionsInput> = (args) => (
    <Box sx={{ width: '300px' }}>
        <SuggestionsInput {...args} />
    </Box>
);

const food = [
    {
        name: 'orange',
        color: 'light orange',
    },
    {
        name: 'apple',
        color: 'dark red',
    },
    {
        name: 'lemon',
        color: 'crazy yellow',
    },
    {
        name: 'cucumber',
        color: 'mint green',
    },
    {
        name: 'kale',
        color: 'deep green',
    },
    {
        name: 'banana',
        color: 'bright yellow',
    },
    {
        name: 'grapes',
        color: 'greenish purple',
    },
    {
        name: 'watermelon',
        color: 'green and red',
    },
    {
        name: 'kiwi',
        color: 'green',
    },
];

const onDataLoad = (params: SearchRequestParams, expressions: ViewExpressionRest[]) => {
    const filtered = food.filter((item) => item[expressions[0].property || 'name'].includes(expressions[0].value || ''));
    const sorted = filtered.sort((a, b) =>
        a[params?.orderBy || 'name'] > b[params?.orderBy || 'name'] ? 1 : b[params?.orderBy || 'name'] > a[params?.orderBy || 'name'] ? -1 : 0,
    );
    const mockedSuggestedResults: PagingResult<{ name: string; color: string }> = {
        totalCount: sorted.length,
        records: sorted,
    };
    return new Promise((resolve) => setTimeout(() => resolve(mockedSuggestedResults), 2000));
};

export const Default: StoryFn<typeof SuggestionsInput> = Template.bind({});
Default.args = {
    inputPlaceholder: 'search by name',
    onDataLoad: onDataLoad,
    defaultExpressionProperty: 'name',
};

export const Filled: StoryFn<typeof SuggestionsInput> = Template.bind({});
Filled.args = {
    ...Default.args,
    value: food[0].name,
};

export const SuggestionField: StoryFn<typeof SuggestionsInput> = Template.bind({});
SuggestionField.args = {
    ...Default.args,
    suggestionField: 'color',
    defaultExpressionProperty: 'color',
    inputPlaceholder: 'search by color',
};
SuggestionField.parameters = {
    docs: {
        description: {
            story: 'Try searching by color',
        },
    },
};

export const OrderedBy: StoryFn<typeof SuggestionsInput> = Template.bind({});
OrderedBy.args = {
    ...SuggestionField.args,
    orderByParam: 'color',
};
OrderedBy.parameters = {
    docs: {
        description: {
            story: 'Results are ordered by color',
        },
    },
};

// add 'create new' suggestion scenario
