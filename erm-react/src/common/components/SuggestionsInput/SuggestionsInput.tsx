import React, { ChangeEvent, ReactNode, Ref, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { InputProps } from '@mui/material/Input';
import Paper from '@mui/material/Paper';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import MenuItem from '@mui/material/MenuItem';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import ListItemText from '@mui/material/ListItemText';
import Popper from '@mui/material/Popper';
import MenuList from '@mui/material/MenuList';
import IconButton from '@mui/material/IconButton';
import { IdOnly } from 'app/types';
import Input from '@protecht/ui-library/library/components/Inputs/Input';
import Loading from '../Loading';
import { getReactRoot } from 'config';
import { debounce, isEqual } from 'lodash';
import { strings } from 'common/utils/i18n';
import Highlighted from '../Highlighted';
import { LoaderType } from '../Loading/Loading';
import useAbortableQuery from 'common/hooks/useAbortableQuery/useAbortableQuery';
import useInfiniteScrollLoading from 'common/hooks/useInfiniteScrollLoading';
import { FieldPathValue } from 'react-hook-form/dist/types/path/eager';
import { FieldValues } from 'react-hook-form';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { ViewExpressionRest } from 'api/generated/types';
import { ExpressionType } from 'view/types';

type SuggestionInputProps<T> = {
    value?: string;
    onSelect?: (selected: T) => void;
    multiselect?: boolean;
    selected?: T[];
    suggestionField?: string;
    inputPlaceholder?: string;
    inputProps?: Partial<InputProps>;
    openSuggestionsIcon?: string | ReactNode;
    focused?: boolean;
    defaultValue?: string;
    onDataLoad?: (params: SearchRequestParams, expressions: ViewExpressionRest[], abortController: AbortController) => Promise<any>;
    options?: T[];
    setInitialValue?: (string) => void;
    defaultExpressionProperty?: string;
    orderByParam?: string;
    onChange?: (event?: ChangeEvent | FieldPathValue<FieldValues, string>) => void;
    readOnly?: boolean;
};

const ROWS_PER_PAGE = 6;

const SuggestionsInput = <T extends IdOnly>(props: SuggestionInputProps<T>): JSX.Element => {
    const {
        multiselect = false,
        value,
        suggestionField = 'name',
        options,
        defaultExpressionProperty,
        selected,
        inputProps,
        inputPlaceholder,
        openSuggestionsIcon,
        onDataLoad,
        onSelect,
        setInitialValue,
        focused = false,
        onChange,
        orderByParam = 'type',
        readOnly,
    } = props;
    const [inputValue, setInputValue] = useState<string | undefined>(value);
    const [isFocused, setIsFocused] = useState(focused);
    const [suggestionData, setSuggestionData] = useState<T[]>([]);
    const [suggestionsDataVisible, setSuggestionsDataVisible] = useState<boolean>(false);
    const [cursor, setCursor] = useState<number | undefined>(undefined);
    const inputRef = useRef<HTMLDivElement>(null);
    const listRef = useRef<HTMLUListElement>(null);

    useEffect(() => {
        setIsFocused(focused);
    }, [focused]);

    const [queryParams, setQueryParams] = useState<SearchRequestParams | undefined>(undefined);

    useEffect(() => {
        setQueryParams((previousParams) => {
            const newParams = {
                limit: ROWS_PER_PAGE,
                offset: 0,
                orderBy: orderByParam,
            };

            return !isEqual(previousParams, newParams) ? newParams : previousParams;
        });
    }, [orderByParam]);

    const suggestionsQuery = useCallback(
        (abortController: AbortController, page: number) => {
            if (onDataLoad && inputValue) {
                return onDataLoad(
                    { ...queryParams!, offset: page ? page * (queryParams?.limit ?? 0) : queryParams!.offset },
                    createSearchExpression(inputValue, defaultExpressionProperty),
                    abortController,
                );
            } else {
                return Promise.resolve([]);
            }
        },
        [defaultExpressionProperty, inputValue, onDataLoad, queryParams],
    );

    const abortableSuggestionsQuery = useAbortableQuery(suggestionsQuery);

    const {
        intersectionObserver,
        isLoading,
        loadedItems,
        hasMoreToLoad,
        reset: resetSuggestions,
    } = useInfiniteScrollLoading<T>(queryParams ? abortableSuggestionsQuery : undefined);

    useEffect(() => {
        if (loadedItems?.length) {
            const filteredLoadedItems = loadedItems.filter((item) => {
                if (selected) {
                    //remove already selected values
                    return !selected?.some((s) => s.id === item.id);
                }
                if (options && inputValue) {
                    //if data are passed as options, apply local filter
                    return `${item[suggestionField]}`.toLowerCase().includes(inputValue.toLowerCase());
                }
                return true;
            });
            setSuggestionData(filteredLoadedItems);
        }
    }, [loadedItems, inputValue, selected, options, suggestionField]);

    useEffect(() => {
        setInputValue(value);
        resetSuggestions();
    }, [value, resetSuggestions]);

    const createSearchExpression = (inputVal?: string, defaultExpressionProp?: string): ViewExpressionRest[] => {
        if (inputVal) {
            return [
                {
                    value: inputVal,
                    expression: ExpressionType.CONTAINS,
                    property: defaultExpressionProp,
                    type: 'STRING',
                },
            ];
        }
        return [];
    };

    const select = (selection: T | null) => {
        setInputValue(`${selection?.[suggestionField]}` || undefined);
        resetSuggestions();
    };

    const addSelected = (selection: T) => {
        setIsFocused(true);
        onSelect?.(selection);
        if (multiselect) {
            setInputValue(undefined);
            resetSuggestions();
        } else {
            select(selection);
        }
        setSuggestionsDataVisible(false);
    };

    const handleValueChanged = useMemo(
        () =>
            debounce((event: ChangeEvent<HTMLInputElement>) => {
                setCursor(undefined);
                const val = (event.target as HTMLInputElement).value || undefined;
                setInputValue(val);
                resetSuggestions();
                setSuggestionsDataVisible(val !== undefined);
                if (onChange) {
                    onChange();
                }
                if (setInitialValue) {
                    setInitialValue(val);
                }
                if (!options) {
                    setSuggestionData([]);
                }
            }, 300),
        [onChange, options, resetSuggestions, setInitialValue],
    );

    useEffect(() => {
        return () => {
            handleValueChanged.cancel();
        };
    }, [handleValueChanged]);

    const handleOnFocus = () => {
        setIsFocused(true);
    };

    const handleOnBlur = (e) => {
        setIsFocused(false);
        if (!(e.relatedTarget as HTMLInputElement)?.classList?.contains('menu-item-add-new')) {
            if (multiselect) {
                setInputValue(undefined);
                resetSuggestions();
            }
        }
    };

    useEffect(() => {
        if (listRef.current && cursor !== undefined) {
            listRef.current.children[cursor]?.scrollIntoView(true);
        }
    }, [cursor]);

    const handleOnKeyDown = (e) => {
        e.stopPropagation();
        const { keyCode } = e;
        if (keyCode === 27) {
            // escape
            setSuggestionData([]);
            setSuggestionsDataVisible(false);
        }
        if (keyCode === 38) {
            // arrow up
            if (cursor === undefined) {
                return;
            } else {
                if (cursor > 0) {
                    setCursor(cursor - 1);
                } else {
                    setCursor(undefined);
                }
            }
        }
        if (keyCode === 40) {
            // arrow down
            if (cursor === undefined) {
                setCursor(0);
            } else {
                if (cursor < suggestionData.length - 1) {
                    setCursor(cursor + 1);
                } else {
                    if (!hasMoreToLoad) {
                        setCursor(0);
                    }
                }
            }
        }
        if (keyCode === 13 && !isLoading) {
            // enter
            setSuggestionsDataVisible(false);
            if (inputValue && cursor === undefined) {
                if (multiselect) {
                    setInputValue(undefined);
                    resetSuggestions();
                }
            } else {
                if (cursor !== undefined) {
                    addSelected(suggestionData[cursor]);
                    setCursor(undefined);
                }
            }
        }
    };

    const NoMatch = () => {
        return suggestionData && suggestionData.length === 0 ? (
            <MenuItem>
                <ListItemText primary={strings('common:message.noMatch')} />
            </MenuItem>
        ) : null;
    };

    const getRef = (index: number) => {
        if (!options && suggestionData.length === index + 1) {
            return intersectionObserver as Ref<HTMLLIElement>;
        }

        return null;
    };

    return (
        <div>
            <Input
                ref={inputRef}
                value={inputValue}
                onFocus={handleOnFocus}
                onBlur={handleOnBlur}
                onKeyDown={handleOnKeyDown}
                disabled={false}
                onChange={handleValueChanged}
                placeholder={inputPlaceholder}
                InputProps={{
                    readOnly,
                    startAdornment: multiselect && !isFocused ? inputProps?.startAdornment : undefined,
                    endAdornment: (
                        <InputAdornment position="end">
                            {openSuggestionsIcon && <IconButton onClick={() => setSuggestionsDataVisible(true)}>{openSuggestionsIcon}</IconButton>}
                            {inputProps?.endAdornment}
                        </InputAdornment>
                    ),
                }}
            />
            {suggestionsDataVisible && inputRef && (
                <Popper
                    style={{ width: inputRef.current?.offsetWidth || '', maxHeight: '150px', zIndex: 1500 }}
                    open
                    anchorEl={inputRef.current}
                    container={getReactRoot()}
                >
                    <Paper>
                        <ClickAwayListener onClickAway={() => setSuggestionsDataVisible(false)}>
                            <Box>
                                <MenuList
                                    dense
                                    ref={listRef}
                                    sx={{ maxHeight: '200px', overflow: 'auto' }}
                                >
                                    {suggestionData.map((item, index) => {
                                        const itemName = item[suggestionField];
                                        const escapedValue = inputValue?.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                                        return (
                                            <MenuItem
                                                key={`${item.id}_${index}`}
                                                ref={getRef(index)}
                                                onClick={() => addSelected(item)}
                                                sx={{ whiteSpace: 'pre-wrap' }}
                                                selected={index === cursor}
                                            >
                                                <ListItemText>
                                                    <Highlighted
                                                        text={itemName}
                                                        highlight={escapedValue}
                                                    />
                                                </ListItemText>
                                            </MenuItem>
                                        );
                                    })}
                                    {isLoading ? <Loading loaderType={LoaderType.Slim} /> : <NoMatch />}
                                </MenuList>
                            </Box>
                        </ClickAwayListener>
                    </Paper>
                </Popper>
            )}
        </div>
    );
};

export default SuggestionsInput;
