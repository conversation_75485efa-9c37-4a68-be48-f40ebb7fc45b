import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React, { useMemo } from 'react';
import ScreenSettingsItem from './ScreenSettingsItem';
import Button from '@protecht/ui-library/library/components/Button';
import MetricSelector from 'common/components/MetricsSelector';
import { ExpressionType } from 'view/types';
import { DisplayFunc, MetricContextValues, Module, WorkspaceMetric } from 'metrics/types';
import { DragHandle, SortableItem, SortableList } from '@protecht/ui-library/library/components/SortableList';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { ViewExpressionRest } from 'api/generated/types';
import { FilterType, IdWithName } from '@protecht/ui-library/library/types';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

type ScreenSettingsProps = {
    title: string;
    subtitle?: string;
    addButtonText: string;
    formPropName: string;
    selectionLimit?: number;
    module: Module;
    workspaceModule: WorkspaceMetric;
};

type ItemWithId<T> = T & { originalId: number; id: string };

const ScreenSettings = <T extends IdWithName>({ title, subtitle = '', addButtonText, formPropName, selectionLimit, module }: ScreenSettingsProps) => {
    const [selectorVisible, setSelectorVisible] = React.useState(false);

    const { control } = useFormContext();
    const { fields, remove, move, replace } = useFieldArray({
        name: formPropName,
        control,
    });

    const expressions: ViewExpressionRest[] = useMemo(() => {
        return [
            ...(formPropName === MetricContextValues.vrmMetrics
                ? [
                      {
                          expression: ExpressionType.NOT_EQUAL,
                          property: 'func',
                          type: FilterType.STRING,
                          value: `${DisplayFunc.FIELD_VALUE}`,
                      },
                  ]
                : []),
            {
                expression: '=',
                property: 'source',
                value: module,
            },
        ] as ViewExpressionRest[];
    }, [formPropName, module]);

    return (
        <>
            <Grid
                container
                spacing="21px"
                padding="0 8px 8px 8px"
            >
                <Grid
                    item
                    xs={12}
                    display="flex"
                    sx={{ flexDirection: { xs: 'column', lg: 'row' } }}
                    columnGap={1}
                >
                    <Typography variant="h2">{title}</Typography>

                    <Typography
                        variant="h2"
                        fontWeight={400}
                        sx={{ display: { xs: 'none', sm: 'none', md: 'none', lg: 'block' } }}
                    >
                        -
                    </Typography>

                    <Typography
                        variant="h2"
                        fontWeight={400}
                    >
                        {subtitle}
                    </Typography>
                </Grid>
                <Grid
                    item
                    container
                    xs={12}
                    spacing="12px"
                >
                    <Grid
                        item
                        xs={12}
                    >
                        <SortableList
                            items={fields}
                            onChange={(fromIndex, toIndex) => {
                                move(fromIndex, toIndex);
                            }}
                            renderItem={(item: ItemWithId<T>, index: number) => (
                                <SortableItem
                                    key={item.id}
                                    id={item.id}
                                    styles={{
                                        display: 'flex',
                                        flex: 1,
                                        alignItems: 'center',
                                        gap: '8px',
                                    }}
                                >
                                    <ScreenSettingsItem
                                        item={item}
                                        isFirst={index === 0}
                                        isLast={index === fields.length - 1}
                                        remove={() => remove(index)}
                                        moveUp={() => move(index, index - 1)}
                                        moveDown={() => move(index, index + 1)}
                                    />
                                    <DragHandle />
                                </SortableItem>
                            )}
                        />
                    </Grid>
                </Grid>
                <Grid
                    item
                    xs={12}
                >
                    <Button
                        size="large"
                        variant="secondary"
                        startIcon={<Add data-icon="add" />}
                        onClick={() => setSelectorVisible(true)}
                        dataTestId={`add-${formPropName}`}
                    >
                        {addButtonText}
                    </Button>
                </Grid>
            </Grid>
            {selectorVisible && (
                <MetricSelector
                    selected={fields.map((field: ItemWithId<T>) => ({ id: field.originalId, name: field.name }))}
                    onSelect={(selected) => replace(selected.map((item) => ({ originalId: item.id, name: item.name })))}
                    onClose={() => setSelectorVisible(false)}
                    expressions={expressions}
                    selectionLimit={selectionLimit}
                />
            )}
        </>
    );
};

export default ScreenSettings;
