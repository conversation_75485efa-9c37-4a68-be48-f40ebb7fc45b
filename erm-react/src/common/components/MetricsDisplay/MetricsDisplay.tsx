import Typography from '@mui/material/Typography';
import BackIconButton from '@protecht/ui-library/library/components/BackIconButton';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import SettingsLayout from 'common/layouts/SettingsLayout';
import { strings } from 'common/utils/i18n';
import React, { useCallback, useEffect } from 'react';
import { generatePath, useNavigate } from 'react-router';
import { Module, WorkspaceMetric } from 'metrics/types';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { IdWithName } from '@protecht/ui-library/library/types';
import useSnackbar from 'common/hooks/useSnackbar';
import Loading from 'common/components/Loading';
import { useUnsavedChangesAlert } from 'common/hooks/useUnsavedChangesAlert';
import { DialogType } from 'common/types';
import { MetricDisplayConfig } from 'api/generated/types';
import { useWrsGetDisplayConfigurationUsingGetQuery, useWrsSetDisplayConfigurationUsingPostMutation } from 'cyberrisk/rtkApi';

export type ScreenSettingsItem = IdWithName & { originalId: number };

export const getScreenSettings = (context: string, config?: MetricDisplayConfig) => {
    return (
        config?.metricModule?.metricContexts
            ?.find((item) => item.context === context)
            ?.metricDisplays?.slice()
            .sort((metric1, metric2) => (metric1.metricOrder ?? 0) - (metric2.metricOrder ?? 0))
            .map((metric) => ({ id: metric.metricId, name: metric.metricName, originalId: metric.metricId })) || []
    );
};

export interface MetricsDisplayProps {
    module: Module;
    defaultValues: any;
    getInitialValues: (metricDisplayConfig?: MetricDisplayConfig) => Record<string, ScreenSettingsItem[]>;
    homePath: string;
    getMetricContexts: (data) => any[];
    renderSettings: () => JSX.Element;
    workspaceModule: WorkspaceMetric;
}

const MetricsDisplay: React.FC<MetricsDisplayProps> = ({
    module,
    defaultValues,
    getInitialValues,
    homePath,
    getMetricContexts,
    renderSettings,
    workspaceModule,
}) => {
    const navigate = useNavigate();
    const { enqueueSuccess, enqueueError } = useSnackbar();
    const { data: metricDisplayConfig, isFetching: isLoadingDisplayConfiguration } = useWrsGetDisplayConfigurationUsingGetQuery({ workspaceModule });
    const [setDisplayConfiguration] = useWrsSetDisplayConfigurationUsingPostMutation();
    const methods = useForm({
        mode: 'onChange',
        defaultValues,
    });

    const {
        formState: { isDirty },
        handleSubmit,
        reset,
    } = methods;

    useEffect(() => {
        const initialValues = getInitialValues(metricDisplayConfig);
        reset(initialValues);
    }, [metricDisplayConfig, reset, getInitialValues]);

    const back = useCallback(() => {
        void navigate(generatePath(homePath));
    }, [navigate, homePath]);

    const save: SubmitHandler<Record<string, ScreenSettingsItem[]>> = useCallback(
        async (data) => {
            if (isDirty) {
                const body = {
                    module,
                    metricModule: {
                        metricContexts: getMetricContexts(data),
                    },
                };
                await setDisplayConfiguration({ workspaceModule, metricDisplayConfig: body }).then((result) => {
                    if ('error' in result) {
                        enqueueError(strings('metrics:message.updateDisplayConfigurationError'));
                    } else {
                        enqueueSuccess(strings('metrics:message.updateDisplayConfigurationSuccess'));
                    }
                });
            }
        },
        [isDirty, enqueueError, enqueueSuccess, setDisplayConfiguration, getMetricContexts, module, workspaceModule],
    );

    useUnsavedChangesAlert({
        blockNavigation: isDirty,
        onConfirm: handleSubmit(save),
        dialogType: DialogType.UNSAVED,
    });

    return (
        <ApplicationLayout>
            <ToolbarContainer
                disableGutters={false}
                variant="regular"
            >
                <ToolbarGroup
                    flex={1}
                    justifyContent="space-between"
                >
                    <ToolbarGroup>
                        <BackIconButton
                            onClick={back}
                            dataTestId={'back'}
                        />
                        <Typography
                            variant="h1"
                            data-testid={'metrics-display-heading'}
                        >
                            {strings('metrics:title.metricsDisplay')}
                        </Typography>
                    </ToolbarGroup>
                    <ToolbarGroup>
                        <Button
                            {...ButtonStyles.pageToolbarButton}
                            variant={'secondary'}
                            onClick={back}
                            dataTestId="button-cancel"
                        >
                            {isDirty ? strings('common:button.cancel') : strings('common:button.close')}
                        </Button>
                        <Button
                            {...ButtonStyles.pageToolbarButton}
                            onClick={handleSubmit(save)}
                            dataTestId="button-save"
                            disabled={!isDirty}
                        >
                            {strings('common:button.save')}
                        </Button>
                    </ToolbarGroup>
                </ToolbarGroup>
            </ToolbarContainer>
            <SettingsLayout>
                <FormProvider {...methods}>{renderSettings()}</FormProvider>
            </SettingsLayout>
            {isLoadingDisplayConfiguration && <Loading />}
        </ApplicationLayout>
    );
};

export default MetricsDisplay;
