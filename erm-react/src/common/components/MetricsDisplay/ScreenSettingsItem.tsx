import { faArrowDown, faArrowUp, faTimesCircle } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import useTheme from '@mui/system/useTheme';
import { IdWithName } from '@protecht/ui-library/library/types';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import React, { useCallback } from 'react';

type ScreenSettingsItemProps<T> = {
    item: T;
    isFirst?: boolean;
    isLast?: boolean;
    remove: (item: T) => void;
    moveUp: (item: T) => void;
    moveDown: (item: T) => void;
};

const ItemContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    border: '1px solid ' + theme.palette.protechtGrey?.grey_220,
    padding: '12px 24px',
}));

const ActionsContainer = styled(Box)({
    gap: '12px',
});

const ScreenSettingsItemProps = <T extends IdWithName>({ item, isFirst = false, isLast = false, remove, moveUp, moveDown }: ScreenSettingsItemProps<T>) => {
    const theme = useTheme();

    const moveUpDisabled = isFirst;
    const moveDownDisabled = isLast;

    const removeItem = useCallback(() => {
        remove(item);
    }, [item, remove]);

    const moveItemUp = useCallback(() => {
        moveUp(item);
    }, [item, moveUp]);

    const moveItemDown = useCallback(() => {
        moveDown(item);
    }, [item, moveDown]);

    return (
        <ItemContainer>
            <Typography
                variant="body2"
                flexGrow={1}
            >
                {item.name}
            </Typography>
            <ActionsContainer>
                <IconButton
                    color="secondary"
                    onClick={removeItem}
                >
                    <FontAwesomeIcon
                        icon={faTimesCircle}
                        color={theme.palette.accentColors?.red}
                    />
                </IconButton>
                <IconButton
                    color="secondary"
                    onClick={moveItemUp}
                    disabled={moveUpDisabled}
                >
                    <FontAwesomeIcon
                        icon={faArrowUp}
                        color={moveUpDisabled ? theme.palette.protechtGrey?.grey_220 : theme.palette.primary.main}
                    />
                </IconButton>
                <IconButton
                    color="secondary"
                    onClick={moveItemDown}
                    disabled={moveDownDisabled}
                >
                    <FontAwesomeIcon
                        icon={faArrowDown}
                        color={moveDownDisabled ? theme.palette.protechtGrey?.grey_220 : theme.palette.primary.main}
                    />
                </IconButton>
            </ActionsContainer>
        </ItemContainer>
    );
};

export default ScreenSettingsItemProps;
