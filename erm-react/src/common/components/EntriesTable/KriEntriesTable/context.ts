import { createContext, useContext } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { DataGridColDef, IdOnly, SearchRequestParams } from '@protecht/ui-library/library/types';

import { PaginRestResultKeyRiskIndicatorEntryRest, ViewExpressionRest } from 'api/generated/types';

export type KriTableContext = {
    colDef: DataGridColDef[];
    columnVisibilityModel?: GridColumnVisibilityModel;
    entries: PaginRestResultKeyRiskIndicatorEntryRest | undefined;
    loadingEntries: boolean;
    searchField?: string;
    selectedEntries: IdOnly[];
    requestParams?: SearchRequestParams;
    viewColumns: DataGridColDef[];
    viewParamsLoaded: boolean;
    updateColumnVisibilityModel: (model?: GridColumnVisibilityModel) => void;
    updateSelectedEntries: (selectedEntries: IdOnly[]) => void;
    updateRequestParams: (params: SearchRequestParams) => void;
    updateSearchExpression: (expression: ViewExpressionRest | undefined) => void;
    updateSearchField: (searchField?: string) => void;
    updateViewColumns: (columns: DataGridColDef[]) => void;
    updateViewExpressions: (expressions: ViewExpressionRest[] | undefined) => void;
    updateViewParamsLoaded: (loaded: boolean) => void;
};

export const KriTableContext = createContext<KriTableContext | null>(null);

export const useKriTableContext = () => {
    const context = useContext(KriTableContext);

    if (!context) {
        throw new Error('useKriTableContext must be rendered within the KriTableContext');
    }

    return context;
};
