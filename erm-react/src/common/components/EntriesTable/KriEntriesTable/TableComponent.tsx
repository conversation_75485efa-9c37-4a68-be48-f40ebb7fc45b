import React, { FC, useMemo } from 'react';

import { KeyRiskIndicatorEntryRest } from 'api/generated/types';
import { TableComponent as SharedTableComponent, TableComponentProps } from 'common/components/EntriesTable/shared/TableComponent';
import { useKriTableContext } from './context';

type KeyRiskIndicatorEntryRestWithId = Omit<KeyRiskIndicatorEntryRest, 'id'> & { id: number };

const TableComponent: FC<TableComponentProps<KeyRiskIndicatorEntryRestWithId>> = ({ multiselect = false, onSelect, ...tableProps }) => {
    const { columnVisibilityModel, entries, loadingEntries, requestParams, viewColumns, viewParamsLoaded, updateSelectedEntries, updateRequestParams } =
        useKriTableContext();

    const tableRows = useMemo(() => (entries?.records as KeyRiskIndicatorEntryRestWithId[]) ?? [], [entries?.records]);

    const onSelectionChange = (selectedEntries: KeyRiskIndicatorEntryRestWithId[]) => {
        updateSelectedEntries(selectedEntries);
        onSelect?.(selectedEntries);
    };

    return (
        <SharedTableComponent
            {...tableProps}
            columns={viewColumns}
            columnVisibilityModel={columnVisibilityModel}
            loading={loadingEntries}
            multiselect={multiselect}
            params={requestParams}
            rows={tableRows}
            totalCount={entries?.totalCount}
            viewParamsLoaded={viewParamsLoaded}
            onSelect={onSelectionChange}
            updateRequestParams={updateRequestParams}
        />
    );
};

export default TableComponent;
