import React, { FC, PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { DataGridColDef, IdOnly, SearchRequestParams, SYSTEM_COLUMN } from '@protecht/ui-library/library/types';

import { ExpressionType } from 'view/types';
import { getApiFields } from 'common/utils/definitions';
import { KriColDef } from 'library/components/Kri/KriDefinitions';
import { KriTableContext } from './context';
import { mapParamsFields } from 'common/utils/mappings';
import { PaginRestResultKeyRiskIndicatorEntryRest, ViewExpressionRest } from 'api/generated/types';
import { useKriersGetAllKeyRiskIndicatorEntriesUsingPostMutation } from 'library/components/Kri/rtkApi';

type KriEntriesTableProps = {
    additionalExpressions?: ViewExpressionRest[];
    excludedIds?: number[];
    onLoadingChange?: (isLoading: boolean) => void;
};

const colDef = KriColDef;

const KriEntriesTable: FC<PropsWithChildren<KriEntriesTableProps>> = ({ additionalExpressions, excludedIds, children }) => {
    const [columnVisibilityModel, updateColumnVisibilityModel] = useState<GridColumnVisibilityModel | undefined>(undefined);
    const [requestParams, updateRequestParams] = useState<SearchRequestParams | undefined>({ offset: 0, limit: 7 });
    const [searchExpression, updateSearchExpression] = useState<ViewExpressionRest | undefined>(undefined);
    const [searchField, updateSearchField] = useState<string | undefined>(undefined);
    const [selectedEntries, updateSelectedEntries] = useState<IdOnly[]>([]);
    const [viewColumns, updateViewColumns] = useState<DataGridColDef[]>([]);
    const [viewExpressions, updateViewExpressions] = useState<ViewExpressionRest[] | undefined>(undefined);
    const [viewParamsLoaded, updateViewParamsLoaded] = useState<boolean>(false);

    const API_FIELDS = useMemo(() => getApiFields(colDef), []);

    const mappedParams: SearchRequestParams = useMemo(
        () => (requestParams ? mapParamsFields(requestParams, { ...API_FIELDS, qualitativeValue: 'value' }) : {}),
        [requestParams, API_FIELDS],
    );

    const { offset, limit, orderBy, orderType, viewId: _viewId } = mappedParams;

    const apiExpressions = useMemo(() => {
        const expressions: ViewExpressionRest[] = searchExpression?.value
            ? [{ ...searchExpression, property: searchExpression.property === 'qualitativeValue' ? 'value' : searchExpression.property }]
            : [];

        if (excludedIds) {
            expressions.push({
                id: 0,
                expression: ExpressionType.NOT_IN,
                property: SYSTEM_COLUMN.ID,
                type: 'STRING',
                value: excludedIds.join(';'),
            });
        }

        if (viewExpressions) {
            expressions.push(
                ...viewExpressions.map((expression) => ({
                    ...expression,
                    property: expression.property === 'qualitativeValue' ? 'value' : expression.property,
                })),
            );
        }

        if (additionalExpressions) {
            expressions.push(...additionalExpressions);
        }
        return expressions;
    }, [searchExpression, excludedIds, viewExpressions, additionalExpressions]);

    const [entries, setEntries] = useState<PaginRestResultKeyRiskIndicatorEntryRest | undefined>(undefined);
    const [getKriEntries, { isLoading: loadingEntries }] = useKriersGetAllKeyRiskIndicatorEntriesUsingPostMutation();

    const loadKriEntries = useCallback(async () => {
        if (viewParamsLoaded) {
            const entries = await getKriEntries({
                offset,
                limit,
                // viewId,
                orderBy,
                orderType: orderBy ? orderType : undefined,
                complianceKriFilterWrapperRest: { viewExpressions: apiExpressions },
                isReportMonth: false,
            }).unwrap();

            setEntries(entries);
        }
    }, [apiExpressions, getKriEntries, limit, offset, orderBy, orderType, viewParamsLoaded]);

    useEffect(() => {
        void loadKriEntries();
    }, [loadKriEntries]);

    return (
        <KriTableContext.Provider
            value={{
                colDef,
                columnVisibilityModel,
                entries,
                loadingEntries: !entries || loadingEntries,
                requestParams,
                searchField,
                selectedEntries,
                viewColumns,
                viewParamsLoaded,
                updateColumnVisibilityModel,
                updateRequestParams,
                updateSearchExpression,
                updateSearchField,
                updateSelectedEntries,
                updateViewColumns,
                updateViewExpressions,
                updateViewParamsLoaded,
            }}
        >
            {children}
        </KriTableContext.Provider>
    );
};

export default KriEntriesTable;
