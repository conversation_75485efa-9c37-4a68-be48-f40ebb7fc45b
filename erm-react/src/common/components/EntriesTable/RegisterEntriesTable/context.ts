import { createContext, useContext } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { DataGridColDef, IdOnly, SearchRequestParams } from '@protecht/ui-library/library/types';

import { PaginRestResultRegisterDataRest, TableMetadataRest, ViewExpressionRest } from 'api/generated/types';

export type RegisterTableContext = {
    colDef: DataGridColDef[];
    columnVisibilityModel?: GridColumnVisibilityModel;
    entries: PaginRestResultRegisterDataRest | undefined;
    loadingEntries: boolean;
    register?: TableMetadataRest;
    requestParams?: SearchRequestParams;
    searchField?: string;
    selectedEntries: IdOnly[];
    viewColumns: DataGridColDef[];
    viewParamsLoaded: boolean;
    updateColumnVisibilityModel: (model?: GridColumnVisibilityModel) => void;
    updateRequestParams: React.Dispatch<React.SetStateAction<SearchRequestParams | undefined>>;
    updateSelectedEntries: (selectedEntries: IdOnly[]) => void;
    updateSearchExpression: (expression: ViewExpressionRest | undefined) => void;
    updateSearchField: (searchField?: string) => void;
    updateViewColumns: (columns: DataGridColDef[]) => void;
    updateViewExpressions: (expressions: ViewExpressionRest[] | undefined) => void;
    updateViewParamsLoaded: (loaded: boolean) => void;
};

export const RegisterTableContext = createContext<RegisterTableContext | null>(null);

export const useRegisterTableContext = () => {
    const context = useContext(RegisterTableContext);

    if (!context) {
        throw new Error('useRegisterTableContext must be rendered within the RegisterTableContext');
    }

    return context;
};
