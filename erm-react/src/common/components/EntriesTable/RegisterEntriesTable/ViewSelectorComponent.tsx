import React, { FC } from 'react';

import { ViewSelectorComponent as SharedViewSelectorComponent } from 'common/components/EntriesTable/shared/ViewSelectorComponent';

import { useRegisterTableContext } from './context';

type ViewSelectorComponentProps = {
    context?: string;
    defaultOrderField: string;
    defaultOrderType: string;
    defaultSearchField: string;
    identityColumnName?: string;
    isNarrowView?: boolean;
    viewsActionsDisabled?: boolean;
};

const ViewSelectorComponent: FC<ViewSelectorComponentProps> = ({
    context,
    defaultOrderField,
    defaultOrderType,
    defaultSearchField,
    identityColumnName,
    isNarrowView,
    viewsActionsDisabled,
}) => {
    const {
        colDef,
        register,
        requestParams,
        searchField,
        updateColumnVisibilityModel,
        updateRequestParams,
        updateSearchField,
        updateViewColumns,
        updateViewExpressions,
        updateViewParamsLoaded,
    } = useRegisterTableContext();

    const registerContext = register?.tableName;

    return (
        <SharedViewSelectorComponent
            colDef={colDef}
            context={context || registerContext}
            defaultOrderField={defaultOrderField}
            defaultOrderType={defaultOrderType}
            defaultSearchField={defaultSearchField}
            identityColumnName={identityColumnName}
            isNarrowView={isNarrowView}
            requestParams={requestParams}
            searchField={searchField}
            viewsActionsDisabled={viewsActionsDisabled}
            updateColumnVisibilityModel={updateColumnVisibilityModel}
            updateRequestParams={updateRequestParams}
            updateSearchField={updateSearchField}
            updateViewColumns={updateViewColumns}
            updateViewExpressions={updateViewExpressions}
            updateViewParamsLoaded={updateViewParamsLoaded}
        />
    );
};

export default ViewSelectorComponent;
