import React, { FC, PropsWithChildren, useEffect, useMemo, useState } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { IdOnly, SearchRequestParams } from '@protecht/ui-library/library/types';

import { DataGridColDef, SYSTEM_COLUMN } from 'common/types';
import { ExpressionType } from 'view/types';
import { getApiFields } from 'common/utils/definitions';
import { getRegisterColDef } from 'register/definitions/RegisterDefinitions';
import { mapParamsFields } from 'common/utils/mappings';
import { RegisterRest } from 'register/types';
import { RegisterTableContext } from './context';
import { PaginRestResultRegisterDataRest, TableMetadataRest, ViewExpressionRest } from 'api/generated/types';
import { useGetRegisterEntriesSearchPostQuery, useTmrsGetRegisterConfigUsingGet1Query } from 'register/rtkApi';
import useSnackbar from 'common/hooks/useSnackbar';

type RegisterEntriesTableProps = {
    additionalExpressions?: ViewExpressionRest[];
    excludedIds?: number[];
    registerId: number;
    onLoadingChange?: (isLoading: boolean) => void;
    onRegisterLoaded?: (register: TableMetadataRest | undefined) => void;
    fetchData?: (requestParams?: SearchRequestParams, expressions?: ViewExpressionRest[]) => Promise<PaginRestResultRegisterDataRest | undefined>;
};

const RegisterEntriesTable: FC<PropsWithChildren<RegisterEntriesTableProps>> = ({
    additionalExpressions,
    excludedIds,
    registerId,
    onLoadingChange,
    onRegisterLoaded,
    fetchData,
    children,
}) => {
    const { data: register } = useTmrsGetRegisterConfigUsingGet1Query({ id: registerId });

    useEffect(() => {
        onRegisterLoaded?.(register);
    }, [onRegisterLoaded, register]);

    const colDef = useMemo(() => (register ? getRegisterColDef(register as RegisterRest) : []), [register]);

    const [columnVisibilityModel, updateColumnVisibilityModel] = useState<GridColumnVisibilityModel | undefined>(undefined);
    const [requestParams, updateRequestParams] = useState<SearchRequestParams | undefined>({ offset: 0, limit: 7 });
    const [searchExpression, updateSearchExpression] = useState<ViewExpressionRest | undefined>(undefined);
    const [searchField, updateSearchField] = useState<string | undefined>(undefined);
    const [viewColumns, updateViewColumns] = useState<DataGridColDef[]>([]);
    const [viewExpressions, updateViewExpressions] = useState<ViewExpressionRest[] | undefined>(undefined);
    const [viewParamsLoaded, updateViewParamsLoaded] = useState<boolean>(false);
    const [selectedEntries, updateSelectedEntries] = useState<IdOnly[]>([]);
    const [fetchedData, setFetchedData] = useState<PaginRestResultRegisterDataRest>();
    const [isFetchLoading, setIsFetchLoading] = useState<boolean>(false);
    const API_FIELDS = useMemo(() => getApiFields(colDef), [colDef]);
    const mappedParams = useMemo(() => (requestParams ? mapParamsFields(requestParams, API_FIELDS) : {}), [requestParams, API_FIELDS]);
    const { enqueueError } = useSnackbar();

    useEffect(() => {
        updateViewParamsLoaded(false);
    }, [registerId]);

    const apiExpressions = useMemo(() => {
        const expressions = searchExpression?.value ? [searchExpression] : [];

        if (excludedIds) {
            expressions.push({
                id: 0,
                expression: ExpressionType.NOT_IN,
                property: SYSTEM_COLUMN.ID,
                type: 'STRING',
                value: excludedIds.join(';'),
            });
        }

        if (viewExpressions) {
            expressions.push(...viewExpressions);
        }

        if (additionalExpressions) {
            expressions.push(...additionalExpressions);
        }
        return expressions;
    }, [searchExpression, excludedIds, viewExpressions, additionalExpressions]);

    const { data: entries, isFetching: loadingEntries } = useGetRegisterEntriesSearchPostQuery(
        {
            regId: registerId,
            ...mappedParams,
            body: apiExpressions,
        },
        { skip: !viewParamsLoaded || fetchData !== undefined, refetchOnMountOrArgChange: true },
    );

    useEffect(() => {
        if (fetchData && viewParamsLoaded) {
            setIsFetchLoading(true);
            void fetchData(mappedParams, apiExpressions)
                ?.then((data) => {
                    setFetchedData(data);
                })
                .catch((error) => {
                    setFetchedData(undefined);
                    const errorMessage = error?.message || error?.data?.message || 'Search failed';
                    enqueueError(errorMessage);
                })
                .finally(() => {
                    setIsFetchLoading(false);
                });
        }
    }, [mappedParams, apiExpressions, fetchData, viewParamsLoaded, enqueueError]);

    useEffect(() => {
        onLoadingChange?.(loadingEntries || !viewParamsLoaded);
    }, [onLoadingChange, loadingEntries, viewParamsLoaded]);

    return (
        <RegisterTableContext.Provider
            value={{
                colDef,
                columnVisibilityModel,
                entries: fetchData ? fetchedData : entries,
                loadingEntries: fetchData ? isFetchLoading : loadingEntries,
                register,
                requestParams,
                searchField,
                selectedEntries,
                viewColumns,
                viewParamsLoaded,
                updateColumnVisibilityModel,
                updateRequestParams,
                updateSearchExpression,
                updateSearchField,
                updateSelectedEntries,
                updateViewColumns,
                updateViewExpressions,
                updateViewParamsLoaded,
            }}
        >
            {children}
        </RegisterTableContext.Provider>
    );
};

export default RegisterEntriesTable;
