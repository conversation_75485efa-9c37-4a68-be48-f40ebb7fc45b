import React, { useMemo } from 'react';

import { TableComponent as SharedTableComponent, TableComponentProps } from 'common/components/EntriesTable/shared/TableComponent';
import { useRegisterTableContext } from './context';
import { getEntryTableRow } from 'register/utils';
import { RegisterEntryRest, RegisterRest } from 'register/types';
import { IdOnly } from 'app/types';

const TableComponent = <T extends IdOnly>({ multiselect = false, onSelect, ...tableProps }: TableComponentProps<T>) => {
    const {
        columnVisibilityModel,
        entries,
        loadingEntries,
        register,
        requestParams,
        viewColumns,
        viewParamsLoaded,
        updateSelectedEntries,
        updateRequestParams,
    } = useRegisterTableContext();

    const tableRows = useMemo(() => {
        return loadingEntries ? [] : entries?.records?.map((entry) => getEntryTableRow(entry.record as RegisterEntryRest, register as RegisterRest)) ?? [];
    }, [entries?.records, loadingEntries, register]);

    const onSelectionChange = (selectedEntries: T[]) => {
        updateSelectedEntries(selectedEntries);
        onSelect?.(selectedEntries);
    };

    return (
        <SharedTableComponent
            {...tableProps}
            columns={viewColumns}
            columnVisibilityModel={columnVisibilityModel}
            loading={loadingEntries}
            multiselect={multiselect}
            params={requestParams}
            rows={tableRows}
            totalCount={entries?.totalCount}
            viewParamsLoaded={viewParamsLoaded}
            onSelect={onSelectionChange}
            updateRequestParams={updateRequestParams}
        />
    );
};

export default TableComponent;
