import React, { <PERSON> } from 'react';

import { SearchByFieldComponent as SharedSearchByFieldComponent } from 'common/components/EntriesTable/shared/SearchByFieldComponent';

import { useRegisterTableContext } from './context';

type SearchByFieldComponentProps = {
    defaultSearchField: string;
};

const SearchByFieldComponent: FC<SearchByFieldComponentProps> = ({ defaultSearchField }) => {
    const { colDef, searchField, updateSearchExpression, updateSearchField } = useRegisterTableContext();

    return (
        <SharedSearchByFieldComponent
            colDef={colDef}
            defaultSearchField={defaultSearchField}
            searchField={searchField}
            updateSearchExpression={updateSearchExpression}
            updateSearchField={updateSearchField}
        />
    );
};

export default SearchByFieldComponent;
