import React, { FC } from 'react';

import { IdOnly } from '@protecht/ui-library/library/types';

import SharedOpenSelectedEntryButtonComponent from 'common/components/EntriesTable/shared/OpenSelectedEntryButtonComponent';
import { useRegisterTableContext } from './context';

type OpenSelectedEntryButtonComponentProps = {
    onClick: (selectedEntries: IdOnly[]) => void;
};

const OpenSelectedEntryButtonComponent: FC<OpenSelectedEntryButtonComponentProps> = ({ onClick }) => {
    const { selectedEntries } = useRegisterTableContext();

    return (
        <SharedOpenSelectedEntryButtonComponent
            disabled={!selectedEntries || !selectedEntries.length}
            onClick={() => onClick(selectedEntries)}
        />
    );
};

export default OpenSelectedEntryButtonComponent;
