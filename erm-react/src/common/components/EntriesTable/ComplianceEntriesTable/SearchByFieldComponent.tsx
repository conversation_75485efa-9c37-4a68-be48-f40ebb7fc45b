import React, { <PERSON> } from 'react';

import { SearchByFieldComponent as SharedSearchByFieldComponent } from 'common/components/EntriesTable/shared/SearchByFieldComponent';

import { useComplianceTableContext } from './context';

type SearchByFieldComponentProps = {
    defaultSearchField: string;
};

const SearchByFieldComponent: FC<SearchByFieldComponentProps> = ({ defaultSearchField }) => {
    const { colDef, searchField, updateSearchExpression, updateSearchField } = useComplianceTableContext();

    return (
        <SharedSearchByFieldComponent
            colDef={colDef}
            defaultSearchField={defaultSearchField}
            searchField={searchField}
            updateSearchExpression={updateSearchExpression}
            updateSearchField={updateSearchField}
        />
    );
};

export default SearchByFieldComponent;
