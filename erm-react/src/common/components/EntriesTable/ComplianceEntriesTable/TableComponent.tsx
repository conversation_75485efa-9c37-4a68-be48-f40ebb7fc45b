import React, { FC, useMemo } from 'react';

import { ComplianceEntryRest } from 'api/generated/types';
import { TableComponent as SharedTableComponent, TableComponentProps } from 'common/components/EntriesTable/shared/TableComponent';
import { useComplianceTableContext } from './context';

type ComplianceEntryRestWithId = Omit<ComplianceEntryRest, 'id'> & { id: number };

const TableComponent: FC<TableComponentProps<ComplianceEntryRestWithId>> = ({ multiselect = false, onSelect, ...tableProps }) => {
    const { columnVisibilityModel, entries, loadingEntries, requestParams, viewColumns, viewParamsLoaded, updateSelectedEntries, updateRequestParams } =
        useComplianceTableContext();

    const tableRows = useMemo(() => {
        return (entries?.records as ComplianceEntryRestWithId[]) ?? [];
    }, [entries?.records]);

    const onSelectionChange = (selectedEntries: ComplianceEntryRestWithId[]) => {
        updateSelectedEntries(selectedEntries);
        onSelect?.(selectedEntries);
    };

    return (
        <SharedTableComponent
            {...tableProps}
            columns={viewColumns}
            columnVisibilityModel={columnVisibilityModel}
            loading={loadingEntries}
            multiselect={multiselect}
            params={requestParams}
            rows={tableRows}
            totalCount={entries?.totalCount}
            viewParamsLoaded={viewParamsLoaded}
            onSelect={onSelectionChange}
            updateRequestParams={updateRequestParams}
        />
    );
};

export default TableComponent;
