import React, { FC } from 'react';

import { ViewSelectorComponent as SharedViewSelectorComponent } from 'common/components/EntriesTable/shared/ViewSelectorComponent';

import { useComplianceTableContext } from './context';

type ViewSelectorComponentProps = {
    context?: string;
    defaultOrderField: string;
    defaultOrderType: string;
    defaultSearchField: string;
    identityColumnName?: string;
    isNarrowView?: boolean;
    viewsActionsDisabled?: boolean;
};

const ViewSelectorComponent: FC<ViewSelectorComponentProps> = ({
    context,
    defaultOrderField,
    defaultOrderType,
    defaultSearchField,
    identityColumnName,
    isNarrowView,
    viewsActionsDisabled,
}) => {
    const {
        colDef,
        requestParams,
        searchField,
        updateColumnVisibilityModel,
        updateRequestParams,
        updateSearchField,
        updateViewColumns,
        updateViewExpressions,
        updateViewParamsLoaded,
    } = useComplianceTableContext();

    return (
        <SharedViewSelectorComponent
            colDef={colDef}
            context={context}
            defaultOrderField={defaultOrderField}
            defaultOrderType={defaultOrderType}
            defaultSearchField={defaultSearchField}
            identityColumnName={identityColumnName}
            isNarrowView={isNarrowView}
            requestParams={requestParams}
            searchField={searchField}
            viewsActionsDisabled={viewsActionsDisabled}
            updateColumnVisibilityModel={updateColumnVisibilityModel}
            updateRequestParams={updateRequestParams}
            updateSearchField={updateSearchField}
            updateViewColumns={updateViewColumns}
            updateViewExpressions={updateViewExpressions}
            updateViewParamsLoaded={updateViewParamsLoaded}
        />
    );
};

export default ViewSelectorComponent;
