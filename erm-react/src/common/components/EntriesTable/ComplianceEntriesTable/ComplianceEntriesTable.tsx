import React, { FC, PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { DataGridColDef, IdOnly, SearchRequestParams, SYSTEM_COLUMN } from '@protecht/ui-library/library/types';

import { ComplianceColDef } from 'library/components/Compliance/columnDefinition';
import { ComplianceTableContext } from './context';
import { ExpressionType } from 'view/types';
import { getApiFields } from 'common/utils/definitions';
import { mapParamsFields } from 'common/utils/mappings';
import { PaginRestResultComplianceEntryRest, ViewExpressionRest } from 'api/generated/types';
import { useCersGetAllViewComplianceEntriesUsingPostMutation } from 'library/components/Compliance/rtkApi';

type ComplianceEntriesTableProps = {
    additionalExpressions?: ViewExpressionRest[];
    excludedIds?: number[];
    onLoadingChange?: (isLoading: boolean) => void;
};

const colDef = ComplianceColDef;

const ComplianceEntriesTable: FC<PropsWithChildren<ComplianceEntriesTableProps>> = ({ additionalExpressions, excludedIds, children }) => {
    const [columnVisibilityModel, updateColumnVisibilityModel] = useState<GridColumnVisibilityModel | undefined>(undefined);
    const [requestParams, updateRequestParams] = useState<SearchRequestParams | undefined>({ offset: 0, limit: 7 });
    const [searchExpression, updateSearchExpression] = useState<ViewExpressionRest | undefined>(undefined);
    const [searchField, updateSearchField] = useState<string | undefined>(undefined);
    const [selectedEntries, updateSelectedEntries] = useState<IdOnly[]>([]);
    const [viewColumns, updateViewColumns] = useState<DataGridColDef[]>([]);
    const [viewExpressions, updateViewExpressions] = useState<ViewExpressionRest[] | undefined>(undefined);
    const [viewParamsLoaded, updateViewParamsLoaded] = useState<boolean>(false);

    const API_FIELDS = useMemo(() => getApiFields(colDef), []);

    const mappedParams: SearchRequestParams = useMemo(() => (requestParams ? mapParamsFields(requestParams, API_FIELDS) : {}), [requestParams, API_FIELDS]);

    const { offset, limit, orderBy, orderType, viewId: _viewId } = mappedParams;

    const apiExpressions = useMemo(() => {
        const expressions = searchExpression?.value ? [searchExpression] : [];

        if (excludedIds) {
            expressions.push({
                id: 0,
                expression: ExpressionType.NOT_IN,
                property: SYSTEM_COLUMN.ID,
                type: 'STRING',
                value: excludedIds.join(';'),
            });
        }

        if (viewExpressions) {
            expressions.push(...viewExpressions);
        }

        if (additionalExpressions) {
            expressions.push(...additionalExpressions);
        }
        return expressions;
    }, [searchExpression, excludedIds, viewExpressions, additionalExpressions]);

    const [entries, setEntries] = useState<PaginRestResultComplianceEntryRest | undefined>(undefined);
    const [getComplianceEntries, { isLoading: loadingEntries }] = useCersGetAllViewComplianceEntriesUsingPostMutation();

    const loadComplianceEntries = useCallback(async () => {
        if (viewParamsLoaded) {
            const entries = await getComplianceEntries({
                offset,
                limit,
                // viewId,
                orderBy,
                orderType: orderBy ? orderType : undefined,
                complianceKriFilterWrapperRest: { viewExpressions: apiExpressions },
                isReportMonth: false,
            }).unwrap();

            setEntries(entries);
        }
    }, [apiExpressions, getComplianceEntries, limit, offset, orderBy, orderType, viewParamsLoaded]);

    useEffect(() => {
        void loadComplianceEntries();
    }, [loadComplianceEntries]);

    return (
        <ComplianceTableContext.Provider
            value={{
                colDef,
                columnVisibilityModel,
                entries,
                loadingEntries: !entries || loadingEntries,
                requestParams,
                searchField,
                selectedEntries,
                viewColumns,
                viewParamsLoaded,
                updateColumnVisibilityModel,
                updateRequestParams,
                updateSearchExpression,
                updateSearchField,
                updateSelectedEntries,
                updateViewColumns,
                updateViewExpressions,
                updateViewParamsLoaded,
            }}
        >
            {children}
        </ComplianceTableContext.Provider>
    );
};

export default ComplianceEntriesTable;
