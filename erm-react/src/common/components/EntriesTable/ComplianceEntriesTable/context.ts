import { createContext, useContext } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { DataGridColDef, IdOnly, SearchRequestParams } from '@protecht/ui-library/library/types';

import { PaginRestResultComplianceEntryRest, ViewExpressionRest } from 'api/generated/types';

export type ComplianceTableContext = {
    colDef: DataGridColDef[];
    columnVisibilityModel?: GridColumnVisibilityModel;
    entries: PaginRestResultComplianceEntryRest | undefined;
    loadingEntries: boolean;
    searchField?: string;
    selectedEntries: IdOnly[];
    requestParams?: SearchRequestParams;
    viewColumns: DataGridColDef[];
    viewParamsLoaded: boolean;
    updateColumnVisibilityModel: (model?: GridColumnVisibilityModel) => void;
    updateSelectedEntries: (selectedEntries: IdOnly[]) => void;
    updateRequestParams: (params: SearchRequestParams) => void;
    updateSearchExpression: (expression: ViewExpressionRest | undefined) => void;
    updateSearchField: (searchField?: string) => void;
    updateViewColumns: (columns: DataGridColDef[]) => void;
    updateViewExpressions: (expressions: ViewExpressionRest[] | undefined) => void;
    updateViewParamsLoaded: (loaded: boolean) => void;
};

export const ComplianceTableContext = createContext<ComplianceTableContext | null>(null);

export const useComplianceTableContext = () => {
    const context = useContext(ComplianceTableContext);

    if (!context) {
        throw new Error('useComplianceTableContext must be rendered within the ComplianceTableContext');
    }

    return context;
};
