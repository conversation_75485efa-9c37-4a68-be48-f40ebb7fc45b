import { render, screen } from 'test/utils/rtl';
import { LibraryEntriesTable, LibraryEntriesTableContext } from '../context';
import React from 'react';
import OpenTagFilterButtonComponent from './OpenTagFilterButtonComponent';
import { strings } from 'common/utils/i18n';
import { TagFilterParams } from '@protecht/ui-library/library/types';

const onClick = jest.fn();

const renderButton = (tagFilterParams: TagFilterParams | undefined = undefined, isSmallerScreen = false) => {
    return render(
        <LibraryEntriesTableContext.Provider value={{ isSmallerScreen, tagFilterParams } as LibraryEntriesTable<any>}>
            <OpenTagFilterButtonComponent onClick={onClick} />
        </LibraryEntriesTableContext.Provider>,
    );
};

describe('<OpenTagFilterButtonComponent/>', () => {
    it('renders a button component', () => {
        renderButton();
        const button = screen.getByTestId('openTagFilter');
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent(strings('ermConstants:tags'));
        expect(document.body).toMatchSnapshot();
    });

    it('renders active filter button component when tags are selected', () => {
        renderButton({ tagIds: [1, 2, 3] });
        const button = screen.getByTestId('openTagFilter');
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent(strings('common:button.filtering'));
        expect(document.body).toMatchSnapshot();
    });

    it('calls and on click function when clicked', async () => {
        const { user } = renderButton();
        const button = screen.getByTestId('openTagFilter');
        await user.click(button);

        expect(onClick).toHaveBeenCalled();
    });
});
