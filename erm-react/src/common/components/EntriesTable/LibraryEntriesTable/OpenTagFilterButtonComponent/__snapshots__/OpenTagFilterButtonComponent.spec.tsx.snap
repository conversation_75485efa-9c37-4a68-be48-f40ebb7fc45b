// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<OpenTagFilterButtonComponent/> renders a button component 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root <PERSON><PERSON>B<PERSON><PERSON>-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-10m5r44-MuiButtonBase-root-MuiButton-root"
      data-testid="openTagFilter"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-<PERSON><PERSON><PERSON><PERSON><PERSON>-startIcon"
      >
        <svg
          data-icon="filter-outline"
          fill="currentColor"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M5.164 6.46c-.42-.613.013-1.46.768-1.46h11.223c.755 0 1.188.847.768 1.46l-4.52 6.617v4.99a.93.93 0 0 1-.932.933h-1.855a.93.93 0 0 1-.931-.933v-4.99zm.771-.526h.001l4.52 6.617c.**************.163.527v4.989h1.848v-4.989c0-.188.057-.372.163-.527l4.52-6.616.002-.001zm0-.002-.003.001H5.93h.005"
            fill="currentColor"
            fill-rule="evenodd"
          />
          <path
            d="m5.934 5.932-.002.001H5.93h.005m-.771.528C4.744 5.847 5.177 5 5.932 5h11.223c.755 0 1.188.847.768 1.46l-4.52 6.617v4.99a.93.93 0 0 1-.932.933h-1.855a.93.93 0 0 1-.931-.933v-4.99zm.771-.527h.001l4.52 6.617c.**************.163.527v4.989h1.848v-4.989c0-.188.057-.372.163-.527l4.52-6.616.002-.001H5.934Z"
            stroke="currentColor"
            stroke-width="0.5"
          />
        </svg>
      </span>
      <span
        class="css-1d0doyg"
      >
        Tags
      </span>
    </button>
  </div>
</body>
`;

exports[`<OpenTagFilterButtonComponent/> renders active filter button component when tags are selected 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-10m5r44-MuiButtonBase-root-MuiButton-root"
      data-testid="openTagFilter"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-MuiButton-startIcon"
      >
        <svg
          data-icon="filter-outline-filled"
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M5.16392 6.46072C4.74446 5.84682 5.17658 5 5.93217 5H17.1547C17.9103 5 18.3425 5.8468 17.923 6.46072L13.4025 13.0769L13.4021 13.0783V18.0667C13.4021 18.5787 12.9887 19 12.471 19H10.6158C10.0982 19 9.68469 18.5787 9.68469 18.0667V13.0783L9.6844 13.0769L5.16392 6.46072Z"
            fill="currentColor"
            fill-rule="evenodd"
            stroke="currentColor"
            stroke-width="0.5"
          />
        </svg>
      </span>
      <span
        class="css-1d0doyg"
      >
        Filtering
      </span>
    </button>
  </div>
</body>
`;
