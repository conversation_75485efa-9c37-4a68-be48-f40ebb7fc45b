import React, { FC } from 'react';
import { useLibraryEntriesTableContext } from '../context';
import Button from '@protecht/ui-library/library/components/Button';
import { FilterOutline, FilterOutlineFilled } from '@protecht/ui-library/library/components/SVGIcons';
import { strings } from 'common/utils/i18n';

type Props = {
    onClick: () => void;
};

const OpenTagFilterButtonComponent: FC<Props> = ({ onClick }) => {
    const { tagFilterParams } = useLibraryEntriesTableContext();
    const isFilterActive = tagFilterParams?.tagIds?.length ?? 0 > 0;

    return (
        <Button
            variant="secondary"
            startIcon={isFilterActive ? <FilterOutlineFilled /> : <FilterOutline />}
            sx={{ minWidth: 'auto' }}
            onClick={onClick}
            size="medium"
            hideTextOnSmallScreen={false}
            dataTestId="openTagFilter"
        >
            {isFilterActive ? strings('common:button.filtering') : strings('ermConstants:tags')}
        </Button>
    );
};

export default OpenTagFilterButtonComponent;
