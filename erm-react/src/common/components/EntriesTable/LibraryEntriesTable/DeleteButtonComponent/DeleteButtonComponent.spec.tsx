import React from 'react';
import { fireEvent } from '@testing-library/react';
import { render, screen } from 'test/utils/rtl';
import DeleteButtonComponent from './DeleteButtonComponent';

describe('DeleteButtonComponent', () => {
    describe('visibility', () => {
        it('renders nothing when visible is false', () => {
            render(<DeleteButtonComponent visible={false} />);

            expect(screen.queryByTestId('button-delete')).not.toBeInTheDocument();
        });

        it('renders icon button when visible is true', () => {
            render(<DeleteButtonComponent visible={true} />);

            expect(screen.getByTestId('button-delete')).toBeInTheDocument();
        });
    });

    describe('disabled state', () => {
        it('disables button when disabled is true', () => {
            render(<DeleteButtonComponent disabled={true} />);

            const button = screen.getByTestId('button-delete');
            expect(button).toBeDisabled();
        });

        it('enables button when disabled is false', () => {
            render(<DeleteButtonComponent disabled={false} />);

            const button = screen.getByTestId('button-delete');
            expect(button).toBeEnabled();
        });
    });

    describe('click handling', () => {
        it('fires onClick when enabled button is clicked', async () => {
            const mockOnClick = jest.fn();
            const { user } = render(
                <DeleteButtonComponent
                    onClick={mockOnClick}
                    disabled={false}
                />,
            );

            const button = screen.getByTestId('button-delete');
            await user.click(button);

            expect(mockOnClick).toHaveBeenCalledTimes(1);
        });

        it('does not fire onClick when disabled button is clicked', async () => {
            const mockOnClick = jest.fn();
            render(
                <DeleteButtonComponent
                    onClick={mockOnClick}
                    disabled={true}
                />,
            );

            const button = screen.getByTestId('button-delete');
            // Use fireEvent for disabled elements since they have pointer-events: none
            fireEvent.click(button);

            expect(mockOnClick).not.toHaveBeenCalled();
        });

        it('handles missing onClick prop gracefully', async () => {
            const { user } = render(<DeleteButtonComponent />);
            const button = screen.getByTestId('button-delete');

            await expect(user.click(button)).resolves.not.toThrow();
        });
    });
});
