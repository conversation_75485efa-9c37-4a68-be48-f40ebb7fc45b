import { useTheme } from '@mui/material/styles';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { ClearIcon } from 'common/icons/ClearIcon';
import React, { FC } from 'react';

type Props = {
    visible?: boolean;
    disabled?: boolean;
    onClick?: () => void;
};

const DeleteButtonComponent: FC<Props> = ({ visible = true, disabled = false, onClick }) => {
    const theme = useTheme();

    if (!visible) {
        return null;
    }

    return (
        <IconButton
            size="small"
            disabled={disabled}
            color={'primary'}
            data-testid="button-delete"
            onClick={onClick}
        >
            <ClearIcon
                $disabled={disabled}
                height={20}
                width={20}
                color={disabled ? theme.palette.protechtGrey?.grey_192 : theme.palette.accentColors?.red}
            />
        </IconButton>
    );
};

export default DeleteButtonComponent;
