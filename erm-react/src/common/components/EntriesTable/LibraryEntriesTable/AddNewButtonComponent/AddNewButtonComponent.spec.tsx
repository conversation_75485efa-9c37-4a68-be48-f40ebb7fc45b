import React from 'react';
import { render, screen } from 'test/utils/rtl';
import AddNewButtonComponent from './AddNewButtonComponent';
import { LibraryEntriesTableContext, LibraryEntriesTable } from '../context';

const onClick = jest.fn();

const renderButton = (tagFilterVisible = false, isSmallerScreen = false) => {
    return render(
        <LibraryEntriesTableContext.Provider value={{ isSmallerScreen } as LibraryEntriesTable<any>}>
            <AddNewButtonComponent
                filterVisible={tagFilterVisible}
                onClick={onClick}
            />
        </LibraryEntriesTableContext.Provider>,
    );
};

describe('<AddNewButton/>', () => {
    it('renders a button component', () => {
        renderButton();
        const button = screen.getByTestId('fullButton');
        expect(button).toBeInTheDocument();
        expect(document.body).toMatchSnapshot();
    });

    it('renders icon button version when tag filter is opened', () => {
        renderButton(true);
        const iconButton = screen.getByTestId('iconButton');
        expect(iconButton).toBeInTheDocument();

        const fullButton = screen.queryByTestId('fullButton');
        expect(fullButton).not.toBeInTheDocument();

        expect(document.body).toMatchSnapshot();
    });

    it('renders icon button version when screen is smaller', () => {
        renderButton(false, true);
        const iconButton = screen.getByTestId('iconButton');
        expect(iconButton).toBeInTheDocument();

        const fullButton = screen.queryByTestId('fullButton');
        expect(fullButton).not.toBeInTheDocument();

        expect(document.body).toMatchSnapshot();
    });

    it('calls and on click function when clicked', async () => {
        const { user } = renderButton();
        const button = screen.getByTestId('fullButton');
        await user.click(button);

        expect(onClick).toHaveBeenCalled();
    });
});
