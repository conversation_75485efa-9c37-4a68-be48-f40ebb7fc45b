import React, { FC } from 'react';
import Button from '@protecht/ui-library/library/components/Button';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { useLibraryEntriesTableContext } from '../context';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';
import { strings } from 'common/utils/i18n';

type Props = {
    filterVisible?: boolean;
    onClick?: () => void;
};

const AddNewButtonComponent: FC<Props> = ({ filterVisible, onClick }) => {
    const { isSmallerScreen } = useLibraryEntriesTableContext();

    return filterVisible || isSmallerScreen ? (
        <IconButton
            color="primary"
            size="small"
            onClick={() => {
                onClick?.();
                // todo
            }}
            data-testid="iconButton"
        >
            <Add
                width="20px"
                height="20px"
            />
        </IconButton>
    ) : (
        <Button
            variant="secondary"
            startIcon={<Add />}
            sx={{ minWidth: 'auto' }}
            onClick={() => {
                onClick?.();
                // todo
            }}
            size="medium"
            dataTestId="fullButton"
        >
            {strings('common:button.new')}
        </Button>
    );
};

export default AddNewButtonComponent;
