// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<AddNewButton/> renders a button component 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-13e7hkx-MuiButtonBase-root-MuiButton-root"
      data-testid="fullButton"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-MuiButton-startIcon"
      >
        <svg
          data-icon="add"
          fill="currentColor"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.5 10.75h15v2.5h-15z"
            fill="currentColor"
          />
          <path
            d="M10.75 19.5v-15h2.5v15z"
            fill="currentColor"
          />
        </svg>
      </span>
      <span
        class="css-qv0y8m"
      >
        New
      </span>
    </button>
  </div>
</body>
`;

exports[`<AddNewButton/> renders icon button version when screen is smaller 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeSmall css-v548nt-MuiButtonBase-root-MuiIconButton-root"
      data-testid="iconButton"
      tabindex="0"
      type="button"
    >
      <svg
        data-icon="add"
        fill="currentColor"
        height="20px"
        viewBox="0 0 24 24"
        width="20px"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.5 10.75h15v2.5h-15z"
          fill="currentColor"
        />
        <path
          d="M10.75 19.5v-15h2.5v15z"
          fill="currentColor"
        />
      </svg>
    </button>
  </div>
</body>
`;

exports[`<AddNewButton/> renders icon button version when tag filter is opened 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeSmall css-v548nt-MuiButtonBase-root-MuiIconButton-root"
      data-testid="iconButton"
      tabindex="0"
      type="button"
    >
      <svg
        data-icon="add"
        fill="currentColor"
        height="20px"
        viewBox="0 0 24 24"
        width="20px"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.5 10.75h15v2.5h-15z"
          fill="currentColor"
        />
        <path
          d="M10.75 19.5v-15h2.5v15z"
          fill="currentColor"
        />
      </svg>
    </button>
  </div>
</body>
`;
