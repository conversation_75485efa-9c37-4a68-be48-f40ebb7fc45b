import React, { FC } from 'react';

import { SearchByFieldComponent as SharedSearchByFieldComponent } from 'common/components/EntriesTable/shared/SearchByFieldComponent';

import { useLibraryEntriesTableContext } from './context';

type SearchByFieldComponentProps = {
    defaultSearchField: string;
    defaultSearchValue?: string;
    searchPlaceholder?: string;
    hideSelectField: boolean;
    onSearchFieldChanged?: (field?: string) => void;
    onSearchValueChanged?: (value: string) => void;
};

const SearchByFieldComponent: FC<SearchByFieldComponentProps> = ({
    defaultSearchField,
    defaultSearchValue,
    searchPlaceholder,
    hideSelectField,
    onSearchFieldChanged,
    onSearchValueChanged,
}) => {
    const { colDef, searchField, updateSearchExpression, updateSearchField } = useLibraryEntriesTableContext();

    return (
        <SharedSearchByFieldComponent
            colDef={colDef}
            defaultSearchField={defaultSearchField}
            defaultSearchValue={defaultSearchValue}
            hideSelectField={hideSelectField}
            searchField={searchField}
            searchPlaceholder={searchPlaceholder}
            onSearchFieldChanged={onSearchFieldChanged}
            onSearchValueChanged={onSearchValueChanged}
            updateSearchExpression={updateSearchExpression}
            updateSearchField={updateSearchField}
        />
    );
};

export default SearchByFieldComponent;
