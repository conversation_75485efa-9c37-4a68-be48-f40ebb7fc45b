import { createContext, useContext } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { DataGridColDef, IdOnly, SearchRequestParams, TagFilterParams } from '@protecht/ui-library/library/types';

import { ViewExpressionRest } from 'api/generated/types';

export type LibraryEntries<T extends IdOnly> = {
    totalCount?: number;
    records?: T[];
};

export type LibraryEntriesTable<T extends IdOnly> = {
    colDef: DataGridColDef[];
    columnVisibilityModel?: GridColumnVisibilityModel;
    entries: LibraryEntries<T> | undefined;
    isSmallerScreen: boolean;
    loadingEntries: boolean;
    searchField?: string;
    searchValue?: string;
    selectedEntries: T[];
    tagFilterParams?: TagFilterParams;
    requestParams?: SearchRequestParams;
    viewColumns: DataGridColDef[];
    viewParamsLoaded: boolean;
    updateColumnVisibilityModel: (model?: GridColumnVisibilityModel) => void;
    updateSelectedEntries: (selectedEntries: T[]) => void;
    updateRequestParams: React.Dispatch<React.SetStateAction<SearchRequestParams | undefined>>;
    updateSearchExpression: (expression: ViewExpressionRest | undefined) => void;
    updateSearchField: (searchField?: string) => void;
    updateSearchValue: (searchValue?: string) => void;
    updateTagFilterParams: React.Dispatch<React.SetStateAction<TagFilterParams | undefined>>;
    updateViewColumns: (columns: DataGridColDef[]) => void;
    updateViewExpressions: (expressions: ViewExpressionRest[] | undefined) => void;
    updateViewParamsLoaded: (loaded: boolean) => void;
};

export const LibraryEntriesTableContext = createContext<LibraryEntriesTable<any> | null>(null);

export function useLibraryEntriesTableContext<T extends IdOnly>() {
    const context = useContext<LibraryEntriesTable<T> | null>(LibraryEntriesTableContext);

    if (!context) {
        throw new Error('useLibraryEntriesTableContext must be rendered within the LibraryEntriesTableContext.Provider');
    }

    return context;
}
