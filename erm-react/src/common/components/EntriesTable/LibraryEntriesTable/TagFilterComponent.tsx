import React, { <PERSON> } from 'react';
import { useLibraryEntriesTableContext } from './context';
import TagFilter from 'common/components/TagFilter';
import { TagContext, TagFilterParams } from '@protecht/ui-library/library/types';

type Props = {
    tagContext: TagContext;
    onTagFilterChanged?: (tagFilter: TagFilterParams) => void;
};

const TagFilterComponent: FC<Props> = ({ tagContext, onTagFilterChanged }) => {
    const { tagFilterParams, updateTagFilterParams } = useLibraryEntriesTableContext();

    return (
        <TagFilter
            tagContext={tagContext}
            selectedOperator={tagFilterParams?.tagOperator}
            selectedTagIds={tagFilterParams?.tagIds}
            onChange={(tagFilter: TagFilterParams) => {
                updateTagFilterParams((previous) => ({ ...previous, ...tagFilter }));
                onTagFilterChanged?.(tagFilter);
            }}
        />
    );
};

export default TagFilterComponent;
