import React, { FC } from 'react';

import { ViewSelectorComponent as SharedViewSelectorComponent } from 'common/components/EntriesTable/shared/ViewSelectorComponent';

import { useLibraryEntriesTableContext } from './context';
import { ViewRest } from 'api/generated/types';
import { ShowAll } from 'app/types';

type ViewSelectorComponentProps = {
    context?: string;
    defaultOrderField: string;
    defaultOrderType: string;
    defaultSearchField: string;
    defaultSelectedView?: ViewRest | ShowAll | undefined;
    identityColumnName?: string;
    isNarrowView?: boolean;
    viewsActionsDisabled?: boolean;
    onViewSelected?: (selectedView: ViewRest | ShowAll | undefined) => void;
};

const ViewSelectorComponent: FC<ViewSelectorComponentProps> = ({
    context,
    defaultOrderField,
    defaultOrderType,
    defaultSearchField,
    defaultSelectedView,
    identityColumnName,
    isNarrowView,
    viewsActionsDisabled,
    onViewSelected,
}) => {
    const {
        colDef,
        requestParams,
        searchField,
        updateColumnVisibilityModel,
        updateRequestParams,
        updateSearchField,
        updateViewColumns,
        updateViewExpressions,
        updateViewParamsLoaded,
    } = useLibraryEntriesTableContext();

    return (
        <SharedViewSelectorComponent
            colDef={colDef}
            context={context}
            defaultOrderField={defaultOrderField}
            defaultOrderType={defaultOrderType}
            defaultSearchField={defaultSearchField}
            defaultSelectedView={defaultSelectedView}
            identityColumnName={identityColumnName}
            isNarrowView={isNarrowView}
            requestParams={requestParams}
            searchField={searchField}
            viewsActionsDisabled={viewsActionsDisabled}
            onViewSelected={onViewSelected}
            updateColumnVisibilityModel={updateColumnVisibilityModel}
            updateRequestParams={updateRequestParams}
            updateSearchField={updateSearchField}
            updateViewColumns={updateViewColumns}
            updateViewExpressions={updateViewExpressions}
            updateViewParamsLoaded={updateViewParamsLoaded}
        />
    );
};

export default ViewSelectorComponent;
