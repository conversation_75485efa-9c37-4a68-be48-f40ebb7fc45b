import React, { FC } from 'react';
// import { useLibraryEntriesTableContext } from '../context';
import Button from '@protecht/ui-library/library/components/Button';
import { FilterOutline, FilterOutlineFilled } from '@protecht/ui-library/library/components/SVGIcons';
import { strings } from 'common/utils/i18n';

type Props = {
    onClick: () => void;
};

const OpenCategoryFilterButtonComponent: FC<Props> = ({ onClick }) => {
    // const { categoryFilterParams } = useLibraryEntriesTableContext(); // todo
    // const isFilterActive = categoryFilterParams?.categoryIds?.length ?? 0 > 0; // todo
    const isFilterActive = false;

    return (
        <Button
            variant="secondary"
            startIcon={isFilterActive ? <FilterOutlineFilled /> : <FilterOutline />}
            sx={{ minWidth: 'auto' }}
            onClick={onClick}
            size="medium"
            hideTextOnSmallScreen={false}
            dataTestId="openCategoryFilter"
        >
            {isFilterActive ? strings('common:button.filtering') : strings('common:button.categories')}
        </Button>
    );
};

export default OpenCategoryFilterButtonComponent;
