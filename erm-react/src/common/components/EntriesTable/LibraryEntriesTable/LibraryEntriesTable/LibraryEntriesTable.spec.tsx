import React, { useEffect } from 'react';
import { render, screen } from 'test/utils/rtl';
import LibraryEntriesTable from './LibraryEntriesTable';
import { useLibraryEntriesTableContext } from '../context';
import { SearchRequestParams, TagFilterParams, TagOperator } from '@protecht/ui-library/library/types';

const loadEntries = jest.fn();

const TestChildComponent = () => {
    const { updateSearchExpression, updateViewParamsLoaded } = useLibraryEntriesTableContext();

    useEffect(() => {
        updateViewParamsLoaded(true);
        updateSearchExpression({
            property: 'description',
            value: 'test description',
            expression: 'contains',
            type: 'string',
        });
    }, [updateSearchExpression, updateViewParamsLoaded]);

    return <div data-testid="testComponent">test component</div>;
};

const renderLibraryEntriesTable = (
    isFetching = false,
    compositeSearch = false,
    defaultRequestParams: SearchRequestParams | undefined = undefined,
    defaultTagFilterParams: TagFilterParams | undefined = undefined,
) => {
    return render(
        <LibraryEntriesTable
            colDef={[]}
            isFetching={isFetching}
            loadEntries={loadEntries}
            compositeSearch={compositeSearch}
            defaultRequestParams={defaultRequestParams}
            defaultTagFilterParams={defaultTagFilterParams}
        >
            <TestChildComponent />
        </LibraryEntriesTable>,
    );
};

describe('<LibraryEntriesTable/>', () => {
    it('renders children wrapped in LibraryEntriesTableContext.Provider', () => {
        renderLibraryEntriesTable();
        const testChildComponent = screen.getByTestId('testComponent');
        expect(testChildComponent).toBeInTheDocument();
    });

    it('uses composite search on smaller screen', () => {
        renderLibraryEntriesTable(false, true);
        expect(loadEntries).toHaveBeenCalledWith({}, expect.arrayContaining([expect.objectContaining({ property: 'name' })]));
    });

    it('uses regular search on bigger screen', () => {
        renderLibraryEntriesTable(false, false);
        expect(loadEntries).toHaveBeenCalledWith({}, expect.arrayContaining([expect.objectContaining({ property: 'description' })]));
    });

    it('uses default request params when provided', () => {
        const defaultRequestParams = {
            offset: 2,
            limit: 20,
            viewId: 123,
        };
        renderLibraryEntriesTable(false, false, defaultRequestParams);
        expect(loadEntries).toHaveBeenCalledWith(expect.objectContaining(defaultRequestParams), [expect.objectContaining({ property: 'description' })]);
    });

    it('uses default tag filter params when provided', () => {
        const defaultRequestParams = {
            offset: 2,
            limit: 20,
            viewId: 123,
        };

        const defaultTagFilterParams = {
            tagOperator: TagOperator.And,
            tagIds: [1, 2, 3],
        };
        renderLibraryEntriesTable(false, false, defaultRequestParams, defaultTagFilterParams);
        expect(loadEntries).toHaveBeenCalledWith(expect.objectContaining(defaultTagFilterParams), [expect.objectContaining({ property: 'description' })]);
    });
});
