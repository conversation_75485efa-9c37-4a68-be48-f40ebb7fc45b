import React, { PropsWithChildren, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

import { IdOnly, SearchRequestParams, TagFilterParams } from '@protecht/ui-library/library/types';

import { DataGridColDef } from 'common/types';
import { getApiFields } from 'common/utils/definitions';
import { mapParamsFields } from 'common/utils/mappings';
import { LibraryEntries, LibraryEntriesTableContext } from '../context';
import { ViewExpressionRest } from 'api/generated/types';

type RiskCauseEntriesTableProps<T extends IdOnly> = {
    additionalExpressions?: ViewExpressionRest[];
    colDef: DataGridColDef[];
    compositeSearch?: boolean;
    defaultRequestParams?: SearchRequestParams;
    defaultTagFilterParams?: TagFilterParams;
    isFetching: boolean;
    loadEntries: (searchRequestParams: SearchRequestParams, viewExpressions: ViewExpressionRest[]) => Promise<LibraryEntries<T> | null>;
    onLoadingChange?: (isLoading: boolean) => void;
};

const LibraryEntriesTable = <T extends IdOnly>({
    additionalExpressions,
    colDef,
    compositeSearch,
    defaultRequestParams,
    defaultTagFilterParams,
    loadEntries,
    isFetching,
    onLoadingChange,
    children,
}: PropsWithChildren<RiskCauseEntriesTableProps<T>>) => {
    const [columnVisibilityModel, updateColumnVisibilityModel] = useState<GridColumnVisibilityModel | undefined>(undefined);
    const [requestParams, updateRequestParams] = useState<SearchRequestParams | undefined>(defaultRequestParams);
    const [tagFilterParams, updateTagFilterParams] = useState<SearchRequestParams | undefined>(defaultTagFilterParams);
    const [searchExpression, updateSearchExpression] = useState<ViewExpressionRest | undefined>(undefined);
    const [searchField, updateSearchField] = useState<string | undefined>(undefined);
    const [searchValue, updateSearchValue] = useState<string | undefined>(undefined);
    const [viewColumns, updateViewColumns] = useState<DataGridColDef[]>([]);
    const [viewExpressions, updateViewExpressions] = useState<ViewExpressionRest[] | undefined>(undefined);
    const [viewParamsLoaded, updateViewParamsLoaded] = useState<boolean>(false);
    const [selectedEntries, updateSelectedEntries] = useState<T[]>([]);

    const theme = useTheme();
    const isSmallerScreen = useMediaQuery(theme.breakpoints.down('sm'));

    const API_FIELDS = useMemo(() => getApiFields(colDef), [colDef]);
    const mappedParams = useMemo(
        () => (requestParams ? mapParamsFields({ ...requestParams, ...tagFilterParams }, API_FIELDS) : {}),
        [requestParams, tagFilterParams, API_FIELDS],
    );

    const compositeSearchRef = useRef(compositeSearch);
    useEffect(() => {
        compositeSearchRef.current = compositeSearch;
    }, [compositeSearch]);

    const apiExpressions = useMemo(() => {
        if (compositeSearchRef.current) {
            // todo implement later when BE supports composite search: one search string is used to search within multiple columns
            // for now: searching only within 'name' column
            const expressions = [];

            if (searchExpression?.value) {
                updateSearchField('name');
                return [{ ...searchExpression, property: 'name' }];
            }

            return expressions;
        } else {
            const expressions = searchExpression?.value ? [searchExpression] : [];

            if (viewExpressions) {
                expressions.push(...viewExpressions);
            }

            if (additionalExpressions) {
                expressions.push(...additionalExpressions);
            }

            return expressions;
        }
    }, [searchExpression, viewExpressions, additionalExpressions]);

    const { offset, limit, orderBy, orderType, viewId, tagIds, tagOperator } = mappedParams;

    const [entries, setEntries] = useState<LibraryEntries<T>>();

    const loadLibraryEntries = useCallback(async () => {
        if (viewParamsLoaded) {
            const entries = await loadEntries(
                {
                    offset,
                    limit,
                    viewId,
                    orderBy,
                    orderType: orderBy ? orderType : undefined,
                    tagIds,
                    tagOperator,
                },
                apiExpressions,
            );

            if (entries) {
                setEntries(entries);
            }
        }
    }, [viewParamsLoaded, loadEntries, offset, limit, viewId, orderBy, orderType, tagIds, tagOperator, apiExpressions]);

    useEffect(() => {
        void loadLibraryEntries();
    }, [loadLibraryEntries]);

    useEffect(() => {
        onLoadingChange?.(isFetching || !viewParamsLoaded);
    }, [onLoadingChange, isFetching, viewParamsLoaded]);

    return (
        <LibraryEntriesTableContext.Provider
            value={{
                colDef,
                columnVisibilityModel,
                entries,
                isSmallerScreen,
                loadingEntries: isFetching,
                requestParams,
                searchField,
                searchValue,
                selectedEntries,
                tagFilterParams,
                viewColumns,
                viewParamsLoaded,
                updateColumnVisibilityModel,
                updateRequestParams,
                updateSearchExpression,
                updateSearchField,
                updateSearchValue,
                updateSelectedEntries,
                updateTagFilterParams,
                updateViewColumns,
                updateViewExpressions,
                updateViewParamsLoaded,
            }}
        >
            {children}
        </LibraryEntriesTableContext.Provider>
    );
};

export default LibraryEntriesTable;
