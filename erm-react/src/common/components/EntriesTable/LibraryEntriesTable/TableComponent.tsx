import React, { useMemo } from 'react';
import { TableComponent as SharedTableComponent, TableComponentProps } from 'common/components/EntriesTable/shared/TableComponent';
import { useLibraryEntriesTableContext } from './context';
import { IdOnly } from 'app/types';

const TableComponent = <T extends IdOnly>({ multiselect = false, onSelect, ...tableProps }: TableComponentProps<T>) => {
    const { columnVisibilityModel, entries, loadingEntries, requestParams, viewColumns, viewParamsLoaded, updateSelectedEntries, updateRequestParams } =
        useLibraryEntriesTableContext<T>();

    const tableRows = useMemo(() => entries?.records ?? [], [entries?.records]);

    const onSelectionChange = (selectedEntries: T[]) => {
        updateSelectedEntries(selectedEntries);
        onSelect?.(selectedEntries);
    };

    return (
        <SharedTableComponent
            {...tableProps}
            columns={viewColumns}
            columnVisibilityModel={columnVisibilityModel}
            loading={loadingEntries}
            multiselect={multiselect}
            params={requestParams}
            rows={tableRows}
            totalCount={entries?.totalCount}
            viewParamsLoaded={viewParamsLoaded}
            onSelect={onSelectionChange}
            updateRequestParams={updateRequestParams}
        />
    );
};

export default TableComponent;
