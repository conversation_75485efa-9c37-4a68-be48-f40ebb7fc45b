import React from 'react';
import { render, screen } from 'test/utils/rtl';
import SelectButtonComponent from './SelectButtonComponent';
import { LibraryEntriesTableContext, LibraryEntriesTable } from '../context';
import { fireEvent } from '@testing-library/react';

const onClick = jest.fn();

const renderButton = (isSmallerScreen = false, disabled = false, onClickHandler?: () => void) => {
    return render(
        <LibraryEntriesTableContext.Provider value={{ isSmallerScreen } as LibraryEntriesTable<any>}>
            <SelectButtonComponent
                onClick={onClickHandler ?? onClick}
                disabled={disabled}
            />
        </LibraryEntriesTableContext.Provider>,
    );
};

describe('<SelectButtonComponent/>', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('rendering', () => {
        it('renders a button component when screen is larger', () => {
            renderButton();
            const button = screen.getByTestId('fullButton');
            expect(button).toBeInTheDocument();
            expect(document.body).toMatchSnapshot();
        });

        it('renders icon button version when screen is smaller', () => {
            renderButton(true);
            const iconButton = screen.getByTestId('iconButton');
            expect(iconButton).toBeInTheDocument();

            const fullButton = screen.queryByTestId('fullButton');
            expect(fullButton).not.toBeInTheDocument();

            expect(document.body).toMatchSnapshot();
        });
    });

    describe('disabled state', () => {
        it('disables full button when disabled is true', () => {
            renderButton(false, true);

            const button = screen.getByTestId('fullButton');
            expect(button).toBeDisabled();
        });

        it('enables full button when disabled is false', () => {
            renderButton(false, false);

            const button = screen.getByTestId('fullButton');
            expect(button).toBeEnabled();
        });
        it('disables icon button when disabled is true', () => {
            renderButton(true, true);

            const button = screen.getByTestId('iconButton');
            expect(button).toBeDisabled();
        });

        it('enables icon button when disabled is false', () => {
            renderButton(true, false);

            const button = screen.getByTestId('iconButton');
            expect(button).toBeEnabled();
        });
    });

    describe('click handling', () => {
        it('fires onClick when enabled button is clicked', async () => {
            const { user } = renderButton();

            const button = screen.getByTestId('fullButton');
            await user.click(button);

            expect(onClick).toHaveBeenCalledTimes(1);
        });

        it('does not fire onClick when disabled button is clicked', async () => {
            renderButton(false, true);

            const button = screen.getByTestId('fullButton');
            // Use fireEvent for disabled elements since they have pointer-events: none
            fireEvent.click(button);

            expect(onClick).not.toHaveBeenCalled();
        });

        it('handles missing onClick prop gracefully', async () => {
            const { user } = renderButton(false, false, undefined);
            const button = screen.getByTestId('fullButton');

            await expect(user.click(button)).resolves.not.toThrow();
        });
    });
});
