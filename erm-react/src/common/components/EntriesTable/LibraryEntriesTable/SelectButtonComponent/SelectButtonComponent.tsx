import React, { FC } from 'react';
import Button from '@protecht/ui-library/library/components/Button';
import IconButton from '@protecht/ui-library/library/components/IconButton';
import { useLibraryEntriesTableContext } from '../context';
import { Link } from '@protecht/ui-library/library/components/SVGIcons';
import { strings } from 'common/utils/i18n';

type Props = {
    disabled?: boolean;
    onClick?: () => void;
};

const SelectButtonComponent: FC<Props> = ({ onClick, disabled }) => {
    const { isSmallerScreen } = useLibraryEntriesTableContext();

    return isSmallerScreen ? (
        <IconButton
            disabled={disabled}
            color="primary"
            size="small"
            onClick={onClick}
            data-testid="iconButton"
        >
            <Link />
        </IconButton>
    ) : (
        <Button
            variant="secondary"
            disabled={disabled}
            startIcon={<Link />}
            sx={{ minWidth: 'auto' }}
            onClick={onClick}
            size="medium"
            dataTestId="fullButton"
        >
            {strings('common:button.add')}
        </Button>
    );
};

export default SelectButtonComponent;
