// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SelectButtonComponent/> rendering renders a button component when screen is larger 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root <PERSON><PERSON>Button-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-13e7hkx-MuiButtonBase-root-MuiButton-root"
      data-testid="fullButton"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-gcc2o7-<PERSON><PERSON><PERSON><PERSON><PERSON>-startIcon"
      >
        <svg
          data-icon="link"
          fill="currentColor"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M14.0587 9.94135C13.6716 9.55425 13.2493 9.27273 12.7918 9.02639C12.6158 8.95601 12.4399 8.9912 12.2991 9.13196L12.1584 9.27273C11.8416 9.55425 11.6657 9.94135 11.6305 10.3284C11.5953 10.5044 11.7009 10.6804 11.8416 10.7859C12.088 10.8915 12.4751 11.1378 12.651 11.349C13.8123 12.5103 13.8123 14.3754 12.651 15.5367L10.0117 18.176C8.85044 19.3372 6.98534 19.3372 5.82405 18.176C4.66276 17.0147 4.66276 15.1496 5.82405 13.9883L7.44282 12.3695C7.54839 12.2639 7.58358 12.1232 7.54839 11.9824C7.47801 11.6305 7.40762 11.0323 7.37243 10.6452C7.37243 10.2933 6.91496 10.1173 6.66862 10.3636C6.24633 10.7859 5.57771 11.4545 4.45161 12.5806C2.51613 14.5161 2.51613 17.6481 4.45161 19.5484C6.35191 21.4839 9.48387 21.4839 11.4194 19.5484C14.305 16.6628 14.1642 16.8035 14.3754 16.522C15.9589 14.6217 15.8534 11.7361 14.0587 9.94135ZM19.5484 4.45161C17.6481 2.51613 14.5161 2.51613 12.5806 4.45161C9.69501 7.33724 9.83578 7.19648 9.62463 7.47801C8.04106 9.3783 8.14663 12.2639 9.94135 14.0587C10.3284 14.4457 10.7507 14.7273 11.2082 14.9736C11.3842 15.044 11.5601 15.0088 11.7009 14.868L11.8416 14.7273C12.1584 14.4457 12.3343 14.0587 12.3695 13.6716C12.4047 13.4956 12.2991 13.3196 12.1584 13.2141C11.912 13.1085 11.5249 12.8622 11.349 12.651C10.1877 11.4897 10.1877 9.62463 11.349 8.46334L13.9883 5.82405C15.1496 4.66276 17.0147 4.66276 18.176 5.82405C19.3372 6.98534 19.3372 8.85044 18.176 10.0117L16.5572 11.6305C16.4516 11.7361 16.4164 11.8768 16.4516 12.0176C16.522 12.3695 16.5924 12.9677 16.6276 13.3548C16.6276 13.7067 17.085 13.8827 17.3314 13.6364C17.7537 13.2141 18.4223 12.5455 19.5484 11.4194C21.4839 9.48387 21.4839 6.35191 19.5484 4.45161Z"
            fill="currentColor"
          />
        </svg>
      </span>
      <span
        class="css-qv0y8m"
      >
        Add
      </span>
    </button>
  </div>
</body>
`;

exports[`<SelectButtonComponent/> rendering renders icon button version when screen is smaller 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorPrimary MuiIconButton-sizeSmall css-v548nt-MuiButtonBase-root-MuiIconButton-root"
      data-testid="iconButton"
      tabindex="0"
      type="button"
    >
      <svg
        data-icon="link"
        fill="currentColor"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M14.0587 9.94135C13.6716 9.55425 13.2493 9.27273 12.7918 9.02639C12.6158 8.95601 12.4399 8.9912 12.2991 9.13196L12.1584 9.27273C11.8416 9.55425 11.6657 9.94135 11.6305 10.3284C11.5953 10.5044 11.7009 10.6804 11.8416 10.7859C12.088 10.8915 12.4751 11.1378 12.651 11.349C13.8123 12.5103 13.8123 14.3754 12.651 15.5367L10.0117 18.176C8.85044 19.3372 6.98534 19.3372 5.82405 18.176C4.66276 17.0147 4.66276 15.1496 5.82405 13.9883L7.44282 12.3695C7.54839 12.2639 7.58358 12.1232 7.54839 11.9824C7.47801 11.6305 7.40762 11.0323 7.37243 10.6452C7.37243 10.2933 6.91496 10.1173 6.66862 10.3636C6.24633 10.7859 5.57771 11.4545 4.45161 12.5806C2.51613 14.5161 2.51613 17.6481 4.45161 19.5484C6.35191 21.4839 9.48387 21.4839 11.4194 19.5484C14.305 16.6628 14.1642 16.8035 14.3754 16.522C15.9589 14.6217 15.8534 11.7361 14.0587 9.94135ZM19.5484 4.45161C17.6481 2.51613 14.5161 2.51613 12.5806 4.45161C9.69501 7.33724 9.83578 7.19648 9.62463 7.47801C8.04106 9.3783 8.14663 12.2639 9.94135 14.0587C10.3284 14.4457 10.7507 14.7273 11.2082 14.9736C11.3842 15.044 11.5601 15.0088 11.7009 14.868L11.8416 14.7273C12.1584 14.4457 12.3343 14.0587 12.3695 13.6716C12.4047 13.4956 12.2991 13.3196 12.1584 13.2141C11.912 13.1085 11.5249 12.8622 11.349 12.651C10.1877 11.4897 10.1877 9.62463 11.349 8.46334L13.9883 5.82405C15.1496 4.66276 17.0147 4.66276 18.176 5.82405C19.3372 6.98534 19.3372 8.85044 18.176 10.0117L16.5572 11.6305C16.4516 11.7361 16.4164 11.8768 16.4516 12.0176C16.522 12.3695 16.5924 12.9677 16.6276 13.3548C16.6276 13.7067 17.085 13.8827 17.3314 13.6364C17.7537 13.2141 18.4223 12.5455 19.5484 11.4194C21.4839 9.48387 21.4839 6.35191 19.5484 4.45161Z"
          fill="currentColor"
        />
      </svg>
    </button>
  </div>
</body>
`;
