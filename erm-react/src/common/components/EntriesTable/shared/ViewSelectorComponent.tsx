import React, { FC, useCallback, useEffect, useState } from 'react';

import { GridColumnVisibilityModel } from '@mui/x-data-grid';

import { DataGridColDef, SearchRequestParams } from '@protecht/ui-library/library/types';

import { ShowAll } from 'app/types';
import { SYSTEM_COLUMN } from 'common/types';
import { useView } from 'view/hooks/useView';
import { ViewExpressionRest, ViewRest } from 'api/generated/types';
import ViewSelector, { SHOW_ALL } from 'view/components/ViewSelector/ViewSelector';

type ViewSelectorComponentProps = {
    colDef: DataGridColDef[];
    context?: string;
    defaultOrderField: string;
    defaultOrderType: string;
    defaultSearchField: string;
    defaultSelectedView?: ViewRest | ShowAll | undefined;
    identityColumnName?: string;
    isNarrowView?: boolean;
    requestParams?: SearchRequestParams;
    searchField?: string;
    viewsActionsDisabled?: boolean;
    onViewSelected?: (selectedView: ViewRest | ShowAll | undefined) => void;
    updateColumnVisibilityModel: (model?: GridColumnVisibilityModel) => void;
    updateRequestParams: (params: SearchRequestParams) => void;
    updateSearchField: (searchField?: string) => void;
    updateViewColumns: (columns: DataGridColDef[]) => void;
    updateViewExpressions: (expressions: ViewExpressionRest[] | undefined) => void;
    updateViewParamsLoaded: (loaded: boolean) => void;
};

export const ViewSelectorComponent: FC<ViewSelectorComponentProps> = ({
    colDef,
    context,
    defaultOrderField,
    defaultOrderType,
    defaultSearchField,
    defaultSelectedView,
    identityColumnName,
    isNarrowView,
    searchField,
    requestParams,
    viewsActionsDisabled,
    onViewSelected,
    updateColumnVisibilityModel,
    updateRequestParams,
    updateSearchField,
    updateViewColumns,
    updateViewExpressions,
    updateViewParamsLoaded,
}) => {
    const [selectedView, setSelectedView] = useState<ViewRest | ShowAll | undefined>(defaultSelectedView);

    const { columns, columnVisibilityModel, viewParams, defaultViewSearchProperty, viewExpressions } = useView(
        colDef,
        requestParams,
        isNarrowView ? SHOW_ALL : selectedView,
    );

    const [viewContext, setViewContext] = useState<string | undefined>();

    useEffect(() => {
        setViewContext((previous) => {
            if (previous) {
                setSelectedView(undefined);
            }
            return context;
        });
    }, [context]);

    useEffect(() => {
        if (viewParams !== undefined) {
            updateViewParamsLoaded(true);
            updateRequestParams(viewParams);
            updateViewExpressions(viewExpressions);
        }
    }, [viewParams, viewExpressions, updateViewParamsLoaded, updateViewExpressions, updateRequestParams]);

    useEffect(() => {
        if (defaultViewSearchProperty) {
            updateSearchField(defaultViewSearchProperty);
        }
    }, [defaultViewSearchProperty, updateSearchField]);

    useEffect(() => {
        updateColumnVisibilityModel(columnVisibilityModel);
    }, [columnVisibilityModel, updateColumnVisibilityModel]);

    useEffect(() => {
        if (isNarrowView) {
            updateViewColumns(
                columns.map((column) => ({
                    ...column,
                    hidden: column.field !== (identityColumnName ?? SYSTEM_COLUMN.ID),
                })),
            );
        } else {
            updateViewColumns(columns);
        }
    }, [columns, identityColumnName, isNarrowView, updateViewColumns]);

    useEffect(() => {
        updateViewExpressions(viewExpressions);
    }, [updateViewExpressions, viewExpressions]);

    const handleSelectView = useCallback(
        (view: ViewRest | ShowAll | undefined) => {
            setSelectedView(view);
            onViewSelected?.(view);
        },
        [onViewSelected],
    );

    return (
        <ViewSelector
            columns={colDef}
            context={viewContext || ''}
            defaultFilter={searchField || defaultSearchField}
            defaultOrderBy={requestParams?.orderBy || defaultOrderField}
            defaultOrderType={requestParams?.orderType || defaultOrderType}
            editDisabled={viewsActionsDisabled}
            hide={isNarrowView}
            key="selectView"
            selectedView={isNarrowView ? SHOW_ALL : selectedView}
            onViewSelected={handleSelectView}
        />
    );
};
