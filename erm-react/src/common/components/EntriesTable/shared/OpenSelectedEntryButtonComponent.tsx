import React, { FC } from 'react';

import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';

import { strings } from 'common/utils/i18n';

type OpenSelectedEntryButtonComponentProps = {
    disabled?: boolean;
    onClick: () => void;
};

const OpenSelectedEntryButtonComponent: FC<OpenSelectedEntryButtonComponentProps> = ({ disabled, onClick }) => {
    return (
        <Button
            {...ButtonStyles.tableToolbarButton}
            dataTestId="button-open"
            disabled={disabled}
            variant="outlined"
            onClick={onClick}
        >
            {strings('common:button.open')}
        </Button>
    );
};

export default OpenSelectedEntryButtonComponent;
