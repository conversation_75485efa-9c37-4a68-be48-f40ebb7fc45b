import React, { <PERSON> } from 'react';

import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import { Add } from '@protecht/ui-library/library/components/SVGIcons';

import { strings } from 'common/utils/i18n';

type CreateNewEntryButtonProps = {
    onCreateNew?: () => void;
};

const CreateNewEntryButtonComponent: FC<CreateNewEntryButtonProps> = ({ onCreateNew }) => {
    return (
        <Button
            {...ButtonStyles.tableToolbarButton}
            dataTestId="button-new"
            startIcon={<Add />}
            variant="outlined"
            onClick={onCreateNew}
        >
            {strings('common:button.new')}
        </Button>
    );
};

export default CreateNewEntryButtonComponent;
