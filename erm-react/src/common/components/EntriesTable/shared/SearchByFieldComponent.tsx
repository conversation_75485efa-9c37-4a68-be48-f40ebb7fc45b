import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';

import { DataGridColDef } from '@protecht/ui-library/library/types';

import { getSearchFields } from 'common/utils/definitions';
import SearchByField from 'common/components/SearchByField';
import useSearchExpression from 'common/hooks/useSearchExpression';
import { ViewExpressionRest } from 'api/generated/types';

type SearchByFieldComponentProps = {
    colDef: DataGridColDef[];
    defaultSearchField: string;
    defaultSearchValue?: string;
    searchPlaceholder?: string;
    hideSelectField?: boolean;
    searchField: string | undefined;
    onSearchFieldChanged?: (field?: string) => void;
    onSearchValueChanged?: (value: string) => void;
    updateSearchExpression: (expression: ViewExpressionRest | undefined) => void;
    updateSearchField: (searchField?: string) => void;
};

export const SearchByFieldComponent: FC<SearchByFieldComponentProps> = ({
    colDef,
    defaultSearchField,
    defaultSearchValue = '',
    searchPlaceholder,
    hideSelectField,
    searchField,
    onSearchFieldChanged,
    onSearchValueChanged,
    updateSearchExpression,
    updateSearchField,
}) => {
    const searchFields = useMemo(() => getSearchFields(colDef), [colDef]);

    const [searchValue, setSearchValue] = useState<string>(defaultSearchValue);

    const searchExpression = useSearchExpression(searchValue, searchField);

    useEffect(() => {
        updateSearchExpression(searchExpression);
    }, [searchExpression, updateSearchExpression]);

    const handleSearchValueChange = useCallback(
        (value: string) => {
            setSearchValue(value);
            onSearchValueChanged?.(value);
        },
        [onSearchValueChanged],
    );

    const handleSearchFieldChange = useCallback(
        (property?: string) => {
            if (property !== searchField) {
                updateSearchField(property);
            }
        },
        [searchField, updateSearchField],
    );

    useEffect(() => {
        onSearchFieldChanged?.(searchField);
    }, [onSearchFieldChanged, searchField]);

    return (
        <SearchByField
            aria-label="search-by"
            fields={searchFields}
            hideSelectField={hideSelectField}
            searchField={searchField || defaultSearchField}
            searchValue={searchValue}
            searchPlaceholder={searchPlaceholder}
            onPropertyChanged={handleSearchFieldChange}
            onValueChanged={handleSearchValueChange}
        />
    );
};
