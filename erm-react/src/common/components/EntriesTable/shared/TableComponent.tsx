import { styled } from '@mui/material/styles';
import { Table } from '@protecht/ui-library/library/components/Table';
import { TableProps } from '@protecht/ui-library/library/components/Table/Table';
import { IdOnly, SearchRequestParams } from '@protecht/ui-library/library/types';
import { strings } from 'common/utils/i18n';
import React, { useCallback, useState } from 'react';

const StyledTable = styled(Table)(({ theme }) => ({
    '& .MuiDataGrid-row.disabledText': {
        opacity: 1,
        color: theme.palette.text.primary,
    },
}));

export type TableComponentProps<T> = Partial<Omit<TableProps<T>, 'onSelect' | 'selected' | 'rows'>> & {
    selected?: T[];
    rows?: T[];
    onSelect?: (selectedEntries: T[]) => void;
};

type Props<T> = TableComponentProps<T> & {
    viewParamsLoaded: boolean;
    updateRequestParams: React.Dispatch<React.SetStateAction<SearchRequestParams | undefined>>;
};

export const TableComponent = <T extends IdOnly>({
    columns,
    params,
    rows,
    selected,
    onSelect,
    loading,
    totalCount,
    viewParamsLoaded,
    updateRequestParams,
    ...tableProps
}: Props<T>) => {
    const [currentSelection, setCurrentSelection] = useState<T[]>(selected ?? []);

    const handleSelectEntries = useCallback(
        (selection: T[]) => {
            const finalSelection = tableProps.multiselect ? selection : selection.slice(-1);
            setCurrentSelection(finalSelection);
            onSelect?.(finalSelection);
        },
        [onSelect, tableProps.multiselect],
    );

    const handleTableParamsChange = useCallback(
        (params: SearchRequestParams) => {
            updateRequestParams((previous) => ({ ...previous, ...params }));
        },
        [updateRequestParams],
    );

    return (
        <StyledTable
            {...tableProps}
            columns={viewParamsLoaded && columns ? columns : []}
            loading={loading || !viewParamsLoaded}
            loadingMessage={strings('common:message.loading')}
            params={params}
            rows={loading ? [] : rows || []}
            selected={currentSelection}
            totalCount={totalCount || 0}
            onParamsChanged={handleTableParamsChange}
            onSelect={handleSelectEntries}
        />
    );
};
