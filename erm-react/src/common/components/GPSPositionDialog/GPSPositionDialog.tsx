import React, { useCallback, useState } from 'react';
import { strings } from 'common/utils/i18n';
import DialogActions from '@mui/material/DialogActions';
import Button, { ButtonStyles } from '@protecht/ui-library/library/components/Button';
import GoogleMap from 'common/components/GoogleMap';
import Dialog from '@protecht/ui-library/library/components/Dialog';

type Props = {
    open: boolean;
    onClose: () => void;
    defaultCenterPosition: google.maps.LatLngLiteral;
    selectedPosition?: google.maps.LatLngLiteral;
    defaultZoom: number;
    onPositionSave: (position: google.maps.LatLngLiteral, zoom: number | undefined) => void;
};

const GPSPositionDialog = ({ open, onClose, onPositionSave, defaultZoom, defaultCenterPosition, selectedPosition }: Props) => {
    const [markerPosition, setMarkerPosition] = useState<google.maps.LatLngLiteral | undefined>(selectedPosition);
    const [zoom, setZoom] = useState<number | undefined>(defaultZoom);

    const handlePositionSave = useCallback(() => {
        if (markerPosition) {
            onPositionSave(markerPosition, zoom);
            onClose();
        }
    }, [markerPosition, onPositionSave, onClose, zoom]);

    return (
        <Dialog
            visible={open}
            title={strings('common:title.adjustGPSPosition')}
            width={1824}
            height={1470}
            noPadding={true}
            dialogActions={
                <DialogActions>
                    <Button
                        {...ButtonStyles.dialogButton}
                        variant={'secondary'}
                        onClick={onClose}
                        dataTestId="button-cancel"
                    >
                        {strings('common:button.cancel')}
                    </Button>
                    <Button
                        {...ButtonStyles.dialogButton}
                        type="submit"
                        dataTestId="button-confirm"
                        disabled={!markerPosition}
                        onClick={handlePositionSave}
                    >
                        {strings('common:button.save')}
                    </Button>
                </DialogActions>
            }
            sx={{
                top: 155,
                margin: 0,
                '& .MuiDialog-paper': { margin: '24px 48px', maxWidth: 'calc(100% - 96px)', maxHeight: 'calc(100% - 48px)' },
            }}
        >
            <GoogleMap
                mapHeight={'100%'}
                defaultZoom={defaultZoom || 10}
                defaultCenter={defaultCenterPosition}
                mapOptions={{ gestureHandling: 'greedy', disableDefaultUI: true }}
                markerPosition={markerPosition}
                onClick={(event, map) => {
                    let newMarkerPosition: google.maps.LatLngLiteral | undefined = undefined;

                    const currentZoom = map.getZoom();

                    if (event.latLng) {
                        newMarkerPosition = { lat: event.latLng.lat(), lng: event.latLng.lng() };
                    }

                    setZoom(currentZoom);
                    setMarkerPosition(newMarkerPosition);
                }}
            />
        </Dialog>
    );
};

export default GPSPositionDialog;
