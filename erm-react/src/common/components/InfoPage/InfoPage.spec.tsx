import React from 'react';
import { render, screen } from 'test/utils';
import InfoPage from './InfoPage';
import { AlertType } from '@protecht/ui-library/library/types';

const mockOnClick = jest.fn();

describe('InfoPage', () => {
    it('should render titles, message and icon', () => {
        const { container } = render(
            <InfoPage
                type={AlertType.Error}
                title="Some page title"
                subtitle="Some title"
                message="Some message"
            />,
        );

        expect(screen.getByTestId('infoPage-pageTitle')).toHaveTextContent('Some page title');
        expect(screen.getByTestId('infoBox-title')).toHaveTextContent('Some title');
        expect(screen.getByTestId('infoBox-message')).toHaveTextContent('Some message');
        expect(screen.getByTestId('infoBox-icon')).toBeInTheDocument();
        expect(screen.queryByTestId('infoPage-backButton')).not.toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('should render back button when needed', () => {
        const { container } = render(
            <InfoPage
                type={AlertType.Error}
                title="Some page title"
                subtitle="Some title"
                message="Some message"
                onBack={mockOnClick}
            />,
        );

        expect(screen.getByTestId('infoPage-backButton')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('should not render main title when not available', () => {
        const { container } = render(
            <InfoPage
                type={AlertType.Error}
                subtitle="Some title"
                message="Some message"
            />,
        );

        expect(screen.queryByTestId('infoPage-pageTitle')).not.toBeInTheDocument();
        expect(screen.getByTestId('infoBox-title')).toHaveTextContent('Some title');
        expect(screen.getByTestId('infoBox-message')).toHaveTextContent('Some message');
        expect(screen.getByTestId('infoBox-icon')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });

    it('should render children when provided', () => {
        const { container } = render(
            <InfoPage
                type={AlertType.Error}
                title="Some page title"
                subtitle="Some title"
                message="Some message"
            >
                <div data-testid="children">some content</div>
            </InfoPage>,
        );

        expect(screen.getByTestId('children')).toHaveTextContent('some content');
        expect(container).toMatchSnapshot();
    });
});
