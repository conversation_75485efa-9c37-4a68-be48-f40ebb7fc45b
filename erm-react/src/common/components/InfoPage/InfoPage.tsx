import React, { PropsWithChildren } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import { useTheme } from '@mui/material/styles';

import InfoBox from '@protecht/ui-library/library/components/InfoBox';
import { AlertType } from '@protecht/ui-library/library/types';
import ApplicationLayout from 'common/layouts/ApplicationLayout';
import MainLayout from 'common/layouts/MainLayout';
import ContentLayout from 'common/layouts/ContentLayout';
import ToolbarContainer from 'common/components/ToolbarSpacing/ToolbarContainer';
import IconButton from '@protecht/ui-library/library/components/IconButton';

type InfoPageProps = PropsWithChildren<{
    type: AlertType;
    title?: string;
    subtitle: string;
    message: string;
    onBack?: () => void;
    infoBoxSx?: React.CSSProperties;
}>;

const InfoPage: React.FC<InfoPageProps> = ({ title, subtitle, message, type, onBack, children, infoBoxSx }) => {
    const theme = useTheme();

    return (
        <ApplicationLayout>
            {(title || onBack) && (
                <ToolbarContainer
                    disableGutters={false}
                    variant="regular"
                    sx={{ borderBottom: `1px solid ${theme.palette.protechtGrey?.grey_231}` }}
                >
                    <Grid
                        container
                        gap="6px"
                        alignItems="center"
                    >
                        {onBack && (
                            <IconButton
                                color="secondary"
                                size="large"
                                onClick={onBack}
                                data-testid={'infoPage-backButton'}
                                sx={{
                                    marginLeft: '-9px',
                                }}
                            >
                                <FontAwesomeIcon
                                    icon={faArrowLeft}
                                    color={theme.palette.primary.main}
                                />
                            </IconButton>
                        )}
                        {title && (
                            <Typography
                                variant="h1"
                                noWrap
                                data-testid="infoPage-pageTitle"
                            >
                                {title}
                            </Typography>
                        )}
                    </Grid>
                </ToolbarContainer>
            )}
            <MainLayout>
                <ContentLayout sx={{ background: theme.palette.protechtGrey?.grey_250, margin: 0, paddingX: '24px' }}>
                    <InfoBox
                        title={subtitle}
                        message={message}
                        type={type}
                        sx={{ marginTop: 4, ...infoBoxSx }}
                    >
                        {children}
                    </InfoBox>
                </ContentLayout>
            </MainLayout>
        </ApplicationLayout>
    );
};

export default InfoPage;
