import React, { useCallback, useMemo } from 'react';
import { ExternalIDPConfigurationRest } from 'auth/saml/types';
import { AZURE_IDP_TYPE, GOOGLE_IDP_TYPE, OKTA_IDP_TYPE } from '../constants';
import { strings } from 'common/utils/i18n';
import Button from '@mui/material/Button';
import { Google, Microsoft, Okta } from './SamlIDPIcons';

type Props = {
    idp: ExternalIDPConfigurationRest;
};

const SamlIDPButton = (props: Props): React.ReactElement => {
    const goToRedirectionUrl = useCallback(() => {
        if (props.idp.redirectionUrl) {
            window.location.href = props.idp.redirectionUrl;
        }
    }, [props.idp.redirectionUrl]);

    const idpIcon = useMemo(() => {
        if (props.idp.type == AZURE_IDP_TYPE) {
            return <Microsoft />;
        } else if (props.idp.type == GOOGLE_IDP_TYPE) {
            return <Google />;
        } else if (props.idp.type == OKTA_IDP_TYPE) {
            return <Okta light={props.idp.default ? true : false} />;
        } else {
            return;
        }
    }, [props.idp.type]);

    return (
        <Button
            onClick={goToRedirectionUrl}
            startIcon={idpIcon}
            variant={props.idp.default ? 'primary' : 'secondary'}
            size="large"
            role="button"
            style={{
                width: '300px',
                height: '40px',
                minHeight: '40px',
                color: props.idp.default ? 'white' : ProtechtDictionary.accentColor,
                borderColor: ProtechtDictionary.accentColor,
                textTransform: 'none',
                border: props.idp.default ? 'none' : '1px solid ' + ProtechtDictionary.accentColor,
            }}
            sx={{
                bgcolor: props.idp.default ? ProtechtDictionary.accentColor : 'white',
                ':hover': {
                    backgroundImage: props.idp.default ? 'linear-gradient(rgba(0, 0, 0, 0.33) 0px, rgba(0, 0, 0, 0.33) 0px)' : 'none',
                    bgcolor: props.idp.default ? ProtechtDictionary.accentColor : ProtechtDictionary.accentColor + '10',
                },
            }}
        >
            {`${strings('auth:saml.dialog.signInWith')} ${props.idp.name ?? strings('auth:saml.dialog.unnamedIdp')}`}
        </Button>
    );
};

export default SamlIDPButton;
