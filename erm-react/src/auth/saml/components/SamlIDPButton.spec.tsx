import React from 'react';
import { render, screen } from '@testing-library/react';
import { ExternalIDPConfigurationRest } from '../types';
import SamlIDPButton from './SamlIDPButton';
import { strings } from 'common/utils/i18n';

const setupIDPButton = async (idp: ExternalIDPConfigurationRest) => {
    render(<SamlIDPButton idp={idp} />);
};

describe('<SamlIDPButton />', () => {
    describe('Default IDP', () => {
        it('render default IDP button', async () => {
            const defaultIDP: ExternalIDPConfigurationRest = {
                name: 'Default IDP',
                default: true,
                redirectionUrl: 'https://dev.protechtgroup.com/redirection/address',
                type: 'OTHER',
            };
            await setupIDPButton(defaultIDP);
            const button = screen.getByRole('button', { name: `${strings('auth:saml.dialog.signInWith')} ${defaultIDP.name}` });
            expect(button).toBeInTheDocument();
            expect(button).toBeEnabled();

            expect(document.body).toMatchSnapshot();
        });
    });

    describe('Non-Default IDP', () => {
        it('render non-default IDP button', async () => {
            const nonDefaultIDP: ExternalIDPConfigurationRest = {
                name: 'Non-Default IDP',
                default: false,
                redirectionUrl: 'https://dev.protechtgroup.com/redirection/address/2',
                type: 'OTHER',
            };
            await setupIDPButton(nonDefaultIDP);

            const button = screen.getByRole('button', { name: `${strings('auth:saml.dialog.signInWith')} ${nonDefaultIDP.name}` });
            expect(button).toBeInTheDocument();
            expect(button).toBeEnabled();

            expect(document.body).toMatchSnapshot();
        });
    });

    describe('Non-Name IDP', () => {
        it('render unnamed IDP button', async () => {
            const noNameIDP: Partial<ExternalIDPConfigurationRest> = {};
            await setupIDPButton(noNameIDP);

            const button = screen.getByRole('button', { name: `${strings('auth:saml.dialog.signInWith')} ${strings('auth:saml.dialog.unnamedIdp')}` });
            expect(button).toBeInTheDocument();
            expect(button).toBeEnabled();

            expect(document.body).toMatchSnapshot();
        });
    });

    describe('AZURE Type IDP', () => {
        it('render azure type with icon', async () => {
            const azureAdIDP: ExternalIDPConfigurationRest = {
                name: 'Azure AD',
                default: false,
                redirectionUrl: 'https://dev.protechtgroup.com/redirection/address/azure',
                type: 'AZURE',
            };
            await setupIDPButton(azureAdIDP);

            const button = screen.getByRole('button', { name: `${strings('auth:saml.dialog.signInWith')} ${azureAdIDP.name}` });
            expect(button).toBeInTheDocument();
            expect(button).toBeEnabled();

            // Ensure that the start icon exists
            const buttonIcon = screen.getByRole('img', { hidden: true });
            expect(buttonIcon).toBeInTheDocument();

            expect(document.body).toMatchSnapshot();
        });
    });

    describe('GOOGLE Type IDP', () => {
        it('render google type with icon', async () => {
            const googleIDP: ExternalIDPConfigurationRest = {
                name: 'Google IDP',
                default: false,
                redirectionUrl: 'https://dev.protechtgroup.com/redirection/address/google',
                type: 'GOOGLE',
            };
            await setupIDPButton(googleIDP);

            const button = screen.getByRole('button', { name: `${strings('auth:saml.dialog.signInWith')} ${googleIDP.name}` });
            expect(button).toBeInTheDocument();
            expect(button).toBeEnabled();

            // Ensure that the start icon exists
            const buttonIcon = screen.getByRole('img', { hidden: true });
            expect(buttonIcon).toBeInTheDocument();

            expect(document.body).toMatchSnapshot();
        });
    });

    describe('OTHER Type IDP', () => {
        it('render other type without icon', async () => {
            const otherIDP: ExternalIDPConfigurationRest = {
                name: 'Other IDP',
                default: false,
                redirectionUrl: 'https://dev.protechtgroup.com/redirection/address/other',
                type: 'OTHER',
            };
            await setupIDPButton(otherIDP);

            const button = screen.getByRole('button', { name: `${strings('auth:saml.dialog.signInWith')} ${otherIDP.name}` });
            expect(button).toBeInTheDocument();
            expect(button).toBeEnabled();

            // Ensure that the start icon does not exist for the 'OTHER' type
            const buttonIcon = screen.queryByRole('img', { hidden: true });
            expect(buttonIcon).not.toBeInTheDocument();

            expect(document.body).toMatchSnapshot();
        });
    });
});
