// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SamlIDPDialog /> Create IDP selection dialog with default and non-default IDPs 1`] = `
<body
  style="padding-right: 1024px; overflow: hidden;"
>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-ngxxlx-MuiModal-root-MuiDialog-root"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-1so2nzc-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-hz1bth-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      tabindex="-1"
    >
      <div
        aria-labelledby=":r1:"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthSm css-1t1j96h-MuiPaper-root-MuiDialog-paper"
        role="dialog"
      >
        <div
          class="css-1lr7lnk"
        >
          <div
            class="css-yi21r8"
          >
            <img
              alt="Login Icon"
              src="/camilla/worms/client/styles/style/logo_login"
              style="width: 60%; top: 30%; left: 20%; position: relative;"
            />
          </div>
          <div
            class="css-cmumcq"
          >
            <h1
              class="MuiTypography-root MuiTypography-h1 css-o2w69a-MuiTypography-root"
              data-testid="dialog-heading"
              style="padding-bottom: 24px; font-size: 1.1em; font-weight: 600; color: rgb(27, 74, 213);"
            >
              Protecht Sign In
            </h1>
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-primary MuiButton-primaryPrimary MuiButton-sizeLarge MuiButton-primarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-primary MuiButton-primaryPrimary MuiButton-sizeLarge MuiButton-primarySizeLarge MuiButton-colorPrimary css-suk6b7-MuiButtonBase-root-MuiButton-root"
              role="button"
              style="width: 300px; height: 40px; min-height: 40px; color: white; text-transform: none;"
              tabindex="0"
              type="button"
            >
              Sign in with Default IDP
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </button>
            <div
              class="css-1ccqa8k"
            />
            <div
              class="css-cplnog"
            >
              <button
                class="MuiButtonBase-root MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary css-1i5ii58-MuiButtonBase-root-MuiButton-root"
                role="button"
                style="width: 300px; height: 40px; min-height: 40px; color: rgb(27, 74, 213); text-transform: none; border: 1px solid #1b4ad5;"
                tabindex="0"
                type="button"
              >
                Sign in with Non-Default IDP1
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
              <button
                class="MuiButtonBase-root MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary css-1i5ii58-MuiButtonBase-root-MuiButton-root"
                role="button"
                style="width: 300px; height: 40px; min-height: 40px; color: rgb(27, 74, 213); text-transform: none; border: 1px solid #1b4ad5;"
                tabindex="0"
                type="button"
              >
                Sign in with Non-Default IDP2
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
            <div
              class="css-d2t41l"
            >
              <a
                class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary css-195ss7i-MuiButtonBase-root-MuiButton-root"
                href="/camilla/worms/client/public/home/<USER>"
                role="button"
                tabindex="0"
              >
                Or sign in with Protecht ID
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>
`;
