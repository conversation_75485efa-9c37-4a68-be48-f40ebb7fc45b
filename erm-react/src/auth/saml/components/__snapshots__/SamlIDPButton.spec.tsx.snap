// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SamlIDPButton /> AZURE Type IDP render azure type with icon 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge <PERSON>on-secondarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge <PERSON>-secondarySizeLarge MuiButton-colorPrimary css-1i5ii58-MuiButtonBase-root-MuiButton-root"
      role="button"
      style="width: 300px; height: 40px; min-height: 40px; color: rgb(27, 74, 213); text-transform: none; border: 1px solid #1b4ad5;"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon Mu<PERSON><PERSON>utton-startIcon MuiButton-iconSizeLarge css-htszrh-MuiButton-startIcon"
      >
        <svg
          enable-background="new 0 0 2499.6 2500"
          height="20px"
          role="img"
          viewBox="0 0 2499.6 2500"
          width="20px"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="m1187.9 1187.9h-1187.9v-1187.9h1187.9z"
            fill="#f1511b"
          />
          <path
            d="m2499.6 1187.9h-1188v-1187.9h1187.9v1187.9z"
            fill="#80cc28"
          />
          <path
            d="m1187.9 2500h-1187.9v-1187.9h1187.9z"
            fill="#00adef"
          />
          <path
            d="m2499.6 2500h-1188v-1187.9h1187.9v1187.9z"
            fill="#fbbc09"
          />
        </svg>
      </span>
      Sign in with Azure AD
      <span
        class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
      />
    </button>
  </div>
</body>
`;

exports[`<SamlIDPButton /> Default IDP render default IDP button 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-primary MuiButton-primaryPrimary MuiButton-sizeLarge MuiButton-primarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-primary MuiButton-primaryPrimary MuiButton-sizeLarge MuiButton-primarySizeLarge MuiButton-colorPrimary css-suk6b7-MuiButtonBase-root-MuiButton-root"
      role="button"
      style="width: 300px; height: 40px; min-height: 40px; color: white; text-transform: none;"
      tabindex="0"
      type="button"
    >
      Sign in with Default IDP
      <span
        class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
      />
    </button>
  </div>
</body>
`;

exports[`<SamlIDPButton /> GOOGLE Type IDP render google type with icon 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary css-1i5ii58-MuiButtonBase-root-MuiButton-root"
      role="button"
      style="width: 300px; height: 40px; min-height: 40px; color: rgb(27, 74, 213); text-transform: none; border: 1px solid #1b4ad5;"
      tabindex="0"
      type="button"
    >
      <span
        class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeLarge css-htszrh-MuiButton-startIcon"
      >
        <svg
          height="20px"
          role="img"
          viewBox="0 0 24 24"
          width="20px"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            fill="#4285F4"
          />
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            fill="#34A853"
          />
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            fill="#FBBC05"
          />
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            fill="#EA4335"
          />
          <path
            d="M1 1h22v22H1z"
            fill="none"
          />
        </svg>
      </span>
      Sign in with Google IDP
      <span
        class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
      />
    </button>
  </div>
</body>
`;

exports[`<SamlIDPButton /> Non-Default IDP render non-default IDP button 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary css-1i5ii58-MuiButtonBase-root-MuiButton-root"
      role="button"
      style="width: 300px; height: 40px; min-height: 40px; color: rgb(27, 74, 213); text-transform: none; border: 1px solid #1b4ad5;"
      tabindex="0"
      type="button"
    >
      Sign in with Non-Default IDP
      <span
        class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
      />
    </button>
  </div>
</body>
`;

exports[`<SamlIDPButton /> Non-Name IDP render unnamed IDP button 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary css-1i5ii58-MuiButtonBase-root-MuiButton-root"
      role="button"
      style="width: 300px; height: 40px; min-height: 40px; color: rgb(27, 74, 213); text-transform: none; border: 1px solid #1b4ad5;"
      tabindex="0"
      type="button"
    >
      Sign in with Unnamed Identity Provider
      <span
        class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
      />
    </button>
  </div>
</body>
`;

exports[`<SamlIDPButton /> OTHER Type IDP render other type without icon 1`] = `
<body>
  <div>
    <button
      class="MuiButtonBase-root MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary MuiButton-root MuiButton-secondary MuiButton-secondaryPrimary MuiButton-sizeLarge MuiButton-secondarySizeLarge MuiButton-colorPrimary css-1i5ii58-MuiButtonBase-root-MuiButton-root"
      role="button"
      style="width: 300px; height: 40px; min-height: 40px; color: rgb(27, 74, 213); text-transform: none; border: 1px solid #1b4ad5;"
      tabindex="0"
      type="button"
    >
      Sign in with Other IDP
      <span
        class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
      />
    </button>
  </div>
</body>
`;
