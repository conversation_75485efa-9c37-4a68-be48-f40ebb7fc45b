import React from 'react';
import { render, screen } from '@testing-library/react';
import { ExternalSamlConfigurationRest } from '../types';
import SamlIDPDialog from './SamlIDPDialog';
import { strings } from 'common/utils/i18n';

const setupIdpDialog = async (externalSamlConfiguration: ExternalSamlConfigurationRest) => {
    render(<SamlIDPDialog externalSamlConfiguration={externalSamlConfiguration} />);
};

describe('<SamlIDPDialog />', () => {
    describe('Create IDP selection dialog', () => {
        it('with no provided IDPs', async () => {
            await setupIdpDialog({});

            const signInTitle = screen.getByText(`${strings('auth:saml.dialog.signInTitle')}`);
            expect(signInTitle).toBeInTheDocument();
            expect(signInTitle).toBeVisible();

            const nativeLoginButton = screen.getByRole('button', { name: `${strings('auth:saml.dialog.nativeLoginButton')}` });
            expect(nativeLoginButton).toBeInTheDocument();
            expect(nativeLoginButton).toBeVisible();
        });

        it('with default and non-default IDPs', async () => {
            const config: ExternalSamlConfigurationRest = {
                externalIdps: [
                    {
                        name: 'Default IDP',
                        default: true,
                        redirectionUrl: 'https://dev.protechtgroup.com/redirection/address/default',
                        type: 'OTHER',
                    },
                    {
                        name: 'Non-Default IDP1',
                        default: false,
                        redirectionUrl: 'https://dev.protechtgroup.com/redirection/address/1',
                        type: 'OTHER',
                    },
                    {
                        name: 'Non-Default IDP2',
                        default: false,
                        redirectionUrl: 'https://dev.protechtgroup.com/redirection/address/2',
                        type: 'OTHER',
                    },
                ],
            };
            await setupIdpDialog(config);

            expect(document.body).toMatchSnapshot();

            const signInTitle = screen.getByText(`${strings('auth:saml.dialog.signInTitle')}`);
            expect(signInTitle).toBeInTheDocument();
            expect(signInTitle).toBeVisible();

            const nativeLoginButton = screen.getByRole('button', { name: `${strings('auth:saml.dialog.nativeLoginButton')}` });
            expect(nativeLoginButton).toBeInTheDocument();
            expect(nativeLoginButton).toBeVisible();
        });
    });
});
