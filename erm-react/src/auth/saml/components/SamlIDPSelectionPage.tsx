import React, { useEffect } from 'react';
import Loading from 'common/components/Loading';
import SamlIDPDialog from './SamlIDPDialog';
import { ExternalSamlConfigurationRest } from 'auth/saml/types';
import { getExternalEnabledIdps } from 'auth/saml/api';
import useAsync from 'common/hooks/useAsync';

const SamlIDPSelectionPage = (): React.ReactElement => {
    const { response: externalSamlConfiguration, isLoading, error, asyncLoad } = useAsync<ExternalSamlConfigurationRest>();

    useEffect(() => {
        void asyncLoad(getExternalEnabledIdps());
    }, [getExternalEnabledIdps, asyncLoad]);

    return (
        <>
            {isLoading && <Loading />}
            {error?.response.message}
            {!error?.response.message && externalSamlConfiguration && <SamlIDPDialog externalSamlConfiguration={externalSamlConfiguration} />}
        </>
    );
};

export default SamlIDPSelectionPage;
