import React, { useMemo } from 'react';
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ExternalIDPConfigurationRest, ExternalSamlConfigurationRest } from 'auth/saml/types';
import SamlIDPButton from './SamlIDPButton';
import { OTHER_IDP_TYPE, PATH_TO_LOGIN } from '../constants';
import { strings } from 'common/utils/i18n';
import Button from '@mui/material/Button';
import StyledDialog from '@protecht/ui-library/library/components/StyledDialog';

const Container = styled('div')({
    height: 'inherit',
    display: 'flex',
    flexDirection: 'row',
});

const LeftContainer = styled('div')({
    flex: '5',
    height: 'inherit',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ProtechtDictionary.accentColor,
});

const RightContainer = styled('div')({
    flex: '6',
    marginLeft: '32px',
    marginRight: '32px',
    height: 'inherit',
    alignSelf: 'center',
    alignItems: 'center',
    textAlign: 'center',
    paddingTop: '56px',
    display: 'flex',
    justifyContent: 'flex-start',
    flexDirection: 'column',
});

const ButtonFrame = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    height: '45%',
    overflowY: 'auto',
    whiteSpace: 'nowrap',
    paddingTop: '4px',
});

const NativeLoginFrame = styled('div')({
    position: 'absolute',
    bottom: '16px',
    justifyContent: 'flex-end',
    alignItems: 'center',
    textAlign: 'center',
    left: '52.5%',
});

const Divider = styled('div')({
    borderRadius: '1px',
    border: 'solid 1px #c0c0c0',
    marginTop: '16px',
    marginBottom: '12px',
    alignSelf: 'center',
    marginLeft: '10px',
    marginRight: '10px',
    width: '100%',
});

type Props = {
    externalSamlConfiguration: ExternalSamlConfigurationRest;
};

const SamlIDPDialog = (props: Props): React.ReactElement => {
    const defaultIdp = useMemo(() => getDefaultIdp(), [props.externalSamlConfiguration]);
    const nonDefaultIdps = useMemo(() => withoutDefaultIdp(), [props.externalSamlConfiguration]);

    function getDefaultIdp(): ExternalIDPConfigurationRest | undefined {
        let defaultIdp: ExternalIDPConfigurationRest | undefined = undefined;
        Array.from(props.externalSamlConfiguration.externalIdps ?? []).forEach((idp) => {
            if (idp.default) {
                defaultIdp = idp;
            }
        });
        return defaultIdp;
    }

    function withoutDefaultIdp(): Array<ExternalIDPConfigurationRest> {
        const array = Array<ExternalIDPConfigurationRest>();
        Array.from(props.externalSamlConfiguration.externalIdps ?? [])
            .sort((a, b) => (a.type == OTHER_IDP_TYPE ? 1 : b.type == OTHER_IDP_TYPE ? -1 : 1))
            .forEach((idp) => {
                if (!idp.default) {
                    array.push(idp);
                }
            });
        return array;
    }

    return (
        <StyledDialog
            open={true}
            $height={400}
            $width={640}
            slotProps={{ backdrop: { sx: { background: 'rgba(255, 255, 255, 0.25)' } } }}
        >
            <Container>
                <LeftContainer>
                    <img
                        style={{
                            width: '60%',
                            top: '30%',
                            left: '20%',
                            position: 'relative',
                        }}
                        src={ProtechtDictionary.context + '/worms/client/styles/style/logo_login'}
                        alt="Login Icon"
                    />
                </LeftContainer>
                <RightContainer>
                    <Typography
                        variant="h1"
                        data-testid="dialog-heading"
                        style={{
                            paddingBottom: '24px',
                            fontSize: '1.1em',
                            fontWeight: '600',
                            color: ProtechtDictionary.accentColor,
                        }}
                    >
                        {strings('auth:saml.dialog.signInTitle')}
                    </Typography>
                    {defaultIdp && <SamlIDPButton idp={defaultIdp} />}
                    <Divider />
                    <ButtonFrame>
                        {nonDefaultIdps.map((idp, index) => (
                            <SamlIDPButton
                                key={index}
                                idp={idp}
                            />
                        ))}
                    </ButtonFrame>
                    <NativeLoginFrame>
                        <Button
                            size="medium"
                            variant="text"
                            role="button"
                            sx={{
                                color: ProtechtDictionary.accentColor,
                                fontWeight: '600',
                                ':hover': {
                                    bgcolor: ProtechtDictionary.accentColor,
                                    color: 'white',
                                },
                                textTransform: 'none',
                            }}
                            href={ProtechtDictionary.context + PATH_TO_LOGIN}
                        >
                            {strings('auth:saml.dialog.nativeLoginButton')}
                        </Button>
                    </NativeLoginFrame>
                </RightContainer>
            </Container>
        </StyledDialog>
    );
};

export default SamlIDPDialog;
