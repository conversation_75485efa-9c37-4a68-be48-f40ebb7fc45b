import React from 'react';

const Okta = (props) => {
    return (
        <svg
            width="20px"
            height="20px"
            viewBox="0 0 256 256"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            preserveAspectRatio="xMidYMid"
            role="img"
        >
            <title>okta</title>
            <g>
                <path
                    d="M140.84428,1.77777778 L135.578481,66.6311111 C133.0879,66.3466667 130.59732,66.2044444 128.03558,66.2044444 C124.833405,66.2044444 121.702389,66.4177778 118.642533,66.9155556 L115.653837,35.4844444 C115.582677,34.4888889 116.365431,33.6355556 117.361663,33.6355556 L122.698621,33.6355556 L120.136881,1.84888889 C120.065722,0.853333333 120.848476,0 121.773549,0 L139.207612,0 C140.203845,0 140.986598,0.853333333 140.84428,1.84888889 L140.84428,1.84888889 L140.84428,1.77777778 Z M96.8677434,4.97777778 C96.5831056,4.05333333 95.5868734,3.48444444 94.6618006,3.84 L78.2951286,9.81333333 C77.3700558,10.1688889 76.9430991,11.2355556 77.3700558,12.0888889 L90.6768718,41.1022222 L85.6245513,42.9511111 C84.6994785,43.3066667 84.2725218,44.3733333 84.6994785,45.2266667 L98.2909322,73.7422222 C103.200934,71.04 108.537892,68.9777778 114.159488,67.6977778 L96.9389028,4.97777778 L96.8677434,4.97777778 Z M56.7338171,23.04 L94.3771628,76.0888889 C89.6094801,79.2177778 85.268754,82.9155556 81.568463,87.1822222 L59.0109193,64.9955556 C58.2993249,64.2844444 58.3704843,63.1466667 59.0820788,62.5066667 L63.2093265,59.0933333 L40.7941017,36.4088889 C40.0825073,35.6977778 40.1536667,34.56 40.9364206,33.92 L54.2432366,22.7555556 C55.0259905,22.1155556 56.0933821,22.2577778 56.6626577,23.04 L56.7338171,23.04 Z M25.1390241,53.76 C24.3562702,53.1911111 23.2177191,53.4755556 22.719603,54.3288889 L14.0381509,69.4044444 C13.5400348,70.2577778 13.895832,71.3244444 14.7497453,71.7511111 L43.6404795,85.4044444 L40.9364206,90.0266667 C40.4383045,90.88 40.7941017,92.0177778 41.7191745,92.3733333 L70.4675897,105.528889 C72.5312136,100.195556 75.3064319,95.2177778 78.7220852,90.7377778 L25.1390241,53.76 Z M3.86235042,94.72 C4.00466931,93.7244444 5.00090152,93.1555556 5.92597429,93.3688889 L68.902082,109.795556 C67.2654148,115.128889 66.340342,120.746667 66.1980231,126.577778 L34.6743896,124.017778 C33.6781573,123.946667 32.9665629,123.022222 33.1800412,122.026667 L34.105114,116.764444 L2.29684266,113.777778 C1.30061044,113.706667 0.660175451,112.782222 0.802494339,111.786667 L3.79119098,94.6488889 L3.79119098,94.6488889 L3.86235042,94.72 Z M1.51408878,137.244444 C0.517856564,137.315556 -0.12257843,138.24 0.0197404578,139.235556 L3.07959654,156.373333 C3.22191543,157.368889 4.21814764,157.937778 5.14322041,157.724444 L36.026419,149.688889 L36.9514918,154.951111 C37.0938106,155.946667 38.0900429,156.515556 39.0151156,156.302222 L69.4713575,147.911111 C67.6923714,142.648889 66.5538203,137.031111 66.2691826,131.271111 L1.44292933,137.244444 L1.51408878,137.244444 Z M11.6187298,182.328889 C11.1206137,181.475556 11.4764109,180.408889 12.3303242,179.982222 L71.1080247,152.106667 C73.3139675,157.368889 76.3026641,162.275556 79.8606363,166.684444 L54.1009177,185.031111 C53.3181638,185.6 52.1796127,185.386667 51.6814966,184.533333 L48.9774377,179.84 L22.719603,197.973333 C21.9368491,198.542222 20.798298,198.257778 20.3001819,197.404444 L11.5475703,182.328889 L11.6187298,182.328889 Z M82.849333,170.097778 L37.0938106,216.391111 C36.3822162,217.102222 36.4533756,218.24 37.2361295,218.88 L50.6141049,230.044444 C51.3968588,230.684444 52.4642505,230.542222 53.033526,229.76 L71.5349814,203.733333 L75.6622291,207.217778 C76.444983,207.857778 77.5835341,207.715556 78.1528097,206.862222 L96.0849895,180.835556 C91.2461473,177.848889 86.7631024,174.222222 82.9204924,170.097778 L82.849333,170.097778 Z M73.8120836,244.408889 C72.8870108,244.053333 72.4600542,242.986667 72.8870108,242.133333 L99.9987589,182.968889 C104.97992,185.528889 110.388038,187.448889 116.009634,188.515556 L108.039776,219.093333 C107.826298,220.017778 106.758906,220.586667 105.833833,220.231111 L100.781513,218.382222 L92.313539,249.173333 C92.0289012,250.097778 91.032669,250.666667 90.1075962,250.311111 L73.7409242,244.337778 L73.7409242,244.337778 L73.8120836,244.408889 Z M120.492679,189.297778 L115.22688,254.151111 C115.15572,255.146667 115.938474,256 116.863547,256 L134.297611,256 C135.293843,256 136.076597,255.146667 135.934278,254.151111 L133.372538,222.364444 L138.709496,222.364444 C139.705728,222.364444 140.488482,221.511111 140.417323,220.515556 L137.428626,189.084444 C134.36877,189.582222 131.237755,189.795556 128.03558,189.795556 C125.47384,189.795556 122.983259,189.653333 120.492679,189.297778 L120.492679,189.297778 Z M183.255308,13.7244444 C183.682265,12.8 183.255308,11.8044444 182.330235,11.4488889 L165.963563,5.47555556 C165.03849,5.12 164.042258,5.68888889 163.75762,6.61333333 L155.289647,37.4044444 L150.237326,35.5555556 C149.312253,35.2 148.316021,35.7688889 148.031383,36.6933333 L140.061526,67.2711111 C145.754281,68.4088889 151.091239,70.3288889 156.072401,72.8177778 L183.255308,13.7244444 L183.255308,13.7244444 Z M218.977349,39.5377778 L173.221826,85.8311111 C169.379217,81.7066667 164.967331,78.08 160.057329,75.0933333 L177.989509,49.0666667 C178.558785,48.2844444 179.697336,48.0711111 180.48009,48.7111111 L184.607337,52.1955556 L203.108793,26.1688889 C203.678068,25.3866667 204.81662,25.2444444 205.528214,25.8844444 L218.906189,37.0488889 C219.688943,37.6888889 219.688943,38.8266667 219.048508,39.5377778 L218.977349,39.5377778 Z M243.740835,75.9466667 C244.665908,75.52 244.950546,74.4533333 244.45243,73.6 L235.699818,58.5244444 C235.201702,57.6711111 234.063151,57.4577778 233.280397,57.9555556 L207.022562,76.0888889 L204.318503,71.4666667 C203.820387,70.6133333 202.681836,70.3288889 201.899082,70.9688889 L176.139364,89.3155556 C179.697336,93.7244444 182.614873,98.6311111 184.891975,103.893333 L243.669676,76.0177778 L243.740835,75.9466667 Z M252.991563,99.5555556 L255.98026,116.693333 C256.122578,117.688889 255.482143,118.542222 254.485911,118.684444 L189.659658,124.728889 C189.37502,118.897778 188.236469,113.351111 186.457483,108.088889 L216.913725,99.6977778 C217.838798,99.4133333 218.83503,100.053333 218.977349,101.048889 L219.902422,106.311111 L250.78562,98.2755556 C251.710693,98.0622222 252.706925,98.6311111 252.849244,99.6266667 L252.849244,99.6266667 L252.991563,99.5555556 Z M250.074026,162.488889 C250.999098,162.702222 251.995331,162.133333 252.13765,161.137778 L255.126346,144 C255.268665,143.004444 254.62823,142.151111 253.631998,142.008889 L221.823727,139.022222 L222.748799,133.76 C222.891118,132.764444 222.250683,131.911111 221.254451,131.768889 L189.730817,129.208889 C189.588499,135.04 188.663426,140.657778 187.026759,145.991111 L250.002866,162.417778 L250.002866,162.417778 L250.074026,162.488889 Z M233.280397,201.6 C232.782281,202.453333 231.64373,202.666667 230.860976,202.168889 L177.277915,165.191111 C180.693568,160.711111 183.468786,155.733333 185.53241,150.4 L214.280826,163.555556 C215.205898,163.982222 215.561696,165.048889 215.063579,165.902222 L212.359521,170.524444 L241.250255,184.177778 C242.104168,184.604444 242.459965,185.671111 241.961849,186.524444 L233.280397,201.6 L233.280397,201.6 L233.280397,201.6 Z M161.622837,179.768889 L199.266183,232.817778 C199.835458,233.6 200.97401,233.742222 201.685604,233.102222 L214.99242,221.937778 C215.775174,221.297778 215.775174,220.16 215.134739,219.448889 L192.719514,196.764444 L196.846762,193.351111 C197.629516,192.711111 197.629516,191.573333 196.917921,190.862222 L174.360378,168.675556 C170.588927,172.942222 166.31936,176.711111 161.551678,179.768889 L161.551678,179.768889 L161.622837,179.768889 Z M161.26704,252.017778 C160.341967,252.373333 159.345735,251.804444 159.061097,250.88 L141.840512,188.16 C147.462108,186.88 152.799066,184.817778 157.709068,182.115556 L171.300522,210.631111 C171.727478,211.555556 171.300522,212.622222 170.375449,212.906667 L165.323128,214.755556 L178.629944,243.768889 C179.056901,244.693333 178.629944,245.688889 177.704871,246.044444 L161.338199,252.017778 L161.338199,252.017778 L161.26704,252.017778 Z"
                    fill={props.light ? '#FFFFFF' : '#000000'}
                ></path>
            </g>
        </svg>
    );
};

const Microsoft = () => {
    return (
        <svg
            width="20px"
            height="20px"
            enableBackground="new 0 0 2499.6 2500"
            viewBox="0 0 2499.6 2500"
            xmlns="http://www.w3.org/2000/svg"
            role="img"
        >
            <path
                d="m1187.9 1187.9h-1187.9v-1187.9h1187.9z"
                fill="#f1511b"
            />
            <path
                d="m2499.6 1187.9h-1188v-1187.9h1187.9v1187.9z"
                fill="#80cc28"
            />
            <path
                d="m1187.9 2500h-1187.9v-1187.9h1187.9z"
                fill="#00adef"
            />
            <path
                d="m2499.6 2500h-1188v-1187.9h1187.9v1187.9z"
                fill="#fbbc09"
            />
        </svg>
    );
};

const Google = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            height="20px"
            viewBox="0 0 24 24"
            width="20px"
            role="img"
        >
            <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
            />
            <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
            />
            <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
            />
            <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
            />
            <path
                d="M1 1h22v22H1z"
                fill="none"
            />
        </svg>
    );
};

export { Microsoft, Okta, Google };
export default Okta;
