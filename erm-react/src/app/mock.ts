import { ProtechtUserRest } from 'api/generated/types';
import { LayoutOption, SYSTEM_COLUMN } from 'common/types';
import { State } from './reducer';

export const mockedAppSlice: State = {
    isGwtOverlayOpen: false,
    token: 'token',
    tokenExpiration: new Date(Date.now() + 3600000).toISOString(),
    currentUser: {
        businessUnit: { id: 123, name: 'Test BU Name' },
        businessUnits: [{ id: 123, name: 'Test BU Name' }],
    } as ProtechtUserRest,
    systemConfiguration: {},
    errors: {},
    isCriticalError: false,
    layoutProps: {
        path: {
            layoutOption: LayoutOption.CARD,
            searchValue: '',
            searchProperty: SYSTEM_COLUMN.ID,
            selectedView: null,
            requestParams: undefined,
        },
    },
    tableLimit: 10,
    currentRoute: {
        from: '',
        to: '',
    },
    parentPathname: null,
    scaleSets: [],
    mainNavigation: {
        isHidden: false,
        isExpanded: false,
        isPinned: false,
        activeItemId: null,
    },
    logos: {
        main: 'mainLogoUrl',
        login: 'LoginLogoUrl',
    },
    pageLayout: {
        topBarHeight: 48,
    },
};
