export type PaletteMode = 'light' | 'dark';

export interface IdWithNameAndStatusRest extends IdWithName {
    status?: ObjectStatus | undefined;
}

export enum ObjectStatus {
    Active,
    Disabled,
    Deleted,
    PasswordExpired,
    PasswordInvalidAttempts,
}

export interface IdWithName extends IdOnly {
    name: string;
}

export interface IdWithParams extends IdOnly {
    [key: string]: any;
}

export enum RolesAndPermissionsHomepage {
    'MyTasks' = 'My Tasks',
    'AnalyticReports' = 'Analytic Reports',
}

export interface IdOnly {
    id: number;
}
export type Selector = (store: unknown) => any;

export enum FieldIndicatorType {
    ERROR,
    INFO,
    EMPTY,
}

export interface GlobalSettingsContextType {
    disableEnforceFocus: boolean;
    setDisableEnforceFocus: (value: boolean) => void;
}

export enum ProtechtDetailsKey {
    DATE_FORMAT_PATTERN = 'global_date_format',
    TIME_FORMAT_24HOUR = 'time_format_24hour',
    SESSION_TIMEOUT = 'session_timeout',
    GLOBAL_DASHBOARD = 'global_dashboard',
    UI_THEME_ACCENT = 'ui_theme_accent',
    MAX_ATTACHMENT_SIZE_BYTES = 'max_attach_size_bytes',
    COMPLIANCE_ENTRY_RADIO_RESPONSE = 'compliance_entry_radio_response',
    ACTION_REGISTER_LIMITING = 'action_register_limiting',
    CONFIG_KRI_SCALES = 'config_kri_scales',
    INETSOFT_NEW_SYNC = 'inetsoft_new_sync',
    INETSOFT_SINGLE_CLIENT_SETUP = 'inetsoft_single_client',
    INETSOFT_SIMPLE_MODELS = 'inetsoft_simple_models',
    MOBILE_SESSION_TIMEOUT = 'mobile_session_timeout',
    MYTASKS_DUE_FRAME = 'mytasks_due_frame_days',
    LICENSE_LIMITS = 'license_limits',
    MIN_PASSWORD_LENGTH = 'min_password_length',
    MIN_PASSWORD_STRENGTH = 'min_password_strength',
    CURRENCY_DEFAULT = 'currency_default',
    UPLOAD_WHITELIST = 'upload_whitelist',
    OPEN_ATTACHMENTS_IN_BROWSER = 'open_attachments_in_browser',
    REGISTER_ENTRY_LINKING = 'register_entry_linking',
    USE_MODERN_APPLICATION = 'use_modern_application',
    ERM_MODULES = 'erm_modules',
    AI_COPILOT_FEATURE = 'ai_copilot_feature',
}

export type SystemConfiguration = Partial<Record<ProtechtDetailsKey, string>>;

export type Route = {
    from: string;
    to: string;
};

export type ScaleSet = {
    id: number;
    name: string;
    scoreType: ScoreType;
    scoreFormula?: string;
    xAxis: string;
    likelihood: Scale;
    consequences: Scale[];
    ratings: RiskMatrixRating[];
    cells: RiskMatrixCell[];
    uuid: string;
};

export enum ScoreType {
    MULTIPLICATION = 'MULTIPLICATION',
    ADDITION = 'ADDITION',
    MANUAL = 'MANUAL',
    FORMULA = 'FORMULA',
}

export type Scale = {
    id: number;
    name: string;
    context: ScaleContext;
    scaleItems: ScaleItem[];
    uuid: string;
};

export enum ScaleContext {
    LIKELIHOOD = 'Likelihood',
    CONSEQUENCE = 'Consequence',
}

export type ScaleItem = {
    id: number;
    baseScale: number;
    value: number;
    label: string;
};

export type RiskMatrixRating = {
    id: number;
    label: string;
    color: string;
    order: number;
    clientId: number;
};

export type RiskMatrixCell = {
    id: number;
    color: string;
    cellValue: number;
    likelihoodValue: number;
    consequenceValue: number;
    rating: RiskMatrixRating;
};

export type ShowAll = null;
