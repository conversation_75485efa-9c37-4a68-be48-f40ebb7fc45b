import { baseInjected<PERSON>pi } from 'api/generated/scalesets';
import { SystemConfiguration } from './types';
import { DEFAULT_SYSTEM_CONFIGURATION_KEYS } from './constants';
import { baseInjectedApi as systemConfigurationRtk } from 'api/generated/system';

const systemApiWithTag = baseInjectedApi.enhanceEndpoints({
    addTagTypes: ['system', 'scaleSets'],
    endpoints: {
        srsGetScaleSetUsingGet: {
            providesTags: ['system'],
        },
        srsGetScaleSetsUsingGet: {
            providesTags: ['scaleSets'],
        },
    },
});

export const systemApi = systemApiWithTag.injectEndpoints({
    endpoints: (build) => ({
        // added undefined | void to query parameters because query options cannot be used otherwise
        getSystemConfiguration: build.query<SystemConfiguration, undefined | void>({
            query: () => ({
                url: `/v1/api/system?key=${encodeURIComponent(DEFAULT_SYSTEM_CONFIGURATION_KEYS.join(','))}`,
                method: 'GET',
            }),
        }),
    }),
});
export const { useGetSystemConfigurationQuery, useSrsGetScaleSetUsingGetQuery, useSrsGetScaleSetsUsingGetQuery } = systemApi;

export const systemConfigurationApi = systemConfigurationRtk.enhanceEndpoints({
    addTagTypes: ['timezones'],
    endpoints: {},
});
export const { useScrsGetTimezonesUsingGetQuery } = systemConfigurationApi;
