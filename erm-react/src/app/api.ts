import { ProtechtDetailsKey, ScaleSet, SystemConfiguration } from './types';
import authenticatedClient from '../common/api/utils/api';
import { PagingResult } from 'common/api/types';

export const getSystemConfiguration = async (key: ProtechtDetailsKey[]): Promise<SystemConfiguration> => {
    return authenticatedClient.get(`/v1/api/system?key=${encodeURIComponent(key.join(','))}`);
};

export const getScaleSets = async (): Promise<PagingResult<ScaleSet>> => {
    return authenticatedClient.get('/v1/api/scalesets', { params: { pagingDisabled: true } });
};
