import { swapItems } from "./utils";

describe('app/utils', () => {
    describe('swapItems', () => {
        const array = [
            { id: 1, name: 'First', order: 1 },
            { id: 2, name: 'Second', order: 2 },
            { id: 3, name: 'Third', order: 3 },
        ];

        it('should swap two items based on their indices', () => {
            const result = swapItems(array, 0, 2);
            expect(result).toEqual([
                { id: 3, name: 'Third', order: 3 },
                { id: 2, name: 'Second', order: 2 },
                { id: 1, name: 'First', order: 1 },
            ]);
        });

        it('should not modify the original array', () => {
            swapItems(array, 0, 2);
            expect(array).toEqual([
                { id: 1, name: 'First', order: 1 },
                { id: 2, name: 'Second', order: 2 },
                { id: 3, name: 'Third', order: 3 },
            ]);
        });
    });
})