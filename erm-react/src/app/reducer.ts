import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { isNil, omitBy } from 'lodash';
import { ApiError } from 'common/api/types';
import { v4 as uuid } from 'uuid';
import { IdWithParams, ShowAll, SystemConfiguration } from 'app/types';
import { LayoutOption, SYSTEM_COLUMN } from 'common/types';
import { Route } from './types';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { ProtechtUserRest, ScaleSetRest, ViewRest } from 'api/generated/types';
import { systemApi } from './rtkApi';
import { usersApi } from 'user/rtkApi';
import { PINNED_MENU_LOCAL_STORAGE_KEY } from 'ui/constants';
import { APP_LOGO_LOGIN_URL, APP_LOGO_MAIN_URL } from 'config';

type LayoutProps = {
    layoutOption: LayoutOption;
    searchValue: string;
    searchProperty: string;
    selectedView?: ViewRest | ShowAll;
    selectedEntry?: IdWithParams;
    requestParams?: SearchRequestParams;
};

type MainNavigationProps = {
    isHidden: boolean;
    isPinned: boolean;
    isExpanded: boolean;
    activeItemId: string | null;
};

type LogosProps = {
    main: string;
    login: string;
};

const isPinnedMenuFromLocalStorage = localStorage.getItem(PINNED_MENU_LOCAL_STORAGE_KEY) === 'true';

export interface State {
    isGwtOverlayOpen: boolean;
    token: string | null;
    tokenExpiration: string | null;
    errors: Record<string, Partial<ApiError>>;
    currentUser: ProtechtUserRest | null;
    systemConfiguration: SystemConfiguration;
    layoutProps: Record<string, LayoutProps>;
    tableLimit: number;
    currentRoute: Route;
    parentPathname: string | null;
    scaleSets: ScaleSetRest[];
    mainNavigation: MainNavigationProps;
    logos: LogosProps;
    isCriticalError: boolean;
    pageLayout: {
        topBarHeight: number;
    };
}

export const initialLayoutProps = {
    layoutOption: LayoutOption.CARD,
    searchValue: '',
    searchProperty: SYSTEM_COLUMN.ID,
    selectedView: undefined,
    selectedEntry: undefined,
    requestParams: undefined,
};

export const initialState: State = {
    isGwtOverlayOpen: false,
    token: null,
    tokenExpiration: null,
    currentUser: null,
    errors: {},
    isCriticalError: false,
    systemConfiguration: {},
    layoutProps: {},
    tableLimit: 0,
    currentRoute: {
        from: '',
        to: '',
    },
    parentPathname: null,
    scaleSets: [],
    mainNavigation: {
        isHidden: true,
        isPinned: isPinnedMenuFromLocalStorage,
        isExpanded: false,
        activeItemId: null,
    },
    logos: {
        main: APP_LOGO_MAIN_URL,
        login: APP_LOGO_LOGIN_URL,
    },
    pageLayout: {
        topBarHeight: 0,
    },
};

const appSlice = createSlice({
    name: 'app',
    initialState,
    reducers: {
        setGwtOverlayOpen: (state, action: PayloadAction<boolean>) => {
            state.isGwtOverlayOpen = action.payload;
        },
        setToken: (state, action) => {
            state.token = action.payload.token;
            state.tokenExpiration = action.payload.tokenExpiration;
        },
        setIsCriticalError: (state, action: PayloadAction<boolean>) => {
            state.isCriticalError = action.payload;
        },
        setGlobalError(state, action: PayloadAction<Partial<ApiError>>) {
            state.errors[uuid()] = action.payload;
        },
        clearError(state, action: PayloadAction<string>) {
            delete state.errors[action.payload];
        },
        setLayoutOption(state, action: PayloadAction<LayoutOption>) {
            state.layoutProps[`${location.pathname}`] = getUpdatedLayoutProps(state.layoutProps[`${location.pathname}`], 'layoutOption', action.payload);
        },
        setMultiPathLayoutOption(state, action: PayloadAction<{ paths: string[]; layoutOption: LayoutOption }>) {
            action.payload.paths.forEach((path) => {
                state.layoutProps[path] = getUpdatedLayoutProps(state.layoutProps[path], 'layoutOption', action.payload.layoutOption);
            });
        },
        setSearchValue(state, action: PayloadAction<string>) {
            state.layoutProps[`${location.pathname}`] = getUpdatedLayoutProps(state.layoutProps[`${location.pathname}`], 'searchValue', action.payload);
        },
        setSearchProperty(state, action: PayloadAction<string>) {
            state.layoutProps[`${location.pathname}`] = getUpdatedLayoutProps(state.layoutProps[`${location.pathname}`], 'searchProperty', action.payload);
        },
        setSelectedView(state, action: PayloadAction<ViewRest | ShowAll | undefined>) {
            state.layoutProps[`${location.pathname}`] = getUpdatedLayoutProps(state.layoutProps[`${location.pathname}`], 'selectedView', action.payload);
        },
        setSelectedEntry(state, action: PayloadAction<IdWithParams | undefined>) {
            state.layoutProps[`${location.pathname}`] = getUpdatedLayoutProps(state.layoutProps[`${location.pathname}`], 'selectedEntry', action.payload);
        },
        setRequestParams(state, action: PayloadAction<SearchRequestParams | undefined>) {
            const params = action.payload ? omitBy(action.payload, isNil) : undefined;

            if (params && !params.limit) {
                params.limit = state.tableLimit;
            }

            state.layoutProps[`${location.pathname}`] = getUpdatedLayoutProps(state.layoutProps[`${location.pathname}`], 'requestParams', params);
            state.tableLimit = params?.limit ?? 0;
        },
        resetLayoutProps(state) {
            state.layoutProps[`${location.pathname}`] = initialLayoutProps;
        },

        resetLayoutWithoutLayoutOptionsProps(state) {
            const lo = state.layoutProps?.[`${location.pathname}`]?.layoutOption;
            state.layoutProps[`${location.pathname}`] = { ...initialLayoutProps, layoutOption: lo ?? initialLayoutProps.layoutOption };
        },

        resetPageLayoutProps(state) {
            state.layoutProps[`${location.pathname}`] = getUpdatedLayoutProps(state.layoutProps[`${location.pathname}`], 'requestParams', {
                ...(state.layoutProps[`${location.pathname}`]?.requestParams ?? {}),
                offset: 0,
            });
        },
        resetTableLimit(state) {
            state.tableLimit = 0;
        },

        setCurrentPathname(state, action: PayloadAction<string>) {
            state.currentRoute = {
                from: state.currentRoute.to,
                to: action.payload,
            };
        },
        setParentPathname(state, action: PayloadAction<string | null>) {
            state.parentPathname = action.payload;
        },
        forceMainLogoReload(state) {
            state.logos.main = `${APP_LOGO_MAIN_URL}?${Date.now()}`;
        },
        setIsMenuHidden(state, action: PayloadAction<boolean>) {
            state.mainNavigation.isHidden = action.payload;
        },
        setIsPinnedMenu(state, action: PayloadAction<boolean>) {
            state.mainNavigation.isPinned = action.payload;
            localStorage.setItem(PINNED_MENU_LOCAL_STORAGE_KEY, action.payload.toString());
        },
        setIsExpandedMenu(state, action: PayloadAction<boolean>) {
            state.mainNavigation.isExpanded = action.payload;
            localStorage.setItem(PINNED_MENU_LOCAL_STORAGE_KEY, action.payload.toString());
        },
        setActiveMenuId(state, action: PayloadAction<string | null>) {
            state.mainNavigation.activeItemId = action.payload;
        },
        setPageTopBarHeight(state, action: PayloadAction<number>) {
            state.pageLayout.topBarHeight = action.payload;
        },
    },
    extraReducers: (builder) => {
        // Temporarily set the scale sets, current user and system configuration to the 'app' store until RTK query is fully implemented
        builder.addMatcher(systemApi.endpoints.srsGetScaleSetsUsingGet.matchFulfilled, (state, action) => {
            state.scaleSets = action.payload.records ?? [];
        });
        builder.addMatcher(usersApi.endpoints.pursGetCurrentUserUsingGet.matchFulfilled, (state, action) => {
            state.currentUser = action.payload ?? null;
        });
        builder.addMatcher(systemApi.endpoints.getSystemConfiguration.matchFulfilled, (state, action) => {
            state.systemConfiguration = action.payload ?? {};
        });
    },
});

const getUpdatedLayoutProps = (currentProps: LayoutProps, property: string, value: unknown) => {
    if (!currentProps) {
        return { ...initialLayoutProps, [property]: value };
    } else {
        return { ...currentProps, [property]: value };
    }
};

export const {
    setGwtOverlayOpen,
    setGlobalError,
    clearError,
    setIsCriticalError,
    setLayoutOption,
    setMultiPathLayoutOption,
    setSearchValue,
    setSearchProperty,
    setSelectedView,
    setSelectedEntry,
    resetLayoutProps,
    resetLayoutWithoutLayoutOptionsProps,
    resetPageLayoutProps,
    resetTableLimit,
    setRequestParams,
    setCurrentPathname,
    setParentPathname,
    setIsMenuHidden,
    setIsPinnedMenu,
    setIsExpandedMenu,
    setActiveMenuId,
    forceMainLogoReload,
    setPageTopBarHeight,
} = appSlice.actions;

export default appSlice;
