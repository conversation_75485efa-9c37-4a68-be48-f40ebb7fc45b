import React from 'react';
import { Outlet } from 'react-router';
import Box from '@mui/material/Box';

import AppBar from 'ui/components/AppNavigationBar/AppBar';
import { HORIZONTAL_MENU_HEIGHT } from 'ui/constants';

const LogoOnlyLayout: React.FC = () => {
    return (
        <Box
            display="flex"
            flexDirection="column"
            height="100vh"
        >
            <AppBar logoClickable={false} />
            <Box
                flex="1"
                height={`calc(100% - ${HORIZONTAL_MENU_HEIGHT}px)`}
                marginTop={`${HORIZONTAL_MENU_HEIGHT}px`}
                position="relative"
            >
                <Outlet />
            </Box>
        </Box>
    );
};

export default React.memo(LogoOnlyLayout);
