import * as utils from './utils';

jest.mock('ui/components/AppNavigationBar/utils', () => ({
    ...jest.requireActual('ui/components/AppNavigationBar/utils'),
    isGwtReactUrl: jest.fn(),
    isGwtWidgetUrl: jest.fn(),
}));

describe('isWithoutMenu', () => {
    const originalLocation = window.location;

    beforeEach(() => {
        Object.defineProperty(window, 'location', {
            writable: true,
            value: { href: '' },
        });
    });

    afterEach(() => {
        (window as any).location.location = originalLocation;
        jest.clearAllMocks();
    });

    it('should return true if it is Vendor Portal', () => {
        const locationPathName = '/vendorPortal/somePath';
        const result = utils.isWithoutMenu(locationPathName);
        expect(result).toBe(true);
    });

    it('should return true if it is Marketplace center', () => {
        const locationPathName = '/marketplace/packages/deployed';
        const result = utils.isWithoutMenu(locationPathName);
        expect(result).toBe(true);
    });

    it('should return true if it is Marketplace center and single package', () => {
        const locationPathName = '/marketplace/packages/123/2.0';
        const result = utils.isWithoutMenu(locationPathName);
        expect(result).toBe(true);
    });

    it('should return false if it is Installed packages', () => {
        const locationPathName = '/marketplace/installedPackages/published';
        const result = utils.isWithoutMenu(locationPathName);
        expect(result).toBe(false);
    });

    it('should return false if it is single Installed package', () => {
        const locationPathName = '/marketplace/packages/installed/123/2.1';
        const result = utils.isWithoutMenu(locationPathName);
        expect(result).toBe(false);
    });

    it('should return false if it is other module', () => {
        const locationPathName = '/bowties/somePath';
        const result = utils.isWithoutMenu(locationPathName);
        expect(result).toBe(false);
    });
});

describe('isGwtWithoutMenu', () => {
    let getGwtModuleFromUrlSpy: jest.SpyInstance;

    beforeAll(() => {
        getGwtModuleFromUrlSpy = jest.spyOn(utils, 'getGwtModuleFromUrl');
    });

    afterAll(() => {
        getGwtModuleFromUrlSpy.mockRestore();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return true if widgetId is VendorPortal', () => {
        const widgetId = 'VendorPortal';
        const result = utils.isGwtWithoutMenu(widgetId);
        expect(result).toBe(true);
    });

    it('should return true if widgetId is MarketplaceCenter', () => {
        const widgetId = 'MarketplaceCenter';
        const result = utils.isGwtWithoutMenu(widgetId);
        expect(result).toBe(true);
    });

    it('should return false if widgetId is not in NO_MENU_MODULES', () => {
        const widgetId = 'someWidgetId';
        const result = utils.isGwtWithoutMenu(widgetId);
        expect(result).toBe(false);
    });

    it('should return true if module from url is VendorPortal', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('VendorPortal');
        const result = utils.isGwtWithoutMenu();
        expect(result).toBe(true);
    });

    it('should return true if module from url is MarketplaceCenter', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('MarketplaceCenter');
        const result = utils.isGwtWithoutMenu();
        expect(result).toBe(true);
    });

    it('should return false if module from url is not in NO_MENU_MODULES', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('SomeModule');
        const result = utils.isGwtWithoutMenu();
        expect(result).toBe(false);
    });

    it('should return false if module from url is null', () => {
        getGwtModuleFromUrlSpy.mockReturnValue(null);
        const result = utils.isGwtWithoutMenu();
        expect(result).toBe(false);
    });
});

describe('isGwtWithLogoOnly', () => {
    let getGwtModuleFromUrlSpy: jest.SpyInstance;

    beforeAll(() => {
        getGwtModuleFromUrlSpy = jest.spyOn(utils, 'getGwtModuleFromUrl');
    });

    afterAll(() => {
        getGwtModuleFromUrlSpy.mockRestore();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return true if widgetId is AnonymousEntry', () => {
        const widgetId = 'AnonymousEntry';
        const result = utils.isGwtWithLogoOnly(widgetId);
        expect(result).toBe(true);
    });

    it('should return true if widgetId is AnonymousRegisterEntry', () => {
        const widgetId = 'AnonymousRegisterEntry';
        const result = utils.isGwtWithLogoOnly(widgetId);
        expect(result).toBe(true);
    });

    it('should return false if widgetId is not in NO_MENU_MODULES', () => {
        const widgetId = 'someWidgetId';
        const result = utils.isGwtWithLogoOnly(widgetId);
        expect(result).toBe(false);
    });

    it('should return true if module from url is AnonymousEntry', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('AnonymousEntry');
        const result = utils.isGwtWithLogoOnly();
        expect(result).toBe(true);
    });

    it('should return true if module from url is AnonymousRegisterEntry', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('AnonymousRegisterEntry');
        const result = utils.isGwtWithLogoOnly();
        expect(result).toBe(true);
    });

    it('should return false if module from url is not in NO_MENU_MODULES', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('SomeModule');
        const result = utils.isGwtWithLogoOnly();
        expect(result).toBe(false);
    });

    it('should return false if module from url is null', () => {
        getGwtModuleFromUrlSpy.mockReturnValue(null);
        const result = utils.isGwtWithLogoOnly();
        expect(result).toBe(false);
    });
});

describe('isWithLogoOnly', () => {
    it('should return true if it is Anonymous register entry', () => {
        const locationPathName = '/registers/table_253630/entries/anonymous';
        const result = utils.isWithLogoOnly(locationPathName);
        expect(result).toBe(true);
    });

    it('should return false if it is other module', () => {
        const locationPathName = '/bowties/somePath';
        const result = utils.isWithLogoOnly(locationPathName);
        expect(result).toBe(false);
    });
});

describe('createRouteRegex', () => {
    it('should escape special regex characters', () => {
        const pattern = '/test^$.*+?()[]{}|';
        const regex = utils.createRouteRegex(pattern);
        expect(regex.source).toBe('\\/test\\^\\$\\.\\*\\+\\?\\(\\)\\[\\]\\{\\}\\|');
    });

    it('should replace :param with dynamic regex segment', () => {
        const pattern = '/register/:tableName';
        const regex = utils.createRouteRegex(pattern);
        expect(regex.source).toBe('\\/register\\/([^/]+)');
    });

    it('should handle multiple dynamic segments', () => {
        const pattern = '/user/:userId/profile/:profileId';
        const regex = utils.createRouteRegex(pattern);
        expect(regex.source).toBe('\\/user\\/([^/]+)\\/profile\\/([^/]+)');
    });

    it('should handle static routes correctly', () => {
        const pattern = '/home/<USER>';
        const regex = utils.createRouteRegex(pattern);
        expect(regex.source).toBe('\\/home\\/about');
    });

    it('should match route patterns correctly', () => {
        const pattern = '/user/:id';
        const regex = utils.createRouteRegex(pattern);

        expect(regex.test('/user/123')).toBe(true);
        expect(regex.test('/user/abc')).toBe(true);

        expect(regex.test('/user')).toBe(false);
        expect(regex.test('/user/123/extra')).toBe(true);
    });

    it('should handle trailing slashes correctly', () => {
        const pattern = '/user/:id/';
        const regex = utils.createRouteRegex(pattern);

        expect(regex.test('/user/123/')).toBe(true);
        expect(regex.test('/user/123')).toBe(false);
    });
});

describe('isVendorPortal', () => {
    it('should return true if locationPathName starts with /vendorPortal', () => {
        const locationPathName = '/vendorPortal';
        const result = utils.isVendorPortal(locationPathName);
        expect(result).toBe(true);
    });

    it('should return true if locationPathName starts with /vendorPortal/', () => {
        const locationPathName = '/vendorPortal/some/nested/path';
        const result = utils.isVendorPortal(locationPathName);
        expect(result).toBe(true);
    });

    it('should return false if locationPathName is different', () => {
        const locationPathName = '/bowties/123';
        const result = utils.isVendorPortal(locationPathName);
        expect(result).toBe(false);
    });

    it('should return false for the root path', () => {
        const locationPathName = '/';
        const result = utils.isVendorPortal(locationPathName);
        expect(result).toBe(false);
    });
});

describe('isGwtVendorPortal', () => {
    let getGwtModuleFromUrlSpy: jest.SpyInstance;

    beforeAll(() => {
        getGwtModuleFromUrlSpy = jest.spyOn(utils, 'getGwtModuleFromUrl');
    });

    afterAll(() => {
        getGwtModuleFromUrlSpy.mockRestore();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return true if getGwtModuleFromUrl returns "VendorPortal"', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('VendorPortal');
        const result = utils.isGwtVendorPortal();
        expect(result).toBe(true);
        expect(getGwtModuleFromUrlSpy).toHaveBeenCalledTimes(1);
    });

    it('should return false if getGwtModuleFromUrl returns a different string', () => {
        getGwtModuleFromUrlSpy.mockReturnValue('SomeOtherModule');
        const result = utils.isGwtVendorPortal();
        expect(result).toBe(false);
        expect(getGwtModuleFromUrlSpy).toHaveBeenCalledTimes(1);
    });
});
