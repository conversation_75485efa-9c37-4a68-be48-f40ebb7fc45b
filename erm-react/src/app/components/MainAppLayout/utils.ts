import { VendorPortalPath } from 'vendorRiskManagement/routes/vendorPortalRoutes';
import { LOCAL_LOGO_ONLY_PATHS, LOCAL_NO_MENU_PATHS, LOCAL_WITH_MENU_PATHS, LOGO_ONLY_MODULES, NO_MENU_MODULES } from './constants';
import { isGwtReactUrl, isGwtWidgetUrl } from 'ui/components/AppNavigationBar/utils';

export const isVendorPortal = (locationPathName: string) => {
    const regex = createRouteRegex(VendorPortalPath.VENDOR_PORTAL);

    if (regex.test(locationPathName)) {
        return true;
    }

    return false;
};

export const isGwtVendorPortal = () => {
    const module = getGwtModuleFromUrl();

    return module === 'VendorPortal';
};

export const isWithoutMenu = (locationPathName: string) => {
    let withoutMenu = false;

    LOCAL_NO_MENU_PATHS.forEach((path) => {
        const regex = createRouteRegex(path);
        if (regex.test(locationPathName)) {
            let isSpecialWithMenu = false;
            LOCAL_WITH_MENU_PATHS.forEach((withMenuPath) => {
                const withMenuRegex = createRouteRegex(withMenuPath);
                if (withMenuRegex.test(locationPathName)) {
                    isSpecialWithMenu = true;
                    return;
                }
            });

            if (!isSpecialWithMenu) {
                withoutMenu = true;
                return;
            }
        }
    });

    return withoutMenu;
};

export const getGwtModuleFromUrl = (): string | null => {
    const pathName = window.location.pathname;
    let widget: string | null = null;

    if (isGwtWidgetUrl(pathName)) {
        const params = new URLSearchParams(window.location.search);
        widget = params.get('widget');
    } else if (isGwtReactUrl(pathName)) {
        const match = pathName.match(/(?<=worms\/client\/app\/react\/)([^/]+)/);
        widget = match ? match[0] : null;
    }

    return widget;
};

export const isGwtWidgetPage = () => {
    const pathName = window.location.pathname;
    return isGwtWidgetUrl(pathName);
};

export const isGwtWithoutMenu = (widgetId?: string) => {
    // if we know widgetId, we can check it directly
    if (widgetId) {
        return NO_MENU_MODULES.includes(widgetId);
    }

    // otherwise, we need to get module from the URL
    const widget = getGwtModuleFromUrl();

    if (widget && NO_MENU_MODULES.includes(widget)) {
        return true;
    }

    return false;
};

export const isGwtWithLogoOnly = (widgetId?: string) => {
    // if we know widgetId, we can check it directly
    if (widgetId) {
        return LOGO_ONLY_MODULES.includes(widgetId);
    }

    // otherwise, we need to get module from the URL
    const widget = getGwtModuleFromUrl();

    if (widget && LOGO_ONLY_MODULES.includes(widget)) {
        return true;
    }

    return false;
};

export const isWithLogoOnly = (locationPathName: string) => {
    let withLogoOnly = false;

    LOCAL_LOGO_ONLY_PATHS.forEach((path) => {
        const regex = createRouteRegex(path);
        if (regex.test(locationPathName)) {
            withLogoOnly = true;
            return;
        }
    });

    return withLogoOnly;
};

/**
 * Create a route regex from a route pattern.
 * @param routePattern
 * @returns regex
 */
export const createRouteRegex = (routePattern: string): RegExp => {
    // Escape special regex characters and replace :param with dynamic segment
    const escapedPattern = routePattern
        .replace(/[\^$.*+?()[\]{}|]/g, '\\$&') // Escape special characters
        .replace(/\/:([^/]+)/g, '/([^/]+)'); // Replace ":param" with "([^/]+)"

    // Ensure it matches the full pathname
    return new RegExp(`${escapedPattern}`);
};
