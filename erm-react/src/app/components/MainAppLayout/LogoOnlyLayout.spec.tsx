import React from 'react';

import { render, screen } from 'test/utils';
import LogoOnlyLayout from './LogoOnlyLayout';
import { HORIZONTAL_MENU_HEIGHT } from 'ui/constants';

jest.mock('ui/components/AppNavigationBar/AppBar', () => jest.fn(() => <div data-testid="app-bar" />));
jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    Outlet: jest.fn(() => <div data-testid="outlet-content" />),
}));

describe('LogoOnlyLayout', () => {
    it('should render', () => {
        const { container } = render(<LogoOnlyLayout />);
        expect(container).toMatchSnapshot();
    });

    it('renders the AppBar component', () => {
        render(<LogoOnlyLayout />);
        const appBar = screen.getByTestId('app-bar');
        expect(appBar).toBeInTheDocument();
    });

    it('renders the Outlet component for nested routes', () => {
        render(<LogoOnlyLayout />);
        const outletContent = screen.getByTestId('outlet-content');
        expect(outletContent).toBeInTheDocument();
    });

    it('sets the correct styles for the content container', () => {
        render(<LogoOnlyLayout />);
        const contentBox = screen.getByTestId('outlet-content').parentElement;
        expect(contentBox).toHaveStyle({
            flex: '1',
            position: 'relative',
            marginTop: `${HORIZONTAL_MENU_HEIGHT}px`,
            height: `calc(100% - ${HORIZONTAL_MENU_HEIGHT}px)`,
        });
    });
});
