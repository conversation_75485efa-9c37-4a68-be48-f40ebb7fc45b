import React, { useEffect, useMemo } from 'react';
import { Outlet, useLocation } from 'react-router';
import { useSelector } from 'react-redux';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';

import { HORIZONTAL_MENU_HEIGHT, RESPONSIVE_BREAKPOINTS, VERTICAL_MENU_WIDTH_COLLAPSED, VERTICAL_MENU_WIDTH_EXPANDED } from 'ui/constants';
import { useDispatch } from 'store';
import { isProduction, isReactUiClient } from 'config';
import { getIsCriticalError, getIsMenuHidden, getIsPinnedMenu } from 'app/selectors';
import { setIsMenuHidden } from 'app/reducer';
import useCriticalAppApi from 'common/hooks/useCriticalAppApi';
import { strings } from 'common/utils/i18n';
import { HorizontalNavigationBar, VerticalNavigationBar } from 'ui/components/AppNavigationBar';

import LogoOnlyLayout from './LogoOnlyLayout';
import { isGwtWithoutMenu, isVendorPortal, isWithLogoOnly, isWithoutMenu } from './utils';

import { AppEventType, emitAppEvent } from 'app/NotificationService';
import useUserMenuOrientation from 'common/hooks/useUserMenuOrientation';
import Loading from 'common/components/Loading';
import ChatBot from 'ai/components/ChatBot';
import UnexpectedErrorPage from '../UnexpectedErrorPage';
import { useGetSystemConfigurationQuery } from 'app/rtkApi';

const MainAppLayout: React.FC = () => {
    const dispatch = useDispatch();
    const location = useLocation();
    const isSmallScreen = useMediaQuery(`(max-width:${RESPONSIVE_BREAKPOINTS.small.max}px)`);
    const isMediumScreen = useMediaQuery(`(min-width:${RESPONSIVE_BREAKPOINTS.medium.min}px) and (max-width:${RESPONSIVE_BREAKPOINTS.medium.max}px)`);
    const isLargeScreen = useMediaQuery(`(min-width:${RESPONSIVE_BREAKPOINTS.large.min}px)`);
    const isMenuHidden = useSelector(getIsMenuHidden);
    const isPinnedMenu = useSelector(getIsPinnedMenu);
    const isLogoOnly = isWithLogoOnly(location.pathname);
    const isVendorPortalPage = isVendorPortal(location.pathname);
    const isCriticalError = useSelector(getIsCriticalError);
    const { hasVerticalMenu, isLoading: isLoadingUserMenuOrientation } = useUserMenuOrientation();
    const { data: systemConfig } = useGetSystemConfigurationQuery();
    const isChatBotEnabledInSystemConfig = systemConfig?.ai_copilot_feature === 'true';

    useCriticalAppApi({
        skipMenu: isLogoOnly || isMenuHidden || (isProduction && !isReactUiClient),
        skipPermissions: isVendorPortalPage,
    });

    useEffect(() => {
        if (location.pathname === '/') {
            return;
        }

        emitAppEvent({
            type: AppEventType.NAVIGATION,
            locationPathName: location.pathname,
        });
    }, [location]);

    useEffect(() => {
        if (!isProduction) {
            const menuHidden = isWithoutMenu(location.pathname);
            dispatch(setIsMenuHidden(menuHidden));
        }
    }, [location, dispatch]);

    const mainContentOffsetTop = useMemo(() => {
        return hasVerticalMenu && !isSmallScreen ? 0 : HORIZONTAL_MENU_HEIGHT;
    }, [hasVerticalMenu, isSmallScreen]);

    const mainContentOffsetLeft = useMemo(() => {
        if (isSmallScreen || !hasVerticalMenu) {
            return 0;
        }

        if (isMediumScreen) {
            return VERTICAL_MENU_WIDTH_COLLAPSED;
        }

        if (isLargeScreen) {
            return isPinnedMenu ? VERTICAL_MENU_WIDTH_EXPANDED : VERTICAL_MENU_WIDTH_COLLAPSED;
        }

        return 0;
    }, [isSmallScreen, isMediumScreen, isPinnedMenu, isLargeScreen, hasVerticalMenu]);

    if (isCriticalError) {
        return (
            <UnexpectedErrorPage
                showAppBar={!isProduction || (isProduction && !isReactUiClient && isGwtWithoutMenu())}
                errorStack={!isProduction ? strings('common:message.criticalApiFailed') : undefined}
            />
        );
    }

    if (isLoadingUserMenuOrientation) {
        return <Loading />;
    }

    return (
        <>
            {/*  in production we render menu outside of #react-root container */}
            {isProduction || isMenuHidden ? (
                <Outlet />
            ) : isLogoOnly ? (
                <LogoOnlyLayout />
            ) : (
                <>
                    {hasVerticalMenu || isSmallScreen ? <VerticalNavigationBar /> : <HorizontalNavigationBar />}
                    <Box
                        position="relative"
                        height={`calc(100% - ${mainContentOffsetTop}px)`}
                        mt={`${mainContentOffsetTop}px`}
                        ml={`${mainContentOffsetLeft}px`}
                        data-testid="main-app-content"
                    >
                        <Outlet />
                    </Box>
                </>
            )}
            {/* on production chatbot is part of modern ui app, here only for local development */}
            {!isProduction && isChatBotEnabledInSystemConfig && <ChatBot />}
        </>
    );
};

export default MainAppLayout;
