import React from 'react';
import { MemoryRouter } from 'react-router';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useSelector } from 'react-redux';

import { render, screen } from 'test/utils';
import MainAppLayout from './MainAppLayout';
import * as config from 'config';
import { getIsCriticalError, getIsMenuHidden, getIsPinnedMenu } from 'app/selectors';
import { isWithoutMenu, isWithLogoOnly, isGwtWithoutMenu, isVendorPortal } from './utils';
import UnexpectedErrorPage from 'app/components/UnexpectedErrorPage';
import { mockedAppSlice } from 'app/mock';
import { strings } from 'common/utils/i18n';
import useUserMenuOrientation from 'common/hooks/useUserMenuOrientation';
import { HORIZONTAL_MENU_HEIGHT, RESPONSIVE_BREAKPOINTS, VERTICAL_MENU_WIDTH_COLLAPSED, VERTICAL_MENU_WIDTH_EXPANDED } from 'ui/constants';
import { AppEventType, emitAppEvent } from 'app/NotificationService';

jest.mock('common/hooks/useUserMenuOrientation', () => jest.fn());

jest.mock('app/NotificationService', () => ({
    AppEventType: {
        NAVIGATION: 'navigation',
    },
    emitAppEvent: jest.fn(),
}));

jest.mock('app/components/UnexpectedErrorPage', () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="unexpected-error-page">error</div>),
}));

const mockUseCriticalAppApi = jest.fn();
jest.mock('common/hooks/useCriticalAppApi', () => ({
    __esModule: true,
    default: (args) => mockUseCriticalAppApi(args),
}));

const mockUseGetSystemConfigurationQuery = jest.fn();
jest.mock('app/rtkApi', () => ({
    systemApi: {
        endpoints: {
            srsGetScaleSetsUsingGet: {
                matchFulfilled: jest.fn(),
            },
            getSystemConfiguration: {
                matchFulfilled: jest.fn(),
            },
        },
    },
    useGetSystemConfigurationQuery: () => mockUseGetSystemConfigurationQuery(),
}));

jest.mock('user/rtkApi', () => ({
    usersApi: {
        endpoints: {
            pursGetCurrentUserUsingGet: {
                matchFulfilled: jest.fn(),
            },
        },
    },
}));

jest.mock('ai/components/ChatBot', () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="chatbot">ChatBot</div>),
}));

const mockConfig = config as { isProduction: boolean; isReactUiClient: boolean };
jest.mock('@mui/material/useMediaQuery', () => jest.fn());

jest.mock('ui/components/AppNavigationBar', () => ({
    VerticalNavigationBar: jest.fn(() => <div data-testid="vertical-nav-bar" />),
    HorizontalNavigationBar: jest.fn(() => <div data-testid="horizontal-nav-bar" />),
}));
jest.mock('./LogoOnlyLayout', () => jest.fn(() => <div data-testid="logo-only-layout" />));
jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    Outlet: jest.fn(() => <div data-testid="outlet-content" />),
}));
jest.mock('react-redux', () => ({
    ...jest.requireActual('react-redux'),
    useSelector: jest.fn(),
}));

jest.mock('./utils', () => ({
    ...jest.requireActual('./utils'),
    isWithLogoOnly: jest.fn(),
    isWithoutMenu: jest.fn(),
    isGwtWithoutMenu: jest.fn(),
    isVendorPortal: jest.fn(),
}));

const setup = () => {
    return render(
        <MemoryRouter>
            <MainAppLayout />
        </MemoryRouter>,
        {
            preloadedState: {
                app: {
                    ...mockedAppSlice,
                },
            },
        },
    );
};

describe('MainAppLayout', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (useMediaQuery as jest.Mock).mockReturnValue(true);
        (isWithLogoOnly as jest.Mock).mockReturnValue(false);
        (isWithoutMenu as jest.Mock).mockReturnValue(false);
        (isVendorPortal as jest.Mock).mockReturnValue(false);
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === getIsPinnedMenu) return false;
            if (selector === getIsCriticalError) return false;
            return undefined;
        });
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: false,
            isLoading: false,
        });

        mockUseGetSystemConfigurationQuery.mockReturnValue({
            data: { ai_copilot_feature: 'false' },
        });

        mockConfig.isProduction = false;
        mockConfig.isReactUiClient = false;
    });

    it('renders only Outlet when in production', () => {
        mockConfig.isProduction = true;
        (isWithoutMenu as jest.Mock).mockReturnValue(true);
        setup();
        const outletContent = screen.getByTestId('outlet-content');
        expect(outletContent).toBeInTheDocument();
    });

    it('renders only Outlet when it is without menu', () => {
        (isWithoutMenu as jest.Mock).mockReturnValue(true);
        setup();

        const outletContent = screen.getByTestId('outlet-content');
        expect(outletContent).toBeInTheDocument();
    });

    it('renders only logo layout', () => {
        (isWithLogoOnly as jest.Mock).mockReturnValue(true);
        setup();
        const component = screen.getByTestId('logo-only-layout');
        expect(component).toBeInTheDocument();
    });

    it('renders loading when loading user menu orientation', () => {
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: false,
            isLoading: true,
        });
        setup();
        const component = screen.getByText('Loading...');
        expect(component).toBeInTheDocument();
    });

    it('renders the VerticalNavigationBar component and sets the correct styles when it is small screen', () => {
        (useMediaQuery as jest.Mock).mockImplementation((query) => query === `(max-width:${RESPONSIVE_BREAKPOINTS.small.max}px)`);
        setup();

        const navBar = screen.getByTestId('vertical-nav-bar');
        const contentBox = screen.getByTestId('outlet-content').parentElement;

        expect(navBar).toBeInTheDocument();
        expect(contentBox).toHaveStyle({
            marginTop: `${HORIZONTAL_MENU_HEIGHT}px`,
            marginLeft: '0px',
            height: `calc(100% - ${HORIZONTAL_MENU_HEIGHT}px)`,
        });
    });

    it('renders the VerticalNavigationBar component and sets the correct styles when it is medium screen', () => {
        (useMediaQuery as jest.Mock).mockImplementation(
            (query) => query === `(min-width:${RESPONSIVE_BREAKPOINTS.medium.min}px) and (max-width:${RESPONSIVE_BREAKPOINTS.medium.max}px)`,
        );
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });
        setup();

        const navBar = screen.getByTestId('vertical-nav-bar');
        const contentBox = screen.getByTestId('outlet-content').parentElement;

        expect(navBar).toBeInTheDocument();
        expect(contentBox).toHaveStyle({
            marginTop: '0px',
            marginLeft: `${VERTICAL_MENU_WIDTH_COLLAPSED}px`,
            height: 'calc(100% - 0px)',
        });
    });

    it('renders the VerticalNavigationBar component and sets the correct styles when it is large screen and menu is not pinned', () => {
        (useMediaQuery as jest.Mock).mockImplementation((query) => query === `(min-width:${RESPONSIVE_BREAKPOINTS.large.min}px)`);
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });
        setup();

        const navBar = screen.getByTestId('vertical-nav-bar');
        const contentBox = screen.getByTestId('outlet-content').parentElement;

        expect(navBar).toBeInTheDocument();
        expect(contentBox).toHaveStyle({
            marginTop: '0px',
            marginLeft: `${VERTICAL_MENU_WIDTH_COLLAPSED}px`,
            height: 'calc(100% - 0px)',
        });
    });

    it('renders the VerticalNavigationBar component and sets the correct styles when it is large screen and menu is pinned', () => {
        (useMediaQuery as jest.Mock).mockImplementation((query) => query === `(min-width:${RESPONSIVE_BREAKPOINTS.large.min}px)`);
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === getIsPinnedMenu) return true;
            return undefined;
        });
        setup();

        const navBar = screen.getByTestId('vertical-nav-bar');
        const contentBox = screen.getByTestId('outlet-content').parentElement;

        expect(navBar).toBeInTheDocument();
        expect(contentBox).toHaveStyle({
            marginTop: '0px',
            marginLeft: `${VERTICAL_MENU_WIDTH_EXPANDED}px`,
            height: 'calc(100% - 0px)',
        });
    });

    it('renders the HorizontalNavigationBar component and sets the correct styles', () => {
        (useMediaQuery as jest.Mock).mockImplementation((query) => query === `(min-width:${RESPONSIVE_BREAKPOINTS.large.min}px)`);
        setup();

        const navBar = screen.getByTestId('horizontal-nav-bar');
        const contentBox = screen.getByTestId('outlet-content').parentElement;

        expect(navBar).toBeInTheDocument();
        expect(contentBox).toHaveStyle({
            marginTop: `${HORIZONTAL_MENU_HEIGHT}px`,
            marginLeft: '0px',
            height: `calc(100% - ${HORIZONTAL_MENU_HEIGHT}px)`,
        });
    });

    it('does not render unexpected error page when no critical error occurs', () => {
        (useMediaQuery as jest.Mock).mockReturnValue(false);
        setup();
        const horizontalNav = screen.getByTestId('horizontal-nav-bar');
        const unexpectedErrorPage = screen.queryByTestId('unexpected-error-page');

        expect(unexpectedErrorPage).not.toBeInTheDocument();
        expect(horizontalNav).toBeInTheDocument();
    });

    it('renders unexpected error page with correct props when critical error occurs and is not production', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === getIsCriticalError) return true;
            return undefined;
        });
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const unexpectedErrorPage = screen.getByTestId('unexpected-error-page');

        expect(unexpectedErrorPage).toBeInTheDocument();
        expect(horizontalNav).not.toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith(
            {
                showAppBar: true,
                errorStack: strings('common:message.criticalApiFailed'),
            },
            {},
        );
    });

    it('renders unexpected error page with correct props when critical error occurs and is production and without modern client', () => {
        mockConfig.isProduction = true;
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(false);
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === getIsCriticalError) return true;
            return undefined;
        });
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const unexpectedErrorPage = screen.getByTestId('unexpected-error-page');

        expect(unexpectedErrorPage).toBeInTheDocument();
        expect(horizontalNav).not.toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith(
            {
                showAppBar: false,
                errorStack: undefined,
            },
            {},
        );
    });

    it('renders unexpected error page with correct props when critical error occurs and is production and without modern client and without menu', () => {
        mockConfig.isProduction = true;
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === getIsCriticalError) return true;
            return undefined;
        });
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const unexpectedErrorPage = screen.getByTestId('unexpected-error-page');

        expect(unexpectedErrorPage).toBeInTheDocument();
        expect(horizontalNav).not.toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith(
            {
                showAppBar: true,
                errorStack: undefined,
            },
            {},
        );
    });

    it('calls useCriticalAppApi with correct arguments', () => {
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipMenu: false, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when is logo only', () => {
        (isWithLogoOnly as jest.Mock).mockReturnValue(true);
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipMenu: true, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when is menu hidden', () => {
        (isWithoutMenu as jest.Mock).mockReturnValue(true);
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === getIsMenuHidden) return true;
            return undefined;
        });
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipMenu: true, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when it is vendor portal', () => {
        (isWithoutMenu as jest.Mock).mockReturnValue(true);
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === getIsMenuHidden) return true;
            return undefined;
        });
        (isVendorPortal as jest.Mock).mockReturnValue(true);
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipMenu: true, skipPermissions: true });
    });

    it('calls useCriticalAppApi with correct arguments when is production and modern client', () => {
        mockConfig.isProduction = true;
        mockConfig.isReactUiClient = true;
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipMenu: false, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when is production and not modern client', () => {
        mockConfig.isProduction = true;
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipMenu: true, skipPermissions: false });
    });

    describe('ChatBot rendering', () => {
        it('renders ChatBot when not in production and chatbot is enabled in system config', () => {
            mockConfig.isProduction = false;
            mockUseGetSystemConfigurationQuery.mockReturnValue({
                data: { ai_copilot_feature: 'true' },
            });
            setup();

            const chatbot = screen.getByTestId('chatbot');
            expect(chatbot).toBeInTheDocument();
        });

        it('does not render ChatBot when in production', () => {
            mockConfig.isProduction = true;
            mockUseGetSystemConfigurationQuery.mockReturnValue({
                data: { ai_copilot_feature: 'true' },
            });
            setup();

            const chatbot = screen.queryByTestId('chatbot');
            expect(chatbot).not.toBeInTheDocument();
        });

        it('does not render ChatBot when chatbot is disabled in system config', () => {
            mockConfig.isProduction = false;
            mockUseGetSystemConfigurationQuery.mockReturnValue({
                data: { ai_copilot_feature: 'false' },
            });
            setup();

            const chatbot = screen.queryByTestId('chatbot');
            expect(chatbot).not.toBeInTheDocument();
        });

        it('does not render ChatBot when system config data is undefined', () => {
            mockConfig.isProduction = false;
            mockUseGetSystemConfigurationQuery.mockReturnValue({
                data: undefined,
            });
            setup();

            const chatbot = screen.queryByTestId('chatbot');
            expect(chatbot).not.toBeInTheDocument();
        });
    });

    describe('Event emission', () => {
        it('emits navigation event when location pathname changes and is not root', () => {
            render(
                <MemoryRouter initialEntries={['/some-path']}>
                    <MainAppLayout />
                </MemoryRouter>,
                {
                    preloadedState: {
                        app: {
                            ...mockedAppSlice,
                        },
                    },
                },
            );

            expect(emitAppEvent).toHaveBeenCalledWith({
                type: AppEventType.NAVIGATION,
                locationPathName: '/some-path',
            });
        });

        it('emits navigation event for different paths', () => {
            render(
                <MemoryRouter initialEntries={['/another-path']}>
                    <MainAppLayout />
                </MemoryRouter>,
                {
                    preloadedState: {
                        app: {
                            ...mockedAppSlice,
                        },
                    },
                },
            );

            expect(emitAppEvent).toHaveBeenCalledWith({
                type: AppEventType.NAVIGATION,
                locationPathName: '/another-path',
            });
        });

        it('does not emit navigation event when location pathname is root', () => {
            render(
                <MemoryRouter initialEntries={['/']}>
                    <MainAppLayout />
                </MemoryRouter>,
                {
                    preloadedState: {
                        app: {
                            ...mockedAppSlice,
                        },
                    },
                },
            );

            expect(emitAppEvent).not.toHaveBeenCalled();
        });
    });
});
