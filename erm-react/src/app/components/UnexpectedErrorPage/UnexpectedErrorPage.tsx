import React from 'react';
import Box from '@mui/material/Box';

import { AlertType } from '@protecht/ui-library/library/types';
import { InfoBoxContent } from '@protecht/ui-library/library/components/InfoBox';

import InfoPage from 'common/components/InfoPage';
import { strings } from 'common/utils/i18n';
import { APP_LOGO_MAIN_URL } from 'config';
import { HORIZONTAL_MENU_HEIGHT } from 'ui/constants';

type UnexpectedErrorPageProps = {
    showAppBar?: boolean;
    errorStack?: string;
    showError?: boolean;
    isFullScreen?: boolean;
    id?: string;
};

const UnexpectedErrorPage: React.FC<UnexpectedErrorPageProps> = ({ showAppBar = true, showError = true, isFullScreen = false, errorStack, id }) => {
    return (
        <Box
            position={isFullScreen ? 'fixed' : 'static'}
            top={0}
            left={0}
            right={0}
            bottom={isFullScreen ? 0 : undefined}
            zIndex={isFullScreen ? 1100 : undefined}
            data-testid="unexpected-error-container"
            display="flex"
            flexDirection="column"
            height="100%"
            id={id}
        >
            {showAppBar && (
                <Box
                    height={HORIZONTAL_MENU_HEIGHT}
                    bgcolor="primary.main"
                    display="flex"
                    alignItems="center"
                    data-testid="unexpected-error-appbar"
                    flexShrink={0}
                >
                    <img
                        src={APP_LOGO_MAIN_URL}
                        alt="Logo"
                        style={{ maxHeight: '40px' }}
                    />
                </Box>
            )}
            {showError && (
                <InfoPage
                    subtitle={strings('common:message.unexpectedError')}
                    message={strings('common:message.unexpectedErrorMessage')}
                    type={AlertType.Error}
                    infoBoxSx={!errorStack ? { maxWidth: '80%' } : {}}
                >
                    {errorStack && (
                        <InfoBoxContent>
                            <Box
                                overflow="auto"
                                maxHeight={600}
                                bgcolor="protechtGrey.grey_231"
                                data-testid="unexpected-error-stack"
                            >
                                <pre style={{ whiteSpace: 'pre', padding: '20px', margin: '5px', overflowX: 'auto' }}>
                                    <code>{errorStack}</code>
                                </pre>
                            </Box>
                        </InfoBoxContent>
                    )}
                </InfoPage>
            )}
        </Box>
    );
};

export default UnexpectedErrorPage;
