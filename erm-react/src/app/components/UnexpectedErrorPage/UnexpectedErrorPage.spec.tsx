import React from 'react';
import { render, screen } from 'test/utils';
import UnexpectedErrorPage from './UnexpectedErrorPage';

describe('UnexpectedErrorPage', () => {
    it('renders app bar when showAppBar is true', () => {
        render(<UnexpectedErrorPage showAppBar={true} />);
        expect(screen.getByTestId('unexpected-error-appbar')).toBeInTheDocument();
    });

    it('does not render app bar when showAppBar is false', () => {
        render(<UnexpectedErrorPage showAppBar={false} />);
        expect(screen.queryByTestId('unexpected-error-appbar')).not.toBeInTheDocument();
    });

    it('renders error stack when errorStack is provided', () => {
        const errorStack = 'Error: Test Error';
        render(<UnexpectedErrorPage errorStack={errorStack} />);
        expect(screen.getByTestId('unexpected-error-stack')).toBeInTheDocument();
        expect(screen.getByText(errorStack)).toBeInTheDocument();
    });

    it('does not render error stack when errorStack is not provided', () => {
        render(<UnexpectedErrorPage />);
        expect(screen.queryByTestId('unexpected-error-stack')).not.toBeInTheDocument();
    });

    it('renders error when showError is true', () => {
        render(<UnexpectedErrorPage showError={true} />);
        expect(screen.getByTestId('infoBox')).toBeInTheDocument();
    });

    it('does not render error when showError is false', () => {
        render(<UnexpectedErrorPage showError={false} />);
        expect(screen.queryByTestId('infoBox')).not.toBeInTheDocument();
    });

    it('renders correctly not in fullscreen mode', () => {
        const { container } = render(<UnexpectedErrorPage isFullScreen={false} />);
        expect(container).toMatchSnapshot();
    });

    it('renders correctly in fullscreen mode', () => {
        const { container } = render(<UnexpectedErrorPage isFullScreen={true} />);
        expect(container).toMatchSnapshot();
    });
});
