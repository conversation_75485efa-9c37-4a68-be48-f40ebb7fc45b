// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UnexpectedErrorPage renders correctly in fullscreen mode 1`] = `
<div>
  <div
    class="MuiBox-root css-krme3f"
    data-testid="unexpected-error-container"
  >
    <div
      class="MuiBox-root css-14vgrjh"
      data-testid="unexpected-error-appbar"
    >
      <img
        alt="Logo"
        src="/camilla/worms/client/styles/style/logo_left"
        style="max-height: 40px;"
      />
    </div>
    <div
      class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-container MuiGrid-item css-1w5ebv4-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-item css-259gsz-MuiGrid-root"
          >
            <div
              class="MuiBox-root css-fxa6q7"
              data-testid="infoBox"
            >
              <div
                class="MuiBox-root css-1sb31b2"
              >
                <div
                  class="MuiBox-root css-72fd9l"
                >
                  <svg
                    data-icon="alert"
                    data-testid="infoBox-icon"
                    fill="currentColor"
                    height="48"
                    viewBox="0 0 52 52"
                    width="48"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="Warning alt2 1"
                    >
                      <path
                        d="M17.29 47.3687L4.82764 34.9064V17.2821L17.29 4.81976H34.9143L47.3766 17.2821V34.9064L34.9143 47.3687H17.29Z"
                        fill="#F00000"
                        id="Vector"
                        stroke="#920004"
                        stroke-miterlimit="10"
                        stroke-width="4"
                      />
                      <path
                        d="M26.0164 37.8855C25.1419 37.8855 24.4206 37.6203 23.8525 37.0905C23.2841 36.5605 23 35.9182 23 35.1641C23 34.3772 23.2868 33.7326 23.8607 33.2297C24.4344 32.7271 25.153 32.4756 26.0164 32.4756C26.8907 32.4756 27.6066 32.7298 28.164 33.2379C28.7214 33.7462 29 34.3883 29 35.1641C29 35.951 28.7239 36.6014 28.1722 37.1151C27.6202 37.6286 26.9017 37.8855 26.0164 37.8855ZM28.705 14L28.0656 30.1969H23.8689L23.2623 14H28.705Z"
                        fill="#FAFAFA"
                        id="Light"
                      />
                    </g>
                  </svg>
                </div>
                <div
                  class="MuiBox-root css-paf9g0"
                >
                  <div
                    class="MuiBox-root css-0"
                  >
                    <h6
                      class="MuiTypography-root MuiTypography-h6 css-1y2uior-MuiTypography-root"
                      data-testid="infoBox-title"
                    >
                      An unexpected error has occurred.
                    </h6>
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-zutzfa-MuiTypography-root"
                      data-testid="infoBox-message"
                    >
                      This page or feature could not be opened right now. We apologise for the inconvenience. Try again later and if the problem persists contact your administrator.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`UnexpectedErrorPage renders correctly not in fullscreen mode 1`] = `
<div>
  <div
    class="MuiBox-root css-19oxd5c"
    data-testid="unexpected-error-container"
  >
    <div
      class="MuiBox-root css-14vgrjh"
      data-testid="unexpected-error-appbar"
    >
      <img
        alt="Logo"
        src="/camilla/worms/client/styles/style/logo_left"
        style="max-height: 40px;"
      />
    </div>
    <div
      class="MuiGrid-root MuiGrid-container css-9rv5j6-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-item css-1ggnfgx-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-container MuiGrid-item css-1w5ebv4-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-item css-259gsz-MuiGrid-root"
          >
            <div
              class="MuiBox-root css-fxa6q7"
              data-testid="infoBox"
            >
              <div
                class="MuiBox-root css-1sb31b2"
              >
                <div
                  class="MuiBox-root css-72fd9l"
                >
                  <svg
                    data-icon="alert"
                    data-testid="infoBox-icon"
                    fill="currentColor"
                    height="48"
                    viewBox="0 0 52 52"
                    width="48"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="Warning alt2 1"
                    >
                      <path
                        d="M17.29 47.3687L4.82764 34.9064V17.2821L17.29 4.81976H34.9143L47.3766 17.2821V34.9064L34.9143 47.3687H17.29Z"
                        fill="#F00000"
                        id="Vector"
                        stroke="#920004"
                        stroke-miterlimit="10"
                        stroke-width="4"
                      />
                      <path
                        d="M26.0164 37.8855C25.1419 37.8855 24.4206 37.6203 23.8525 37.0905C23.2841 36.5605 23 35.9182 23 35.1641C23 34.3772 23.2868 33.7326 23.8607 33.2297C24.4344 32.7271 25.153 32.4756 26.0164 32.4756C26.8907 32.4756 27.6066 32.7298 28.164 33.2379C28.7214 33.7462 29 34.3883 29 35.1641C29 35.951 28.7239 36.6014 28.1722 37.1151C27.6202 37.6286 26.9017 37.8855 26.0164 37.8855ZM28.705 14L28.0656 30.1969H23.8689L23.2623 14H28.705Z"
                        fill="#FAFAFA"
                        id="Light"
                      />
                    </g>
                  </svg>
                </div>
                <div
                  class="MuiBox-root css-paf9g0"
                >
                  <div
                    class="MuiBox-root css-0"
                  >
                    <h6
                      class="MuiTypography-root MuiTypography-h6 css-1y2uior-MuiTypography-root"
                      data-testid="infoBox-title"
                    >
                      An unexpected error has occurred.
                    </h6>
                    <p
                      class="MuiTypography-root MuiTypography-body1 css-zutzfa-MuiTypography-root"
                      data-testid="infoBox-message"
                    >
                      This page or feature could not be opened right now. We apologise for the inconvenience. Try again later and if the problem persists contact your administrator.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
