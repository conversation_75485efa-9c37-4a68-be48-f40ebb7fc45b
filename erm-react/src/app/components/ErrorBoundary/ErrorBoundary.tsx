import React, { useCallback, useEffect } from 'react';
import { strings } from 'common/utils/i18n';
import { useSnackbar } from 'context/SnackbarProvider';

interface Props {
    children: any;
}

// https://github.com/facebook/react/issues/19630#issuecomment-675390931
const ErrorBoundary: React.FC<Props> = (props) => {
    const { enqueueSnackbar } = useSnackbar();

    const handleError = useCallback(
        (error: Error) => {
            enqueueSnackbar(process.env.NODE_ENV !== 'production' && error.message ? error.message : strings('common:message.genericError'), {
                variant: 'error',
            });
        },
        [enqueueSnackbar],
    );

    useEffect(() => {
        window.onunhandledrejection = (e: PromiseRejectionEvent) => {
            handleError(e.reason);
        };
    }, [handleError]);

    return props.children;
};

export default ErrorBoundary;
