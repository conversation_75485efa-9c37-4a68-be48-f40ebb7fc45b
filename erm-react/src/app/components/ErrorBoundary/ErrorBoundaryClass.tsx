import { isProduction } from 'config';
import React, { PropsWithChildren } from 'react';
import UnexpectedErrorPage from '../UnexpectedErrorPage';
import { isGwtWithoutMenu } from '../MainAppLayout/utils';

type ErrorBoundaryState = {
    hasError: boolean;
    error: Error | null;
};

class ErrorBoundaryClass extends React.Component<PropsWithChildren, ErrorBoundaryState> {
    // This lifecycle method is invoked when an error is caught in a child component
    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        // Update state to indicate that an error has occurred
        return { hasError: true, error };
    }

    constructor(props: PropsWithChildren) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    componentDidCatch(error: Error): void {
        console.error(error);
    }

    render() {
        if (!this.state.hasError) {
            return this.props.children;
        }

        return (
            <UnexpectedErrorPage
                showAppBar={!isProduction || (isProduction && isGwtWithoutMenu())}
                errorStack={!isProduction ? this.state.error?.stack : undefined}
            />
        );
    }
}

export default ErrorBoundaryClass;
