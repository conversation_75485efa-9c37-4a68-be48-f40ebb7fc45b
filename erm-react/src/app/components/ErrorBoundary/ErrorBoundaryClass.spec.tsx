import React from 'react';
import { render, screen } from 'test/utils';
import ErrorBoundaryClass from './ErrorBoundaryClass';
import * as config from 'config';
import { isGwtWithoutMenu } from '../MainAppLayout/utils';

const mockConfig = config as { isProduction: boolean };

jest.mock('../MainAppLayout/utils', () => ({
    ...jest.requireActual('../MainAppLayout/utils'),
    isGwtWithoutMenu: jest.fn(),
}));

const setup = (ui: React.ReactNode) => {
    return render(<ErrorBoundaryClass>{ui}</ErrorBoundaryClass>);
};

describe('ErrorBoundaryClass', () => {
    it('should render children when no error occurs', () => {
        setup(<div>Test Child</div>);
        expect(screen.getByText('Test Child')).toBeInTheDocument();
    });

    it('should display error UI without stack and app bar when an error is caught in production with menu', () => {
        mockConfig.isProduction = true;
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(false);
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        const { container } = setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-container')).toBeInTheDocument();
        expect(screen.queryByTestId('unexpected-error-appbar')).not.toBeInTheDocument();
        expect(screen.queryByTestId('unexpected-error-stack')).not.toBeInTheDocument();
        expect(container).toMatchSnapshot();

        errorSpy.mockRestore();
    });

    it('should display error UI without stack and with app bar when an error is caught in production without menu', () => {
        mockConfig.isProduction = true;
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        const { container } = setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-container')).toBeInTheDocument();
        expect(screen.getByTestId('unexpected-error-appbar')).toBeInTheDocument();
        expect(screen.queryByTestId('unexpected-error-stack')).not.toBeInTheDocument();
        expect(container).toMatchSnapshot();

        errorSpy.mockRestore();
    });

    it('should display error UI with stack and app bar when an error is caught but not in production', () => {
        mockConfig.isProduction = false;
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();

        const ThrowError = () => {
            throw new Error('Some Test Error');
        };

        setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-container')).toBeInTheDocument();
        expect(screen.getByTestId('unexpected-error-appbar')).toBeInTheDocument();
        expect(screen.getByTestId('unexpected-error-stack')).toBeInTheDocument();
        expect(screen.getByRole('code')).toHaveTextContent('Error: Some Test Error');

        errorSpy.mockRestore();
    });
});
