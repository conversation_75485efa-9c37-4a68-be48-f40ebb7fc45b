import React from 'react';

import { AlertType } from '@protecht/ui-library/library/types';
import Button from '@protecht/ui-library/library/components/Button';
import { InfoBoxActions } from '@protecht/ui-library/library/components/InfoBox';

import { strings } from 'common/utils/i18n';
import InfoPage from 'common/components/InfoPage';
import { useNavigate } from 'react-router';
import { isProduction } from 'config';

const PageNotFound: React.FC = () => {
    const navigate = useNavigate();

    return (
        <InfoPage
            type={AlertType.Error}
            subtitle={strings('common:title.pageNotFound')}
            message={strings('common:message.pageNotFound')}
        >
            <InfoBoxActions>
                <Button
                    size="large"
                    variant="outlined"
                    onClick={() => {
                        if (isProduction) {
                            window.location.href = ProtechtDictionary.siteUrl;
                        } else {
                            void navigate('/');
                        }
                    }}
                >
                    {strings('common:button.returnHome')}
                </Button>
            </InfoBoxActions>
        </InfoPage>
    );
};

export default PageNotFound;
