import React, { useEffect, useState } from 'react';
import { RouterProvider } from 'react-router';
import ErrorBoundary from 'app/components/ErrorBoundary';
import { GlobalSettingsContextProvider } from 'context/GlobalSettingsContext';
import { useSelector } from 'store';
import { isAlertDialogOpen } from 'ui/dialog.selectors';
import ReduxSimpleAlertDialog from 'ui/components/ReduxSimpleDialog/ReduxSimpleDialog';
import useSnackbarWithRedux from 'common/hooks/useSnackbarWithRedux';
import getAppRouter, { getGwtBridgeModule } from '../../app.router';
import useTheme from '@mui/system/useTheme';
import ErrorBoundaryClass from '../ErrorBoundary/ErrorBoundaryClass';

const App: React.FunctionComponent = () => {
    const theme = useTheme();
    useSnackbarWithRedux();

    const isAlert = useSelector(isAlertDialogOpen);

    const actualModule = getGwtBridgeModule();
    const [router, setRouter] = useState<ReturnType<typeof getAppRouter>>();

    useEffect(() => {
        setRouter(getAppRouter(actualModule));
    }, [actualModule]);

    useEffect(() => {
        if (window.GwtBridge) {
            window.GwtBridge.reactRouting = true;
        }
    }, [window]);

    return (
        <main style={{ height: '100%', display: 'flex', flexDirection: 'column', backgroundColor: theme.palette.background.default }}>
            <GlobalSettingsContextProvider>
                <ErrorBoundaryClass>
                    <ErrorBoundary>
                        {isAlert && <ReduxSimpleAlertDialog />}
                        {router && <RouterProvider router={router} />}
                    </ErrorBoundary>
                </ErrorBoundaryClass>
            </GlobalSettingsContextProvider>
        </main>
    );
};

export default App;
