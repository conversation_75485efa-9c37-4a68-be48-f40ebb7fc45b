import React from 'react';
import Typography from '@mui/material/Typography';
import { strings } from 'common/utils/i18n';
import { MenuItemType } from 'ui/types';
import { ObjectStatus } from 'app/types';
import ToolbarGroup from 'common/components/ToolbarSpacing/ToolbarGroup';
import Select from '@protecht/ui-library/library/components/Inputs/Select';

type Props = {
    value?: number[];
    updateValue: (newValue: number[]) => void;
};

const STATUS_FILTER_OPTIONS: MenuItemType<string>[] = [
    { value: ObjectStatus.Active.toString(), label: strings('library:label.active') },
    { value: ObjectStatus.Deleted.toString(), label: strings('library:label.deleted') },
    // user can be locked
];

export const ObjectStatusFilter: React.FC<Props> = ({ value, updateValue }: Props) => {
    return (
        <ToolbarGroup aria-label="active-filter">
            <Typography variant="body1">{strings('ermConstants:show')}</Typography>
            <Select
                autoWidth
                value={value?.map((item) => item.toString()) || [STATUS_FILTER_OPTIONS[0].value]}
                options={STATUS_FILTER_OPTIONS}
                placeholder={strings('ermMessages:status_filter')}
                multiple
                onChange={(event: string[]) => {
                    const selected = event;
                    const statuses = selected.map((item) => Number(item));
                    updateValue(statuses);
                }}
                sx={{ height: '28px' }}
            />
        </ToolbarGroup>
    );
};
