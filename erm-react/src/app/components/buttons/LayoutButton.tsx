import React from 'react';
import { useTheme } from '@mui/material/styles';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faList, faTh } from '@fortawesome/pro-solid-svg-icons';
import { LayoutOption } from 'common/types';

type Props = {
    value: LayoutOption;
    updateValue: (newValue: LayoutOption) => void;
    disabled?: boolean;
};

export const LayoutButton: React.FC<Props> = ({ value, updateValue, disabled = false }: Props) => {
    const theme = useTheme();

    return (
        <ToggleButtonGroup
            value={value}
            exclusive
            onChange={(event, newValue) => updateValue(newValue ?? value)}
            disabled={disabled}
            color="primary"
            aria-label="layout options"
            sx={{
                '& .MuiToggleButtonGroup-grouped': {
                    border: `1px solid ${theme.palette.protechtGrey?.grey_178} !important`,
                },
                '& .MuiToggleButtonGroup-grouped:not(:first-of-type)': {
                    position: 'relative',
                    '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        bottom: 0,
                        left: '-1px',
                        width: '1px',
                        backgroundColor: `${theme.palette.protechtGrey?.grey_231} !important`,
                    },
                },
                '& .MuiToggleButtonGroup-grouped:first-of-type': {
                    borderTopLeftRadius: '4px',
                    borderBottomLeftRadius: '4px',
                },
                '& .MuiToggleButtonGroup-grouped:last-of-type': {
                    borderTopRightRadius: '4px',
                    borderBottomRightRadius: '4px',
                },
                '&:hover .MuiToggleButtonGroup-grouped': {
                    borderColor: `${theme.palette.protechtGrey?.grey_128} !important`,
                },
            }}
        >
            <ToggleButton
                value={LayoutOption.CARD}
                aria-label="card layout"
            >
                <FontAwesomeIcon icon={faTh} />
            </ToggleButton>
            <ToggleButton
                value={LayoutOption.LIST}
                aria-label="list layout"
            >
                <FontAwesomeIcon icon={faList} />
            </ToggleButton>
        </ToggleButtonGroup>
    );
};
