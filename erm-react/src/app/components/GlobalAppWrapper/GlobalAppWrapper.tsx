import React, { PropsWithChildren, useMemo } from 'react';
import { Provider } from 'react-redux';
import { ThemeProvider } from '@mui/material/styles';

import store from 'store';
import SnackbarProvider from 'context/SnackbarProvider';
import { getProtechtTheme } from 'app/theme';
import { injectGlobalStyles } from './globalStylesInjector';

injectGlobalStyles();

const GlobalAppWrapper: React.FC<PropsWithChildren> = ({ children }) => {
    const theme = useMemo(() => getProtechtTheme(), []);

    return (
        <Provider store={store}>
            <ThemeProvider theme={theme}>
                <SnackbarProvider>{children}</SnackbarProvider>
            </ThemeProvider>
        </Provider>
    );
};

export default GlobalAppWrapper;
