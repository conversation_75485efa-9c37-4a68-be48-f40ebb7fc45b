import React from 'react';
import GlobalStylesMui from '@mui/styled-engine/GlobalStyles';
import { useTheme } from '@mui/material/styles';
import 'app/components/App/App.css';

const GlobalStyles = () => {
    const theme = useTheme();

    return (
        <GlobalStylesMui
            styles={{
                'html, body': {
                    margin: 0,
                    padding: 0,
                },
                '#react-root': {
                    boxSizing: 'border-box',
                    height: '100%',
                },
                '*, *::after, *::before': {
                    boxSizing: 'inherit',
                },
                img: {
                    verticalAlign: 'middle',
                },
                '.expand': {
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                },
                '.modern-component': {
                    boxSizing: 'border-box',
                    '& *, & *::before, & *::after': {
                        boxSizing: 'inherit',
                    },
                },
                '.os-scrollbar': {
                    '--os-size': '20px',
                    '--os-handle-bg': theme.palette.protechtGrey?.grey_220,
                    '--os-handle-bg-hover': theme.palette.protechtGrey?.grey_128,
                    '--os-handle-bg-active': theme.palette.protechtGrey?.grey_128,
                    '--os-padding-axis': '5px',
                    '--os-padding-perpendicular': '7px',
                    '--os-handle-max-size': '222px',
                    '--os-handle-interactive-area-offset': '0px',
                    transition: 'none',

                    '&.os-scrollbar-horizontal .os-scrollbar-handle, &.os-scrollbar-vertical .os-scrollbar-handle': {
                        transition: 'none',
                    },
                },
            }}
        />
    );
};

export default GlobalStyles;
