import React from 'react';
import { createRoot } from 'react-dom/client';
import { ClickScrollPlugin, OverlayScrollbars } from 'overlayscrollbars';

import { getProtechtTheme } from 'app/theme';
import { StyledEngineProvider, ThemeProvider } from '@mui/material/styles';
import GlobalStyles from './GlobalStyles';

let stylesInjected = false;

export const injectGlobalStyles = () => {
    if (stylesInjected) {
        return;
    }

    OverlayScrollbars.plugin(ClickScrollPlugin);

    const theme = getProtechtTheme();

    const container = document.createElement('div');
    container.id = 'global-mui-styles-container';
    document.body.appendChild(container);

    createRoot(container).render(
        <StyledEngineProvider injectFirst>
            <ThemeProvider theme={theme}>
                <GlobalStyles />
            </ThemeProvider>
        </StyledEngineProvider>,
    );

    stylesInjected = true;
};
