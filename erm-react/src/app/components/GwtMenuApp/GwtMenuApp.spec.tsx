import React from 'react';
import { render, screen } from 'test/utils';
import GwtMenuApp from './GwtMenuApp';
import useSnackbar from 'common/hooks/useSnackbar';

jest.mock('common/hooks/useSnackbar', () => jest.fn());
jest.mock('./Layout', () => jest.fn(() => <div data-testid="menu-layout"></div>));
jest.mock('./ErrorBoundary', () => jest.fn(({ children }) => <div data-testid="error-boundary">{children}</div>));

describe('GwtMenuApp', () => {
    beforeEach(() => {
        (useSnackbar as jest.Mock).mockReturnValue({});
        jest.clearAllMocks();
    });

    it('should render GwtMenuLayout inside ErrorBoundary', () => {
        const { container } = render(<GwtMenuApp />);
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
        expect(screen.getByTestId('menu-layout')).toBeInTheDocument();
        expect(container).toMatchSnapshot();
    });
});
