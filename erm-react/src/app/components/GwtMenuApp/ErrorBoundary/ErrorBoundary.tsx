import React, { PropsWithChildren } from 'react';

import store from 'store';
import { setIsCriticalError } from 'app/reducer';
import { HORIZONTAL_MENU_HEIGHT } from 'ui/constants';
import { SnackbarContext } from 'common/hooks/useSnackbar';
import { isGwtWidgetPage, isGwtWithLogoOnly, isGwtWithoutMenu } from 'app/components/MainAppLayout/utils';
import UnexpectedErrorPage from 'app/components/UnexpectedErrorPage';

type ErrorBoundaryState = {
    hasError: boolean;
    error: Error | null;
};

type ErrorBoundaryProps = {
    snackbarContext: SnackbarContext;
};

class ErrorBoundary extends React.Component<PropsWithChildren<ErrorBoundaryProps>, ErrorBoundaryState> {
    // This lifecycle method is invoked when an error is caught in a child component
    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        // Update state to indicate that an error has occurred
        return { hasError: true, error };
    }

    constructor(props: PropsWithChildren<ErrorBoundaryProps>) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    componentDidCatch(error: Error): void {
        window.ptgwt.Functions.setMenuContainerSizes(`${HORIZONTAL_MENU_HEIGHT}px`, '0px');
        const isLogoOnly = isGwtWithLogoOnly();
        const isGwtWidget = isGwtWidgetPage();
        const isWithoutMenu = isGwtWithoutMenu();

        if (isLogoOnly || isWithoutMenu) {
            return;
        }

        if (isGwtWidget) {
            document.getElementById('main-menu-panel-container')?.style.setProperty('z-index', '9999');
            document.getElementById('left-menu-panel-container')?.style.setProperty('z-index', '9999');
        } else {
            store.dispatch(setIsCriticalError(true));
        }

        console.error(error);
    }

    render() {
        if (!this.state.hasError) {
            return this.props.children;
        }

        const isGwtWidgetWithoutLogoOnly = isGwtWidgetPage() && !isGwtWithLogoOnly();

        return (
            <UnexpectedErrorPage
                showAppBar={!isGwtWithoutMenu()}
                showError={isGwtWidgetWithoutLogoOnly}
                isFullScreen={isGwtWidgetWithoutLogoOnly}
            />
        );
    }
}

export default ErrorBoundary;
