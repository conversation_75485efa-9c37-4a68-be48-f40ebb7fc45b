import React from 'react';
import { render, screen } from 'test/utils';
import ErrorBoundary from './ErrorBoundary';
import { isGwtWidgetPage, isGwtWithLogoOnly, isGwtWithoutMenu } from 'app/components/MainAppLayout/utils';
import store from 'store';
import { setIsCriticalError } from 'app/reducer';
import UnexpectedErrorPage from 'app/components/UnexpectedErrorPage';

jest.mock('app/components/UnexpectedErrorPage', () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="unexpected-error-page">error</div>),
}));

jest.mock('app/components/MainAppLayout/utils', () => ({
    ...jest.requireActual('app/components/MainAppLayout/utils'),
    isGwtWidgetPage: jest.fn(),
    isGwtWithLogoOnly: jest.fn(),
    isGwtWithoutMenu: jest.fn(),
}));

const snackbarContextMock = {
    enqueueError: jest.fn(),
    enqueueSuccess: jest.fn(),
    enqueueInfo: jest.fn(),
    enqueueWarning: jest.fn(),
    enqueueSnackbar: jest.fn(),
    closeSnackbar: jest.fn(),
};

const setup = (ui: React.ReactNode) => {
    return render(<ErrorBoundary snackbarContext={snackbarContextMock}>{ui}</ErrorBoundary>);
};

describe('ErrorBoundary', () => {
    let storeSpy;

    beforeEach(() => {
        jest.clearAllMocks();
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(false);
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(false);
        (isGwtWidgetPage as jest.Mock).mockReturnValue(false);
        storeSpy = jest.spyOn(store, 'dispatch');
    });

    afterEach(() => {
        storeSpy.mockReset();
        storeSpy.mockRestore();
    });

    it('should render children when no error occurs', () => {
        setup(<div>Test Child</div>);
        expect(screen.getByText('Test Child')).toBeInTheDocument();
        expect(store.dispatch).not.toHaveBeenCalled();
    });

    it('dispatches critical error when it is not GWT widget page', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(store.dispatch).toHaveBeenCalledWith(setIsCriticalError(true));

        errorSpy.mockRestore();
    });

    it('does not dispatch critical error when it is GWT widget page', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        (isGwtWidgetPage as jest.Mock).mockReturnValue(true);

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(store.dispatch).not.toHaveBeenCalled();

        errorSpy.mockRestore();
    });

    it('does not dispatch critical error when it is Logo only page', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(true);

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(store.dispatch).not.toHaveBeenCalled();

        errorSpy.mockRestore();
    });

    it('does not dispatch critical error when it is without menu', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(store.dispatch).not.toHaveBeenCalled();

        errorSpy.mockRestore();
    });

    it('should display error UI with correct props when an error is caught and it is React page with menu', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-page')).toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith({ showAppBar: true, showError: false, isFullScreen: false }, {});

        errorSpy.mockRestore();
    });

    it('should display error UI with correct props when an error is caught and it is React page without menu', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-page')).toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith({ showAppBar: false, showError: false, isFullScreen: false }, {});

        errorSpy.mockRestore();
    });

    it('should display error UI with correct props when an error is caught and it is React page with logo only', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(true);

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-page')).toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith({ showAppBar: true, showError: false, isFullScreen: false }, {});

        errorSpy.mockRestore();
    });

    it('should display error UI with correct props when an error is caught and it is GWT page with menu', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        (isGwtWidgetPage as jest.Mock).mockReturnValue(true);

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-page')).toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith({ showAppBar: true, showError: true, isFullScreen: true }, {});

        errorSpy.mockRestore();
    });

    it('should display error UI with correct props when an error is caught and it is GWT page with logo only', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        (isGwtWidgetPage as jest.Mock).mockReturnValue(true);
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(true);

        const ThrowError = () => {
            throw new Error('Test Error');
        };

        setup(<ThrowError />);

        expect(screen.getByTestId('unexpected-error-page')).toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith({ showAppBar: true, showError: false, isFullScreen: false }, {});

        errorSpy.mockRestore();
    });
});
