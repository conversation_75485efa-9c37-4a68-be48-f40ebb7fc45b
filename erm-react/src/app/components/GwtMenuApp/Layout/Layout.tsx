import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import Portal from '@mui/material/Portal';
import { useSelector } from 'react-redux';

import { useDispatch } from 'store';
import { getIsPinnedMenu, getIsMenuHidden, getIsCriticalError } from 'app/selectors';
import { forceMainLogoReload, setIsMenuHidden } from 'app/reducer';
import { menuApi } from 'common/api/menu';
import useCriticalAppApi from 'common/hooks/useCriticalAppApi';

import { VERTICAL_MENU_WIDTH_COLLAPSED, VERTICAL_MENU_WIDTH_EXPANDED, RESPONSIVE_BREAKPOINTS, HORIZONTAL_MENU_HEIGHT } from 'ui/constants';
import { HorizontalNavigationBar, VerticalNavigationBar } from 'ui/components/AppNavigationBar';
import AppBar from 'ui/components/AppNavigationBar/AppBar';
import { getGwtModuleFromUrl, isGwtVendorPortal, isGwtWidgetPage, isGwtWithLogoOnly, isGwtWithoutMenu } from 'app/components/MainAppLayout/utils';
import UnexpectedErrorPage from 'app/components/UnexpectedErrorPage';
import useUserMenuOrientation from 'common/hooks/useUserMenuOrientation';
import ChatBot from 'ai/components/ChatBot';
import { AppEventType, emitAppEvent } from 'app/NotificationService';
import { useGetSystemConfigurationQuery } from 'app/rtkApi';

const GwtMenuLayout: React.FC = () => {
    const dispatch = useDispatch();
    const isSmallScreen = useMediaQuery(`(max-width:${RESPONSIVE_BREAKPOINTS.small.max}px)`);
    const isLargeScreen = useMediaQuery(`(min-width:${RESPONSIVE_BREAKPOINTS.large.min}px)`);
    const isWithLogoOnly = isGwtWithLogoOnly();
    const isMenuHidden = useSelector(getIsMenuHidden);
    const isPinnedMenu = useSelector(getIsPinnedMenu);
    const [isInitiallyChecked, setIsInitiallyChecked] = useState(false);
    const isCriticalError = useSelector(getIsCriticalError);
    const isVendorPortalPage = isGwtVendorPortal();

    const { data: systemConfig } = useGetSystemConfigurationQuery();
    const isChatBotEnabledInSystemConfig = systemConfig?.ai_copilot_feature === 'true';

    useCriticalAppApi({ skipAll: isWithLogoOnly && isGwtWidgetPage(), skipMenu: isMenuHidden, skipPermissions: isVendorPortalPage });

    const { hasVerticalMenu, isLoading: isLoadingUserMenuOrientation } = useUserMenuOrientation();
    const isWithVerticalMenu = (hasVerticalMenu || isSmallScreen) && !isWithLogoOnly;

    useEffect(() => {
        if (window.ReactBridge?.Menu) {
            window.ReactBridge.Menu.reloadReactMenu = () => {
                dispatch(menuApi.util.invalidateTags(['menu']));
                dispatch(forceMainLogoReload());
            };
        }
    }, [dispatch]);

    useEffect(() => {
        if (!isWithLogoOnly) {
            dispatch(setIsMenuHidden(isGwtWithoutMenu()));
        }

        setIsInitiallyChecked(true);
    }, [dispatch, isWithLogoOnly]);

    const getContainerSizes = useCallback((): [number, number] => {
        // Returns [topSize, leftSize]

        if (isWithLogoOnly || isCriticalError) {
            return [HORIZONTAL_MENU_HEIGHT, 0];
        }

        if (isMenuHidden) {
            return [0, 0];
        }

        if (isWithVerticalMenu) {
            if (isSmallScreen) {
                return [HORIZONTAL_MENU_HEIGHT, 0];
            } else if (isLargeScreen && isPinnedMenu) {
                return [0, VERTICAL_MENU_WIDTH_EXPANDED];
            } else {
                return [0, VERTICAL_MENU_WIDTH_COLLAPSED];
            }
        } else {
            return [HORIZONTAL_MENU_HEIGHT, 0];
        }
    }, [isWithLogoOnly, isCriticalError, isMenuHidden, isWithVerticalMenu, isSmallScreen, isLargeScreen, isPinnedMenu]);

    useEffect(() => {
        if (!isInitiallyChecked || isLoadingUserMenuOrientation) {
            return;
        }

        const [topSize, leftSize] = getContainerSizes();
        window.ptgwt.Functions.setMenuContainerSizes(`${topSize}px`, `${leftSize}px`);
    }, [isLoadingUserMenuOrientation, isInitiallyChecked, getContainerSizes]);

    useEffect(() => {
        emitAppEvent({
            type: AppEventType.NAVIGATION,
            widgetId: getGwtModuleFromUrl() ?? '',
        });
    }, []);

    const navBar = useMemo(() => {
        if (isWithLogoOnly) {
            return <AppBar logoClickable={false} />;
        }

        if (isMenuHidden) {
            return null;
        }

        return isWithVerticalMenu ? <VerticalNavigationBar /> : <HorizontalNavigationBar />;
    }, [isWithLogoOnly, isMenuHidden, isWithVerticalMenu]);

    const navBarWrapper = useMemo(() => <div id={isWithVerticalMenu ? 'left-react-menu' : 'top-react-menu'}>{navBar}</div>, [isWithVerticalMenu, navBar]);

    const navigationContent = useMemo(() => {
        return isWithVerticalMenu ? <Portal container={() => document.getElementById('left-menu-panel-container')}>{navBarWrapper}</Portal> : navBarWrapper;
    }, [isWithVerticalMenu, navBarWrapper]);

    if (isCriticalError) {
        const isGwtWidget = isGwtWidgetPage();

        return (
            <UnexpectedErrorPage
                showError={isGwtWidget}
                isFullScreen={isGwtWidget}
                id="top-react-menu"
            />
        );
    }

    if (isLoadingUserMenuOrientation) {
        return null;
    }

    return (
        <>
            {navigationContent}
            {isChatBotEnabledInSystemConfig && <ChatBot />}
        </>
    );
};

export default GwtMenuLayout;
