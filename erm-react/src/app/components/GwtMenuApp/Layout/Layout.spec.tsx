import React from 'react';
import { MemoryRouter } from 'react-router';
import useMediaQuery from '@mui/material/useMediaQuery';

import * as store from 'store';
import { render, screen } from 'test/utils';
import GwtMenuLayout from './Layout';
import { HORIZONTAL_MENU_HEIGHT, RESPONSIVE_BREAKPOINTS, VERTICAL_MENU_WIDTH_COLLAPSED, VERTICAL_MENU_WIDTH_EXPANDED } from 'ui/constants';
import { isGwtWidgetPage, isGwtWithoutMenu, isGwtWithLogoOnly, isGwtVendorPortal, getGwtModuleFromUrl } from 'app/components/MainAppLayout/utils';
import { mockPtgwtFunctionsSetMenuContainerSizes } from 'test/config/setup';
import { mockedAppSlice } from 'app/mock';
import { forceMainLogoReload } from 'app/reducer';
import UnexpectedErrorPage from 'app/components/UnexpectedErrorPage';
import useUserMenuOrientation from 'common/hooks/useUserMenuOrientation';
import { AppEventType, emitAppEvent } from 'app/NotificationService';

jest.mock('app/components/UnexpectedErrorPage', () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="unexpected-error-page">error</div>),
}));

jest.mock('ai/components/ChatBot', () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="chatbot">ChatBot</div>),
}));

jest.mock('app/NotificationService', () => ({
    AppEventType: {
        NAVIGATION: 'navigation',
    },
    emitAppEvent: jest.fn(),
}));

const mockUseGetSystemConfigurationQuery = jest.fn();
jest.mock('app/rtkApi', () => ({
    useGetSystemConfigurationQuery: () => mockUseGetSystemConfigurationQuery(),
    systemApi: {
        endpoints: {
            srsGetScaleSetsUsingGet: {
                matchFulfilled: jest.fn(),
            },
            getSystemConfiguration: {
                matchFulfilled: jest.fn(),
            },
        },
    },
}));

jest.mock('user/rtkApi', () => ({
    usersApi: {
        endpoints: {
            pursGetCurrentUserUsingGet: {
                matchFulfilled: jest.fn(),
            },
        },
    },
}));

const mockUseCriticalAppApi = jest.fn();
jest.mock('common/hooks/useCriticalAppApi', () => ({
    __esModule: true,
    default: (args) => mockUseCriticalAppApi(args),
}));

jest.mock('common/hooks/useUserMenuOrientation', () => jest.fn());

jest.mock('common/api/menu', () => ({
    menuApi: {
        util: {
            invalidateTags: jest.fn((tags) => ({ type: 'menu/invalidateTags', payload: tags })),
        },
    },
}));

jest.mock('@mui/material/useMediaQuery', () => jest.fn());

jest.mock('ui/components/AppNavigationBar', () => ({
    HorizontalNavigationBar: jest.fn(() => <div data-testid="horizontal-nav-bar" />),
    VerticalNavigationBar: jest.fn(() => <div data-testid="vertical-nav-bar" />),
}));

jest.mock('app/components/MainAppLayout/utils', () => ({
    ...jest.requireActual('app/components/MainAppLayout/utils'),
    isGwtWithoutMenu: jest.fn(),
    isGwtWidgetPage: jest.fn(),
    isGwtWithLogoOnly: jest.fn(),
    isGwtVendorPortal: jest.fn(),
    getGwtModuleFromUrl: jest.fn(),
}));

jest.mock('app/app.router', () => ({
    ...jest.requireActual('app/app.router'),
    getGwtBridgeModule: jest.fn(),
}));

const setup = (menuPinned: boolean = false, menuHidden: boolean = false, isCriticalError: boolean = false, systemConfig: any = {}) => {
    mockUseGetSystemConfigurationQuery.mockReturnValue({
        data: systemConfig,
        isLoading: false,
        error: null,
    });

    return render(
        <MemoryRouter>
            <GwtMenuLayout />
        </MemoryRouter>,
        {
            preloadedState: {
                app: {
                    ...mockedAppSlice,
                    isCriticalError,
                    mainNavigation: { ...mockedAppSlice.mainNavigation, isPinned: menuPinned, isHidden: menuHidden },
                },
            },
        },
    );
};

const isSmallScreen = (query) => query === `(max-width:${RESPONSIVE_BREAKPOINTS.small.max}px)`;
const isLargeScreen = (query) => query === `(min-width:${RESPONSIVE_BREAKPOINTS.large.min}px)`;

describe('GwtMenuLayout', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (useMediaQuery as jest.Mock).mockReturnValue(false);
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(false);
        (isGwtWidgetPage as jest.Mock).mockReturnValue(false);
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(false);
        (isGwtVendorPortal as jest.Mock).mockReturnValue(false);
        (getGwtModuleFromUrl as jest.Mock).mockReturnValue('TestModule');
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: false,
            isLoading: false,
        });
        mockUseGetSystemConfigurationQuery.mockReturnValue({
            data: {},
            isLoading: false,
            error: null,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.restoreAllMocks();
    });

    it('does not render anything when loading user menu orientation', () => {
        (useMediaQuery as jest.Mock).mockReturnValue(true);
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(false);
        (isGwtWidgetPage as jest.Mock).mockReturnValue(false);
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(false);
        (isGwtVendorPortal as jest.Mock).mockReturnValue(false);
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: false,
            isLoading: true,
        });
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const verticalNav = screen.queryByTestId('vertical-nav-bar');
        const unexpectedErrorPage = screen.queryByTestId('unexpected-error-page');
        expect(horizontalNav).not.toBeInTheDocument();
        expect(verticalNav).not.toBeInTheDocument();
        expect(unexpectedErrorPage).not.toBeInTheDocument();
    });

    it('renders empty container without menu when menu is vertical and is hidden', () => {
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const verticalNav = screen.queryByTestId('vertical-nav-bar');
        expect(horizontalNav).not.toBeInTheDocument();
        expect(verticalNav).not.toBeInTheDocument();
        const element = document.querySelector('#left-react-menu');
        expect(element).toBeEmptyDOMElement();
    });

    it('renders empty container without menu when menu is horizontal and is hidden', () => {
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const verticalNav = screen.queryByTestId('vertical-nav-bar');
        expect(horizontalNav).not.toBeInTheDocument();
        expect(verticalNav).not.toBeInTheDocument();
        const element = document.querySelector('#top-react-menu');
        expect(element).toBeEmptyDOMElement();
    });

    it('renders VerticalNavigationBar when is small screen', () => {
        (useMediaQuery as jest.Mock).mockImplementation((query) => query === `(max-width:${RESPONSIVE_BREAKPOINTS.small.max}px)`);
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const verticalNav = screen.getByTestId('vertical-nav-bar');
        expect(horizontalNav).not.toBeInTheDocument();
        expect(verticalNav).toBeInTheDocument();
    });

    it('renders VerticalNavigationBar when vertical menu is turned on', () => {
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });
        setup();
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const verticalNav = screen.getByTestId('vertical-nav-bar');
        expect(horizontalNav).not.toBeInTheDocument();
        expect(verticalNav).toBeInTheDocument();
    });

    it('renders HorizontalNavigationBar when vertical menu is turned off', () => {
        setup();
        const horizontalNav = screen.getByTestId('horizontal-nav-bar');
        const verticalNav = screen.queryByTestId('vertical-nav-bar');
        expect(horizontalNav).toBeInTheDocument();
        expect(verticalNav).not.toBeInTheDocument();
    });

    it('calls setMenuContainerSizes correctly when menu is not visible', () => {
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);
        setup();
        expect(mockPtgwtFunctionsSetMenuContainerSizes).toHaveBeenCalledWith('0px', '0px');
    });

    it('calls setMenuContainerSizes correctly on small screen and when vertical menu is turned on', () => {
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });
        (useMediaQuery as jest.Mock).mockImplementation(isSmallScreen);
        setup();
        expect(mockPtgwtFunctionsSetMenuContainerSizes).toHaveBeenCalledWith(`${HORIZONTAL_MENU_HEIGHT}px`, '0px');
    });

    it('calls setMenuContainerSizes correctly on large screen when vertical menu is pinned', () => {
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });

        (useMediaQuery as jest.Mock).mockImplementation(isLargeScreen);
        setup(true);
        expect(mockPtgwtFunctionsSetMenuContainerSizes).toHaveBeenCalledWith('0px', `${VERTICAL_MENU_WIDTH_EXPANDED}px`);
    });

    it('calls setMenuContainerSizes correctly on large screen when vertical menu is unpinned', () => {
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });

        (useMediaQuery as jest.Mock).mockImplementation(isLargeScreen);
        setup();
        expect(mockPtgwtFunctionsSetMenuContainerSizes).toHaveBeenCalledWith('0px', `${VERTICAL_MENU_WIDTH_COLLAPSED}px`);
    });

    it('calls setMenuContainerSizes correctly when menu is horizontal', () => {
        setup();
        expect(mockPtgwtFunctionsSetMenuContainerSizes).toHaveBeenCalledWith(`${HORIZONTAL_MENU_HEIGHT}px`, '0px');
    });

    it('calls setMenuContainerSizes correctly when critical error occurred', () => {
        (useUserMenuOrientation as jest.Mock).mockReturnValue({
            hasVerticalMenu: true,
            isLoading: false,
        });
        setup(false, false, true);
        expect(mockPtgwtFunctionsSetMenuContainerSizes).toHaveBeenCalledWith(`${HORIZONTAL_MENU_HEIGHT}px`, '0px');
    });

    it('does not set reloadReactMenu function when window.ReactBridge.Menu is undefined', () => {
        window.ReactBridge = { Menu: undefined };

        setup();
        expect(window.ReactBridge?.Menu?.reloadReactMenu).toBeUndefined();
    });

    it('sets reloadReactMenu function when window.ReactBridge.Menu is defined', () => {
        window.ReactBridge = { Menu: {} };
        setup();
        expect(window.ReactBridge!.Menu!.reloadReactMenu).toBeInstanceOf(Function);
    });

    it('dispatches correct actions when reloadRectMenu is called', () => {
        window.ReactBridge = { Menu: {} };
        const mockDispatch = jest.fn();
        jest.spyOn(store, 'useDispatch').mockReturnValue(mockDispatch);

        setup();

        window.ReactBridge!.Menu!.reloadReactMenu!();

        expect(mockDispatch).toHaveBeenCalledWith({
            type: 'menu/invalidateTags',
            payload: ['menu'],
        });

        expect(mockDispatch).toHaveBeenCalledWith(forceMainLogoReload());
    });

    it('does not render unexpected error page when no critical error occurs', () => {
        setup();
        const horizontalNav = screen.getByTestId('horizontal-nav-bar');
        const unexpectedErrorPage = screen.queryByTestId('unexpected-error-page');

        expect(unexpectedErrorPage).not.toBeInTheDocument();
        expect(horizontalNav).toBeInTheDocument();
    });

    it('renders unexpected error page with correct props when critical error occurs and is gwt widget page', () => {
        (isGwtWidgetPage as jest.Mock).mockReturnValue(true);
        setup(false, false, true);
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const unexpectedErrorPage = screen.getByTestId('unexpected-error-page');

        expect(unexpectedErrorPage).toBeInTheDocument();
        expect(horizontalNav).not.toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith(
            {
                showError: true,
                isFullScreen: true,
                id: 'top-react-menu',
            },
            {},
        );
    });

    it('renders unexpected error page with correct props when critical error occurs and is not gwt widget page', () => {
        (isGwtWidgetPage as jest.Mock).mockReturnValue(false);
        setup(false, false, true);
        const horizontalNav = screen.queryByTestId('horizontal-nav-bar');
        const unexpectedErrorPage = screen.getByTestId('unexpected-error-page');

        expect(unexpectedErrorPage).toBeInTheDocument();
        expect(horizontalNav).not.toBeInTheDocument();
        expect(UnexpectedErrorPage).toHaveBeenCalledWith(
            {
                showError: false,
                isFullScreen: false,
                id: 'top-react-menu',
            },
            {},
        );
    });

    it('calls useCriticalAppApi with correct arguments', () => {
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipAll: false, skipMenu: false, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when is logo only', () => {
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(true);
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipAll: false, skipMenu: false, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when is logo only and GWT page', () => {
        (isGwtWidgetPage as jest.Mock).mockReturnValue(true);
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(true);
        setup();
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipAll: true, skipMenu: false, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when is menu hidden', () => {
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);
        (isGwtWithLogoOnly as jest.Mock).mockReturnValue(false);
        setup(false, true);
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipAll: false, skipMenu: true, skipPermissions: false });
    });

    it('calls useCriticalAppApi with correct arguments when it is vendor portal', () => {
        (isGwtVendorPortal as jest.Mock).mockReturnValue(true);
        (isGwtWithoutMenu as jest.Mock).mockReturnValue(true);
        setup(false, true);
        expect(mockUseCriticalAppApi).toHaveBeenCalledWith({ skipAll: false, skipMenu: true, skipPermissions: true });
    });

    describe('ChatBot functionality', () => {
        it('renders ChatBot when system config enables it', () => {
            setup(false, false, false, { ai_copilot_feature: 'true' });
            const chatbot = screen.getByTestId('chatbot');
            expect(chatbot).toBeInTheDocument();
        });

        it('does not render ChatBot when system config disables it', () => {
            setup(false, false, false, { ai_copilot_feature: 'false' });
            const chatbot = screen.queryByTestId('chatbot');
            expect(chatbot).not.toBeInTheDocument();
        });

        it('does not render ChatBot when system config is undefined', () => {
            setup(false, false, false, {});
            const chatbot = screen.queryByTestId('chatbot');
            expect(chatbot).not.toBeInTheDocument();
        });

        it('does not render ChatBot when there is a critical error', () => {
            setup(false, false, true, { ai_copilot_feature: 'true' });
            const chatbot = screen.queryByTestId('chatbot');
            expect(chatbot).not.toBeInTheDocument();
        });
    });

    describe('App event emission', () => {
        it('emits navigation event on component mount', () => {
            (getGwtModuleFromUrl as jest.Mock).mockReturnValue('TestModule');
            setup();

            expect(emitAppEvent).toHaveBeenCalledWith({
                type: AppEventType.NAVIGATION,
                widgetId: 'TestModule',
            });
        });

        it('emits navigation event with empty string when no module found', () => {
            (getGwtModuleFromUrl as jest.Mock).mockReturnValue(null);
            setup();

            expect(emitAppEvent).toHaveBeenCalledWith({
                type: AppEventType.NAVIGATION,
                widgetId: '',
            });
        });

        it('emits navigation event only once per component mount', () => {
            setup();
            expect(emitAppEvent).toHaveBeenCalledTimes(1);
        });
    });
});
