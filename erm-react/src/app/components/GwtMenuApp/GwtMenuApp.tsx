import React from 'react';

import useSnackbar from 'common/hooks/useSnackbar';
import GwtMenuLayout from './Layout';
import ErrorBoundary from './ErrorBoundary';

const GwtMenuApp: React.FC = () => {
    const snackbarContext = useSnackbar();

    return (
        <ErrorBoundary snackbarContext={snackbarContext}>
            <GwtMenuLayout />
        </ErrorBoundary>
    );
};

export default GwtMenuApp;
