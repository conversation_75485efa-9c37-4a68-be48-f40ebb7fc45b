import { VrsGetExpressionContextUsingGetApiResponse } from 'api/generated/types';
import { ExpressionContextType } from 'view/types';

export const initiateFileDownload = (fileName: string, file: Blob) => {
    const link = document.createElement('a');
    link.href = URL.createObjectURL(file);
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    link?.parentNode?.removeChild(link);
};

export function handleApiError(error, context) {
    console.error(`Error in ${context}:`, error);

    return null;
}

export function convertToExpressionContextType(payload: VrsGetExpressionContextUsingGetApiResponse): ExpressionContextType {
    return {
        ATTACHMENT: payload.attachment || [],
        BOOLEAN: payload.boolean || [],
        BOOLEAN_PRIMITIVE: payload.booleanPrimitive || [],
        COMPOUND_STRING: payload.compoundString || [],
        DATE: payload.date || [],
        NUMBER: payload.number || [],
        STRING: payload.string || [],
    };
}

export const swapItems = (array, index1, index2) => {
    const newArray = [...array];

    [newArray[index1], newArray[index2]] = [newArray[index2], newArray[index1]];

    return newArray;
};
