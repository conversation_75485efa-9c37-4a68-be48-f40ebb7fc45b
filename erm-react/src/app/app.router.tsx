import React from 'react';
import { createBrowserRouter, Navigate, useRouteError } from 'react-router';
import store from 'store';

import { ERM_CONTEXT } from 'config';
import { usersApi } from 'user/rtkApi';
import { getCurrentUser, getSystemConfiguration } from './selectors';

// routes
import MarketplaceRoutes from 'marketplace/routesConfig';
import ResilienceRoutes from 'resilience/routes';
import RolesAndPermissionsRoutes from 'rolesAndPermissions/routes';
import VendorPortalRoutes from 'vendorRiskManagement/routes/vendorPortalRoutesConfig';
import VrmRoutes from 'vendorRiskManagement/routes/vrmRoutesConfig';
import QuestionnaireRoutes from 'questionnaire/routes';
import BowtieRoutes from 'bowtie/routes';
import LibraryRoutes from 'library/routes';
import RegisterRoutes from 'register/routesConfig';
import UserRoutes from 'user/routes';
import { CyberRiskRoutes } from 'cyberrisk/routes';
import SamlRoutes from 'auth/saml/routes';
import { FrameworkRoutes } from 'frameworks/routes';
import { ComplianceRoutes } from 'compliance/routesConfig';

import { systemApi } from './rtkApi';
import MainAppLayout from './components/MainAppLayout';
import PageNotFound from './components/PageNotFound';

export const getGwtBridgeModule = (): string => {
    return window.GwtBridge?.module || 'default';
};

// This script handles the loading of the Google Maps API for local development
// when deployed, Google Maps API is loaded on BE side and appended to window object
const handleLoadMaps = () => {
    if (typeof window?.google?.maps === 'object') {
        return;
    }
    //Create the script element and load Google Maps API
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GOOGLE_MAPS_API_KEY}&libraries=&v=weekly&loading=async`;
    script.type = 'text/javascript';
    script.async = true;
    script.defer = true;

    document.head.appendChild(script);
};

const ErrorBoundary = () => {
    const error = useRouteError();

    if (error) {
        // Rethrowing the error as a workaround because React Router catches all errors by default.
        // If not rethrown, any custom error boundary placed above React Router's ErrorBoundary
        // (such as a global ErrorBoundaryClass) will never receive the error, preventing it from handling it properly.
        // https://github.com/remix-run/react-router/discussions/10166
        throw error;
    }

    return null;
};

const getAppRouter = (module: string) => {
    return createBrowserRouter(
        [
            {
                path: '/',
                element: <MainAppLayout />,
                ErrorBoundary,
                loader: async () => {
                    void store.dispatch(usersApi.endpoints.pursGetCurrentUserUsingGet.initiate(undefined, { subscribe: false }));
                    void store.dispatch(systemApi.endpoints.getSystemConfiguration.initiate(undefined, { subscribe: false }));
                    handleLoadMaps();

                    return null;
                },
                shouldRevalidate: () => {
                    const currentUser = getCurrentUser(store.getState());
                    const systemConfiguration = getSystemConfiguration(store.getState());
                    return !currentUser || !systemConfiguration;
                },
                children: [
                    {
                        index: true,
                        element: (
                            <Navigate
                                to={ProtechtDictionary.reactInitialRoute}
                                replace
                            />
                        ),
                    },
                    ...BowtieRoutes,
                    ...RegisterRoutes,
                    ...LibraryRoutes,
                    ...RolesAndPermissionsRoutes,
                    ...MarketplaceRoutes,
                    ...ResilienceRoutes,
                    ...VrmRoutes,
                    ...VendorPortalRoutes,
                    ...QuestionnaireRoutes,
                    ...UserRoutes,
                    ...CyberRiskRoutes,
                    ...SamlRoutes,
                    ...FrameworkRoutes,
                    ...ComplianceRoutes,

                    // 404 page when no route is matched
                    {
                        path: '*',
                        element: <PageNotFound />,
                    },
                ],
            },
        ],
        {
            basename: `${ERM_CONTEXT}/worms/client/app/react/${module}`,
        },
    );
};

export default getAppRouter;
