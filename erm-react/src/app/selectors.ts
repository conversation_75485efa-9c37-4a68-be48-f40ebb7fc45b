import { LayoutOption } from 'common/types';
import { RootState } from '../store';
import { IdWithParams, Route, ShowAll, SystemConfiguration } from './types';
import { SearchRequestParams } from '@protecht/ui-library/library/types/types';
import { initialLayoutProps } from './reducer';
import { IdWithNameRest, ViewRest, ScaleSetRest } from 'api/generated/types';

export const getGwtOverlayOpen = (state: RootState) => state.app.isGwtOverlayOpen;
export const getErrors = (state: RootState) => state.app.errors;
export const getIsCriticalError = (state: RootState) => state.app.isCriticalError;
export const getSystemConfiguration = (state: RootState): SystemConfiguration => state.app.systemConfiguration;
export const getCurrentUser = (state: RootState) => state.app.currentUser;
export const getCurrentUserBusinessUnit = (state: RootState): IdWithNameRest | null => state.app.currentUser?.businessUnit ?? null;
export const getLayoutOption = (state: RootState): LayoutOption =>
    state.app.layoutProps[`${location.pathname}`]?.layoutOption ?? initialLayoutProps.layoutOption;

export const getSearchValue = (state: RootState): string => state.app.layoutProps[`${location.pathname}`]?.searchValue ?? initialLayoutProps.searchValue;

export const getSearchProperty = (state: RootState): string =>
    state.app.layoutProps[`${location.pathname}`]?.searchProperty ?? initialLayoutProps.searchProperty;

export const getSelectedView = (state: RootState): ViewRest | ShowAll | undefined => state.app.layoutProps[`${location.pathname}`]?.selectedView;

export const getSelectedEntry = (state: RootState): IdWithParams | undefined => state.app.layoutProps[`${location.pathname}`]?.selectedEntry;

export const getRequestParams = (state: RootState): SearchRequestParams | undefined => state.app.layoutProps[`${location.pathname}`]?.requestParams;
export const getCurrentRoute = (state: RootState): Route => state.app.currentRoute;
export const getParentPathname = (state: RootState) => state.app.parentPathname;
export const getScaleSets = (state: RootState): ScaleSetRest[] => state.app.scaleSets;
export const getScaleSet = (state: RootState, scaleSetId: string): ScaleSetRest | undefined => {
    return state.app.scaleSets?.find((ss) => `${ss.id}` === `${scaleSetId}`);
};

export const getMainLogo = (state: RootState) => state.app.logos.main;
export const getLoginLogo = (state: RootState) => state.app.logos.login;

export const getIsMenuHidden = (state: RootState) => state.app.mainNavigation.isHidden;
export const getIsPinnedMenu = (state: RootState) => state.app.mainNavigation.isPinned;
export const getIsExpandedMenu = (state: RootState) => state.app.mainNavigation.isExpanded;
export const getActiveMenuId = (state: RootState) => state.app.mainNavigation.activeItemId;

export const getPageTopBarHeight = (state: RootState) => state.app.pageLayout.topBarHeight;
