<link
    rel="preconnect"
    href="https://fonts.googleapis.com"
/>
<link
    rel="preconnect"
    href="https://fonts.gstatic.com"
    crossorigin
/>
<link
    href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"
    rel="stylesheet"
/>

<script>
    window.ProtechtDictionary = {
        isApp: true,
        anonymousWidget: false,
        accentColor: '#1B4AD5',
        accentInvColor: '#FFFFFF',
        useLightMenu: 'false',
        conditionalFields: true,
        maxAt: '10',
        existingSessionsMonitor: false,
        preventCsvInjection: false,
        enableUiIds: true,
        encouragePassphrase: 'false',
        rememberMe: true,
        protechtStyling: false,
        loggedUserLogin: '<%= process.env.REACT_APP_API_USERNAME %>',
        loggedUserName: '<%= process.env.REACT_APP_API_USERNAME %>',
        loggedUserId: '<%= process.env.REACT_APP_API_USERID %>',
        loggedUserWalkMeId: 'camilla-2220',
        loggedUserWalkMePermissions: '[WALKME.ADMIN, WALKME.ANALYTICS, WALKME.ENDUSER]',
        helpUrl: 'http://127.0.0.1:8080/camilla/documentation',
        singlePage: true,
        ariaEnabled: false,
        homePage: false,
        homepageType: 'AnalyticReports',
        homeDashUrl: '/camilla/worms/client/app/Reports?op=vs&path=ERM/Global%20Reports/bestofbreed/Everyone/User%20Launchpad%20LOD1&delivery=direct',
        whiteMenuTheme: false,
        registerDesigner: false,
        subtUpdatePropagation: false,
        esftp: true,
        historicalReportingEnabled: false,
        sso: false,
        psk: '',
        walkMe: false,
        disableAutofill: true,
        useColumnLimiter: true,
        registerSelectorExtras: false,
        siteUrl: '<%= process.env.BASE_URL %>/<%= context %>',
        context: '/<%= context %>',
        reactInitialRoute: '/bowties/recent',
        isInternal: 'true',
    };

    window.ThemeConfig = {
        name: 'Triton',
        rowHeight: 34,
        defaultAccentColor: '#1B4AD5',
        defaultAccentInvColor: '#FFFFFF',
    };
</script>
