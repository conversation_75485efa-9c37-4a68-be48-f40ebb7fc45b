import React from 'react';
import { getProtechtTheme } from '../src/app/theme';
import { ConfirmationAlertProvider } from '../src/context/ConfirmationAlertProvider/ConfirmationAlertProvider';
import GlobalStyles from '../src/app/components/GlobalAppWrapper/GlobalStyles';
import { ThemeProvider } from '@mui/material/styles';
import { DocsContainer } from '@storybook/addon-docs';

export const decorators = [
    (Story) => (
        <GlobalStyles>
            <ThemeProvider theme={getProtechtTheme()}>
                <Story />
            </ThemeProvider>
        </GlobalStyles>
    ),
];

export const parameters = {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
        matchers: {
            color: /(background|color)$/i,
            date: /Date$/,
        },
    },
    backgrounds: {
        values: [
            { name: 'primary', value: '#1B4AD5' },
            { name: 'white', value: '#fff' },
        ],
    },
    docs: {
        container: (props) => {
            return (
                <GlobalStyles>
                    <ThemeProvider theme={getProtechtTheme()}>
                        <ConfirmationAlertProvider>
                            <DocsContainer {...props} />
                        </ConfirmationAlertProvider>
                    </ThemeProvider>
                </GlobalStyles>
            );
        },
    },
};
