const TsConfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const path = require('path');

module.exports = {
    stories: ['../src/**/*.stories.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx)'],

    addons: [
        '@storybook/addon-links',
        '@storybook/addon-essentials',
        '@storybook/addon-interactions',
        '@storybook/addon-controls',
        '@storybook/addon-mdx-gfm'
    ],

    framework: {
        name: '@storybook/react-webpack5',
        options: {}
    },

    webpackFinal: async (config, { configType }) => {
        config.resolve.plugins = [
            ...(config.resolve.plugins || []),
            new TsConfigPathsPlugin({
                extensions: config.resolve.extensions,
            }),
        ];
        return config;
    },

    typescript: {
        reactDocgen: 'react-docgen-typescript',
        reactDocgenTypescriptOptions: {
            shouldRemoveUndefinedFromOptional: true,
            propFilter: (prop) => {
                return prop.parent
                    ? prop.parent.name !== 'DOMAttributes' && prop.parent.name !== 'HTMLAttributes' && prop.parent.name !== 'AriaAttributes'
                    : true;
            },
        },
    },

    docs: {
        autodocs: true
    }
};
