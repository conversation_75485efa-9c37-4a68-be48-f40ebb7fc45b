<svg xmlns="http://www.w3.org/2000/svg" width="222.796" height="192.756" viewBox="0 0 58.948 51" xmlns:v="https://vecta.io/nano">
  <defs>
    <linearGradient id="A" spreadMethod="pad" gradientUnits="userSpaceOnUse" y2="113.624" x2="87.107" y1="95.862" x1="144.842"><stop offset="0" stop-color="#009bff"/><stop offset=".165" stop-color="#009bff"/><stop offset="1" stop-color="#00f"/></linearGradient>
    <linearGradient id="B" spreadMethod="pad" gradientUnits="userSpaceOnUse" y2="87.535" x2="124.554" y1="106.163" x1="67.22"><stop offset="0" stop-color="#009bff"/><stop offset=".186" stop-color="#009bff"/><stop offset="1" stop-color="#00f"/></linearGradient>
    <clipPath id="C"><path d="M0 161.575h212.598V0H0z"/></clipPath>
  </defs>
  <g transform="matrix(.352778 0 0 -.352778 -8.03245 58.25017)">
    <g>
      <animateTransform dur="1.5s" values="0 106.091522 100.803406;360 106.091522 100.803406;360 106.091522 100.803406;" calcMode="linear" keyTimes="0;0.7;1" repeatCount="indefinite" attributeName="transform" type="rotate" attributeType="XML"/>
      <path d="M153.958 138.017c-1.642-1.235-9.53-7.396-17.4-13.562l.002-.002-17.634-13.833c.222-.263 1.698-1.93 2.58-5.12.637-2.29.903-5.672.224-8.266-2.31-8.82-11.24-14.55-20.886-11.36-2.923 1.064-5.563 2.893-7.338 5.024.02-.026.04-.054.06-.08l-17.765-13.97C80.54 70.7 87.994 66.078 95.053 63.88c9.98-3.343 25.68-2.152 37.38 8.913 4.786 4.525 8.165 9.474 10.553 17.075l14.66 48.145c.284.923.003 1.363-.654 1.363-.68 0-1.76-.47-3.034-1.36"
      fill="url(#A)"/>
      <path d="M69.298 112.483C66.372 104.15 54.53 63.667 54.53 63.667c-.58-1.88 1.355-1.898 3.872-.16 4.62 3.2 23.92 18.485 35.155 27.31-2.947 3.64-4.398 9.8-2.98 14.57 2.57 8.644 11.055 13.18 19.153 11.33 3.65-.833 6.697-2.727 9.18-6.097l17.64 13.833c-5.102 6.146-10.9 10.92-20.13 13.557-3.104.887-6.504 1.36-10.022 1.36-14.2.001-30.36-7.694-37.1-26.886"
      fill="url(#B)"/>
    </g>
    <g clip-path="url(#C)" fill="#1f56f5">
      <path d="M39.383 33.912c0-4.693-2.937-7.4-8.062-7.4h-4v-5.758h-4.55v20.154h8.55c5.125 0 8.062-2.534 8.062-6.996m-4.318-.145c0 2.247-1.44 3.37-3.974 3.37h-3.77v-6.824h3.772c2.533 0 3.974 1.152 3.974 3.455M56.1 20.754l-3.196 5.758h-4.636v-5.758H43.73v20.153h8.752c5.27 0 8.263-2.533 8.263-6.996 0-3.108-1.323-5.355-3.772-6.507l4.3-6.65zm-7.832 9.56h4.204c2.505 0 3.945 1.15 3.945 3.455 0 2.245-1.44 3.37-3.945 3.37h-4.204zm37.957.516c0-5.816-4.664-10.28-10.826-10.28s-10.825 4.434-10.825 10.28c0 5.873 4.664 10.22 10.825 10.22s10.826-4.376 10.826-10.22m-16.987 0c0-3.542 2.85-6.306 6.22-6.306s6.104 2.764 6.104 6.306-2.735 6.247-6.104 6.247a6.2 6.2 0 0 1-6.22-6.247m35.7 10.078V37.05h-6.162V20.754h-4.548V37.05h-6.133v3.858zm19.232 0v-3.772h-10.798V32.76h9.704v-3.772h-9.704v-4.464h11.114v-3.77h-15.663v20.154zm14.683-3.8c-3.455 0-6.133-2.678-6.133-6.2s2.678-6.22 6.133-6.22c1.813 0 3.8.922 5.24 2.36l2.678-2.908c-2.132-2.188-5.212-3.598-8.148-3.598-6.018 0-10.568 4.462-10.568 10.307 0 5.816 4.637 10.192 10.74 10.192 2.9 0 5.96-1.295 7.95-3.34l-2.65-3.224c-1.383 1.583-3.37 2.62-5.24 2.62m16.608 3.812V32.5h9.127v8.407h4.55V20.754h-4.55V28.7h-9.127v-7.946h-4.55v20.154zm34.405 0V37.05h-6.162V20.754h-4.55V37.05h-6.132v3.858z"/>
    </g>
  </g>
</svg>
