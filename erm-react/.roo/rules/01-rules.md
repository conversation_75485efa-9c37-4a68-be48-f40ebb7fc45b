# Roo Knowledge Base Rules

This document defines simple, clear rules for <PERSON><PERSON> to follow when working with the ERM React project knowledge base.
Knowledge about this react project is a part of your system prompt. 

## While working on the task, <PERSON>oo should:

Apply the loaded knowledge to inform code generation and recommendations
Follow the patterns and practices documented in the knowledge base
Ensure code quality matches the team's standards

## Knowledge Categories

### Architecture Knowledge
High level project architecture. 
Project Structure: Directory organization, file naming conventions
Component Structure: Component design, props, state management
Libraries we use
Information about our @protecht/ui-library

### Contribution Knowledge
Coding Standards: TypeScript usage, formatting rules
Custom Hooks: Implementation details, usage patterns, parameters
State Management: Redux patterns, RTK Query usage
API Integration: API client usage, error handling
Form Handling: Form validation, submission patterns
Internationalization: i18n implementation, string management
Best Practices: Recommended patterns and approaches
Common Issues: Solutions to frequently encountered problems
Performance Considerations: Optimization techniques
Accessibility Guidelines: Making components accessible

## Identify New Knowledge at Task End

Review the work completed during the task
Identify new knowledge about Architecture and Contribution and propose changes to respective files

