print [CONTRIBUTION] to the chat
# ERM React Project Contribution Guidelines

This document contains guidelines and best practices for developers working on the ERM React project. It is automatically updated by Roo when new best practices or guidelines are learned during interactions.

## Coding Standards

### TypeScript

- Use TypeScript for all new files
- Define proper types for all variables, function parameters, and return values
- Use interfaces for complex object structures
- Leverage TypeScript's type inference where appropriate
- Use type guards when narrowing types

### Component Structure

- Use functional components with hooks instead of class components
- Follow a consistent naming convention:
  - Component files should be named using PascalCase (e.g., `ProgressDialog.tsx`)
  - Component directories should match the component name
  - Include index.ts files for cleaner imports
- Keep components focused on a single responsibility
- Extract reusable logic into custom hooks

### File Organization

- Group related files in feature-based directories
- Use index.ts files to simplify imports
- Keep test files alongside the files they test with a `.spec.tsx` suffix
- Use barrel exports for cleaner imports

## Custom Hooks

### useForm

A wrapper around react-hook-form with Yup schema validation.

- **Implementation**: `src/common/hooks/useForm.ts`
- **Usage**:
  ```typescript
  const formMethods = useForm<FormValues>({
    schema: myYupSchema,
    defaultValues: { ... },
    mode: 'onChange'
  });
  ```
- **Features**:
  - Integrates Yup validation with react-hook-form
  - Handles required field validation
  - Supports dynamic schema updates
  - Tracks errors in a ref for comparison

## Development Workflow

### Form Implementation

- Use the custom `useForm` hook for all forms to ensure consistent validation
- Define Yup schemas for form validation
- Follow the pattern:
  ```typescript
  const formMethods = useForm<FormValues>({
    schema: myYupSchema,
    defaultValues: { ... },
    mode: 'onChange'
  });
  
  const { handleSubmit, reset } = formMethods;
  
  // In JSX
  <FormProvider {...formMethods}>
    {/* Form fields */}
  </FormProvider>
  ```

## State Management

The application uses Redux with RTK Query for state management:

- **API Slices**: Generated from OpenAPI specs using RTK Query codegen
- **Endpoints**: Organized by feature (frameworks, users, etc.)
- **Caching**: Utilizes RTK Query's caching with tag invalidation

- Use RTK Query for API calls to benefit from caching and automatic refetching
- Follow the Redux pattern for global state management
- Use local state (useState) for component-specific state
- Consider using context for state that needs to be shared among a few components

### RTK Query Extension Pattern

The application follows a specific pattern for extending the generated RTK Query API clients:

```mermaid
flowchart TD
    A[Generated API] -->|Import| B[Enhance with Tags]
    B -->|Add Custom Endpoints| C[Inject Endpoints]
    C -->|Configure Cache| D[Enhance Endpoints Again]
    D -->|Export Hooks| E[Use in Components]
    
    subgraph "Cache Management"
        F[Define Tag Types]
        G[Provide Tags]
        H[Invalidate Tags]
        I[Cache Profiles]
    end
    
    B -.-> F
    D -.-> G & H
    D -.-> I
```

#### Base Structure

1. **Base API Configuration**: The application defines a base API in `src/api/baseApi.ts` with token refresh handling.
2. **Generated API Files**: API clients are auto-generated from OpenAPI specs in `src/api/generated/`.
3. **Feature-specific Extensions**: Each feature extends the generated APIs with custom endpoints and caching in files like `src/frameworks/rtkApi.ts`.

#### Enhancing Endpoints with Tags

The first step in extending RTK Query is adding tag types for cache invalidation:

```typescript
export const enhancedFrameworksApi = baseFrameworksApi.enhanceEndpoints({
    addTagTypes: ['frameworks', 'framework', 'frameworkCategories', 'nodes', 'metadataRegisters'],
});
```

#### Creating Custom Endpoints

Custom endpoints are added by injecting new endpoints into the enhanced API:

```typescript
export const frameworksApi = enhancedFrameworksApi.injectEndpoints({
    endpoints: (builder) => ({
        // Custom endpoint with specific parameter handling
        fcDuplicateFrameworkUsingPost1: builder.mutation<ResponseType, { id: string; name: string }>({
            query: ({ id, name }) => ({
                method: 'POST',
                headers: {
                    'Content-type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({ name }).toString(),
                url: `/api/frameworks/${id}/duplicate`,
            }),
        }),
        
        // Custom endpoint with special response handling
        fcExportFrameworkUsingGet1: builder.query<BlobResponseType, { id: number; type: string }>({
            query(args) {
                return {
                    url: `/api/frameworks/${args.id}/export`,
                    params: { type: args.type },
                    method: 'GET',
                    cache: 'no-cache',
                    responseHandler: async (response: Response) => {
                        if (response.ok) {
                            return response.blob();
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                    },
                };
            },
        }),
    }),
    overrideExisting: false, // Don't override existing endpoints
});
```

#### Configuring Cache Invalidation

After defining custom endpoints, cache invalidation is configured:

```typescript
frameworksApi.enhanceEndpoints({
    endpoints: {
        // Endpoint provides tags for cache identification
        fcGetFrameworksUsingGet: {
            providesTags: ['frameworks'],
            ...cacheProfiles.noCache, // Apply cache profile
        },
        
        // Endpoint invalidates tags when called
        fcDeleteFrameworkUsingDelete: {
            invalidatesTags: ['frameworks'],
        },
    },
});
```

#### Cache Profiles

The application defines reusable cache profiles in `src/api/cacheProfiles.ts`:

```typescript
export const cacheProfiles = {
    // Static data that doesn't change frequently
    staticData: {
        keepUnusedDataFor: 30 * 60, // 30 minutes
        refetchOnMountOrArgChange: false,
    },
    
    // Short-lived cache for navigation
    backAndForthNavigation: {
        keepUnusedDataFor: 5, // 5 seconds
        refetchOnMountOrArgChange: false,
    },
    
    // No caching, always refetch
    noCache: {
        keepUnusedDataFor: 0,
        refetchOnMountOrArgChange: true,
    },
};
```

#### Exporting Hooks

The final step is exporting the generated hooks for use in components:

```typescript
export const {
    useFcGetFrameworksUsingGetQuery,
    useFcCreateDraftFrameworkMutation,
    // Other hooks...
} = frameworksApi;
```

#### Naming Conventions

- Custom endpoints typically follow the naming pattern of the generated endpoints with a suffix (e.g., `UsingPost1`)
- Custom endpoints that completely replace generated ones use descriptive names (e.g., `fcCreateDraftFramework`)

### RTK Query Extension Best Practices

When extending generated RTK Query APIs:

1. **Follow the established extension pattern**:
   ```typescript
   // 1. Import the generated API
   import { baseInjectedApi as baseFrameworksApi } from 'api/generated/frameworks';
   
   // 2. Enhance with tag types
   export const enhancedFrameworksApi = baseFrameworksApi.enhanceEndpoints({
     addTagTypes: ['tagType1', 'tagType2'],
   });
   
   // 3. Inject custom endpoints
   export const frameworksApi = enhancedFrameworksApi.injectEndpoints({
     endpoints: (builder) => ({
       // Custom endpoints here
     }),
     overrideExisting: false,
   });
   
   // 4. Configure cache invalidation
   frameworksApi.enhanceEndpoints({
     endpoints: {
       // Cache configuration here
     },
   });
   
   // 5. Export hooks
   export const { useCustomEndpointQuery, useCustomEndpointMutation } = frameworksApi;
   ```

2. **Use consistent naming conventions**:
   - For custom endpoints that extend generated ones, use the same name with a suffix (e.g., `UsingPost1`)
   - For completely new endpoints, use descriptive names that follow the API naming pattern

3. **Apply appropriate cache profiles**:
   - Use `cacheProfiles.staticData` for data that rarely changes
   - Use `cacheProfiles.backAndForthNavigation` for data that should be briefly cached
   - Use `cacheProfiles.noCache` for data that should always be fresh

4. **Implement proper tag invalidation**:
   - Use `providesTags` to identify cached data
   - Use `invalidatesTags` to clear cache when data changes
   - Be specific with tags to avoid over-invalidation

5. **Handle special responses appropriately**:
   - For file downloads, use custom `responseHandler` to process blobs
   - For complex transformations, use the `transformResponse` option

## API Integration

- **RTK Query**: Used for API calls with automatic caching
- **OpenAPI Generation**: API clients are generated from OpenAPI specs
- **Endpoints**: Organized by feature (frameworks, users, etc.)
- **Extension Pattern**: Generated API clients are extended with custom endpoints and caching strategies

## Form Handling

Forms are implemented using:

- **React Hook Form**: For form state management
- **Yup**: For schema validation
- **Custom useForm hook**: To integrate the two

## Internationalization

The application uses i18next for internationalization:

- **Resource Files**: Located in `src/i18n/en/`
- **Structure**: Organized by feature (common, frameworks, etc.)
- **Usage**: Via the `strings()` function that accesses translations

- Use the i18n system for all user-facing text
- Access translations using the `strings()` function
- Organize translations by feature in the appropriate files under `src/i18n/en/`
- Use translation keys that follow a consistent pattern: `namespace:category.key`

### Error Handling

- Use try/catch blocks for error handling in async functions
- Provide meaningful error messages
- Use the snackbar system for displaying user-facing errors

## Best Practices

### Performance Optimization

- Use memoization (useMemo, useCallback) for expensive calculations or to prevent unnecessary re-renders
- Avoid unnecessary re-renders by properly using dependency arrays in hooks
- Consider code splitting for large components or features

### Testing

- Write unit tests for all new components and hooks
- Use Jest and React Testing Library
- Test component behavior, not implementation details
- Mock external dependencies

### Accessibility

- Ensure all interactive elements are keyboard accessible
- Use semantic HTML elements
- Provide appropriate ARIA attributes when needed
- Ensure sufficient color contrast

### Security

- Validate all user inputs
- Sanitize data displayed to users
- Use proper authentication and authorization checks

## Common Patterns

### Dialog Implementation

```tsx
<Dialog
  visible={isVisible}
  title={strings('namespace:title')}
  width={663}
  dialogActions={
    <DialogActions>
      <Button
        variant={'secondary'}
        onClick={handleCancel}
      >
        {strings('common:button.cancel')}
      </Button>
      <Button
        onClick={handleConfirm}
      >
        {strings('common:button.confirm')}
      </Button>
    </DialogActions>
  }
>
  {/* Dialog content */}
</Dialog>
```

### Form Field Implementation

```tsx
<FormProvider {...formMethods}>
  <AttachmentField
    multiple={false}
    accept={{ 'text/csv': ['.csv'] }}
    formFieldProps={{
      name: 'importFile',
      label: strings('namespace:label')
    }}
  />
</FormProvider>
```

### API Call Implementation

```tsx
const [apiFunction, { isLoading }] = useApiMutation();

const handleSubmit = async (data) => {
  try {
    await apiFunction(data).unwrap();
    // Handle success
  } catch (error) {
    // Handle error
  }
};
```

## Troubleshooting

### Common Issues

- **Form validation not working**: Ensure you're using the custom `useForm` hook and have defined a proper Yup schema
- **API calls not updating cache**: Check that you're using the correct tag invalidation in your RTK Query setup
- **Components not re-rendering**: Verify dependency arrays in useEffect, useMemo, and useCallback hooks

## Code Review Guidelines

- Ensure code follows the project's coding standards
- Check for proper error handling
- Verify that internationalization is used for all user-facing text
- Look for potential performance issues
- Ensure proper typing with TypeScript
- Verify that tests are included for new functionality