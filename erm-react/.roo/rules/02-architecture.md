Print [ARCHITECTURE] to chat at the start of the chat

# ERM React Project Architecture

This document contains technical details about the ERM React project's architecture, components, and high-level structure. It is automatically updated by <PERSON>oo when new technical information is learned during interactions.

## Project Overview

The ERM (Enterprise Risk Management) React application is built with:
- TypeScript
- React 18.3.1
- Redux with RTK Query for state management and API calls
- Material UI components
- React Hook Form with Yup validation
- Internationalization (i18n) support

## Directory Structure

The project follows a feature-based organization:

```
src/
├── api/               # API configuration and generated API clients
├── common/            # Shared components, hooks, utils, and types
│   ├── components/    # Reusable UI components
│   ├── hooks/         # Custom React hooks
│   ├── utils/         # Utility functions
│   ├── icons/         # SVG icons and icon components
│   └── types.ts       # Common TypeScript types
├── frameworks/        # Framework-related components and logic
├── i18n/              # Internationalization resources
├── register/          # Register feature module
├── resilience/        # Resilience feature module
├── risk/              # Risk management feature module
└── rolesAndPermissions/ # User roles and permissions module
```

## Component Structure

### ProgressDialog

A component for displaying progress of long-running operations.

- **Implementation**: `src/common/components/ProgressDialog/ProgressDialog.tsx`
- **Usage**: Used for showing import/export progress, file uploads, etc.
- **Props**:
  - `visible`: Controls dialog visibility
  - `title`: Dialog title
  - `progressType`: Type of progress (Import, VERIFICATION)
  - `infoMessage`: Message to display
  - `totalCount`: Total number of items
  - `processedCount`: Number of processed items
  - `validationMessages`: Error messages
  - `processFinished`: Whether the process is complete
  - `progressVariant`: Progress bar variant
  - `onClose`: Callback when dialog is closed
  - `entityName`: Name of the entity being processed

### Framework Nodes

Components for managing framework nodes:

- **NodesIO**: Handles import/export of framework nodes
  - **Implementation**: `src/frameworks/FrameworkNodes/NodesIO.tsx`
  - **Usage**: Used for importing CSV data into frameworks

## Libraries and Dependencies

The application uses several key libraries:
- **Redux Toolkit**: For state management
- **RTK Query**: For API data fetching and caching
- **Material UI**: For UI components
- **React Hook Form**: For form handling
- **Yup**: For form validation
- **i18next**: For internationalization

## File Upload/Import Capabilities

The application supports file uploads and imports:

- **CSV Import**: For importing data into frameworks
- **Validation**: Files are validated before import
- **Progress Tracking**: Import progress is tracked and displayed