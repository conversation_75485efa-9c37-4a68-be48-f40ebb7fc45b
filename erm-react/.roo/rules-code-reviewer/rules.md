When using git command line tools, compare local changes with the dev branch, unless user specifies a different target branch. You must not use "git diff <target_branch>" to get all changes in the diff, always specify the list of files when using "git diff".

If instructed to do a quick review:
- use git command line tools to get paths of files that have local changes to the target branch
- use git command line tools to get the diff of changes in the non-excluded files
- only review the changes in the diff and don't read entire files

If asked to do a detailed code review
- use git command line tools to get the paths of files that have local changes to the target branch
- prepare a list of files that you will review and all applicable related files
- read the contents and do a full review of these files one by one
- check for common linting issues like undeclared variables, unused variables/imports, and potential runtime errors (e.g., ReferenceError, TypeError) in all changed files and any other files you review in detail.

If instructed to review a single file or a specific list of files:
- ignore exclusion rules in this case
- use git command line tools to get the diff of changes in the specified files
- by default, review only the diff and don't read the files, unless user explicitly states that you should review full files
- review specified files one by one

If not specified, do a quick review.

You must not fix issues during the review.

These files, along with the file in which these changes were made, are present in the current workspace.
Only after all preparations are complete do you conduct the review.

During your review, do not run lint or any other static code review tools. Dont raise "missing import" issues unless you actually know that the import is missing.

After a review, list the comments that you plan to make in detail, including their severity. In the summary comment, ensure you provide a recommendation on whether you approve the changes or not in the first line of the comment.

Use the following list of severities: ‼️ Critical, 🔴 Major, 🟡 Minor, 🟢 Suggestion.
Use following rules when determining the PR approve recommendation:
If there are any Critical or Major issues, Request changes.
If there are Minor issues, Approve with Minor Changes.
If there are no issues or Suggestions only, Approve.

Here are the guidelines for the assignment of severity:
Code style issues, documentation problems are considered Suggestion
Error handling, missing parameters, redundant calls or unused variables are considered Minor
Functional issues, library downgrades, performance problems and low-mid severity security issues are considered Major
Runtime errors or high-severity security issues are considered Critical

Issues found are reserved for problems and potential opportunities for improvement. Dont list positive changes as issues and dont list positive changes as suggestions. If you find no issues or suggestions, write "No issues found" in the summary comment and dont list any issues in the "Issues Found" section. If you need to note positive changes, do that in the Brief Code review findings summary.

Use the following markdown format for the summary comment:
```
## Code Review Summary - {Request Changes ❌/Approve ✅/Approve With Minor Changes ⚠️/etc.}

{Brief Code review findings summary}

### Issues Found: {show a bullet point for severity of all issues and suggestions that were identified}

* {visual severity indicator e.g. 🔴} **{Severity} \({count of issues with this severity}\)**: {comma delimited list of short issue descriptions}

### Recommendation:

{write your recommendation here}
```
Use the following markdown format for the Line level comment:
```
### {visual severity indicator e.g. 🔴} {Severity}: {short issue description}

{Issue details}

### Recommendation:

{write a recommendation for a fix for the issue, ensure the recommended code is valid}
```

Ensure the comments are properly formatted using Markdown. Ensure you show line-level comments for the correct line in each file.

After the review is done, let the user review these comments. Only work on fixing of these issues if specifically instructed by the user, and only after the review is completed.

**IMPORTANT: After the review is completed, ask the user for next steps using the ask_followup_question tool. DO NOT use attempt_completion. The task is not completed until the user explicitly confirms the task is completed. You must not use attempt_completion otherwise.**
