const yaml = require('yaml');
const fs = require('fs');
const ICONS_FILE = '../../camilla/src/com/protecht/utils/fontawesome/icons.yml';
const CATEGORIES_FILE = '../../camilla/src/com/protecht/utils/fontawesome/categories.yml';
const TARGET_FILE = '../src/common/utils/icons/ermIcons.ts';

const toFaCamelCase = (icon) => {
    let newIcon = icon.split('-');
    return (
        'fa' +
        newIcon
            .map((i) => {
                return i.charAt(0).toUpperCase() + i.slice(1);
            })
            .join('')
    );
};

const parseCategoriesYamlFile = (icons, fileSource) => {
    const categoriesMap = {};
    const file = fs.readFileSync(fileSource, 'UTF-8');
    const parsedContent = yaml.parseDocument(file, {}).contents.items;
    parsedContent.forEach((category) => {
        const newCategory = {
            name: category.key.value,
        };
        category.value.items.forEach((item) => {
            if (item.key.value === 'icons') {
                item.value.items.forEach((item) => {
                    push(categoriesMap, toFaCamelCase(item.value), newCategory);
                });
            } else if (item.key.value === 'label') {
                newCategory.label = item.value.value;
            }
        });
    });

    icons.list.forEach((icon) => {
        const array = categoriesMap[icon.name];
        icon.categories = array ? array : [];
    });
};

const parseIconsYamlFile = (fileSource) => {
    const iconsMap = {
        list: [],
        duplicates: [],
    };
    const file = fs.readFileSync(fileSource, 'UTF-8');
    const parsedContent = yaml.parseDocument(file, {}).contents.items;
    parsedContent.forEach((icon) => {
        const newIcon = {};
        icon.value.items.forEach((item) => {
            if (item.key.value === 'styles') {
                const newIconName = toFaCamelCase(icon.key.value);
                newIcon.name = newIconName;
                newIcon.originalName = icon.key.value;
                newIcon.styles = item.value.items.map((i) => i.value);
                if (item.value.items.length === 1) {
                    push(iconsMap, item.value.items[0], newIconName);
                } else {
                    item.value.items.forEach((item) => {
                        push(iconsMap, item.value, newIconName);
                    });
                    iconsMap.duplicates.push(newIconName);
                }
            } else if (item.key.value === 'search') {
                item.value.items.forEach((item) => {
                    if (item.key.value === 'terms') {
                        newIcon.terms = item.value.items.map((i) => String(i.value));
                    }
                });
            } else if (item.key.value === 'label') {
                newIcon.label = item.value.value;
            }
        });
        iconsMap.list.push(newIcon);
    });
    return iconsMap;
};

const push = (map, key, value) => {
    if (!map[key]) {
        map[key] = [];
    }
    map[key].push(value);
};

const createItemsArray = (arr, duplicates, iconBrand, type) => {
    return arr.map((item) => {
        if (duplicates.indexOf(item) !== -1) {
            return type === 'import' ? `${item} as ${item + iconBrand}` : `${item + iconBrand}`;
        } else {
            return item;
        }
    });
};

const createImportStatement = (str, library) => {
    return `import {${str}} from '@fortawesome/${library === 'brands' ? 'free-brands' : 'pro-' + library}-svg-icons';`;
};

const createLibraryStatement = (str) => {
    return `library.add(${str});`;
};

const splitArrayToMultiline = (arr, breakNumber = 5) => {
    return arr.reduce((prev, curr, index) => {
        if (index % breakNumber === 1 && index !== 1) {
            return `${prev} ${curr}`;
        } else if (index % breakNumber === 0) {
            return `${prev}, ${curr},\n   `;
        } else {
            return `${prev}, ${curr}`;
        }
    });
};

const createImportLine = (type, arr, duplicates, iconSet) => {
    const itemsArray = createItemsArray(arr, duplicates, iconSet.slice(0, 1).toUpperCase(), type);
    const importStr = splitArrayToMultiline(itemsArray, 5);
    return type === 'import' ? createImportStatement(importStr, iconSet) : createLibraryStatement(importStr);
};

const makeFileContent = (iconsData) => {
    let fileContent = ["import {library} from '@fortawesome/fontawesome-svg-core';"];
    ['import', 'library'].forEach((type) => {
        Object.keys(iconsData)
            .slice(2)
            .forEach((iconSet) => {
                const line = createImportLine(type, iconsData[iconSet], iconsData['duplicates'], iconSet);
                fileContent.push(line);
                fileContent.push('\n');
            });
    });

    fileContent.push('export const ICONS = {');
    iconsData.list.forEach((icon, index) => {
        const last = iconsData.list.length - 1 === index;
        icon.styles.forEach((style, index) => {
            let terms = icon.terms.map((term) => term.replace("'", "\\'")).join("','");
            if (terms) {
                terms = `'${terms}'`;
            }
            let categories = icon.categories.map((category) => category.label.replace("'", "\\'")).join("','");
            if (categories) {
                categories = `'${categories}'`;
            }
            const classes = `'fa${style.slice(0, 1)} fa-${icon.originalName}'`;
            //const name = icon.name + '_' + style;
            fileContent.push(
                `    ${classes}: {` +
                    `name: '${icon.name}', ` +
                    `style: '${style}', ` +
                    `className: 'fa-${icon.originalName}', ` +
                    `classStyle: 'fa${style.slice(0, 1)}', ` +
                    `label: '${icon.label.replace("'", "\\'")}', ` +
                    `terms: [${terms}], ` +
                    `categories: [${categories}], ` +
                    `definition: ${getIconName(icon.name, style, iconsData.duplicates)}` +
                    '}' +
                    (last && icon.styles.length - 1 === index ? '' : ','),
            );
        });
    });
    fileContent.push('};');

    return fileContent;
};

const createIconsScript = (targetFile, fileContent) => {
    let file = fs.createWriteStream(targetFile);
    file.on('error', () => {
        console.log('File not created!');
    });
    fileContent.forEach((line) => {
        file.write(line);
        file.write('\n');
    });
    file.end();
};

const getIconName = (iconName, iconStyle, duplicates) => {
    if (duplicates.indexOf(iconName) !== -1) {
        return `${iconName + iconStyle.slice(0, 1).toUpperCase()}`;
    } else {
        return iconName;
    }
};

const createFontAwesomeImportFile = () => {
    const icons = parseIconsYamlFile(ICONS_FILE);
    parseCategoriesYamlFile(icons, CATEGORIES_FILE);
    const fileContent = makeFileContent(icons);
    createIconsScript(TARGET_FILE, fileContent);
};

createFontAwesomeImportFile();
