const fs = require('fs');

const MESSAGE_TRANSFORM_PATHS = [
    {
        source: '../erm-common/src/main/java/com/protecht/gwt/client/i18n/messages/ProtechtMessages.properties',
        target: 'src/i18n/en/ermMessages.json',
    },
];

const ERROR_TRANSFORM_PATHS = [
    {
        source: '../erm-common/src/main/java/com/protecht/gwt/client/i18n/errors/ProtechtErrors.properties',
        target: 'src/i18n/en/ermErrors.json',
    },
];

const CONSTANT_TRANSFORM_PATHS = [
    {
        source: '../erm-common/src/main/java/com/protecht/gwt/client/i18n/constants/ProtechtConstants.properties',
        target: 'src/i18n/en/ermConstants.json',
    },
];

const transformFile = ({ source, target }) => {
    const data = fs.readFileSync(source, 'UTF-8');
    const lines = data.split(/\r?\n/);
    const result = {};
    for (const line of lines) {
        if (line) {
            let key = line.substring(0, line.indexOf('='));
            let value = line
                .substring(line.indexOf('=') + 1)
                .replace(/\{/g, '{{')
                .replace(/\}/g, '}}');
            const matches = line.match(/\[(.*?)\]=/);

            if (matches) {
                key = key.substring(0, key.indexOf('['));
                const stringInBrackets = matches[1];
                if (stringInBrackets.indexOf('=') > -1) {
                    value = value.substring(value.indexOf('=') + 1);
                }
                if (stringInBrackets === 'one' || stringInBrackets === '1') {
                    result[`${key}_other`] = result[key].replace('{{0}}', '{{count}}');
                    result[`${key}_one`] = value.replace('{{0}}', '{{count}}');
                } else if (stringInBrackets.indexOf('|') > -1) {
                    // TODO: Use context to handle multiple inputs with conditions
                } else {
                    //console.log(key, stringInBrackets, value);
                    result[`${key}_${stringInBrackets}`] = value.replace('{{0}}', '{{count}}').replace('=', '');
                }
            } else {
                result[key] = value;
            }
        }
    }
    fs.writeFileSync(target, JSON.stringify(result, null, 4));
};

const transformI18n = () => {
    for (const message of MESSAGE_TRANSFORM_PATHS) {
        transformFile(message);
    }
    for (const error of ERROR_TRANSFORM_PATHS) {
        transformFile(error);
    }
    for (const constant of CONSTANT_TRANSFORM_PATHS) {
        transformFile(constant);
    }
};

transformI18n();
